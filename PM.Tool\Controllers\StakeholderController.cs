using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Enums;
using PM.Tool.Services;
using PM.Tool.Models.ViewModels;

namespace PM.Tool.Controllers
{
    public class StakeholderController : SecureBaseController
    {
        private readonly IStakeholderService _stakeholderService;
        private readonly IProjectService _projectService;
        private readonly IFormHelperService _formHelper;
        private readonly ILogger<StakeholderController> _logger;

        public StakeholderController(
            IStakeholderService stakeholderService,
            IProjectService projectService,
            IFormHelperService formHelper,
            ILogger<StakeholderController> logger,
            IAuditService auditService) : base(auditService)
        {
            _stakeholderService = stakeholderService;
            _projectService = projectService;
            _formHelper = formHelper;
            _logger = logger;
        }

        // GET: Stakeholder
        public async Task<IActionResult> Index()
        {
            try
            {
                var stakeholders = await _stakeholderService.GetAllStakeholdersAsync();
                return View(stakeholders);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading stakeholders.";
                return View(new List<Stakeholder>());
            }
        }

        // GET: Stakeholder/Details/5
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var stakeholder = await _stakeholderService.GetStakeholderByIdAsync(id);
                if (stakeholder == null) return NotFound();

                return View(stakeholder);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading stakeholder details.";
                return RedirectToAction("Index");
            }
        }

        // GET: Stakeholder/Create
        public async Task<IActionResult> Create()
        {
            try
            {
                await PopulateDropdowns();
                return View(new StakeholderCreateViewModel());
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading stakeholder creation form.";
                return RedirectToAction("Index");
            }
        }

        // POST: Stakeholder/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(StakeholderCreateViewModel viewModel)
        {
            return await this.HandleCreateAsync<StakeholderCreateViewModel, Stakeholder>(
                viewModel,
                async (stakeholder) => {
                    var createdStakeholder = await _stakeholderService.CreateStakeholderAsync(stakeholder);
                    await LogAuditAsync(AuditAction.Create, "Stakeholder", createdStakeholder.Id);
                    return createdStakeholder;
                },
                stakeholder => stakeholder.Id,
                PopulateDropdowns,
                _formHelper,
                _logger,
                "Stakeholder"
            );
        }

        // GET: Stakeholder/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                var stakeholder = await _stakeholderService.GetStakeholderByIdAsync(id);
                if (stakeholder == null) return NotFound();

                var viewModel = StakeholderEditViewModel.FromEntity(stakeholder);
                await PopulateDropdowns();
                return View(viewModel);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading stakeholder for editing.";
                return RedirectToAction("Index");
            }
        }

        // POST: Stakeholder/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, StakeholderEditViewModel viewModel)
        {
            if (id != viewModel.Id) return NotFound();

            return await this.HandleUpdateAsync<StakeholderEditViewModel, Stakeholder>(
                id,
                viewModel,
                async (stakeholderId) => await _stakeholderService.GetStakeholderByIdAsync(stakeholderId),
                async (stakeholder) => {
                    await _stakeholderService.UpdateStakeholderAsync(stakeholder);
                    await LogAuditAsync(AuditAction.Update, "Stakeholder", id);
                    return stakeholder;
                },
                PopulateDropdowns,
                _formHelper,
                _logger,
                "Stakeholder"
            );
        }

        // GET: Stakeholder/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var stakeholder = await _stakeholderService.GetStakeholderByIdAsync(id);
                if (stakeholder == null) return NotFound();

                return View(stakeholder);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading stakeholder for deletion.";
                return RedirectToAction("Index");
            }
        }

        // POST: Stakeholder/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var success = await _stakeholderService.DeleteStakeholderAsync(id);
                if (success)
                {
                    await LogAuditAsync(AuditAction.Delete, "Stakeholder", id);
                    TempData["Success"] = "Stakeholder deleted successfully.";
                }
                else
                {
                    TempData["Error"] = "Error deleting stakeholder.";
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error deleting stakeholder.";
            }

            return RedirectToAction("Index");
        }

        // POST: Stakeholder/Deactivate/5
        [HttpPost]
        public async Task<IActionResult> Deactivate(int id)
        {
            try
            {
                var success = await _stakeholderService.DeactivateStakeholderAsync(id);
                if (success)
                {
                    await LogAuditAsync(AuditAction.Update, "Stakeholder", id);
                    return Json(new { success = true, message = "Stakeholder deactivated successfully." });
                }
                return Json(new { success = false, message = "Error deactivating stakeholder." });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Error deactivating stakeholder." });
            }
        }

        // API: Get Stakeholders for dropdowns
        [HttpGet]
        public async Task<IActionResult> GetStakeholders()
        {
            try
            {
                var stakeholders = await _stakeholderService.GetActiveStakeholdersAsync();
                var result = stakeholders.Select(s => new {
                    id = s.Id,
                    name = s.Name,
                    email = s.Email,
                    organization = s.Organization,
                    fullContact = s.FullContact
                });

                return Json(result);
            }
            catch (Exception ex)
            {
                return Json(new List<object>());
            }
        }

        // API: Get Project Stakeholders
        [HttpGet]
        public async Task<IActionResult> GetProjectStakeholders(int projectId)
        {
            try
            {
                var stakeholders = await _stakeholderService.GetStakeholdersByProjectAsync(projectId);
                var result = stakeholders.Select(s => new {
                    id = s.Id,
                    name = s.Name,
                    email = s.Email,
                    organization = s.Organization,
                    role = s.Role.ToString(),
                    influence = s.Influence.ToString(),
                    interest = s.Interest.ToString(),
                    priority = s.Priority.ToString()
                });

                return Json(result);
            }
            catch (Exception ex)
            {
                return Json(new List<object>());
            }
        }

        private async Task PopulateDropdowns()
        {
            // Stakeholder Types
            ViewBag.StakeholderTypes = Enum.GetValues<StakeholderType>()
                .Select(t => new SelectListItem
                {
                    Value = ((int)t).ToString(),
                    Text = t.ToString()
                }).ToList();

            // Stakeholder Roles
            ViewBag.StakeholderRoles = Enum.GetValues<StakeholderRole>()
                .Select(r => new SelectListItem
                {
                    Value = ((int)r).ToString(),
                    Text = r.ToString()
                }).ToList();

            // Influence Levels
            ViewBag.InfluenceLevels = Enum.GetValues<InfluenceLevel>()
                .Select(i => new SelectListItem
                {
                    Value = ((int)i).ToString(),
                    Text = i.ToString()
                }).ToList();

            // Interest Levels
            ViewBag.InterestLevels = Enum.GetValues<InterestLevel>()
                .Select(i => new SelectListItem
                {
                    Value = ((int)i).ToString(),
                    Text = i.ToString()
                }).ToList();
        }
    }
}
