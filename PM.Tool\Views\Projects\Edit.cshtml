@model ProjectEditViewModel

@{
    ViewData["Title"] = "Edit Project";
    ViewData["Subtitle"] = "Update project information and settings";
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-edit mr-3 text-primary-600 dark:text-primary-400"></i>
                Edit Project
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Update project information and settings
            </p>
        </div>
        <div class="flex space-x-3">
            @{
                ViewData["Text"] = "View Details";
                ViewData["Variant"] = "outline";
                ViewData["Icon"] = "fas fa-eye";
                ViewData["Href"] = Url.Action("Details", new { id = Model.Id });
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "Back to Projects";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Index");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Edit Project Form -->
<div class="card-custom">
    <div class="card-header-custom">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                <i class="fas fa-project-diagram text-primary-600 dark:text-primary-400 text-sm"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Project Information</h3>
            </div>
        </div>
    </div>
    <div class="card-body-custom">
        <form asp-action="Edit" class="space-y-6">
            <div asp-validation-summary="ModelOnly" class="alert-danger-custom"></div>
            <input type="hidden" asp-for="Id" />

            <!-- Project Name -->
            @{
                ViewData["Label"] = Html.DisplayNameFor(m => m.Name).ToString();
                ViewData["Name"] = "Name";
                ViewData["Type"] = "text";
                ViewData["Required"] = true;
                ViewData["Icon"] = "fas fa-tag";
                ViewData["Value"] = Model.Name;
                ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Name");
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

            <!-- Project Description -->
            @{
                ViewData["Label"] = Html.DisplayNameFor(m => m.Description).ToString();
                ViewData["Name"] = "Description";
                ViewData["Type"] = "textarea";
                ViewData["Required"] = false;
                ViewData["Icon"] = "fas fa-align-left";
                ViewData["Value"] = Model.Description;
                ViewData["Rows"] = 4;
                ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Description");
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

        <!-- Date Range -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Start Date -->
            @{
                ViewData["Label"] = Html.DisplayNameFor(m => m.StartDate).ToString();
                ViewData["Name"] = "StartDate";
                ViewData["Type"] = "date";
                ViewData["Required"] = true;
                ViewData["Icon"] = "fas fa-calendar-alt";
                ViewData["Value"] = Model.StartDate.ToString("yyyy-MM-dd");
                ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("StartDate");
                ViewData["ContainerClasses"] = "";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

            <!-- End Date -->
            @{
                ViewData["Label"] = Html.DisplayNameFor(m => m.EndDate).ToString();
                ViewData["Name"] = "EndDate";
                ViewData["Type"] = "date";
                ViewData["Required"] = false;
                ViewData["Icon"] = "fas fa-calendar-check";
                ViewData["Value"] = Model.EndDate?.ToString("yyyy-MM-dd");
                ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("EndDate");
                ViewData["ContainerClasses"] = "";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />
        </div>

        <!-- Budget and Client -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Budget -->
            @{
                ViewData["Label"] = Html.DisplayNameFor(m => m.Budget).ToString();
                ViewData["Name"] = "Budget";
                ViewData["Type"] = "number";
                ViewData["Required"] = false;
                ViewData["Icon"] = "fas fa-dollar-sign";
                ViewData["Value"] = Model.Budget.ToString("F2");
                ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Budget");
                ViewData["ContainerClasses"] = "";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

            <!-- Client Name -->
            @{
                ViewData["Label"] = Html.DisplayNameFor(m => m.ClientName).ToString();
                ViewData["Name"] = "ClientName";
                ViewData["Type"] = "text";
                ViewData["Required"] = false;
                ViewData["Icon"] = "fas fa-building";
                ViewData["Value"] = Model.ClientName;
                ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("ClientName");
                ViewData["ContainerClasses"] = "";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />
        </div>

        <!-- Status and Actual End Date -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Status -->
            @{
                var statusOptions = "<option value=\"\">-- Select Status --</option>";
                foreach (var item in Html.GetEnumSelectList<PM.Tool.Core.Enums.ProjectStatus>())
                {
                    var selected = item.Value == Model.Status.ToString() ? "selected" : "";
                    statusOptions += $"<option value=\"{item.Value}\" {selected}>{item.Text}</option>";
                }

                ViewData["Label"] = Html.DisplayNameFor(m => m.Status).ToString();
                ViewData["Name"] = "Status";
                ViewData["Type"] = "select";
                ViewData["Required"] = true;
                ViewData["Icon"] = "fas fa-info-circle";
                ViewData["Options"] = statusOptions;
                ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Status");
                ViewData["ContainerClasses"] = "";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

            <!-- Actual End Date -->
            @{
                ViewData["Label"] = Html.DisplayNameFor(m => m.ActualEndDate).ToString();
                ViewData["Name"] = "ActualEndDate";
                ViewData["Type"] = "date";
                ViewData["Required"] = false;
                ViewData["Icon"] = "fas fa-calendar-times";
                ViewData["Value"] = Model.ActualEndDate?.ToString("yyyy-MM-dd");
                ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("ActualEndDate");
                ViewData["ContainerClasses"] = "";
                ViewData["HelpText"] = "Set this when the project is actually completed";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />
        </div>

        <!-- Form Actions -->
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0 pt-6 border-t border-neutral-200 dark:border-dark-200">
            <div>
                @{
                    ViewData["Text"] = "Cancel";
                    ViewData["Variant"] = "secondary";
                    ViewData["Icon"] = "fas fa-times";
                    ViewData["Href"] = Url.Action("Details", new { id = Model.Id });
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
            <div>
                @{
                    ViewData["Text"] = "Save Changes";
                    ViewData["Variant"] = "primary";
                    ViewData["Icon"] = "fas fa-save";
                    ViewData["Type"] = "submit";
                    ViewData["Href"] = null;
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        $(document).ready(function() {
            // Add form validation styling
            $('form').on('submit', function(e) {
                var isValid = true;

                // Check required fields
                $(this).find('[required]').each(function() {
                    if (!$(this).val()) {
                        isValid = false;
                        $(this).addClass('border-danger-300 dark:border-danger-600');
                    } else {
                        $(this).removeClass('border-danger-300 dark:border-danger-600');
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    // Show error message
                    if (!$('.alert-danger-custom').length) {
                        $('form').prepend(`
                            <div class="alert-danger-custom mb-6">
                                <i class="fas fa-exclamation-circle text-danger-600 dark:text-danger-400"></i>
                                <div>
                                    <p class="font-medium">Validation Error</p>
                                    <p class="text-sm">Please fill in all required fields.</p>
                                </div>
                            </div>
                        `);
                    }
                }
            });

            // Date validation
            $('input[name="EndDate"], input[name="ActualEndDate"]').on('change', function() {
                var startDate = $('input[name="StartDate"]').val();
                var endDate = $(this).val();
                var fieldName = $(this).attr('name');

                if (startDate && endDate && new Date(endDate) < new Date(startDate)) {
                    $(this).addClass('border-danger-300 dark:border-danger-600');
                    if (!$(this).siblings('.text-danger-600').length) {
                        $(this).after(`<p class="text-sm text-danger-600 dark:text-danger-400 mt-1">${fieldName.replace('Date', ' date')} must be after start date</p>`);
                    }
                } else {
                    $(this).removeClass('border-danger-300 dark:border-danger-600');
                    $(this).siblings('.text-danger-600').remove();
                }
            });

            // Status change handling
            $('select[name="Status"]').on('change', function() {
                var status = $(this).val();
                var actualEndDateField = $('input[name="ActualEndDate"]');

                if (status === 'Completed') {
                    // Suggest setting actual end date if not already set
                    if (!actualEndDateField.val()) {
                        actualEndDateField.val(new Date().toISOString().split('T')[0]);
                        actualEndDateField.focus();
                    }
                }
            });
        });
    </script>
}
