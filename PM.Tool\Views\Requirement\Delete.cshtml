@model PM.Tool.Core.Entities.Requirement
@{
    ViewData["Title"] = "Delete Requirement";
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Requirements", Href = Url.Action("Index", "Requirement"), Icon = "fas fa-clipboard-list" },
        new { Text = Model.Title, Href = Url.Action("Details", new { id = Model.Id }), Icon = "fas fa-file-alt" },
        new { Text = "Delete", Href = "", Icon = "fas fa-trash" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Page Header -->
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <div class="flex items-center space-x-3 mb-2">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200">
                    <EMAIL>("D4")
                </span>
                @{
                    var statusClass = GetStatusTailwindClass(Model.Status);
                }
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @statusClass">
                    @Model.Status
                </span>
            </div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-exclamation-triangle mr-3 text-danger-600 dark:text-danger-400"></i>
                Delete Requirement
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                This action cannot be undone. Please confirm deletion.
            </p>
        </div>
        @{
            ViewData["Text"] = "Back to Requirements";
            ViewData["Variant"] = "secondary";
            ViewData["Icon"] = "fas fa-arrow-left";
            ViewData["Href"] = Url.Action("Index");
        }
        <partial name="Components/_Button" view-data="ViewData" />
    </div>
</div>

<!-- Warning Alert -->
<div class="bg-danger-50 dark:bg-danger-900/20 border border-danger-200 dark:border-danger-800 rounded-lg p-6 mb-8">
    <div class="flex items-start">
        <div class="flex-shrink-0">
            <i class="fas fa-exclamation-triangle text-danger-600 dark:text-danger-400 text-xl"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-lg font-medium text-danger-800 dark:text-danger-200 mb-2">
                Warning: Permanent Deletion
            </h3>
            <div class="text-danger-700 dark:text-danger-300 space-y-2">
                <p>You are about to permanently delete this requirement. This action will:</p>
                <ul class="list-disc list-inside space-y-1 ml-4">
                    <li>Remove the requirement and all its data</li>
                    <li>Delete all associated comments and attachments</li>
                    <li>Remove any task relationships</li>
                    <li>Break parent-child requirement relationships</li>
                    <li>Remove from any traceability matrices</li>
                </ul>
                <p class="font-medium">This action cannot be undone.</p>
            </div>
        </div>
    </div>
</div>

<!-- Requirement Details -->
<div class="card-custom">
    <div class="card-header-custom">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                <i class="fas fa-file-alt text-primary-600 dark:text-primary-400 text-sm"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Requirement Details</h3>
            </div>
        </div>
    </div>
    <div class="card-body-custom">
        <div class="space-y-6">
            <!-- Basic Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-1">Title</h4>
                    <p class="text-neutral-900 dark:text-dark-100">@Model.Title</p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-1">Project</h4>
                    <p class="text-neutral-900 dark:text-dark-100">@(Model.Project?.Name ?? "No Project")</p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-1">Type</h4>
                    <p class="text-neutral-900 dark:text-dark-100">@Model.Type</p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-1">Priority</h4>
                    <p class="text-neutral-900 dark:text-dark-100">@Model.Priority</p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-1">Status</h4>
                    <p class="text-neutral-900 dark:text-dark-100">@Model.Status</p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-1">Created</h4>
                    <p class="text-neutral-900 dark:text-dark-100">@Model.CreatedAt.ToString("MMM dd, yyyy")</p>
                </div>
            </div>

            <!-- Description -->
            <div>
                <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Description</h4>
                <div class="bg-neutral-50 dark:bg-dark-700 rounded-lg p-4">
                    <p class="text-neutral-900 dark:text-dark-100 leading-relaxed">@Model.Description</p>
                </div>
            </div>

            <!-- Assignments -->
            <div>
                <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-3">Assignments</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-user-tie text-neutral-400 dark:text-dark-500"></i>
                        <div>
                            <p class="text-xs text-neutral-500 dark:text-dark-400">Stakeholder</p>
                            <p class="text-sm text-neutral-900 dark:text-dark-100">@(Model.Stakeholder?.UserName ?? "Not Assigned")</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-user-chart text-neutral-400 dark:text-dark-500"></i>
                        <div>
                            <p class="text-xs text-neutral-500 dark:text-dark-400">Analyst</p>
                            <p class="text-sm text-neutral-900 dark:text-dark-100">@(Model.Analyst?.UserName ?? "Not Assigned")</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-code text-neutral-400 dark:text-dark-500"></i>
                        <div>
                            <p class="text-xs text-neutral-500 dark:text-dark-400">Developer</p>
                            <p class="text-sm text-neutral-900 dark:text-dark-100">@(Model.Developer?.UserName ?? "Not Assigned")</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-bug text-neutral-400 dark:text-dark-500"></i>
                        <div>
                            <p class="text-xs text-neutral-500 dark:text-dark-400">Tester</p>
                            <p class="text-sm text-neutral-900 dark:text-dark-100">@(Model.Tester?.UserName ?? "Not Assigned")</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Impact Analysis -->
@{
    ViewData["Title"] = "Impact Analysis";
    ViewData["Icon"] = "fas fa-exclamation-circle";
    ViewData["BodyContent"] = @"
        <div class='space-y-4'>
            <div class='flex items-center space-x-3 p-3 bg-warning-50 dark:bg-warning-900/20 rounded-lg'>
                <i class='fas fa-sitemap text-warning-600 dark:text-warning-400'></i>
                <div>
                    <p class='text-sm font-medium text-warning-800 dark:text-warning-200'>Child Requirements</p>
                    <p class='text-xs text-warning-700 dark:text-warning-300' id='childRequirementsCount'>Loading...</p>
                </div>
            </div>

            <div class='flex items-center space-x-3 p-3 bg-info-50 dark:bg-info-900/20 rounded-lg'>
                <i class='fas fa-tasks text-info-600 dark:text-info-400'></i>
                <div>
                    <p class='text-sm font-medium text-info-800 dark:text-info-200'>Related Tasks</p>
                    <p class='text-xs text-info-700 dark:text-info-300' id='relatedTasksCount'>Loading...</p>
                </div>
            </div>

            <div class='flex items-center space-x-3 p-3 bg-neutral-50 dark:bg-dark-700 rounded-lg'>
                <i class='fas fa-comments text-neutral-600 dark:text-dark-400'></i>
                <div>
                    <p class='text-sm font-medium text-neutral-800 dark:text-dark-200'>Comments & Attachments</p>
                    <p class='text-xs text-neutral-700 dark:text-dark-300' id='commentsAttachmentsCount'>Loading...</p>
                </div>
            </div>
        </div>
    ";
}
<partial name="Components/_Card" view-data="ViewData" />

<!-- Confirmation Form -->
<form asp-action="Delete" method="post" id="deleteForm" class="space-y-6">
    <input asp-for="Id" type="hidden" />

    @{
        ViewData["Title"] = "Confirmation Required";
        ViewData["Icon"] = "fas fa-shield-alt";
        ViewData["BodyContent"] = @"
            <div class='space-y-4'>
                <div class='bg-neutral-50 dark:bg-dark-700 rounded-lg p-4'>
                    <label for='confirmationText' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>
                        Type <strong>DELETE</strong> to confirm:
                    </label>
                    <input type='text' id='confirmationText' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-danger-500 focus:border-danger-500' placeholder='Type DELETE to confirm' autocomplete='off'>
                </div>

                <div class='flex items-start space-x-3'>
                    <input type='checkbox' id='confirmUnderstand' class='mt-1 h-4 w-4 text-danger-600 focus:ring-danger-500 border-neutral-300 dark:border-dark-600 rounded'>
                    <label for='confirmUnderstand' class='text-sm text-neutral-700 dark:text-dark-300'>
                        I understand that this action is permanent and cannot be undone.
                    </label>
                </div>
            </div>
        ";
    }
    <partial name="Components/_Card" view-data="ViewData" />

    <!-- Form Actions -->
    <div class="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3 pt-6 border-t border-neutral-200 dark:border-dark-600">
        @{
            ViewData["Text"] = "Cancel";
            ViewData["Variant"] = "secondary";
            ViewData["Href"] = Url.Action("Details", new { id = Model.Id });
        }
        <partial name="Components/_Button" view-data="ViewData" />

        @{
            ViewData["Text"] = "Delete Requirement";
            ViewData["Variant"] = "danger";
            ViewData["Type"] = "submit";
            ViewData["Icon"] = "fas fa-trash";
            ViewData["Id"] = "deleteButton";
            ViewData["Disabled"] = true;
        }
        <partial name="Components/_Button" view-data="ViewData" />
    </div>
</form>

@section Scripts {
    <script>
        $(document).ready(function() {
            loadImpactAnalysis();
            setupConfirmation();
        });

        function loadImpactAnalysis() {
            // Load child requirements count
            $.get('@Url.Action("GetChildRequirementsCount", "Requirement")', { id: @Model.Id })
                .done(function(count) {
                    $('#childRequirementsCount').text(count + ' child requirement(s) will be orphaned');
                })
                .fail(function() {
                    $('#childRequirementsCount').text('Unable to determine impact');
                });

            // Load related tasks count
            $.get('@Url.Action("GetRelatedTasksCount", "Requirement")', { id: @Model.Id })
                .done(function(count) {
                    $('#relatedTasksCount').text(count + ' task relationship(s) will be removed');
                })
                .fail(function() {
                    $('#relatedTasksCount').text('Unable to determine impact');
                });

            // Load comments and attachments count
            $.get('@Url.Action("GetCommentsAttachmentsCount", "Requirement")', { id: @Model.Id })
                .done(function(data) {
                    $('#commentsAttachmentsCount').text(data.comments + ' comment(s) and ' + data.attachments + ' attachment(s) will be deleted');
                })
                .fail(function() {
                    $('#commentsAttachmentsCount').text('Unable to determine impact');
                });
        }

        function setupConfirmation() {
            const confirmationText = $('#confirmationText');
            const confirmCheckbox = $('#confirmUnderstand');
            const deleteButton = $('#deleteButton');

            function checkConfirmation() {
                const textValid = confirmationText.val().toUpperCase() === 'DELETE';
                const checkboxValid = confirmCheckbox.is(':checked');

                if (textValid && checkboxValid) {
                    deleteButton.prop('disabled', false).removeClass('opacity-50 cursor-not-allowed');
                } else {
                    deleteButton.prop('disabled', true).addClass('opacity-50 cursor-not-allowed');
                }
            }

            confirmationText.on('input', checkConfirmation);
            confirmCheckbox.on('change', checkConfirmation);

            $('#deleteForm').on('submit', function(e) {
                if (!confirmationText.val() || confirmationText.val().toUpperCase() !== 'DELETE') {
                    e.preventDefault();
                    alert('Please type DELETE to confirm deletion.');
                    return false;
                }

                if (!confirmCheckbox.is(':checked')) {
                    e.preventDefault();
                    alert('Please confirm that you understand this action is permanent.');
                    return false;
                }

                // Final confirmation
                if (!confirm('Are you absolutely sure you want to delete this requirement? This action cannot be undone.')) {
                    e.preventDefault();
                    return false;
                }
            });
        }
    </script>
}

@functions {
    string GetStatusTailwindClass(PM.Tool.Core.Entities.RequirementStatus status)
    {
        return status switch
        {
            PM.Tool.Core.Entities.RequirementStatus.Draft => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200",
            PM.Tool.Core.Entities.RequirementStatus.UnderReview => "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
            PM.Tool.Core.Entities.RequirementStatus.Approved => "bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200",
            PM.Tool.Core.Entities.RequirementStatus.InProgress => "bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200",
            PM.Tool.Core.Entities.RequirementStatus.Completed => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
            PM.Tool.Core.Entities.RequirementStatus.OnHold => "bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200",
            _ => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200"
        };
    }
}
