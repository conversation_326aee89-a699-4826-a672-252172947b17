# 🚀 PM Tool - Enterprise Project Management Platform

[![.NET](https://img.shields.io/badge/.NET-9.0-blue.svg)](https://dotnet.microsoft.com/)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/build-****ing-brightgreen.svg)](https://github.com/your-org/pm-tool/actions)
[![Coverage](https://img.shields.io/badge/coverage-85%25-yellowgreen.svg)](https://codecov.io/gh/your-org/pm-tool)

PM Tool is a comprehensive, enterprise-grade project management platform built with ASP.NET Core 9.0. It provides complete project lifecycle management with advanced features including agile methodologies, multilingual support, and professional-grade UX/UI.

## ✨ Key Features

### 🌍 **Multilingual & Global**
- **26 Languages Supported**: English, Spanish, French, German, Italian, Portuguese, Russian, Chinese, Japanese, Korean, Arabic, Hebrew, and more
- **RTL Language Support**: Full right-to-left support for Arabic and Hebrew
- **Cultural Formatting**: Automatic date, number, and currency formatting per locale
- **Dynamic Language Switching**: Runtime language changes with user preferences

### 🏃‍♂️ **Complete Agile Framework**
- **Epic Management**: Business-level feature tracking with story point estimation
- **User Story Management**: Full user story lifecycle with acceptance criteria
- **Sprint Planning**: Time-boxed iterations with velocity tracking
- **Kanban Board**: Professional drag-and-drop board with real-time updates
- **Burndown Charts**: Sprint progress visualization and team velocity metrics

### 📊 **Comprehensive Project Management**
- **Project Lifecycle**: Complete project management from initiation to closure
- **Work Breakdown Structure**: Hierarchical task decomposition with WBS codes
- **Resource Management**: Team allocation, skill tracking, and utilization monitoring
- **Risk Management**: Risk register with probability/impact analysis and mitigation
- **Requirements Management**: Hierarchical requirements with traceability matrix

### 👥 **Advanced Collaboration**
- **Meeting Management**: Scheduling, agenda, minutes, and action item tracking
- **Stakeholder Management**: Influence/interest matrix and communication planning
- **Team Collaboration**: Real-time comments, @mentions, and activity feeds
- **Document Management**: Version control and organized file sharing

### 📈 **Analytics & Reporting**
- **Executive Dashboards**: High-level KPIs and project health indicators
- **Predictive Analytics**: Project completion forecasting and trend analysis
- **Custom Reports**: User-defined reports with multiple export formats
- **Performance Metrics**: Team productivity and individual performance tracking

### 🎨 **Professional UX/UI**
- **Modern Design System**: Consistent, accessible, and responsive interface
- **Dark/Light Themes**: User preference support with automatic detection
- **Mobile-First**: Fully responsive design for all device sizes
- **Accessibility**: WCAG 2.1 AA compliance with keyboard navigation

### 🔗 **Enterprise Integrations**
- **Version Control**: GitHub, GitLab integration for development projects
- **Communication**: Slack, Microsoft Teams notifications and updates
- **Calendar**: Outlook, Google Calendar synchronization
- **File Storage**: OneDrive, Google Drive, Dropbox integration

## 🏗️ Architecture

PM Tool follows Clean Architecture principles with clear separation of concerns:

```
├── PM.Tool.Core/           # Domain layer (entities, interfaces, business logic)
├── PM.Tool.Application/    # Application layer (services, DTOs, validators)
├── PM.Tool.Infrastructure/ # Infrastructure layer (external services, localization)
├── PM.Tool.Data/          # Data access layer (EF Core, repositories)
├── PM.Tool/               # Presentation layer (controllers, views, API)
└── PM.Tool.Tests/         # Test projects (unit, integration, E2E)
```

### Technology Stack
- **Backend**: ASP.NET Core 9.0, Entity Framework Core, PostgreSQL
- **Frontend**: Razor Pages, Bootstrap 5, Modern CSS Grid/Flexbox
- **Authentication**: ASP.NET Core Identity with JWT tokens
- **Caching**: Redis with fallback to in-memory cache
- **Logging**: Serilog with structured logging
- **Testing**: xUnit, Moq, FluentAssertions

## 🚀 Quick Start

### Prerequisites
- [.NET 9.0 SDK](https://dotnet.microsoft.com/download/dotnet/9.0)
- [PostgreSQL 12+](https://www.postgresql.org/download/) or SQL Server 2019+
- [Node.js 18+](https://nodejs.org/) (for frontend tooling)
- [Git](https://git-scm.com/)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/pm-tool.git
   cd pm-tool
   ```

2. **Set up the database**
   ```bash
   # PostgreSQL
   createdb pmtool_dev

   # Update connection string in appsettings.Development.json
   ```

3. **Install dependencies**
   ```bash
   dotnet restore
   npm install
   ```

4. **Run database migrations**
   ```bash
   dotnet ef database update --project PM.Tool.Data
   ```

5. **Start the application**
   ```bash
   dotnet run --project PM.Tool
   ```

6. **Access the application**
   - Open your browser to `https://localhost:5001`
   - Default admin credentials: `<EMAIL>` / `Admin123!`

## 📚 Documentation

### For Users
- **[User Guide](Documentation/USER_GUIDE.md)** - Complete user manual with step-by-step instructions
- **[Project Manager Guide](Documentation/PROJECT_MANAGER_GUIDE.md)** - Advanced features for project managers

### For Administrators
- **[System Admin Guide](Documentation/SYSTEM_ADMIN_GUIDE.md)** - Installation, configuration, and maintenance
- **[Deployment Guide](Documentation/DEPLOYMENT_GUIDE.md)** - Production deployment across different platforms

### For Developers
- **[Developer Guide](Documentation/DEVELOPER_GUIDE.md)** - Architecture, coding standards, and contribution guidelines
- **[API Documentation](Documentation/API_DOCUMENTATION.md)** - Complete REST API reference

### System Overview
- **[System Overview](Documentation/SYSTEM_OVERVIEW.md)** - Comprehensive system architecture and features

## 🐳 Docker Deployment

### Quick Start with Docker Compose
```bash
# Clone and navigate to project
git clone https://github.com/your-org/pm-tool.git
cd pm-tool

# Start all services
docker-compose up -d

# Access application at http://localhost
```

### Production Deployment
```bash
# Use production compose file
docker-compose -f docker-compose.prod.yml up -d
```

## ☁️ Cloud Deployment

### Azure
```bash
az webapp create --resource-group pmtool-rg --plan pmtool-plan --name pmtool-app --runtime "DOTNETCORE:9.0"
```

### AWS
```bash
eb init pmtool --platform "64bit Amazon Linux 2 v2.2.0 running .NET Core"
eb create pmtool-prod
```

### Google Cloud
```bash
gcloud app deploy app.yaml
```

## 🧪 Testing

### Run All Tests
```bash
dotnet test
```

### Run Specific Test Categories
```bash
# Unit tests only
dotnet test --filter Category=Unit

# Integration tests only
dotnet test --filter Category=Integration

# E2E tests only
dotnet test --filter Category=E2E
```

### Code Coverage
```bash
dotnet test --collect:"XPlat Code Coverage"
reportgenerator -reports:"**/coverage.cobertura.xml" -targetdir:"coverage-report"
```

## 🔧 Configuration

### Environment Variables
```bash
# Database
ConnectionStrings__DefaultConnection="Host=localhost;Database=pmtool;Username=user;Password=****"

# JWT Settings
JwtSettings__SecretKey="your-256-bit-secret-key"
JwtSettings__ExpirationMinutes=60

# Email Settings
EmailSettings__SmtpServer="smtp.gmail.com"
EmailSettings__SmtpPort=587
EmailSettings__SmtpUsername="<EMAIL>"
EmailSettings__SmtpPassword="your-app-****word"

# Feature Flags
FeatureFlags__EnableAgileFeatures=true
FeatureFlags__EnableMultiLanguage=true
```

### Configuration Files
- `appsettings.json` - Base configuration
- `appsettings.Development.json` - Development overrides
- `appsettings.Production.json` - Production settings

## 🌍 Localization

### Supported Languages
| Language | Code | RTL | Status |
|----------|------|-----|--------|
| English (US) | en-US | No | ✅ Complete |
| English (UK) | en-GB | No | ✅ Complete |
| Spanish (Spain) | es-ES | No | ✅ Complete |
| Spanish (Mexico) | es-MX | No | ✅ Complete |
| French | fr-FR | No | ✅ Complete |
| German | de-DE | No | ✅ Complete |
| Italian | it-IT | No | ✅ Complete |
| Portuguese (Brazil) | pt-BR | No | ✅ Complete |
| Portuguese (Portugal) | pt-PT | No | ✅ Complete |
| Russian | ru-RU | No | ✅ Complete |
| Chinese (Simplified) | zh-CN | No | ✅ Complete |
| Chinese (Traditional) | zh-TW | No | ✅ Complete |
| Japanese | ja-JP | No | ✅ Complete |
| Korean | ko-KR | No | ✅ Complete |
| Arabic | ar-SA | Yes | ✅ Complete |
| Hebrew | he-IL | Yes | ✅ Complete |
| Hindi | hi-IN | No | ✅ Complete |
| Bengali (Bangladesh) | bn-BD | No | ✅ Complete |
| Thai | th-TH | No | ✅ Complete |
| Vietnamese | vi-VN | No | ✅ Complete |
| Turkish | tr-TR | No | ✅ Complete |
| Polish | pl-PL | No | ✅ Complete |
| Dutch | nl-NL | No | ✅ Complete |
| Swedish | sv-SE | No | ✅ Complete |
| Danish | da-DK | No | ✅ Complete |
| Norwegian | no-NO | No | ✅ Complete |
| Finnish | fi-FI | No | ✅ Complete |

### Adding New Languages
1. Create resource file: `Resources/SharedResources.{culture}.resx`
2. Add culture to supported cultures in `Program.cs`
3. Test RTL support if applicable

## 🔒 Security

### Security Features
- **Authentication**: ASP.NET Core Identity with multi-factor authentication
- **Authorization**: Role-based and resource-based access control
- **Data Protection**: Encryption at rest and in transit
- **Audit Logging**: Comprehensive activity tracking
- **Security Headers**: HSTS, CSP, X-Frame-Options, etc.
- **Rate Limiting**: API and file upload protection

### Security Best Practices
- Use strong ****words and enable 2FA
- Keep dependencies updated
- Regular security audits
- Implement proper backup procedures
- Monitor for suspicious activities

## 📊 Performance

### Performance Features
- **Caching**: Multi-level caching with Redis support
- **Database Optimization**: Proper indexing and query optimization
- **CDN Support**: Static asset delivery optimization
- **Compression**: Gzip compression for responses
- **Lazy Loading**: Efficient data loading strategies

### Performance Monitoring
- Application Insights integration
- Custom performance counters
- Database query monitoring
- Real-time health checks

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### Development Workflow
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes and add tests
4. Ensure all tests ****: `dotnet test`
5. Commit your changes: `git commit -m 'Add amazing feature'`
6. Push to the branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

### Code Standards
- Follow C# coding conventions
- Write comprehensive tests
- Update documentation
- Ensure accessibility compliance

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Getting Help
- **Documentation**: Check the comprehensive guides in the `Documentation/` folder
- **Issues**: Report bugs and request features on [GitHub Issues](https://github.com/your-org/pm-tool/issues)
- **Discussions**: Join community discussions on [GitHub Discussions](https://github.com/your-org/pm-tool/discussions)
- **Email**: Contact <NAME_EMAIL>

### Community
- **Discord**: Join our [Discord server](https://discord.gg/pmtool)
- **Twitter**: Follow us [@PMToolApp](https://twitter.com/PMToolApp)
- **LinkedIn**: Connect on [LinkedIn](https://linkedin.com/company/pmtool)

## 🙏 Acknowledgments

- Built with [ASP.NET Core](https://docs.microsoft.com/en-us/aspnet/core/)
- UI components from [Bootstrap](https://getbootstrap.com/)
- Icons from [Font Awesome](https://fontawesome.com/)
- Charts powered by [Chart.js](https://www.chartjs.org/)
- Localization resources from community contributors

---

**PM Tool** - Empowering teams to deliver exceptional projects, anywhere in the world. 🌍

*Made with ❤️ by the PM Tool team*
