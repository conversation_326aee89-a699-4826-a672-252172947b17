@model PM.Tool.Core.Entities.Agile.UserStory
@{
    ViewData["Title"] = $"User Story: {Model.Title}";
    var project = ViewBag.Project as PM.Tool.Core.Entities.Project;
    var comments = ViewBag.Comments as IEnumerable<PM.Tool.Core.Entities.Agile.UserStoryComment> ?? new List<PM.Tool.Core.Entities.Agile.UserStoryComment>();
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="@Url.Action("Index", "Projects")" class="text-neutral-500 hover:text-neutral-700 dark:text-dark-400 dark:hover:text-dark-200">
                            <i class="fas fa-project-diagram mr-2"></i>Projects
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-neutral-400 dark:text-dark-500 mx-2"></i>
                            <a href="@Url.Action("Details", "Projects", new { id = project?.Id })" class="text-neutral-500 hover:text-neutral-700 dark:text-dark-400 dark:hover:text-dark-200">
                                @project?.Name
                            </a>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-neutral-400 dark:text-dark-500 mx-2"></i>
                            <a href="@Url.Action("Backlog", new { projectId = project?.Id })" class="text-neutral-500 hover:text-neutral-700 dark:text-dark-400 dark:hover:text-dark-200">
                                Backlog
                            </a>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-neutral-400 dark:text-dark-500 mx-2"></i>
                            <span class="text-neutral-700 dark:text-dark-300">@Model.StoryKey</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <div class="flex items-center space-x-4 mb-2">
                <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100">@Model.Title</h1>
                @{
                    var statusColor = Model.Status.ToString().ToLower() switch {
                        "backlog" => "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300",
                        "ready" => "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300",
                        "inprogress" => "bg-warning-100 text-warning-700 dark:bg-warning-900 dark:text-warning-300",
                        "review" => "bg-info-100 text-info-700 dark:bg-info-900 dark:text-info-300",
                        "done" => "bg-success-100 text-success-700 dark:bg-success-900 dark:text-success-300",
                        "cancelled" => "bg-danger-100 text-danger-700 dark:bg-danger-900 dark:text-danger-300",
                        _ => "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300"
                    };
                    var priorityColor = Model.Priority.ToString().ToLower() switch {
                        "critical" => "bg-danger-100 text-danger-700 dark:bg-danger-900 dark:text-danger-300",
                        "high" => "bg-warning-100 text-warning-700 dark:bg-warning-900 dark:text-warning-300",
                        "medium" => "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300",
                        "low" => "bg-success-100 text-success-700 dark:bg-success-900 dark:text-success-300",
                        _ => "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300"
                    };
                }
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @statusColor">
                    @Model.Status
                </span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @priorityColor">
                    @Model.Priority Priority
                </span>
                @if (Model.StoryPoints > 0)
                {
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300">
                        @Model.StoryPoints SP
                    </span>
                }
            </div>

            <p class="text-sm text-neutral-500 dark:text-dark-400">
                Story @Model.StoryKey • Created @Model.CreatedAt.ToString("MMM dd, yyyy")
                @if (Model.TargetDate.HasValue)
                {
                    <span> • Target: @Model.TargetDate.Value.ToString("MMM dd, yyyy")</span>
                }
            </p>
        </div>

        <div class="mt-4 sm:mt-0 flex space-x-3">
            @{
                ViewData["Text"] = "Edit Story";
                ViewData["Variant"] = "outline";
                ViewData["Icon"] = "fas fa-edit";
                ViewData["Href"] = Url.Action("EditUserStory", new { id = Model.Id });
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- User Story Overview -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-8">
        <!-- User Story Format -->
        @{
            ViewData["Title"] = "User Story";
            ViewData["Icon"] = "fas fa-user-story";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-lg p-6">
                <div class="prose dark:prose-invert max-w-none">
                    <p class="text-lg text-primary-800 dark:text-primary-200 font-medium leading-relaxed">
                        @Model.UserStoryFormat
                    </p>
                </div>
            </div>
        </partial>

        <!-- Description -->
        @if (!string.IsNullOrEmpty(Model.Description))
        {
            ViewData["Title"] = "Description";
            ViewData["Icon"] = "fas fa-align-left";
            <partial name="Components/_Card" view-data="ViewData">
                <div class="prose dark:prose-invert max-w-none">
                    <p class="text-neutral-700 dark:text-dark-300 leading-relaxed">@Model.Description</p>
                </div>
            </partial>
        }

        <!-- Acceptance Criteria -->
        @if (!string.IsNullOrEmpty(Model.AcceptanceCriteria))
        {
            ViewData["Title"] = "Acceptance Criteria";
            ViewData["Icon"] = "fas fa-check-circle";
            <partial name="Components/_Card" view-data="ViewData">
                <div class="prose dark:prose-invert max-w-none">
                    <div class="space-y-2">
                        @foreach (var criterion in Model.AcceptanceCriteria.Split('\n', StringSplitOptions.RemoveEmptyEntries))
                        {
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-check text-success-600 dark:text-success-400 mt-1 text-sm"></i>
                                <span class="text-neutral-700 dark:text-dark-300">@criterion.Trim()</span>
                            </div>
                        }
                    </div>
                </div>
            </partial>
        }

        <!-- Comments -->
        @{
            ViewData["Title"] = $"Comments ({comments.Count()})";
            ViewData["Icon"] = "fas fa-comments";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="space-y-6">
                <!-- Add Comment Form -->
                <div class="border-b border-neutral-200 dark:border-dark-200 pb-6">
                    <form id="addCommentForm" class="space-y-4">
                        <div>
                            <label for="commentContent" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">
                                Add a comment
                            </label>
                            <textarea id="commentContent" name="content" rows="3"
                                      class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-800 dark:text-dark-100"
                                      placeholder="Write your comment here..."></textarea>
                        </div>
                        <div class="flex justify-end">
                            @{
                                ViewData["Text"] = "Add Comment";
                                ViewData["Variant"] = "primary";
                                ViewData["Icon"] = "fas fa-plus";
                                ViewData["Type"] = "submit";
                            }
                            <partial name="Components/_Button" view-data="ViewData" />
                        </div>
                    </form>
                </div>

                <!-- Comments List -->
                <div id="commentsList" class="space-y-4">
                    @if (comments.Any())
                    {
                        @foreach (var comment in comments.OrderByDescending(c => c.CreatedAt))
                        {
                            <div class="flex space-x-4">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                                        <span class="text-sm font-medium text-primary-600 dark:text-primary-400">
                                            @comment.User.UserName?.Substring(0, 1).ToUpper()
                                        </span>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2 mb-1">
                                        <span class="text-sm font-medium text-neutral-900 dark:text-dark-100">@comment.User.UserName</span>
                                        <span class="text-xs text-neutral-500 dark:text-dark-400">@comment.CreatedAt.ToString("MMM dd, yyyy HH:mm")</span>
                                        @if (comment.Type != PM.Tool.Core.Entities.Agile.CommentType.General)
                                        {
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-info-100 text-info-700 dark:bg-info-900 dark:text-info-300">
                                                @comment.Type
                                            </span>
                                        }
                                    </div>
                                    <div class="text-sm text-neutral-700 dark:text-dark-300">
                                        @comment.Content
                                    </div>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-8">
                            <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-comments text-2xl text-neutral-400 dark:text-dark-500"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">No Comments</h3>
                            <p class="text-neutral-500 dark:text-dark-400">Be the first to comment on this user story.</p>
                        </div>
                    }
                </div>
            </div>
        </partial>
    </div>

    <!-- Sidebar -->
    <div class="space-y-6">
        <!-- Story Information -->
        @{
            ViewData["Title"] = "Story Information";
            ViewData["Icon"] = "fas fa-info-circle";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <dl class="space-y-4">
                <div>
                    <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">Story Key</dt>
                    <dd class="text-sm text-neutral-900 dark:text-dark-100 font-mono">@Model.StoryKey</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">Status</dt>
                    <dd class="text-sm text-neutral-900 dark:text-dark-100">@Model.Status</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">Priority</dt>
                    <dd class="text-sm text-neutral-900 dark:text-dark-100">@Model.Priority</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">Story Points</dt>
                    <dd class="text-sm text-neutral-900 dark:text-dark-100">@(Model.StoryPoints > 0 ? Model.StoryPoints.ToString() : "Not estimated")</dd>
                </div>
                @if (Model.Epic != null)
                {
                    <div>
                        <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">Epic</dt>
                        <dd class="text-sm text-neutral-900 dark:text-dark-100">
                            <a href="@Url.Action("EpicDetails", new { id = Model.Epic.Id })" class="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300">
                                @Model.Epic.Title
                            </a>
                        </dd>
                    </div>
                }
                @if (Model.Sprint != null)
                {
                    <div>
                        <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">Sprint</dt>
                        <dd class="text-sm text-neutral-900 dark:text-dark-100">
                            <a href="@Url.Action("SprintDetails", new { id = Model.Sprint.Id })" class="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300">
                                @Model.Sprint.Name
                            </a>
                        </dd>
                    </div>
                }
                @if (Model.AssignedTo != null)
                {
                    <div>
                        <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">Assigned To</dt>
                        <dd class="text-sm text-neutral-900 dark:text-dark-100">@Model.AssignedTo.UserName</dd>
                    </div>
                }
                <div>
                    <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">Created</dt>
                    <dd class="text-sm text-neutral-900 dark:text-dark-100">@Model.CreatedAt.ToString("MMM dd, yyyy")</dd>
                </div>
                @if (Model.UpdatedAt.HasValue)
                {
                    <div>
                        <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">Last Updated</dt>
                        <dd class="text-sm text-neutral-900 dark:text-dark-100">@Model.UpdatedAt.Value.ToString("MMM dd, yyyy")</dd>
                    </div>
                }
                @if (Model.TargetDate.HasValue)
                {
                    <div>
                        <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">Target Date</dt>
                        <dd class="text-sm text-neutral-900 dark:text-dark-100">@Model.TargetDate.Value.ToString("MMM dd, yyyy")</dd>
                    </div>
                }
            </dl>
        </partial>

        <!-- Actions -->
        @{
            ViewData["Title"] = "Actions";
            ViewData["Icon"] = "fas fa-cog";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="space-y-3">
                @{
                    ViewData["Text"] = "Edit Story";
                    ViewData["Variant"] = "outline";
                    ViewData["Icon"] = "fas fa-edit";
                    ViewData["Href"] = Url.Action("EditUserStory", new { id = Model.Id });
                    ViewData["Classes"] = "w-full justify-center";
                }
                <partial name="Components/_Button" view-data="ViewData" />

                <form asp-action="DeleteUserStory" asp-route-id="@Model.Id" method="post" class="delete-form">
                    @Html.AntiForgeryToken()
                    @{
                        ViewData["Text"] = "Delete Story";
                        ViewData["Variant"] = "danger";
                        ViewData["Icon"] = "fas fa-trash";
                        ViewData["Type"] = "submit";
                        ViewData["Classes"] = "w-full justify-center";
                        ViewData["OnClick"] = "return confirm('Are you sure you want to delete this user story? This action cannot be undone.')";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </form>
            </div>
        </partial>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const addCommentForm = document.getElementById('addCommentForm');
            const commentsList = document.getElementById('commentsList');

            addCommentForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                const content = document.getElementById('commentContent').value.trim();
                if (!content) return;

                try {
                    const response = await fetch('@Url.Action("AddComment")', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                        },
                        body: JSON.stringify({
                            userStoryId: @Model.Id,
                            content: content,
                            type: 0 // General
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        // Clear the form
                        document.getElementById('commentContent').value = '';

                        // Reload the page to show the new comment
                        location.reload();
                    } else {
                        alert('Failed to add comment: ' + (result.message || 'Unknown error'));
                    }
                } catch (error) {
                    console.error('Error adding comment:', error);
                    alert('Failed to add comment. Please try again.');
                }
            });
        });
    </script>
}
