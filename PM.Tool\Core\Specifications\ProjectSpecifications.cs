using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;

namespace PM.Tool.Core.Specifications
{
    public class ProjectsByUserSpec : BaseSpecification<Project>
    {
        public ProjectsByUserSpec(string userId, bool includeInactive = false)
            : base(p => !p.IsDeleted &&
                       (includeInactive || p.Status == ProjectStatus.Active || p.Status == ProjectStatus.InProgress || p.Status == ProjectStatus.Planning) &&
                       (p.CreatedByUserId == userId || p.Members.Any(m => m.UserId == userId && m.IsActive)))
        {
            AddInclude(p => p.CreatedBy);
            AddInclude(p => p.Manager);
            AddInclude(p => p.Members.Where(m => m.IsActive));
            AddInclude(p => p.Tasks.Where(t => !t.IsDeleted));
            AddIncludeString("Tasks.AssignedTo");
            ApplyOrderByDescending(p => p.UpdatedAt);
        }
    }

    public class ActiveProjectsWithMilestonesSpec : BaseSpecification<Project>
    {
        public ActiveProjectsWithMilestonesSpec()
            : base(p => !p.IsDeleted && p.Status == ProjectStatus.Active)
        {
            AddInclude(p => p.Milestones.Where(m => !m.IsDeleted));
            AddInclude(p => p.Members.Where(m => m.IsActive));
            ApplyOrderBy(p => p.EndDate);
        }
    }

    public class ProjectWithFullDetailsSpec : BaseSpecification<Project>
    {
        public ProjectWithFullDetailsSpec(int projectId)
            : base(p => p.Id == projectId && !p.IsDeleted)
        {
            AddInclude(p => p.CreatedBy);
            AddInclude(p => p.Manager);
            AddInclude(p => p.Members.Where(m => m.IsActive));
            AddInclude(p => p.Tasks.Where(t => !t.IsDeleted));
            AddIncludeString("Tasks.AssignedTo");
            AddInclude(p => p.Milestones.Where(m => !m.IsDeleted));
        }
    }
}
