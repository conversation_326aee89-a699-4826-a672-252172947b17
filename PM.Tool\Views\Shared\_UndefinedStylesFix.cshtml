<style>
    /* Fix for undefined colors and styles throughout the application */

    /* Progress Bar Colors - Missing from WBS */
    .progress-bar-red {
        background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
    }

    .progress-bar-orange {
        background: linear-gradient(90deg, #f97316 0%, #ea580c 100%);
    }

    .progress-bar-yellow {
        background: linear-gradient(90deg, #eab308 0%, #ca8a04 100%);
    }

    .progress-bar-blue {
        background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
    }

    .progress-bar-green {
        background: linear-gradient(90deg, #22c55e 0%, #16a34a 100%);
    }

    /* WBS Code Badge Styles */
    .wbs-code {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 600;
        font-family: 'Courier New', monospace;
        border: 1px solid rgba(0, 0, 0, 0.1);
    }

    /* Task Title Styles */
    .task-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
        margin: 0 0 0.75rem 0;
        line-height: 1.4;
    }

    .dark .task-title {
        color: #f9fafb;
    }

    /* Task Description Styles */
    .task-description {
        font-size: 0.875rem;
        color: #6b7280;
        margin: 0.5rem 0;
        line-height: 1.5;
    }

    .dark .task-description {
        color: #d1d5db;
    }

    /* Task Details Styles */
    .task-details {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e5e7eb;
    }

    .dark .task-details {
        border-top-color: #374151;
    }

    /* Progress Container */
    .progress-container {
        width: 100%;
        height: 0.5rem;
        background-color: #e5e7eb;
        border-radius: 9999px;
        overflow: hidden;
    }

    .dark .progress-container {
        background-color: #374151;
    }

    /* Compact View Styles */
    .compact-view .wbs-node {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .compact-view .task-title {
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }

    .compact-view .task-description {
        font-size: 0.8125rem;
    }

    .compact-view .wbs-stat-card {
        padding: 0.75rem;
    }

    .compact-view .wbs-stat-card .stat-value {
        font-size: 1.25rem;
    }

    /* Detailed View Styles */
    .detailed-view .task-details {
        display: block !important;
    }

    .detailed-view .wbs-node {
        padding: 2.5rem;
    }

    /* Animation Keyframes */
    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @@keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @@keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.7;
        }
    }

    /* Modal Overlay Styles */
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(4px);
        z-index: 9998;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 1rem;
    }

    .modal-content {
        background: white;
        border-radius: 1rem;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        max-width: 90vw;
        max-height: 90vh;
        overflow-y: auto;
        position: relative;
        z-index: 9999;
    }

    .dark .modal-content {
        background: #1f2937;
        border: 1px solid #374151;
    }

    /* Select2 Theme Fixes */
    .select2-container--tailwind .select2-selection--single {
        height: 42px !important;
        border: 1px solid #d1d5db !important;
        border-radius: 0.5rem !important;
        background-color: white !important;
    }

    .dark .select2-container--tailwind .select2-selection--single {
        background-color: #374151 !important;
        border-color: #4b5563 !important;
        color: white !important;
    }

    .select2-container--tailwind .select2-selection__rendered {
        line-height: 40px !important;
        padding-left: 12px !important;
        color: #1f2937 !important;
    }

    .dark .select2-container--tailwind .select2-selection__rendered {
        color: #f9fafb !important;
    }

    /* Focus States */
    .action-btn:focus,
    .menu-item:focus,
    .dropdown-item:focus {
        outline: 2px solid #3b82f6;
        outline-offset: 2px;
    }

    /* High Contrast Mode Support */
    @@media (prefers-contrast: high) {
        .wbs-node {
            border-width: 2px;
        }

        .priority-badge,
        .status-badge {
            border-width: 1px;
            border-style: solid;
        }
    }

    /* Print Styles */
    @@media print {
        .no-print {
            display: none !important;
        }

        .wbs-node {
            break-inside: avoid;
            margin-bottom: 1rem;
        }

        .task-actions {
            display: none !important;
        }
    }

    /* Loading States */
    .loading-spinner {
        display: inline-block;
        width: 1rem;
        height: 1rem;
        border: 2px solid #e5e7eb;
        border-radius: 50%;
        border-top-color: #3b82f6;
        animation: spin 1s ease-in-out infinite;
    }

    @@keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }

    /* Error States */
    .error-state {
        color: #dc2626;
        background-color: #fef2f2;
        border: 1px solid #fecaca;
        border-radius: 0.5rem;
        padding: 1rem;
        text-align: center;
    }

    .dark .error-state {
        color: #fca5a5;
        background-color: #7f1d1d;
        border-color: #991b1b;
    }

    /* Empty States */
    .empty-state {
        text-align: center;
        padding: 3rem 1rem;
        color: #6b7280;
    }

    .dark .empty-state {
        color: #9ca3af;
    }

    .empty-state-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    /* Utility Classes */
    .text-truncate {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .visually-hidden {
        position: absolute !important;
        width: 1px !important;
        height: 1px !important;
        padding: 0 !important;
        margin: -1px !important;
        overflow: hidden !important;
        clip: rect(0, 0, 0, 0) !important;
        white-space: nowrap !important;
        border: 0 !important;
    }
</style>
