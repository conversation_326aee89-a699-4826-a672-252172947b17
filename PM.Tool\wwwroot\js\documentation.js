/**
 * Professional Documentation Application
 * Handles search, navigation, theming, and interactive features
 */

class DocsApp {
    constructor(options = {}) {
        this.options = {
            baseUrl: '/docs',
            enableSearch: true,
            enableFeedback: true,
            searchDelay: 300,
            ...options
        };
        
        this.searchTimeout = null;
        this.currentTheme = localStorage.getItem('docs-theme') || 'light';
        this.searchResults = [];
        this.activeSearchIndex = -1;
    }

    static init(options = {}) {
        const app = new DocsApp(options);
        app.initialize();
        return app;
    }

    initialize() {
        this.setupTheme();
        this.setupSearch();
        this.setupNavigation();
        this.setupTableOfContents();
        this.setupKeyboardShortcuts();
        this.setupScrollSpy();
        this.setupCodeBlocks();
        this.setupExternalLinks();
        this.setupPrintSupport();
        this.setupMobileMenu();
        
        console.log('Documentation app initialized');
    }

    // Theme Management
    setupTheme() {
        const themeToggle = document.getElementById('theme-toggle');
        const html = document.documentElement;
        
        // Apply saved theme
        html.setAttribute('data-theme', this.currentTheme);
        this.updateThemeIcon();
        
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        document.documentElement.setAttribute('data-theme', this.currentTheme);
        localStorage.setItem('docs-theme', this.currentTheme);
        this.updateThemeIcon();
    }

    updateThemeIcon() {
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            const icon = themeToggle.querySelector('i');
            if (icon) {
                icon.className = this.currentTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
        }
    }

    // Search Functionality
    setupSearch() {
        if (!this.options.enableSearch) return;

        const searchInput = document.getElementById('docs-search');
        const searchResults = document.getElementById('search-results');

        if (!searchInput || !searchResults) return;

        searchInput.addEventListener('input', (e) => {
            clearTimeout(this.searchTimeout);
            const query = e.target.value.trim();

            if (query.length < 2) {
                this.hideSearchResults();
                return;
            }

            this.searchTimeout = setTimeout(() => {
                this.performSearch(query);
            }, this.options.searchDelay);
        });

        searchInput.addEventListener('keydown', (e) => {
            this.handleSearchKeydown(e);
        });

        searchInput.addEventListener('focus', () => {
            if (this.searchResults.length > 0) {
                this.showSearchResults();
            }
        });

        // Hide search results when clicking outside
        document.addEventListener('click', (e) => {
            if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
                this.hideSearchResults();
            }
        });
    }

    async performSearch(query) {
        try {
            const response = await fetch(`${this.options.baseUrl}/api/search?q=${encodeURIComponent(query)}`);
            const data = await response.json();
            
            this.searchResults = data.results || [];
            this.activeSearchIndex = -1;
            this.displaySearchResults();
        } catch (error) {
            console.error('Search error:', error);
            this.searchResults = [];
            this.hideSearchResults();
        }
    }

    displaySearchResults() {
        const searchResults = document.getElementById('search-results');
        if (!searchResults) return;

        if (this.searchResults.length === 0) {
            searchResults.innerHTML = '<div class="search-no-results">No results found</div>';
        } else {
            searchResults.innerHTML = this.searchResults.map((result, index) => `
                <div class="search-result-item" data-index="${index}" onclick="docsApp.navigateToResult(${index})">
                    <div class="search-result-section">${result.sectionTitle}</div>
                    <div class="search-result-title">${this.highlightSearchTerms(result.pageTitle, result.matchedTerms)}</div>
                    <div class="search-result-excerpt">${this.highlightSearchTerms(result.excerpt, result.matchedTerms)}</div>
                </div>
            `).join('');
        }

        this.showSearchResults();
    }

    highlightSearchTerms(text, terms) {
        if (!terms || terms.length === 0) return text;
        
        let highlightedText = text;
        terms.forEach(term => {
            const regex = new RegExp(`(${this.escapeRegex(term)})`, 'gi');
            highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
        });
        
        return highlightedText;
    }

    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    handleSearchKeydown(e) {
        const searchResults = document.getElementById('search-results');
        if (!searchResults || this.searchResults.length === 0) return;

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                this.activeSearchIndex = Math.min(this.activeSearchIndex + 1, this.searchResults.length - 1);
                this.updateActiveSearchResult();
                break;
            case 'ArrowUp':
                e.preventDefault();
                this.activeSearchIndex = Math.max(this.activeSearchIndex - 1, -1);
                this.updateActiveSearchResult();
                break;
            case 'Enter':
                e.preventDefault();
                if (this.activeSearchIndex >= 0) {
                    this.navigateToResult(this.activeSearchIndex);
                }
                break;
            case 'Escape':
                this.hideSearchResults();
                e.target.blur();
                break;
        }
    }

    updateActiveSearchResult() {
        const items = document.querySelectorAll('.search-result-item');
        items.forEach((item, index) => {
            item.classList.toggle('active', index === this.activeSearchIndex);
        });
    }

    navigateToResult(index) {
        if (index >= 0 && index < this.searchResults.length) {
            const result = this.searchResults[index];
            window.location.href = result.url;
        }
    }

    showSearchResults() {
        const searchResults = document.getElementById('search-results');
        if (searchResults) {
            searchResults.style.display = 'block';
        }
    }

    hideSearchResults() {
        const searchResults = document.getElementById('search-results');
        if (searchResults) {
            searchResults.style.display = 'none';
        }
        this.activeSearchIndex = -1;
    }

    // Navigation
    setupNavigation() {
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                if (target) {
                    const headerHeight = document.querySelector('.docs-header')?.offsetHeight || 0;
                    const targetPosition = target.offsetTop - headerHeight - 20;
                    
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Active navigation highlighting
        this.updateActiveNavigation();
        window.addEventListener('scroll', () => {
            this.updateActiveNavigation();
        });
    }

    updateActiveNavigation() {
        const sections = document.querySelectorAll('.docs-content h1, .docs-content h2, .docs-content h3');
        const navLinks = document.querySelectorAll('.docs-toc-nav a');
        
        let activeSection = null;
        const scrollPosition = window.scrollY + 100;

        sections.forEach(section => {
            if (section.offsetTop <= scrollPosition) {
                activeSection = section;
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (activeSection && link.getAttribute('href') === `#${activeSection.id}`) {
                link.classList.add('active');
            }
        });
    }

    // Table of Contents
    setupTableOfContents() {
        const tocLinks = document.querySelectorAll('.docs-toc-nav a');
        
        tocLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                const target = document.getElementById(targetId);
                
                if (target) {
                    const headerHeight = document.querySelector('.docs-header')?.offsetHeight || 0;
                    const targetPosition = target.offsetTop - headerHeight - 20;
                    
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            });
        });
    }

    // Keyboard Shortcuts
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K for search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.getElementById('docs-search');
                if (searchInput) {
                    searchInput.focus();
                }
            }

            // Escape to close search
            if (e.key === 'Escape') {
                this.hideSearchResults();
                const searchInput = document.getElementById('docs-search');
                if (searchInput && document.activeElement === searchInput) {
                    searchInput.blur();
                }
            }

            // Alt + T for theme toggle
            if (e.altKey && e.key === 't') {
                e.preventDefault();
                this.toggleTheme();
            }
        });
    }

    // Scroll Spy
    setupScrollSpy() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const id = entry.target.id;
                    const tocLink = document.querySelector(`.docs-toc-nav a[href="#${id}"]`);
                    
                    // Remove active class from all links
                    document.querySelectorAll('.docs-toc-nav a').forEach(link => {
                        link.classList.remove('active');
                    });
                    
                    // Add active class to current link
                    if (tocLink) {
                        tocLink.classList.add('active');
                    }
                }
            });
        }, {
            rootMargin: '-100px 0px -80% 0px'
        });

        // Observe all headings
        document.querySelectorAll('.docs-content h1, .docs-content h2, .docs-content h3, .docs-content h4, .docs-content h5, .docs-content h6').forEach(heading => {
            if (heading.id) {
                observer.observe(heading);
            }
        });
    }

    // Code Blocks
    setupCodeBlocks() {
        // Add copy buttons to code blocks
        document.querySelectorAll('pre code').forEach(codeBlock => {
            const pre = codeBlock.parentElement;
            const copyButton = document.createElement('button');
            copyButton.className = 'code-copy-btn';
            copyButton.innerHTML = '<i class="fas fa-copy"></i>';
            copyButton.title = 'Copy code';
            
            copyButton.addEventListener('click', async () => {
                try {
                    await navigator.clipboard.writeText(codeBlock.textContent);
                    copyButton.innerHTML = '<i class="fas fa-check"></i>';
                    copyButton.classList.add('copied');
                    
                    setTimeout(() => {
                        copyButton.innerHTML = '<i class="fas fa-copy"></i>';
                        copyButton.classList.remove('copied');
                    }, 2000);
                } catch (err) {
                    console.error('Failed to copy code:', err);
                }
            });
            
            pre.style.position = 'relative';
            pre.appendChild(copyButton);
        });
    }

    // External Links
    setupExternalLinks() {
        document.querySelectorAll('a[href^="http"]').forEach(link => {
            if (!link.hostname.includes(window.location.hostname)) {
                link.setAttribute('target', '_blank');
                link.setAttribute('rel', 'noopener noreferrer');
                
                // Add external link icon
                const icon = document.createElement('i');
                icon.className = 'fas fa-external-link-alt';
                icon.style.marginLeft = '0.25rem';
                icon.style.fontSize = '0.75rem';
                link.appendChild(icon);
            }
        });
    }

    // Print Support
    setupPrintSupport() {
        // Add print styles and optimize content for printing
        window.addEventListener('beforeprint', () => {
            // Expand all collapsed sections for printing
            document.querySelectorAll('.collapse:not(.show)').forEach(collapse => {
                collapse.classList.add('show', 'print-expanded');
            });
        });

        window.addEventListener('afterprint', () => {
            // Restore collapsed state after printing
            document.querySelectorAll('.print-expanded').forEach(collapse => {
                collapse.classList.remove('show', 'print-expanded');
            });
        });
    }

    // Mobile Menu
    setupMobileMenu() {
        const mobileToggle = document.getElementById('mobile-sidebar-toggle');
        const sidebar = document.getElementById('docs-sidebar');
        
        if (mobileToggle && sidebar) {
            mobileToggle.addEventListener('click', () => {
                sidebar.classList.toggle('show');
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!sidebar.contains(e.target) && !mobileToggle.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            });

            // Close mobile menu when navigating
            sidebar.querySelectorAll('a').forEach(link => {
                link.addEventListener('click', () => {
                    sidebar.classList.remove('show');
                });
            });
        }
    }

    // Utility Methods
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

// Export/Page Functions
window.exportPage = function(format) {
    const pageId = getPageIdFromUrl();
    if (!pageId) {
        alert('Unable to determine page ID for export');
        return;
    }

    fetch(`/docs/api/export/${pageId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ format: format })
    })
    .then(response => {
        if (!response.ok) throw new Error('Export failed');
        return response.blob();
    })
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `pmtool-docs-${pageId}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    })
    .catch(error => {
        console.error('Export error:', error);
        alert('Export failed. Please try again.');
    });
};

window.exportAll = function(format) {
    fetch('/docs/api/export-all', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ format: format })
    })
    .then(response => {
        if (!response.ok) throw new Error('Export failed');
        return response.blob();
    })
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `pmtool-docs-complete.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    })
    .catch(error => {
        console.error('Export error:', error);
        alert('Export failed. Please try again.');
    });
};

function getPageIdFromUrl() {
    const pathParts = window.location.pathname.split('/');
    return pathParts[pathParts.length - 1];
}

// Global instance
let docsApp;

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // DocsApp will be initialized from the layout with configuration
});

// Export for global use
window.DocsApp = DocsApp;
