using System;
using System.Collections.Generic;
using PM.Tool.Core.Enums;

namespace PM.Tool.Models.ViewModels
{
    public class ReportsViewModel
    {
        public ProjectReportData ProjectReport { get; set; } = new();
        public TaskReportData TaskReport { get; set; } = new();
        public UserProductivityData UserProductivity { get; set; } = new();
        public List<ProjectSummaryViewModel> Projects { get; set; } = new();
    }

    public class ProjectReportData
    {
        public int TotalProjects { get; set; }
        public int PlanningProjects { get; set; }
        public int ActiveProjects { get; set; }
        public int OnHoldProjects { get; set; }
        public int CompletedProjects { get; set; }
        public int CancelledProjects { get; set; }
        public List<MonthlyProjectData> MonthlyData { get; set; } = new();
    }

    public class TaskReportData
    {
        public int TotalTasks { get; set; }
        public int ToDoTasks { get; set; }
        public int InProgressTasks { get; set; }
        public int InReviewTasks { get; set; }
        public int DoneTasks { get; set; }
        public int CancelledTasks { get; set; }
        public int OverdueTasks { get; set; }
        public List<MonthlyTaskData> MonthlyData { get; set; } = new();
        public List<PriorityTaskData> PriorityData { get; set; } = new();
    }


    public class MonthlyProjectData
    {
        public string Month { get; set; } = string.Empty;
        public int Created { get; set; }
        public int Completed { get; set; }
    }

    public class MonthlyTaskData
    {
        public string Month { get; set; } = string.Empty;
        public int Created { get; set; }
        public int Completed { get; set; }
    }

    public class PriorityTaskData
    {
        public TaskPriority Priority { get; set; }
        public int Count { get; set; }
        public int Completed { get; set; }
    }

    public class UserTaskStats
    {
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public int AssignedTasks { get; set; }
        public int CompletedTasks { get; set; }
        public int OverdueTasks { get; set; }
        public double CompletionRate { get; set; }
        public int TotalHours { get; set; }
    }
}
