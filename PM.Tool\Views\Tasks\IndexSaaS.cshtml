@{
    ViewData["Title"] = "Tasks";
    ViewBag.PageIcon = "fas fa-tasks";
    ViewBag.PageDescription = "Manage and track your project tasks";
    ViewBag.BreadcrumbParent = "Projects";
    ViewBag.BreadcrumbParentUrl = Url.Action("Index", "Projects");
    Layout = "_LayoutSaaS";
}

@section PageActions {
    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTaskModal">
        <i class="fas fa-plus me-2"></i>
        New Task
    </button>
    <div class="dropdown">
        <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
            <i class="fas fa-filter me-2"></i>
            Filter
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#">All Tasks</a></li>
            <li><a class="dropdown-item" href="#">My Tasks</a></li>
            <li><a class="dropdown-item" href="#">Overdue</a></li>
            <li><a class="dropdown-item" href="#">Completed</a></li>
        </ul>
    </div>
    <button type="button" class="btn btn-outline-secondary">
        <i class="fas fa-download me-2"></i>
        Export
    </button>
}

<!-- Task Statistics -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-icon bg-primary">
                <i class="fas fa-tasks"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number">42</div>
                <div class="stat-label">Total Tasks</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-icon bg-warning">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number">18</div>
                <div class="stat-label">In Progress</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-icon bg-success">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number">20</div>
                <div class="stat-label">Completed</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-icon bg-danger">
                <i class="fas fa-exclamation-circle"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number">4</div>
                <div class="stat-label">Overdue</div>
            </div>
        </div>
    </div>
</div>

<!-- Task List -->
<div class="card surface-elevated">
    <div class="card-header d-flex align-items-center justify-content-between">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>
            Task List
        </h5>
        <div class="d-flex gap-2">
            <div class="btn-group btn-group-sm" role="group">
                <input type="radio" class="btn-check" name="viewMode" id="listView" checked>
                <label class="btn btn-outline-secondary" for="listView">
                    <i class="fas fa-list"></i>
                </label>
                <input type="radio" class="btn-check" name="viewMode" id="cardView">
                <label class="btn btn-outline-secondary" for="cardView">
                    <i class="fas fa-th-large"></i>
                </label>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <!-- Search and Filters -->
        <div class="task-filters p-3 border-bottom border-custom">
            <div class="row g-3">
                <div class="col-md-4">
                    <div class="position-relative">
                        <input type="search" class="form-control ps-4" placeholder="Search tasks...">
                        <i class="fas fa-search position-absolute top-50 start-0 translate-middle-y ms-3 text-muted"></i>
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select">
                        <option>All Status</option>
                        <option>To Do</option>
                        <option>In Progress</option>
                        <option>Completed</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select">
                        <option>All Priority</option>
                        <option>High</option>
                        <option>Medium</option>
                        <option>Low</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select">
                        <option>All Assignees</option>
                        <option>John Doe</option>
                        <option>Jane Smith</option>
                        <option>Mike Johnson</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-times me-1"></i>
                        Clear
                    </button>
                </div>
            </div>
        </div>

        <!-- Task Items -->
        <div class="task-list">
            <!-- Task Item 1 -->
            <div class="task-item">
                <div class="task-checkbox">
                    <input type="checkbox" class="form-check-input">
                </div>
                <div class="task-priority priority-high">
                    <i class="fas fa-circle"></i>
                </div>
                <div class="task-content">
                    <div class="task-title">Implement user authentication system</div>
                    <div class="task-meta">
                        <span class="task-project">
                            <i class="fas fa-folder me-1"></i>
                            Mobile App Redesign
                        </span>
                        <span class="task-assignee">
                            <i class="fas fa-user me-1"></i>
                            John Doe
                        </span>
                        <span class="task-due-date">
                            <i class="fas fa-calendar me-1"></i>
                            Dec 15, 2024
                        </span>
                    </div>
                </div>
                <div class="task-status">
                    <span class="badge bg-warning">In Progress</span>
                </div>
                <div class="task-actions">
                    <div class="dropdown">
                        <button class="btn btn-sm btn-link text-muted" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-edit me-2"></i>Edit</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-copy me-2"></i>Duplicate</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-trash me-2"></i>Delete</a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Task Item 2 -->
            <div class="task-item">
                <div class="task-checkbox">
                    <input type="checkbox" class="form-check-input" checked>
                </div>
                <div class="task-priority priority-medium">
                    <i class="fas fa-circle"></i>
                </div>
                <div class="task-content">
                    <div class="task-title completed">Design login page mockups</div>
                    <div class="task-meta">
                        <span class="task-project">
                            <i class="fas fa-folder me-1"></i>
                            Mobile App Redesign
                        </span>
                        <span class="task-assignee">
                            <i class="fas fa-user me-1"></i>
                            Jane Smith
                        </span>
                        <span class="task-due-date">
                            <i class="fas fa-calendar me-1"></i>
                            Dec 10, 2024
                        </span>
                    </div>
                </div>
                <div class="task-status">
                    <span class="badge bg-success">Completed</span>
                </div>
                <div class="task-actions">
                    <div class="dropdown">
                        <button class="btn btn-sm btn-link text-muted" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-edit me-2"></i>Edit</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-copy me-2"></i>Duplicate</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-trash me-2"></i>Delete</a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Task Item 3 -->
            <div class="task-item">
                <div class="task-checkbox">
                    <input type="checkbox" class="form-check-input">
                </div>
                <div class="task-priority priority-low">
                    <i class="fas fa-circle"></i>
                </div>
                <div class="task-content">
                    <div class="task-title">Update project documentation</div>
                    <div class="task-meta">
                        <span class="task-project">
                            <i class="fas fa-folder me-1"></i>
                            API Integration
                        </span>
                        <span class="task-assignee">
                            <i class="fas fa-user me-1"></i>
                            Mike Johnson
                        </span>
                        <span class="task-due-date overdue">
                            <i class="fas fa-calendar me-1"></i>
                            Dec 5, 2024
                        </span>
                    </div>
                </div>
                <div class="task-status">
                    <span class="badge bg-secondary">To Do</span>
                </div>
                <div class="task-actions">
                    <div class="dropdown">
                        <button class="btn btn-sm btn-link text-muted" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-edit me-2"></i>Edit</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-copy me-2"></i>Duplicate</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-trash me-2"></i>Delete</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        <div class="card-footer bg-surface">
            <div class="d-flex align-items-center justify-content-between">
                <div class="text-muted small">
                    Showing 1-10 of 42 tasks
                </div>
                <nav>
                    <ul class="pagination pagination-sm mb-0">
                        <li class="page-item disabled">
                            <span class="page-link">Previous</span>
                        </li>
                        <li class="page-item active">
                            <span class="page-link">1</span>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">2</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">3</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">Next</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Create Task Modal -->
<div class="modal fade" id="createTaskModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2 text-primary"></i>
                    Create New Task
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label">Task Title</label>
                            <input type="text" class="form-control" placeholder="Enter task title">
                        </div>
                        <div class="col-12">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" rows="3" placeholder="Enter task description"></textarea>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Project</label>
                            <select class="form-select">
                                <option>Select project</option>
                                <option>Mobile App Redesign</option>
                                <option>API Integration</option>
                                <option>Analytics Dashboard</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Assignee</label>
                            <select class="form-select">
                                <option>Select assignee</option>
                                <option>John Doe</option>
                                <option>Jane Smith</option>
                                <option>Mike Johnson</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Priority</label>
                            <select class="form-select">
                                <option>Medium</option>
                                <option>High</option>
                                <option>Low</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Status</label>
                            <select class="form-select">
                                <option>To Do</option>
                                <option>In Progress</option>
                                <option>Completed</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Due Date</label>
                            <input type="date" class="form-control">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    Cancel
                </button>
                <button type="button" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>
                    Create Task
                </button>
            </div>
        </div>
    </div>
</div>

@section Styles {
<style>
/* Task List Specific Styles */
.stat-card {
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-md);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all var(--transition-normal);
}

.stat-card:hover {
    box-shadow: var(--shadow);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-text-inverse);
    font-size: 1.25rem;
}

.stat-number {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--color-text-primary);
    line-height: 1;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    margin-top: 0.25rem;
}

.task-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--color-border-light);
    transition: background-color var(--transition-fast);
}

.task-item:hover {
    background-color: var(--color-bg-secondary);
}

.task-item:last-child {
    border-bottom: none;
}

.task-checkbox {
    flex-shrink: 0;
}

.task-priority {
    flex-shrink: 0;
    font-size: 0.75rem;
}

.priority-high {
    color: var(--color-danger);
}

.priority-medium {
    color: var(--color-warning);
}

.priority-low {
    color: var(--color-success);
}

.task-content {
    flex-grow: 1;
    min-width: 0;
}

.task-title {
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: 0.5rem;
}

.task-title.completed {
    text-decoration: line-through;
    color: var(--color-text-muted);
}

.task-meta {
    display: flex;
    gap: 1rem;
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    flex-wrap: wrap;
}

.task-meta span {
    display: flex;
    align-items: center;
    white-space: nowrap;
}

.task-due-date.overdue {
    color: var(--color-danger);
}

.task-status {
    flex-shrink: 0;
}

.task-actions {
    flex-shrink: 0;
}

.pagination .page-link {
    background-color: var(--color-surface);
    border-color: var(--color-border);
    color: var(--color-text-primary);
}

.pagination .page-link:hover {
    background-color: var(--color-bg-secondary);
    border-color: var(--color-border);
    color: var(--color-text-primary);
}

.pagination .page-item.active .page-link {
    background-color: var(--color-accent);
    border-color: var(--color-accent);
    color: var(--color-text-inverse);
}

@@media (max-width: 767.98px) {
    .task-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .task-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .stat-card {
        text-align: center;
        flex-direction: column;
    }
}
</style>
}
