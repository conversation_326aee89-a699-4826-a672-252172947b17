using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities
{
    public class Milestone
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(1000)]
        public string? Description { get; set; }

        public int ProjectId { get; set; }

        private DateTime _dueDate;
        public DateTime DueDate
        {
            get => _dueDate;
            set => _dueDate = DateTime.SpecifyKind(value, DateTimeKind.Utc);
        }

        private DateTime? _completedDate;
        public DateTime? CompletedDate
        {
            get => _completedDate;
            set => _completedDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        private DateTime _createdAt = DateTime.UtcNow;
        public DateTime CreatedAt
        {
            get => _createdAt;
            set => _createdAt = DateTime.SpecifyKind(value, DateTimeKind.Utc);
        }

        public bool IsCompleted { get; set; } = false;

        public bool IsDeleted { get; set; } = false;

        // Navigation properties
        public virtual Project Project { get; set; } = null!;

        // Computed properties
        public bool IsOverdue => !IsCompleted && DateTime.UtcNow > DueDate;
    }
}
