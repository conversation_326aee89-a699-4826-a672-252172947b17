@model PM.Tool.Models.ViewModels.EpicCreateViewModel
@{
    ViewData["Title"] = "Create Epic";
    var project = ViewBag.Project as PM.Tool.Core.Entities.Project;
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="@Url.Action("Index", "Projects")" class="text-neutral-500 hover:text-neutral-700 dark:text-dark-400 dark:hover:text-dark-200">
                            <i class="fas fa-project-diagram mr-2"></i>Projects
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-neutral-400 dark:text-dark-500 mx-2"></i>
                            <a href="@Url.Action("Details", "Projects", new { id = project?.Id })" class="text-neutral-500 hover:text-neutral-700 dark:text-dark-400 dark:hover:text-dark-200">
                                @project?.Name
                            </a>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-neutral-400 dark:text-dark-500 mx-2"></i>
                            <a href="@Url.Action("Epics", new { projectId = project?.Id })" class="text-neutral-500 hover:text-neutral-700 dark:text-dark-400 dark:hover:text-dark-200">
                                Epics
                            </a>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-neutral-400 dark:text-dark-500 mx-2"></i>
                            <span class="text-neutral-700 dark:text-dark-300">Create Epic</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-mountain mr-3 text-primary-600 dark:text-primary-400"></i>
                Create New Epic
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Create a new epic for @project?.Name
            </p>
        </div>
    </div>
</div>

<!-- Form -->
<div class="max-w-4xl">
    <form asp-action="CreateEpic" method="post" class="space-y-8">
        @Html.AntiForgeryToken()
        @Html.HiddenFor(m => m.ProjectId)

        <!-- Basic Information -->
        @{
            ViewData["Title"] = "Basic Information";
            ViewData["Icon"] = "fas fa-info-circle";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Title -->
                <div class="md:col-span-2">
                    <label asp-for="Title" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">
                        Epic Title <span class="text-danger-600">*</span>
                    </label>
                    <input asp-for="Title"
                           class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-800 dark:text-dark-100"
                           placeholder="Enter a descriptive title for the epic" />
                    <span asp-validation-for="Title" class="text-danger-600 text-sm"></span>
                </div>

                <!-- Note: Status will be set to Draft automatically when creating -->

                <!-- Priority -->
                <div>
                    <label asp-for="Priority" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">
                        Priority
                    </label>
                    <select asp-for="Priority" asp-items="ViewBag.EpicPriorities"
                            class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-800 dark:text-dark-100">
                    </select>
                    <span asp-validation-for="Priority" class="text-danger-600 text-sm"></span>
                </div>

                <!-- Target Date -->
                <div>
                    <label asp-for="TargetDate" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">
                        Target Date
                    </label>
                    <input asp-for="TargetDate" type="date"
                           class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-800 dark:text-dark-100" />
                    <span asp-validation-for="TargetDate" class="text-danger-600 text-sm"></span>
                </div>

                <!-- Estimated Story Points -->
                <div>
                    <label asp-for="EstimatedStoryPoints" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">
                        Estimated Story Points
                    </label>
                    <input asp-for="EstimatedStoryPoints" type="number" step="0.5" min="0"
                           class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-800 dark:text-dark-100"
                           placeholder="0" />
                    <span asp-validation-for="EstimatedStoryPoints" class="text-danger-600 text-sm"></span>
                </div>
            </div>
        </partial>

        <!-- Description -->
        @{
            ViewData["Title"] = "Description";
            ViewData["Icon"] = "fas fa-align-left";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div>
                <label asp-for="Description" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">
                    Epic Description <span class="text-danger-600">*</span>
                </label>
                <textarea asp-for="Description" rows="6"
                          class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-800 dark:text-dark-100"
                          placeholder="Provide a detailed description of what this epic aims to achieve..."></textarea>
                <span asp-validation-for="Description" class="text-danger-600 text-sm"></span>
                <p class="mt-2 text-sm text-neutral-500 dark:text-dark-400">
                    Describe the epic's purpose, scope, and expected outcomes.
                </p>
            </div>
        </partial>

        <!-- Acceptance Criteria -->
        @{
            ViewData["Title"] = "Acceptance Criteria";
            ViewData["Icon"] = "fas fa-check-circle";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div>
                <label asp-for="AcceptanceCriteria" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">
                    Acceptance Criteria
                </label>
                <textarea asp-for="AcceptanceCriteria" rows="4"
                          class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-800 dark:text-dark-100"
                          placeholder="Define the criteria that must be met for this epic to be considered complete..."></textarea>
                <span asp-validation-for="AcceptanceCriteria" class="text-danger-600 text-sm"></span>
                <p class="mt-2 text-sm text-neutral-500 dark:text-dark-400">
                    List the specific conditions that must be satisfied for the epic to be marked as done.
                </p>
            </div>
        </partial>

        <!-- Business Value -->
        @{
            ViewData["Title"] = "Business Value";
            ViewData["Icon"] = "fas fa-chart-line";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div>
                <label asp-for="BusinessValue" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">
                    Business Value
                </label>
                <textarea asp-for="BusinessValue" rows="4"
                          class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-800 dark:text-dark-100"
                          placeholder="Explain the business value and benefits this epic will deliver..."></textarea>
                <span asp-validation-for="BusinessValue" class="text-danger-600 text-sm"></span>
                <p class="mt-2 text-sm text-neutral-500 dark:text-dark-400">
                    Describe how this epic contributes to business goals and what value it provides to users or stakeholders.
                </p>
            </div>
        </partial>

        <!-- Additional Information -->
        @{
            ViewData["Title"] = "Additional Information";
            ViewData["Icon"] = "fas fa-tags";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Tags -->
                <div>
                    <label asp-for="Tags" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">
                        Tags
                    </label>
                    <input asp-for="Tags"
                           class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-800 dark:text-dark-100"
                           placeholder="feature, enhancement, bug-fix" />
                    <span asp-validation-for="Tags" class="text-danger-600 text-sm"></span>
                    <p class="mt-1 text-xs text-neutral-500 dark:text-dark-400">
                        Separate tags with commas
                    </p>
                </div>

                <!-- Sort Order -->
                <div>
                    <label asp-for="SortOrder" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">
                        Sort Order
                    </label>
                    <input asp-for="SortOrder" type="number" min="0"
                           class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-800 dark:text-dark-100"
                           placeholder="0" />
                    <span asp-validation-for="SortOrder" class="text-danger-600 text-sm"></span>
                    <p class="mt-1 text-xs text-neutral-500 dark:text-dark-400">
                        Lower numbers appear first
                    </p>
                </div>
            </div>
        </partial>

        <!-- Form Actions -->
        @{
            ViewData["SubmitText"] = "Create Epic";
            ViewData["SubmitIcon"] = "fas fa-save";
            ViewData["CancelUrl"] = Url.Action("Epics", new { projectId = Model.ProjectId });
        }
        <partial name="Components/_FormActions" view-data="ViewData" />
    </form>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Set minimum date to today
            const dateInputs = document.querySelectorAll('input[type="date"]');
            const today = new Date().toISOString().split('T')[0];
            dateInputs.forEach(input => {
                input.setAttribute('min', today);
            });
        });
    </script>
}
