@model MyTasksViewModel
@using PM.Tool.Core.Enums
@using TaskStatus = PM.Tool.Core.Enums.TaskStatus

<!-- Grid View (Hidden by default) -->
<div id="gridView" class="view-content hidden">
    @if (Model.Tasks.Any())
    {
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
            @foreach (var task in Model.Tasks)
            {
                <div class="task-card card-custom <EMAIL>().ToLower() @(task.IsOverdue ? "border-red-300" : "")">
                    <div class="card-body-custom">
                        <div class="flex items-start justify-between mb-3">
                            <input type="checkbox" class="task-checkbox" value="@task.Id" />
                            <span class="status-pill <EMAIL>().ToLower()">
                                @task.Status.ToString()
                            </span>
                        </div>
                        
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-white mb-2">
                            <a href="@Url.Action("Details", new { id = task.Id })" class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                                @task.Title
                            </a>
                        </h3>
                        
                        @if (!string.IsNullOrEmpty(task.Description))
                        {
                            <p class="text-neutral-600 dark:text-neutral-400 text-sm mb-3 line-clamp-3">
                                @task.Description
                            </p>
                        }
                        
                        <div class="space-y-2 text-sm text-neutral-500 dark:text-neutral-400">
                            <div class="flex items-center">
                                <i class="fas fa-folder mr-2 w-4"></i>
                                <span class="truncate">@task.ProjectName</span>
                            </div>
                            
                            @if (task.DueDate.HasValue)
                            {
                                <div class="flex items-center @(task.IsOverdue ? "text-red-600 dark:text-red-400" : "")">
                                    <i class="fas fa-calendar mr-2 w-4"></i>
                                    <span>@task.DueDate.Value.ToString("MMM dd")</span>
                                    @if (task.IsOverdue)
                                    {
                                        <i class="fas fa-exclamation-triangle ml-1"></i>
                                    }
                                </div>
                            }
                            
                            @if (task.Priority == TaskPriority.Critical || task.Priority == TaskPriority.High)
                            {
                                <div class="flex items-center">
                                    <i class="fas fa-flag mr-2 w-4"></i>
                                    <span>@task.Priority Priority</span>
                                </div>
                            }
                        </div>
                        
                        @if (task.SubTaskCount > 0)
                        {
                            <div class="mt-3">
                                <div class="flex items-center justify-between text-sm text-neutral-500 dark:text-neutral-400 mb-1">
                                    <span>Progress</span>
                                    <span>@task.CompletedSubTasks/@task.SubTaskCount</span>
                                </div>
                                <div class="w-full h-2 bg-neutral-200 dark:bg-dark-600 rounded-full">
                                    <div class="h-2 bg-primary-600 rounded-full" style="width: @task.Progress%"></div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            }
        </div>
    }
</div>
