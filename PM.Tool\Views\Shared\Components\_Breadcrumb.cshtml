@*
    Professional Breadcrumb Component - Usage Examples:

    1. Basic breadcrumb:
    @{
        ViewData["Items"] = new[] {
            new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
            new { Text = "Projects", Href = Url.Action("Index", "Projects"), Icon = "fas fa-project-diagram" },
            new { Text = "Project Details", Href = "", Icon = "" }
        };
    }
    <partial name="Components/_Breadcrumb" view-data="ViewData" />

    2. Breadcrumb with custom separator:
    @{
        ViewData["Items"] = breadcrumbItems;
        ViewData["Separator"] = "fas fa-angle-right";
        ViewData["ShowHome"] = true;
        ViewData["ShowIcons"] = true;
    }
    <partial name="Components/_Breadcrumb" view-data="ViewData" />
*@

@{
    var items = ViewData["Items"] as IEnumerable<object> ?? new object[0];
    var separator = ViewData["Separator"]?.ToString() ?? "fas fa-chevron-right";
    var showHome = ViewData["ShowHome"] as bool? ?? true;
    var showIcons = ViewData["ShowIcons"] as bool? ?? true;
    var containerClass = ViewData["ContainerClass"]?.ToString() ?? "mb-6";
    var textSize = ViewData["TextSize"]?.ToString() ?? "text-sm";
    
    var breadcrumbItems = items.ToList();
    
    // Add home item if requested and not already present
    if (showHome && (!breadcrumbItems.Any() || !breadcrumbItems.First().ToString().Contains("Dashboard")))
    {
        breadcrumbItems.Insert(0, new { 
            Text = "Dashboard", 
            Href = Url.Action("Index", "Home"), 
            Icon = "fas fa-home" 
        });
    }
}

@if (breadcrumbItems.Any())
{
    <nav class="@containerClass" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-1 md:space-x-3">
            @for (int i = 0; i < breadcrumbItems.Count; i++)
            {
                var item = breadcrumbItems[i];
                var text = item.GetType().GetProperty("Text")?.GetValue(item)?.ToString() ?? "";
                var href = item.GetType().GetProperty("Href")?.GetValue(item)?.ToString() ?? "";
                var icon = item.GetType().GetProperty("Icon")?.GetValue(item)?.ToString() ?? "";
                var isLast = i == breadcrumbItems.Count - 1;
                var isClickable = !string.IsNullOrEmpty(href) && !isLast;

                <li class="inline-flex items-center">
                    @if (isClickable)
                    {
                        <a href="@href" 
                           class="inline-flex items-center @textSize font-medium text-neutral-500 hover:text-neutral-700 dark:text-dark-400 dark:hover:text-dark-200 transition-colors">
                            @if (showIcons && !string.IsNullOrEmpty(icon))
                            {
                                <i class="@icon mr-2"></i>
                            }
                            @text
                        </a>
                    }
                    else
                    {
                        <span class="inline-flex items-center @textSize font-medium @(isLast ? "text-neutral-700 dark:text-dark-300" : "text-neutral-500 dark:text-dark-400")">
                            @if (showIcons && !string.IsNullOrEmpty(icon))
                            {
                                <i class="@icon mr-2"></i>
                            }
                            @text
                        </span>
                    }
                </li>

                @if (!isLast)
                {
                    <li aria-hidden="true">
                        <i class="@separator text-neutral-400 dark:text-dark-500 mx-2 @textSize"></i>
                    </li>
                }
            }
        </ol>
    </nav>
}

@section Scripts {
    <script>
        // Breadcrumb utilities
        window.BreadcrumbComponent = {
            // Add breadcrumb item dynamically
            addItem: function(text, href = '', icon = '') {
                var breadcrumb = document.querySelector('nav[aria-label="Breadcrumb"] ol');
                if (!breadcrumb) return;

                // Create separator
                var separator = document.createElement('li');
                separator.setAttribute('aria-hidden', 'true');
                separator.innerHTML = '<i class="fas fa-chevron-right text-neutral-400 dark:text-dark-500 mx-2 text-sm"></i>';
                breadcrumb.appendChild(separator);

                // Create new item
                var listItem = document.createElement('li');
                listItem.className = 'inline-flex items-center';
                
                var content = '';
                if (href) {
                    content = `<a href="${href}" class="inline-flex items-center text-sm font-medium text-neutral-500 hover:text-neutral-700 dark:text-dark-400 dark:hover:text-dark-200 transition-colors">`;
                } else {
                    content = `<span class="inline-flex items-center text-sm font-medium text-neutral-700 dark:text-dark-300">`;
                }
                
                if (icon) {
                    content += `<i class="${icon} mr-2"></i>`;
                }
                content += text;
                content += href ? '</a>' : '</span>';
                
                listItem.innerHTML = content;
                breadcrumb.appendChild(listItem);
            },

            // Update last item (current page)
            updateCurrent: function(text, icon = '') {
                var breadcrumb = document.querySelector('nav[aria-label="Breadcrumb"] ol');
                if (!breadcrumb) return;

                var lastItem = breadcrumb.lastElementChild;
                if (!lastItem) return;

                var content = `<span class="inline-flex items-center text-sm font-medium text-neutral-700 dark:text-dark-300">`;
                if (icon) {
                    content += `<i class="${icon} mr-2"></i>`;
                }
                content += text + '</span>';
                
                lastItem.innerHTML = content;
            },

            // Generate breadcrumb from current URL
            generateFromUrl: function() {
                var path = window.location.pathname;
                var segments = path.split('/').filter(segment => segment);
                var breadcrumbItems = [];

                // Add home
                breadcrumbItems.push({
                    text: 'Dashboard',
                    href: '/',
                    icon: 'fas fa-home'
                });

                // Process URL segments
                var currentPath = '';
                segments.forEach(function(segment, index) {
                    currentPath += '/' + segment;
                    var isLast = index === segments.length - 1;
                    
                    // Capitalize and format segment
                    var text = segment.charAt(0).toUpperCase() + segment.slice(1);
                    if (text.toLowerCase() === 'details' && index > 0) {
                        text = segments[index - 1].charAt(0).toUpperCase() + segments[index - 1].slice(1) + ' Details';
                    }

                    breadcrumbItems.push({
                        text: text,
                        href: isLast ? '' : currentPath,
                        icon: this.getIconForSegment(segment)
                    });
                });

                return breadcrumbItems;
            },

            getIconForSegment: function(segment) {
                var iconMap = {
                    'projects': 'fas fa-project-diagram',
                    'tasks': 'fas fa-tasks',
                    'analytics': 'fas fa-chart-line',
                    'documents': 'fas fa-file-alt',
                    'meetings': 'fas fa-calendar',
                    'requirements': 'fas fa-list-check',
                    'risks': 'fas fa-exclamation-triangle',
                    'resources': 'fas fa-users',
                    'agile': 'fas fa-columns',
                    'timetracking': 'fas fa-clock',
                    'wbs': 'fas fa-sitemap'
                };
                return iconMap[segment.toLowerCase()] || '';
            }
        };
    </script>
}
