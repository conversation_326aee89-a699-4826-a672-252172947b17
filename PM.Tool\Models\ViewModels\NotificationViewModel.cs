using System;
using PM.Tool.Core.Enums;

namespace PM.Tool.Models.ViewModels
{
    public class NotificationViewModel
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public NotificationType Type { get; set; }
        public bool IsRead { get; set; }
        public DateTime CreatedAt { get; set; }
        public int? RelatedProjectId { get; set; }
        public int? RelatedTaskId { get; set; }
        public string? RelatedProjectName { get; set; }
        public string? RelatedTaskTitle { get; set; }
    }
}
