using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities
{
    public class Integration : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        public IntegrationType Type { get; set; } = IntegrationType.GitHub;

        public int? ProjectId { get; set; }

        public string Configuration { get; set; } = string.Empty; // JSON configuration

        public bool IsActive { get; set; } = true;

        public string CreatedByUserId { get; set; } = string.Empty;

        private DateTime? _lastSync;
        public DateTime? LastSync
        {
            get => _lastSync;
            set => _lastSync = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        public string? LastSyncStatus { get; set; }

        public string? LastSyncError { get; set; }

        // Navigation properties
        public virtual Project? Project { get; set; }
        public virtual ApplicationUser CreatedBy { get; set; } = null!;
        public virtual ICollection<IntegrationLog> Logs { get; set; } = new List<IntegrationLog>();
    }

    public class IntegrationLog : BaseEntity
    {
        public int IntegrationId { get; set; }

        public LogLevel Level { get; set; } = LogLevel.Info;

        [Required]
        [MaxLength(500)]
        public string Message { get; set; } = string.Empty;

        public string? Details { get; set; }

        private DateTime _timestamp = DateTime.UtcNow;
        public DateTime Timestamp
        {
            get => _timestamp;
            set => _timestamp = DateTime.SpecifyKind(value, DateTimeKind.Utc);
        }

        // Navigation properties
        public virtual Integration Integration { get; set; } = null!;
    }

    public class ExternalCommit : BaseEntity
    {
        public int IntegrationId { get; set; }

        public int? TaskId { get; set; }

        [Required]
        [MaxLength(100)]
        public string CommitHash { get; set; } = string.Empty;

        [Required]
        [MaxLength(500)]
        public string Message { get; set; } = string.Empty;

        [MaxLength(100)]
        public string Author { get; set; } = string.Empty;

        [MaxLength(100)]
        public string AuthorEmail { get; set; } = string.Empty;

        private DateTime _commitDate;
        public DateTime CommitDate
        {
            get => _commitDate;
            set => _commitDate = DateTime.SpecifyKind(value, DateTimeKind.Utc);
        }

        [MaxLength(200)]
        public string? Branch { get; set; }

        [MaxLength(500)]
        public string? Repository { get; set; }

        public string? FilesChanged { get; set; } // JSON array

        // Navigation properties
        public virtual Integration Integration { get; set; } = null!;
        public virtual TaskEntity? Task { get; set; }
    }

    public class CalendarSync : BaseEntity
    {
        public int ProjectId { get; set; }

        public string UserId { get; set; } = string.Empty;

        public CalendarProvider Provider { get; set; } = CalendarProvider.Outlook;

        public string ExternalCalendarId { get; set; } = string.Empty;

        public string AccessToken { get; set; } = string.Empty;

        public string? RefreshToken { get; set; }

        private DateTime? _tokenExpiry;
        public DateTime? TokenExpiry
        {
            get => _tokenExpiry;
            set => _tokenExpiry = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        public bool IsActive { get; set; } = true;

        public SyncDirection Direction { get; set; } = SyncDirection.Bidirectional;

        private DateTime? _lastSync;
        public DateTime? LastSync
        {
            get => _lastSync;
            set => _lastSync = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        // Navigation properties
        public virtual Project Project { get; set; } = null!;
        public virtual ApplicationUser User { get; set; } = null!;
    }

    public class NotificationChannel : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        public ChannelType Type { get; set; } = ChannelType.Email;

        public int? ProjectId { get; set; }

        public string Configuration { get; set; } = string.Empty; // JSON configuration

        public bool IsActive { get; set; } = true;

        public string CreatedByUserId { get; set; } = string.Empty;

        // Navigation properties
        public virtual Project? Project { get; set; }
        public virtual ApplicationUser CreatedBy { get; set; } = null!;
        public virtual ICollection<NotificationRule> Rules { get; set; } = new List<NotificationRule>();
    }

    public class NotificationRule : BaseEntity
    {
        public int ChannelId { get; set; }

        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;

        public string EventType { get; set; } = string.Empty; // e.g., "TaskCreated", "ProjectCompleted"

        public string Conditions { get; set; } = string.Empty; // JSON conditions

        public string Template { get; set; } = string.Empty; // Message template

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual NotificationChannel Channel { get; set; } = null!;
    }

    public enum IntegrationType
    {
        GitHub = 1,
        GitLab = 2,
        Slack = 3,
        Teams = 4,
        Jira = 5,
        Trello = 6
    }

    public enum LogLevel
    {
        Debug = 1,
        Info = 2,
        Warning = 3,
        Error = 4
    }

    public enum CalendarProvider
    {
        Outlook = 1,
        Google = 2,
        Apple = 3
    }

    public enum SyncDirection
    {
        Import = 1,
        Export = 2,
        Bidirectional = 3
    }

    public enum ChannelType
    {
        Email = 1,
        Slack = 2,
        Teams = 3,
        Webhook = 4,
        SMS = 5
    }
}
