using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities.Agile
{
    public class Bug : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [MaxLength(5000)]
        public string Description { get; set; } = string.Empty;

        public int ProjectId { get; set; }
        public int? UserStoryId { get; set; }
        public int? FeatureId { get; set; }

        [MaxLength(50)]
        public string BugKey { get; set; } = string.Empty; // BG-001

        public BugSeverity Severity { get; set; } = BugSeverity.Medium;
        public BugPriority Priority { get; set; } = BugPriority.Medium;
        public BugStatus Status { get; set; } = BugStatus.New;

        [Required]
        [MaxLength(2000)]
        public string StepsToReproduce { get; set; } = string.Empty;

        [Required]
        [MaxLength(1000)]
        public string ExpectedResult { get; set; } = string.Empty;

        [Required]
        [MaxLength(1000)]
        public string ActualResult { get; set; } = string.Empty;

        [MaxLength(100)]
        public string? FoundInVersion { get; set; }

        [MaxLength(100)]
        public string? FixedInVersion { get; set; }

        public string? AssignedToUserId { get; set; }
        public string? ReportedByUserId { get; set; }

        private DateTime? _reproducedDate;
        public DateTime? ReproducedDate
        {
            get => _reproducedDate;
            set => _reproducedDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        private DateTime? _resolvedDate;
        public DateTime? ResolvedDate
        {
            get => _resolvedDate;
            set => _resolvedDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        private DateTime? _verifiedDate;
        public DateTime? VerifiedDate
        {
            get => _verifiedDate;
            set => _verifiedDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        [MaxLength(500)]
        public string? Tags { get; set; } // JSON array

        public decimal EstimatedHours { get; set; }
        public decimal ActualHours { get; set; }

        // Navigation properties
        public virtual Project Project { get; set; } = null!;
        public virtual UserStory? UserStory { get; set; }
        public virtual Feature? Feature { get; set; }
        public virtual ApplicationUser? AssignedTo { get; set; }
        public virtual ApplicationUser? ReportedBy { get; set; }
        public virtual ICollection<BugComment> Comments { get; set; } = new List<BugComment>();
        public virtual ICollection<BugAttachment> Attachments { get; set; } = new List<BugAttachment>();

        // Computed properties
        public bool IsResolved => Status == BugStatus.Resolved || Status == BugStatus.Closed;
        public bool IsOverdue => TargetDate.HasValue && DateTime.UtcNow > TargetDate.Value && !IsResolved;
        public int DaysOpen => (DateTime.UtcNow - CreatedAt).Days;

        private DateTime? _targetDate;
        public DateTime? TargetDate
        {
            get => _targetDate;
            set => _targetDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }
    }

    public class BugComment : BaseEntity
    {
        public int BugId { get; set; }

        [Required]
        [MaxLength(2000)]
        public string Content { get; set; } = string.Empty;

        public string UserId { get; set; } = string.Empty;

        public CommentType Type { get; set; } = CommentType.General;

        // Navigation properties
        public virtual Bug Bug { get; set; } = null!;
        public virtual ApplicationUser User { get; set; } = null!;
    }

    public class BugAttachment : BaseEntity
    {
        public int BugId { get; set; }

        [Required]
        [MaxLength(255)]
        public string FileName { get; set; } = string.Empty;

        [Required]
        [MaxLength(500)]
        public string FilePath { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string ContentType { get; set; } = string.Empty;

        public long FileSize { get; set; }

        public string UploadedByUserId { get; set; } = string.Empty;

        // Navigation properties
        public virtual Bug Bug { get; set; } = null!;
        public virtual ApplicationUser UploadedBy { get; set; } = null!;
    }

    public enum BugSeverity
    {
        Critical = 1,    // System crash, data loss
        High = 2,        // Major functionality broken
        Medium = 3,      // Minor functionality issues
        Low = 4          // Cosmetic issues
    }

    public enum BugPriority
    {
        Critical = 1,    // Fix immediately
        High = 2,        // Fix in current sprint
        Medium = 3,      // Fix in next sprint
        Low = 4          // Fix when time permits
    }

    public enum BugStatus
    {
        New = 1,
        Assigned = 2,
        InProgress = 3,
        Resolved = 4,
        Verified = 5,
        Closed = 6,
        Reopened = 7,
        Deferred = 8
    }
}
