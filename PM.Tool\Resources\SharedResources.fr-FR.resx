<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>

  <!-- Common UI Elements - French -->
  <data name="Common.Save" xml:space="preserve">
    <value>Enregistrer</value>
  </data>
  <data name="Common.Cancel" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="Common.Delete" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="Common.Edit" xml:space="preserve">
    <value>Modifier</value>
  </data>
  <data name="Common.Create" xml:space="preserve">
    <value>Créer</value>
  </data>
  <data name="Common.Update" xml:space="preserve">
    <value>Mettre à jour</value>
  </data>
  <data name="Common.Details" xml:space="preserve">
    <value>Détails</value>
  </data>
  <data name="Common.Back" xml:space="preserve">
    <value>Retour</value>
  </data>
  <data name="Common.Next" xml:space="preserve">
    <value>Suivant</value>
  </data>
  <data name="Common.Previous" xml:space="preserve">
    <value>Précédent</value>
  </data>
  <data name="Common.Search" xml:space="preserve">
    <value>Rechercher</value>
  </data>
  <data name="Common.Filter" xml:space="preserve">
    <value>Filtrer</value>
  </data>
  <data name="Common.Export" xml:space="preserve">
    <value>Exporter</value>
  </data>
  <data name="Common.Import" xml:space="preserve">
    <value>Importer</value>
  </data>
  <data name="Common.Print" xml:space="preserve">
    <value>Imprimer</value>
  </data>
  <data name="Common.Download" xml:space="preserve">
    <value>Télécharger</value>
  </data>
  <data name="Common.Upload" xml:space="preserve">
    <value>Téléverser</value>
  </data>
  <data name="Common.Yes" xml:space="preserve">
    <value>Oui</value>
  </data>
  <data name="Common.No" xml:space="preserve">
    <value>Non</value>
  </data>
  <data name="Common.OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Common.Close" xml:space="preserve">
    <value>Fermer</value>
  </data>
  <data name="Common.Loading" xml:space="preserve">
    <value>Chargement...</value>
  </data>
  <data name="Common.NoData" xml:space="preserve">
    <value>Aucune donnée disponible</value>
  </data>
  <data name="Common.Error" xml:space="preserve">
    <value>Erreur</value>
  </data>
  <data name="Common.Success" xml:space="preserve">
    <value>Succès</value>
  </data>
  <data name="Common.Warning" xml:space="preserve">
    <value>Avertissement</value>
  </data>
  <data name="Common.Information" xml:space="preserve">
    <value>Information</value>
  </data>

  <!-- Navigation - French -->
  <data name="Nav.Dashboard" xml:space="preserve">
    <value>Tableau de bord</value>
  </data>
  <data name="Nav.Projects" xml:space="preserve">
    <value>Projets</value>
  </data>
  <data name="Nav.Tasks" xml:space="preserve">
    <value>Tâches</value>
  </data>
  <data name="Nav.Management" xml:space="preserve">
    <value>Gestion</value>
  </data>
  <data name="Nav.Analytics" xml:space="preserve">
    <value>Analytiques</value>
  </data>
  <data name="Nav.Resources" xml:space="preserve">
    <value>Ressources</value>
  </data>
  <data name="Nav.Risks" xml:space="preserve">
    <value>Risques</value>
  </data>
  <data name="Nav.Meetings" xml:space="preserve">
    <value>Réunions</value>
  </data>
  <data name="Nav.Requirements" xml:space="preserve">
    <value>Exigences</value>
  </data>
  <data name="Nav.Backlog" xml:space="preserve">
    <value>Backlog</value>
  </data>
  <data name="Nav.Kanban" xml:space="preserve">
    <value>Tableau Kanban</value>
  </data>
  <data name="Nav.Documentation" xml:space="preserve">
    <value>Documentation</value>
  </data>
  <data name="Nav.SkillsManagement" xml:space="preserve">
    <value>Gestion des compétences</value>
  </data>
  <data name="Nav.ResourceUtilization" xml:space="preserve">
    <value>Utilisation des ressources</value>
  </data>
  <data name="Nav.MeetingCalendar" xml:space="preserve">
    <value>Calendrier des réunions</value>
  </data>
  <data name="Nav.ActionItems" xml:space="preserve">
    <value>Éléments d'action</value>
  </data>
  <data name="Nav.AnalyticsDashboard" xml:space="preserve">
    <value>Tableau de bord analytique</value>
  </data>
  <data name="Nav.AdvancedReports" xml:space="preserve">
    <value>Rapports avancés</value>
  </data>
  <data name="Nav.TeamAnalytics" xml:space="preserve">
    <value>Analytiques d'équipe</value>
  </data>
  <data name="Nav.BurndownCharts" xml:space="preserve">
    <value>Graphiques Burndown</value>
  </data>
  <data name="Nav.VelocityCharts" xml:space="preserve">
    <value>Graphiques de vélocité</value>
  </data>
  <data name="Nav.ExportData" xml:space="preserve">
    <value>Exporter les données</value>
  </data>

  <!-- Project Management - French -->
  <data name="Project.Title" xml:space="preserve">
    <value>Titre</value>
  </data>
  <data name="Project.Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Project.StartDate" xml:space="preserve">
    <value>Date de début</value>
  </data>
  <data name="Project.EndDate" xml:space="preserve">
    <value>Date de fin</value>
  </data>
  <data name="Project.Status" xml:space="preserve">
    <value>Statut</value>
  </data>
  <data name="Project.Priority" xml:space="preserve">
    <value>Priorité</value>
  </data>
  <data name="Project.Budget" xml:space="preserve">
    <value>Budget</value>
  </data>
  <data name="Project.Progress" xml:space="preserve">
    <value>Progrès</value>
  </data>
  <data name="Project.Manager" xml:space="preserve">
    <value>Chef de projet</value>
  </data>
  <data name="Project.Team" xml:space="preserve">
    <value>Équipe</value>
  </data>

  <!-- Task Management - French -->
  <data name="Task.Title" xml:space="preserve">
    <value>Titre de la tâche</value>
  </data>
  <data name="Task.Description" xml:space="preserve">
    <value>Description de la tâche</value>
  </data>
  <data name="Task.AssignedTo" xml:space="preserve">
    <value>Assigné à</value>
  </data>
  <data name="Task.DueDate" xml:space="preserve">
    <value>Date d'échéance</value>
  </data>
  <data name="Task.EstimatedHours" xml:space="preserve">
    <value>Heures estimées</value>
  </data>
  <data name="Task.ActualHours" xml:space="preserve">
    <value>Heures réelles</value>
  </data>
  <data name="Task.StoryPoints" xml:space="preserve">
    <value>Points d'histoire</value>
  </data>

  <!-- Agile Terms - French -->
  <data name="Agile.Epic" xml:space="preserve">
    <value>Épique</value>
  </data>
  <data name="Agile.UserStory" xml:space="preserve">
    <value>Histoire utilisateur</value>
  </data>
  <data name="Agile.Sprint" xml:space="preserve">
    <value>Sprint</value>
  </data>
  <data name="Agile.Backlog" xml:space="preserve">
    <value>Backlog</value>
  </data>
  <data name="Agile.Kanban" xml:space="preserve">
    <value>Kanban</value>
  </data>
  <data name="Agile.Scrum" xml:space="preserve">
    <value>Scrum</value>
  </data>
  <data name="Agile.Velocity" xml:space="preserve">
    <value>Vélocité</value>
  </data>
  <data name="Agile.Burndown" xml:space="preserve">
    <value>Burndown</value>
  </data>

  <!-- Status Values - French -->
  <data name="Status.Active" xml:space="preserve">
    <value>Actif</value>
  </data>
  <data name="Status.Inactive" xml:space="preserve">
    <value>Inactif</value>
  </data>
  <data name="Status.Completed" xml:space="preserve">
    <value>Terminé</value>
  </data>
  <data name="Status.InProgress" xml:space="preserve">
    <value>En cours</value>
  </data>
  <data name="Status.Pending" xml:space="preserve">
    <value>En attente</value>
  </data>
  <data name="Status.Cancelled" xml:space="preserve">
    <value>Annulé</value>
  </data>

  <!-- Priority Values - French -->
  <data name="Priority.Critical" xml:space="preserve">
    <value>Critique</value>
  </data>
  <data name="Priority.High" xml:space="preserve">
    <value>Élevée</value>
  </data>
  <data name="Priority.Medium" xml:space="preserve">
    <value>Moyenne</value>
  </data>
  <data name="Priority.Low" xml:space="preserve">
    <value>Faible</value>
  </data>

  <!-- Messages - French -->
  <data name="Message.SaveSuccess" xml:space="preserve">
    <value>Élément enregistré avec succès</value>
  </data>
  <data name="Message.DeleteSuccess" xml:space="preserve">
    <value>Élément supprimé avec succès</value>
  </data>
  <data name="Message.UpdateSuccess" xml:space="preserve">
    <value>Élément mis à jour avec succès</value>
  </data>
  <data name="Message.ErrorOccurred" xml:space="preserve">
    <value>Une erreur s'est produite. Veuillez réessayer.</value>
  </data>
  <data name="Message.ConfirmDelete" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir supprimer cet élément ?</value>
  </data>

  <!-- Meeting Management - French -->
  <data name="Meeting.Title" xml:space="preserve">
    <value>Titre de la réunion</value>
  </data>
  <data name="Meeting.Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Meeting.StartTime" xml:space="preserve">
    <value>Heure de début</value>
  </data>
  <data name="Meeting.EndTime" xml:space="preserve">
    <value>Heure de fin</value>
  </data>
  <data name="Meeting.Location" xml:space="preserve">
    <value>Lieu</value>
  </data>
  <data name="Meeting.Type" xml:space="preserve">
    <value>Type de réunion</value>
  </data>
  <data name="Meeting.Status" xml:space="preserve">
    <value>Statut</value>
  </data>
  <data name="Meeting.Organizer" xml:space="preserve">
    <value>Organisateur</value>
  </data>
  <data name="Meeting.Attendees" xml:space="preserve">
    <value>Participants</value>
  </data>
  <data name="Meeting.ActionItems" xml:space="preserve">
    <value>Éléments d'action</value>
  </data>
  <data name="Meeting.Documents" xml:space="preserve">
    <value>Documents</value>
  </data>
  <data name="Meeting.Minutes" xml:space="preserve">
    <value>Procès-verbal</value>
  </data>
  <data name="Meeting.Agenda" xml:space="preserve">
    <value>Ordre du jour</value>
  </data>

  <!-- Requirements Management - French -->
  <data name="Requirement.Title" xml:space="preserve">
    <value>Titre de l'exigence</value>
  </data>
  <data name="Requirement.Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Requirement.Type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="Requirement.Priority" xml:space="preserve">
    <value>Priorité</value>
  </data>
  <data name="Requirement.Status" xml:space="preserve">
    <value>Statut</value>
  </data>
  <data name="Requirement.Source" xml:space="preserve">
    <value>Source</value>
  </data>
  <data name="Requirement.Stakeholder" xml:space="preserve">
    <value>Partie prenante</value>
  </data>
  <data name="Requirement.AcceptanceCriteria" xml:space="preserve">
    <value>Critères d'acceptation</value>
  </data>
  <data name="Requirement.BusinessValue" xml:space="preserve">
    <value>Valeur métier</value>
  </data>
  <data name="Requirement.Comments" xml:space="preserve">
    <value>Commentaires</value>
  </data>
  <data name="Requirement.Attachments" xml:space="preserve">
    <value>Pièces jointes</value>
  </data>
  <data name="Requirement.Changes" xml:space="preserve">
    <value>Historique des modifications</value>
  </data>
  <data name="Requirement.Tasks" xml:space="preserve">
    <value>Tâches associées</value>
  </data>

  <!-- Risk Management - French -->
  <data name="Risk.Title" xml:space="preserve">
    <value>Titre du risque</value>
  </data>
  <data name="Risk.Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Risk.Category" xml:space="preserve">
    <value>Catégorie</value>
  </data>
  <data name="Risk.Probability" xml:space="preserve">
    <value>Probabilité</value>
  </data>
  <data name="Risk.Impact" xml:space="preserve">
    <value>Impact</value>
  </data>
  <data name="Risk.Score" xml:space="preserve">
    <value>Score de risque</value>
  </data>
  <data name="Risk.Status" xml:space="preserve">
    <value>Statut</value>
  </data>
  <data name="Risk.Owner" xml:space="preserve">
    <value>Propriétaire du risque</value>
  </data>
  <data name="Risk.MitigationPlan" xml:space="preserve">
    <value>Plan d'atténuation</value>
  </data>
  <data name="Risk.MitigationActions" xml:space="preserve">
    <value>Actions d'atténuation</value>
  </data>
  <data name="Risk.ContingencyPlan" xml:space="preserve">
    <value>Plan de contingence</value>
  </data>

  <!-- Resource Management - French -->
  <data name="Resource.Name" xml:space="preserve">
    <value>Nom de la ressource</value>
  </data>
  <data name="Resource.Type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="Resource.Department" xml:space="preserve">
    <value>Département</value>
  </data>
  <data name="Resource.Location" xml:space="preserve">
    <value>Lieu</value>
  </data>
  <data name="Resource.HourlyRate" xml:space="preserve">
    <value>Taux horaire</value>
  </data>
  <data name="Resource.Capacity" xml:space="preserve">
    <value>Capacité</value>
  </data>
  <data name="Resource.Skills" xml:space="preserve">
    <value>Compétences</value>
  </data>
  <data name="Resource.Availability" xml:space="preserve">
    <value>Disponibilité</value>
  </data>
  <data name="Resource.Utilization" xml:space="preserve">
    <value>Utilisation</value>
  </data>
  <data name="Resource.Allocation" xml:space="preserve">
    <value>Allocation</value>
  </data>

  <!-- Agile/Scrum Terms - French -->
  <data name="Agile.SprintPlanning" xml:space="preserve">
    <value>Planification de sprint</value>
  </data>
  <data name="Agile.SprintReview" xml:space="preserve">
    <value>Revue de sprint</value>
  </data>
  <data name="Agile.SprintRetrospective" xml:space="preserve">
    <value>Rétrospective de sprint</value>
  </data>
  <data name="Agile.DailyStandup" xml:space="preserve">
    <value>Mêlée quotidienne</value>
  </data>
  <data name="Agile.ProductBacklog" xml:space="preserve">
    <value>Backlog produit</value>
  </data>
  <data name="Agile.SprintBacklog" xml:space="preserve">
    <value>Backlog de sprint</value>
  </data>
  <data name="Agile.Definition" xml:space="preserve">
    <value>Définition de terminé</value>
  </data>
  <data name="Agile.StoryPoints" xml:space="preserve">
    <value>Points d'histoire</value>
  </data>
  <data name="Agile.SprintStarted" xml:space="preserve">
    <value>Sprint démarré avec succès</value>
  </data>
  <data name="Agile.SprintCompleted" xml:space="preserve">
    <value>Sprint terminé avec succès</value>
  </data>

  <!-- Enum Values - Meeting Types - French -->
  <data name="Enum.MeetingType.General" xml:space="preserve">
    <value>Général</value>
  </data>
  <data name="Enum.MeetingType.Standup" xml:space="preserve">
    <value>Mêlée quotidienne</value>
  </data>
  <data name="Enum.MeetingType.Planning" xml:space="preserve">
    <value>Planification</value>
  </data>
  <data name="Enum.MeetingType.Review" xml:space="preserve">
    <value>Revue</value>
  </data>
  <data name="Enum.MeetingType.Retrospective" xml:space="preserve">
    <value>Rétrospective</value>
  </data>
  <data name="Enum.MeetingType.Stakeholder" xml:space="preserve">
    <value>Partie prenante</value>
  </data>
  <data name="Enum.MeetingType.Requirements" xml:space="preserve">
    <value>Exigences</value>
  </data>
  <data name="Enum.MeetingType.Technical" xml:space="preserve">
    <value>Technique</value>
  </data>
  <data name="Enum.MeetingType.Status" xml:space="preserve">
    <value>Statut</value>
  </data>

  <!-- Enum Values - Meeting Status - French -->
  <data name="Enum.MeetingStatus.Scheduled" xml:space="preserve">
    <value>Planifié</value>
  </data>
  <data name="Enum.MeetingStatus.InProgress" xml:space="preserve">
    <value>En cours</value>
  </data>
  <data name="Enum.MeetingStatus.Completed" xml:space="preserve">
    <value>Terminé</value>
  </data>
  <data name="Enum.MeetingStatus.Cancelled" xml:space="preserve">
    <value>Annulé</value>
  </data>
  <data name="Enum.MeetingStatus.Postponed" xml:space="preserve">
    <value>Reporté</value>
  </data>

  <!-- Enum Values - Requirement Types - French -->
  <data name="Enum.RequirementType.Functional" xml:space="preserve">
    <value>Fonctionnel</value>
  </data>
  <data name="Enum.RequirementType.NonFunctional" xml:space="preserve">
    <value>Non-fonctionnel</value>
  </data>
  <data name="Enum.RequirementType.Business" xml:space="preserve">
    <value>Métier</value>
  </data>
  <data name="Enum.RequirementType.Technical" xml:space="preserve">
    <value>Technique</value>
  </data>
  <data name="Enum.RequirementType.Performance" xml:space="preserve">
    <value>Performance</value>
  </data>
  <data name="Enum.RequirementType.Security" xml:space="preserve">
    <value>Sécurité</value>
  </data>
  <data name="Enum.RequirementType.Usability" xml:space="preserve">
    <value>Utilisabilité</value>
  </data>
  <data name="Enum.RequirementType.Compliance" xml:space="preserve">
    <value>Conformité</value>
  </data>
  <data name="Enum.RequirementType.Integration" xml:space="preserve">
    <value>Intégration</value>
  </data>

  <!-- Enum Values - Risk Categories - French -->
  <data name="Enum.RiskCategory.Technical" xml:space="preserve">
    <value>Technique</value>
  </data>
  <data name="Enum.RiskCategory.Schedule" xml:space="preserve">
    <value>Calendrier</value>
  </data>
  <data name="Enum.RiskCategory.Budget" xml:space="preserve">
    <value>Budget</value>
  </data>
  <data name="Enum.RiskCategory.Resource" xml:space="preserve">
    <value>Ressource</value>
  </data>
  <data name="Enum.RiskCategory.Quality" xml:space="preserve">
    <value>Qualité</value>
  </data>
  <data name="Enum.RiskCategory.External" xml:space="preserve">
    <value>Externe</value>
  </data>
  <data name="Enum.RiskCategory.Organizational" xml:space="preserve">
    <value>Organisationnel</value>
  </data>

  <!-- Enum Values - Resource Types - French -->
  <data name="Enum.ResourceType.Human" xml:space="preserve">
    <value>Ressource humaine</value>
  </data>
  <data name="Enum.ResourceType.Equipment" xml:space="preserve">
    <value>Équipement</value>
  </data>
  <data name="Enum.ResourceType.Material" xml:space="preserve">
    <value>Matériel</value>
  </data>
  <data name="Enum.ResourceType.Software" xml:space="preserve">
    <value>Logiciel</value>
  </data>
  <data name="Enum.ResourceType.Facility" xml:space="preserve">
    <value>Installation</value>
  </data>

  <!-- WBS Specific Messages - French -->
  <data name="WBS.ErrorLoadingStructure" xml:space="preserve">
    <value>Erreur lors du chargement de la structure WBS</value>
  </data>
  <data name="WBS.TaskCreatedSuccessfully" xml:space="preserve">
    <value>Tâche créée avec succès</value>
  </data>
  <data name="WBS.TaskDeletedSuccessfully" xml:space="preserve">
    <value>Tâche supprimée avec succès</value>
  </data>
  <data name="WBS.TaskDuplicatedSuccessfully" xml:space="preserve">
    <value>Tâche dupliquée avec succès</value>
  </data>
  <data name="WBS.TaskMovedSuccessfully" xml:space="preserve">
    <value>Tâche déplacée {0} avec succès</value>
  </data>
  <data name="WBS.TaskStatusUpdated" xml:space="preserve">
    <value>Statut de la tâche mis à jour vers {0}</value>
  </data>
  <data name="WBS.WbsCodesGenerated" xml:space="preserve">
    <value>Codes WBS générés avec succès</value>
  </data>
  <data name="WBS.ExportingTo" xml:space="preserve">
    <value>Exportation WBS vers {0}...</value>
  </data>
  <data name="WBS.ExportingTask" xml:space="preserve">
    <value>Exportation de la tâche...</value>
  </data>
  <data name="WBS.PrintingWbs" xml:space="preserve">
    <value>Ouverture de la boîte de dialogue d'impression...</value>
  </data>
  <data name="WBS.CompactViewEnabled" xml:space="preserve">
    <value>Vue compacte activée</value>
  </data>
  <data name="WBS.NormalViewEnabled" xml:space="preserve">
    <value>Vue normale activée</value>
  </data>
  <data name="WBS.DetailedViewEnabled" xml:space="preserve">
    <value>Vue détaillée activée</value>
  </data>
  <data name="WBS.RefreshingView" xml:space="preserve">
    <value>Actualisation de la vue WBS...</value>
  </data>
  <data name="WBS.FilteredToShow" xml:space="preserve">
    <value>Filtré pour afficher les tâches {0}</value>
  </data>
  <data name="WBS.ShowingAllTasks" xml:space="preserve">
    <value>Affichage de toutes les tâches</value>
  </data>
  <data name="WBS.FilteredOverdue" xml:space="preserve">
    <value>Filtré pour afficher les tâches en retard</value>
  </data>
  <data name="WBS.TaskTitleRequired" xml:space="preserve">
    <value>Le titre de la tâche est requis</value>
  </data>
  <data name="WBS.ProjectIdMissing" xml:space="preserve">
    <value>L'ID du projet est manquant</value>
  </data>
  <data name="WBS.ValidationError" xml:space="preserve">
    <value>Erreur de validation. Veuillez vérifier votre saisie.</value>
  </data>
  <data name="WBS.AccessDenied" xml:space="preserve">
    <value>Accès refusé. Veuillez actualiser la page et réessayer.</value>
  </data>
  <data name="WBS.RequestFormatError" xml:space="preserve">
    <value>Erreur de format de requête. Veuillez réessayer.</value>
  </data>
  <data name="WBS.ErrorCreatingTask" xml:space="preserve">
    <value>Erreur lors de la création de la tâche</value>
  </data>
  <data name="WBS.ErrorLoadingTaskDetails" xml:space="preserve">
    <value>Erreur lors du chargement des détails de la tâche</value>
  </data>
  <data name="WBS.ErrorDeletingTask" xml:space="preserve">
    <value>Erreur lors de la suppression de la tâche</value>
  </data>
  <data name="WBS.ErrorDuplicatingTask" xml:space="preserve">
    <value>Erreur lors de la duplication de la tâche</value>
  </data>
  <data name="WBS.ErrorMovingTask" xml:space="preserve">
    <value>Erreur lors du déplacement de la tâche {0}</value>
  </data>
  <data name="WBS.ErrorUpdatingTaskStatus" xml:space="preserve">
    <value>Erreur lors de la mise à jour du statut de la tâche</value>
  </data>
  <data name="WBS.ErrorGeneratingCodes" xml:space="preserve">
    <value>Erreur lors de la génération des codes WBS</value>
  </data>
  <data name="WBS.ConfirmDeleteTask" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir supprimer cette tâche ? Cette action ne peut pas être annulée.</value>
  </data>
  <data name="WBS.ConfirmDuplicateTask" xml:space="preserve">
    <value>Créer un duplicata de cette tâche ?</value>
  </data>
  <data name="WBS.ConfirmGenerateCodes" xml:space="preserve">
    <value>Cela va régénérer tous les codes WBS. Continuer ?</value>
  </data>
  <data name="WBS.CreateNewTask" xml:space="preserve">
    <value>Créer une nouvelle tâche</value>
  </data>
  <data name="WBS.CreateChildTaskFor" xml:space="preserve">
    <value>Créer une tâche enfant pour : {0}</value>
  </data>
  <data name="WBS.TaskNotFound" xml:space="preserve">
    <value>Tâche non trouvée</value>
  </data>
  <data name="WBS.UnsupportedExportFormat" xml:space="preserve">
    <value>Format d'exportation non pris en charge</value>
  </data>
  <data name="WBS.GanttViewComingSoon" xml:space="preserve">
    <value>La vue Gantt arrive bientôt !</value>
  </data>

  <!-- WBS Task Actions - French -->
  <data name="WBS.Action.ViewDetails" xml:space="preserve">
    <value>Voir les détails</value>
  </data>
  <data name="WBS.Action.EditTask" xml:space="preserve">
    <value>Modifier la tâche</value>
  </data>
  <data name="WBS.Action.Duplicate" xml:space="preserve">
    <value>Dupliquer</value>
  </data>
  <data name="WBS.Action.AddChild" xml:space="preserve">
    <value>Ajouter un enfant</value>
  </data>
  <data name="WBS.Action.MoreActions" xml:space="preserve">
    <value>Plus d'actions</value>
  </data>
  <data name="WBS.Action.MoveUp" xml:space="preserve">
    <value>Déplacer vers le haut</value>
  </data>
  <data name="WBS.Action.MoveDown" xml:space="preserve">
    <value>Déplacer vers le bas</value>
  </data>
  <data name="WBS.Action.StartTask" xml:space="preserve">
    <value>Démarrer la tâche</value>
  </data>
  <data name="WBS.Action.MarkInReview" xml:space="preserve">
    <value>Marquer en révision</value>
  </data>
  <data name="WBS.Action.MarkComplete" xml:space="preserve">
    <value>Marquer comme terminé</value>
  </data>
  <data name="WBS.Action.CancelTask" xml:space="preserve">
    <value>Annuler la tâche</value>
  </data>
  <data name="WBS.Action.ExportTask" xml:space="preserve">
    <value>Exporter la tâche</value>
  </data>
  <data name="WBS.Action.DeleteTask" xml:space="preserve">
    <value>Supprimer la tâche</value>
  </data>

  <!-- WBS Labels - French -->
  <data name="WBS.Label.Progress" xml:space="preserve">
    <value>Progrès</value>
  </data>
  <data name="WBS.Label.Unassigned" xml:space="preserve">
    <value>Non assigné</value>
  </data>
  <data name="WBS.Label.Overdue" xml:space="preserve">
    <value>En retard</value>
  </data>
  <data name="WBS.Label.Start" xml:space="preserve">
    <value>Début</value>
  </data>
  <data name="WBS.Label.Due" xml:space="preserve">
    <value>Échéance</value>
  </data>
  <data name="WBS.Label.GeneratedOn" xml:space="preserve">
    <value>Généré le</value>
  </data>
  <data name="WBS.Label.WorkBreakdownStructure" xml:space="preserve">
    <value>Structure de décomposition du travail</value>
  </data>

</root>
