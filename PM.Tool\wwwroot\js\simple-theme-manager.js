/**
 * Simple and Reliable Theme Manager for PM Tool
 * Clean implementation with error handling
 */
class SimpleThemeManager {
    constructor() {
        this.currentTheme = 'light';
        this.storageKey = 'pm-tool-theme';
        this.toggleButton = null;
        this.themeIcon = null;
    }

    /**
     * Initialize the theme manager
     */
    init() {
        try {
            this.loadSavedTheme();
            this.applyTheme();
            this.setupToggleButton();
            this.updateIcon();
            console.log('Theme Manager initialized successfully');
        } catch (error) {
            console.error('Error initializing theme manager:', error);
        }
    }

    /**
     * Load saved theme from localStorage
     */
    loadSavedTheme() {
        try {
            const savedTheme = localStorage.getItem(this.storageKey);
            if (savedTheme === 'light' || savedTheme === 'dark') {
                this.currentTheme = savedTheme;
            } else {
                // Default to light theme
                this.currentTheme = 'light';
            }
        } catch (error) {
            console.warn('Error loading saved theme:', error);
            this.currentTheme = 'light';
        }
    }

    /**
     * Apply theme to document
     */
    applyTheme() {
        try {
            // Apply to html and body
            if (document.documentElement) {
                document.documentElement.setAttribute('data-theme', this.currentTheme);
            }
            if (document.body) {
                document.body.setAttribute('data-theme', this.currentTheme);
            }

            // Save to localStorage
            localStorage.setItem(this.storageKey, this.currentTheme);

            // Dispatch event
            window.dispatchEvent(new CustomEvent('themeChanged', {
                detail: { theme: this.currentTheme }
            }));
        } catch (error) {
            console.warn('Error applying theme:', error);
        }
    }

    /**
     * Setup theme toggle button
     */
    setupToggleButton() {
        try {
            this.toggleButton = document.getElementById('theme-toggle');
            this.themeIcon = document.getElementById('theme-icon');
            
            if (this.toggleButton) {
                this.toggleButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.toggle();
                });
            }
        } catch (error) {
            console.warn('Error setting up toggle button:', error);
        }
    }

    /**
     * Toggle theme
     */
    toggle() {
        try {
            this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
            this.applyTheme();
            this.updateIcon();
        } catch (error) {
            console.error('Error toggling theme:', error);
        }
    }

    /**
     * Set specific theme
     */
    setTheme(theme) {
        if (theme === 'light' || theme === 'dark') {
            this.currentTheme = theme;
            this.applyTheme();
            this.updateIcon();
        }
    }

    /**
     * Update toggle button icon
     */
    updateIcon() {
        try {
            if (this.themeIcon) {
                this.themeIcon.className = this.currentTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
            }
            if (this.toggleButton) {
                this.toggleButton.title = this.currentTheme === 'light' ? 'Switch to dark mode' : 'Switch to light mode';
            }
        } catch (error) {
            console.warn('Error updating icon:', error);
        }
    }

    /**
     * Get current theme
     */
    getCurrentTheme() {
        return this.currentTheme;
    }

    /**
     * Check if dark theme is active
     */
    isDarkTheme() {
        return this.currentTheme === 'dark';
    }
}

// Create global instance
let themeManager;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    try {
        themeManager = new SimpleThemeManager();
        themeManager.init();
        
        // Make it globally accessible
        window.ThemeManager = themeManager;
        
        console.log('Simple Theme Manager loaded successfully');
    } catch (error) {
        console.error('Failed to initialize Simple Theme Manager:', error);
    }
});

// Fallback for immediate access
if (document.readyState !== 'loading') {
    try {
        themeManager = new SimpleThemeManager();
        themeManager.init();
        window.ThemeManager = themeManager;
    } catch (error) {
        console.error('Failed to initialize theme manager immediately:', error);
    }
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SimpleThemeManager;
}
