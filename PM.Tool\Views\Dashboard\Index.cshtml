@using PM.Tool.Models.ViewModels
@model DashboardViewModel
@{
    ViewData["Title"] = "Dashboard";
    ViewData["Subtitle"] = "Project overview and analytics";
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100">Dashboard</h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Welcome back! Here's what's happening with your projects.
            </p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
            @{
                ViewData["Text"] = "New Project";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-plus";
                ViewData["Href"] = Url.Action("Create", "Projects");
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "New Task";
                ViewData["Variant"] = "outline";
                ViewData["Icon"] = "fas fa-tasks";
                ViewData["Href"] = Url.Action("Create", "Tasks");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Date Range and Export Controls -->
<div class="card-custom mb-8">
    <div class="card-body-custom">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div>
                <form id="dateRangeForm" class="flex flex-col sm:flex-row gap-4" method="get">
                    <div>
                        @{
                            ViewData["Label"] = "From";
                            ViewData["Name"] = "startDate";
                            ViewData["Type"] = "date";
                            ViewData["Value"] = Model.StartDate.ToString("yyyy-MM-dd");
                            ViewData["ContainerClasses"] = "";
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />
                    </div>
                    <div>
                        @{
                            ViewData["Label"] = "To";
                            ViewData["Name"] = "endDate";
                            ViewData["Type"] = "date";
                            ViewData["Value"] = Model.EndDate.ToString("yyyy-MM-dd");
                            ViewData["ContainerClasses"] = "";
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />
                    </div>
                    <div class="flex items-end">
                        @{
                            ViewData["Text"] = "Apply";
                            ViewData["Variant"] = "primary";
                            ViewData["Type"] = "submit";
                        }
                        <partial name="Components/_Button" view-data="ViewData" />
                    </div>
                </form>
            </div>
            <div class="flex flex-col sm:flex-row gap-3">
                @{
                    ViewData["Text"] = "Export Tasks";
                    ViewData["Variant"] = "outline";
                    ViewData["Icon"] = "fas fa-download";
                    ViewData["Href"] = Url.Action("ExportTasks", new { startDate = Model.StartDate, endDate = Model.EndDate });
                }
                <partial name="Components/_Button" view-data="ViewData" />

                @{
                    ViewData["Text"] = "Export Projects";
                    ViewData["Variant"] = "outline";
                    ViewData["Icon"] = "fas fa-download";
                    ViewData["Href"] = Url.Action("ExportProjects", new { startDate = Model.StartDate, endDate = Model.EndDate });
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
    <!-- Active Projects Card -->
    <div class="card-custom">
        <div class="card-body-custom">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-neutral-500 dark:text-dark-400">Active Projects</p>
                    <p class="text-3xl font-bold text-neutral-900 dark:text-dark-100">@Model.Stats.ActiveProjects</p>
                </div>
                <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-folder-open text-primary-600 dark:text-primary-400 text-xl"></i>
                </div>
            </div>
            <div class="mt-4 flex items-center">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200">
                    @((Model.Stats.TotalProjects > 0 ? (double)Model.Stats.ActiveProjects / Model.Stats.TotalProjects * 100 : 0).ToString("F1"))%
                </span>
                <span class="ml-2 text-sm text-neutral-500 dark:text-dark-400">of total projects</span>
            </div>
        </div>
    </div>

    <!-- My Tasks Card -->
    <div class="card-custom">
        <div class="card-body-custom">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-neutral-500 dark:text-dark-400">My Tasks</p>
                    <p class="text-3xl font-bold text-neutral-900 dark:text-dark-100">@Model.Stats.MyTasks</p>
                </div>
                <div class="w-12 h-12 bg-info-100 dark:bg-info-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-tasks text-info-600 dark:text-info-400 text-xl"></i>
                </div>
            </div>
            <div class="mt-4 flex items-center">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200">
                    @Model.Stats.CompletedTasks completed
                </span>
            </div>
        </div>
    </div>

    <!-- Overall Progress Card -->
    <div class="card-custom">
        <div class="card-body-custom">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-neutral-500 dark:text-dark-400">Overall Progress</p>
                    <p class="text-3xl font-bold text-neutral-900 dark:text-dark-100">@Model.Stats.OverallProgress.ToString("F1")%</p>
                </div>
                <div class="w-12 h-12 bg-success-100 dark:bg-success-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-chart-line text-success-600 dark:text-success-400 text-xl"></i>
                </div>
            </div>
            <div class="mt-4">
                <div class="w-full bg-neutral-200 dark:bg-dark-700 rounded-full h-2">
                    <div class="bg-success-600 h-2 rounded-full transition-all duration-300" style="width: @(Model.Stats.OverallProgress)%"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Overdue Tasks Card -->
    <div class="card-custom">
        <div class="card-body-custom">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-neutral-500 dark:text-dark-400">Overdue Tasks</p>
                    <p class="text-3xl font-bold text-neutral-900 dark:text-dark-100">@Model.Stats.OverdueTasks</p>
                </div>
                <div class="w-12 h-12 bg-danger-100 dark:bg-danger-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle text-danger-600 dark:text-danger-400 text-xl"></i>
                </div>
            </div>
            <div class="mt-4 flex items-center">
                @if (Model.Stats.OverdueTasks > 0)
                {
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200">
                        <i class="fas fa-exclamation-circle mr-1"></i>
                        Needs Attention
                    </span>
                }
                else
                {
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200">
                        <i class="fas fa-check-circle mr-1"></i>
                        All Good
                    </span>
                }
            </div>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="grid grid-cols-1 xl:grid-cols-2 gap-6 mb-8">
    <!-- Project Status Distribution -->
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-chart-pie text-primary-600 dark:text-primary-400 text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Project Status Distribution</h3>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <div class="h-80">
                <canvas id="projectStatusChart" class="w-full h-full"></canvas>
            </div>
        </div>
    </div>

    <!-- Task Priority Distribution -->
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-chart-donut text-primary-600 dark:text-primary-400 text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Task Priority Distribution</h3>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <div class="h-80">
                <canvas id="taskPriorityChart" class="w-full h-full"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="grid grid-cols-1 xl:grid-cols-3 gap-6 mb-8">
    <!-- Project Progress Over Time -->
    <div class="card-custom xl:col-span-2">
        <div class="card-header-custom">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-chart-line text-primary-600 dark:text-primary-400 text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Project Progress Over Time</h3>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <div class="h-80">
                <canvas id="progressChart" class="w-full h-full"></canvas>
            </div>
        </div>
    </div>

    <!-- Team Workload -->
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-users text-primary-600 dark:text-primary-400 text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Team Workload</h3>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <div class="h-80">
                <canvas id="workloadChart" class="w-full h-full"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Velocity and Productivity Charts -->
<div class="grid grid-cols-1 xl:grid-cols-2 gap-6 mb-8">
    <!-- Team Velocity -->
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-tachometer-alt text-primary-600 dark:text-primary-400 text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Team Velocity</h3>
                    <p class="text-sm text-neutral-500 dark:text-dark-400">Avg Velocity: @Model.VelocityMetrics.AverageVelocity.ToString("F1") points/week • Avg Cycle Time: @Model.VelocityMetrics.AverageCycleTime.ToString("F1") days</p>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <div class="h-80">
                <canvas id="velocityChart" class="w-full h-full"></canvas>
            </div>
        </div>
    </div>

    <!-- Team Productivity -->
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-chart-bar text-primary-600 dark:text-primary-400 text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Team Productivity</h3>
                    <p class="text-sm text-neutral-500 dark:text-dark-400">Avg Tasks Per Member: @Model.ProductivityMetrics.AverageTasksPerMember.ToString("F1") • Efficiency Ratio: @Model.ProductivityMetrics.AverageEfficiencyRatio.ToString("F2")</p>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <div class="h-80">
                <canvas id="productivityChart" class="w-full h-full"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Tasks and Milestones -->
<div class="grid grid-cols-1 xl:grid-cols-2 gap-6">
    <!-- My Tasks -->
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user-check text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">My Tasks</h3>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <a href="@Url.Action("Index", "Tasks")" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium">View All</a>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <div class="overflow-x-auto">
                <table class="table-custom">
                    <thead class="table-header-custom">
                        <tr>
                            <th class="table-header-cell-custom">Task</th>
                            <th class="table-header-cell-custom">Project</th>
                            <th class="table-header-cell-custom">Due Date</th>
                            <th class="table-header-cell-custom">Priority</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var task in Model.MyTasks)
                        {
                            <tr class="hover:bg-neutral-50 dark:hover:bg-dark-800 transition-colors">
                                <td class="table-cell-custom">
                                    <a href="/Tasks/Details/@task.Id" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium">
                                        @task.Title
                                    </a>
                                </td>
                                <td class="table-cell-custom">
                                    <a href="/Projects/Details/@task.ProjectId" class="text-neutral-600 dark:text-dark-300 hover:text-primary-600 dark:hover:text-primary-400">
                                        @task.ProjectName
                                    </a>
                                </td>
                                <td class="table-cell-custom text-sm">
                                    @(task.DueDate?.ToString("MMM dd, yyyy") ?? "-")
                                </td>
                                <td class="table-cell-custom">
                                    @{
                                        var priorityClass = task.Priority.ToString().ToLower() switch {
                                            "high" => "bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200",
                                            "medium" => "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
                                            "low" => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
                                            _ => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200"
                                        };
                                    }
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @priorityClass">
                                        @task.Priority
                                    </span>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Upcoming Milestones -->
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-flag-checkered text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Upcoming Milestones</h3>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <a href="@Url.Action("Index", "Projects")" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium">View All</a>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <div class="overflow-x-auto">
                <table class="table-custom">
                    <thead class="table-header-custom">
                        <tr>
                            <th class="table-header-cell-custom">Milestone</th>
                            <th class="table-header-cell-custom">Project</th>
                            <th class="table-header-cell-custom">Due Date</th>
                            <th class="table-header-cell-custom">Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var milestone in Model.UpcomingMilestones)
                        {
                            <tr class="hover:bg-neutral-50 dark:hover:bg-dark-800 transition-colors">
                                <td class="table-cell-custom font-medium">
                                    @milestone.Title
                                </td>
                                <td class="table-cell-custom">
                                    <a href="/Projects/Details/@milestone.ProjectId" class="text-neutral-600 dark:text-dark-300 hover:text-primary-600 dark:hover:text-primary-400">
                                        @milestone.ProjectName
                                    </a>
                                </td>
                                <td class="table-cell-custom text-sm">
                                    @milestone.DueDate.ToString("MMM dd, yyyy")
                                </td>
                                <td class="table-cell-custom">
                                    @{
                                        var statusClass = milestone.Status.ToString().ToLower() switch {
                                            "completed" => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
                                            "inprogress" => "bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200",
                                            "pending" => "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
                                            _ => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200"
                                        };
                                    }
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @statusClass">
                                        @milestone.Status
                                    </span>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Chart.js default configuration for dark mode support
        Chart.defaults.color = document.documentElement.classList.contains('dark') ? '#a1a1aa' : '#6b7280';
        Chart.defaults.borderColor = document.documentElement.classList.contains('dark') ? '#404040' : '#e5e7eb';
        Chart.defaults.backgroundColor = document.documentElement.classList.contains('dark') ? '#262626' : '#ffffff';

        // Listen for theme changes and update charts
        window.addEventListener('themeChanged', function(e) {
            Chart.defaults.color = e.detail.theme === 'dark' ? '#a1a1aa' : '#6b7280';
            Chart.defaults.borderColor = e.detail.theme === 'dark' ? '#404040' : '#e5e7eb';
            Chart.defaults.backgroundColor = e.detail.theme === 'dark' ? '#262626' : '#ffffff';

            // Update all chart instances
            Chart.instances.forEach(chart => {
                chart.update();
            });
        });

        // Project Status Chart
        new Chart(document.getElementById('projectStatusChart'), {
            type: 'doughnut',
            data: {
                labels: @Html.Raw(Json.Serialize(Model.Charts.StatusByProject.Select(x => x.Label))),
                datasets: [{
                    data: @Html.Raw(Json.Serialize(Model.Charts.StatusByProject.Select(x => x.Value))),
                    backgroundColor: @Html.Raw(Json.Serialize(Model.Charts.StatusByProject.Select(x => x.Color))),
                    borderWidth: 2,
                    borderColor: Chart.defaults.backgroundColor
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 12
                            }
                        }
                    }
                }
            }
        });

        // Task Priority Chart
        new Chart(document.getElementById('taskPriorityChart'), {
            type: 'pie',
            data: {
                labels: @Html.Raw(Json.Serialize(Model.Charts.PriorityByTask.Select(x => x.Label))),
                datasets: [{
                    data: @Html.Raw(Json.Serialize(Model.Charts.PriorityByTask.Select(x => x.Value))),
                    backgroundColor: @Html.Raw(Json.Serialize(Model.Charts.PriorityByTask.Select(x => x.Color))),
                    borderWidth: 2,
                    borderColor: Chart.defaults.backgroundColor
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 12
                            }
                        }
                    }
                }
            }
        });

        // Progress Over Time Chart
        new Chart(document.getElementById('progressChart'), {
            type: 'line',
            data: {
                labels: @Html.Raw(Json.Serialize(Model.Charts.ProgressByTimelineProject.Select(x => x.Date?.ToString("MMM dd")))),
                datasets: [{
                    label: 'Project Progress',
                    data: @Html.Raw(Json.Serialize(Model.Charts.ProgressByTimelineProject.Select(x => x.Value))),
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: '#3b82f6',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });

        // Team Workload Chart
        new Chart(document.getElementById('workloadChart'), {
            type: 'bar',
            data: {
                labels: @Html.Raw(Json.Serialize(Model.Charts.WorkloadByTeamMember.Select(x => x.Label))),
                datasets: [{
                    label: 'Active Tasks',
                    data: @Html.Raw(Json.Serialize(Model.Charts.WorkloadByTeamMember.Select(x => x.Value))),
                    backgroundColor: '#3b82f6',
                    borderRadius: 6,
                    borderSkipped: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        // Team Velocity Chart
        new Chart(document.getElementById('velocityChart'), {
            type: 'line',
            data: {
                labels: @Html.Raw(Json.Serialize(Model.VelocityMetrics.WeeklyVelocity.Select(x => x.Date.ToString("MMM dd")))),
                datasets: [{
                    label: 'Velocity (Points)',
                    data: @Html.Raw(Json.Serialize(Model.VelocityMetrics.WeeklyVelocity.Select(x => x.CompletedPoints))),
                    borderColor: '#22c55e',
                    backgroundColor: 'rgba(34, 197, 94, 0.1)',
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: '#22c55e',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                }, {
                    label: 'Tasks Completed',
                    data: @Html.Raw(Json.Serialize(Model.VelocityMetrics.WeeklyVelocity.Select(x => x.CompletedTasks))),
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: '#3b82f6',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Team Productivity Chart
        new Chart(document.getElementById('productivityChart'), {
            type: 'bar',
            data: {
                labels: @Html.Raw(Json.Serialize(Model.ProductivityMetrics.TeamMembers.Select(x => x.MemberName))),
                datasets: [{
                    label: 'Tasks Completed',
                    data: @Html.Raw(Json.Serialize(Model.ProductivityMetrics.TeamMembers.Select(x => x.CompletedTasks))),
                    backgroundColor: '#22c55e',
                    borderRadius: 6,
                    borderSkipped: false
                }, {
                    label: 'Hours Spent',
                    data: @Html.Raw(Json.Serialize(Model.ProductivityMetrics.TeamMembers.Select(x => x.TotalHours))),
                    backgroundColor: '#3b82f6',
                    borderRadius: 6,
                    borderSkipped: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Date range picker initialization
        document.getElementById('dateRangeForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            window.location.href = `@Url.Action("Index")?startDate=${startDate}&endDate=${endDate}`;
        });
    </script>
}
