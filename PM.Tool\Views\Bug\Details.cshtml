@model PM.Tool.Models.ViewModels.BugDetailsViewModel
@using PM.Tool.Core.Entities.Agile

@{
    ViewData["Title"] = $"Bug: {Model.Bug.Title}";
    ViewData["PageTitle"] = $"{Model.Bug.BugKey} - {Model.Bug.Title}";
    ViewData["PageDescription"] = "Bug details, resolution progress, and activity history.";
}

@section Styles {
    <style>
        .severity-indicator {
            width: 6px;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
            border-radius: 0.5rem 0 0 0.5rem;
        }
        .comment-card {
            transition: all 0.2s ease-in-out;
        }
        .comment-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .timeline-item {
            position: relative;
            padding-left: 2rem;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: 0.5rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e5e7eb;
        }
        .timeline-item:last-child::before {
            display: none;
        }
        .timeline-dot {
            position: absolute;
            left: 0;
            top: 0.5rem;
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            border: 2px solid #fff;
            z-index: 1;
        }
    </style>
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <div class="flex items-center space-x-3 mb-2">
                <span class="text-sm font-mono text-neutral-500 dark:text-dark-400 bg-neutral-100 dark:bg-dark-300 px-3 py-1 rounded">
                    @Model.Bug.BugKey
                </span>
                <span class="px-3 py-1 text-sm font-medium rounded-full @GetSeverityBadgeClass(Model.Bug.Severity)">
                    @Model.Bug.Severity
                </span>
                <span class="px-3 py-1 text-sm font-medium rounded-full @GetStatusBadgeClass(Model.Bug.Status)">
                    @Model.Bug.Status
                </span>
                <span class="px-3 py-1 text-sm font-medium rounded-full @GetPriorityBadgeClass(Model.Bug.Priority)">
                    @Model.Bug.Priority
                </span>
                @if (Model.Bug.IsOverdue)
                {
                    <span class="px-3 py-1 text-sm font-medium rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                        <i class="fas fa-clock mr-1"></i>Overdue
                    </span>
                }
            </div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-bug mr-3 text-primary-600 dark:text-primary-400"></i>
                @Model.Bug.Title
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                @if (Model.Bug.Feature != null)
                {
                    <span>Feature: @Model.Bug.Feature.Title | </span>
                }
                Project: @Model.Bug.Project?.Name
            </p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
            @{
                ViewData["Text"] = "Edit";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-edit";
                ViewData["Href"] = Url.Action("Edit", new { id = Model.Bug.Id });
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "Back to List";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Index", new { projectId = Model.Bug.ProjectId });
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Bug Overview -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
    <!-- Main Details -->
    <div class="lg:col-span-2">
        @{
            ViewData["Title"] = "Bug Details";
            ViewData["Icon"] = "fas fa-info-circle";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="space-y-6">
                <!-- Description -->
                <div>
                    <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-100 mb-2">Description</h4>
                    <p class="text-neutral-600 dark:text-dark-300">@Model.Bug.Description</p>
                </div>

                <!-- Steps to Reproduce -->
                @if (!string.IsNullOrEmpty(Model.Bug.StepsToReproduce))
                {
                    <div>
                        <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-100 mb-2">Steps to Reproduce</h4>
                        <div class="bg-neutral-50 dark:bg-dark-300 rounded-lg p-4">
                            <pre class="text-sm text-neutral-600 dark:text-dark-300 whitespace-pre-wrap">@Model.Bug.StepsToReproduce</pre>
                        </div>
                    </div>
                }

                <!-- Expected vs Actual Results -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    @if (!string.IsNullOrEmpty(Model.Bug.ExpectedResult))
                    {
                        <div>
                            <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-100 mb-2">Expected Result</h4>
                            <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                                <p class="text-sm text-green-800 dark:text-green-200">@Model.Bug.ExpectedResult</p>
                            </div>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.Bug.ActualResult))
                    {
                        <div>
                            <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-100 mb-2">Actual Result</h4>
                            <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                                <p class="text-sm text-red-800 dark:text-red-200">@Model.Bug.ActualResult</p>
                            </div>
                        </div>
                    }
                </div>

                <!-- Version Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    @if (!string.IsNullOrEmpty(Model.Bug.FoundInVersion))
                    {
                        <div>
                            <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-100 mb-2">Found in Version</h4>
                            <p class="text-neutral-600 dark:text-dark-300 font-mono">@Model.Bug.FoundInVersion</p>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.Bug.FixedInVersion))
                    {
                        <div>
                            <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-100 mb-2">Fixed in Version</h4>
                            <p class="text-neutral-600 dark:text-dark-300 font-mono">@Model.Bug.FixedInVersion</p>
                        </div>
                    }
                </div>

                <!-- Tags -->
                @if (!string.IsNullOrEmpty(Model.Bug.Tags))
                {
                    <div>
                        <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-100 mb-2">Tags</h4>
                        <div class="flex flex-wrap gap-2">
                            @foreach (var tag in Model.Bug.Tags.Split(',', StringSplitOptions.RemoveEmptyEntries))
                            {
                                <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full">
                                    @tag.Trim()
                                </span>
                            }
                        </div>
                    </div>
                }
            </div>
        </partial>
    </div>

    <!-- Bug Info & Metrics -->
    <div class="space-y-6">
        <!-- Bug Information -->
        @{
            ViewData["Title"] = "Information";
            ViewData["Icon"] = "fas fa-info";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="space-y-3">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-neutral-600 dark:text-dark-300">Reported By</span>
                    <span class="font-medium">@(Model.Bug.ReportedBy?.UserName ?? "Unknown")</span>
                </div>
                @if (!string.IsNullOrEmpty(Model.Bug.AssignedTo?.UserName))
                {
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-neutral-600 dark:text-dark-300">Assigned To</span>
                        <span class="font-medium">@Model.Bug.AssignedTo.UserName</span>
                    </div>
                }
                @if (Model.Bug.TargetDate.HasValue)
                {
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-neutral-600 dark:text-dark-300">Target Date</span>
                        <span class="font-medium @(Model.Bug.IsOverdue ? "text-red-600 dark:text-red-400" : "")">
                            @Model.Bug.TargetDate.Value.ToString("MMM dd, yyyy")
                        </span>
                    </div>
                }
                <div class="flex justify-between items-center">
                    <span class="text-sm text-neutral-600 dark:text-dark-300">Created</span>
                    <span class="font-medium">@Model.Bug.CreatedAt.ToString("MMM dd, yyyy")</span>
                </div>
                @if (Model.Bug.ResolvedDate.HasValue)
                {
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-neutral-600 dark:text-dark-300">Resolved</span>
                        <span class="font-medium text-green-600 dark:text-green-400">@Model.Bug.ResolvedDate.Value.ToString("MMM dd, yyyy")</span>
                    </div>
                }
                <div class="flex justify-between items-center">
                    <span class="text-sm text-neutral-600 dark:text-dark-300">Days Open</span>
                    <span class="font-medium">@Model.Metrics.DaysOpen</span>
                </div>
            </div>
        </partial>

        <!-- Metrics -->
        @{
            ViewData["Title"] = "Metrics";
            ViewData["Icon"] = "fas fa-chart-bar";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="space-y-4">
                @if (Model.Metrics.TimeToResolve.HasValue)
                {
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-neutral-600 dark:text-dark-300">Time to Resolve</span>
                        <span class="font-medium">@Model.Metrics.TimeToResolve.Value.Days days</span>
                    </div>
                }
                <div class="flex justify-between items-center">
                    <span class="text-sm text-neutral-600 dark:text-dark-300">Comments</span>
                    <span class="font-medium">@Model.Metrics.TotalComments</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-neutral-600 dark:text-dark-300">Attachments</span>
                    <span class="font-medium">@Model.Metrics.TotalAttachments</span>
                </div>
                @if (Model.Bug.EstimatedHours > 0)
                {
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-neutral-600 dark:text-dark-300">Estimated Hours</span>
                        <span class="font-medium">@Model.Bug.EstimatedHours</span>
                    </div>
                }
                @if (Model.Bug.ActualHours > 0)
                {
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-neutral-600 dark:text-dark-300">Actual Hours</span>
                        <span class="font-medium">@Model.Bug.ActualHours</span>
                    </div>
                }
            </div>
        </partial>

        <!-- Quick Actions -->
        @{
            ViewData["Title"] = "Quick Actions";
            ViewData["Icon"] = "fas fa-bolt";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="space-y-3">
                @if (Model.Bug.Status != BugStatus.Resolved && Model.Bug.Status != BugStatus.Closed)
                {
                    ViewData["Text"] = "Resolve Bug";
                    ViewData["Variant"] = "success";
                    ViewData["Icon"] = "fas fa-check";
                    ViewData["Href"] = "#";
                    ViewData["OnClick"] = "showResolveModal()";
                    ViewData["FullWidth"] = true;
                    <partial name="Components/_Button" view-data="ViewData" />
                }

                @if (string.IsNullOrEmpty(Model.Bug.AssignedToUserId))
                {
                    ViewData["Text"] = "Assign to Me";
                    ViewData["Variant"] = "primary";
                    ViewData["Icon"] = "fas fa-user";
                    ViewData["Href"] = "#";
                    ViewData["OnClick"] = "assignToMe()";
                    ViewData["FullWidth"] = true;
                    <partial name="Components/_Button" view-data="ViewData" />
                }

                @{
                    ViewData["Text"] = "Add Comment";
                    ViewData["Variant"] = "secondary";
                    ViewData["Icon"] = "fas fa-comment";
                    ViewData["Href"] = "#comments";
                    ViewData["FullWidth"] = true;
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
        </partial>
    </div>
</div>

<!-- Comments Section -->
@{
    ViewData["Title"] = $"Comments ({Model.Comments.Count()})";
    ViewData["Icon"] = "fas fa-comments";
    ViewData["ShowHeader"] = true;
}
<partial name="Components/_Card" view-data="ViewData">
    <div id="comments" class="space-y-6">
        <!-- Add Comment Form -->
        <form id="addCommentForm" class="bg-neutral-50 dark:bg-dark-300 rounded-lg p-4">
            <div class="space-y-4">
                @{
                    ViewData["Label"] = "Add Comment";
                    ViewData["Name"] = "commentContent";
                    ViewData["Type"] = "textarea";
                    ViewData["Required"] = true;
                    ViewData["Icon"] = "fas fa-comment";
                    ViewData["Placeholder"] = "Add your comment...";
                    ViewData["Rows"] = "3";
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <div class="flex justify-between items-center">
                    <div class="flex space-x-2">
                        <label class="inline-flex items-center">
                            <input type="radio" name="commentType" value="@((int)PM.Tool.Core.Entities.Agile.CommentType.General)" checked class="form-radio">
                            <span class="ml-2 text-sm">General</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="commentType" value="@((int)PM.Tool.Core.Entities.Agile.CommentType.Resolution)" class="form-radio">
                            <span class="ml-2 text-sm">Resolution</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="commentType" value="@((int)PM.Tool.Core.Entities.Agile.CommentType.StatusUpdate)" class="form-radio">
                            <span class="ml-2 text-sm">Status Update</span>
                        </label>
                    </div>
                    @{
                        ViewData["Text"] = "Add Comment";
                        ViewData["Variant"] = "primary";
                        ViewData["Icon"] = "fas fa-plus";
                        ViewData["Type"] = "submit";
                        ViewData["Href"] = null;
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </div>
            </div>
        </form>

        <!-- Comments List -->
        @if (Model.Comments.Any())
        {
            <div class="space-y-4">
                @foreach (var comment in Model.Comments.OrderByDescending(c => c.CreatedAt))
                {
                    <div class="comment-card bg-white dark:bg-surface-dark border border-neutral-200 dark:border-dark-200 rounded-lg p-4">
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-primary-600 dark:text-primary-400 text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center space-x-2 mb-2">
                                    <span class="font-medium text-neutral-900 dark:text-dark-100">@(comment.User?.UserName ?? "Unknown User")</span>
                                    <span class="text-sm text-neutral-500 dark:text-dark-400">@comment.CreatedAt.ToString("MMM dd, yyyy 'at' h:mm tt")</span>
                                    <span class="px-2 py-1 text-xs font-medium rounded-full @GetCommentTypeBadgeClass(comment.Type)">
                                        @comment.Type
                                    </span>
                                </div>
                                <p class="text-neutral-600 dark:text-dark-300">@comment.Content</p>
                            </div>
                        </div>
                    </div>
                }
            </div>
        }
        else
        {
            <div class="text-center py-8">
                <i class="fas fa-comments text-3xl text-neutral-300 dark:text-dark-400 mb-3"></i>
                <p class="text-neutral-500 dark:text-dark-400">No comments yet. Be the first to add one!</p>
            </div>
        }
    </div>
</partial>

@functions {
    private string GetSeverityBadgeClass(BugSeverity severity)
    {
        return severity switch
        {
            BugSeverity.Critical => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            BugSeverity.High => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
            BugSeverity.Medium => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            BugSeverity.Low => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100"
        };
    }

    private string GetStatusBadgeClass(BugStatus status)
    {
        return status switch
        {
            BugStatus.New => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            BugStatus.Assigned => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            BugStatus.InProgress => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
            BugStatus.Resolved => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            BugStatus.Closed => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100"
        };
    }

    private string GetPriorityBadgeClass(BugPriority priority)
    {
        return priority switch
        {
            BugPriority.Low => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            BugPriority.Medium => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            BugPriority.High => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
            BugPriority.Critical => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100"
        };
    }

    private string GetCommentTypeBadgeClass(PM.Tool.Core.Entities.Agile.CommentType type)
    {
        return type switch
        {
            PM.Tool.Core.Entities.Agile.CommentType.General => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
            PM.Tool.Core.Entities.Agile.CommentType.Resolution => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            PM.Tool.Core.Entities.Agile.CommentType.StatusUpdate => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100"
        };
    }
}

@section Scripts {
    <script>
        $(document).ready(function() {
            setupCommentForm();
        });

        function setupCommentForm() {
            $('#addCommentForm').on('submit', function(e) {
                e.preventDefault();

                const content = $('textarea[name="commentContent"]').val().trim();
                const type = $('input[name="commentType"]:checked').val();

                if (!content) {
                    alert('Please enter a comment.');
                    return;
                }

                // Create comment data
                const commentData = {
                    BugId: @Model.Bug.Id,
                    Content: content,
                    Type: parseInt(type)
                };

                // Submit comment (this would typically be an AJAX call)
                $.post('@Url.Action("AddComment")', commentData)
                    .done(function() {
                        location.reload(); // Refresh to show new comment
                    })
                    .fail(function() {
                        alert('Error adding comment. Please try again.');
                    });
            });
        }

        function showResolveModal() {
            const resolution = prompt('Enter resolution details:');
            if (resolution) {
                // This would typically make an AJAX call to resolve the bug
                window.location.href = '@Url.Action("Edit", new { id = Model.Bug.Id })' + '?status=@((int)BugStatus.Resolved)&resolution=' + encodeURIComponent(resolution);
            }
        }

        function assignToMe() {
            if (confirm('Assign this bug to yourself?')) {
                // This would typically make an AJAX call to assign the bug
                window.location.href = '@Url.Action("Edit", new { id = Model.Bug.Id })' + '?assignToMe=true';
            }
        }
    </script>
}
