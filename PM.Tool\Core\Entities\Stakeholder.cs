using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities
{
    public class Stakeholder : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(100)]
        public string? Title { get; set; }

        [MaxLength(200)]
        public string? Organization { get; set; }

        [MaxLength(100)]
        public string? Department { get; set; }

        [Required]
        [MaxLength(255)]
        public string Email { get; set; } = string.Empty;

        [MaxLength(20)]
        public string? Phone { get; set; }

        public StakeholderType Type { get; set; } = StakeholderType.Internal;

        public StakeholderRole Role { get; set; } = StakeholderRole.User;

        public InfluenceLevel Influence { get; set; } = InfluenceLevel.Medium;

        public InterestLevel Interest { get; set; } = InterestLevel.Medium;

        public bool IsActive { get; set; } = true;

        [MaxLength(1000)]
        public string? Notes { get; set; }

        [MaxLength(500)]
        public string? CommunicationPreferences { get; set; }

        public string? UserId { get; set; } // Link to ApplicationUser if they have system access

        // Navigation properties
        public virtual ApplicationUser? User { get; set; }
        public virtual ICollection<ProjectStakeholder> ProjectStakeholders { get; set; } = new List<ProjectStakeholder>();
        public virtual ICollection<StakeholderCommunication> Communications { get; set; } = new List<StakeholderCommunication>();

        // Computed properties
        public StakeholderPriority Priority => GetStakeholderPriority();
        public string FullContact => $"{Name} ({Email})";

        private StakeholderPriority GetStakeholderPriority()
        {
            var influenceScore = (int)Influence;
            var interestScore = (int)Interest;
            var totalScore = influenceScore + interestScore;

            return totalScore switch
            {
                >= 8 => StakeholderPriority.Critical,
                >= 6 => StakeholderPriority.High,
                >= 4 => StakeholderPriority.Medium,
                _ => StakeholderPriority.Low
            };
        }
    }

    public class ProjectStakeholder : BaseEntity
    {
        public int ProjectId { get; set; }

        public int StakeholderId { get; set; }

        public ProjectRole ProjectRole { get; set; } = ProjectRole.Stakeholder;

        public bool IsKeyStakeholder { get; set; } = false;

        public bool ReceiveUpdates { get; set; } = true;

        [MaxLength(1000)]
        public string? ProjectSpecificNotes { get; set; }

        private DateTime _assignedDate = DateTime.UtcNow;
        public DateTime AssignedDate
        {
            get => _assignedDate;
            set => _assignedDate = DateTime.SpecifyKind(value, DateTimeKind.Utc);
        }

        // Navigation properties
        public virtual Project Project { get; set; } = null!;
        public virtual Stakeholder Stakeholder { get; set; } = null!;
    }

    public class StakeholderCommunication : BaseEntity
    {
        public int StakeholderId { get; set; }

        public int? ProjectId { get; set; }

        [Required]
        [MaxLength(200)]
        public string Subject { get; set; } = string.Empty;

        [Required]
        [MaxLength(5000)]
        public string Content { get; set; } = string.Empty;

        public CommunicationType Type { get; set; } = CommunicationType.Email;

        public CommunicationDirection Direction { get; set; } = CommunicationDirection.Outbound;

        private DateTime _communicationDate = DateTime.UtcNow;
        public DateTime CommunicationDate
        {
            get => _communicationDate;
            set => _communicationDate = DateTime.SpecifyKind(value, DateTimeKind.Utc);
        }

        public string CommunicatedByUserId { get; set; } = string.Empty;

        [MaxLength(1000)]
        public string? Notes { get; set; }

        public bool RequiresFollowUp { get; set; } = false;

        private DateTime? _followUpDate;
        public DateTime? FollowUpDate
        {
            get => _followUpDate;
            set => _followUpDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        // Navigation properties
        public virtual Stakeholder Stakeholder { get; set; } = null!;
        public virtual Project? Project { get; set; }
        public virtual ApplicationUser CommunicatedBy { get; set; } = null!;
    }

    public enum StakeholderType
    {
        Internal = 1,
        External = 2,
        Customer = 3,
        Vendor = 4,
        Regulatory = 5,
        Partner = 6
    }

    public enum StakeholderRole
    {
        Sponsor = 1,
        Owner = 2,
        User = 3,
        Approver = 4,
        Reviewer = 5,
        Consultant = 6,
        Observer = 7
    }

    public enum InfluenceLevel
    {
        VeryLow = 1,
        Low = 2,
        Medium = 3,
        High = 4,
        VeryHigh = 5
    }

    public enum InterestLevel
    {
        VeryLow = 1,
        Low = 2,
        Medium = 3,
        High = 4,
        VeryHigh = 5
    }

    public enum StakeholderPriority
    {
        Low = 1,
        Medium = 2,
        High = 3,
        Critical = 4
    }

    public enum ProjectRole
    {
        Stakeholder = 1,
        Sponsor = 2,
        Owner = 3,
        Manager = 4,
        TeamMember = 5,
        Reviewer = 6,
        Approver = 7
    }

    public enum CommunicationType
    {
        Email = 1,
        Phone = 2,
        Meeting = 3,
        Document = 4,
        Presentation = 5,
        Workshop = 6,
        Survey = 7
    }

    public enum CommunicationDirection
    {
        Inbound = 1,
        Outbound = 2,
        Bidirectional = 3
    }
}
