using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;

namespace PM.Tool.Core.Interfaces
{
    public interface IAuditService
    {
        Task LogAsync(AuditAction action, string entityName, int? entityId, string userId, string? oldValues = null, string? newValues = null, string? ipAddress = null, string? userAgent = null);
        Task<IEnumerable<AuditLog>> GetAuditLogsAsync(string? entityName = null, int? entityId = null, string? userId = null, DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<AuditLog>> GetUserActivityAsync(string userId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<AuditLog>> GetEntityHistoryAsync(string entityName, int entityId);
    }
}
