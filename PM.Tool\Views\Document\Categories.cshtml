@model IEnumerable<DocumentCategoryViewModel>
@{
    ViewData["Title"] = "Document Categories";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Document Categories</h2>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createCategoryModal">
            <i class="bi bi-folder-plus me-2"></i>New Category
        </button>
    </div>

    @if (!Model.Any())
    {
        <div class="alert alert-info">
            No document categories have been created yet.
        </div>
    }
    else
    {
        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
            @foreach (var category in Model)
            {
                <div class="col">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-folder me-2"></i>@category.Name
                            </h5>
                            @if (!string.IsNullOrEmpty(category.Description))
                            {
                                <p class="card-text">@category.Description</p>
                            }
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <div class="text-muted small">
                                    <div>Created by @category.CreatedByName</div>
                                    <div>@category.CreatedAt.ToString("MMM dd, yyyy")</div>
                                </div>
                                <div class="text-end small">
                                    <div>@category.ProjectAttachmentCount Project Files</div>
                                    <div>@category.TaskAttachmentCount Task Files</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
</div>

<!-- Create Category Modal -->
<div class="modal fade" id="createCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form asp-action="CreateCategory" method="post">
                @Html.AntiForgeryToken()
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">Category Name</label>
                        <input type="text" class="form-control" id="categoryName" name="Name" required maxlength="100">
                    </div>
                    <div class="mb-3">
                        <label for="categoryDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="categoryDescription" name="Description" maxlength="500" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Category</button>
                </div>
            </form>
        </div>
    </div>
</div>
