using System.ComponentModel.DataAnnotations;

using PM.Tool.Core.Entities;

namespace PM.Tool.Models.ViewModels
{
    public class RequirementCreateViewModel : BaseCreateViewModel, IEntityViewModel<Requirement>
    {
        [Required]
        [MaxLength(50)]
        public string RequirementId { get; set; } = string.Empty;

        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [MaxLength(2000)]
        public string Description { get; set; } = string.Empty;

        [Required]
        public int ProjectId { get; set; }

        public int? ParentRequirementId { get; set; }

        public RequirementType Type { get; set; } = RequirementType.Functional;

        public RequirementPriority Priority { get; set; } = RequirementPriority.Medium;

        public RequirementStatus Status { get; set; } = RequirementStatus.Draft;

        public string? StakeholderUserId { get; set; }

        public string? AnalystUserId { get; set; }

        public string? DeveloperUserId { get; set; }

        public string? TesterUserId { get; set; }

        [MaxLength(1000)]
        public string? AcceptanceCriteria { get; set; }

        [MaxLength(500)]
        public string? BusinessJustification { get; set; }

        public decimal EstimatedEffort { get; set; }

        public DateTime? TargetDate { get; set; }

        public Requirement ToEntity()
        {
            return new Requirement
            {
                RequirementId = RequirementId,
                Title = Title,
                Description = Description,
                ProjectId = ProjectId,
                ParentRequirementId = ParentRequirementId,
                Type = Type,
                Priority = Priority,
                Status = Status,
                StakeholderUserId = StakeholderUserId,
                AnalystUserId = AnalystUserId,
                DeveloperUserId = DeveloperUserId,
                TesterUserId = TesterUserId,
                AcceptanceCriteria = AcceptanceCriteria,
                BusinessJustification = BusinessJustification,
                EstimatedEffort = EstimatedEffort,
                TargetDate = TargetDate
            };
        }

        public void UpdateEntity(Requirement requirement)
        {
            requirement.RequirementId = RequirementId;
            requirement.Title = Title;
            requirement.Description = Description;
            requirement.ProjectId = ProjectId;
            requirement.ParentRequirementId = ParentRequirementId;
            requirement.Type = Type;
            requirement.Priority = Priority;
            requirement.Status = Status;
            requirement.StakeholderUserId = StakeholderUserId;
            requirement.AnalystUserId = AnalystUserId;
            requirement.DeveloperUserId = DeveloperUserId;
            requirement.TesterUserId = TesterUserId;
            requirement.AcceptanceCriteria = AcceptanceCriteria;
            requirement.BusinessJustification = BusinessJustification;
            requirement.EstimatedEffort = EstimatedEffort;
            requirement.TargetDate = TargetDate;
        }
    }

    public class RequirementEditViewModel : RequirementCreateViewModel, IFromEntity<Requirement, RequirementEditViewModel>
    {
        public int Id { get; set; }
        // Note: CreatedBy info comes from BaseEntity, not a separate property
        public DateTime? ApprovedDate { get; set; }
        public string? ApprovedByUserId { get; set; }

        public static RequirementEditViewModel FromEntity(Requirement requirement)
        {
            return new RequirementEditViewModel
            {
                Id = requirement.Id,
                RequirementId = requirement.RequirementId,
                Title = requirement.Title,
                Description = requirement.Description,
                ProjectId = requirement.ProjectId,
                ParentRequirementId = requirement.ParentRequirementId,
                Type = requirement.Type,
                Priority = requirement.Priority,
                Status = requirement.Status,
                StakeholderUserId = requirement.StakeholderUserId,
                AnalystUserId = requirement.AnalystUserId,
                DeveloperUserId = requirement.DeveloperUserId,
                TesterUserId = requirement.TesterUserId,
                AcceptanceCriteria = requirement.AcceptanceCriteria,
                BusinessJustification = requirement.BusinessJustification,
                EstimatedEffort = requirement.EstimatedEffort,
                TargetDate = requirement.TargetDate,
                // CreatedBy info comes from BaseEntity
                ApprovedDate = requirement.ApprovedDate,
                ApprovedByUserId = requirement.ApprovedByUserId
            };
        }

        // For backward compatibility
        public static RequirementEditViewModel FromRequirement(Requirement requirement) => FromEntity(requirement);
    }
}
