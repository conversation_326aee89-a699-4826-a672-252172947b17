using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;

namespace PM.Tool.Application.Services
{
    public class DocumentService : IDocumentService
    {
        private readonly ApplicationDbContext _context;

        public DocumentService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<DocumentCategory> CreateCategoryAsync(DocumentCategory category)
        {
            _context.DocumentCategories.Add(category);
            await _context.SaveChangesAsync();
            return category;
        }

        public async Task<DocumentCategory?> GetCategoryByIdAsync(int categoryId)
        {
            return await _context.DocumentCategories
                .Include(c => c.CreatedBy)
                .FirstOrDefaultAsync(c => c.Id == categoryId);
        }

        public async Task<IEnumerable<DocumentCategory>> GetAllCategoriesAsync()
        {
            return await _context.DocumentCategories
                .Include(c => c.CreatedBy)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<DocumentCategory> UpdateCategoryAsync(DocumentCategory category)
        {
            _context.DocumentCategories.Update(category);
            await _context.SaveChangesAsync();
            return category;
        }

        public async Task<bool> DeleteCategoryAsync(int categoryId)
        {
            var category = await _context.DocumentCategories.FindAsync(categoryId);
            if (category == null)
                return false;

            _context.DocumentCategories.Remove(category);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<DocumentVersion> AddVersionAsync(DocumentVersion version)
        {
            _context.DocumentVersions.Add(version);
            
            // Update the current version number on the parent attachment
            if (version.AttachmentType == "Project")
            {
                var projectAttachment = await _context.ProjectAttachments.FindAsync(version.AttachmentId);
                if (projectAttachment != null)
                {
                    projectAttachment.CurrentVersion = version.Version;
                    projectAttachment.LastModifiedAt = DateTime.UtcNow;
                }
            }
            else if (version.AttachmentType == "Task")
            {
                var taskAttachment = await _context.TaskAttachments.FindAsync(version.AttachmentId);
                if (taskAttachment != null)
                {
                    taskAttachment.CurrentVersion = version.Version;
                    taskAttachment.LastModifiedAt = DateTime.UtcNow;
                }
            }

            await _context.SaveChangesAsync();
            return version;
        }

        public async Task<DocumentVersion?> GetVersionByIdAsync(int versionId)
        {
            return await _context.DocumentVersions
                .Include(v => v.UploadedBy)
                .FirstOrDefaultAsync(v => v.Id == versionId);
        }

        public async Task<IEnumerable<DocumentVersion>> GetVersionsForAttachmentAsync(int attachmentId, string attachmentType)
        {
            return await _context.DocumentVersions
                .Include(v => v.UploadedBy)
                .Where(v => v.AttachmentId == attachmentId && v.AttachmentType == attachmentType)
                .OrderByDescending(v => v.Version)
                .ToListAsync();
        }

        public async Task<bool> DeleteVersionAsync(int versionId)
        {
            var version = await _context.DocumentVersions.FindAsync(versionId);
            if (version == null)
                return false;

            _context.DocumentVersions.Remove(version);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<(ProjectAttachment?, TaskAttachment?)> UpdateAttachmentCategoryAsync(
            int attachmentId,
            string attachmentType,
            int? categoryId)
        {
            if (attachmentType == "Project")
            {
                var attachment = await _context.ProjectAttachments.FindAsync(attachmentId);
                if (attachment != null)
                {
                    attachment.CategoryId = categoryId;
                    attachment.LastModifiedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                    return (attachment, null);
                }
            }
            else if (attachmentType == "Task")
            {
                var attachment = await _context.TaskAttachments.FindAsync(attachmentId);
                if (attachment != null)
                {
                    attachment.CategoryId = categoryId;
                    attachment.LastModifiedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                    return (null, attachment);
                }
            }

            return (null, null);
        }

        public async Task<(ProjectAttachment?, TaskAttachment?)> UpdateAttachmentMetadataAsync(
            int attachmentId,
            string attachmentType,
            string? description = null,
            string? tags = null)
        {
            if (attachmentType == "Project")
            {
                var attachment = await _context.ProjectAttachments.FindAsync(attachmentId);
                if (attachment != null)
                {
                    if (description != null) attachment.Description = description;
                    if (tags != null) attachment.Tags = tags;
                    attachment.LastModifiedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                    return (attachment, null);
                }
            }
            else if (attachmentType == "Task")
            {
                var attachment = await _context.TaskAttachments.FindAsync(attachmentId);
                if (attachment != null)
                {
                    if (description != null) attachment.Description = description;
                    if (tags != null) attachment.Tags = tags;
                    attachment.LastModifiedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                    return (null, attachment);
                }
            }

            return (null, null);
        }
    }
}
