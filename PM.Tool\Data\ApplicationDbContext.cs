﻿using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Entities.Agile;
using PM.Tool.Core.Enums;

namespace PM.Tool.Data
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        // DbSets
        public DbSet<Project> Projects { get; set; }
        public DbSet<TaskEntity> Tasks { get; set; }
        public DbSet<ProjectMember> ProjectMembers { get; set; }
        public DbSet<Milestone> Milestones { get; set; }
        public DbSet<TaskComment> TaskComments { get; set; }
        public DbSet<TaskAttachment> TaskAttachments { get; set; }
        public DbSet<ProjectAttachment> ProjectAttachments { get; set; }
        public DbSet<Notification> Notifications { get; set; }
        public DbSet<AuditLog> AuditLogs { get; set; }
        public DbSet<Comment> Comments { get; set; }
        public DbSet<TimeEntry> TimeEntries { get; set; }
        public DbSet<TaskDependency> TaskDependencies { get; set; }
        public DbSet<DocumentCategory> DocumentCategories { get; set; }
        public DbSet<DocumentVersion> DocumentVersions { get; set; }

        // New DbSets for enhanced features
        public DbSet<Resource> Resources { get; set; }
        public DbSet<ResourceAllocation> ResourceAllocations { get; set; }
        public DbSet<ResourceSkill> ResourceSkills { get; set; }
        public DbSet<Skill> Skills { get; set; }
        public DbSet<TaskSkillRequirement> TaskSkillRequirements { get; set; }
        public DbSet<Risk> Risks { get; set; }
        public DbSet<RiskMitigationAction> RiskMitigationActions { get; set; }
        public DbSet<Workflow> Workflows { get; set; }
        public DbSet<WorkflowState> WorkflowStates { get; set; }
        public DbSet<WorkflowTransition> WorkflowTransitions { get; set; }
        public DbSet<WorkflowRule> WorkflowRules { get; set; }
        public DbSet<Dashboard> Dashboards { get; set; }
        public DbSet<DashboardWidget> DashboardWidgets { get; set; }
        public DbSet<Report> Reports { get; set; }
        public DbSet<ReportExecution> ReportExecutions { get; set; }
        public DbSet<ProjectMetrics> ProjectMetrics { get; set; }
        public DbSet<Integration> Integrations { get; set; }
        public DbSet<IntegrationLog> IntegrationLogs { get; set; }
        public DbSet<ExternalCommit> ExternalCommits { get; set; }
        public DbSet<CalendarSync> CalendarSyncs { get; set; }
        public DbSet<NotificationChannel> NotificationChannels { get; set; }
        public DbSet<NotificationRule> NotificationRules { get; set; }

        // Meeting Management
        public DbSet<Meeting> Meetings { get; set; }
        public DbSet<MeetingAttendee> MeetingAttendees { get; set; }
        public DbSet<MeetingActionItem> MeetingActionItems { get; set; }
        public DbSet<MeetingDocument> MeetingDocuments { get; set; }

        // Requirements Management
        public DbSet<Requirement> Requirements { get; set; }
        public DbSet<RequirementComment> RequirementComments { get; set; }
        public DbSet<RequirementAttachment> RequirementAttachments { get; set; }
        public DbSet<RequirementChange> RequirementChanges { get; set; }
        public DbSet<RequirementTask> RequirementTasks { get; set; }

        // Stakeholder Management
        public DbSet<Stakeholder> Stakeholders { get; set; }
        public DbSet<ProjectStakeholder> ProjectStakeholders { get; set; }
        public DbSet<StakeholderCommunication> StakeholderCommunications { get; set; }

        // Agile Management
        public DbSet<Epic> Epics { get; set; }
        public DbSet<Feature> Features { get; set; }
        public DbSet<UserStory> UserStories { get; set; }
        public DbSet<Sprint> Sprints { get; set; }
        public DbSet<UserStoryComment> UserStoryComments { get; set; }
        public DbSet<Bug> Bugs { get; set; }
        public DbSet<BugComment> BugComments { get; set; }
        public DbSet<BugAttachment> BugAttachments { get; set; }
        public DbSet<TestCase> TestCases { get; set; }
        public DbSet<TestExecution> TestExecutions { get; set; }

        // Unified People Management
        public DbSet<Person> People { get; set; }
        public DbSet<PersonRole> PersonRoles { get; set; }
        public DbSet<PersonSkill> PersonSkills { get; set; }
        public DbSet<PersonProject> PersonProjects { get; set; }
        public DbSet<PersonStakeholder> PersonStakeholders { get; set; }
        public DbSet<PersonResource> PersonResources { get; set; }
        public DbSet<PersonContact> PersonContacts { get; set; }
        public DbSet<PersonAvailability> PersonAvailabilities { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // Configure ApplicationUser
            builder.Entity<ApplicationUser>(entity =>
            {
                entity.Property(e => e.FirstName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.LastName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Bio).HasMaxLength(500);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
                entity.Property(e => e.IsActive).HasDefaultValue(true);
            });

            // Configure Project
            builder.Entity<Project>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Description).HasMaxLength(1000);
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.Budget).HasColumnType("decimal(18,2)");
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");                entity.HasOne(e => e.CreatedBy)
                    .WithMany(u => u.CreatedProjects)
                    .HasForeignKey(e => e.CreatedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Manager)
                    .WithMany()
                    .HasForeignKey("ManagerId")
                    .OnDelete(DeleteBehavior.Restrict)
                    .IsRequired();

                entity.HasIndex(e => e.Name);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.CreatedByUserId);
            });

            // Configure TaskEntity
            builder.Entity<TaskEntity>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Title).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Description).HasMaxLength(2000);
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.Priority).HasConversion<int>();
                entity.Property(e => e.WbsCode).HasMaxLength(50);
                entity.Property(e => e.WbsLevel).HasDefaultValue(1);
                entity.Property(e => e.SortOrder).HasDefaultValue(0);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.HasOne(e => e.Project)
                    .WithMany(p => p.Tasks)
                    .HasForeignKey(e => e.ProjectId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.AssignedTo)
                    .WithMany(u => u.AssignedTasks)
                    .HasForeignKey(e => e.AssignedToUserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.CreatedBy)
                    .WithMany()
                    .HasForeignKey(e => e.CreatedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.ParentTask)
                    .WithMany(t => t.SubTasks)
                    .HasForeignKey(e => e.ParentTaskId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => e.ProjectId);
                entity.HasIndex(e => e.AssignedToUserId);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.Priority);
                entity.HasIndex(e => e.DueDate);
            });

            // Configure ProjectMember
            builder.Entity<ProjectMember>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Role).HasConversion<int>();
                entity.Property(e => e.JoinedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
                entity.Property(e => e.IsActive).HasDefaultValue(true);

                entity.HasOne(e => e.Project)
                    .WithMany(p => p.Members)
                    .HasForeignKey(e => e.ProjectId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.User)
                    .WithMany(u => u.ProjectMemberships)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => new { e.ProjectId, e.UserId }).IsUnique();
            });

            // Configure Milestone
            builder.Entity<Milestone>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Description).HasMaxLength(1000);
                entity.Property(e => e.IsCompleted).HasDefaultValue(false);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.HasOne(e => e.Project)
                    .WithMany(p => p.Milestones)
                    .HasForeignKey(e => e.ProjectId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => e.ProjectId);
                entity.HasIndex(e => e.DueDate);
            });

            // Configure TaskComment
            builder.Entity<TaskComment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Content).IsRequired().HasMaxLength(2000);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.HasOne(e => e.Task)
                    .WithMany(t => t.Comments)
                    .HasForeignKey(e => e.TaskId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.User)
                    .WithMany(u => u.TaskComments)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => e.TaskId);
                entity.HasIndex(e => e.UserId);
            });

            // Configure TaskAttachment
            builder.Entity<TaskAttachment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FileName).IsRequired().HasMaxLength(255);
                entity.Property(e => e.FilePath).IsRequired().HasMaxLength(500);
                entity.Property(e => e.ContentType).HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.Tags).HasMaxLength(100);
                entity.Property(e => e.UploadedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.HasOne(e => e.Task)
                    .WithMany(t => t.Attachments)
                    .HasForeignKey(e => e.TaskId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.UploadedBy)
                    .WithMany()
                    .HasForeignKey(e => e.UploadedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                // New category relationship
                entity.HasOne(e => e.Category)
                    .WithMany(c => c.TaskAttachments)
                    .HasForeignKey(e => e.CategoryId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => e.TaskId);
                entity.HasIndex(e => e.CategoryId);
            });

            // Configure ProjectAttachment
            builder.Entity<ProjectAttachment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FileName).IsRequired().HasMaxLength(255);
                entity.Property(e => e.FilePath).IsRequired().HasMaxLength(500);
                entity.Property(e => e.ContentType).HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.Tags).HasMaxLength(100);
                entity.Property(e => e.UploadedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.HasOne(e => e.Project)
                    .WithMany(p => p.Attachments)
                    .HasForeignKey(e => e.ProjectId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.UploadedBy)
                    .WithMany()
                    .HasForeignKey(e => e.UploadedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                // New category relationship
                entity.HasOne(e => e.Category)
                    .WithMany(c => c.ProjectAttachments)
                    .HasForeignKey(e => e.CategoryId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => e.ProjectId);
                entity.HasIndex(e => e.CategoryId);
            });

            // Configure Notification
            builder.Entity<Notification>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Title).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Message).IsRequired().HasMaxLength(1000);
                entity.Property(e => e.Type).HasConversion<int>();
                entity.Property(e => e.IsRead).HasDefaultValue(false);
                entity.Property(e => e.IsEmailSent).HasDefaultValue(false);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.HasOne(e => e.User)
                    .WithMany(u => u.Notifications)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.RelatedProject)
                    .WithMany()
                    .HasForeignKey(e => e.RelatedProjectId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.RelatedTask)
                    .WithMany()
                    .HasForeignKey(e => e.RelatedTaskId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => e.UserId);
                entity.HasIndex(e => e.IsRead);
                entity.HasIndex(e => e.CreatedAt);
            });

            // Configure AuditLog
            builder.Entity<AuditLog>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Action).HasConversion<int>();
                entity.Property(e => e.EntityName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.OldValues).HasMaxLength(2000);
                entity.Property(e => e.NewValues).HasMaxLength(2000);
                entity.Property(e => e.IpAddress).HasMaxLength(45);
                entity.Property(e => e.UserAgent).HasMaxLength(500);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.HasOne(e => e.User)
                    .WithMany(u => u.AuditLogs)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(e => e.UserId);
                entity.HasIndex(e => e.EntityName);
                entity.HasIndex(e => e.CreatedAt);
            });

            // Configure TaskDependency relationships
            builder.Entity<TaskDependency>()
                .HasOne(td => td.DependentTask)
                .WithMany()
                .HasForeignKey(td => td.DependentTaskId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<TaskDependency>()
                .HasOne(td => td.DependencyTask)
                .WithMany()
                .HasForeignKey(td => td.DependencyTaskId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure TimeEntry relationships
            builder.Entity<TimeEntry>()
                .HasOne(te => te.Task)
                .WithMany(t => t.TimeEntries)
                .HasForeignKey(te => te.TaskId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<TimeEntry>()
                .HasOne(te => te.User)
                .WithMany()
                .HasForeignKey(te => te.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure DocumentCategory
            builder.Entity<DocumentCategory>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.HasOne(e => e.CreatedBy)
                    .WithMany()
                    .HasForeignKey(e => e.CreatedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(e => e.Name);
            });

            // Configure DocumentVersion
            builder.Entity<DocumentVersion>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FileName).IsRequired().HasMaxLength(255);
                entity.Property(e => e.FilePath).IsRequired().HasMaxLength(500);
                entity.Property(e => e.ContentType).HasMaxLength(100);
                entity.Property(e => e.UploadedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.HasOne(e => e.UploadedBy)
                    .WithMany()
                    .HasForeignKey(e => e.UploadedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                // Conditional relationships based on AttachmentType
                entity.HasOne(e => e.ProjectAttachment)
                    .WithMany(pa => pa.Versions)
                    .HasForeignKey(e => e.AttachmentId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .IsRequired(false);

                entity.HasOne(e => e.TaskAttachment)
                    .WithMany(ta => ta.Versions)
                    .HasForeignKey(e => e.AttachmentId)
                    .OnDelete(DeleteBehavior.Cascade)
                    .IsRequired(false);

                // Filtering conditions for Project vs Task attachments
                entity.HasQueryFilter(e =>
                    (e.AttachmentType == "Project" && e.ProjectAttachment != null) ||
                    (e.AttachmentType == "Task" && e.TaskAttachment != null));

                entity.HasIndex(e => new { e.AttachmentType, e.AttachmentId });
            });

            // Configure new entities
            ConfigureResourceEntities(builder);
            ConfigureRiskEntities(builder);
            ConfigureWorkflowEntities(builder);
            ConfigureAnalyticsEntities(builder);
            ConfigureIntegrationEntities(builder);
            ConfigureMeetingEntities(builder);
            ConfigureRequirementEntities(builder);
            ConfigureStakeholderEntities(builder);
            ConfigureAgileEntities(builder);
            ConfigurePersonEntities(builder);
        }

        private void ConfigureResourceEntities(ModelBuilder builder)
        {
            // Configure Resource
            builder.Entity<Resource>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Description).HasMaxLength(1000);
                entity.Property(e => e.Type).HasConversion<int>();
                entity.Property(e => e.Department).HasMaxLength(100);
                entity.Property(e => e.Location).HasMaxLength(100);
                entity.Property(e => e.HourlyRate).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Capacity).HasColumnType("decimal(18,2)").HasDefaultValue(8);
                entity.Property(e => e.IsActive).HasDefaultValue(true);
                entity.Property(e => e.Skills).HasMaxLength(500);
                entity.Property(e => e.Email).HasMaxLength(100);
                entity.Property(e => e.Phone).HasMaxLength(20);

                entity.HasOne(e => e.User)
                    .WithMany(u => u.Resources)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => e.Type);
                entity.HasIndex(e => e.IsActive);
            });

            // Configure ResourceAllocation
            builder.Entity<ResourceAllocation>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.AllocatedHours).HasColumnType("decimal(18,2)");
                entity.Property(e => e.AllocationPercentage).HasColumnType("decimal(5,2)").HasDefaultValue(100);
                entity.Property(e => e.Notes).HasMaxLength(500);
                entity.Property(e => e.Status).HasConversion<int>();

                entity.HasOne(e => e.Resource)
                    .WithMany(r => r.Allocations)
                    .HasForeignKey(e => e.ResourceId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Project)
                    .WithMany()
                    .HasForeignKey(e => e.ProjectId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Task)
                    .WithMany()
                    .HasForeignKey(e => e.TaskId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.CreatedBy)
                    .WithMany(u => u.ResourceAllocations)
                    .HasForeignKey(e => e.CreatedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(e => e.ResourceId);
                entity.HasIndex(e => e.ProjectId);
                entity.HasIndex(e => e.TaskId);
                entity.HasIndex(e => new { e.StartDate, e.EndDate });
            });

            // Configure Skill
            builder.Entity<Skill>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.Category).HasMaxLength(100);
                entity.Property(e => e.IsActive).HasDefaultValue(true);

                entity.HasIndex(e => e.Name).IsUnique();
                entity.HasIndex(e => e.Category);
            });

            // Configure ResourceSkill
            builder.Entity<ResourceSkill>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Level).HasConversion<int>();
                entity.Property(e => e.Notes).HasMaxLength(500);

                entity.HasOne(e => e.Resource)
                    .WithMany(r => r.ResourceSkills)
                    .HasForeignKey(e => e.ResourceId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Skill)
                    .WithMany(s => s.ResourceSkills)
                    .HasForeignKey(e => e.SkillId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => new { e.ResourceId, e.SkillId }).IsUnique();
            });

            // Configure TaskSkillRequirement
            builder.Entity<TaskSkillRequirement>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.RequiredLevel).HasConversion<int>();
                entity.Property(e => e.IsRequired).HasDefaultValue(true);
                entity.Property(e => e.Notes).HasMaxLength(500);

                entity.HasOne(e => e.Task)
                    .WithMany()
                    .HasForeignKey(e => e.TaskId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Skill)
                    .WithMany(s => s.TaskRequirements)
                    .HasForeignKey(e => e.SkillId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => new { e.TaskId, e.SkillId }).IsUnique();
            });
        }

        private void ConfigureRiskEntities(ModelBuilder builder)
        {
            // Configure Risk
            builder.Entity<Risk>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Title).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Description).IsRequired().HasMaxLength(2000);
                entity.Property(e => e.Category).HasConversion<int>();
                entity.Property(e => e.Probability).HasConversion<int>();
                entity.Property(e => e.Impact).HasConversion<int>();
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.MitigationPlan).HasMaxLength(2000);
                entity.Property(e => e.ContingencyPlan).HasMaxLength(2000);
                entity.Property(e => e.EstimatedCost).HasColumnType("decimal(18,2)");

                entity.HasOne(e => e.Project)
                    .WithMany()
                    .HasForeignKey(e => e.ProjectId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Task)
                    .WithMany()
                    .HasForeignKey(e => e.TaskId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Owner)
                    .WithMany(u => u.OwnedRisks)
                    .HasForeignKey(e => e.OwnerId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.CreatedBy)
                    .WithMany(u => u.CreatedRisks)
                    .HasForeignKey(e => e.CreatedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(e => e.ProjectId);
                entity.HasIndex(e => e.TaskId);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.Category);
            });

            // Configure RiskMitigationAction
            builder.Entity<RiskMitigationAction>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Action).IsRequired().HasMaxLength(500);
                entity.Property(e => e.Description).HasMaxLength(1000);
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.Notes).HasMaxLength(1000);

                entity.HasOne(e => e.Risk)
                    .WithMany(r => r.MitigationActions)
                    .HasForeignKey(e => e.RiskId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.AssignedTo)
                    .WithMany()
                    .HasForeignKey(e => e.AssignedToUserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => e.RiskId);
                entity.HasIndex(e => e.Status);
            });
        }

        private void ConfigureWorkflowEntities(ModelBuilder builder)
        {
            // Configure Workflow
            builder.Entity<Workflow>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Description).HasMaxLength(1000);
                entity.Property(e => e.Type).HasConversion<int>();
                entity.Property(e => e.IsActive).HasDefaultValue(true);
                entity.Property(e => e.IsDefault).HasDefaultValue(false);

                entity.HasOne(e => e.Project)
                    .WithMany()
                    .HasForeignKey(e => e.ProjectId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.CreatedBy)
                    .WithMany()
                    .HasForeignKey(e => e.CreatedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(e => e.ProjectId);
                entity.HasIndex(e => e.Type);
            });

            // Configure WorkflowState
            builder.Entity<WorkflowState>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.Color).HasMaxLength(20).HasDefaultValue("#6c757d");
                entity.Property(e => e.IsInitial).HasDefaultValue(false);
                entity.Property(e => e.IsFinal).HasDefaultValue(false);
                entity.Property(e => e.IsActive).HasDefaultValue(true);

                entity.HasOne(e => e.Workflow)
                    .WithMany(w => w.States)
                    .HasForeignKey(e => e.WorkflowId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => e.WorkflowId);
            });

            // Configure WorkflowTransition
            builder.Entity<WorkflowTransition>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.RequiresApproval).HasDefaultValue(false);
                entity.Property(e => e.IsActive).HasDefaultValue(true);

                entity.HasOne(e => e.Workflow)
                    .WithMany(w => w.Transitions)
                    .HasForeignKey(e => e.WorkflowId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.FromState)
                    .WithMany(s => s.FromTransitions)
                    .HasForeignKey(e => e.FromStateId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.ToState)
                    .WithMany(s => s.ToTransitions)
                    .HasForeignKey(e => e.ToStateId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(e => e.WorkflowId);
                entity.HasIndex(e => new { e.FromStateId, e.ToStateId });
            });

            // Configure WorkflowRule
            builder.Entity<WorkflowRule>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Description).HasMaxLength(1000);
                entity.Property(e => e.Type).HasConversion<int>();
                entity.Property(e => e.Condition).IsRequired();
                entity.Property(e => e.Action).IsRequired();
                entity.Property(e => e.IsActive).HasDefaultValue(true);
                entity.Property(e => e.Priority).HasDefaultValue(0);

                entity.HasOne(e => e.Workflow)
                    .WithMany(w => w.Rules)
                    .HasForeignKey(e => e.WorkflowId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Transition)
                    .WithMany(t => t.Rules)
                    .HasForeignKey(e => e.TransitionId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => e.WorkflowId);
                entity.HasIndex(e => e.TransitionId);
            });
        }

        private void ConfigureAnalyticsEntities(ModelBuilder builder)
        {
            // Configure Dashboard
            builder.Entity<Dashboard>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Description).HasMaxLength(1000);
                entity.Property(e => e.IsDefault).HasDefaultValue(false);
                entity.Property(e => e.IsPublic).HasDefaultValue(false);
                entity.Property(e => e.Layout).IsRequired();

                entity.HasOne(e => e.User)
                    .WithMany(u => u.Dashboards)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => e.UserId);
            });

            // Configure DashboardWidget
            builder.Entity<DashboardWidget>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Title).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Type).HasConversion<int>();
                entity.Property(e => e.Configuration).IsRequired();
                entity.Property(e => e.Width).HasDefaultValue(4);
                entity.Property(e => e.Height).HasDefaultValue(3);
                entity.Property(e => e.IsVisible).HasDefaultValue(true);
                entity.Property(e => e.RefreshInterval).HasDefaultValue(300);

                entity.HasOne(e => e.Dashboard)
                    .WithMany(d => d.Widgets)
                    .HasForeignKey(e => e.DashboardId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => e.DashboardId);
            });

            // Configure Report
            builder.Entity<Report>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Description).HasMaxLength(1000);
                entity.Property(e => e.Type).HasConversion<int>();
                entity.Property(e => e.Parameters).IsRequired();
                entity.Property(e => e.IsScheduled).HasDefaultValue(false);

                entity.HasOne(e => e.CreatedBy)
                    .WithMany(u => u.Reports)
                    .HasForeignKey(e => e.CreatedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(e => e.Type);
                entity.HasIndex(e => e.CreatedByUserId);
            });

            // Configure ReportExecution
            builder.Entity<ReportExecution>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Status).HasConversion<int>();

                entity.HasOne(e => e.Report)
                    .WithMany(r => r.Executions)
                    .HasForeignKey(e => e.ReportId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.ExecutedBy)
                    .WithMany()
                    .HasForeignKey(e => e.ExecutedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(e => e.ReportId);
                entity.HasIndex(e => e.ExecutedAt);
            });

            // Configure ProjectMetrics
            builder.Entity<ProjectMetrics>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.BudgetSpent).HasColumnType("decimal(18,2)");
                entity.Property(e => e.BudgetRemaining).HasColumnType("decimal(18,2)");

                entity.HasOne(e => e.Project)
                    .WithMany()
                    .HasForeignKey(e => e.ProjectId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => e.ProjectId);
                entity.HasIndex(e => e.Date);
                entity.HasIndex(e => new { e.ProjectId, e.Date }).IsUnique();
            });
        }

        private void ConfigureIntegrationEntities(ModelBuilder builder)
        {
            // Configure Integration
            builder.Entity<Integration>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Type).HasConversion<int>();
                entity.Property(e => e.Configuration).IsRequired();
                entity.Property(e => e.IsActive).HasDefaultValue(true);

                entity.HasOne(e => e.Project)
                    .WithMany()
                    .HasForeignKey(e => e.ProjectId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.CreatedBy)
                    .WithMany(u => u.Integrations)
                    .HasForeignKey(e => e.CreatedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(e => e.ProjectId);
                entity.HasIndex(e => e.Type);
            });

            // Configure IntegrationLog
            builder.Entity<IntegrationLog>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Level).HasConversion<int>();
                entity.Property(e => e.Message).IsRequired().HasMaxLength(500);

                entity.HasOne(e => e.Integration)
                    .WithMany(i => i.Logs)
                    .HasForeignKey(e => e.IntegrationId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => e.IntegrationId);
                entity.HasIndex(e => e.Timestamp);
            });

            // Configure ExternalCommit
            builder.Entity<ExternalCommit>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.CommitHash).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Message).IsRequired().HasMaxLength(500);
                entity.Property(e => e.Author).HasMaxLength(100);
                entity.Property(e => e.AuthorEmail).HasMaxLength(100);
                entity.Property(e => e.Branch).HasMaxLength(200);
                entity.Property(e => e.Repository).HasMaxLength(500);

                entity.HasOne(e => e.Integration)
                    .WithMany()
                    .HasForeignKey(e => e.IntegrationId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Task)
                    .WithMany()
                    .HasForeignKey(e => e.TaskId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => e.IntegrationId);
                entity.HasIndex(e => e.TaskId);
                entity.HasIndex(e => e.CommitHash).IsUnique();
            });

            // Configure CalendarSync
            builder.Entity<CalendarSync>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Provider).HasConversion<int>();
                entity.Property(e => e.Direction).HasConversion<int>();
                entity.Property(e => e.IsActive).HasDefaultValue(true);

                entity.HasOne(e => e.Project)
                    .WithMany()
                    .HasForeignKey(e => e.ProjectId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.User)
                    .WithMany(u => u.CalendarSyncs)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => e.ProjectId);
                entity.HasIndex(e => e.UserId);
            });

            // Configure NotificationChannel
            builder.Entity<NotificationChannel>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Type).HasConversion<int>();
                entity.Property(e => e.Configuration).IsRequired();
                entity.Property(e => e.IsActive).HasDefaultValue(true);

                entity.HasOne(e => e.Project)
                    .WithMany()
                    .HasForeignKey(e => e.ProjectId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.CreatedBy)
                    .WithMany()
                    .HasForeignKey(e => e.CreatedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(e => e.ProjectId);
                entity.HasIndex(e => e.Type);
            });

            // Configure NotificationRule
            builder.Entity<NotificationRule>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.EventType).IsRequired();
                entity.Property(e => e.Conditions).IsRequired();
                entity.Property(e => e.Template).IsRequired();
                entity.Property(e => e.IsActive).HasDefaultValue(true);

                entity.HasOne(e => e.Channel)
                    .WithMany(c => c.Rules)
                    .HasForeignKey(e => e.ChannelId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => e.ChannelId);
                entity.HasIndex(e => e.EventType);
            });
        }

        private void ConfigureMeetingEntities(ModelBuilder builder)
        {
            // Configure Meeting
            builder.Entity<Meeting>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Title).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Description).HasMaxLength(2000);
                entity.Property(e => e.Type).HasConversion<int>();
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.Location).HasMaxLength(500);
                entity.Property(e => e.MeetingLink).HasMaxLength(500);
                entity.Property(e => e.Agenda).HasMaxLength(2000);
                entity.Property(e => e.Minutes).HasMaxLength(5000);
                entity.Property(e => e.ActionItems).HasMaxLength(2000);
                entity.Property(e => e.RecurrencePattern).HasMaxLength(100);

                entity.HasOne(e => e.Project)
                    .WithMany()
                    .HasForeignKey(e => e.ProjectId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Organizer)
                    .WithMany()
                    .HasForeignKey(e => e.OrganizerUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(e => e.ProjectId);
                entity.HasIndex(e => e.ScheduledDate);
                entity.HasIndex(e => e.Status);
            });

            // Configure MeetingAttendee
            builder.Entity<MeetingAttendee>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Role).HasConversion<int>();
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.Notes).HasMaxLength(500);
                entity.Property(e => e.IsRequired).HasDefaultValue(true);

                entity.HasOne(e => e.Meeting)
                    .WithMany(m => m.Attendees)
                    .HasForeignKey(e => e.MeetingId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.User)
                    .WithMany()
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => new { e.MeetingId, e.UserId }).IsUnique();
            });

            // Configure MeetingActionItem
            builder.Entity<MeetingActionItem>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Description).IsRequired().HasMaxLength(500);
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.Notes).HasMaxLength(1000);
                entity.Property(e => e.Priority).HasDefaultValue(3);

                entity.HasOne(e => e.Meeting)
                    .WithMany(m => m.MeetingActionItems)
                    .HasForeignKey(e => e.MeetingId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.AssignedTo)
                    .WithMany()
                    .HasForeignKey(e => e.AssignedToUserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => e.MeetingId);
                entity.HasIndex(e => e.Status);
            });

            // Configure MeetingDocument
            builder.Entity<MeetingDocument>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FileName).IsRequired().HasMaxLength(255);
                entity.Property(e => e.FilePath).IsRequired().HasMaxLength(500);
                entity.Property(e => e.ContentType).HasMaxLength(100);
                entity.Property(e => e.Type).HasConversion<int>();
                entity.Property(e => e.Notes).HasMaxLength(500);

                entity.HasOne(e => e.Meeting)
                    .WithMany(m => m.Documents)
                    .HasForeignKey(e => e.MeetingId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.UploadedBy)
                    .WithMany()
                    .HasForeignKey(e => e.UploadedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(e => e.MeetingId);
            });
        }

        private void ConfigureRequirementEntities(ModelBuilder builder)
        {
            // Configure Requirement
            builder.Entity<Requirement>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Title).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Description).IsRequired().HasMaxLength(5000);
                entity.Property(e => e.RequirementId).HasMaxLength(50);
                entity.Property(e => e.Type).HasConversion<int>();
                entity.Property(e => e.Priority).HasConversion<int>();
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.Source).HasConversion<int>();
                entity.Property(e => e.AcceptanceCriteria).HasMaxLength(2000);
                entity.Property(e => e.BusinessJustification).HasMaxLength(1000);
                entity.Property(e => e.EstimatedEffort).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Notes).HasMaxLength(2000);
                entity.Property(e => e.TestCases).HasMaxLength(1000);
                entity.Property(e => e.Tags).HasMaxLength(500);
                entity.Property(e => e.IsBlocked).HasDefaultValue(false);
                entity.Property(e => e.BlockedReason).HasMaxLength(1000);

                entity.HasOne(e => e.Project)
                    .WithMany()
                    .HasForeignKey(e => e.ProjectId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.ParentRequirement)
                    .WithMany(r => r.ChildRequirements)
                    .HasForeignKey(e => e.ParentRequirementId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Stakeholder)
                    .WithMany()
                    .HasForeignKey(e => e.StakeholderUserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.Analyst)
                    .WithMany()
                    .HasForeignKey(e => e.AnalystUserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.Developer)
                    .WithMany()
                    .HasForeignKey(e => e.DeveloperUserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.Tester)
                    .WithMany()
                    .HasForeignKey(e => e.TesterUserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.ApprovedBy)
                    .WithMany()
                    .HasForeignKey(e => e.ApprovedByUserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => e.ProjectId);
                entity.HasIndex(e => e.RequirementId).IsUnique();
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.Priority);
            });

            // Configure RequirementComment
            builder.Entity<RequirementComment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Content).IsRequired().HasMaxLength(2000);
                entity.Property(e => e.Type).HasConversion<int>();

                entity.HasOne(e => e.Requirement)
                    .WithMany(r => r.Comments)
                    .HasForeignKey(e => e.RequirementId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.User)
                    .WithMany()
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(e => e.RequirementId);
            });

            // Configure RequirementAttachment
            builder.Entity<RequirementAttachment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FileName).IsRequired().HasMaxLength(255);
                entity.Property(e => e.FilePath).IsRequired().HasMaxLength(500);
                entity.Property(e => e.ContentType).HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);

                entity.HasOne(e => e.Requirement)
                    .WithMany(r => r.Attachments)
                    .HasForeignKey(e => e.RequirementId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.UploadedBy)
                    .WithMany()
                    .HasForeignKey(e => e.UploadedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(e => e.RequirementId);
            });

            // Configure RequirementChange
            builder.Entity<RequirementChange>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ChangeDescription).IsRequired().HasMaxLength(500);
                entity.Property(e => e.Type).HasConversion<int>();
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.Justification).HasMaxLength(2000);
                entity.Property(e => e.ImpactEffort).HasColumnType("decimal(18,2)");
                entity.Property(e => e.ImpactAnalysis).HasMaxLength(1000);

                entity.HasOne(e => e.Requirement)
                    .WithMany(r => r.Changes)
                    .HasForeignKey(e => e.RequirementId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.RequestedBy)
                    .WithMany()
                    .HasForeignKey(e => e.RequestedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.ApprovedBy)
                    .WithMany()
                    .HasForeignKey(e => e.ApprovedByUserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => e.RequirementId);
                entity.HasIndex(e => e.Status);
            });

            // Configure RequirementTask
            builder.Entity<RequirementTask>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Notes).HasMaxLength(500);

                entity.HasOne(e => e.Requirement)
                    .WithMany(r => r.Tasks)
                    .HasForeignKey(e => e.RequirementId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Task)
                    .WithMany()
                    .HasForeignKey(e => e.TaskId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => new { e.RequirementId, e.TaskId }).IsUnique();
            });
        }

        private void ConfigureStakeholderEntities(ModelBuilder builder)
        {
            // Configure Stakeholder
            builder.Entity<Stakeholder>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Title).HasMaxLength(100);
                entity.Property(e => e.Organization).HasMaxLength(200);
                entity.Property(e => e.Department).HasMaxLength(100);
                entity.Property(e => e.Email).IsRequired().HasMaxLength(255);
                entity.Property(e => e.Phone).HasMaxLength(20);
                entity.Property(e => e.Type).HasConversion<int>();
                entity.Property(e => e.Role).HasConversion<int>();
                entity.Property(e => e.Influence).HasConversion<int>();
                entity.Property(e => e.Interest).HasConversion<int>();
                entity.Property(e => e.IsActive).HasDefaultValue(true);
                entity.Property(e => e.Notes).HasMaxLength(1000);
                entity.Property(e => e.CommunicationPreferences).HasMaxLength(500);

                entity.HasOne(e => e.User)
                    .WithMany()
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => e.Email).IsUnique();
                entity.HasIndex(e => e.Type);
                entity.HasIndex(e => e.IsActive);
            });

            // Configure ProjectStakeholder
            builder.Entity<ProjectStakeholder>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ProjectRole).HasConversion<int>();
                entity.Property(e => e.IsKeyStakeholder).HasDefaultValue(false);
                entity.Property(e => e.ReceiveUpdates).HasDefaultValue(true);
                entity.Property(e => e.ProjectSpecificNotes).HasMaxLength(1000);

                entity.HasOne(e => e.Project)
                    .WithMany()
                    .HasForeignKey(e => e.ProjectId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Stakeholder)
                    .WithMany(s => s.ProjectStakeholders)
                    .HasForeignKey(e => e.StakeholderId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => new { e.ProjectId, e.StakeholderId }).IsUnique();
            });

            // Configure StakeholderCommunication
            builder.Entity<StakeholderCommunication>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Subject).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Content).IsRequired().HasMaxLength(5000);
                entity.Property(e => e.Type).HasConversion<int>();
                entity.Property(e => e.Direction).HasConversion<int>();
                entity.Property(e => e.Notes).HasMaxLength(1000);
                entity.Property(e => e.RequiresFollowUp).HasDefaultValue(false);

                entity.HasOne(e => e.Stakeholder)
                    .WithMany(s => s.Communications)
                    .HasForeignKey(e => e.StakeholderId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Project)
                    .WithMany()
                    .HasForeignKey(e => e.ProjectId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.CommunicatedBy)
                    .WithMany()
                    .HasForeignKey(e => e.CommunicatedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(e => e.StakeholderId);
                entity.HasIndex(e => e.ProjectId);
                entity.HasIndex(e => e.CommunicationDate);
            });
        }

        private void ConfigureAgileEntities(ModelBuilder builder)
        {
            // Configure Epic
            builder.Entity<Epic>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Title).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Description).IsRequired().HasMaxLength(5000);
                entity.Property(e => e.EpicKey).HasMaxLength(50);
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.Priority).HasConversion<int>();
                entity.Property(e => e.AcceptanceCriteria).HasMaxLength(2000);
                entity.Property(e => e.BusinessValue).HasMaxLength(1000);
                entity.Property(e => e.EstimatedStoryPoints).HasColumnType("decimal(18,2)");
                entity.Property(e => e.ActualStoryPoints).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Tags).HasMaxLength(500);

                entity.HasOne(e => e.Project)
                    .WithMany()
                    .HasForeignKey(e => e.ProjectId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Owner)
                    .WithMany()
                    .HasForeignKey(e => e.OwnerId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => e.ProjectId);
                entity.HasIndex(e => e.EpicKey).IsUnique();
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.Priority);
            });

            // Configure Feature
            builder.Entity<Feature>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Title).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Description).IsRequired().HasMaxLength(5000);
                entity.Property(e => e.FeatureKey).HasMaxLength(50);
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.Priority).HasConversion<int>();
                entity.Property(e => e.BusinessValue).HasMaxLength(2000);
                entity.Property(e => e.AcceptanceCriteria).HasMaxLength(2000);
                entity.Property(e => e.EstimatedStoryPoints).HasColumnType("decimal(18,2)");
                entity.Property(e => e.ActualStoryPoints).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Tags).HasMaxLength(500);

                entity.HasOne(e => e.Epic)
                    .WithMany(ep => ep.Features)
                    .HasForeignKey(e => e.EpicId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Project)
                    .WithMany()
                    .HasForeignKey(e => e.ProjectId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Owner)
                    .WithMany()
                    .HasForeignKey(e => e.OwnerId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => e.FeatureKey).IsUnique();
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.Priority);
                entity.HasIndex(e => e.ProjectId);
                entity.HasIndex(e => e.EpicId);
            });

            // Configure UserStory
            builder.Entity<UserStory>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Title).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Description).IsRequired().HasMaxLength(5000);
                entity.Property(e => e.StoryKey).HasMaxLength(50);
                entity.Property(e => e.AsA).IsRequired().HasMaxLength(1000);
                entity.Property(e => e.IWant).IsRequired().HasMaxLength(1000);
                entity.Property(e => e.SoThat).IsRequired().HasMaxLength(1000);
                entity.Property(e => e.AcceptanceCriteria).HasMaxLength(2000);
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.Priority).HasConversion<int>();
                entity.Property(e => e.StoryPoints).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Tags).HasMaxLength(500);

                entity.HasOne(e => e.Project)
                    .WithMany()
                    .HasForeignKey(e => e.ProjectId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Epic)
                    .WithMany(ep => ep.UserStories)
                    .HasForeignKey(e => e.EpicId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.Feature)
                    .WithMany(f => f.UserStories)
                    .HasForeignKey(e => e.FeatureId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.Sprint)
                    .WithMany(s => s.UserStories)
                    .HasForeignKey(e => e.SprintId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.AssignedTo)
                    .WithMany()
                    .HasForeignKey(e => e.AssignedToUserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => e.ProjectId);
                entity.HasIndex(e => e.StoryKey).IsUnique();
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.Priority);
                entity.HasIndex(e => e.BacklogOrder);
                entity.HasIndex(e => e.KanbanColumn);
            });

            // Configure Sprint
            builder.Entity<Sprint>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Description).HasMaxLength(1000);
                entity.Property(e => e.SprintKey).HasMaxLength(50);
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.Goal).HasMaxLength(2000);
                entity.Property(e => e.PlannedStoryPoints).HasColumnType("decimal(18,2)");
                entity.Property(e => e.CompletedStoryPoints).HasColumnType("decimal(18,2)");

                entity.HasOne(e => e.Project)
                    .WithMany()
                    .HasForeignKey(e => e.ProjectId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.ScrumMaster)
                    .WithMany()
                    .HasForeignKey(e => e.ScrumMasterId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => e.ProjectId);
                entity.HasIndex(e => e.SprintKey).IsUnique();
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.StartDate);
                entity.HasIndex(e => e.EndDate);
            });

            // Configure UserStoryComment
            builder.Entity<UserStoryComment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Content).IsRequired().HasMaxLength(2000);
                entity.Property(e => e.Type).HasConversion<int>();

                entity.HasOne(e => e.UserStory)
                    .WithMany(us => us.Comments)
                    .HasForeignKey(e => e.UserStoryId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.User)
                    .WithMany()
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(e => e.UserStoryId);
                entity.HasIndex(e => e.CreatedAt);
            });

            // Configure Bug
            builder.Entity<Bug>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Title).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Description).IsRequired().HasMaxLength(5000);
                entity.Property(e => e.BugKey).HasMaxLength(50);
                entity.Property(e => e.Severity).HasConversion<int>();
                entity.Property(e => e.Priority).HasConversion<int>();
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.StepsToReproduce).IsRequired().HasMaxLength(2000);
                entity.Property(e => e.ExpectedResult).IsRequired().HasMaxLength(1000);
                entity.Property(e => e.ActualResult).IsRequired().HasMaxLength(1000);
                entity.Property(e => e.FoundInVersion).HasMaxLength(100);
                entity.Property(e => e.FixedInVersion).HasMaxLength(100);
                entity.Property(e => e.Tags).HasMaxLength(500);
                entity.Property(e => e.EstimatedHours).HasColumnType("decimal(18,2)");
                entity.Property(e => e.ActualHours).HasColumnType("decimal(18,2)");

                entity.HasOne(e => e.Project)
                    .WithMany()
                    .HasForeignKey(e => e.ProjectId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.UserStory)
                    .WithMany()
                    .HasForeignKey(e => e.UserStoryId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.Feature)
                    .WithMany()
                    .HasForeignKey(e => e.FeatureId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.AssignedTo)
                    .WithMany()
                    .HasForeignKey(e => e.AssignedToUserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.ReportedBy)
                    .WithMany()
                    .HasForeignKey(e => e.ReportedByUserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => e.BugKey).IsUnique();
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.Severity);
                entity.HasIndex(e => e.Priority);
                entity.HasIndex(e => e.ProjectId);
                entity.HasIndex(e => e.UserStoryId);
                entity.HasIndex(e => e.FeatureId);
            });

            // Configure BugComment
            builder.Entity<BugComment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Content).IsRequired().HasMaxLength(2000);
                entity.Property(e => e.Type).HasConversion<int>();

                entity.HasOne(e => e.Bug)
                    .WithMany(b => b.Comments)
                    .HasForeignKey(e => e.BugId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.User)
                    .WithMany()
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(e => e.BugId);
                entity.HasIndex(e => e.CreatedAt);
            });

            // Configure BugAttachment
            builder.Entity<BugAttachment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FileName).IsRequired().HasMaxLength(255);
                entity.Property(e => e.FilePath).IsRequired().HasMaxLength(500);
                entity.Property(e => e.ContentType).IsRequired().HasMaxLength(100);

                entity.HasOne(e => e.Bug)
                    .WithMany(b => b.Attachments)
                    .HasForeignKey(e => e.BugId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.UploadedBy)
                    .WithMany()
                    .HasForeignKey(e => e.UploadedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(e => e.BugId);
            });

            // Configure TestCase
            builder.Entity<TestCase>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Title).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Description).HasMaxLength(2000);
                entity.Property(e => e.TestCaseKey).HasMaxLength(50);
                entity.Property(e => e.Type).HasConversion<int>();
                entity.Property(e => e.Priority).HasConversion<int>();
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.PreConditions).HasMaxLength(1000);
                entity.Property(e => e.TestSteps).IsRequired().HasMaxLength(5000);
                entity.Property(e => e.ExpectedResult).IsRequired().HasMaxLength(2000);
                entity.Property(e => e.Tags).HasMaxLength(500);
                entity.Property(e => e.EstimatedExecutionTime).HasColumnType("decimal(18,2)");

                entity.HasOne(e => e.Project)
                    .WithMany()
                    .HasForeignKey(e => e.ProjectId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.UserStory)
                    .WithMany()
                    .HasForeignKey(e => e.UserStoryId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.Feature)
                    .WithMany()
                    .HasForeignKey(e => e.FeatureId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.CreatedBy)
                    .WithMany()
                    .HasForeignKey(e => e.CreatedByUserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.AssignedTo)
                    .WithMany()
                    .HasForeignKey(e => e.AssignedToUserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => e.TestCaseKey).IsUnique();
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.Type);
                entity.HasIndex(e => e.Priority);
                entity.HasIndex(e => e.ProjectId);
                entity.HasIndex(e => e.UserStoryId);
                entity.HasIndex(e => e.FeatureId);
            });

            // Configure TestExecution
            builder.Entity<TestExecution>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Result).HasConversion<int>();
                entity.Property(e => e.ActualResult).HasMaxLength(2000);
                entity.Property(e => e.Notes).HasMaxLength(2000);
                entity.Property(e => e.ExecutionTime).HasColumnType("decimal(18,2)");

                entity.HasOne(e => e.TestCase)
                    .WithMany(tc => tc.TestExecutions)
                    .HasForeignKey(e => e.TestCaseId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.ExecutedBy)
                    .WithMany()
                    .HasForeignKey(e => e.ExecutedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Bug)
                    .WithMany()
                    .HasForeignKey(e => e.BugId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => e.TestCaseId);
                entity.HasIndex(e => e.ExecutedAt);
                entity.HasIndex(e => e.Result);
            });
        }

        private void ConfigurePersonEntities(ModelBuilder builder)
        {
            // Configure Person
            builder.Entity<Person>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.PersonCode).IsRequired().HasMaxLength(20);
                entity.HasIndex(e => e.PersonCode).IsUnique();
                entity.Property(e => e.FirstName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.LastName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Email).IsRequired().HasMaxLength(255);
                entity.HasIndex(e => e.Email).IsUnique();
                entity.Property(e => e.Phone).HasMaxLength(20);
                entity.Property(e => e.Type).HasConversion<int>();
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.Organization).HasMaxLength(200);
                entity.Property(e => e.Department).HasMaxLength(100);
                entity.Property(e => e.Title).HasMaxLength(100);
                entity.Property(e => e.Location).HasMaxLength(100);
                entity.Property(e => e.EmployeeId).HasMaxLength(50);
                entity.Property(e => e.Bio).HasMaxLength(1000);
                entity.Property(e => e.CommunicationPreferences).HasMaxLength(500);
                entity.Property(e => e.WorkingHours).HasMaxLength(100);
                entity.Property(e => e.TimeZone).HasMaxLength(50);
                entity.Property(e => e.Notes).HasMaxLength(1000);
                entity.Property(e => e.HasSystemAccess).HasDefaultValue(false);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("CURRENT_TIMESTAMP");

                // Relationships
                entity.HasOne(e => e.User)
                    .WithMany()
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.SetNull);

                // Indexes
                entity.HasIndex(e => e.Type);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.Department);
                entity.HasIndex(e => e.Organization);
            });

            // Configure PersonRole
            builder.Entity<PersonRole>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.RoleCode).IsRequired().HasMaxLength(50);
                entity.Property(e => e.RoleName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.RoleDescription).HasMaxLength(200);
                entity.Property(e => e.Scope).HasConversion<int>();
                entity.Property(e => e.ScopeId).HasMaxLength(50);
                entity.Property(e => e.ScopeName).HasMaxLength(100);
                entity.Property(e => e.IsActive).HasDefaultValue(true);
                entity.Property(e => e.Notes).HasMaxLength(500);
                entity.Property(e => e.EffectiveFrom).HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.HasOne(e => e.Person)
                    .WithMany(p => p.Roles)
                    .HasForeignKey(e => e.PersonId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.AssignedBy)
                    .WithMany()
                    .HasForeignKey(e => e.AssignedByUserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => e.PersonId);
                entity.HasIndex(e => e.RoleCode);
                entity.HasIndex(e => e.Scope);
            });

            // Configure PersonSkill
            builder.Entity<PersonSkill>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Level).HasConversion<int>();
                entity.Property(e => e.CertificationBody).HasMaxLength(100);
                entity.Property(e => e.CertificationNumber).HasMaxLength(100);
                entity.Property(e => e.IsVerified).HasDefaultValue(false);
                entity.Property(e => e.Notes).HasMaxLength(500);

                entity.HasOne(e => e.Person)
                    .WithMany(p => p.Skills)
                    .HasForeignKey(e => e.PersonId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Skill)
                    .WithMany()
                    .HasForeignKey(e => e.SkillId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.VerifiedBy)
                    .WithMany()
                    .HasForeignKey(e => e.VerifiedByUserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => new { e.PersonId, e.SkillId }).IsUnique();
            });

            // Configure PersonProject
            builder.Entity<PersonProject>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ProjectRole).IsRequired().HasMaxLength(100);
                entity.Property(e => e.AllocationPercentage).HasColumnType("decimal(5,2)").HasDefaultValue(100);
                entity.Property(e => e.IsKeyMember).HasDefaultValue(false);
                entity.Property(e => e.ReceiveNotifications).HasDefaultValue(true);
                entity.Property(e => e.IsActive).HasDefaultValue(true);
                entity.Property(e => e.Notes).HasMaxLength(1000);
                entity.Property(e => e.StartDate).HasDefaultValueSql("CURRENT_TIMESTAMP");

                entity.HasOne(e => e.Person)
                    .WithMany(p => p.ProjectAssignments)
                    .HasForeignKey(e => e.PersonId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Project)
                    .WithMany()
                    .HasForeignKey(e => e.ProjectId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.AssignedBy)
                    .WithMany()
                    .HasForeignKey(e => e.AssignedByUserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => new { e.PersonId, e.ProjectId }).IsUnique();
            });

            // Configure PersonStakeholder
            builder.Entity<PersonStakeholder>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Type).HasConversion<int>();
                entity.Property(e => e.Influence).HasConversion<int>();
                entity.Property(e => e.Interest).HasConversion<int>();
                entity.Property(e => e.StakeholderNotes).HasMaxLength(1000);
                entity.Property(e => e.CommunicationStrategy).HasMaxLength(500);
                entity.Property(e => e.RequiresRegularUpdates).HasDefaultValue(true);

                entity.HasOne(e => e.Person)
                    .WithOne(p => p.StakeholderInfo)
                    .HasForeignKey<PersonStakeholder>(e => e.PersonId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => e.PersonId).IsUnique();
            });

            // Configure PersonResource
            builder.Entity<PersonResource>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.HourlyRate).HasColumnType("decimal(18,2)").HasDefaultValue(0);
                entity.Property(e => e.DailyCapacity).HasColumnType("decimal(5,2)").HasDefaultValue(8);
                entity.Property(e => e.CostCenter).HasMaxLength(50);
                entity.Property(e => e.JobGrade).HasMaxLength(100);
                entity.Property(e => e.ContractType).HasMaxLength(100);
                entity.Property(e => e.IsAvailableForAllocation).HasDefaultValue(true);
                entity.Property(e => e.ResourceNotes).HasMaxLength(500);

                entity.HasOne(e => e.Person)
                    .WithOne(p => p.ResourceInfo)
                    .HasForeignKey<PersonResource>(e => e.PersonId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Manager)
                    .WithMany()
                    .HasForeignKey(e => e.ManagerId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => e.PersonId).IsUnique();
            });

            // Configure PersonContact
            builder.Entity<PersonContact>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Type).HasConversion<int>();
                entity.Property(e => e.Value).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Label).HasMaxLength(100);
                entity.Property(e => e.IsPrimary).HasDefaultValue(false);
                entity.Property(e => e.IsPublic).HasDefaultValue(true);

                entity.HasOne(e => e.Person)
                    .WithMany(p => p.Contacts)
                    .HasForeignKey(e => e.PersonId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => e.PersonId);
            });

            // Configure PersonAvailability
            builder.Entity<PersonAvailability>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Type).HasConversion<int>();
                entity.Property(e => e.Reason).HasMaxLength(200);
                entity.Property(e => e.AvailabilityPercentage).HasColumnType("decimal(5,2)").HasDefaultValue(100);
                entity.Property(e => e.Notes).HasMaxLength(500);

                entity.HasOne(e => e.Person)
                    .WithMany(p => p.Availability)
                    .HasForeignKey(e => e.PersonId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => e.PersonId);
                entity.HasIndex(e => new { e.StartDate, e.EndDate });
            });
        }
    }
}
