using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Models.ViewModels
{
    public class TimeEntryViewModel
    {
        public int Id { get; set; }
        public int TaskId { get; set; }
        public string TaskTitle { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public double Duration { get; set; }
        public string? Description { get; set; }
    }

    public class TimeTrackingViewModel
    {
        public List<TimeEntryViewModel> TimeEntries { get; set; } = new();
        public TimeEntryViewModel? ActiveTimeEntry { get; set; }
    }

    public class TimeByTaskViewModel
    {
        public string TaskName { get; set; } = string.Empty;
        public double Hours { get; set; }
    }

    public class TimeTrackingReportViewModel
    {
        [DataType(DataType.Date)]
        public DateTime StartDate { get; set; }

        [DataType(DataType.Date)]
        public DateTime EndDate { get; set; }

        public List<TimeByTaskViewModel> TimeByTask { get; set; } = new();
        
        public double TotalHours => TimeByTask.Sum(t => t.Hours);
    }
}
