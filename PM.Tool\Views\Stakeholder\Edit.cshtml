@model PM.Tool.Models.ViewModels.StakeholderEditViewModel
@using PM.Tool.Core.Entities

@{
    ViewData["Title"] = "Edit Stakeholder";
    ViewData["PageTitle"] = $"Edit {Model.Name}";
    ViewData["PageDescription"] = "Update stakeholder information and settings.";
}

@section Styles {
    <style>
        .form-section {
            transition: all 0.3s ease-in-out;
        }
        .form-group {
            margin-bottom: 1.5rem;
        }
        .required-field::after {
            content: " *";
            color: #ef4444;
        }
        .influence-matrix {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 0.5rem;
            margin-top: 0.5rem;
        }
        .influence-option {
            padding: 0.5rem;
            text-align: center;
            border: 2px solid #e5e7eb;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.2s;
        }
        .influence-option.selected {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
    </style>
}

<!-- Page Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8">
    <div>
        <h1 class="text-3xl font-bold text-neutral-900 dark:text-white mb-2">@ViewData["PageTitle"]</h1>
        <p class="text-neutral-600 dark:text-dark-400">@ViewData["PageDescription"]</p>
    </div>
    <div class="flex space-x-3 mt-4 lg:mt-0">
        <a href="@Url.Action("Details", new { id = Model.Id })" class="btn-secondary">
            <i class="fas fa-eye mr-2"></i>
            View Details
        </a>
        <a href="@Url.Action("Index")" class="btn-secondary">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Stakeholders
        </a>
    </div>
</div>

<form asp-action="Edit" method="post" id="editStakeholderForm">
    <div asp-validation-summary="ModelOnly" class="text-danger mb-4"></div>
    
    <input type="hidden" asp-for="Id" />
    <input type="hidden" asp-for="CreatedAt" />

    <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
        <!-- Main Form Content -->
        <div class="xl:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
                <h2 class="text-xl font-semibold text-neutral-900 dark:text-white mb-6">Basic Information</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="form-group">
                        <label asp-for="Name" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1 required-field">Full Name</label>
                        <input asp-for="Name" class="form-input" required />
                        <span asp-validation-for="Name" class="text-danger text-sm"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Email" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1 required-field">Email Address</label>
                        <input asp-for="Email" type="email" class="form-input" required />
                        <span asp-validation-for="Email" class="text-danger text-sm"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Phone" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Phone Number</label>
                        <input asp-for="Phone" type="tel" class="form-input" />
                        <span asp-validation-for="Phone" class="text-danger text-sm"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Title" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Job Title</label>
                        <input asp-for="Title" class="form-input" />
                        <span asp-validation-for="Title" class="text-danger text-sm"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Department" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Department</label>
                        <input asp-for="Department" class="form-input" />
                        <span asp-validation-for="Department" class="text-danger text-sm"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Organization" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Organization</label>
                        <input asp-for="Organization" class="form-input" />
                        <span asp-validation-for="Organization" class="text-danger text-sm"></span>
                    </div>
                </div>
            </div>

            <!-- Stakeholder Analysis -->
            <div class="bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
                <h2 class="text-xl font-semibold text-neutral-900 dark:text-white mb-6">Stakeholder Analysis</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- Influence Level -->
                    <div class="form-group">
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Influence Level</label>
                        <div class="influence-matrix">
                            @foreach (InfluenceLevel level in Enum.GetValues<InfluenceLevel>())
                            {
                                <div class="influence-option @(Model.Influence == level ? "selected" : "")" 
                                     data-value="@level" data-type="influence">
                                    <div class="text-xs font-medium">@level</div>
                                </div>
                            }
                        </div>
                        <input type="hidden" asp-for="Influence" id="influenceInput" />
                        <span asp-validation-for="Influence" class="text-danger text-sm"></span>
                    </div>

                    <!-- Interest Level -->
                    <div class="form-group">
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Interest Level</label>
                        <div class="influence-matrix">
                            @foreach (InterestLevel level in Enum.GetValues<InterestLevel>())
                            {
                                <div class="influence-option @(Model.Interest == level ? "selected" : "")" 
                                     data-value="@level" data-type="interest">
                                    <div class="text-xs font-medium">@level</div>
                                </div>
                            }
                        </div>
                        <input type="hidden" asp-for="Interest" id="interestInput" />
                        <span asp-validation-for="Interest" class="text-danger text-sm"></span>
                    </div>
                </div>

                <!-- Priority Display -->
                <div class="mt-6 p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-neutral-700 dark:text-dark-300">Calculated Priority:</span>
                        <span id="priorityDisplay" class="px-3 py-1 rounded-full text-sm font-medium">
                            <!-- Will be updated by JavaScript -->
                        </span>
                    </div>
                </div>
            </div>

            <!-- Communication & Notes -->
            <div class="bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
                <h2 class="text-xl font-semibold text-neutral-900 dark:text-white mb-6">Communication & Notes</h2>
                
                <div class="space-y-6">
                    <div class="form-group">
                        <label asp-for="CommunicationPreferences" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Communication Preferences</label>
                        <textarea asp-for="CommunicationPreferences" rows="3" class="form-textarea" placeholder="Preferred communication methods, frequency, best times to contact, etc."></textarea>
                        <span asp-validation-for="CommunicationPreferences" class="text-danger text-sm"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Notes" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Notes</label>
                        <textarea asp-for="Notes" rows="4" class="form-textarea" placeholder="Additional notes about this stakeholder, their concerns, requirements, etc."></textarea>
                        <span asp-validation-for="Notes" class="text-danger text-sm"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Type & Role -->
            <div class="bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-white mb-4">Type & Role</h3>
                
                <div class="space-y-4">
                    <div class="form-group">
                        <label asp-for="Type" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Stakeholder Type</label>
                        <select asp-for="Type" class="form-select">
                            @foreach (StakeholderType type in Enum.GetValues<StakeholderType>())
                            {
                                <option value="@type">@type</option>
                            }
                        </select>
                        <span asp-validation-for="Type" class="text-danger text-sm"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Role" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Stakeholder Role</label>
                        <select asp-for="Role" class="form-select">
                            @foreach (StakeholderRole role in Enum.GetValues<StakeholderRole>())
                            {
                                <option value="@role">@role</option>
                            }
                        </select>
                        <span asp-validation-for="Role" class="text-danger text-sm"></span>
                    </div>
                </div>
            </div>

            <!-- Status & System Access -->
            <div class="bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-white mb-4">Status & Access</h3>
                
                <div class="space-y-4">
                    <div class="flex items-center">
                        <input asp-for="IsActive" type="checkbox" class="rounded border-neutral-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50" />
                        <label asp-for="IsActive" class="ml-2 text-sm text-neutral-700 dark:text-dark-300">Active stakeholder</label>
                    </div>
                    
                    <div class="form-group">
                        <label asp-for="UserId" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Linked User Account</label>
                        <input asp-for="UserId" class="form-input" placeholder="User ID if they have system access" />
                        <span asp-validation-for="UserId" class="text-danger text-sm"></span>
                        <p class="text-xs text-neutral-500 mt-1">Link to system user account if applicable</p>
                    </div>
                </div>
            </div>

            <!-- Metadata -->
            <div class="bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-white mb-4">Information</h3>
                
                <div class="space-y-3 text-sm">
                    <div class="flex justify-between">
                        <span class="text-neutral-600 dark:text-dark-400">Created:</span>
                        <span class="text-neutral-900 dark:text-white">@Model.CreatedAt.ToString("MMM dd, yyyy")</span>
                    </div>
                    
                    @if (Model.UpdatedAt.HasValue)
                    {
                        <div class="flex justify-between">
                            <span class="text-neutral-600 dark:text-dark-400">Last Updated:</span>
                            <span class="text-neutral-900 dark:text-white">@Model.UpdatedAt.Value.ToString("MMM dd, yyyy")</span>
                        </div>
                    }
                </div>
            </div>

            <!-- Form Actions -->
            <div class="bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
                @{
                    ViewData["SubmitText"] = "Update Stakeholder";
                    ViewData["SubmitIcon"] = "fas fa-save";
                    ViewData["CancelUrl"] = Url.Action("Details", new { id = Model.Id });
                    ViewData["ShowCancel"] = true;
                    ViewData["SubmitClass"] = "btn-primary";
                    ViewData["CancelClass"] = "btn-secondary";
                }
                <partial name="Components/_FormActions" view-data="ViewData" />
            </div>
        </div>
    </div>
</form>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        $(document).ready(function() {
            // Handle influence/interest matrix selection
            $('.influence-option').on('click', function() {
                const type = $(this).data('type');
                const value = $(this).data('value');
                
                // Remove selected class from siblings
                $(this).siblings().removeClass('selected');
                // Add selected class to clicked option
                $(this).addClass('selected');
                
                // Update hidden input
                if (type === 'influence') {
                    $('#influenceInput').val(value);
                } else if (type === 'interest') {
                    $('#interestInput').val(value);
                }
                
                // Update priority display
                updatePriorityDisplay();
            });

            function updatePriorityDisplay() {
                const influence = $('#influenceInput').val();
                const interest = $('#interestInput').val();
                
                // Calculate priority based on influence and interest
                let priority = 'Medium';
                let priorityClass = 'bg-yellow-100 text-yellow-800';
                
                if ((influence === 'High' || influence === 'VeryHigh') && 
                    (interest === 'High' || interest === 'VeryHigh')) {
                    priority = 'Critical';
                    priorityClass = 'bg-red-100 text-red-800';
                } else if ((influence === 'High' || influence === 'VeryHigh') || 
                          (interest === 'High' || interest === 'VeryHigh')) {
                    priority = 'High';
                    priorityClass = 'bg-orange-100 text-orange-800';
                } else if ((influence === 'Low' || influence === 'VeryLow') && 
                          (interest === 'Low' || interest === 'VeryLow')) {
                    priority = 'Low';
                    priorityClass = 'bg-neutral-100 text-neutral-800';
                }
                
                $('#priorityDisplay').text(priority + ' Priority').attr('class', 'px-3 py-1 rounded-full text-sm font-medium ' + priorityClass);
            }

            // Initialize priority display
            updatePriorityDisplay();

            // Form validation
            $('#editStakeholderForm').on('submit', function(e) {
                const name = $('#Name').val().trim();
                const email = $('#Email').val().trim();

                if (!name || !email) {
                    e.preventDefault();
                    alert('Please fill in all required fields (Name, Email)');
                    return false;
                }

                // Basic email validation
                const emailRegex = /^[^\s@@]+@@[^\s@@]+\.[^\s@@]+$/;
                if (!emailRegex.test(email)) {
                    e.preventDefault();
                    alert('Please enter a valid email address');
                    return false;
                }

                return true;
            });
        });
    </script>
}
