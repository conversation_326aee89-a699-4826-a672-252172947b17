# PowerShell script to fix placeholder translations in resource files
# This script replaces placeholder values with proper translations for key languages

$resourcesPath = "PM.Tool/Resources"

Write-Host "Fixing placeholder translations..." -ForegroundColor Green

# Define translations for key WBS strings
$wbsTranslations = @{
    "zh-CN" = @{
        "WBS.ErrorLoadingStructure" = "加载WBS结构时出错"
        "WBS.TaskCreatedSuccessfully" = "任务创建成功"
        "WBS.TaskDeletedSuccessfully" = "任务删除成功"
        "WBS.TaskDuplicatedSuccessfully" = "任务复制成功"
        "WBS.TaskMovedSuccessfully" = "任务{0}移动成功"
        "WBS.TaskStatusUpdated" = "任务状态已更新为{0}"
        "WBS.WbsCodesGenerated" = "WBS代码生成成功"
        "WBS.ExportingTo" = "正在导出WBS到{0}..."
        "WBS.ExportingTask" = "正在导出任务..."
        "WBS.PrintingWbs" = "正在打开打印对话框..."
        "WBS.CompactViewEnabled" = "紧凑视图已启用"
        "WBS.NormalViewEnabled" = "正常视图已启用"
        "WBS.DetailedViewEnabled" = "详细视图已启用"
        "WBS.RefreshingView" = "正在刷新WBS视图..."
        "WBS.FilteredToShow" = "已筛选显示{0}任务"
        "WBS.ShowingAllTasks" = "显示所有任务"
        "WBS.FilteredOverdue" = "已筛选显示逾期任务"
        "WBS.TaskTitleRequired" = "任务标题是必需的"
        "WBS.ProjectIdMissing" = "项目ID缺失"
        "WBS.ValidationError" = "验证错误。请检查您的输入。"
        "WBS.AccessDenied" = "访问被拒绝。请刷新页面后重试。"
        "WBS.RequestFormatError" = "请求格式错误。请重试。"
        "WBS.ErrorCreatingTask" = "创建任务时出错"
        "WBS.ErrorLoadingTaskDetails" = "加载任务详情时出错"
        "WBS.ErrorDeletingTask" = "删除任务时出错"
        "WBS.ErrorDuplicatingTask" = "复制任务时出错"
        "WBS.ErrorMovingTask" = "移动任务{0}时出错"
        "WBS.ErrorUpdatingTaskStatus" = "更新任务状态时出错"
        "WBS.ErrorGeneratingCodes" = "生成WBS代码时出错"
        "WBS.ConfirmDeleteTask" = "您确定要删除此任务吗？此操作无法撤销。"
        "WBS.ConfirmDuplicateTask" = "创建此任务的副本？"
        "WBS.ConfirmGenerateCodes" = "这将重新生成所有WBS代码。继续？"
        "WBS.CreateNewTask" = "创建新任务"
        "WBS.CreateChildTaskFor" = "为{0}创建子任务"
        "WBS.TaskNotFound" = "未找到任务"
        "WBS.UnsupportedExportFormat" = "不支持的导出格式"
        "WBS.GanttViewComingSoon" = "甘特图视图即将推出！"
        "WBS.Action.ViewDetails" = "查看详情"
        "WBS.Action.EditTask" = "编辑任务"
        "WBS.Action.Duplicate" = "复制"
        "WBS.Action.AddChild" = "添加子项"
        "WBS.Action.MoreActions" = "更多操作"
        "WBS.Action.MoveUp" = "上移"
        "WBS.Action.MoveDown" = "下移"
        "WBS.Action.StartTask" = "开始任务"
        "WBS.Action.MarkInReview" = "标记为审核中"
        "WBS.Action.MarkComplete" = "标记为完成"
        "WBS.Action.CancelTask" = "取消任务"
        "WBS.Action.ExportTask" = "导出任务"
        "WBS.Action.DeleteTask" = "删除任务"
        "WBS.Label.Progress" = "进度"
        "WBS.Label.Unassigned" = "未分配"
        "WBS.Label.Overdue" = "逾期"
        "WBS.Label.Start" = "开始"
        "WBS.Label.Due" = "截止"
        "WBS.Label.GeneratedOn" = "生成于"
        "WBS.Label.WorkBreakdownStructure" = "工作分解结构"
    }
    
    "ja-JP" = @{
        "WBS.ErrorLoadingStructure" = "WBS構造の読み込みエラー"
        "WBS.TaskCreatedSuccessfully" = "タスクが正常に作成されました"
        "WBS.TaskDeletedSuccessfully" = "タスクが正常に削除されました"
        "WBS.TaskDuplicatedSuccessfully" = "タスクが正常に複製されました"
        "WBS.TaskMovedSuccessfully" = "タスクが{0}に正常に移動されました"
        "WBS.TaskStatusUpdated" = "タスクステータスが{0}に更新されました"
        "WBS.WbsCodesGenerated" = "WBSコードが正常に生成されました"
        "WBS.ExportingTo" = "WBSを{0}にエクスポート中..."
        "WBS.ExportingTask" = "タスクをエクスポート中..."
        "WBS.PrintingWbs" = "印刷ダイアログを開いています..."
        "WBS.CompactViewEnabled" = "コンパクトビューが有効になりました"
        "WBS.NormalViewEnabled" = "通常ビューが有効になりました"
        "WBS.DetailedViewEnabled" = "詳細ビューが有効になりました"
        "WBS.RefreshingView" = "WBSビューを更新中..."
        "WBS.FilteredToShow" = "{0}タスクを表示するようにフィルタリングされました"
        "WBS.ShowingAllTasks" = "すべてのタスクを表示"
        "WBS.FilteredOverdue" = "期限切れタスクを表示するようにフィルタリングされました"
        "WBS.TaskTitleRequired" = "タスクタイトルが必要です"
        "WBS.ProjectIdMissing" = "プロジェクトIDが不足しています"
        "WBS.ValidationError" = "検証エラー。入力を確認してください。"
        "WBS.AccessDenied" = "アクセスが拒否されました。ページを更新して再試行してください。"
        "WBS.RequestFormatError" = "リクエスト形式エラー。再試行してください。"
        "WBS.ErrorCreatingTask" = "タスク作成エラー"
        "WBS.ErrorLoadingTaskDetails" = "タスク詳細の読み込みエラー"
        "WBS.ErrorDeletingTask" = "タスク削除エラー"
        "WBS.ErrorDuplicatingTask" = "タスク複製エラー"
        "WBS.ErrorMovingTask" = "タスク{0}移動エラー"
        "WBS.ErrorUpdatingTaskStatus" = "タスクステータス更新エラー"
        "WBS.ErrorGeneratingCodes" = "WBSコード生成エラー"
        "WBS.ConfirmDeleteTask" = "このタスクを削除してもよろしいですか？この操作は元に戻せません。"
        "WBS.ConfirmDuplicateTask" = "このタスクの複製を作成しますか？"
        "WBS.ConfirmGenerateCodes" = "これによりすべてのWBSコードが再生成されます。続行しますか？"
        "WBS.CreateNewTask" = "新しいタスクを作成"
        "WBS.CreateChildTaskFor" = "{0}の子タスクを作成"
        "WBS.TaskNotFound" = "タスクが見つかりません"
        "WBS.UnsupportedExportFormat" = "サポートされていないエクスポート形式"
        "WBS.GanttViewComingSoon" = "ガントビュー機能は近日公開予定です！"
        "WBS.Action.ViewDetails" = "詳細を表示"
        "WBS.Action.EditTask" = "タスクを編集"
        "WBS.Action.Duplicate" = "複製"
        "WBS.Action.AddChild" = "子を追加"
        "WBS.Action.MoreActions" = "その他のアクション"
        "WBS.Action.MoveUp" = "上に移動"
        "WBS.Action.MoveDown" = "下に移動"
        "WBS.Action.StartTask" = "タスクを開始"
        "WBS.Action.MarkInReview" = "レビュー中としてマーク"
        "WBS.Action.MarkComplete" = "完了としてマーク"
        "WBS.Action.CancelTask" = "タスクをキャンセル"
        "WBS.Action.ExportTask" = "タスクをエクスポート"
        "WBS.Action.DeleteTask" = "タスクを削除"
        "WBS.Label.Progress" = "進捗"
        "WBS.Label.Unassigned" = "未割り当て"
        "WBS.Label.Overdue" = "期限切れ"
        "WBS.Label.Start" = "開始"
        "WBS.Label.Due" = "期限"
        "WBS.Label.GeneratedOn" = "生成日"
        "WBS.Label.WorkBreakdownStructure" = "作業分解構造"
    }
    
    "ru-RU" = @{
        "WBS.ErrorLoadingStructure" = "Ошибка загрузки структуры WBS"
        "WBS.TaskCreatedSuccessfully" = "Задача успешно создана"
        "WBS.TaskDeletedSuccessfully" = "Задача успешно удалена"
        "WBS.TaskDuplicatedSuccessfully" = "Задача успешно дублирована"
        "WBS.TaskMovedSuccessfully" = "Задача успешно перемещена {0}"
        "WBS.TaskStatusUpdated" = "Статус задачи обновлен на {0}"
        "WBS.WbsCodesGenerated" = "Коды WBS успешно сгенерированы"
        "WBS.ExportingTo" = "Экспорт WBS в {0}..."
        "WBS.ExportingTask" = "Экспорт задачи..."
        "WBS.PrintingWbs" = "Открытие диалога печати..."
        "WBS.CompactViewEnabled" = "Компактный вид включен"
        "WBS.NormalViewEnabled" = "Обычный вид включен"
        "WBS.DetailedViewEnabled" = "Подробный вид включен"
        "WBS.RefreshingView" = "Обновление вида WBS..."
        "WBS.FilteredToShow" = "Отфильтровано для показа задач {0}"
        "WBS.ShowingAllTasks" = "Показаны все задачи"
        "WBS.FilteredOverdue" = "Отфильтровано для показа просроченных задач"
        "WBS.TaskTitleRequired" = "Название задачи обязательно"
        "WBS.ProjectIdMissing" = "Отсутствует ID проекта"
        "WBS.ValidationError" = "Ошибка валидации. Проверьте ввод."
        "WBS.AccessDenied" = "Доступ запрещен. Обновите страницу и попробуйте снова."
        "WBS.RequestFormatError" = "Ошибка формата запроса. Попробуйте снова."
        "WBS.ErrorCreatingTask" = "Ошибка создания задачи"
        "WBS.ErrorLoadingTaskDetails" = "Ошибка загрузки деталей задачи"
        "WBS.ErrorDeletingTask" = "Ошибка удаления задачи"
        "WBS.ErrorDuplicatingTask" = "Ошибка дублирования задачи"
        "WBS.ErrorMovingTask" = "Ошибка перемещения задачи {0}"
        "WBS.ErrorUpdatingTaskStatus" = "Ошибка обновления статуса задачи"
        "WBS.ErrorGeneratingCodes" = "Ошибка генерации кодов WBS"
        "WBS.ConfirmDeleteTask" = "Вы уверены, что хотите удалить эту задачу? Это действие нельзя отменить."
        "WBS.ConfirmDuplicateTask" = "Создать дубликат этой задачи?"
        "WBS.ConfirmGenerateCodes" = "Это перегенерирует все коды WBS. Продолжить?"
        "WBS.CreateNewTask" = "Создать новую задачу"
        "WBS.CreateChildTaskFor" = "Создать дочернюю задачу для: {0}"
        "WBS.TaskNotFound" = "Задача не найдена"
        "WBS.UnsupportedExportFormat" = "Неподдерживаемый формат экспорта"
        "WBS.GanttViewComingSoon" = "Вид Гантта скоро появится!"
        "WBS.Action.ViewDetails" = "Просмотр деталей"
        "WBS.Action.EditTask" = "Редактировать задачу"
        "WBS.Action.Duplicate" = "Дублировать"
        "WBS.Action.AddChild" = "Добавить дочернюю"
        "WBS.Action.MoreActions" = "Больше действий"
        "WBS.Action.MoveUp" = "Переместить вверх"
        "WBS.Action.MoveDown" = "Переместить вниз"
        "WBS.Action.StartTask" = "Начать задачу"
        "WBS.Action.MarkInReview" = "Отметить на проверке"
        "WBS.Action.MarkComplete" = "Отметить завершенной"
        "WBS.Action.CancelTask" = "Отменить задачу"
        "WBS.Action.ExportTask" = "Экспортировать задачу"
        "WBS.Action.DeleteTask" = "Удалить задачу"
        "WBS.Label.Progress" = "Прогресс"
        "WBS.Label.Unassigned" = "Не назначено"
        "WBS.Label.Overdue" = "Просрочено"
        "WBS.Label.Start" = "Начало"
        "WBS.Label.Due" = "Срок"
        "WBS.Label.GeneratedOn" = "Сгенерировано"
        "WBS.Label.WorkBreakdownStructure" = "Структура декомпозиции работ"
    }
}

# Process each culture that has translations
foreach ($culture in $wbsTranslations.Keys) {
    $cultureFile = "$resourcesPath/SharedResources.$culture.resx"
    
    if (Test-Path $cultureFile) {
        Write-Host "Processing culture: $culture" -ForegroundColor Yellow
        
        try {
            # Read the file content as text to handle encoding properly
            $content = Get-Content $cultureFile -Raw -Encoding UTF8
            
            $updatedCount = 0
            
            # Replace placeholder values with actual translations
            foreach ($key in $wbsTranslations[$culture].Keys) {
                $translation = $wbsTranslations[$culture][$key]
                $placeholderPattern = "\[.*? - $culture\]"
                
                # Look for the specific key and replace its placeholder value
                $keyPattern = "(<data name=`"$([regex]::Escape($key))`"[^>]*>[\s\S]*?<value>)$placeholderPattern(</value>)"
                
                if ($content -match $keyPattern) {
                    $content = $content -replace $keyPattern, "`$1$translation`$2"
                    $updatedCount++
                }
            }
            
            if ($updatedCount -gt 0) {
                # Save the updated content
                $content | Out-File -FilePath $cultureFile -Encoding UTF8 -NoNewline
                Write-Host "  Updated $updatedCount translations" -ForegroundColor Green
            } else {
                Write-Host "  No placeholder values found to update" -ForegroundColor Cyan
            }
        }
        catch {
            Write-Host "  Error processing $cultureFile: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "Culture file not found: $cultureFile" -ForegroundColor Red
    }
}

Write-Host "`nPlaceholder translation fix completed!" -ForegroundColor Green
