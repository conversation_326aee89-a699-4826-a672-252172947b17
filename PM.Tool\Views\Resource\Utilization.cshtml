@model IEnumerable<object>

@{
    ViewData["Title"] = "Resource Utilization";
    ViewData["PageTitle"] = "Resource Utilization Report";
    ViewData["PageDescription"] = "View resource utilization metrics and analytics";
    ViewData["BreadcrumbItems"] = new List<(string Text, string? Url)>
    {
        ("Resources", Url.Action("Index")),
        ("Utilization", null)
    };

    var startDate = ViewBag.StartDate as DateTime? ?? DateTime.Today.AddDays(-30);
    var endDate = ViewBag.EndDate as DateTime? ?? DateTime.Today;
}

<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100">Resource Utilization</h1>
            <p class="text-neutral-600 dark:text-dark-300">
                @startDate.ToString("MMM dd, yyyy") - @endDate.ToString("MMM dd, yyyy")
            </p>
        </div>

        <div class="flex space-x-3">
            <button type="button" onclick="exportReport()" class="btn-secondary">
                <i class="fas fa-download mr-2"></i>
                Export
            </button>
            <button type="button" onclick="refreshData()" class="btn-primary">
                <i class="fas fa-sync-alt mr-2"></i>
                Refresh
            </button>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="card-custom">
        <div class="card-header-custom">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                <i class="fas fa-calendar-alt mr-2 text-primary-500"></i>
                Date Range
            </h3>
        </div>
        <div class="card-body-custom">
            <form method="get" class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                <div>
                    <label for="startDate" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Start Date</label>
                    <input type="date" id="startDate" name="startDate" value="@startDate.ToString("yyyy-MM-dd")" class="form-input" />
                </div>

                <div>
                    <label for="endDate" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">End Date</label>
                    <input type="date" id="endDate" name="endDate" value="@endDate.ToString("yyyy-MM-dd")" class="form-input" />
                </div>

                <div>
                    <button type="submit" class="btn-primary w-full">
                        <i class="fas fa-search mr-2"></i>
                        Update Report
                    </button>
                </div>

                <div>
                    <button type="button" onclick="setQuickRange('week')" class="btn-outline-secondary w-full">
                        Last Week
                    </button>
                </div>
            </form>

            <div class="flex space-x-2 mt-4">
                <button type="button" onclick="setQuickRange('month')" class="btn-outline-secondary">
                    Last Month
                </button>
                <button type="button" onclick="setQuickRange('quarter')" class="btn-outline-secondary">
                    Last Quarter
                </button>
                <button type="button" onclick="setQuickRange('year')" class="btn-outline-secondary">
                    Last Year
                </button>
            </div>
        </div>
    </div>

    <!-- Summary Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        @{
            var totalResources = Model.Count();
            var avgUtilization = Model.Any() ? Model.Average(r => (double)(r.GetType().GetProperty("Utilization")?.GetValue(r) ?? 0.0)) : 0;
            var overUtilized = Model.Count(r => (double)(r.GetType().GetProperty("Utilization")?.GetValue(r) ?? 0) > 100);
            var underUtilized = Model.Count(r => (double)(r.GetType().GetProperty("Utilization")?.GetValue(r) ?? 0) < 50);
        }

        @{
            ViewData["Title"] = "Total Resources";
            ViewData["Icon"] = "fas fa-users";
            ViewData["IconColor"] = "bg-gradient-to-br from-blue-500 to-blue-600";
            ViewData["Value"] = totalResources.ToString();
            ViewData["Description"] = "active resources";
        }
        <partial name="Components/_StatsCard" view-data="ViewData" />

        @{
            ViewData["Title"] = "Average Utilization";
            ViewData["Icon"] = "fas fa-chart-line";
            ViewData["IconColor"] = "bg-gradient-to-br from-green-500 to-green-600";
            ViewData["Value"] = $"{avgUtilization:F1}%";
            ViewData["Description"] = "across all resources";
        }
        <partial name="Components/_StatsCard" view-data="ViewData" />

        @{
            ViewData["Title"] = "Over-Utilized";
            ViewData["Icon"] = "fas fa-exclamation-triangle";
            ViewData["IconColor"] = "bg-gradient-to-br from-red-500 to-red-600";
            ViewData["Value"] = overUtilized.ToString();
            ViewData["Description"] = "resources > 100%";
        }
        <partial name="Components/_StatsCard" view-data="ViewData" />

        @{
            ViewData["Title"] = "Under-Utilized";
            ViewData["Icon"] = "fas fa-arrow-down";
            ViewData["IconColor"] = "bg-gradient-to-br from-yellow-500 to-yellow-600";
            ViewData["Value"] = underUtilized.ToString();
            ViewData["Description"] = "resources < 50%";
        }
        <partial name="Components/_StatsCard" view-data="ViewData" />
    </div>

    <!-- Utilization Chart -->
    <div class="card-custom">
        <div class="card-header-custom">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                <i class="fas fa-chart-bar mr-2 text-primary-500"></i>
                Utilization Overview
            </h3>
        </div>
        <div class="card-body-custom">
            <div class="h-64 flex items-center justify-center bg-neutral-50 dark:bg-dark-700 rounded-lg">
                <canvas id="utilizationChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Resource Utilization Table -->
    <div class="card-custom">
        <div class="card-header-custom">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                <i class="fas fa-table mr-2 text-primary-500"></i>
                Resource Details
            </h3>
        </div>
        <div class="card-body-custom">
            @if (Model.Any())
            {
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-neutral-200 dark:divide-dark-600">
                        <thead class="bg-neutral-50 dark:bg-dark-700">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-300 uppercase tracking-wider">
                                    Resource
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-300 uppercase tracking-wider">
                                    Type
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-300 uppercase tracking-wider">
                                    Capacity
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-300 uppercase tracking-wider">
                                    Utilization
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-300 uppercase tracking-wider">
                                    Status
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-300 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-dark-800 divide-y divide-neutral-200 dark:divide-dark-600">
                            @foreach (var item in Model)
                            {
                                var resource = item.GetType().GetProperty("Resource")?.GetValue(item) as PM.Tool.Core.Entities.Resource;
                                var utilization = (double)(item.GetType().GetProperty("Utilization")?.GetValue(item) ?? 0);

                                if (resource != null)
                                {
                                    <tr class="hover:bg-neutral-50 dark:hover:bg-dark-700">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-8 h-8 @GetResourceTypeIconBg(resource.Type) rounded-lg flex items-center justify-center mr-3">
                                                    <i class="@GetResourceTypeIcon(resource.Type) text-white text-sm"></i>
                                                </div>
                                                <div>
                                                    <div class="text-sm font-medium text-neutral-900 dark:text-dark-100">
                                                        @resource.Name
                                                    </div>
                                                    @if (!string.IsNullOrEmpty(resource.Department))
                                                    {
                                                        <div class="text-sm text-neutral-500 dark:text-dark-400">
                                                            @resource.Department
                                                        </div>
                                                    }
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="badge-info">@resource.Type</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-neutral-900 dark:text-dark-100">
                                            @resource.Capacity hrs/day
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-16 bg-neutral-200 dark:bg-dark-600 rounded-full h-2 mr-3">
                                                    <div class="@GetUtilizationBarColor(utilization) h-2 rounded-full"
                                                         style="width: @Math.Min(utilization, 100)%"></div>
                                                </div>
                                                <span class="text-sm font-medium @GetUtilizationTextColor(utilization)">
                                                    @utilization.ToString("F1")%
                                                </span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            @{
                                                var statusBadge = utilization switch
                                                {
                                                    > 100 => "badge-danger",
                                                    >= 80 => "badge-warning",
                                                    >= 50 => "badge-success",
                                                    _ => "badge-secondary"
                                                };
                                                var statusText = utilization switch
                                                {
                                                    > 100 => "Over-utilized",
                                                    >= 80 => "High",
                                                    >= 50 => "Optimal",
                                                    _ => "Under-utilized"
                                                };
                                            }
                                            <span class="@statusBadge">@statusText</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <a asp-action="Details" asp-route-id="@resource.Id"
                                                   class="text-primary-600 hover:text-primary-900 dark:text-primary-400">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-action="Allocations" asp-route-id="@resource.Id"
                                                   class="text-green-600 hover:text-green-900 dark:text-green-400">
                                                    <i class="fas fa-calendar-alt"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-12">
                    <i class="fas fa-chart-line text-6xl text-neutral-400 dark:text-dark-500 mb-4"></i>
                    <h3 class="text-lg font-medium text-neutral-900 dark:text-dark-100 mb-2">No Utilization Data</h3>
                    <p class="text-neutral-600 dark:text-dark-300">No resource utilization data found for the selected period.</p>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        // Quick date range functions
        function setQuickRange(range) {
            const endDate = new Date();
            let startDate = new Date();

            switch(range) {
                case 'week':
                    startDate.setDate(endDate.getDate() - 7);
                    break;
                case 'month':
                    startDate.setMonth(endDate.getMonth() - 1);
                    break;
                case 'quarter':
                    startDate.setMonth(endDate.getMonth() - 3);
                    break;
                case 'year':
                    startDate.setFullYear(endDate.getFullYear() - 1);
                    break;
            }

            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
        }

        // Chart initialization
        const ctx = document.getElementById('utilizationChart').getContext('2d');
        const utilizationData = @Html.Raw(Json.Serialize(Model.Select(r => new {
            Name = ((PM.Tool.Core.Entities.Resource)r.GetType().GetProperty("Resource").GetValue(r)).Name,
            Utilization = (double)(r.GetType().GetProperty("Utilization")?.GetValue(r) ?? 0)
        })));

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: utilizationData.map(r => r.name),
                datasets: [{
                    label: 'Utilization %',
                    data: utilizationData.map(r => r.utilization),
                    backgroundColor: utilizationData.map(r =>
                        r.utilization > 100 ? '#ef4444' :
                        r.utilization >= 80 ? '#f59e0b' :
                        r.utilization >= 50 ? '#10b981' : '#6b7280'
                    ),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: Math.max(120, Math.max(...utilizationData.map(r => r.utilization)) + 10)
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        function exportReport() {
            // Implement export functionality
            alert('Export functionality would be implemented here');
        }

        function refreshData() {
            location.reload();
        }
    </script>
}

@functions {
    private string GetResourceTypeIcon(PM.Tool.Core.Entities.ResourceType type)
    {
        return type switch
        {
            PM.Tool.Core.Entities.ResourceType.Human => "fas fa-user",
            PM.Tool.Core.Entities.ResourceType.Equipment => "fas fa-tools",
            PM.Tool.Core.Entities.ResourceType.Material => "fas fa-boxes",
            PM.Tool.Core.Entities.ResourceType.Facility => "fas fa-building",
            _ => "fas fa-cube"
        };
    }

    private string GetResourceTypeIconBg(PM.Tool.Core.Entities.ResourceType type)
    {
        return type switch
        {
            PM.Tool.Core.Entities.ResourceType.Human => "bg-gradient-to-br from-blue-500 to-blue-600",
            PM.Tool.Core.Entities.ResourceType.Equipment => "bg-gradient-to-br from-orange-500 to-orange-600",
            PM.Tool.Core.Entities.ResourceType.Material => "bg-gradient-to-br from-green-500 to-green-600",
            PM.Tool.Core.Entities.ResourceType.Facility => "bg-gradient-to-br from-purple-500 to-purple-600",
            _ => "bg-gradient-to-br from-neutral-500 to-neutral-600"
        };
    }

    private string GetUtilizationBarColor(double utilization)
    {
        return utilization switch
        {
            > 100 => "bg-red-500",
            >= 80 => "bg-yellow-500",
            >= 50 => "bg-green-500",
            _ => "bg-gray-500"
        };
    }

    private string GetUtilizationTextColor(double utilization)
    {
        return utilization switch
        {
            > 100 => "text-red-600 dark:text-red-400",
            >= 80 => "text-yellow-600 dark:text-yellow-400",
            >= 50 => "text-green-600 dark:text-green-400",
            _ => "text-gray-600 dark:text-gray-400"
        };
    }
}
