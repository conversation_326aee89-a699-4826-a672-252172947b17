using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities
{
    public class TimeEntry : BaseEntity
    {
        public new int Id { get; set; }
        
        public int TaskId { get; set; }
        public TaskEntity Task { get; set; } = null!;
        
        public string UserId { get; set; } = string.Empty;
        public ApplicationUser User { get; set; } = null!;
        
        private DateTime _startTime;
        public DateTime StartTime
        {
            get => _startTime;
            set => _startTime = DateTime.SpecifyKind(value, DateTimeKind.Utc);
        }

        private DateTime? _endTime;
        public DateTime? EndTime
        {
            get => _endTime;
            set => _endTime = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        public double Duration { get; set; } // Duration in hours
        
        [MaxLength(500)]
        public string? Description { get; set; }
        
        private DateTime _createdAt = DateTime.UtcNow;
        public new DateTime CreatedAt
        {
            get => _createdAt;
            set => _createdAt = DateTime.SpecifyKind(value, DateTimeKind.Utc);
        }

        private DateTime? _updatedAt;
        public new DateTime? UpdatedAt
        {
            get => _updatedAt;
            set => _updatedAt = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }
    }
}
