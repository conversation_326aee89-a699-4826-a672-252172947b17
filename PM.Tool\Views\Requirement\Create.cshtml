@model RequirementCreateViewModel
@{
    ViewData["Title"] = "Create Requirement";
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Requirements", Href = Url.Action("Index", "Requirement"), Icon = "fas fa-clipboard-list" },
        new { Text = "Create Requirement", Href = "", Icon = "fas fa-plus" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Page Header -->
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-plus mr-3 text-primary-600 dark:text-primary-400"></i>
                Create New Requirement
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Define a new project requirement with detailed specifications
            </p>
        </div>
        @{
            ViewData["Text"] = "Back to Requirements";
            ViewData["Variant"] = "secondary";
            ViewData["Icon"] = "fas fa-arrow-left";
            ViewData["Href"] = Url.Action("Index");
        }
        <partial name="Components/_Button" view-data="ViewData" />
    </div>
</div>

<!-- Form -->
<form asp-action="Create" method="post" id="requirementForm" class="space-y-8">
    <div asp-validation-summary="ModelOnly" class="bg-danger-50 dark:bg-danger-900/20 border border-danger-200 dark:border-danger-800 rounded-lg p-4 text-danger-800 dark:text-danger-200"></div>

    <!-- Basic Information -->
    @{
        var basicInfoContent = @"
            <div class='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div class='md:col-span-2'>
                    <label asp-for='Title' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Title *</label>
                    <input asp-for='Title' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500' placeholder='Enter requirement title'>
                    <span asp-validation-for='Title' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>

                <div>
                    <label asp-for='ProjectId' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Project *</label>
                    <select asp-for='ProjectId' id='projectSelect' class='select2-enabled w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                        <option value=''>Select a project</option>
                    </select>
                    <span asp-validation-for='ProjectId' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>

                <div>
                    <label asp-for='RequirementId' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Requirement ID</label>
                    <input asp-for='RequirementId' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500' placeholder='REQ-001 (auto-generated if empty)'>
                    <span asp-validation-for='RequirementId' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>

                <div>
                    <label asp-for='Type' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Type *</label>
                    <select asp-for='Type' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                        <option value='Functional'>Functional</option>
                        <option value='NonFunctional'>Non-Functional</option>
                        <option value='Technical'>Technical</option>
                        <option value='Business'>Business</option>
                    </select>
                    <span asp-validation-for='Type' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>

                <div>
                    <label asp-for='Priority' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Priority *</label>
                    <select asp-for='Priority' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                        <option value='Low'>Low</option>
                        <option value='Medium' selected>Medium</option>
                        <option value='High'>High</option>
                        <option value='Critical'>Critical</option>
                    </select>
                    <span asp-validation-for='Priority' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>

                <div>
                    <label asp-for='Source' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Source</label>
                    <select asp-for='Source' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                        <option value='Stakeholder' selected>Stakeholder</option>
                        <option value='Customer'>Customer</option>
                        <option value='BusinessAnalyst'>Business Analyst</option>
                        <option value='Developer'>Developer</option>
                        <option value='Tester'>Tester</option>
                        <option value='ProjectManager'>Project Manager</option>
                    </select>
                    <span asp-validation-for='Source' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>

                <div>
                    <label asp-for='Status' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Status</label>
                    <select asp-for='Status' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                        <option value='Draft' selected>Draft</option>
                        <option value='UnderReview'>Under Review</option>
                        <option value='Approved'>Approved</option>
                        <option value='InProgress'>In Progress</option>
                        <option value='Completed'>Completed</option>
                        <option value='OnHold'>On Hold</option>
                    </select>
                    <span asp-validation-for='Status' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>
            </div>
        ";

        ViewData["Title"] = "Basic Information";
        ViewData["Icon"] = "fas fa-info-circle";
        ViewData["BodyContent"] = basicInfoContent;
    }
    <partial name="Components/_Card" view-data="ViewData" />

    <!-- Description -->
    @{
        var descriptionContent = @"
            <div>
                <label asp-for='Description' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Description *</label>
                <textarea asp-for='Description' rows='6' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500' placeholder='Provide a detailed description of the requirement...'></textarea>
                <span asp-validation-for='Description' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                <p class='mt-1 text-xs text-neutral-500 dark:text-dark-400'>Describe what the requirement should accomplish and any relevant context.</p>
            </div>
        ";

        ViewData["Title"] = "Description";
        ViewData["Icon"] = "fas fa-align-left";
        ViewData["BodyContent"] = descriptionContent;
    }
    <partial name="Components/_Card" view-data="ViewData" />

    <!-- Acceptance Criteria -->
    @{
        var acceptanceCriteriaContent = @"
            <div>
                <label asp-for='AcceptanceCriteria' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Acceptance Criteria</label>
                <textarea asp-for='AcceptanceCriteria' rows='4' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500' placeholder='Define the criteria that must be met for this requirement to be considered complete...'></textarea>
                <span asp-validation-for='AcceptanceCriteria' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                <p class='mt-1 text-xs text-neutral-500 dark:text-dark-400'>List specific, measurable criteria that define when this requirement is complete.</p>
            </div>
        ";

        ViewData["Title"] = "Acceptance Criteria";
        ViewData["Icon"] = "fas fa-check-square";
        ViewData["BodyContent"] = acceptanceCriteriaContent;
    }
    <partial name="Components/_Card" view-data="ViewData" />

    <!-- Business Justification -->
    @{
        var businessJustificationContent = @"
            <div>
                <label asp-for='BusinessJustification' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Business Justification</label>
                <textarea asp-for='BusinessJustification' rows='3' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500' placeholder='Explain the business value and rationale for this requirement...'></textarea>
                <span asp-validation-for='BusinessJustification' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                <p class='mt-1 text-xs text-neutral-500 dark:text-dark-400'>Describe why this requirement is important to the business.</p>
            </div>
        ";

        ViewData["Title"] = "Business Justification";
        ViewData["Icon"] = "fas fa-business-time";
        ViewData["BodyContent"] = businessJustificationContent;
    }
    <partial name="Components/_Card" view-data="ViewData" />

    <!-- Assignments -->
    @{
        var assignmentsContent = @"
            <div class='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div>
                    <label asp-for='StakeholderUserId' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Stakeholder</label>
                    <select asp-for='StakeholderUserId' id='stakeholderSelect' class='select2-enabled w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                        <option value=''>Select stakeholder</option>
                    </select>
                    <span asp-validation-for='StakeholderUserId' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>

                <div>
                    <label asp-for='AnalystUserId' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Business Analyst</label>
                    <select asp-for='AnalystUserId' id='analystSelect' class='select2-enabled w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                        <option value=''>Select analyst</option>
                    </select>
                    <span asp-validation-for='AnalystUserId' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>

                <div>
                    <label asp-for='DeveloperUserId' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Developer</label>
                    <select asp-for='DeveloperUserId' id='developerSelect' class='select2-enabled w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                        <option value=''>Select developer</option>
                    </select>
                    <span asp-validation-for='DeveloperUserId' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>

                <div>
                    <label asp-for='TesterUserId' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Tester</label>
                    <select asp-for='TesterUserId' id='testerSelect' class='select2-enabled w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                        <option value=''>Select tester</option>
                    </select>
                    <span asp-validation-for='TesterUserId' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>
            </div>
        ";

        ViewData["Title"] = "Assignments";
        ViewData["Icon"] = "fas fa-users";
        ViewData["BodyContent"] = assignmentsContent;
    }
    <partial name="Components/_Card" view-data="ViewData" />

    <!-- Additional Information -->
    @{
        var additionalInfoContent = @"
            <div class='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div>
                    <label asp-for='EstimatedEffort' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Estimated Effort (hours)</label>
                    <input asp-for='EstimatedEffort' type='number' step='0.5' min='0' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500' placeholder='0'>
                    <span asp-validation-for='EstimatedEffort' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>

                <div>
                    <label asp-for='ParentRequirementId' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Parent Requirement</label>
                    <select asp-for='ParentRequirementId' id='parentRequirementSelect' class='select2-enabled w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                        <option value=''>No parent requirement</option>
                    </select>
                    <span asp-validation-for='ParentRequirementId' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>
            </div>
        ";

        ViewData["Title"] = "Additional Information";
        ViewData["Icon"] = "fas fa-cog";
        ViewData["BodyContent"] = additionalInfoContent;
    }
    <partial name="Components/_Card" view-data="ViewData" />

    <!-- Form Actions -->
    @{
        ViewData["SubmitText"] = "Create Requirement";
        ViewData["SubmitIcon"] = "fas fa-plus";
        ViewData["CancelUrl"] = Url.Action("Index");
        ViewData["AdditionalButtons"] = "<button type='button' onclick='saveDraft()' class='inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed btn-outline-custom px-4 py-2.5 text-sm rounded-lg'><i class='fas fa-save mr-2'></i><span>Save as Draft</span></button>";
    }
    <partial name="Components/_FormActions" view-data="ViewData" />
</form>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function() {
            loadProjects();
            loadUsers();
            loadParentRequirements();
            setupFormValidation();
        });

        function loadProjects() {
            $.get('@Url.Action("GetProjects", "Projects")')
                .done(function(data) {
                    const select = $('#projectSelect');
                    if (data && Array.isArray(data)) {
                        data.forEach(function(project) {
                            const selected = project.id == @(ViewBag.ProjectId ?? 0) ? 'selected' : '';
                            select.append(`<option value="${project.id}" ${selected}>${project.name}</option>`);
                        });
                    }
                })
                .fail(function() {
                    console.error('Failed to load projects');
                });
        }

        function loadUsers() {
            $.get('/api/UserLookup/Users')
                .done(function(data) {
                    if (data && Array.isArray(data)) {
                        const stakeholderSelect = $('#stakeholderSelect');
                        const analystSelect = $('#analystSelect');
                        const developerSelect = $('#developerSelect');
                        const testerSelect = $('#testerSelect');

                        data.forEach(function(user) {
                            const option = `<option value="${user.id}">${user.fullName || user.userName} (${user.email})</option>`;
                            stakeholderSelect.append(option);
                            analystSelect.append(option);
                            developerSelect.append(option);
                            testerSelect.append(option);
                        });
                    }
                })
                .fail(function() {
                    console.error('Failed to load users');
                });
        }

        function loadParentRequirements() {
            const projectId = $('#projectSelect').val();
            if (projectId) {
                $.get('@Url.Action("GetProjectRequirements", "Requirement")', { projectId: projectId })
                    .done(function(data) {
                        const select = $('#parentRequirementSelect');
                        select.empty().append('<option value="">No parent requirement</option>');

                        if (data && Array.isArray(data)) {
                            data.forEach(function(req) {
                                select.append(`<option value="${req.id}">${req.requirementId} - ${req.title}</option>`);
                            });
                        }
                    })
                    .fail(function() {
                        console.error('Failed to load parent requirements');
                    });
            }
        }

        function setupFormValidation() {
            $('#projectSelect').on('change', loadParentRequirements);

            $('#requirementForm').on('submit', function(e) {
                // Additional client-side validation can be added here
            });
        }

        function saveDraft() {
            $('#Status').val('Draft');
            $('#requirementForm').submit();
        }
    </script>
}
