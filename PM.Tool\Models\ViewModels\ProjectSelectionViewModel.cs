using PM.Tool.Core.Common;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;

namespace PM.Tool.Models.ViewModels
{
    public class ProjectSelectionViewModel
    {
        public PaginatedList<Project> Projects { get; set; } = new(new List<Project>(), 0, 1, 12);
        public string? SearchTerm { get; set; }
        public ProjectStatus? StatusFilter { get; set; }
        public string? SortBy { get; set; } = "Name";
        public string? SortOrder { get; set; } = "asc";
        public int PageSize { get; set; } = 12;
        
        // Statistics
        public int TotalProjects { get; set; }
        public int ActiveProjects { get; set; }
        public int CompletedProjects { get; set; }
        public int PlanningProjects { get; set; }
        
        // Filter options
        public List<ProjectStatus> AvailableStatuses { get; set; } = new()
        {
            ProjectStatus.Active,
            ProjectStatus.Planning,
            ProjectStatus.Completed,
            ProjectStatus.OnHold,
            ProjectStatus.Cancelled
        };
        
        public List<(string Value, string Text)> SortOptions { get; set; } = new()
        {
            ("Name", "Name"),
            ("CreatedAt", "Created Date"),
            ("StartDate", "Start Date"),
            ("Status", "Status")
        };
        
        public List<(int Value, string Text)> PageSizeOptions { get; set; } = new()
        {
            (6, "6 per page"),
            (12, "12 per page"),
            (24, "24 per page"),
            (48, "48 per page")
        };
    }
}
