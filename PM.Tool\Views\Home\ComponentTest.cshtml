@{
    ViewData["Title"] = "Component Test";
}

<div class="space-y-8">
    <h1 class="text-3xl font-bold text-neutral-900 dark:text-dark-100">Professional Component Library Test</h1>
    
    <!-- Stats Cards Test -->
    <section>
        <h2 class="text-xl font-semibold text-neutral-900 dark:text-dark-100 mb-4">Stats Cards</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            @{
                ViewData["Title"] = "Total Projects";
                ViewData["Value"] = "24";
                ViewData["Icon"] = "fas fa-project-diagram";
                ViewData["IconColor"] = "bg-gradient-to-br from-primary-500 to-primary-600";
                ViewData["Trend"] = "+12%";
                ViewData["TrendIcon"] = "fas fa-arrow-up";
                ViewData["TrendColor"] = "text-success-600 dark:text-success-400";
                ViewData["Progress"] = 75m;
                ViewData["Href"] = "/Projects";
            }
            <partial name="Components/_StatsCard" view-data="ViewData" />
            
            @{
                ViewData["Title"] = "Active Tasks";
                ViewData["Value"] = "156";
                ViewData["Icon"] = "fas fa-tasks";
                ViewData["IconColor"] = "bg-gradient-to-br from-success-500 to-success-600";
                ViewData["Trend"] = "+8%";
                ViewData["TrendIcon"] = "fas fa-arrow-up";
                ViewData["TrendColor"] = "text-success-600 dark:text-success-400";
                ViewData["Progress"] = 60m;
                ViewData["Href"] = "/Tasks";
            }
            <partial name="Components/_StatsCard" view-data="ViewData" />
        </div>
    </section>

    <!-- Loading States Test -->
    <section>
        <h2 class="text-xl font-semibold text-neutral-900 dark:text-dark-100 mb-4">Loading States</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="card-custom">
                <div class="card-header-custom">
                    <h3 class="text-lg font-semibold">Spinner Loading</h3>
                </div>
                <div class="card-body-custom">
                    @{
                        ViewData["Type"] = "spinner";
                        ViewData["Message"] = "Loading data...";
                        ViewData["Size"] = "md";
                    }
                    <partial name="Components/_Loading" view-data="ViewData" />
                </div>
            </div>

            <div class="card-custom">
                <div class="card-header-custom">
                    <h3 class="text-lg font-semibold">Skeleton Loading</h3>
                </div>
                <div class="card-body-custom">
                    @{
                        ViewData["Type"] = "skeleton";
                        ViewData["Lines"] = 3;
                        ViewData["ShowAvatar"] = true;
                        ViewData["ShowButton"] = true;
                    }
                    <partial name="Components/_Loading" view-data="ViewData" />
                </div>
            </div>

            <div class="card-custom">
                <div class="card-header-custom">
                    <h3 class="text-lg font-semibold">Dots Loading</h3>
                </div>
                <div class="card-body-custom">
                    @{
                        ViewData["Type"] = "dots";
                        ViewData["Message"] = "Processing...";
                    }
                    <partial name="Components/_Loading" view-data="ViewData" />
                </div>
            </div>
        </div>
    </section>

    <!-- Chart Test -->
    <section>
        <h2 class="text-xl font-semibold text-neutral-900 dark:text-dark-100 mb-4">Charts</h2>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            @{
                ViewData["Title"] = "Project Progress";
                ViewData["ChartType"] = "bar";
                ViewData["ChartId"] = "testChart1";
                ViewData["Labels"] = new[] { "Jan", "Feb", "Mar", "Apr", "May" };
                ViewData["Data"] = new decimal[] { 10, 20, 30, 40, 50 };
                ViewData["ShowExport"] = true;
                ViewData["Height"] = "300px";
            }
            <partial name="Components/_Chart" view-data="ViewData" />

            @{
                ViewData["Title"] = "Team Performance";
                ViewData["ChartType"] = "line";
                ViewData["ChartId"] = "testChart2";
                ViewData["Labels"] = new[] { "Week 1", "Week 2", "Week 3", "Week 4" };
                ViewData["Data"] = new decimal[] { 65, 75, 80, 85 };
                ViewData["ShowExport"] = false;
                ViewData["Height"] = "300px";
            }
            <partial name="Components/_Chart" view-data="ViewData" />
        </div>
    </section>

    <!-- DataTable Test -->
    <section>
        <h2 class="text-xl font-semibold text-neutral-900 dark:text-dark-100 mb-4">Data Table</h2>
        @{
            ViewData["Title"] = "Sample Projects";
            ViewData["Icon"] = "fas fa-project-diagram";
            ViewData["Headers"] = new[] { "Name", "Status", "Progress", "Actions" };
            ViewData["ShowSearch"] = true;
            ViewData["ShowFilters"] = true;
            ViewData["ShowExport"] = true;
            ViewData["FilterOptions"] = new[] { "Active", "Completed", "On Hold" };
            ViewData["Data"] = new[] { 
                new { Name = "Project Alpha", Status = "Active", Progress = "75%" },
                new { Name = "Project Beta", Status = "Completed", Progress = "100%" },
                new { Name = "Project Gamma", Status = "On Hold", Progress = "45%" }
            };
        }
        <partial name="Components/_DataTable" view-data="ViewData" />
    </section>

    <!-- Breadcrumb Test -->
    <section>
        <h2 class="text-xl font-semibold text-neutral-900 dark:text-dark-100 mb-4">Breadcrumb Navigation</h2>
        @{
            ViewData["Items"] = new[] {
                new { Text = "Dashboard", Href = "/", Icon = "fas fa-home" },
                new { Text = "Projects", Href = "/Projects", Icon = "fas fa-project-diagram" },
                new { Text = "Project Details", Href = "", Icon = "fas fa-info-circle" }
            };
            ViewData["ShowIcons"] = true;
        }
        <partial name="Components/_Breadcrumb" view-data="ViewData" />
    </section>

    <!-- Search Test -->
    <section>
        <h2 class="text-xl font-semibold text-neutral-900 dark:text-dark-100 mb-4">Search Component</h2>
        <div class="max-w-2xl">
            @{
                ViewData["Placeholder"] = "Search projects, tasks, documents...";
                ViewData["ShowFilters"] = true;
                ViewData["ShowSuggestions"] = true;
                ViewData["ShowRecentSearches"] = true;
                ViewData["Categories"] = new[] { "Projects", "Tasks", "Documents", "People" };
                ViewData["Size"] = "lg";
            }
            <partial name="Components/_Search" view-data="ViewData" />
        </div>
    </section>

    <!-- Notification Test -->
    <section>
        <h2 class="text-xl font-semibold text-neutral-900 dark:text-dark-100 mb-4">Notifications</h2>
        <div class="space-y-4">
            <button onclick="showTestNotification('success')" class="btn-primary-custom">Show Success</button>
            <button onclick="showTestNotification('error')" class="btn-danger-custom">Show Error</button>
            <button onclick="showTestNotification('warning')" class="btn-warning-custom">Show Warning</button>
            <button onclick="showTestNotification('info')" class="btn-secondary-custom">Show Info</button>
        </div>
    </section>
</div>

@section Scripts {
    <script>
        function showTestNotification(type) {
            var messages = {
                success: { title: 'Success!', message: 'Your changes have been saved successfully.' },
                error: { title: 'Error!', message: 'Something went wrong. Please try again.' },
                warning: { title: 'Warning!', message: 'Please review your input before proceeding.' },
                info: { title: 'Info', message: 'This is an informational message.' }
            };
            
            if (window.NotificationSystem) {
                window.NotificationSystem[type](messages[type].title, messages[type].message);
            } else {
                alert(messages[type].title + ': ' + messages[type].message);
            }
        }
    </script>
}
