using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities.Agile
{
    public class TestCase : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        [MaxLength(2000)]
        public string? Description { get; set; }

        public int ProjectId { get; set; }
        public int? UserStoryId { get; set; }
        public int? FeatureId { get; set; }

        [MaxLength(50)]
        public string TestCaseKey { get; set; } = string.Empty; // TC-001

        public TestCaseType Type { get; set; } = TestCaseType.Functional;
        public TestCasePriority Priority { get; set; } = TestCasePriority.Medium;
        public TestCaseStatus Status { get; set; } = TestCaseStatus.Draft;

        [MaxLength(1000)]
        public string? PreConditions { get; set; }

        [Required]
        [MaxLength(5000)]
        public string TestSteps { get; set; } = string.Empty;

        [Required]
        [MaxLength(2000)]
        public string ExpectedResult { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? Tags { get; set; } // JSON array

        public string? CreatedByUserId { get; set; }
        public string? AssignedToUserId { get; set; }

        public decimal EstimatedExecutionTime { get; set; } // in minutes

        // Navigation properties
        public virtual Project Project { get; set; } = null!;
        public virtual UserStory? UserStory { get; set; }
        public virtual Feature? Feature { get; set; }
        public virtual ApplicationUser? CreatedBy { get; set; }
        public virtual ApplicationUser? AssignedTo { get; set; }
        public virtual ICollection<TestExecution> TestExecutions { get; set; } = new List<TestExecution>();

        // Computed properties
        public bool IsCompleted => Status == TestCaseStatus.Completed;
        public TestExecutionResult? LastExecutionResult => TestExecutions.OrderByDescending(te => te.ExecutedAt).FirstOrDefault()?.Result;
        public int ExecutionCount => TestExecutions.Count;
        public int PassedExecutions => TestExecutions.Count(te => te.Result == TestExecutionResult.Passed);
        public int FailedExecutions => TestExecutions.Count(te => te.Result == TestExecutionResult.Failed);
        public double PassRate => ExecutionCount > 0 ? (double)PassedExecutions / ExecutionCount * 100 : 0;
    }

    public class TestExecution : BaseEntity
    {
        public int TestCaseId { get; set; }

        public TestExecutionResult Result { get; set; } = TestExecutionResult.NotExecuted;

        [MaxLength(2000)]
        public string? ActualResult { get; set; }

        [MaxLength(2000)]
        public string? Notes { get; set; }

        public string ExecutedByUserId { get; set; } = string.Empty;

        private DateTime _executedAt;
        public DateTime ExecutedAt
        {
            get => _executedAt;
            set => _executedAt = DateTime.SpecifyKind(value, DateTimeKind.Utc);
        }

        public decimal ExecutionTime { get; set; } // in minutes

        public int? BugId { get; set; } // Link to bug if test failed

        // Navigation properties
        public virtual TestCase TestCase { get; set; } = null!;
        public virtual ApplicationUser ExecutedBy { get; set; } = null!;
        public virtual Bug? Bug { get; set; }
    }

    public enum TestCaseType
    {
        Functional = 1,
        Integration = 2,
        Performance = 3,
        Security = 4,
        Usability = 5,
        Regression = 6,
        Smoke = 7,
        Acceptance = 8
    }

    public enum TestCasePriority
    {
        Critical = 1,
        High = 2,
        Medium = 3,
        Low = 4
    }

    public enum TestCaseStatus
    {
        Draft = 1,
        Ready = 2,
        UnderReview = 3,
        Approved = 4,
        Completed = 5,
        Obsolete = 6
    }

    public enum TestExecutionResult
    {
        NotExecuted = 1,
        Passed = 2,
        Failed = 3,
        Blocked = 4,
        Skipped = 5,
        NotApplicable = 6
    }
}
