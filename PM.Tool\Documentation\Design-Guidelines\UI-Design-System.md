# PM.Tool UI Design System & Guidelines

## Overview
This document establishes the design system and user experience guidelines for the PM.Tool application, based on the comprehensive improvements made to the MyTasks module. These guidelines ensure consistency, professionalism, and optimal user experience across all views and components.

## 1. Visual Hierarchy & Layout Principles

### 1.1 Page Structure
```
┌─────────────────────────────────────────┐
│ Page Header (with stats & actions)     │ ← Primary level
├─────────────────────────────────────────┤
│ Filter Bar (compact, functional)       │ ← Secondary level
├─────────────────────────────────────────┤
│ View Controls (minimal space)          │ ← Tertiary level
├─────────────────────────────────────────┤
│ Main Content Area (maximum space)      │ ← Content focus
└─────────────────────────────────────────┘
```

**Key Principles:**
- **Content-first approach**: Main content should consume 70-80% of screen space
- **Progressive disclosure**: Show essential info first, details on demand
- **Horizontal efficiency**: Use inline layouts to minimize vertical space consumption
- **Clear separation**: Use borders, spacing, and background changes to define sections

### 1.2 Information Hierarchy
1. **Primary**: Page title, main actions, critical alerts
2. **Secondary**: Filters, navigation, status indicators
3. **Tertiary**: Metadata, pagination, secondary actions
4. **Supporting**: Help text, timestamps, technical details

## 2. Spacing & Layout System

### 2.1 Spacing Scale
```css
/* Base spacing units (Tailwind-based) */
--space-1: 4px;   /* Micro spacing */
--space-2: 8px;   /* Small spacing */
--space-3: 12px;  /* Medium spacing */
--space-4: 16px;  /* Large spacing */
--space-6: 24px;  /* XL spacing */
--space-8: 32px;  /* XXL spacing */
--space-12: 48px; /* Section spacing */
```

### 2.2 Component Spacing Rules

**Header Sections:**
- Outer margin: `mb-8` (32px)
- Inner padding: `px-8 py-6` (32px horizontal, 24px vertical)
- Element gaps: `space-x-8` (32px between major elements)

**Filter Bars:**
- Outer margin: `mb-8` (32px)
- Inner padding: `px-6 py-5` (24px horizontal, 20px vertical)
- Section gaps: `gap-6` (24px between filter groups)

**Content Areas:**
- Section margins: `mb-6` (24px)
- Content padding: `p-4` to `p-6` (16px-24px)
- Element gaps: `gap-4` (16px standard)

**Form Controls:**
- Input padding: `px-4 py-2.5` (16px horizontal, 10px vertical)
- Button padding: `px-5 py-2.5` (20px horizontal, 10px vertical)
- Control gaps: `gap-3` (12px between related controls)

### 2.3 Icon Spacing Standards
```css
/* Icon-to-text spacing by context */
.badge-icon { margin-right: 8px; }      /* mr-2 */
.button-icon { margin-right: 10px; }    /* mr-2.5 */
.header-icon { margin-right: 12px; }    /* mr-3 */
.list-icon { margin-right: 12px; }      /* mr-3 */
```

## 3. Component Design Patterns

### 3.1 Header Pattern
```html
<!-- Standard page header structure -->
<div class="mb-8 page-header rounded-xl border border-neutral-200 dark:border-neutral-700">
    <div class="flex items-center justify-between px-8 py-6 border-b border-neutral-200 dark:border-neutral-700">
        <div class="flex items-center space-x-8">
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-white tracking-tight">[Page Title]</h1>
            <!-- Inline stats badges -->
            <div class="hidden md:flex items-center space-x-4">
                <!-- Stats badges here -->
            </div>
        </div>
        <div class="flex items-center space-x-4">
            <!-- Action buttons here -->
        </div>
    </div>
</div>
```

**Header Guidelines:**
- Always include page title with consistent typography
- Use inline stats badges for key metrics (max 5 badges)
- Place primary actions on the right
- Hide complex stats on mobile, show essential ones only
- Use semantic colors for different metric types

### 3.2 Stats Badge Pattern
```html
<span class="stats-badge inline-flex items-center px-3 py-1.5 bg-[color]-100 dark:bg-[color]-900/50 text-[color]-800 dark:text-[color]-200 rounded-full text-xs font-medium">
    <i class="fas fa-[icon] mr-2 text-[color]-600 dark:text-[color]-400"></i>
    [Number] [Label]
</span>
```

**Badge Color Coding:**
- **Blue**: General metrics, totals, active items
- **Amber**: Time-sensitive, due dates, warnings
- **Red**: Critical, overdue, errors
- **Emerald**: Success, completion, positive metrics
- **Purple**: Analytics, reports, insights

### 3.3 Filter Bar Pattern
```html
<div class="filter-bar bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl shadow-sm mb-8">
    <form class="px-6 py-5">
        <div class="flex flex-wrap items-center gap-6">
            <!-- Quick filters section -->
            <div class="flex items-center gap-3">
                <span class="text-sm font-semibold text-neutral-700 dark:text-neutral-300 mr-3">Quick:</span>
                <div class="flex items-center gap-2">
                    <!-- Quick filter buttons -->
                </div>
            </div>

            <!-- Separator -->
            <div class="h-8 w-px bg-neutral-300 dark:bg-neutral-600"></div>

            <!-- Main filters -->
            <div class="flex items-center gap-4 flex-1">
                <!-- Search and filter controls -->
            </div>

            <!-- Actions -->
            <div class="flex items-center gap-3 ml-auto">
                <!-- Apply/Clear buttons -->
            </div>
        </div>
    </form>
</div>
```

### 3.4 Quick Filter Button Pattern
```html
<button type="button" class="quick-filter-btn px-4 py-2 text-sm font-medium rounded-lg border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-1" data-filter="[value]">
    <i class="fas fa-[icon] mr-2 text-[color]-500"></i>[Label]
</button>
```

**Quick Filter Guidelines:**
- Use semantic icons and colors
- Keep labels short (1-2 words max)
- Provide visual feedback for active states
- Group related filters together
- Maximum 5-6 quick filters per section

### 3.5 View Controls Pattern
```html
<div class="flex items-center justify-between py-4 mb-6 border-b border-neutral-200 dark:border-neutral-700">
    <div class="flex items-center space-x-5">
        <span class="text-sm font-semibold text-neutral-700 dark:text-neutral-300">View:</span>
        <div class="flex rounded-lg border border-neutral-300 dark:border-neutral-600 bg-neutral-50 dark:bg-neutral-800 p-1">
            <!-- View toggle buttons -->
        </div>
    </div>
    <div class="flex items-center space-x-6">
        <!-- Results info and bulk actions -->
    </div>
</div>
```

## 4. Typography System

### 4.1 Font Scale
```css
/* Typography hierarchy */
.text-3xl { font-size: 30px; line-height: 36px; } /* Page titles */
.text-2xl { font-size: 24px; line-height: 32px; } /* Section titles */
.text-xl  { font-size: 20px; line-height: 28px; } /* Subsection titles */
.text-lg  { font-size: 18px; line-height: 28px; } /* Large body text */
.text-base{ font-size: 16px; line-height: 24px; } /* Body text */
.text-sm  { font-size: 14px; line-height: 20px; } /* Small text */
.text-xs  { font-size: 12px; line-height: 16px; } /* Micro text */
```

### 4.2 Font Weight Usage
- **font-bold (700)**: Page titles, important numbers
- **font-semibold (600)**: Section headers, button text, labels
- **font-medium (500)**: Body text, form labels
- **font-normal (400)**: Secondary text, descriptions

### 4.3 Text Color Hierarchy
```css
/* Light mode */
--text-primary: #111827;    /* Main content */
--text-secondary: #374151;  /* Supporting content */
--text-tertiary: #6B7280;   /* Metadata, captions */
--text-disabled: #9CA3AF;   /* Disabled states */

/* Dark mode */
--text-primary-dark: #F9FAFB;
--text-secondary-dark: #E5E7EB;
--text-tertiary-dark: #9CA3AF;
--text-disabled-dark: #6B7280;
```

## 5. Color System

### 5.1 Semantic Color Palette
```css
/* Primary brand colors */
--primary-50: #eff6ff;
--primary-600: #2563eb;
--primary-700: #1d4ed8;

/* Semantic colors */
--success: #10b981;   /* Emerald-500 */
--warning: #f59e0b;   /* Amber-500 */
--error: #ef4444;     /* Red-500 */
--info: #3b82f6;      /* Blue-500 */

/* Neutral grays */
--neutral-50: #f9fafb;
--neutral-100: #f3f4f6;
--neutral-200: #e5e7eb;
--neutral-300: #d1d5db;
--neutral-600: #4b5563;
--neutral-700: #374151;
--neutral-800: #1f2937;
--neutral-900: #111827;
```

### 5.2 Color Usage Guidelines

**Status Colors:**
- **Blue**: Active, in-progress, informational
- **Emerald**: Completed, success, positive metrics
- **Amber**: Pending, due soon, warnings
- **Red**: Overdue, errors, critical items
- **Purple**: Analytics, insights, special features

**Background Colors:**
- **White/Neutral-800**: Primary content areas
- **Neutral-50/Neutral-700**: Secondary backgrounds
- **Color-50/Color-900**: Semantic backgrounds for badges/alerts

## 6. Interactive Elements

### 6.1 Enhanced Button System

#### Button Size Standards
```css
/* Large buttons - for primary actions, hero sections */
.btn-large {
    @apply inline-flex items-center px-6 py-3 text-base font-semibold rounded-lg shadow-sm transition-all duration-200 hover:shadow-md hover:-translate-y-0.5;
}

/* Medium buttons - default size for most use cases */
.btn-medium {
    @apply inline-flex items-center px-5 py-2.5 text-sm font-semibold rounded-lg shadow-sm transition-all duration-200 hover:shadow-md hover:-translate-y-0.5;
}

/* Small buttons - for compact layouts, secondary actions */
.btn-small {
    @apply inline-flex items-center px-4 py-2 text-sm font-medium rounded-md shadow-sm transition-all duration-200 hover:shadow-md hover:-translate-y-0.5;
}

/* Extra small buttons - for tight spaces, table actions */
.btn-xs {
    @apply inline-flex items-center px-3 py-1.5 text-xs font-medium rounded transition-all duration-200;
}
```

#### Professional Button Variants
```css
/* Primary button - Modern gradient with enhanced shadows */
.btn-primary {
    @apply text-white font-semibold;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border: 1px solid #2563eb;
    box-shadow: 0 4px 14px 0 rgba(37, 99, 235, 0.25);
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    border-color: #1d4ed8;
    box-shadow: 0 6px 20px 0 rgba(37, 99, 235, 0.4);
    transform: translateY(-2px);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px 0 rgba(37, 99, 235, 0.3);
}

/* Secondary button - Clean with subtle shadows */
.btn-secondary {
    @apply text-neutral-700 dark:text-neutral-200 font-medium bg-white dark:bg-neutral-800;
    border: 1.5px solid #e5e7eb;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);
    transition: all 0.2s ease;
}

.dark .btn-secondary {
    border-color: #374151;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.2);
}

.btn-secondary:hover {
    background: #f9fafb;
    border-color: #d1d5db;
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.dark .btn-secondary:hover {
    background: #374151;
    border-color: #4b5563;
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.3);
}

/* Tertiary/Ghost button - Minimal with hover effects */
.btn-tertiary {
    @apply text-neutral-600 dark:text-neutral-400 font-medium bg-transparent;
    border: 1px solid transparent;
    transition: all 0.2s ease;
}

.btn-tertiary:hover {
    @apply text-neutral-800 dark:text-neutral-200;
    background: rgba(0, 0, 0, 0.04);
    border-color: #e5e7eb;
    transform: translateY(-1px);
}

.dark .btn-tertiary:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: #374151;
}

/* Danger button - Strong visual warning */
.btn-danger {
    @apply text-white font-semibold;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border: 1px solid #dc2626;
    box-shadow: 0 4px 14px 0 rgba(220, 38, 38, 0.25);
    transition: all 0.2s ease;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    border-color: #b91c1c;
    box-shadow: 0 6px 20px 0 rgba(220, 38, 38, 0.4);
    transform: translateY(-2px);
}

/* Success button - Positive action emphasis */
.btn-success {
    @apply text-white font-semibold;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: 1px solid #059669;
    box-shadow: 0 4px 14px 0 rgba(5, 150, 105, 0.25);
    transition: all 0.2s ease;
}

.btn-success:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    border-color: #047857;
    box-shadow: 0 6px 20px 0 rgba(5, 150, 105, 0.4);
    transform: translateY(-2px);
}
```

#### Icon Spacing Standards
```css
/* Icon spacing by button size */
.btn-large .icon { margin-right: 12px; }    /* mr-3 */
.btn-medium .icon { margin-right: 10px; }   /* mr-2.5 */
.btn-small .icon { margin-right: 8px; }     /* mr-2 */
.btn-xs .icon { margin-right: 6px; }        /* mr-1.5 */

/* Icon-only buttons */
.btn-icon-large {
    @apply w-12 h-12 justify-center;
}

.btn-icon-medium {
    @apply w-10 h-10 justify-center;
}

.btn-icon-small {
    @apply w-8 h-8 justify-center;
}

.btn-icon-xs {
    @apply w-6 h-6 justify-center;
}
```

#### Button States
```css
/* Loading state */
.btn-loading {
    @apply opacity-75 cursor-not-allowed;
}

.btn-loading .icon {
    @apply animate-spin;
}

/* Disabled state */
.btn:disabled {
    @apply opacity-50 cursor-not-allowed pointer-events-none;
}

/* Active state */
.btn-active {
    @apply ring-2 ring-primary-500 ring-offset-2;
}
```

### 6.2 Button Usage Guidelines

#### When to Use Each Button Type

**Primary Buttons:**
- Main call-to-action on a page (limit to 1 per section)
- Form submissions (Save, Submit, Create)
- Destructive actions with confirmation (Delete, Remove)
- Navigation to important flows (Get Started, Sign Up)

**Secondary Buttons:**
- Alternative actions (Cancel, Back, Edit)
- Less important actions alongside primary
- Navigation buttons (View Details, Learn More)
- Export/Import functions

**Tertiary Buttons:**
- Subtle actions that don't need emphasis
- Inline actions within content
- Toggle buttons in compact layouts
- Helper actions (Copy, Share, Refresh)

#### Button Sizing Guidelines

**Large Buttons (px-6 py-3):**
- Hero sections and landing pages
- Mobile primary actions
- Important form submissions
- Call-to-action sections

**Medium Buttons (px-5 py-2.5) - Default:**
- Standard page actions
- Form buttons
- Navigation elements
- Most common use case

**Small Buttons (px-4 py-2):**
- Table row actions
- Compact layouts
- Secondary actions in tight spaces
- Filter and sort controls

**Extra Small Buttons (px-3 py-1.5):**
- Dense data tables
- Inline editing actions
- Tag-like interactive elements
- Minimal space requirements

#### Icon Guidelines for Buttons

**Icon Selection:**
- Use icons that clearly represent the action
- Maintain consistency across similar actions
- Prefer solid icons for buttons (fas fa-*)
- Use outline icons sparingly for subtle actions

**Icon Spacing by Button Size:**
```css
/* Large buttons */
.btn-large i { margin-right: 12px; }

/* Medium buttons */
.btn-medium i { margin-right: 10px; }

/* Small buttons */
.btn-small i { margin-right: 8px; }

/* Extra small buttons */
.btn-xs i { margin-right: 6px; }
```

**Icon-Only Buttons:**
- Always include descriptive title attribute
- Use for space-constrained layouts
- Maintain minimum 44px touch targets on mobile
- Consider accessibility for screen readers

#### Button Grouping

**Horizontal Groups:**
```html
<div class="flex items-center space-x-3">
    <button class="btn-primary btn-medium">Primary Action</button>
    <button class="btn-secondary btn-medium">Secondary Action</button>
    <button class="btn-tertiary btn-medium">Tertiary Action</button>
</div>
```

**Segmented Controls:**
```html
<div class="inline-flex rounded-lg shadow-sm" role="group">
    <button class="btn-segment-left">Option 1</button>
    <button class="btn-segment-middle">Option 2</button>
    <button class="btn-segment-right">Option 3</button>
</div>
```

### 6.3 Form Controls
```css
/* Standard input */
.form-input {
    @apply px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200;
}

/* Select dropdown */
.form-select {
    @apply px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200;
}
```

### 6.3 Hover & Focus States
- **Hover**: Subtle lift effect (`transform: translateY(-1px)`)
- **Focus**: 2px ring with primary color
- **Active**: Pressed state (`transform: translateY(0)`)
- **Disabled**: Reduced opacity (0.5) and no interactions

## 7. Animation & Transitions

### 7.1 Standard Transitions
```css
/* Default transition for most elements */
.transition-default {
    transition: all 0.2s ease;
}

/* Hover effects */
.hover-lift:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Focus animations */
.focus-ring:focus {
    ring: 2px;
    ring-color: var(--primary-500);
    ring-offset: 2px;
}
```

### 7.2 Special Effects
```css
/* Shimmer effect for active states */
@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.shimmer::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: shimmer 2s infinite;
}
```

## 8. Responsive Design Guidelines

### 8.1 Breakpoint Strategy
```css
/* Mobile first approach */
.mobile-first {
    /* Base styles for mobile */
}

@media (min-width: 768px) {
    /* Tablet styles */
}

@media (min-width: 1024px) {
    /* Desktop styles */
}

@media (min-width: 1280px) {
    /* Large desktop styles */
}
```

### 8.2 Responsive Patterns

**Header Adaptation:**
- Mobile: Stack title and actions, hide non-essential stats
- Tablet: Show essential stats, compact actions
- Desktop: Full layout with all elements

**Filter Adaptation:**
- Mobile: Vertical stack, essential filters only
- Tablet: Horizontal with wrapping
- Desktop: Single row, all filters visible

**Content Adaptation:**
- Mobile: Single column, full width
- Tablet: Flexible columns
- Desktop: Multi-column with optimal spacing

## 9. Accessibility Guidelines

### 9.1 Touch Targets
- Minimum 44px touch targets for mobile
- Adequate spacing between interactive elements (8px minimum)
- Clear visual separation for focus indication

### 9.2 Color Contrast
- Text contrast ratio: 4.5:1 minimum
- Interactive element contrast: 3:1 minimum
- Focus indicators: High contrast with background

### 9.3 Keyboard Navigation
- Logical tab order
- Visible focus indicators
- Skip links for main content
- Escape key support for modals/dropdowns

## 10. Performance Guidelines

### 10.1 CSS Optimization
- Use CSS transforms for animations (GPU acceleration)
- Minimize repaints with proper layering
- Optimize transition timing (200ms standard)

### 10.2 Image & Icon Guidelines
- Use SVG icons when possible
- Optimize icon sizes (14px, 16px, 20px, 24px)
- Consistent icon style (FontAwesome or similar)

## 11. Implementation Checklist

### 11.1 New Page/View Checklist
- [ ] Header follows standard pattern with title and stats
- [ ] Filter bar uses compact horizontal layout
- [ ] View controls minimize vertical space
- [ ] Main content area gets maximum space allocation
- [ ] Consistent spacing scale applied throughout
- [ ] Icon spacing follows standards (mr-2, mr-2.5, mr-3)
- [ ] Color usage follows semantic guidelines
- [ ] Typography hierarchy properly implemented
- [ ] Responsive behavior tested on all breakpoints
- [ ] Accessibility requirements met
- [ ] Hover/focus states implemented
- [ ] Loading and error states designed

### 11.2 Component Review Criteria
- **Space Efficiency**: Does it minimize vertical space consumption?
- **Visual Hierarchy**: Is the information hierarchy clear?
- **Consistency**: Does it follow established patterns?
- **Accessibility**: Are all a11y requirements met?
- **Responsiveness**: Does it work on all screen sizes?
- **Performance**: Are animations smooth and efficient?

## 12. Examples & References

### 12.1 Inspiration Sources
- **Azure DevOps**: Professional enterprise interface
- **GitHub**: Clean, developer-focused design
- **Linear**: Modern, efficient task management
- **Notion**: Flexible, content-first approach

### 12.2 Anti-Patterns to Avoid
- ❌ Large stats cards consuming main content space
- ❌ Vertical filter layouts on desktop
- ❌ Inconsistent icon spacing
- ❌ Poor contrast ratios
- ❌ Cramped touch targets on mobile
- ❌ Excessive animation or visual noise
- ❌ Inconsistent spacing scales

## 13. Future Guidelines

This design system will be expanded with additional guidelines for:

- **Data Visualization**: Charts, graphs, and analytics components
- **Form Design**: Complex forms, validation, and input patterns
- **Navigation**: Menu systems, breadcrumbs, and wayfinding
- **Content Management**: Tables, lists, and data presentation
- **Notification Systems**: Alerts, toasts, and status messages
- **Modal & Overlay**: Dialogs, popovers, and contextual interfaces

---

**Document Information:**
- **Version**: 1.0
- **Last Updated**: Based on MyTasks module improvements
- **Next Review**: After next major component implementation

**Note**: This design system should be treated as a living document, updated as new patterns emerge and user feedback is incorporated. All new components should be reviewed against these guidelines to ensure consistency and quality across the PM.Tool application.
