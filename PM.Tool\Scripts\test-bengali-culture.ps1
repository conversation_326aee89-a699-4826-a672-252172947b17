# PowerShell script to test Bengali (Bangladesh) culture support
# This script verifies that the Bengali culture is properly configured

Write-Host "Testing Bengali (Bangladesh) Culture Support..." -ForegroundColor Green
Write-Host ""

# Test 1: Check if resource file exists
$bengaliResourceFile = "PM.Tool/Resources/SharedResources.bn-BD.resx"
if (Test-Path $bengaliResourceFile) {
    Write-Host "✅ Bengali resource file exists: $bengaliResourceFile" -ForegroundColor Green
    
    # Count keys in Bengali resource file
    try {
        [xml]$bengaliContent = Get-Content $bengaliResourceFile -Encoding UTF8
        $bengaliKeys = $bengaliContent.root.data | ForEach-Object { $_.name }
        Write-Host "   - Contains $($bengaliKeys.Count) translation keys" -ForegroundColor Cyan
    }
    catch {
        Write-Host "   - Error reading Bengali resource file: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Bengali resource file not found: $bengaliResourceFile" -ForegroundColor Red
}

# Test 2: Check if culture is in Program.cs
$programFile = "PM.Tool/Program.cs"
if (Test-Path $programFile) {
    $programContent = Get-Content $programFile -Raw
    if ($programContent -match 'new CultureInfo\("bn-BD"\)') {
        Write-Host "✅ Bengali culture found in Program.cs configuration" -ForegroundColor Green
    } else {
        Write-Host "❌ Bengali culture not found in Program.cs configuration" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Program.cs file not found" -ForegroundColor Red
}

# Test 3: Check if culture is in LocalizationService
$localizationServiceFile = "PM.Tool/Infrastructure/Localization/LocalizationService.cs"
if (Test-Path $localizationServiceFile) {
    $localizationContent = Get-Content $localizationServiceFile -Raw
    if ($localizationContent -match 'new CultureInfo\("bn-BD"\)') {
        Write-Host "✅ Bengali culture found in LocalizationService" -ForegroundColor Green
    } else {
        Write-Host "❌ Bengali culture not found in LocalizationService" -ForegroundColor Red
    }
} else {
    Write-Host "❌ LocalizationService.cs file not found" -ForegroundColor Red
}

# Test 4: Check if culture is in HomeController
$homeControllerFile = "PM.Tool/Controllers/HomeController.cs"
if (Test-Path $homeControllerFile) {
    $homeControllerContent = Get-Content $homeControllerFile -Raw
    if ($homeControllerContent -match '"bn-BD"') {
        Write-Host "✅ Bengali culture found in HomeController supported cultures" -ForegroundColor Green
    } else {
        Write-Host "❌ Bengali culture not found in HomeController supported cultures" -ForegroundColor Red
    }
} else {
    Write-Host "❌ HomeController.cs file not found" -ForegroundColor Red
}

# Test 5: Check if Bengali is in the dropdown
$topbarFile = "PM.Tool/Views/Shared/_Topbar.cshtml"
if (Test-Path $topbarFile) {
    $topbarContent = Get-Content $topbarFile -Raw
    if ($topbarContent -match 'culture = "bn-BD"') {
        Write-Host "✅ Bengali culture found in language dropdown" -ForegroundColor Green
    } else {
        Write-Host "❌ Bengali culture not found in language dropdown" -ForegroundColor Red
    }
} else {
    Write-Host "❌ _Topbar.cshtml file not found" -ForegroundColor Red
}

# Test 6: Check if Bengali is in test localization page
$testLocalizationFile = "PM.Tool/Views/Home/TestLocalization.cshtml"
if (Test-Path $testLocalizationFile) {
    $testLocalizationContent = Get-Content $testLocalizationFile -Raw
    if ($testLocalizationContent -match 'culture = "bn-BD"') {
        Write-Host "✅ Bengali culture found in test localization page" -ForegroundColor Green
    } else {
        Write-Host "❌ Bengali culture not found in test localization page" -ForegroundColor Red
    }
} else {
    Write-Host "❌ TestLocalization.cshtml file not found" -ForegroundColor Red
}

Write-Host ""

# Test 7: Sample Bengali translations
Write-Host "Sample Bengali Translations:" -ForegroundColor Yellow
if (Test-Path $bengaliResourceFile) {
    try {
        [xml]$bengaliContent = Get-Content $bengaliResourceFile -Encoding UTF8
        $sampleKeys = @("Common.Save", "Common.Cancel", "Nav.Dashboard", "Nav.Projects", "WBS.TaskCreatedSuccessfully")
        
        foreach ($key in $sampleKeys) {
            $translation = ($bengaliContent.root.data | Where-Object { $_.name -eq $key }).value
            if ($translation) {
                Write-Host "  $key = $translation" -ForegroundColor Cyan
            } else {
                Write-Host "  $key = [NOT FOUND]" -ForegroundColor Red
            }
        }
    }
    catch {
        Write-Host "  Error reading translations: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""

# Test 8: Check .NET culture support
Write-Host "Testing .NET Culture Support:" -ForegroundColor Yellow
try {
    # This will test if .NET recognizes the culture
    $culture = [System.Globalization.CultureInfo]::new("bn-BD")
    Write-Host "✅ .NET recognizes bn-BD culture" -ForegroundColor Green
    Write-Host "   - Display Name: $($culture.DisplayName)" -ForegroundColor Cyan
    Write-Host "   - English Name: $($culture.EnglishName)" -ForegroundColor Cyan
    Write-Host "   - Native Name: $($culture.NativeName)" -ForegroundColor Cyan
    Write-Host "   - Two Letter ISO: $($culture.TwoLetterISOLanguageName)" -ForegroundColor Cyan
}
catch {
    Write-Host "❌ .NET does not recognize bn-BD culture: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Bengali (Bangladesh) Culture Test Completed!" -ForegroundColor Green

# Summary
Write-Host ""
Write-Host "=== SUMMARY ===" -ForegroundColor Magenta
Write-Host "Bengali (Bangladesh) culture support has been added to:" -ForegroundColor White
Write-Host "• Resource files (SharedResources.bn-BD.resx)" -ForegroundColor Green
Write-Host "• Application configuration (Program.cs)" -ForegroundColor Green
Write-Host "• Localization service" -ForegroundColor Green
Write-Host "• Language dropdown in UI" -ForegroundColor Green
Write-Host "• Test localization page" -ForegroundColor Green
Write-Host "• Controller validation" -ForegroundColor Green
Write-Host ""
Write-Host "The Bengali culture is now fully integrated and ready to use!" -ForegroundColor Green
