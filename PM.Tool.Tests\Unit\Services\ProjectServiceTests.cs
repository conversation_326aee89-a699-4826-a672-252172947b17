using Microsoft.EntityFrameworkCore;
using Moq;
using PM.Tool.Application.Services;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Specifications;
using PM.Tool.Data;
using PM.Tool.Tests.Helpers;
using Xunit;
using FluentAssertions;

namespace PM.Tool.Tests.Unit.Services
{
    public class ProjectServiceTests : IDisposable
    {
        private readonly ApplicationDbContext _context;
        private readonly Mock<IProjectRepository> _mockProjectRepository;
        private readonly ProjectService _projectService;
        private readonly ApplicationUser _testUser;

        public ProjectServiceTests()
        {
            _context = TestDbContextFactory.CreateInMemoryContext();
            _mockProjectRepository = MockHelper.CreateMockProjectRepository();
            _projectService = new ProjectService(_mockProjectRepository.Object, _context);

            _testUser = new ApplicationUser
            {
                Id = "test-user-1",
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Test",
                LastName = "User",
                IsActive = true
            };
        }

        [Fact]
        public async Task GetAllProjectsAsync_ReturnsAllProjects()
        {
            // Arrange
            var projects = new List<Project>
            {
                new Project { Id = 1, Name = "Project 1", Status = ProjectStatus.Active },
                new Project { Id = 2, Name = "Project 2", Status = ProjectStatus.Planning }
            };

            _mockProjectRepository.Setup(x => x.GetAllAsync())
                .ReturnsAsync(projects);

            // Act
            var result = await _projectService.GetAllProjectsAsync();

            // Assert
            result.Should().HaveCount(2);
            result.Should().Contain(p => p.Name == "Project 1");
            result.Should().Contain(p => p.Name == "Project 2");
        }

        [Fact]
        public async Task GetUserProjectsAsync_ReturnsUserProjects()
        {
            // Arrange
            var userId = "test-user-1";
            var projects = new List<Project>
            {
                new Project { Id = 1, Name = "User Project 1", CreatedByUserId = userId },
                new Project { Id = 2, Name = "User Project 2", CreatedByUserId = userId }
            };

            _mockProjectRepository.Setup(x => x.FindWithSpecificationAsync(It.IsAny<ProjectsByUserSpec>()))
                .ReturnsAsync(projects);

            // Act
            var result = await _projectService.GetUserProjectsAsync(userId);

            // Assert
            result.Should().HaveCount(2);
            result.Should().Contain(p => p.Name == "User Project 1");
            result.Should().Contain(p => p.Name == "User Project 2");
        }

        [Fact]
        public async Task GetProjectByIdAsync_WithValidId_ReturnsProject()
        {
            // Arrange
            var projectId = 1;
            var project = new Project { Id = projectId, Name = "Test Project" };

            _mockProjectRepository.Setup(x => x.GetByIdAsync(projectId))
                .ReturnsAsync(project);

            // Act
            var result = await _projectService.GetProjectByIdAsync(projectId);

            // Assert
            result.Should().NotBeNull();
            result.Id.Should().Be(projectId);
            result.Name.Should().Be("Test Project");
        }

        [Fact]
        public async Task GetProjectByIdAsync_WithInvalidId_ReturnsNull()
        {
            // Arrange
            var projectId = 999;
            _mockProjectRepository.Setup(x => x.GetByIdAsync(projectId))
                .ReturnsAsync((Project)null);

            // Act
            var result = await _projectService.GetProjectByIdAsync(projectId);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task GetProjectWithDetailsAsync_WithValidId_ReturnsProjectWithDetails()
        {
            // Arrange
            var projectId = 1;
            var project = new Project
            {
                Id = projectId,
                Name = "Test Project",
                Members = new List<ProjectMember>(),
                Tasks = new List<TaskEntity>()
            };

            _mockProjectRepository.Setup(x => x.GetProjectWithMembersAsync(projectId))
                .ReturnsAsync(project);

            // Act
            var result = await _projectService.GetProjectWithDetailsAsync(projectId);

            // Assert
            result.Should().NotBeNull();
            result.Id.Should().Be(projectId);
            result.Members.Should().NotBeNull();
            result.Tasks.Should().NotBeNull();
        }

        [Fact]
        public async Task CreateProjectAsync_WithValidProject_CreatesProjectAndAddsMember()
        {
            // Arrange
            var project = new Project
            {
                Name = "New Project",
                Description = "New Description",
                ManagerId = _testUser.Id,
                CreatedByUserId = _testUser.Id,
                Status = ProjectStatus.Planning
            };

            var createdProject = new Project
            {
                Id = 1,
                Name = project.Name,
                Description = project.Description,
                ManagerId = project.ManagerId,
                CreatedByUserId = project.CreatedByUserId,
                Status = project.Status
            };

            _mockProjectRepository.Setup(x => x.AddAsync(It.IsAny<Project>()))
                .ReturnsAsync(createdProject);
            _mockProjectRepository.Setup(x => x.SaveChangesAsync())
                .ReturnsAsync(true);

            // Act
            var result = await _projectService.CreateProjectAsync(project);

            // Assert
            result.Should().NotBeNull();
            result.Id.Should().Be(1);
            result.Name.Should().Be("New Project");
            result.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
            result.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));

            // Verify that a project member was added
            var projectMember = await _context.ProjectMembers
                .FirstOrDefaultAsync(pm => pm.ProjectId == createdProject.Id && pm.UserId == _testUser.Id);
            projectMember.Should().NotBeNull();
            projectMember!.Role.Should().Be(UserRole.ProjectManager);
        }

        [Fact]
        public async Task UpdateProjectAsync_WithValidProject_UpdatesProject()
        {
            // Arrange
            var project = new Project
            {
                Id = 1,
                Name = "Updated Project",
                Description = "Updated Description",
                Status = ProjectStatus.Active
            };

            _mockProjectRepository.Setup(x => x.UpdateAsync(It.IsAny<Project>()))
                .Returns(Task.CompletedTask);
            _mockProjectRepository.Setup(x => x.SaveChangesAsync())
                .ReturnsAsync(true);

            // Act
            var result = await _projectService.UpdateProjectAsync(project);

            // Assert
            result.Should().NotBeNull();
            result.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
            _mockProjectRepository.Verify(x => x.UpdateAsync(project), Times.Once);
            _mockProjectRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        }

        [Fact]
        public async Task DeleteProjectAsync_WithValidId_DeletesProject()
        {
            // Arrange
            var projectId = 1;
            var project = new Project { Id = projectId, Name = "Test Project" };

            _mockProjectRepository.Setup(x => x.GetByIdAsync(projectId))
                .ReturnsAsync(project);
            _mockProjectRepository.Setup(x => x.DeleteAsync(project))
                .Returns(Task.CompletedTask);
            _mockProjectRepository.Setup(x => x.SaveChangesAsync())
                .ReturnsAsync(true);

            // Act
            var result = await _projectService.DeleteProjectAsync(projectId);

            // Assert
            result.Should().BeTrue();
            _mockProjectRepository.Verify(x => x.DeleteAsync(project), Times.Once);
            _mockProjectRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        }

        [Fact]
        public async Task DeleteProjectAsync_WithInvalidId_ReturnsFalse()
        {
            // Arrange
            var projectId = 999;
            _mockProjectRepository.Setup(x => x.GetByIdAsync(projectId))
                .ReturnsAsync((Project)null);

            // Act
            var result = await _projectService.DeleteProjectAsync(projectId);

            // Assert
            result.Should().BeFalse();
            _mockProjectRepository.Verify(x => x.DeleteAsync(It.IsAny<Project>()), Times.Never);
        }

        [Fact]
        public async Task AddMemberToProjectAsync_WithNewMember_AddsMember()
        {
            // Arrange
            var projectId = 1;
            var userId = "new-user-id";
            var role = UserRole.TeamMember;

            // Act
            var result = await _projectService.AddMemberToProjectAsync(projectId, userId, role);

            // Assert
            result.Should().BeTrue();
            var member = await _context.ProjectMembers
                .FirstOrDefaultAsync(pm => pm.ProjectId == projectId && pm.UserId == userId);
            member.Should().NotBeNull();
            member!.Role.Should().Be(role);
            member.IsActive.Should().BeTrue();
            member.JoinedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
        }

        [Fact]
        public async Task AddMemberToProjectAsync_WithExistingMember_UpdatesRole()
        {
            // Arrange
            var projectId = 1;
            var userId = "existing-user-id";
            var existingMember = new ProjectMember
            {
                ProjectId = projectId,
                UserId = userId,
                Role = UserRole.TeamMember,
                IsActive = false,
                JoinedAt = DateTime.UtcNow.AddDays(-10)
            };

            _context.ProjectMembers.Add(existingMember);
            await _context.SaveChangesAsync();

            // Act
            var result = await _projectService.AddMemberToProjectAsync(projectId, userId, UserRole.ProjectManager);

            // Assert
            result.Should().BeTrue();
            var updatedMember = await _context.ProjectMembers
                .FirstOrDefaultAsync(pm => pm.ProjectId == projectId && pm.UserId == userId);
            updatedMember.Should().NotBeNull();
            updatedMember!.Role.Should().Be(UserRole.ProjectManager);
            updatedMember.IsActive.Should().BeTrue();
        }

        [Fact]
        public async Task GetProjectMembersAsync_ReturnsActiveMembers()
        {
            // Arrange
            var projectId = 1;
            var activeUser = new ApplicationUser { Id = "active-user", FirstName = "Active", LastName = "User" };
            var inactiveUser = new ApplicationUser { Id = "inactive-user", FirstName = "Inactive", LastName = "User" };

            _context.Users.AddRange(activeUser, inactiveUser);

            var members = new List<ProjectMember>
            {
                new ProjectMember { ProjectId = projectId, UserId = activeUser.Id, IsActive = true, Role = UserRole.TeamMember },
                new ProjectMember { ProjectId = projectId, UserId = inactiveUser.Id, IsActive = false, Role = UserRole.TeamMember }
            };

            _context.ProjectMembers.AddRange(members);
            await _context.SaveChangesAsync();

            // Act
            var result = await _projectService.GetProjectMembersAsync(projectId);

            // Assert
            result.Should().HaveCount(1);
            result.First().UserId.Should().Be(activeUser.Id);
            result.First().IsActive.Should().BeTrue();
        }

        [Fact]
        public async Task GetUserRoleInProjectAsync_WithValidMember_ReturnsRole()
        {
            // Arrange
            var projectId = 1;
            var userId = "test-user-id";
            var member = new ProjectMember
            {
                ProjectId = projectId,
                UserId = userId,
                Role = UserRole.ProjectManager,
                IsActive = true
            };

            _context.ProjectMembers.Add(member);
            await _context.SaveChangesAsync();

            // Act
            var result = await _projectService.GetUserRoleInProjectAsync(projectId, userId);

            // Assert
            result.Should().Be(UserRole.ProjectManager);
        }

        [Fact]
        public async Task GetUserRoleInProjectAsync_WithInvalidMember_ReturnsNull()
        {
            // Arrange
            var projectId = 1;
            var userId = "non-existent-user";

            // Act
            var result = await _projectService.GetUserRoleInProjectAsync(projectId, userId);

            // Assert
            result.Should().BeNull();
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}
