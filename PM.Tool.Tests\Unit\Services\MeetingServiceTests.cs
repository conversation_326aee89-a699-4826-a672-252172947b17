using FluentAssertions;
using Moq;
using PM.Tool.Application.Services;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Interfaces;
using PM.Tool.Tests.Helpers;
using Xunit;

namespace PM.Tool.Tests.Unit.Services
{
    /// <summary>
    /// Unit tests for Meeting entities focusing on business logic validation.
    /// For data access integration tests, see MeetingServiceIntegrationTests.
    /// </summary>
    public class MeetingServiceTests
    {
        // Note: For comprehensive service testing, use MeetingServiceIntegrationTests
        // These unit tests focus on entity business logic validation only

        [Fact]
        public void Meeting_Creation_SetsCorrectDefaults()
        {
            // Arrange & Act
            var meeting = MockHelper.CreateMockMeeting();

            // Assert
            meeting.Should().NotBeNull();
            meeting.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(1));
            meeting.Status.Should().Be(MeetingStatus.Scheduled);
            meeting.DurationMinutes.Should().Be<PERSON>reater<PERSON>han(0);
        }

        [Fact]
        public void Meeting_EndTime_CalculatedCorrectly()
        {
            // Arrange
            var meeting = MockHelper.CreateMockMeeting();
            meeting.ScheduledDate = new DateTime(2024, 1, 15, 10, 0, 0);
            meeting.DurationMinutes = 90;

            // Act
            var endTime = meeting.EndTime;

            // Assert
            endTime.Should().Be(new DateTime(2024, 1, 15, 11, 30, 0));
        }

        [Fact]
        public void Meeting_IsOverdue_WhenPastScheduledTime()
        {
            // Arrange
            var meeting = MockHelper.CreateMockMeeting();
            meeting.ScheduledDate = DateTime.UtcNow.AddHours(-2);
            meeting.Status = MeetingStatus.Scheduled;

            // Act & Assert
            meeting.IsOverdue.Should().BeTrue();
        }

        [Fact]
        public void Meeting_IsNotOverdue_WhenFutureScheduledTime()
        {
            // Arrange
            var meeting = MockHelper.CreateMockMeeting();
            meeting.ScheduledDate = DateTime.UtcNow.AddHours(2);
            meeting.Status = MeetingStatus.Scheduled;

            // Act & Assert
            meeting.IsOverdue.Should().BeFalse();
        }

        [Fact]
        public void Meeting_IsNotOverdue_WhenCompleted()
        {
            // Arrange
            var meeting = MockHelper.CreateMockMeeting();
            meeting.ScheduledDate = DateTime.UtcNow.AddHours(-2);
            meeting.Status = MeetingStatus.Completed;

            // Act & Assert
            meeting.IsOverdue.Should().BeFalse();
        }

        [Fact]
        public void MeetingAttendee_Creation_SetsCorrectDefaults()
        {
            // Arrange & Act
            var attendee = new MeetingAttendee
            {
                MeetingId = 1,
                UserId = "user123",
                Role = AttendeeRole.Attendee
            };

            // Assert
            attendee.Status.Should().Be(AttendanceStatus.Invited);
            attendee.InvitedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(1));
        }

        [Fact]
        public void MeetingActionItem_IsOverdue_WhenPastDueDate()
        {
            // Arrange
            var actionItem = new MeetingActionItem
            {
                Description = "Test action item",
                DueDate = DateTime.UtcNow.AddDays(-1),
                Status = ActionItemStatus.Open
            };

            // Act & Assert
            actionItem.IsOverdue.Should().BeTrue();
        }

        [Fact]
        public void MeetingActionItem_IsNotOverdue_WhenCompleted()
        {
            // Arrange
            var actionItem = new MeetingActionItem
            {
                Description = "Test action item",
                DueDate = DateTime.UtcNow.AddDays(-1),
                Status = ActionItemStatus.Completed
            };

            // Act & Assert
            actionItem.IsOverdue.Should().BeFalse();
        }

        [Theory]
        [InlineData(MeetingStatus.Scheduled, true)]
        [InlineData(MeetingStatus.InProgress, false)]
        [InlineData(MeetingStatus.Completed, false)]
        [InlineData(MeetingStatus.Cancelled, false)]
        public void Meeting_CanBeStarted_BasedOnStatus(MeetingStatus status, bool canStart)
        {
            // Arrange
            var meeting = MockHelper.CreateMockMeeting();
            meeting.Status = status;

            // Act & Assert
            var actualCanStart = meeting.Status == MeetingStatus.Scheduled;
            actualCanStart.Should().Be(canStart);
        }
    }
}
