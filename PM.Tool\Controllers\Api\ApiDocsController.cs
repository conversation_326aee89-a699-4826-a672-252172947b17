using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PM.Tool.Core.Interfaces;
using PM.Tool.Models.Api;
using System.Reflection;
using Asp.Versioning;

namespace PM.Tool.Controllers.Api
{
    /// <summary>
    /// API controller for API documentation and metadata
    /// </summary>
    [ApiVersion("1.0")]
    [Tags("Documentation")]
    [AllowAnonymous]
    public class ApiDocsController : BaseApiController
    {
        private readonly IConfiguration _configuration;

        public ApiDocsController(
            IConfiguration configuration,
            IAuditService auditService,
            ILogger<ApiDocsController> logger)
            : base(auditService, logger)
        {
            _configuration = configuration;
        }

        /// <summary>
        /// Get API information and available endpoints
        /// </summary>
        /// <returns>API metadata</returns>
        [HttpGet("info")]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
        public IActionResult GetApiInfo()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version?.ToString() ?? "1.0.0";

                var apiInfo = new
                {
                    Name = "PM.Tool API",
                    Version = version,
                    Description = "A comprehensive project management API for PM.Tool",
                    Documentation = new
                    {
                        SwaggerUrl = "/api/docs",
                        OpenApiSpec = "/swagger/v1/swagger.json"
                    },
                    Endpoints = new
                    {
                        Features = "/api/v1/features",
                        Bugs = "/api/v1/bugs",
                        TestCases = "/api/v1/test-cases",
                        Health = "/api/v1/health"
                    },
                    Authentication = new
                    {
                        Type = "Bearer Token",
                        Header = "Authorization: Bearer {token}",
                        TokenEndpoint = "/api/v1/auth/token"
                    },
                    RateLimiting = new
                    {
                        RequestsPerMinute = 1000,
                        RequestsPerHour = 10000
                    },
                    SupportedFormats = new[] { "application/json" },
                    SupportedMethods = new[] { "GET", "POST", "PUT", "DELETE" },
                    Versioning = new
                    {
                        CurrentVersion = "1.0",
                        SupportedVersions = new[] { "1.0" },
                        VersioningScheme = "URL Path",
                        Example = "/api/v1/features"
                    }
                };

                return Ok(Success(apiInfo));
            }
            catch (Exception ex)
            {
                return HandleException(ex, "GetApiInfo");
            }
        }

        /// <summary>
        /// Get API usage statistics and metrics
        /// </summary>
        /// <returns>API usage metrics</returns>
        [HttpGet("metrics")]
        [Authorize]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
        public IActionResult GetApiMetrics()
        {
            try
            {
                // In a real implementation, these would come from monitoring/analytics
                var metrics = new
                {
                    TotalRequests = 125000,
                    RequestsToday = 2500,
                    AverageResponseTime = 150, // milliseconds
                    ErrorRate = 0.5, // percentage
                    TopEndpoints = new[]
                    {
                        new { Endpoint = "/api/v1/features", Requests = 45000, AvgResponseTime = 120 },
                        new { Endpoint = "/api/v1/bugs", Requests = 38000, AvgResponseTime = 140 },
                        new { Endpoint = "/api/v1/test-cases", Requests = 25000, AvgResponseTime = 180 },
                        new { Endpoint = "/api/v1/health", Requests = 17000, AvgResponseTime = 50 }
                    },
                    StatusCodeDistribution = new
                    {
                        Success_2xx = 92.5,
                        ClientError_4xx = 6.0,
                        ServerError_5xx = 1.5
                    },
                    PeakUsageHours = new[] { 9, 10, 11, 14, 15, 16 },
                    LastUpdated = DateTime.UtcNow
                };

                return Ok(Success(metrics));
            }
            catch (Exception ex)
            {
                return HandleException(ex, "GetApiMetrics");
            }
        }

        /// <summary>
        /// Get API changelog and version history
        /// </summary>
        /// <returns>API changelog</returns>
        [HttpGet("changelog")]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
        public IActionResult GetChangelog()
        {
            try
            {
                var changelog = new
                {
                    CurrentVersion = "1.0.0",
                    Versions = new[]
                    {
                        new
                        {
                            Version = "1.0.0",
                            ReleaseDate = "2024-01-15",
                            Type = "Major Release",
                            Changes = new[]
                            {
                                "Initial API release",
                                "Features CRUD operations",
                                "Bugs CRUD operations",
                                "Test Cases CRUD operations",
                                "Health monitoring endpoints",
                                "Comprehensive API documentation",
                                "JWT authentication support",
                                "API versioning implementation"
                            },
                            BreakingChanges = new string[] { },
                            Deprecations = new string[] { }
                        }
                    },
                    UpcomingFeatures = new[]
                    {
                        "Bulk operations support",
                        "Real-time notifications via WebSockets",
                        "Advanced filtering and search",
                        "Export functionality",
                        "Webhook support",
                        "GraphQL endpoint"
                    }
                };

                return Ok(Success(changelog));
            }
            catch (Exception ex)
            {
                return HandleException(ex, "GetChangelog");
            }
        }

        /// <summary>
        /// Get API rate limiting information
        /// </summary>
        /// <returns>Rate limiting details</returns>
        [HttpGet("rate-limits")]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
        public IActionResult GetRateLimits()
        {
            try
            {
                var rateLimits = new
                {
                    Global = new
                    {
                        RequestsPerMinute = 1000,
                        RequestsPerHour = 10000,
                        RequestsPerDay = 100000
                    },
                    PerUser = new
                    {
                        RequestsPerMinute = 100,
                        RequestsPerHour = 1000,
                        RequestsPerDay = 10000
                    },
                    PerEndpoint = new[]
                    {
                        new { Endpoint = "/api/v1/features", RequestsPerMinute = 200 },
                        new { Endpoint = "/api/v1/bugs", RequestsPerMinute = 200 },
                        new { Endpoint = "/api/v1/test-cases", RequestsPerMinute = 150 },
                        new { Endpoint = "/api/v1/health", RequestsPerMinute = 500 }
                    },
                    Headers = new
                    {
                        RemainingRequests = "X-RateLimit-Remaining",
                        LimitReset = "X-RateLimit-Reset",
                        RetryAfter = "Retry-After"
                    },
                    ErrorResponse = new
                    {
                        StatusCode = 429,
                        Message = "Rate limit exceeded",
                        RetryAfter = "60 seconds"
                    }
                };

                return Ok(Success(rateLimits));
            }
            catch (Exception ex)
            {
                return HandleException(ex, "GetRateLimits");
            }
        }

        /// <summary>
        /// Get API error codes and their descriptions
        /// </summary>
        /// <returns>Error code reference</returns>
        [HttpGet("error-codes")]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
        public IActionResult GetErrorCodes()
        {
            try
            {
                var errorCodes = new
                {
                    StandardHttpCodes = new[]
                    {
                        new { Code = 200, Name = "OK", Description = "Request successful" },
                        new { Code = 201, Name = "Created", Description = "Resource created successfully" },
                        new { Code = 400, Name = "Bad Request", Description = "Invalid request data" },
                        new { Code = 401, Name = "Unauthorized", Description = "Authentication required" },
                        new { Code = 403, Name = "Forbidden", Description = "Access denied" },
                        new { Code = 404, Name = "Not Found", Description = "Resource not found" },
                        new { Code = 409, Name = "Conflict", Description = "Resource conflict" },
                        new { Code = 422, Name = "Unprocessable Entity", Description = "Validation failed" },
                        new { Code = 429, Name = "Too Many Requests", Description = "Rate limit exceeded" },
                        new { Code = 500, Name = "Internal Server Error", Description = "Server error occurred" }
                    },
                    CustomErrorCodes = new[]
                    {
                        new { Code = "VALIDATION_ERROR", Description = "One or more validation errors occurred" },
                        new { Code = "NOT_FOUND", Description = "Requested resource was not found" },
                        new { Code = "GENERAL_ERROR", Description = "A general error occurred" },
                        new { Code = "INTERNAL_ERROR", Description = "An internal server error occurred" },
                        new { Code = "PROJECT_ACCESS_DENIED", Description = "Access to project denied" },
                        new { Code = "FEATURE_NOT_FOUND", Description = "Feature not found" },
                        new { Code = "BUG_NOT_FOUND", Description = "Bug not found" },
                        new { Code = "TESTCASE_NOT_FOUND", Description = "Test case not found" }
                    },
                    ErrorResponseFormat = new
                    {
                        Success = false,
                        Message = "Error description",
                        Error = new
                        {
                            Code = "ERROR_CODE",
                            Message = "Detailed error message",
                            Details = "Additional error details",
                            TraceId = "unique-trace-identifier"
                        },
                        Timestamp = DateTime.UtcNow
                    }
                };

                return Ok(Success(errorCodes));
            }
            catch (Exception ex)
            {
                return HandleException(ex, "GetErrorCodes");
            }
        }

        /// <summary>
        /// Get API examples and sample requests
        /// </summary>
        /// <returns>API usage examples</returns>
        [HttpGet("examples")]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
        public IActionResult GetExamples()
        {
            try
            {
                var examples = new
                {
                    Authentication = new
                    {
                        Description = "All API endpoints require authentication via Bearer token",
                        HeaderExample = "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                        CurlExample = "curl -H \"Authorization: Bearer {token}\" https://api.pmtool.com/api/v1/features"
                    },
                    Features = new
                    {
                        GetAll = new
                        {
                            Method = "GET",
                            Url = "/api/v1/projects/1/features?page=1&pageSize=20&search=login",
                            Description = "Get paginated list of features with optional filtering"
                        },
                        GetById = new
                        {
                            Method = "GET",
                            Url = "/api/v1/features/123",
                            Description = "Get specific feature by ID"
                        },
                        Create = new
                        {
                            Method = "POST",
                            Url = "/api/v1/features",
                            ContentType = "application/json",
                            Body = new
                            {
                                Title = "User Authentication",
                                Description = "Implement user login and registration",
                                Status = "Draft",
                                Priority = "High",
                                ProjectId = 1
                            }
                        }
                    },
                    Pagination = new
                    {
                        Description = "All list endpoints support pagination",
                        Parameters = new
                        {
                            Page = "Page number (1-based)",
                            PageSize = "Items per page (1-100)",
                            Search = "Search term",
                            SortBy = "Field to sort by",
                            SortOrder = "asc or desc"
                        },
                        ResponseFormat = new
                        {
                            Data = "Array of items",
                            Pagination = new
                            {
                                Page = 1,
                                PageSize = 20,
                                TotalCount = 150,
                                TotalPages = 8,
                                HasNextPage = true,
                                HasPreviousPage = false
                            }
                        }
                    }
                };

                return Ok(Success(examples));
            }
            catch (Exception ex)
            {
                return HandleException(ex, "GetExamples");
            }
        }
    }
}
