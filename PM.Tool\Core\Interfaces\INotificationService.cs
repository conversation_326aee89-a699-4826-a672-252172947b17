using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;

namespace PM.Tool.Core.Interfaces
{
    public interface INotificationService
    {
        Task<Notification> CreateNotificationAsync(string userId, string title, string message, NotificationType type, int? projectId = null, int? taskId = null);
        Task<IEnumerable<Notification>> GetUserNotificationsAsync(string userId, bool unreadOnly = false);
        Task<bool> MarkAsReadAsync(int notificationId);
        Task<bool> MarkAllAsReadAsync(string userId);
        Task<int> GetUnreadCountAsync(string userId);
        Task SendEmailNotificationAsync(string userId, string subject, string message);
        Task NotifyTaskAssignedAsync(int taskId, string assignedToUserId);
        Task NotifyTaskUpdatedAsync(int taskId);
        Task NotifyTaskCompletedAsync(int taskId);
        Task NotifyProjectUpdatedAsync(int projectId);
        Task NotifyCommentAddedAsync(int taskId, string commentUserId);
        Task NotifyMilestoneReachedAsync(int milestoneId);
        Task NotifyDeadlineApproachingAsync();
    }
}
