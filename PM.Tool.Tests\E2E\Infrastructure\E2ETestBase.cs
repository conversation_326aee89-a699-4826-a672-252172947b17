using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using PM.Tool.Data;
using PM.Tool.Core.Entities;
using Microsoft.AspNetCore.Identity;
using System.Net.Http;
using System.Text;
using Xunit;
using FluentAssertions;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using System.Text.Encodings.Web;

namespace PM.Tool.Tests.E2E.Infrastructure
{
    public class E2ETestBase : IClassFixture<E2EWebApplicationFactory>, IDisposable
    {
        protected readonly E2EWebApplicationFactory _factory;
        protected readonly HttpClient _client;
        protected readonly ApplicationUser _testUser;

        public E2ETestBase(E2EWebApplicationFactory factory)
        {
            _factory = factory;
            _client = _factory.CreateClient(new WebApplicationFactoryClientOptions
            {
                AllowAutoRedirect = false // We want to control redirects in tests
            });

            _testUser = new ApplicationUser
            {
                Id = "e2e-test-user",
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "E2E",
                LastName = "Test",
                IsActive = true
            };
        }

        protected async Task<string> LoginAsync()
        {
            // Get login page
            var loginResponse = await _client.GetAsync("/Account/Login");
            loginResponse.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var loginContent = await loginResponse.Content.ReadAsStringAsync();

            // Extract anti-forgery token
            var tokenMatch = Regex.Match(loginContent, @"<input name=""__RequestVerificationToken"" type=""hidden"" value=""([^""]+)""");
            var token = tokenMatch.Success ? tokenMatch.Groups[1].Value : "";

            // Simulate login (this would normally go through authentication)
            var loginData = new Dictionary<string, string>
            {
                ["Email"] = _testUser.Email,
                ["Password"] = "TestPassword123!",
                ["__RequestVerificationToken"] = token
            };

            var loginFormContent = new FormUrlEncodedContent(loginData);
            var loginResult = await _client.PostAsync("/Account/Login", loginFormContent);

            // For E2E tests, we'll simulate successful authentication
            // In a real scenario, this would set authentication cookies
            return "authenticated";
        }

        protected async Task<(string content, string token)> GetPageWithTokenAsync(string url)
        {
            var response = await _client.GetAsync(url);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var content = await response.Content.ReadAsStringAsync();

            // Extract anti-forgery token
            var tokenMatch = Regex.Match(content, @"<input name=""__RequestVerificationToken"" type=""hidden"" value=""([^""]+)""");
            var token = tokenMatch.Success ? tokenMatch.Groups[1].Value : "";

            return (content, token);
        }

        protected async Task<HttpResponseMessage> PostFormAsync(string url, Dictionary<string, string> formData, string token = "")
        {
            if (!string.IsNullOrEmpty(token))
            {
                formData["__RequestVerificationToken"] = token;
            }

            var formContent = new FormUrlEncodedContent(formData);
            return await _client.PostAsync(url, formContent);
        }

        protected void AssertPageContains(string content, params string[] expectedTexts)
        {
            foreach (var expectedText in expectedTexts)
            {
                content.Should().Contain(expectedText, $"Page should contain '{expectedText}'");
            }
        }

        protected void AssertPageDoesNotContain(string content, params string[] unexpectedTexts)
        {
            foreach (var unexpectedText in unexpectedTexts)
            {
                content.Should().NotContain(unexpectedText, $"Page should not contain '{unexpectedText}'");
            }
        }

        protected void AssertRedirectTo(HttpResponseMessage response, string expectedLocation)
        {
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.Redirect);
            response.Headers.Location?.ToString().Should().Contain(expectedLocation);
        }

        protected void AssertSuccessResponse(HttpResponseMessage response)
        {
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
        }

        protected void AssertNotFoundResponse(HttpResponseMessage response)
        {
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.NotFound);
        }

        public virtual void Dispose()
        {
            _client?.Dispose();
        }
    }

    public class E2EWebApplicationFactory : WebApplicationFactory<Program>, IAsyncLifetime
    {
        protected override void ConfigureWebHost(IWebHostBuilder builder)
        {
            builder.UseEnvironment("E2ETesting");

            builder.ConfigureServices(services =>
            {
                // Configure for E2E testing - use minimal setup to avoid conflicts
                services.Configure<LoggerFilterOptions>(options =>
                {
                    options.MinLevel = Microsoft.Extensions.Logging.LogLevel.Error;
                });

                // For E2E tests, we'll use a test authentication scheme that bypasses real authentication
                services.AddAuthentication("Test")
                    .AddScheme<Microsoft.AspNetCore.Authentication.AuthenticationSchemeOptions, TestAuthenticationHandler>(
                        "Test", options => { });
            });
        }

        public async Task InitializeAsync()
        {
            // Initialize any E2E test setup
            await Task.CompletedTask;
        }

        public new async Task DisposeAsync()
        {
            // Clean up E2E test resources
            await Task.CompletedTask;
        }
    }

    [CollectionDefinition("E2E")]
    public class E2ETestCollection : ICollectionFixture<E2EWebApplicationFactory>
    {
        // This class has no code, and is never created. Its purpose is simply
        // to be the place to apply [CollectionDefinition] and all the
        // ICollectionFixture<> interfaces.
    }

    public class TestAuthenticationHandler : AuthenticationHandler<AuthenticationSchemeOptions>
    {
        public TestAuthenticationHandler(IOptionsMonitor<AuthenticationSchemeOptions> options,
            ILoggerFactory logger, UrlEncoder encoder)
            : base(options, logger, encoder)
        {
        }

        protected override Task<AuthenticateResult> HandleAuthenticateAsync()
        {
            var claims = new[]
            {
                new Claim(ClaimTypes.Name, "E2E Test User"),
                new Claim(ClaimTypes.NameIdentifier, "e2e-test-user"),
                new Claim(ClaimTypes.Email, "<EMAIL>"),
                new Claim(ClaimTypes.Role, "Admin")
            };

            var identity = new ClaimsIdentity(claims, "Test");
            var principal = new ClaimsPrincipal(identity);
            var ticket = new AuthenticationTicket(principal, "Test");

            return Task.FromResult(AuthenticateResult.Success(ticket));
        }
    }
}
