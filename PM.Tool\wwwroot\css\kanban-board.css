/* Modern Kanban Board Styles */

.kanban-board {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--bg-secondary);
    font-family: var(--font-family-sans);
}

.kanban-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    border-bottom: 1px solid var(--gray-200);
    box-shadow: var(--shadow-sm);
}

.kanban-title {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.kanban-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.kanban-columns {
    display: flex;
    flex: 1;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    overflow-x: auto;
    overflow-y: hidden;
}

.kanban-column {
    flex: 0 0 320px;
    display: flex;
    flex-direction: column;
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    max-height: calc(100vh - 120px);
}

.kanban-column-header {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--gray-200);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.kanban-column-title {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.kanban-column-count {
    background: var(--gray-200);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    font-weight: 500;
    min-width: 24px;
    text-align: center;
}

.kanban-column-body {
    flex: 1;
    padding: var(--spacing-md);
    overflow-y: auto;
    transition: background-color var(--transition-fast);
}

.kanban-column-body.drag-over {
    background-color: var(--gray-50);
    border: 2px dashed var(--primary-color);
}

.kanban-cards-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    min-height: 100px;
}

.kanban-card {
    background: var(--bg-primary);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
    position: relative;
    animation: fadeIn 0.3s ease-in-out;
}

.kanban-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
    border-color: var(--primary-color);
}

.kanban-card.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
    z-index: 1000;
}

.kanban-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.kanban-card-id {
    font-size: var(--text-xs);
    color: var(--text-muted);
    font-weight: 500;
    font-family: var(--font-family-mono);
    background: var(--gray-100);
    padding: 2px 6px;
    border-radius: var(--radius-sm);
}

.kanban-card-priority {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    font-size: var(--text-xs);
}

.priority-critical {
    background: var(--error-color);
    color: white;
}

.priority-high {
    background: var(--warning-color);
    color: white;
}

.priority-medium {
    background: var(--info-color);
    color: white;
}

.priority-low {
    background: var(--success-color);
    color: white;
}

.kanban-card-content {
    margin-bottom: var(--spacing-md);
}

.kanban-card-title {
    font-size: var(--text-base);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-xs) 0;
    line-height: var(--leading-tight);
}

.kanban-card-description {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    line-height: var(--leading-normal);
    margin: 0;
}

.kanban-card-footer {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.kanban-card-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.kanban-card-assignee {
    display: flex;
    align-items: center;
}

.kanban-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 2px solid var(--bg-primary);
    box-shadow: var(--shadow-sm);
}

.kanban-story-points,
.kanban-due-date {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: var(--text-xs);
    color: var(--text-muted);
    background: var(--gray-100);
    padding: 2px 6px;
    border-radius: var(--radius-sm);
}

.kanban-due-date.due-soon {
    background: var(--warning-color);
    color: white;
}

.kanban-card-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.kanban-tag {
    font-size: var(--text-xs);
    background: var(--primary-color);
    color: white;
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-weight: 500;
}

.kanban-tag-more {
    font-size: var(--text-xs);
    background: var(--gray-300);
    color: var(--text-secondary);
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-weight: 500;
}

.kanban-add-card {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-md);
    color: var(--text-muted);
    cursor: pointer;
    transition: all var(--transition-fast);
    margin-top: var(--spacing-sm);
    font-size: var(--text-sm);
}

.kanban-add-card:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: var(--gray-50);
}

/* Loading States */
.kanban-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    color: var(--text-muted);
    z-index: 1000;
}

/* Toast Notifications */
.kanban-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    color: white;
    font-weight: 500;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
    box-shadow: var(--shadow-lg);
}

.kanban-toast.show {
    transform: translateX(0);
}

.toast-success {
    background: var(--success-color);
}

.toast-error {
    background: var(--error-color);
}

.toast-warning {
    background: var(--warning-color);
}

.toast-info {
    background: var(--info-color);
}

/* Status-based Card Styling */
.status-backlog {
    border-left: 4px solid var(--gray-400);
}

.status-ready {
    border-left: 4px solid var(--info-color);
}

.status-in-progress {
    border-left: 4px solid var(--warning-color);
}

.status-review {
    border-left: 4px solid var(--accent-color);
}

.status-done {
    border-left: 4px solid var(--success-color);
    opacity: 0.8;
}

.status-cancelled {
    border-left: 4px solid var(--error-color);
    opacity: 0.6;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .kanban-columns {
        padding: var(--spacing-md);
        gap: var(--spacing-sm);
    }
    
    .kanban-column {
        flex: 0 0 280px;
    }
}

@media (max-width: 768px) {
    .kanban-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }
    
    .kanban-controls {
        justify-content: center;
    }
    
    .kanban-columns {
        flex-direction: column;
        overflow-x: hidden;
        overflow-y: auto;
    }
    
    .kanban-column {
        flex: none;
        max-height: 400px;
    }
    
    .kanban-board {
        height: auto;
        min-height: 100vh;
    }
}

/* RTL Support */
[dir="rtl"] .kanban-columns {
    direction: rtl;
}

[dir="rtl"] .kanban-card-header {
    flex-direction: row-reverse;
}

[dir="rtl"] .kanban-card-meta {
    flex-direction: row-reverse;
}

[dir="rtl"] .kanban-toast {
    right: auto;
    left: 20px;
    transform: translateX(-100%);
}

[dir="rtl"] .kanban-toast.show {
    transform: translateX(0);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .kanban-column-count {
        background: var(--gray-700);
        color: var(--gray-300);
    }
    
    .kanban-card-id {
        background: var(--gray-700);
        color: var(--gray-300);
    }
    
    .kanban-story-points,
    .kanban-due-date {
        background: var(--gray-700);
        color: var(--gray-300);
    }
    
    .kanban-add-card {
        border-color: var(--gray-600);
        color: var(--gray-400);
    }
    
    .kanban-add-card:hover {
        background: var(--gray-800);
    }
}

/* Accessibility */
.kanban-card:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.kanban-add-card:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .kanban-card {
        border-width: 2px;
    }
    
    .kanban-card:hover {
        border-width: 3px;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .kanban-card,
    .kanban-toast,
    .kanban-column-body {
        transition: none;
    }
    
    .kanban-card {
        animation: none;
    }
}

/* Print Styles */
@media print {
    .kanban-header,
    .kanban-add-card {
        display: none;
    }
    
    .kanban-columns {
        flex-direction: column;
        gap: var(--spacing-lg);
    }
    
    .kanban-column {
        break-inside: avoid;
        max-height: none;
    }
    
    .kanban-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }
}
