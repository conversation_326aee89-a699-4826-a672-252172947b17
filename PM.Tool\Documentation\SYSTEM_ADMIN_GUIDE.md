# PM Tool - System Administrator Guide

## 🔧 System Administration Overview

This guide provides comprehensive instructions for system administrators responsible for installing, configuring, maintaining, and troubleshooting the PM Tool system.

## 📋 Prerequisites

### System Requirements
- **Operating System**: Windows Server 2019+, Linux (Ubuntu 20.04+, CentOS 8+), macOS 10.15+
- **Runtime**: .NET 9.0 Runtime and SDK
- **Database**: PostgreSQL 12+, SQL Server 2019+, or SQLite 3.35+
- **Web Server**: IIS 10+, Nginx 1.18+, or Apache 2.4+
- **Memory**: Minimum 4GB RAM (8GB+ recommended)
- **Storage**: Minimum 20GB (100GB+ recommended for production)

### Required Software
```bash
# Install .NET 9.0 SDK
wget https://dot.net/v1/dotnet-install.sh
chmod +x dotnet-install.sh
./dotnet-install.sh --version 9.0.0

# Install PostgreSQL (Ubuntu/Debian)
sudo apt update
sudo apt install postgresql postgresql-contrib

# Install Node.js (for frontend build tools)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

## 🚀 Installation & Deployment

### 1. Database Setup

#### PostgreSQL Configuration
```sql
-- Create database and user
CREATE DATABASE pmtool_db;
CREATE USER pmtool_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE pmtool_db TO pmtool_user;

-- Configure connection settings
-- Edit postgresql.conf
listen_addresses = '*'
max_connections = 200
shared_buffers = 256MB
effective_cache_size = 1GB
```

#### Connection String Configuration
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=pmtool_db;Username=pmtool_user;Password=your_secure_password;SSL Mode=Require;"
  }
}
```

### 2. Application Deployment

#### Option A: Docker Deployment (Recommended)
```dockerfile
# Use the provided Dockerfile
docker build -t pmtool:latest .

# Run with Docker Compose
version: '3.8'
services:
  pmtool:
    image: pmtool:latest
    ports:
      - "80:8080"
      - "443:8443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=Host=db;Database=pmtool_db;Username=pmtool_user;Password=your_secure_password
    depends_on:
      - db
  
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: pmtool_db
      POSTGRES_USER: pmtool_user
      POSTGRES_PASSWORD: your_secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
```

#### Option B: Traditional Deployment
```bash
# Clone and build the application
git clone <repository-url>
cd PM.Tool
dotnet restore
dotnet build --configuration Release
dotnet publish --configuration Release --output ./publish

# Copy to web server directory
sudo cp -r ./publish/* /var/www/pmtool/

# Set permissions
sudo chown -R www-data:www-data /var/www/pmtool/
sudo chmod -R 755 /var/www/pmtool/
```

### 3. Web Server Configuration

#### Nginx Configuration
```nginx
server {
    listen 80;
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection keep-alive;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Static files
    location /css/ {
        alias /var/www/pmtool/wwwroot/css/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /js/ {
        alias /var/www/pmtool/wwwroot/js/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

#### IIS Configuration (Windows)
```xml
<!-- web.config -->
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="dotnet" 
                  arguments=".\PM.Tool.dll" 
                  stdoutLogEnabled="false" 
                  stdoutLogFile=".\logs\stdout" 
                  hostingModel="inprocess" />
    </system.webServer>
  </location>
</configuration>
```

## ⚙️ Configuration Management

### 1. Application Settings

#### Production Configuration (appsettings.Production.json)
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore": "Warning"
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=pmtool_db;Username=pmtool_user;Password=your_secure_password"
  },
  "JwtSettings": {
    "SecretKey": "your-256-bit-secret-key-here",
    "Issuer": "PMTool",
    "Audience": "PMToolUsers",
    "ExpirationMinutes": 60
  },
  "EmailSettings": {
    "SmtpServer": "smtp.your-domain.com",
    "SmtpPort": 587,
    "SmtpUsername": "<EMAIL>",
    "SmtpPassword": "your-email-password",
    "FromEmail": "<EMAIL>",
    "FromName": "PM Tool"
  },
  "FileStorage": {
    "Provider": "Local", // Local, Azure, AWS, Google
    "LocalPath": "/var/www/pmtool/uploads",
    "MaxFileSize": 52428800, // 50MB
    "AllowedExtensions": [".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".jpg", ".jpeg", ".png", ".gif"]
  },
  "CacheSettings": {
    "Provider": "Memory", // Memory, Redis
    "RedisConnectionString": "localhost:6379",
    "DefaultExpirationMinutes": 60
  },
  "IntegrationSettings": {
    "GitHub": {
      "ClientId": "your-github-client-id",
      "ClientSecret": "your-github-client-secret"
    },
    "Slack": {
      "ClientId": "your-slack-client-id",
      "ClientSecret": "your-slack-client-secret"
    }
  }
}
```

### 2. Environment Variables
```bash
# Set environment variables for production
export ASPNETCORE_ENVIRONMENT=Production
export ConnectionStrings__DefaultConnection="Host=localhost;Database=pmtool_db;Username=pmtool_user;Password=your_secure_password"
export JwtSettings__SecretKey="your-256-bit-secret-key-here"
export EmailSettings__SmtpPassword="your-email-password"
```

### 3. Feature Flags
```json
{
  "FeatureFlags": {
    "EnableAgileFeatures": true,
    "EnableMeetingManagement": true,
    "EnableRequirementsManagement": true,
    "EnableAdvancedAnalytics": true,
    "EnableIntegrations": true,
    "EnableMultiLanguage": true,
    "EnableDarkMode": true,
    "EnableOfflineMode": false
  }
}
```

## 🔐 Security Configuration

### 1. SSL/TLS Setup
```bash
# Generate SSL certificate (Let's Encrypt)
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com

# Or use existing certificate
sudo cp your-certificate.crt /etc/ssl/certs/
sudo cp your-private-key.key /etc/ssl/private/
sudo chmod 600 /etc/ssl/private/your-private-key.key
```

### 2. Security Headers
```csharp
// Add to Program.cs
app.Use(async (context, next) =>
{
    context.Response.Headers.Add("X-Content-Type-Options", "nosniff");
    context.Response.Headers.Add("X-Frame-Options", "DENY");
    context.Response.Headers.Add("X-XSS-Protection", "1; mode=block");
    context.Response.Headers.Add("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
    context.Response.Headers.Add("Content-Security-Policy", "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'");
    await next();
});
```

### 3. Database Security
```sql
-- Create read-only user for reporting
CREATE USER pmtool_readonly WITH PASSWORD 'readonly_password';
GRANT CONNECT ON DATABASE pmtool_db TO pmtool_readonly;
GRANT USAGE ON SCHEMA public TO pmtool_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO pmtool_readonly;

-- Enable row-level security
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
CREATE POLICY project_access_policy ON projects
    FOR ALL TO pmtool_user
    USING (true); -- Implement your access logic here
```

## 📊 Monitoring & Logging

### 1. Application Logging
```json
{
  "Serilog": {
    "Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"],
    "MinimumLevel": "Information",
    "WriteTo": [
      {
        "Name": "Console",
        "Args": {
          "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "/var/log/pmtool/log-.txt",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 30,
          "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"
        }
      }
    ]
  }
}
```

### 2. Health Checks
```csharp
// Add to Program.cs
builder.Services.AddHealthChecks()
    .AddDbContextCheck<ApplicationDbContext>()
    .AddUrlGroup(new Uri("https://api.github.com"), "GitHub API")
    .AddCheck("disk-space", () => 
    {
        var drive = new DriveInfo("/");
        var freeSpaceGB = drive.AvailableFreeSpace / (1024 * 1024 * 1024);
        return freeSpaceGB > 5 ? HealthCheckResult.Healthy() : HealthCheckResult.Unhealthy();
    });

app.MapHealthChecks("/health");
```

### 3. Performance Monitoring
```bash
# Install monitoring tools
sudo apt install htop iotop nethogs

# Monitor application performance
dotnet-counters monitor --process-id <pid> --counters System.Runtime,Microsoft.AspNetCore.Hosting

# Database monitoring
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"
```

## 🔄 Backup & Recovery

### 1. Database Backup
```bash
#!/bin/bash
# backup-database.sh
BACKUP_DIR="/var/backups/pmtool"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="pmtool_backup_$DATE.sql"

mkdir -p $BACKUP_DIR
pg_dump -h localhost -U pmtool_user -d pmtool_db > $BACKUP_DIR/$BACKUP_FILE
gzip $BACKUP_DIR/$BACKUP_FILE

# Keep only last 30 days of backups
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete

echo "Backup completed: $BACKUP_FILE.gz"
```

### 2. Application Backup
```bash
#!/bin/bash
# backup-application.sh
APP_DIR="/var/www/pmtool"
BACKUP_DIR="/var/backups/pmtool"
DATE=$(date +%Y%m%d_%H%M%S)

tar -czf $BACKUP_DIR/app_backup_$DATE.tar.gz -C $APP_DIR .
find $BACKUP_DIR -name "app_backup_*.tar.gz" -mtime +7 -delete

echo "Application backup completed: app_backup_$DATE.tar.gz"
```

### 3. Automated Backup Schedule
```bash
# Add to crontab (crontab -e)
# Daily database backup at 2 AM
0 2 * * * /usr/local/bin/backup-database.sh

# Weekly application backup on Sundays at 3 AM
0 3 * * 0 /usr/local/bin/backup-application.sh

# Monthly full system backup
0 4 1 * * /usr/local/bin/full-backup.sh
```

## 🔧 Maintenance Tasks

### 1. Database Maintenance
```sql
-- Weekly maintenance script
-- Analyze tables for query optimization
ANALYZE;

-- Vacuum to reclaim space
VACUUM ANALYZE;

-- Reindex for performance
REINDEX DATABASE pmtool_db;

-- Clean up old audit logs (older than 1 year)
DELETE FROM audit_logs WHERE created_at < NOW() - INTERVAL '1 year';

-- Clean up old notifications (older than 3 months)
DELETE FROM notifications WHERE created_at < NOW() - INTERVAL '3 months' AND is_read = true;
```

### 2. Application Maintenance
```bash
#!/bin/bash
# maintenance.sh

# Clear temporary files
find /tmp -name "pmtool_*" -mtime +1 -delete

# Clear application logs older than 30 days
find /var/log/pmtool -name "*.txt" -mtime +30 -delete

# Clear uploaded files in temp directory
find /var/www/pmtool/uploads/temp -mtime +1 -delete

# Restart application if memory usage is high
MEMORY_USAGE=$(ps -o pid,ppid,cmd,%mem --sort=-%mem | grep PM.Tool | head -1 | awk '{print $4}' | cut -d. -f1)
if [ "$MEMORY_USAGE" -gt 80 ]; then
    echo "High memory usage detected. Restarting application..."
    sudo systemctl restart pmtool
fi
```

### 3. Security Updates
```bash
#!/bin/bash
# security-updates.sh

# Update system packages
sudo apt update && sudo apt upgrade -y

# Update .NET runtime
sudo apt update && sudo apt install -y dotnet-runtime-9.0

# Check for application updates
cd /var/www/pmtool
git fetch origin
if [ $(git rev-list HEAD...origin/main --count) != 0 ]; then
    echo "Updates available. Please review and deploy manually."
fi
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Application Won't Start
```bash
# Check logs
sudo journalctl -u pmtool -f

# Check configuration
dotnet PM.Tool.dll --environment Production --urls http://localhost:5000

# Verify database connection
psql -h localhost -U pmtool_user -d pmtool_db -c "SELECT 1;"
```

#### 2. High Memory Usage
```bash
# Monitor memory usage
dotnet-counters monitor --process-id <pid> --counters System.Runtime

# Check for memory leaks
dotnet-dump collect -p <pid>
dotnet-dump analyze <dump-file>
```

#### 3. Database Performance Issues
```sql
-- Check slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- Check database connections
SELECT count(*) FROM pg_stat_activity;

-- Check table sizes
SELECT schemaname,tablename,attname,n_distinct,correlation 
FROM pg_stats 
WHERE schemaname = 'public' 
ORDER BY n_distinct DESC;
```

#### 4. Integration Issues
```bash
# Test GitHub integration
curl -H "Authorization: token YOUR_TOKEN" https://api.github.com/user

# Test email configuration
echo "Test email" | mail -s "Test Subject" <EMAIL>

# Check SSL certificates
openssl s_client -connect your-domain.com:443 -servername your-domain.com
```

## 📞 Support & Escalation

### Log Collection
```bash
# Collect system information
uname -a > system-info.txt
df -h >> system-info.txt
free -h >> system-info.txt
ps aux | grep PM.Tool >> system-info.txt

# Collect application logs
tar -czf logs.tar.gz /var/log/pmtool/

# Collect configuration (remove sensitive data)
cp appsettings.Production.json config-sanitized.json
# Remove passwords and secrets from config-sanitized.json
```

### Performance Baseline
```bash
# Establish performance baseline
ab -n 1000 -c 10 http://localhost/
wrk -t12 -c400 -d30s http://localhost/

# Database performance
pgbench -i pmtool_db
pgbench -c 10 -j 2 -t 1000 pmtool_db
```

---

*This guide covers the essential system administration tasks for PM Tool. For additional support, please refer to the troubleshooting section or contact the development team.*
