@{
    ViewData["Title"] = "Project Analytics";
    var project = ViewBag.Project as PM.Tool.Core.Entities.Project;
}

@if (project == null)
{
    <div class="alert-danger-custom">
        <i class="fas fa-exclamation-triangle text-danger-600 dark:text-danger-400"></i>
        <div>
            <p class="font-medium">Project Not Found</p>
            <p class="text-sm">The requested project could not be found or you don't have access to it.</p>
        </div>
    </div>
    return;
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-chart-line mr-3 text-primary-600 dark:text-primary-400"></i>
                @project.Name Analytics
            </h1>
            <p class="mt-2 text-lg text-neutral-600 dark:text-dark-300">
                Detailed insights and performance metrics for this project
            </p>
        </div>
        <div class="flex space-x-3">
            @{
                ViewData["Text"] = "Back to Dashboard";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Index");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Project Overview Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
    <!-- Total Tasks -->
    <div class="stats-card-custom">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
                <i class="fas fa-tasks text-white text-lg"></i>
            </div>
            <div class="ml-4">
                <p class="text-2xl font-bold text-neutral-900 dark:text-dark-100">@project.TotalTasks</p>
                <p class="text-sm text-neutral-500 dark:text-dark-400">Total Tasks</p>
            </div>
        </div>
        <div class="mt-4">
            <div class="flex items-center text-xs text-neutral-500 dark:text-dark-400">
                <i class="fas fa-list mr-1"></i>
                All project tasks
            </div>
        </div>
    </div>

    <!-- Completed Tasks -->
    <div class="stats-card-custom">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-success-500 to-success-600 rounded-xl flex items-center justify-center">
                <i class="fas fa-check-circle text-white text-lg"></i>
            </div>
            <div class="ml-4">
                <p class="text-2xl font-bold text-neutral-900 dark:text-dark-100">@project.CompletedTasks</p>
                <p class="text-sm text-neutral-500 dark:text-dark-400">Completed</p>
            </div>
        </div>
        <div class="mt-4">
            <div class="flex items-center text-xs text-neutral-500 dark:text-dark-400">
                <i class="fas fa-check mr-1"></i>
                Successfully finished
            </div>
        </div>
    </div>

    <!-- Remaining Tasks -->
    <div class="stats-card-custom">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-warning-500 to-warning-600 rounded-xl flex items-center justify-center">
                <i class="fas fa-clock text-white text-lg"></i>
            </div>
            <div class="ml-4">
                <p class="text-2xl font-bold text-neutral-900 dark:text-dark-100">@(project.TotalTasks - project.CompletedTasks)</p>
                <p class="text-sm text-neutral-500 dark:text-dark-400">Remaining</p>
            </div>
        </div>
        <div class="mt-4">
            <div class="flex items-center text-xs text-neutral-500 dark:text-dark-400">
                <i class="fas fa-hourglass-half mr-1"></i>
                Still in progress
            </div>
        </div>
    </div>

    <!-- Progress Percentage -->
    <div class="stats-card-custom">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-info-500 to-info-600 rounded-xl flex items-center justify-center">
                <i class="fas fa-percentage text-white text-lg"></i>
            </div>
            <div class="ml-4">
                <p class="text-2xl font-bold text-neutral-900 dark:text-dark-100">@project.ProgressPercentage.ToString("F0")%</p>
                <p class="text-sm text-neutral-500 dark:text-dark-400">Progress</p>
            </div>
        </div>
        <div class="mt-4">
            @{
                var progressClass = project.ProgressPercentage >= 80 ? "bg-success-500" :
                                  project.ProgressPercentage >= 60 ? "bg-info-500" :
                                  project.ProgressPercentage >= 30 ? "bg-warning-500" : "bg-danger-500";
            }
            <div class="w-full bg-neutral-200 dark:bg-dark-600 rounded-full h-2">
                <div class="@progressClass h-2 rounded-full transition-all duration-300" style="width: @project.ProgressPercentage%"></div>
            </div>
        </div>
    </div>
</div>

<!-- Project Details -->
<div class="row">
    <div class="col-md-8">
        <div class="card card-modern">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Project Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-semibold">Status</label>
                            <div>
                                <span class="badge badge-modern bg-@(project.Status == PM.Tool.Core.Enums.ProjectStatus.Active ? "success" : project.Status == PM.Tool.Core.Enums.ProjectStatus.Completed ? "primary" : "warning")">
                                    @project.Status
                                </span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-semibold">Start Date</label>
                            <div>@project.StartDate.ToString("MMM dd, yyyy")</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-semibold">End Date</label>
                            <div>@(project.EndDate?.ToString("MMM dd, yyyy") ?? "Not set")</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-semibold">Budget</label>
                            <div>@(project.Budget.ToString("C") ?? "Not set")</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-semibold">Client</label>
                            <div>@(project.ClientName ?? "Not specified")</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-semibold">Team Size</label>
                            <div>@(project.Members?.Count ?? 0) members</div>
                        </div>
                    </div>
                </div>

                @if (!string.IsNullOrEmpty(project.Description))
                {
                    <div class="mt-3">
                        <label class="form-label fw-semibold">Description</label>
                        <div class="text-muted">@project.Description</div>
                    </div>
                }
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card card-modern">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    Progress Overview
                </h5>
            </div>
            <div class="card-body text-center">
                <div class="position-relative d-inline-block mb-3">
                    <canvas id="progressChart" width="150" height="150"></canvas>
                    <div class="position-absolute top-50 start-50 translate-middle">
                        <h3 class="mb-0">@project.ProgressPercentage.ToString("F0")%</h3>
                        <small class="text-muted">Complete</small>
                    </div>
                </div>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h6 class="mb-1">@project.CompletedTasks</h6>
                            <small class="text-success">Completed</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h6 class="mb-1">@(project.TotalTasks - project.CompletedTasks)</h6>
                        <small class="text-warning">Remaining</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card card-modern">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="@Url.Action("Details", "Projects", new { id = project.Id })" class="btn btn-modern btn-outline-primary w-100 mb-2">
                            <i class="fas fa-eye me-2"></i>
                            View Project Details
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="@Url.Action("Index", "Tasks", new { projectId = project.Id })" class="btn btn-modern btn-outline-primary w-100 mb-2">
                            <i class="fas fa-tasks me-2"></i>
                            View Tasks
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="@Url.Action("Burndown", new { projectId = project.Id })" class="btn btn-modern btn-outline-primary w-100 mb-2">
                            <i class="fas fa-chart-line me-2"></i>
                            Burndown Chart
                        </a>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-modern btn-outline-primary w-100 mb-2" onclick="exportProjectData(@project.Id)">
                            <i class="fas fa-download me-2"></i>
                            Export Data
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Create progress chart if Chart.js is available
            if (typeof Chart !== 'undefined') {
                try {
                    const ctx = document.getElementById('progressChart').getContext('2d');
                    new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            datasets: [{
                                data: [@project.ProgressPercentage, @(100 - project.ProgressPercentage)],
                                backgroundColor: ['#8b5cf6', '#e5e7eb'],
                                borderWidth: 0
                            }]
                        },
                        options: {
                            responsive: false,
                            maintainAspectRatio: false,
                            cutout: '70%',
                            plugins: {
                                legend: {
                                    display: false
                                }
                            }
                        }
                    });
                } catch (error) {
                    console.log('Chart initialization error:', error);
                }
            }

            // Add smooth animations to stats cards
            $('.stats-card').each(function(index) {
                $(this).delay(index * 100).queue(function() {
                    $(this).addClass('fade-in').dequeue();
                });
            });
        });

        function exportProjectData(projectId) {
            // Create a form and submit it to trigger the download
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '@Url.Action("ExportData")';

            const projectIdInput = document.createElement('input');
            projectIdInput.type = 'hidden';
            projectIdInput.name = 'projectId';
            projectIdInput.value = projectId;

            const formatInput = document.createElement('input');
            formatInput.type = 'hidden';
            formatInput.name = 'format';
            formatInput.value = 'csv';

            form.appendChild(projectIdInput);
            form.appendChild(formatInput);
            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }
    </script>
}
