using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Specifications;
using PM.Tool.Data;

namespace PM.Tool.Application.Services
{
    public class ProjectService : IProjectService
    {
        private readonly IProjectRepository _projectRepository;
        private readonly ApplicationDbContext _context;

        public ProjectService(IProjectRepository projectRepository, ApplicationDbContext context)
        {
            _projectRepository = projectRepository;
            _context = context;
        }

        public async Task<IEnumerable<Project>> GetAllProjectsAsync()
        {
            return await _projectRepository.GetAllAsync();
        }

        public async Task<IEnumerable<Project>> GetUserProjectsAsync(string userId)
        {
            // Use a specification that includes all necessary related entities
            var spec = new ProjectsByUserSpec(userId);
            return await _projectRepository.FindWithSpecificationAsync(spec);
        }

        public async Task<Project?> GetProjectByIdAsync(int id)
        {
            return await _projectRepository.GetByIdAsync(id);
        }

        public async Task<Project?> GetProjectWithDetailsAsync(int id)
        {
            return await _projectRepository.GetProjectWithMembersAsync(id);
        }

        public async Task<Project> CreateProjectAsync(Project project)
        {
            project.CreatedAt = DateTime.UtcNow;
            project.UpdatedAt = DateTime.UtcNow;

            // Use a transaction to ensure data consistency (if supported)
            var isInMemory = _context.Database.ProviderName == "Microsoft.EntityFrameworkCore.InMemory";

            if (isInMemory)
            {
                // For InMemory provider, just execute without transaction
                var createdProject = await _projectRepository.AddAsync(project);
                await _projectRepository.SaveChangesAsync();

                // Now add the manager as a project member with ProjectManager role
                var projectMember = new ProjectMember
                {
                    ProjectId = createdProject.Id,
                    UserId = project.ManagerId,
                    Role = UserRole.ProjectManager,
                    JoinedAt = DateTime.UtcNow,
                    IsActive = true
                };

                _context.ProjectMembers.Add(projectMember);
                await _context.SaveChangesAsync();

                return createdProject;
            }
            else
            {
                // For real databases, use transaction
                using var transaction = await _context.Database.BeginTransactionAsync();
                try
                {
                    // Create and save the project first
                    var createdProject = await _projectRepository.AddAsync(project);
                    await _projectRepository.SaveChangesAsync();

                    // Now add the manager as a project member with ProjectManager role
                    var projectMember = new ProjectMember
                    {
                        ProjectId = createdProject.Id,
                        UserId = project.ManagerId,
                        Role = UserRole.ProjectManager,
                        JoinedAt = DateTime.UtcNow,
                        IsActive = true
                    };

                    _context.ProjectMembers.Add(projectMember);
                    await _context.SaveChangesAsync();

                    await transaction.CommitAsync();
                    return createdProject;
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
        }

        public async Task<Project> UpdateProjectAsync(Project project)
        {
            project.UpdatedAt = DateTime.UtcNow;
            await _projectRepository.UpdateAsync(project);
            await _projectRepository.SaveChangesAsync();

            return project;
        }

        public async Task<bool> DeleteProjectAsync(int id)
        {
            var project = await _projectRepository.GetByIdAsync(id);
            if (project == null) return false;

            await _projectRepository.DeleteAsync(project);
            return await _projectRepository.SaveChangesAsync();
        }

        public async Task<bool> AddMemberToProjectAsync(int projectId, string userId, UserRole role)
        {
            var existingMember = await _context.ProjectMembers
                .FirstOrDefaultAsync(pm => pm.ProjectId == projectId && pm.UserId == userId);

            if (existingMember != null)
            {
                existingMember.Role = role;
                existingMember.IsActive = true;
            }
            else
            {
                var newMember = new ProjectMember
                {
                    ProjectId = projectId,
                    UserId = userId,
                    Role = role,
                    JoinedAt = DateTime.UtcNow,
                    IsActive = true
                };
                _context.ProjectMembers.Add(newMember);
            }

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> RemoveMemberFromProjectAsync(int projectId, string userId)
        {
            var member = await _context.ProjectMembers
                .FirstOrDefaultAsync(pm => pm.ProjectId == projectId && pm.UserId == userId);

            if (member == null) return false;

            member.IsActive = false;
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> UpdateMemberRoleAsync(int projectId, string userId, UserRole role)
        {
            var member = await _context.ProjectMembers
                .FirstOrDefaultAsync(pm => pm.ProjectId == projectId && pm.UserId == userId);

            if (member == null) return false;

            member.Role = role;
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<IEnumerable<ProjectMember>> GetProjectMembersAsync(int projectId)
        {
            return await _context.ProjectMembers
                .Include(pm => pm.User)
                .Where(pm => pm.ProjectId == projectId && pm.IsActive)
                .ToListAsync();
        }

        public async Task<bool> IsUserProjectMemberAsync(int projectId, string userId)
        {
            return await _context.ProjectMembers
                .AnyAsync(pm => pm.ProjectId == projectId && pm.UserId == userId && pm.IsActive);
        }

        public async Task<UserRole?> GetUserRoleInProjectAsync(int projectId, string userId)
        {
            var member = await _context.ProjectMembers
                .FirstOrDefaultAsync(pm => pm.ProjectId == projectId && pm.UserId == userId && pm.IsActive);

            return member?.Role;
        }

        public async Task<IEnumerable<Project>> GetOverdueProjectsAsync()
        {
            return await _projectRepository.GetOverdueProjectsAsync();
        }

        public async Task<IEnumerable<Project>> GetProjectsEndingSoonAsync(int days = 7)
        {
            return await _projectRepository.GetProjectsEndingInDaysAsync(days);
        }
    }
}
