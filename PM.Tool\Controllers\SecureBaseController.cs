using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Interfaces;
using System.Security.Claims;

namespace PM.Tool.Controllers
{
    [Authorize]
    [AutoValidateAntiforgeryToken]
    public abstract class SecureBaseController : Controller
    {
        protected readonly IAuditService _auditService;

        protected SecureBaseController(IAuditService auditService)
        {
            _auditService = auditService;
        }

        protected async Task LogAuditAsync(AuditAction action, string entityName, int? entityId = null)
        {
            await _auditService.LogAsync(action, entityName, entityId, User.FindFirstValue(ClaimTypes.NameIdentifier));
        }

        protected async Task LogAuditAsync(AuditAction action, string entityName, int? entityId, string? oldValues, string? newValues)
        {
            await _auditService.LogAsync(action, entityName, entityId, User.FindFirstValue(ClaimTypes.NameIdentifier), oldValues, newValues);
        }

        protected IActionResult RedirectToAccessDenied()
        {
            return RedirectToAction("AccessDenied", "Account", new { area = "Identity" });
        }        protected async Task<bool> UserCanAccessProject(int projectId)
        {
            if (User.IsInRole(Core.Constants.Roles.Admin))
                return true;

            var authorizationService = HttpContext.RequestServices.GetRequiredService<IAuthorizationService>();
            var project = await HttpContext.RequestServices.GetRequiredService<IProjectRepository>()
                .GetByIdAsync(projectId);

            if (project == null)
                return false;

            var authResult = await authorizationService.AuthorizeAsync(User, project,
                new Core.Authorization.ProjectOperationRequirement(Core.Authorization.ProjectOperation.View));

            return authResult.Succeeded;
        }

        protected async Task<bool> UserCanAccessTask(int taskId)
        {
            if (User.IsInRole(Core.Constants.Roles.Admin))
                return true;

            var authorizationService = HttpContext.RequestServices.GetRequiredService<IAuthorizationService>();
            var task = await HttpContext.RequestServices.GetRequiredService<ITaskRepository>()
                .GetByIdAsync(taskId);

            if (task == null)
                return false;

            var authResult = await authorizationService.AuthorizeAsync(User, task,
                new Core.Authorization.TaskOperationRequirement(Core.Authorization.TaskOperation.View));

            return authResult.Succeeded;
        }
    }
}
