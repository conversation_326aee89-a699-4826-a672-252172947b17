using PM.Tool.Core.Entities.Agile;
using PM.Tool.Core.Enums;

namespace PM.Tool.Models.DTOs
{
    #region Feature DTOs

    public class FeatureDto
    {
        public int Id { get; set; }
        public string FeatureKey { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public FeatureStatus Status { get; set; }
        public FeaturePriority Priority { get; set; }
        public string? BusinessValue { get; set; }
        public string? AcceptanceCriteria { get; set; }
        public decimal EstimatedStoryPoints { get; set; }
        public decimal ActualStoryPoints { get; set; }
        public decimal ProgressPercentage { get; set; }
        public DateTime? TargetDate { get; set; }
        public string? Tags { get; set; }
        public int ProjectId { get; set; }
        public string ProjectName { get; set; } = string.Empty;
        public int EpicId { get; set; }
        public string EpicTitle { get; set; } = string.Empty;
        public string? OwnerName { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        public static FeatureDto FromEntity(Feature feature)
        {
            return new FeatureDto
            {
                Id = feature.Id,
                FeatureKey = feature.FeatureKey,
                Title = feature.Title,
                Description = feature.Description,
                Status = feature.Status,
                Priority = feature.Priority,
                BusinessValue = feature.BusinessValue,
                AcceptanceCriteria = feature.AcceptanceCriteria,
                EstimatedStoryPoints = feature.EstimatedStoryPoints,
                ActualStoryPoints = feature.ActualStoryPoints,
                ProgressPercentage = (decimal)feature.ProgressPercentage,
                TargetDate = feature.TargetDate,
                Tags = feature.Tags,
                ProjectId = feature.ProjectId,
                ProjectName = feature.Project?.Name ?? string.Empty,
                EpicId = feature.EpicId,
                EpicTitle = feature.Epic?.Title ?? string.Empty,
                OwnerName = feature.Owner?.UserName ?? string.Empty,
                CreatedAt = feature.CreatedAt,
                UpdatedAt = feature.UpdatedAt
            };
        }
    }

    public class FeatureSummaryDto
    {
        public int Id { get; set; }
        public string FeatureKey { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public FeatureStatus Status { get; set; }
        public FeaturePriority Priority { get; set; }
        public decimal ProgressPercentage { get; set; }
        public int UserStoryCount { get; set; }
        public int CompletedUserStoryCount { get; set; }
        public string EpicTitle { get; set; } = string.Empty;
        public string? OwnerName { get; set; }
        public DateTime? TargetDate { get; set; }
    }

    #endregion

    #region Bug DTOs

    public class BugDto
    {
        public int Id { get; set; }
        public string BugKey { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public BugStatus Status { get; set; }
        public BugSeverity Severity { get; set; }
        public BugPriority Priority { get; set; }
        public string StepsToReproduce { get; set; } = string.Empty;
        public string ExpectedResult { get; set; } = string.Empty;
        public string ActualResult { get; set; } = string.Empty;
        public string? FoundInVersion { get; set; }
        public string? FixedInVersion { get; set; }
        public decimal? EstimatedHours { get; set; }
        public decimal? ActualHours { get; set; }
        public DateTime? TargetDate { get; set; }
        public DateTime? ResolvedDate { get; set; }
        public DateTime? VerifiedDate { get; set; }
        public string? Tags { get; set; }
        public int ProjectId { get; set; }
        public string ProjectName { get; set; } = string.Empty;
        public int? FeatureId { get; set; }
        public string? FeatureTitle { get; set; }
        public int? UserStoryId { get; set; }
        public string? UserStoryTitle { get; set; }
        public string? AssignedToName { get; set; }
        public string? ReportedByName { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        public static BugDto FromEntity(Bug bug)
        {
            return new BugDto
            {
                Id = bug.Id,
                BugKey = bug.BugKey,
                Title = bug.Title,
                Description = bug.Description,
                Status = bug.Status,
                Severity = bug.Severity,
                Priority = bug.Priority,
                StepsToReproduce = bug.StepsToReproduce,
                ExpectedResult = bug.ExpectedResult,
                ActualResult = bug.ActualResult,
                FoundInVersion = bug.FoundInVersion,
                FixedInVersion = bug.FixedInVersion,
                EstimatedHours = bug.EstimatedHours,
                ActualHours = bug.ActualHours,
                TargetDate = bug.TargetDate,
                ResolvedDate = bug.ResolvedDate,
                VerifiedDate = bug.VerifiedDate,
                Tags = bug.Tags,
                ProjectId = bug.ProjectId,
                ProjectName = bug.Project?.Name ?? string.Empty,
                FeatureId = bug.FeatureId,
                FeatureTitle = bug.Feature?.Title,
                UserStoryId = bug.UserStoryId,
                UserStoryTitle = bug.UserStory?.Title,
                AssignedToName = bug.AssignedTo?.UserName,
                ReportedByName = bug.ReportedBy?.UserName,
                CreatedAt = bug.CreatedAt,
                UpdatedAt = bug.UpdatedAt
            };
        }
    }

    public class BugSummaryDto
    {
        public int Id { get; set; }
        public string BugKey { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public BugStatus Status { get; set; }
        public BugSeverity Severity { get; set; }
        public BugPriority Priority { get; set; }
        public string? AssignedToName { get; set; }
        public string? FeatureTitle { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? TargetDate { get; set; }
        public bool IsOverdue { get; set; }
    }

    #endregion

    #region Test Case DTOs

    public class TestCaseDto
    {
        public int Id { get; set; }
        public string TestCaseKey { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string? Description { get; set; }
        public TestCaseType Type { get; set; }
        public TestCasePriority Priority { get; set; }
        public TestCaseStatus Status { get; set; }
        public string? PreConditions { get; set; }
        public string TestSteps { get; set; } = string.Empty;
        public string ExpectedResult { get; set; } = string.Empty;
        public decimal? EstimatedExecutionTime { get; set; }
        public string? Tags { get; set; }
        public int ProjectId { get; set; }
        public string ProjectName { get; set; } = string.Empty;
        public int? FeatureId { get; set; }
        public string? FeatureTitle { get; set; }
        public int? UserStoryId { get; set; }
        public string? UserStoryTitle { get; set; }
        public string? AssignedToName { get; set; }
        public string? CreatedByName { get; set; }
        public TestExecutionResult? LastExecutionResult { get; set; }
        public DateTime? LastExecutedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        public static TestCaseDto FromEntity(TestCase testCase)
        {
            return new TestCaseDto
            {
                Id = testCase.Id,
                TestCaseKey = testCase.TestCaseKey,
                Title = testCase.Title,
                Description = testCase.Description,
                Type = testCase.Type,
                Priority = testCase.Priority,
                Status = testCase.Status,
                PreConditions = testCase.PreConditions,
                TestSteps = testCase.TestSteps,
                ExpectedResult = testCase.ExpectedResult,
                EstimatedExecutionTime = testCase.EstimatedExecutionTime,
                Tags = testCase.Tags,
                ProjectId = testCase.ProjectId,
                ProjectName = testCase.Project?.Name ?? string.Empty,
                FeatureId = testCase.FeatureId,
                FeatureTitle = testCase.Feature?.Title,
                UserStoryId = testCase.UserStoryId,
                UserStoryTitle = testCase.UserStory?.Title,
                AssignedToName = testCase.AssignedTo?.UserName,
                CreatedByName = testCase.CreatedBy?.UserName,
                LastExecutionResult = testCase.LastExecutionResult,
                LastExecutedAt = testCase.TestExecutions?.OrderByDescending(te => te.ExecutedAt).FirstOrDefault()?.ExecutedAt,
                CreatedAt = testCase.CreatedAt,
                UpdatedAt = testCase.UpdatedAt
            };
        }
    }

    public class TestCaseSummaryDto
    {
        public int Id { get; set; }
        public string TestCaseKey { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public TestCaseType Type { get; set; }
        public TestCasePriority Priority { get; set; }
        public TestCaseStatus Status { get; set; }
        public TestExecutionResult? LastExecutionResult { get; set; }
        public DateTime? LastExecutedAt { get; set; }
        public string? AssignedToName { get; set; }
        public string? FeatureTitle { get; set; }
        public int ExecutionCount { get; set; }
        public decimal PassRate { get; set; }
    }

    #endregion

    #region Test Execution DTOs

    public class TestExecutionDto
    {
        public int Id { get; set; }
        public int TestCaseId { get; set; }
        public string TestCaseTitle { get; set; } = string.Empty;
        public TestExecutionResult Result { get; set; }
        public string? ActualResult { get; set; }
        public string? Notes { get; set; }
        public decimal? ExecutionTime { get; set; }
        public DateTime ExecutedAt { get; set; }
        public string? ExecutedByName { get; set; }
        public int? BugId { get; set; }
        public string? BugTitle { get; set; }

        public static TestExecutionDto FromEntity(TestExecution execution)
        {
            return new TestExecutionDto
            {
                Id = execution.Id,
                TestCaseId = execution.TestCaseId,
                TestCaseTitle = execution.TestCase?.Title ?? string.Empty,
                Result = execution.Result,
                ActualResult = execution.ActualResult,
                Notes = execution.Notes,
                ExecutionTime = execution.ExecutionTime,
                ExecutedAt = execution.ExecutedAt,
                ExecutedByName = execution.ExecutedBy?.UserName,
                BugId = execution.BugId,
                BugTitle = execution.Bug?.Title
            };
        }
    }

    #endregion

    #region Analytics DTOs

    public class ProjectAnalyticsDto
    {
        public int ProjectId { get; set; }
        public string ProjectName { get; set; } = string.Empty;
        public FeatureAnalyticsDto Features { get; set; } = new();
        public BugAnalyticsDto Bugs { get; set; } = new();
        public TestAnalyticsDto TestCases { get; set; } = new();
        public VelocityAnalyticsDto Velocity { get; set; } = new();
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    }

    public class FeatureAnalyticsDto
    {
        public int TotalFeatures { get; set; }
        public int CompletedFeatures { get; set; }
        public int InProgressFeatures { get; set; }
        public decimal CompletionRate { get; set; }
        public decimal TotalStoryPoints { get; set; }
        public decimal CompletedStoryPoints { get; set; }
        public decimal AverageProgressPercentage { get; set; }
        public IEnumerable<FeatureProgressDto> TopFeatures { get; set; } = new List<FeatureProgressDto>();
    }

    public class BugAnalyticsDto
    {
        public int TotalBugs { get; set; }
        public int OpenBugs { get; set; }
        public int ResolvedBugs { get; set; }
        public decimal ResolutionRate { get; set; }
        public decimal AverageResolutionTimeDays { get; set; }
        public Dictionary<string, int> BugsBySeverity { get; set; } = new();
        public Dictionary<string, int> BugsByStatus { get; set; } = new();
        public IEnumerable<BugTrendDto> BugTrends { get; set; } = new List<BugTrendDto>();
    }

    public class TestAnalyticsDto
    {
        public int TotalTestCases { get; set; }
        public int ExecutedTestCases { get; set; }
        public int PassedTestCases { get; set; }
        public int FailedTestCases { get; set; }
        public decimal ExecutionRate { get; set; }
        public decimal PassRate { get; set; }
        public Dictionary<string, int> TestCasesByType { get; set; } = new();
        public double TestCoveragePercentage { get; set; }
        public IEnumerable<TestExecutionTrendDto> ExecutionTrends { get; set; } = new List<TestExecutionTrendDto>();
    }

    public class VelocityAnalyticsDto
    {
        public decimal AverageVelocity { get; set; }
        public decimal CurrentSprintVelocity { get; set; }
        public decimal PredictedVelocity { get; set; }
        public IEnumerable<SprintVelocityDto> VelocityTrends { get; set; } = new List<SprintVelocityDto>();
    }

    public class FeatureProgressDto
    {
        public int Id { get; set; }
        public string FeatureKey { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public decimal ProgressPercentage { get; set; }
        public FeatureStatus Status { get; set; }
        public DateTime? TargetDate { get; set; }
        public bool IsOnTrack { get; set; }
    }

    public class BugTrendDto
    {
        public DateTime Date { get; set; }
        public int OpenBugs { get; set; }
        public int ResolvedBugs { get; set; }
        public int NewBugs { get; set; }
    }

    public class TestExecutionTrendDto
    {
        public DateTime Date { get; set; }
        public int TotalExecutions { get; set; }
        public int PassedExecutions { get; set; }
        public int FailedExecutions { get; set; }
        public decimal PassRate { get; set; }
    }

    public class SprintVelocityDto
    {
        public int SprintId { get; set; }
        public string SprintName { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal PlannedStoryPoints { get; set; }
        public decimal CompletedStoryPoints { get; set; }
        public decimal Velocity { get; set; }
        public decimal CapacityUtilization { get; set; }
    }

    #endregion

    #region Detail DTOs for API

    public class FeatureDetailDto
    {
        public int Id { get; set; }
        public string FeatureKey { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string? BusinessValue { get; set; }
        public string? AcceptanceCriteria { get; set; }
        public FeatureStatus Status { get; set; }
        public FeaturePriority Priority { get; set; }
        public int EstimatedStoryPoints { get; set; }
        public int ActualStoryPoints { get; set; }
        public decimal ProgressPercentage { get; set; }
        public DateTime? TargetDate { get; set; }
        public string? Tags { get; set; }
        public int SortOrder { get; set; }
        public int ProjectId { get; set; }
        public int? EpicId { get; set; }
        public string? EpicTitle { get; set; }
        public string? OwnerName { get; set; }
        public int UserStoryCount { get; set; }
        public int CompletedUserStoryCount { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class BugDetailDto
    {
        public int Id { get; set; }
        public string BugKey { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string? StepsToReproduce { get; set; }
        public string? ExpectedResult { get; set; }
        public string? ActualResult { get; set; }
        public BugStatus Status { get; set; }
        public BugSeverity Severity { get; set; }
        public BugPriority Priority { get; set; }
        public string? FoundInVersion { get; set; }
        public string? FixedInVersion { get; set; }
        public double? EstimatedHours { get; set; }
        public double? ActualHours { get; set; }
        public DateTime? TargetDate { get; set; }
        public DateTime? ResolvedDate { get; set; }
        public DateTime? VerifiedDate { get; set; }
        public string? Tags { get; set; }
        public int ProjectId { get; set; }
        public int? FeatureId { get; set; }
        public int? UserStoryId { get; set; }
        public string? FeatureTitle { get; set; }
        public string? UserStoryTitle { get; set; }
        public string? ReportedByName { get; set; }
        public string? AssignedToName { get; set; }
        public bool IsOverdue { get; set; }
        public int DaysOpen { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class TestCaseDetailDto
    {
        public int Id { get; set; }
        public string TestCaseKey { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? Preconditions { get; set; }
        public string TestSteps { get; set; } = string.Empty;
        public string ExpectedResult { get; set; } = string.Empty;
        public TestCaseType Type { get; set; }
        public TestCasePriority Priority { get; set; }
        public TestCaseStatus Status { get; set; }
        public TestExecutionResult? LastExecutionResult { get; set; }
        public int? EstimatedExecutionTime { get; set; }
        public string? Tags { get; set; }
        public int ProjectId { get; set; }
        public int? FeatureId { get; set; }
        public int? UserStoryId { get; set; }
        public string? FeatureTitle { get; set; }
        public string? UserStoryTitle { get; set; }
        public string? CreatedByName { get; set; }
        public string? AssignedToName { get; set; }
        public int ExecutionCount { get; set; }
        public decimal PassRate { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class SearchRequest
    {
        public string SearchTerm { get; set; } = string.Empty;
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string SortBy { get; set; } = "CreatedAt";
        public string SortDirection { get; set; } = "desc";
        public Dictionary<string, object> Filters { get; set; } = new();
    }

    #endregion
}
