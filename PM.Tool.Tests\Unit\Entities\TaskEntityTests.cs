using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using Xunit;
using FluentAssertions;

using TaskStatus = PM.Tool.Core.Enums.TaskStatus;

namespace PM.Tool.Tests.Unit.Entities
{
    public class TaskEntityTests
    {
        [Fact]
        public void TaskEntity_DefaultValues_ShouldBeSetCorrectly()
        {
            // Act
            var task = new TaskEntity();

            // Assert
            task.Status.Should().Be(TaskStatus.ToDo);
            task.Priority.Should().Be(TaskPriority.Medium);
            task.WbsLevel.Should().Be(1);
            task.SortOrder.Should().Be(0);
            task.EstimatedHours.Should().Be(0);
            task.ActualHours.Should().Be(0);
            task.IsRecurring.Should().BeFalse();
            task.IsTemplate.Should().BeFalse();
            task.IsDeleted.Should().BeFalse();
            task.Dependencies.Should().NotBeNull();
            task.TimeEntries.Should().NotBeNull();
            task.SubTasks.Should().NotBeNull();
            task.Comments.Should().NotBeNull();
            task.Attachments.Should().NotBeNull();
        }

        [Fact]
        public void TaskEntity_WithValidData_ShouldSetPropertiesCorrectly()
        {
            // Arrange
            var startDate = DateTime.UtcNow;
            var dueDate = DateTime.UtcNow.AddDays(7);

            // Act
            var task = new TaskEntity
            {
                Title = "Test Task",
                Description = "Test Description",
                Status = TaskStatus.InProgress,
                Priority = TaskPriority.High,
                ProjectId = 1,
                AssignedToUserId = "user-1",
                StartDate = startDate,
                DueDate = dueDate,
                EstimatedHours = 8,
                ActualHours = 4
            };

            // Assert
            task.Title.Should().Be("Test Task");
            task.Description.Should().Be("Test Description");
            task.Status.Should().Be(TaskStatus.InProgress);
            task.Priority.Should().Be(TaskPriority.High);
            task.ProjectId.Should().Be(1);
            task.AssignedToUserId.Should().Be("user-1");
            task.StartDate.Should().Be(startDate);
            task.DueDate.Should().Be(dueDate);
            task.EstimatedHours.Should().Be(8);
            task.ActualHours.Should().Be(4);
        }

        [Fact]
        public void TotalTimeSpent_WithNoTimeEntries_ShouldReturnZero()
        {
            // Arrange
            var task = new TaskEntity();

            // Act
            var totalTimeSpent = task.TotalTimeSpent;

            // Assert
            totalTimeSpent.Should().Be(0);
        }

        [Fact]
        public void TotalTimeSpent_WithTimeEntries_ShouldReturnSum()
        {
            // Arrange
            var task = new TaskEntity
            {
                TimeEntries = new List<TimeEntry>
                {
                    new TimeEntry { Duration = 2.5 },
                    new TimeEntry { Duration = 3.0 },
                    new TimeEntry { Duration = 1.5 }
                }
            };

            // Act
            var totalTimeSpent = task.TotalTimeSpent;

            // Assert
            totalTimeSpent.Should().Be(7.0);
        }

        [Fact]
        public void HasDependencies_WithNoDependencies_ShouldReturnFalse()
        {
            // Arrange
            var task = new TaskEntity();

            // Act
            var hasDependencies = task.HasDependencies;

            // Assert
            hasDependencies.Should().BeFalse();
        }

        [Fact]
        public void HasDependencies_WithDependencies_ShouldReturnTrue()
        {
            // Arrange
            var task = new TaskEntity
            {
                Dependencies = new List<TaskDependency>
                {
                    new TaskDependency { Id = 1 }
                }
            };

            // Act
            var hasDependencies = task.HasDependencies;

            // Assert
            hasDependencies.Should().BeTrue();
        }

        [Fact]
        public void IsOverdue_WithNoDueDate_ShouldReturnFalse()
        {
            // Arrange
            var task = new TaskEntity
            {
                DueDate = null,
                Status = TaskStatus.InProgress
            };

            // Act
            var isOverdue = task.IsOverdue;

            // Assert
            isOverdue.Should().BeFalse();
        }

        [Fact]
        public void IsOverdue_WithFutureDueDate_ShouldReturnFalse()
        {
            // Arrange
            var task = new TaskEntity
            {
                DueDate = DateTime.UtcNow.AddDays(5),
                Status = TaskStatus.InProgress
            };

            // Act
            var isOverdue = task.IsOverdue;

            // Assert
            isOverdue.Should().BeFalse();
        }

        [Fact]
        public void IsOverdue_WithPastDueDateAndNotDone_ShouldReturnTrue()
        {
            // Arrange
            var task = new TaskEntity
            {
                DueDate = DateTime.UtcNow.AddDays(-2),
                Status = TaskStatus.InProgress
            };

            // Act
            var isOverdue = task.IsOverdue;

            // Assert
            isOverdue.Should().BeTrue();
        }

        [Fact]
        public void IsOverdue_WithPastDueDateAndDone_ShouldReturnFalse()
        {
            // Arrange
            var task = new TaskEntity
            {
                DueDate = DateTime.UtcNow.AddDays(-2),
                Status = TaskStatus.Done
            };

            // Act
            var isOverdue = task.IsOverdue;

            // Assert
            isOverdue.Should().BeFalse();
        }

        [Fact]
        public void IsSubTask_WithNoParentTask_ShouldReturnFalse()
        {
            // Arrange
            var task = new TaskEntity
            {
                ParentTaskId = null
            };

            // Act
            var isSubTask = task.IsSubTask;

            // Assert
            isSubTask.Should().BeFalse();
        }

        [Fact]
        public void IsSubTask_WithParentTask_ShouldReturnTrue()
        {
            // Arrange
            var task = new TaskEntity
            {
                ParentTaskId = 1
            };

            // Act
            var isSubTask = task.IsSubTask;

            // Assert
            isSubTask.Should().BeTrue();
        }

        [Fact]
        public void SubTaskCount_WithNoSubTasks_ShouldReturnZero()
        {
            // Arrange
            var task = new TaskEntity();

            // Act
            var subTaskCount = task.SubTaskCount;

            // Assert
            subTaskCount.Should().Be(0);
        }

        [Fact]
        public void SubTaskCount_WithSubTasks_ShouldReturnCorrectCount()
        {
            // Arrange
            var task = new TaskEntity
            {
                SubTasks = new List<TaskEntity>
                {
                    new TaskEntity { Id = 1, Title = "SubTask 1" },
                    new TaskEntity { Id = 2, Title = "SubTask 2" },
                    new TaskEntity { Id = 3, Title = "SubTask 3" }
                }
            };

            // Act
            var subTaskCount = task.SubTaskCount;

            // Assert
            subTaskCount.Should().Be(3);
        }

        [Fact]
        public void CompletedSubTasks_WithNoCompletedSubTasks_ShouldReturnZero()
        {
            // Arrange
            var task = new TaskEntity
            {
                SubTasks = new List<TaskEntity>
                {
                    new TaskEntity { Id = 1, Title = "SubTask 1", Status = TaskStatus.ToDo },
                    new TaskEntity { Id = 2, Title = "SubTask 2", Status = TaskStatus.InProgress }
                }
            };

            // Act
            var completedSubTasks = task.CompletedSubTasks;

            // Assert
            completedSubTasks.Should().Be(0);
        }

        [Fact]
        public void CompletedSubTasks_WithCompletedSubTasks_ShouldReturnCorrectCount()
        {
            // Arrange
            var task = new TaskEntity
            {
                SubTasks = new List<TaskEntity>
                {
                    new TaskEntity { Id = 1, Title = "SubTask 1", Status = TaskStatus.Done },
                    new TaskEntity { Id = 2, Title = "SubTask 2", Status = TaskStatus.Done },
                    new TaskEntity { Id = 3, Title = "SubTask 3", Status = TaskStatus.InProgress }
                }
            };

            // Act
            var completedSubTasks = task.CompletedSubTasks;

            // Assert
            completedSubTasks.Should().Be(2);
        }

        [Fact]
        public void SubTaskProgress_WithNoSubTasks_ShouldReturnZero()
        {
            // Arrange
            var task = new TaskEntity();

            // Act
            var subTaskProgress = task.SubTaskProgress;

            // Assert
            subTaskProgress.Should().Be(0);
        }

        [Fact]
        public void SubTaskProgress_WithSubTasks_ShouldCalculateCorrectly()
        {
            // Arrange
            var task = new TaskEntity
            {
                SubTasks = new List<TaskEntity>
                {
                    new TaskEntity { Id = 1, Title = "SubTask 1", Status = TaskStatus.Done },
                    new TaskEntity { Id = 2, Title = "SubTask 2", Status = TaskStatus.Done },
                    new TaskEntity { Id = 3, Title = "SubTask 3", Status = TaskStatus.InProgress },
                    new TaskEntity { Id = 4, Title = "SubTask 4", Status = TaskStatus.ToDo }
                }
            };

            // Act
            var subTaskProgress = task.SubTaskProgress;

            // Assert
            subTaskProgress.Should().Be(50); // 2 completed out of 4 = 50%
        }

        [Fact]
        public void Progress_WithNoSubTasksAndToDoStatus_ShouldReturnZero()
        {
            // Arrange
            var task = new TaskEntity
            {
                Status = TaskStatus.ToDo
            };

            // Act
            var progress = task.Progress;

            // Assert
            progress.Should().Be(0);
        }

        [Fact]
        public void Progress_WithNoSubTasksAndInProgressStatus_ShouldReturnFifty()
        {
            // Arrange
            var task = new TaskEntity
            {
                Status = TaskStatus.InProgress
            };

            // Act
            var progress = task.Progress;

            // Assert
            progress.Should().Be(50);
        }

        [Fact]
        public void Progress_WithNoSubTasksAndDoneStatus_ShouldReturnHundred()
        {
            // Arrange
            var task = new TaskEntity
            {
                Status = TaskStatus.Done
            };

            // Act
            var progress = task.Progress;

            // Assert
            progress.Should().Be(100);
        }

        [Fact]
        public void Progress_WithSubTasks_ShouldReturnSubTaskProgress()
        {
            // Arrange
            var task = new TaskEntity
            {
                Status = TaskStatus.InProgress,
                SubTasks = new List<TaskEntity>
                {
                    new TaskEntity { Id = 1, Title = "SubTask 1", Status = TaskStatus.Done },
                    new TaskEntity { Id = 2, Title = "SubTask 2", Status = TaskStatus.InProgress }
                }
            };

            // Act
            var progress = task.Progress;

            // Assert
            progress.Should().Be(50); // 1 completed out of 2 = 50%
        }

        [Fact]
        public void IsRootTask_WithNoParentTask_ShouldReturnTrue()
        {
            // Arrange
            var task = new TaskEntity
            {
                ParentTaskId = null
            };

            // Act
            var isRootTask = task.IsRootTask;

            // Assert
            isRootTask.Should().BeTrue();
        }

        [Fact]
        public void IsRootTask_WithParentTask_ShouldReturnFalse()
        {
            // Arrange
            var task = new TaskEntity
            {
                ParentTaskId = 1
            };

            // Act
            var isRootTask = task.IsRootTask;

            // Assert
            isRootTask.Should().BeFalse();
        }

        [Fact]
        public void Depth_ShouldReturnWbsLevelMinusOne()
        {
            // Arrange
            var task = new TaskEntity
            {
                WbsLevel = 3
            };

            // Act
            var depth = task.Depth;

            // Assert
            depth.Should().Be(2);
        }
    }
}
