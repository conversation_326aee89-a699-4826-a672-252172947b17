using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;
using PM.Tool.Models;
using PM.Tool.Models.ViewModels;
using PM.Tool.Services;
using System.Diagnostics;

namespace PM.Tool.Controllers
{
    [Authorize]
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IProjectService _projectService;
        private readonly ITaskService _taskService;
        private readonly ApplicationDbContext _context;
        private readonly DebugLocalizationService _debugLocalizationService;

        public HomeController(
            ILogger<HomeController> logger,
            UserManager<ApplicationUser> userManager,
            IProjectService projectService,
            ITaskService taskService,
            ApplicationDbContext context,
            DebugLocalizationService debugLocalizationService)
        {
            _logger = logger;
            _userManager = userManager;
            _projectService = projectService;
            _taskService = taskService;
            _context = context;
            _debugLocalizationService = debugLocalizationService;
        }

        public async Task<IActionResult> Index()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return RedirectToAction("Login", "Account");
            }

            var dashboardViewModel = new DashboardViewModel();

            try
            {
                // Get user's projects
                var userProjects = await _projectService.GetUserProjectsAsync(user.Id);
                var userTasks = await _taskService.GetUserTasksAsync(user.Id);
                var overdueTasks = await _taskService.GetOverdueTasksAsync();
                var upcomingTasks = await _taskService.GetTasksDueSoonAsync(7);

                // Calculate stats
                dashboardViewModel.Stats = new DashboardStatsViewModel
                {
                    TotalProjects = userProjects.Count(),
                    ActiveProjects = userProjects.Count(p => p.Status == Core.Enums.ProjectStatus.Active),
                    CompletedProjects = userProjects.Count(p => p.Status == Core.Enums.ProjectStatus.Completed),
                    MyTasks = userTasks.Count(),
                    CompletedTasks = userTasks.Count(t => t.Status == Core.Enums.TaskStatus.Done),
                    OverdueTasks = overdueTasks.Count(t => t.AssignedToUserId == user.Id),
                    OverallProgress = userProjects.Any() ? userProjects.Average(p => p.ProgressPercentage) : 0
                };

                // Recent projects
                dashboardViewModel.RecentProjects = userProjects
                    .OrderByDescending(p => p.UpdatedAt)
                    .Take(5)
                    .Select(p => new ProjectSummaryViewModel
                    {
                        Id = p.Id,
                        Name = p.Name,
                        Status = p.Status,
                        EndDate = p.EndDate,
                        ProgressPercentage = p.ProgressPercentage,
                        IsOverdue = p.IsOverdue,
                        MemberCount = p.Members.Count,
                        TaskCount = p.TotalTasks
                    }).ToList();

                // My tasks
                dashboardViewModel.MyTasks = userTasks
                    .OrderByDescending(t => t.UpdatedAt)
                    .Take(10)
                    .Select(t => new TaskSummaryViewModel
                    {
                        Id = t.Id,
                        Title = t.Title,
                        Status = t.Status,
                        Priority = t.Priority,
                        DueDate = t.DueDate,
                        IsOverdue = t.IsOverdue,
                        ProjectName = t.Project.Name
                    }).ToList();

                // Overdue tasks for current user
                dashboardViewModel.OverdueTasks = overdueTasks
                    .Where(t => t.AssignedToUserId == user.Id)
                    .Take(5)
                    .Select(t => new TaskSummaryViewModel
                    {
                        Id = t.Id,
                        Title = t.Title,
                        Status = t.Status,
                        Priority = t.Priority,
                        DueDate = t.DueDate,
                        IsOverdue = t.IsOverdue,
                        ProjectName = t.Project.Name
                    }).ToList();

                // Upcoming tasks
                dashboardViewModel.UpcomingTasks = upcomingTasks
                    .Where(t => t.AssignedToUserId == user.Id)
                    .Take(5)
                    .Select(t => new TaskSummaryViewModel
                    {
                        Id = t.Id,
                        Title = t.Title,
                        Status = t.Status,
                        Priority = t.Priority,
                        DueDate = t.DueDate,
                        IsOverdue = t.IsOverdue,
                        ProjectName = t.Project.Name
                    }).ToList();

                // Recent notifications
                dashboardViewModel.RecentNotifications = await _context.Notifications
                    .Where(n => n.UserId == user.Id)
                    .OrderByDescending(n => n.CreatedAt)
                    .Take(5)
                    .Select(n => new NotificationViewModel
                    {
                        Id = n.Id,
                        Title = n.Title,
                        Message = n.Message,
                        Type = n.Type,
                        IsRead = n.IsRead,
                        CreatedAt = n.CreatedAt,
                        RelatedProjectId = n.RelatedProjectId,
                        RelatedTaskId = n.RelatedTaskId
                    }).ToListAsync();

                // Upcoming milestones
                dashboardViewModel.UpcomingMilestones = await _context.Milestones
                    .Include(m => m.Project)
                    .Where(m => m.Project.Members.Any(pm => pm.UserId == user.Id && pm.IsActive) &&
                               !m.IsCompleted &&
                               m.DueDate >= DateTime.UtcNow)
                    .OrderBy(m => m.DueDate)
                    .Take(5)
                    .Select(m => new MilestoneViewModel
                    {
                        Id = m.Id,
                        Name = m.Name,
                        Description = m.Description,
                        ProjectId = m.ProjectId,
                        ProjectName = m.Project.Name,
                        DueDate = m.DueDate,
                        CompletedDate = m.CompletedDate,
                        IsCompleted = m.IsCompleted,
                        IsOverdue = m.IsOverdue
                    }).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading dashboard data for user {UserId}", user.Id);
                // Return empty dashboard on error
            }

            return View(dashboardViewModel);
        }

        [AllowAnonymous]
        public IActionResult Privacy()
        {
            return View();
        }

        [AllowAnonymous]
        public IActionResult TestLocalization()
        {
            // Run detailed localization analysis
            _debugLocalizationService.LogLocalizationDetails();

            var currentCulture = System.Globalization.CultureInfo.CurrentCulture.Name;
            var currentUICulture = System.Globalization.CultureInfo.CurrentUICulture.Name;
            var cookieValue = Request.Cookies[CookieRequestCultureProvider.DefaultCookieName];

            ViewBag.CurrentCulture = currentCulture;
            ViewBag.CurrentUICulture = currentUICulture;
            ViewBag.CookieValue = cookieValue;

            _logger.LogInformation("TestLocalization called - Culture: {Culture}, UICulture: {UICulture}, Cookie: {Cookie}",
                currentCulture, currentUICulture, cookieValue);

            return View();
        }

        [AllowAnonymous]
        public IActionResult TestComponents()
        {
            return View();
        }

        [AllowAnonymous]
        [HttpGet]
        public IActionResult SetLanguage(string culture, string returnUrl)
        {
            try
            {
                if (!string.IsNullOrEmpty(culture))
                {
                    // Define all supported cultures (matching Program.cs configuration)
                    var supportedCultures = new[]
                    {
                        "en-US", "es-ES", "en", "es", "fr-FR", "de-DE", "it-IT", "pt-BR", "pt-PT",
                        "nl-NL", "sv-SE", "da-DK", "no-NO", "fi-FI", "ru-RU", "pl-PL", "tr-TR",
                        "cs-CZ", "hu-HU", "ro-RO", "bg-BG", "hr-HR", "sk-SK", "sl-SI", "et-EE",
                        "lv-LV", "lt-LT", "zh-CN", "zh-TW", "ja-JP", "ko-KR", "th-TH", "vi-VN",
                        "id-ID", "ms-MY", "ar-SA", "ar-EG", "he-IL", "fa-IR", "ur-PK", "hi-IN",
                        "bn-BD", "ta-IN", "te-IN", "mr-IN", "gu-IN", "kn-IN", "ml-IN", "pa-IN",
                        "en-GB", "en-AU", "en-CA", "es-MX", "es-AR", "es-CO", "es-CL", "es-PE"
                    };

                    // Validate culture - if not supported, try to find a fallback
                    if (!supportedCultures.Contains(culture))
                    {
                        // Try to find a parent culture (e.g., "en" for "en-XX")
                        var parentCulture = culture.Split('-')[0];
                        if (supportedCultures.Contains(parentCulture))
                        {
                            culture = parentCulture;
                        }
                        else
                        {
                            culture = "en-US"; // Default to English (US)
                        }
                    }

                    // Set the culture cookie using the standard ASP.NET Core cookie name
                    var cookieValue = CookieRequestCultureProvider.MakeCookieValue(new RequestCulture(culture));
                    Response.Cookies.Append(
                        CookieRequestCultureProvider.DefaultCookieName,
                        cookieValue,
                        new CookieOptions
                        {
                            Expires = DateTimeOffset.UtcNow.AddYears(1),
                            HttpOnly = false,
                            Secure = Request.IsHttps, // Use HTTPS detection
                            SameSite = SameSiteMode.Lax,
                            Path = "/"
                        }
                    );

                    _logger.LogInformation("Language set to {Culture}, Cookie: {CookieValue}", culture, cookieValue);
                }

                // Redirect back to the previous page or home
                if (!string.IsNullOrEmpty(returnUrl) && Url.IsLocalUrl(returnUrl))
                {
                    return Redirect(returnUrl);
                }

                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting language to {Culture}", culture);
                return RedirectToAction("Index");
            }
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }
    }
}
