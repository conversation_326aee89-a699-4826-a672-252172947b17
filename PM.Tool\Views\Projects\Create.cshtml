@using PM.Tool.Models.ViewModels
@model ProjectCreateViewModel

@{
    ViewData["Title"] = "Create Project";
    ViewData["Subtitle"] = "Set up a new project with all the necessary details";
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-plus-circle mr-3 text-primary-600 dark:text-primary-400"></i>
                Create New Project
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Set up a new project with all the necessary details to get started
            </p>
        </div>
        <div>
            @{
                ViewData["Text"] = "Back to Projects";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Index");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Create Project Form -->
<div class="card-custom">
    <div class="card-header-custom">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                <i class="fas fa-project-diagram text-primary-600 dark:text-primary-400 text-sm"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Project Information</h3>
            </div>
        </div>
    </div>
    <div class="card-body-custom">
        <form asp-action="Create" class="space-y-6">
            <div asp-validation-summary="ModelOnly" class="alert-danger-custom"></div>

            <!-- Project Name -->
            @{
                ViewData["Label"] = Html.DisplayNameFor(m => m.Name).ToString();
                ViewData["Name"] = "Name";
                ViewData["Type"] = "text";
                ViewData["Required"] = true;
                ViewData["Icon"] = "fas fa-tag";
                ViewData["Placeholder"] = "Enter project name";
                ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Name");
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

            <!-- Project Description -->
            @{
                ViewData["Label"] = Html.DisplayNameFor(m => m.Description).ToString();
                ViewData["Name"] = "Description";
                ViewData["Type"] = "textarea";
                ViewData["Required"] = false;
                ViewData["Icon"] = "fas fa-align-left";
                ViewData["Placeholder"] = "Describe the project goals and objectives";
                ViewData["Rows"] = 4;
                ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Description");
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

        <!-- Date Range -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Start Date -->
            @{
                ViewData["Label"] = Html.DisplayNameFor(m => m.StartDate).ToString();
                ViewData["Name"] = "StartDate";
                ViewData["Type"] = "date";
                ViewData["Required"] = true;
                ViewData["Icon"] = "fas fa-calendar-alt";
                ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("StartDate");
                ViewData["ContainerClasses"] = "";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

            <!-- End Date -->
            @{
                ViewData["Label"] = Html.DisplayNameFor(m => m.EndDate).ToString();
                ViewData["Name"] = "EndDate";
                ViewData["Type"] = "date";
                ViewData["Required"] = false;
                ViewData["Icon"] = "fas fa-calendar-check";
                ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("EndDate");
                ViewData["ContainerClasses"] = "";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />
        </div>

        <!-- Budget and Client -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Budget -->
            @{
                ViewData["Label"] = Html.DisplayNameFor(m => m.Budget).ToString();
                ViewData["Name"] = "Budget";
                ViewData["Type"] = "number";
                ViewData["Required"] = false;
                ViewData["Icon"] = "fas fa-dollar-sign";
                ViewData["Placeholder"] = "0.00";
                ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Budget");
                ViewData["ContainerClasses"] = "";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

            <!-- Client Name -->
            @{
                ViewData["Label"] = Html.DisplayNameFor(m => m.ClientName).ToString();
                ViewData["Name"] = "ClientName";
                ViewData["Type"] = "text";
                ViewData["Required"] = false;
                ViewData["Icon"] = "fas fa-building";
                ViewData["Placeholder"] = "Enter client or organization name";
                ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("ClientName");
                ViewData["ContainerClasses"] = "";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />
        </div>

        <!-- Status and Manager -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Status -->
            @{
                var statusOptions = "<option value=\"\">-- Select Status --</option>";
                foreach (var item in Html.GetEnumSelectList<PM.Tool.Core.Enums.ProjectStatus>())
                {
                    statusOptions += $"<option value=\"{item.Value}\">{item.Text}</option>";
                }

                ViewData["Label"] = Html.DisplayNameFor(m => m.Status).ToString();
                ViewData["Name"] = "Status";
                ViewData["Type"] = "select";
                ViewData["Required"] = true;
                ViewData["Icon"] = "fas fa-info-circle";
                ViewData["Options"] = statusOptions;
                ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Status");
                ViewData["ContainerClasses"] = "";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

            <!-- Project Manager -->
            @{
                var managerOptions = "<option value=\"\">-- Select Manager --</option>";
                if (ViewBag.ProjectManagers != null)
                {
                    foreach (var manager in (IEnumerable<SelectListItem>)ViewBag.ProjectManagers)
                    {
                        managerOptions += $"<option value=\"{manager.Value}\">{manager.Text}</option>";
                    }
                }

                ViewData["Label"] = "Project Manager";
                ViewData["Name"] = "ManagerId";
                ViewData["Type"] = "select";
                ViewData["Required"] = false;
                ViewData["Icon"] = "fas fa-user-tie";
                ViewData["Options"] = managerOptions;
                ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("ManagerId");
                ViewData["ContainerClasses"] = "";
                ViewData["UseSelect2"] = true;
                ViewData["Select2Options"] = "{\"placeholder\": \"Select Manager\", \"allowClear\": true, \"theme\": \"tailwind\"}";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />
        </div>

        <!-- Form Actions -->
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0 pt-6 border-t border-neutral-200 dark:border-dark-200">
            <div>
                @{
                    ViewData["Text"] = "Back to List";
                    ViewData["Variant"] = "secondary";
                    ViewData["Icon"] = "fas fa-arrow-left";
                    ViewData["Href"] = Url.Action("Index");
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
            <div>
                @{
                    ViewData["Text"] = "Create Project";
                    ViewData["Variant"] = "primary";
                    ViewData["Icon"] = "fas fa-plus";
                    ViewData["Type"] = "submit";
                    ViewData["Href"] = null;
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        $(document).ready(function() {
            // Add form validation styling
            $('form').on('submit', function(e) {
                var isValid = true;

                // Check required fields
                $(this).find('[required]').each(function() {
                    if (!$(this).val()) {
                        isValid = false;
                        $(this).addClass('border-danger-300 dark:border-danger-600');
                    } else {
                        $(this).removeClass('border-danger-300 dark:border-danger-600');
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    // Show error message
                    if (!$('.alert-danger-custom').length) {
                        $('form').prepend(`
                            <div class="alert-danger-custom mb-6">
                                <i class="fas fa-exclamation-circle text-danger-600 dark:text-danger-400"></i>
                                <div>
                                    <p class="font-medium">Validation Error</p>
                                    <p class="text-sm">Please fill in all required fields.</p>
                                </div>
                            </div>
                        `);
                    }
                }
            });

            // Date validation
            $('input[name="EndDate"]').on('change', function() {
                var startDate = $('input[name="StartDate"]').val();
                var endDate = $(this).val();

                if (startDate && endDate && new Date(endDate) < new Date(startDate)) {
                    $(this).addClass('border-danger-300 dark:border-danger-600');
                    if (!$(this).siblings('.text-danger-600').length) {
                        $(this).after('<p class="text-sm text-danger-600 dark:text-danger-400 mt-1">End date must be after start date</p>');
                    }
                } else {
                    $(this).removeClass('border-danger-300 dark:border-danger-600');
                    $(this).siblings('.text-danger-600').remove();
                }
            });
        });
    </script>
}