using PM.Tool.Core.Enums;
using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities
{
    public class Notification
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [MaxLength(1000)]
        public string Message { get; set; } = string.Empty;

        public NotificationType Type { get; set; }

        public string UserId { get; set; } = string.Empty;

        public bool IsRead { get; set; } = false;

        public bool IsEmailSent { get; set; } = false;

        public int? RelatedProjectId { get; set; }

        public int? RelatedTaskId { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? ReadAt { get; set; }

        // Navigation properties
        public virtual ApplicationUser User { get; set; } = null!;
        public virtual Project? RelatedProject { get; set; }
        public virtual TaskEntity? RelatedTask { get; set; }
    }
}
