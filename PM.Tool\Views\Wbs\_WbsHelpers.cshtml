<script>
    // WBS Helper Functions - Utility functions for colors, formatting, etc.

    // Helper functions for styling
    function getProgressColor(progress) {
        if (progress === 0) return 'progress-bar-red';
        if (progress < 25) return 'progress-bar-orange';
        if (progress < 50) return 'progress-bar-yellow';
        if (progress < 75) return 'progress-bar-blue';
        return 'progress-bar-green';
    }

    function getPriorityColor(priority) {
        switch (priority?.toLowerCase()) {
            case 'critical':
                return {
                    bg: 'bg-red-50 dark:bg-red-900/20',
                    text: 'text-red-900 dark:text-red-100',
                    badge: 'priority-badge priority-critical'
                };
            case 'high':
                return {
                    bg: 'bg-orange-50 dark:bg-orange-900/20',
                    text: 'text-orange-900 dark:text-orange-100',
                    badge: 'priority-badge priority-high'
                };
            case 'medium':
                return {
                    bg: 'bg-blue-50 dark:bg-blue-900/20',
                    text: 'text-blue-900 dark:text-blue-100',
                    badge: 'priority-badge priority-medium'
                };
            case 'low':
                return {
                    bg: 'bg-green-50 dark:bg-green-900/20',
                    text: 'text-green-900 dark:text-green-100',
                    badge: 'priority-badge priority-low'
                };
            default:
                return {
                    bg: 'bg-slate-50 dark:bg-slate-800',
                    text: 'text-slate-900 dark:text-slate-100',
                    badge: 'priority-badge priority-medium'
                };
        }
    }

    function getStatusColor(status) {
        switch (status?.toLowerCase()) {
            case 'done':
                return {
                    badge: 'status-badge status-done'
                };
            case 'inprogress':
                return {
                    badge: 'status-badge status-inprogress'
                };
            case 'inreview':
                return {
                    badge: 'status-badge status-inreview'
                };
            case 'cancelled':
                return {
                    badge: 'status-badge status-cancelled'
                };
            case 'todo':
            default:
                return {
                    badge: 'status-badge status-todo'
                };
        }
    }

    function formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString();
    }

    // Show alert message
    function showAlert(message, type) {
        // Create alert element with pure Tailwind classes
        const alertClasses = {
            success: 'bg-emerald-50 border-emerald-200 text-emerald-800 dark:bg-emerald-900/20 dark:border-emerald-800 dark:text-emerald-200',
            danger: 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200',
            warning: 'bg-amber-50 border-amber-200 text-amber-800 dark:bg-amber-900/20 dark:border-amber-800 dark:text-amber-200',
            info: 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-200'
        };

        const iconClasses = {
            success: 'fas fa-check-circle text-emerald-500',
            danger: 'fas fa-exclamation-circle text-red-500',
            warning: 'fas fa-exclamation-triangle text-amber-500',
            info: 'fas fa-info-circle text-blue-500'
        };

        const alertClass = alertClasses[type] || alertClasses.info;
        const iconClass = iconClasses[type] || iconClasses.info;

        const alert = $(`
            <div class="flex items-start p-4 mb-4 border rounded-lg backdrop-blur-sm ${alertClass} animate-slide-in" role="alert">
                <i class="${iconClass} mt-0.5 mr-3 flex-shrink-0"></i>
                <div class="flex-1">
                    <span class="font-medium">${message}</span>
                </div>
                <button type="button" class="ml-4 -mx-1.5 -my-1.5 rounded-lg p-1.5 hover:bg-black/5 dark:hover:bg-white/5 transition-colors" onclick="$(this).closest('[role=alert]').fadeOut(() => $(this).closest('[role=alert]').remove())">
                    <i class="fas fa-times text-sm"></i>
                </button>
            </div>
        `);

        $('#wbs-container').prepend(alert);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            alert.fadeOut(() => alert.remove());
        }, 5000);
    }

    // WBS Statistics
    function updateWbsStatistics() {
        let totalTasks = 0;
        let completedTasks = 0;
        let inProgressTasks = 0;
        let inReviewTasks = 0;
        let overdueTasks = 0;
        let totalProgress = 0;

        function countTasks(tasks) {
            tasks.forEach(task => {
                totalTasks++;
                totalProgress += (task.progress || 0);

                switch(task.status?.toLowerCase()) {
                    case 'done':
                        completedTasks++;
                        break;
                    case 'inprogress':
                        inProgressTasks++;
                        break;
                    case 'inreview':
                        inReviewTasks++;
                        break;
                }

                // Check if overdue
                if (task.dueDate && new Date(task.dueDate) < new Date() && task.status !== 'Done') {
                    overdueTasks++;
                }

                if (task.children && task.children.length > 0) {
                    countTasks(task.children);
                }
            });
        }

        if (wbsData && wbsData.length > 0) {
            countTasks(wbsData);
        }

        // Update statistics display
        $('#totalTasks').text(totalTasks);
        $('#completedTasks').text(completedTasks);
        $('#inProgressTasks').text(inProgressTasks);
        $('#inReviewTasks').text(inReviewTasks);
        $('#overdueTasks').text(overdueTasks);

        const overallProgress = totalTasks > 0 ? Math.round(totalProgress / totalTasks) : 0;
        $('#overallProgress').text(overallProgress + '%');
    }

    // Accessibility enhancements
    function enhanceAccessibility() {
        // Add ARIA labels to action buttons
        $('.action-btn').each(function() {
            const $btn = $(this);
            const title = $btn.attr('title');
            if (title) {
                $btn.attr('aria-label', title);
            }
        });

        // Add keyboard navigation for task nodes
        $('.wbs-node').attr('tabindex', '0').on('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                const taskId = $(this).data('task-id');
                viewTask(taskId);
            }
        });
    }
</script>
