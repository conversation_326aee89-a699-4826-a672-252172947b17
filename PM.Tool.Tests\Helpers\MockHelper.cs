using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.Extensions.Logging;
using Moq;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Interfaces;
using System.Security.Claims;

namespace PM.Tool.Tests.Helpers
{
    public static class MockHelper
    {
        public static Mock<UserManager<ApplicationUser>> CreateMockUserManager()
        {
            var store = new Mock<IUserStore<ApplicationUser>>();
            var mgr = new Mock<UserManager<ApplicationUser>>(store.Object, null!, null!, null!, null!, null!, null!, null!, null!);
            mgr.Object.UserValidators.Add(new UserValidator<ApplicationUser>());
            mgr.Object.PasswordValidators.Add(new PasswordValidator<ApplicationUser>());
            return mgr;
        }

        public static Mock<SignInManager<ApplicationUser>> CreateMockSignInManager(Mock<UserManager<ApplicationUser>> userManager)
        {
            var contextAccessor = new Mock<IHttpContextAccessor>();
            var userPrincipalFactory = new Mock<IUserClaimsPrincipalFactory<ApplicationUser>>();
            return new Mock<SignInManager<ApplicationUser>>(
                userManager.Object,
                contextAccessor.Object,
                userPrincipalFactory.Object,
                null!, null!, null!, null!);
        }

        public static Mock<ILogger<T>> CreateMockLogger<T>()
        {
            return new Mock<ILogger<T>>();
        }

        public static ClaimsPrincipal CreateTestUser(string userId = "test-user-1", string email = "<EMAIL>")
        {
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, userId),
                new Claim(ClaimTypes.Email, email),
                new Claim(ClaimTypes.Name, email)
            };

            var identity = new ClaimsIdentity(claims, "Test");
            return new ClaimsPrincipal(identity);
        }

        public static void SetupControllerContext(Controller controller, ClaimsPrincipal? user = null)
        {
            user ??= CreateTestUser();

            var httpContext = new DefaultHttpContext
            {
                User = user
            };

            // Mock TempData
            var tempDataProvider = new Mock<ITempDataProvider>();
            var tempDataDictionary = new TempDataDictionary(httpContext, tempDataProvider.Object);

            controller.ControllerContext = new ControllerContext
            {
                HttpContext = httpContext
            };

            controller.TempData = tempDataDictionary;
        }

        public static Mock<ICacheService> CreateMockCacheService()
        {
            var mock = new Mock<ICacheService>();

            // Setup default behavior for cache operations
            mock.Setup(x => x.GetAsync<object>(It.IsAny<string>()))
                .ReturnsAsync((object?)null);

            mock.Setup(x => x.SetAsync(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<TimeSpan?>()))
                .Returns(Task.CompletedTask);

            mock.Setup(x => x.RemoveAsync(It.IsAny<string>()))
                .Returns(Task.CompletedTask);

            mock.Setup(x => x.RemoveByPrefixAsync(It.IsAny<string>()))
                .Returns(Task.CompletedTask);

            return mock;
        }

        public static Mock<IAuditService> CreateMockAuditService()
        {
            var mock = new Mock<IAuditService>();

            mock.Setup(x => x.LogAsync(
                It.IsAny<PM.Tool.Core.Enums.AuditAction>(),
                It.IsAny<string>(),
                It.IsAny<int?>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>()))
                .Returns(Task.CompletedTask);

            return mock;
        }

        public static Mock<INotificationService> CreateMockNotificationService()
        {
            var mock = new Mock<INotificationService>();

            mock.Setup(x => x.CreateNotificationAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<NotificationType>(), It.IsAny<int?>(), It.IsAny<int?>()))
                .ReturnsAsync(new Notification());

            mock.Setup(x => x.NotifyTaskAssignedAsync(It.IsAny<int>(), It.IsAny<string>()))
                .Returns(Task.CompletedTask);

            mock.Setup(x => x.NotifyDeadlineApproachingAsync())
                .Returns(Task.CompletedTask);

            return mock;
        }

        public static Mock<IRepository<T>> CreateMockRepository<T>() where T : BaseEntity
        {
            var mock = new Mock<IRepository<T>>();

            // Setup default CRUD operations
            mock.Setup(x => x.GetByIdAsync(It.IsAny<int>()))
                .ReturnsAsync((T?)null);

            mock.Setup(x => x.GetAllAsync())
                .ReturnsAsync(new List<T>());

            mock.Setup(x => x.AddAsync(It.IsAny<T>()))
                .ReturnsAsync(It.IsAny<T>());

            mock.Setup(x => x.UpdateAsync(It.IsAny<T>()))
                .Returns(Task.CompletedTask);

            mock.Setup(x => x.DeleteAsync(It.IsAny<T>()))
                .Returns(Task.CompletedTask);

            return mock;
        }

        public static Mock<IProjectRepository> CreateMockProjectRepository()
        {
            var mock = new Mock<IProjectRepository>();

            // Setup project-specific methods
            mock.Setup(x => x.GetProjectWithMembersAsync(It.IsAny<int>()))
                .ReturnsAsync((Project?)null);

            mock.Setup(x => x.GetByIdAsync(It.IsAny<int>()))
                .ReturnsAsync((Project?)null);

            mock.Setup(x => x.GetAllAsync())
                .ReturnsAsync(new List<Project>());

            mock.Setup(x => x.AddAsync(It.IsAny<Project>()))
                .ReturnsAsync(It.IsAny<Project>());

            mock.Setup(x => x.UpdateAsync(It.IsAny<Project>()))
                .Returns(Task.CompletedTask);

            mock.Setup(x => x.DeleteAsync(It.IsAny<Project>()))
                .Returns(Task.CompletedTask);

            mock.Setup(x => x.SaveChangesAsync())
                .ReturnsAsync(true);

            return mock;
        }

        public static Mock<ITaskRepository> CreateMockTaskRepository()
        {
            var mock = new Mock<ITaskRepository>();

            // Setup task-specific methods
            mock.Setup(x => x.GetTaskWithSubTasksAsync(It.IsAny<int>()))
                .ReturnsAsync((TaskEntity?)null);

            mock.Setup(x => x.GetTasksByProjectIdAsync(It.IsAny<int>()))
                .ReturnsAsync(new List<TaskEntity>());

            mock.Setup(x => x.GetTasksByUserIdAsync(It.IsAny<string>()))
                .ReturnsAsync(new List<TaskEntity>());

            mock.Setup(x => x.GetByIdAsync(It.IsAny<int>()))
                .ReturnsAsync((TaskEntity?)null);

            mock.Setup(x => x.GetAllAsync())
                .ReturnsAsync(new List<TaskEntity>());

            mock.Setup(x => x.AddAsync(It.IsAny<TaskEntity>()))
                .ReturnsAsync(It.IsAny<TaskEntity>());

            mock.Setup(x => x.UpdateAsync(It.IsAny<TaskEntity>()))
                .Returns(Task.CompletedTask);

            mock.Setup(x => x.DeleteAsync(It.IsAny<TaskEntity>()))
                .Returns(Task.CompletedTask);

            mock.Setup(x => x.SaveChangesAsync())
                .ReturnsAsync(true);

            return mock;
        }

        // Entity Creation Helpers
        public static ApplicationUser CreateMockUser()
        {
            return new ApplicationUser
            {
                Id = "test-user-id",
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Test",
                LastName = "User",
                IsActive = true
            };
        }

        public static Project CreateMockProject()
        {
            return new Project
            {
                Id = 1,
                Name = "Test Project",
                Description = "Test project description",
                StartDate = DateTime.UtcNow,
                EndDate = DateTime.UtcNow.AddMonths(3),
                Status = ProjectStatus.Active,
                CreatedByUserId = "test-user-id",
                ManagerId = "test-user-id",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
        }

        public static TaskEntity CreateMockTask()
        {
            return new TaskEntity
            {
                Id = 1,
                Title = "Test Task",
                Description = "Test task description",
                Status = PM.Tool.Core.Enums.TaskStatus.ToDo,
                Priority = TaskPriority.Medium,
                ProjectId = 1,
                AssignedToUserId = "test-user-id",
                CreatedByUserId = "test-user-id",
                DueDate = DateTime.UtcNow.AddDays(7),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
        }

        public static IEnumerable<TaskEntity> CreateMockTasks(int count)
        {
            var tasks = new List<TaskEntity>();
            for (int i = 1; i <= count; i++)
            {
                tasks.Add(new TaskEntity
                {
                    Id = i,
                    Title = $"Task {i}",
                    Description = $"Description for task {i}",
                    Status = PM.Tool.Core.Enums.TaskStatus.ToDo,
                    Priority = TaskPriority.Medium,
                    ProjectId = 1,
                    AssignedToUserId = "test-user-id",
                    CreatedByUserId = "test-user-id",
                    DueDate = DateTime.UtcNow.AddDays(i),
                    CreatedAt = DateTime.UtcNow.AddDays(-i),
                    UpdatedAt = DateTime.UtcNow.AddDays(-i)
                });
            }
            return tasks;
        }

        public static IEnumerable<Project> CreateMockProjects(int count)
        {
            var projects = new List<Project>();
            for (int i = 1; i <= count; i++)
            {
                projects.Add(new Project
                {
                    Id = i,
                    Name = $"Project {i}",
                    Description = $"Description for project {i}",
                    StartDate = DateTime.UtcNow.AddDays(-30),
                    EndDate = DateTime.UtcNow.AddMonths(i),
                    Status = ProjectStatus.Active,
                    CreatedByUserId = "test-user-id",
                    ManagerId = "test-user-id",
                    CreatedAt = DateTime.UtcNow.AddDays(-i),
                    UpdatedAt = DateTime.UtcNow.AddDays(-i)
                });
            }
            return projects;
        }

        public static Requirement CreateMockRequirement()
        {
            return new Requirement
            {
                Id = 1,
                Title = "Test Requirement",
                Description = "Test requirement description",
                Type = RequirementType.Functional,
                Priority = RequirementPriority.High,
                Status = RequirementStatus.Draft,
                Source = RequirementSource.Stakeholder,
                ProjectId = 1,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
        }

        public static IEnumerable<Requirement> CreateMockRequirements(int count)
        {
            var requirements = new List<Requirement>();
            for (int i = 1; i <= count; i++)
            {
                requirements.Add(new Requirement
                {
                    Id = i,
                    Title = $"Requirement {i}",
                    Description = $"Description for requirement {i}",
                    Type = RequirementType.Functional,
                    Priority = RequirementPriority.High,
                    Status = RequirementStatus.Draft,
                    Source = RequirementSource.Stakeholder,
                    ProjectId = 1,
                    CreatedAt = DateTime.UtcNow.AddDays(-i),
                    UpdatedAt = DateTime.UtcNow.AddDays(-i)
                });
            }
            return requirements;
        }

        public static Meeting CreateMockMeeting()
        {
            return new Meeting
            {
                Id = 1,
                Title = "Test Meeting",
                Description = "Test meeting description",
                ProjectId = 1,
                Type = MeetingType.General,
                ScheduledDate = DateTime.UtcNow.AddDays(1),
                DurationMinutes = 60,
                Location = "Conference Room A",
                Status = MeetingStatus.Scheduled,
                OrganizerUserId = "test-user-id",
                Agenda = "Test agenda",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
        }

        public static IEnumerable<Meeting> CreateMockMeetings(int count)
        {
            var meetings = new List<Meeting>();
            for (int i = 1; i <= count; i++)
            {
                meetings.Add(new Meeting
                {
                    Id = i,
                    Title = $"Meeting {i}",
                    Description = $"Description for meeting {i}",
                    ProjectId = 1,
                    Type = MeetingType.General,
                    ScheduledDate = DateTime.UtcNow.AddDays(i),
                    DurationMinutes = 60,
                    Location = $"Room {i}",
                    Status = MeetingStatus.Scheduled,
                    OrganizerUserId = "test-user-id",
                    Agenda = $"Agenda for meeting {i}",
                    CreatedAt = DateTime.UtcNow.AddDays(-i),
                    UpdatedAt = DateTime.UtcNow.AddDays(-i)
                });
            }
            return meetings;
        }

        public static IEnumerable<MeetingAttendee> CreateMockMeetingAttendees(int count)
        {
            var attendees = new List<MeetingAttendee>();
            for (int i = 1; i <= count; i++)
            {
                attendees.Add(new MeetingAttendee
                {
                    Id = i,
                    MeetingId = 1,
                    UserId = $"user-{i}",
                    Role = AttendeeRole.Attendee,
                    Status = AttendanceStatus.Invited,
                    IsRequired = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                });
            }
            return attendees;
        }

        public static IEnumerable<MeetingActionItem> CreateMockMeetingActionItems(int count)
        {
            var actionItems = new List<MeetingActionItem>();
            for (int i = 1; i <= count; i++)
            {
                actionItems.Add(new MeetingActionItem
                {
                    Id = i,
                    MeetingId = 1,
                    Title = $"Action Item {i}",
                    Description = $"Description for action item {i}",
                    AssignedToUserId = $"user-{i}",
                    DueDate = DateTime.UtcNow.AddDays(7),
                    Status = ActionItemStatus.Open,
                    Priority = 3,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                });
            }
            return actionItems;
        }

        public static IEnumerable<MeetingDocument> CreateMockMeetingDocuments(int count)
        {
            var documents = new List<MeetingDocument>();
            for (int i = 1; i <= count; i++)
            {
                documents.Add(new MeetingDocument
                {
                    Id = i,
                    MeetingId = 1,
                    FileName = $"Document{i}.pdf",
                    FilePath = $"/documents/Document{i}.pdf",
                    ContentType = "application/pdf",
                    FileSize = 1024 * i,
                    Type = DocumentType.Agenda,
                    UploadedByUserId = "test-user-id",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                });
            }
            return documents;
        }
    }
}
