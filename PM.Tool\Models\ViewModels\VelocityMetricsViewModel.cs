using System;
using System.Collections.Generic;

namespace PM.Tool.Models.ViewModels
{
    public class VelocityMetricsViewModel
    {
        public int TotalCompletedTasks { get; set; }
        public List<WeeklyVelocityData> WeeklyVelocity { get; set; } = new();
        public double AverageCycleTime { get; set; }
        public List<TeamMemberMetrics> TeamMembers { get; set; } = new();
        public double AverageVelocity { get; set; }
    }

    public class WeeklyVelocityData
    {
        public DateTime WeekStartDate { get; set; }
        public DateTime Date => WeekStartDate;
        public int CompletedTasks { get; set; }
        public double CompletedPoints { get; set; }
        public double Velocity { get; set; }
    }

    public class TeamMemberMetrics
    {
        public string MemberName { get; set; } = string.Empty;
        public int CompletedTasks { get; set; }
        public int TotalHours { get; set; }
        public double EfficiencyRatio { get; set; }
    }

    public class UserProductivityData
    {
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public int CompletedTasks { get; set; }
        public double TotalTimeSpent { get; set; }
        public double AverageTimePerTask { get; set; }
    }
}
