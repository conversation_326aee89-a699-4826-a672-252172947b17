@* Task Details Styles *@
<style>
    /* Task Details Specific Styles */
    .task-details-container {
        min-height: calc(100vh - 200px);
    }

    /* Task Overview Card Enhancements */
    .task-overview-card {
        transition: all 0.3s ease;
    }

    .task-overview-card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    /* Compact Metadata Cards */
    .metadata-card {
        transition: all 0.2s ease;
        border: 1px solid transparent;
    }

    .metadata-card:hover {
        border-color: var(--primary-200);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .dark .metadata-card:hover {
        border-color: var(--primary-700);
    }

    /* Metadata Icon Containers */
    .metadata-icon {
        transition: all 0.2s ease;
    }

    .metadata-card:hover .metadata-icon {
        transform: scale(1.05);
    }

    /* Tab Styles */
    .tab-button {
        background-color: transparent;
        color: var(--neutral-600);
        border: 1px solid transparent;
    }

    .dark .tab-button {
        color: var(--dark-400);
    }

    .tab-button:hover {
        background-color: var(--neutral-100);
        color: var(--neutral-800);
    }

    .dark .tab-button:hover {
        background-color: var(--dark-600);
        color: var(--dark-200);
    }

    .tab-button.active {
        background-color: var(--primary-100);
        color: var(--primary-700);
        border-color: var(--primary-200);
    }

    .dark .tab-button.active {
        background-color: var(--primary-900);
        color: var(--primary-300);
        border-color: var(--primary-700);
    }

    .tab-content {
        transition: opacity 0.2s ease-in-out;
    }

    .tab-content.hidden {
        display: none;
    }

    .tab-content.active {
        display: block;
        animation: fadeIn 0.3s ease-in-out;
    }

    @@keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Timeline Styles */
    .timeline-dot {
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .timeline-dot:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .timeline-content {
        transition: all 0.2s ease;
    }

    .timeline-content:hover {
        transform: translateX(4px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    /* Azure DevOps inspired timeline */
    .timeline-item {
        position: relative;
    }

    .timeline-item::before {
        content: '';
        position: absolute;
        left: 24px;
        top: 48px;
        bottom: -16px;
        width: 2px;
        background: linear-gradient(to bottom, var(--neutral-300), transparent);
    }

    .timeline-item:last-child::before {
        display: none;
    }

    .dark .timeline-item::before {
        background: linear-gradient(to bottom, var(--dark-400), transparent);
    }

    /* Status and Priority Badge Animations */
    .status-badge, .priority-badge {
        transition: all 0.2s ease;
        animation: fadeInUp 0.5s ease-out;
    }

    .status-badge:hover, .priority-badge:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    /* Progress Bar Animations */
    .progress-bar {
        transition: width 0.8s ease-in-out;
        animation: progressFill 1.2s ease-out;
        position: relative;
        overflow: hidden;
    }

    .progress-bar::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        animation: progressShine 2s ease-in-out infinite;
    }

    @@keyframes progressFill {
        from { width: 0%; }
        to { width: var(--progress-width); }
    }

    @@keyframes progressShine {
        0% { transform: translateX(-100%); }
        50% { transform: translateX(100%); }
        100% { transform: translateX(100%); }
    }

    /* Comment Section Styles */
    .comment-card {
        transition: all 0.2s ease;
        border-left: 3px solid transparent;
    }

    .comment-card:hover {
        border-left-color: var(--primary-500);
        background-color: var(--neutral-50);
        transform: translateX(2px);
    }

    .dark .comment-card:hover {
        background-color: var(--dark-700);
    }

    /* Comment Form Enhancements */
    .comment-form {
        transition: all 0.3s ease;
    }

    .comment-form:focus-within {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        border-color: var(--primary-500);
    }

    /* Attachment Card Styles */
    .attachment-card {
        transition: all 0.2s ease;
        border: 1px solid var(--neutral-200);
    }

    .attachment-card:hover {
        border-color: var(--primary-300);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-1px);
    }

    .dark .attachment-card {
        border-color: var(--dark-300);
    }

    .dark .attachment-card:hover {
        border-color: var(--primary-600);
    }

    /* Subtask Card Styles */
    .subtask-card {
        transition: all 0.2s ease;
        border-left: 3px solid var(--neutral-200);
    }

    .subtask-card:hover {
        border-left-color: var(--primary-500);
        background-color: var(--neutral-50);
        transform: translateX(2px);
    }

    .dark .subtask-card {
        border-left-color: var(--dark-300);
    }

    .dark .subtask-card:hover {
        border-left-color: var(--primary-400);
        background-color: var(--dark-700);
    }

    /* Assignment Card Styles */
    .assignment-card {
        transition: all 0.3s ease;
    }

    .assignment-card:hover {
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    }

    /* Modal Enhancements */
    .modal-content {
        animation: modalSlideIn 0.3s ease-out;
    }

    @@keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    /* Loading States */
    .loading-spinner {
        animation: spin 1s linear infinite;
    }

    @@keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* Fade In Animation for Dynamic Content */
    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .fade-in-up {
        animation: fadeInUp 0.5s ease-out;
    }

    /* Responsive Adjustments */
    @@media (max-width: 768px) {
        .task-details-container {
            padding: 1rem;
        }

        .comment-card:hover,
        .subtask-card:hover,
        .metadata-card:hover {
            transform: none;
        }

        /* Stack metadata cards on mobile */
        .metadata-card {
            min-height: 4rem;
        }

        /* Adjust grid for mobile */
        .grid-cols-2.md\\:grid-cols-4.xl\\:grid-cols-6 {
            grid-template-columns: repeat(1, minmax(0, 1fr));
        }

        @@media (min-width: 640px) {
            .grid-cols-2.md\\:grid-cols-4.xl\\:grid-cols-6 {
                grid-template-columns: repeat(2, minmax(0, 1fr));
            }
        }
    }

    /* Tablet adjustments */
    @@media (min-width: 768px) and (max-width: 1279px) {
        .grid-cols-2.md\\:grid-cols-4.xl\\:grid-cols-6 {
            grid-template-columns: repeat(3, minmax(0, 1fr));
        }
    }

    /* Large desktop - full 6 columns */
    @@media (min-width: 1280px) {
        .grid-cols-2.md\\:grid-cols-4.xl\\:grid-cols-6 {
            grid-template-columns: repeat(6, minmax(0, 1fr));
        }
    }

    /* Print Styles */
    @@media print {
        .task-details-container {
            box-shadow: none;
            border: none;
        }

        .comment-form,
        .modal-content,
        button {
            display: none !important;
        }
    }
</style>
