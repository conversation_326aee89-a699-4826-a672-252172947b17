using System;
using PM.Tool.Core.Enums;

namespace PM.Tool.Models.ViewModels
{
    public class ProjectSummaryViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public ProjectStatus Status { get; set; }
        public DateTime? EndDate { get; set; }
        public double ProgressPercentage { get; set; }
        public bool IsOverdue { get; set; }
        public int MemberCount { get; set; }
        public int TaskCount { get; set; }
    }
}
