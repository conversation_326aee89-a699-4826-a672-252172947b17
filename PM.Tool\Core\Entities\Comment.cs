using PM.Tool.Core.Helpers;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PM.Tool.Core.Entities
{
    public class Comment : BaseEntity
    {        [Required]
        public string Content { get; set; } = string.Empty;
        
        [Required]
        public string AuthorId { get; set; } = string.Empty;
        public virtual ApplicationUser Author { get; set; } = null!;
        
        public int TaskId { get; set; }
        public virtual TaskEntity Task { get; set; } = null!;
        
        public bool IsEdited => UpdatedAt.HasValue;
        
        [NotMapped]
        public string FormattedContent => MarkdownHelper.FormatMarkdown(Content);
    }
}