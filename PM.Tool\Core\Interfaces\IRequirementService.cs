using PM.Tool.Core.Entities;

namespace PM.Tool.Core.Interfaces
{
    public interface IRequirementService
    {
        // Requirement Management
        Task<IEnumerable<Requirement>> GetAllRequirementsAsync();
        Task<IEnumerable<Requirement>> GetProjectRequirementsAsync(int projectId);
        Task<IEnumerable<Requirement>> GetRequirementsByStatusAsync(RequirementStatus status, int? projectId = null);
        Task<Requirement?> GetRequirementByIdAsync(int id);
        Task<Requirement> CreateRequirementAsync(Requirement requirement);
        Task<Requirement> UpdateRequirementAsync(Requirement requirement);
        Task<bool> DeleteRequirementAsync(int id);
        
        // Requirement Hierarchy
        Task<IEnumerable<Requirement>> GetRootRequirementsAsync(int projectId);
        Task<IEnumerable<Requirement>> GetChildRequirementsAsync(int parentRequirementId);
        Task<bool> MoveRequirementAsync(int requirementId, int? newParentId);
        Task<IEnumerable<Requirement>> GetRequirementHierarchyAsync(int projectId);
        
        // Requirement Approval
        Task<bool> SubmitForApprovalAsync(int requirementId);
        Task<bool> ApproveRequirementAsync(int requirementId, string approverUserId, string? comments = null);
        Task<bool> RejectRequirementAsync(int requirementId, string approverUserId, string reason);
        Task<IEnumerable<Requirement>> GetRequirementsPendingApprovalAsync(string approverUserId);
        
        // Requirement Tracking
        Task<bool> UpdateRequirementStatusAsync(int requirementId, RequirementStatus status);
        Task<IEnumerable<Requirement>> GetOverdueRequirementsAsync(int projectId);
        Task<IEnumerable<Requirement>> GetBlockedRequirementsAsync(int projectId);
        Task<bool> BlockRequirementAsync(int requirementId, string reason);
        Task<bool> UnblockRequirementAsync(int requirementId);
        
        // Requirement Comments
        Task<IEnumerable<RequirementComment>> GetRequirementCommentsAsync(int requirementId);
        Task<RequirementComment> AddCommentAsync(int requirementId, string userId, string content, CommentType type = CommentType.General);
        Task<bool> DeleteCommentAsync(int commentId);
        
        // Requirement Attachments
        Task<IEnumerable<RequirementAttachment>> GetRequirementAttachmentsAsync(int requirementId);
        Task<RequirementAttachment> AddAttachmentAsync(int requirementId, string fileName, string filePath, string contentType, long fileSize, string uploadedByUserId, string? description = null);
        Task<bool> RemoveAttachmentAsync(int attachmentId);
        
        // Change Management
        Task<IEnumerable<RequirementChange>> GetRequirementChangesAsync(int requirementId);
        Task<RequirementChange> RequestChangeAsync(RequirementChange change);
        Task<bool> ApproveChangeAsync(int changeId, string approverUserId);
        Task<bool> RejectChangeAsync(int changeId, string approverUserId, string reason);
        Task<IEnumerable<RequirementChange>> GetPendingChangesAsync(int projectId);
        
        // Requirement-Task Linking
        Task<IEnumerable<TaskEntity>> GetRequirementTasksAsync(int requirementId);
        Task<bool> LinkTaskToRequirementAsync(int requirementId, int taskId);
        Task<bool> UnlinkTaskFromRequirementAsync(int requirementId, int taskId);
        Task<IEnumerable<Requirement>> GetTaskRequirementsAsync(int taskId);
        
        // Requirements Analysis
        Task<Dictionary<RequirementType, int>> GetRequirementTypeDistributionAsync(int projectId);
        Task<Dictionary<RequirementStatus, int>> GetRequirementStatusDistributionAsync(int projectId);
        Task<Dictionary<RequirementPriority, int>> GetRequirementPriorityDistributionAsync(int projectId);
        Task<double> GetRequirementCompletionPercentageAsync(int projectId);
        Task<decimal> GetTotalEstimatedEffortAsync(int projectId);
        Task<IEnumerable<Requirement>> GetHighPriorityRequirementsAsync(int projectId);
        
        // Requirements Traceability
        Task<Dictionary<string, object>> GetRequirementTraceabilityAsync(int requirementId);
        Task<IEnumerable<object>> GetRequirementImpactAnalysisAsync(int requirementId);
        Task<bool> ValidateRequirementDependenciesAsync(int projectId);
        
        // Requirements Import/Export
        Task<bool> ImportRequirementsAsync(int projectId, IEnumerable<Requirement> requirements);
        Task<IEnumerable<Requirement>> ExportRequirementsAsync(int projectId, RequirementStatus? status = null);
        
        // Requirements Search and Filtering
        Task<IEnumerable<Requirement>> SearchRequirementsAsync(string searchTerm, int? projectId = null);
        Task<IEnumerable<Requirement>> FilterRequirementsAsync(int projectId, RequirementType? type = null, RequirementPriority? priority = null, RequirementStatus? status = null, string? assignedToUserId = null);
        
        // Requirements Reporting
        Task<Dictionary<string, object>> GetRequirementsReportAsync(int projectId);
        Task<IEnumerable<object>> GetRequirementProgressReportAsync(int projectId);
        Task<IEnumerable<object>> GetRequirementVelocityReportAsync(int projectId, DateTime startDate, DateTime endDate);
    }
}
