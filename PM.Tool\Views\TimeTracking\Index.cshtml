@model TimeTrackingViewModel
@{
    ViewData["Title"] = "Time Tracking";
}

<div class="container-fluid">
    <h2>Time Tracking</h2>

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger">
            @TempData["ErrorMessage"]
        </div>
    }

    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Active Time Entry</h5>
                </div>
                <div class="card-body">
                    @if (Model.ActiveTimeEntry != null)
                    {
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6>@Model.ActiveTimeEntry.TaskTitle</h6>
                                <p class="text-muted mb-0">
                                    Started: @Model.ActiveTimeEntry.StartTime.ToString("g")
                                </p>
                                @if (!string.IsNullOrEmpty(Model.ActiveTimeEntry.Description))
                                {
                                    <p class="text-muted mb-0">@Model.ActiveTimeEntry.Description</p>
                                }
                            </div>
                            <form asp-action="Stop" method="post" class="d-inline">
                                @Html.AntiForgeryToken()
                                <input type="hidden" name="timeEntryId" value="@Model.ActiveTimeEntry.Id" />
                                <button type="submit" class="btn btn-danger">Stop Timer</button>
                            </form>
                        </div>
                    }
                    else
                    {
                        <p class="mb-0">No active time entry</p>
                    }
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Recent Time Entries</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Task</th>
                                    <th>Start Time</th>
                                    <th>End Time</th>
                                    <th>Duration</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var entry in Model.TimeEntries)
                                {
                                    <tr>
                                        <td>
                                            <a asp-controller="Tasks" asp-action="Details" asp-route-id="@entry.TaskId">
                                                @entry.TaskTitle
                                            </a>
                                        </td>
                                        <td>@entry.StartTime.ToString("g")</td>
                                        <td>@(entry.EndTime?.ToString("g") ?? "-")</td>
                                        <td>@(entry.Duration.ToString("F1")) hours</td>
                                        <td>@(entry.Description ?? "-")</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Auto-refresh the page every minute to update the active timer
        if (@(Model.ActiveTimeEntry != null ? "true" : "false")) {
            setTimeout(function() {
                window.location.reload();
            }, 60000);
        }
    </script>
}
