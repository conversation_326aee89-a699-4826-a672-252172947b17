@model NewVersionViewModel
@{
    ViewData["Title"] = "Upload Document";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Upload Document</h2>
        <a asp-action="Index" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-2"></i>Back to List
        </a>
    </div>

    <div class="row">
        <div class="col-md-8 col-lg-6">
            <div class="card">
                <div class="card-body">
                    <form asp-action="AddVersion" method="post" enctype="multipart/form-data">
                        <input type="hidden" asp-for="AttachmentId" />
                        <input type="hidden" asp-for="AttachmentType" />

                        <div class="mb-3">
                            <label asp-for="File" class="form-label">Select File</label>
                            <input asp-for="File" type="file" class="form-control" required />
                            <span asp-validation-for="File" class="text-danger"></span>
                            <small class="form-text text-muted">
                                Maximum file size: 10MB. Supported formats: PDF, Word, Excel, PowerPoint, Images.
                            </small>
                        </div>

                        <div class="mb-3">
                            <label asp-for="ChangeDescription" class="form-label">Change Description</label>
                            <textarea asp-for="ChangeDescription" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="ChangeDescription" class="text-danger"></span>
                            <small class="form-text text-muted">
                                Briefly describe the contents or changes in this version of the document.
                            </small>
                        </div>

                        <div class="alert alert-info" role="alert">
                            <i class="bi bi-info-circle me-2"></i>
                            This document will be uploaded as version 1.0.
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-cloud-upload me-2"></i>Upload Document
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4 col-lg-6">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Tips for Document Upload</h5>
                    <ul class="list-unstyled">
                        <li class="mb-3">
                            <i class="bi bi-check2-circle text-success me-2"></i>
                            Use descriptive filenames that clearly identify the document's content
                        </li>
                        <li class="mb-3">
                            <i class="bi bi-check2-circle text-success me-2"></i>
                            Add a meaningful change description to help others understand the document's purpose
                        </li>
                        <li class="mb-3">
                            <i class="bi bi-check2-circle text-success me-2"></i>
                            Consider categorizing your document for better organization
                        </li>
                        <li class="mb-3">
                            <i class="bi bi-check2-circle text-success me-2"></i>
                            Remember to update the document when changes are made
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
