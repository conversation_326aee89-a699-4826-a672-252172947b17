@model MyTasksViewModel
@using PM.Tool.Core.Enums
@using TaskStatus = PM.Tool.Core.Enums.TaskStatus

<!-- Tasks Table View -->
<div id="listView" class="view-content">
    @if (Model.Tasks.Any())
    {
        <!-- Bulk Actions Toolbar -->
        <div id="bulkActionsToolbar" class="hidden bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-700 rounded-lg p-3 mb-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <span id="selectedCount" class="text-sm font-medium text-primary-700 dark:text-primary-300">0 selected</span>
                    <div class="h-4 w-px bg-primary-300 dark:bg-primary-600"></div>
                    <div class="flex space-x-2">
                        <button type="button" class="bulk-action-btn px-3 py-1.5 text-xs font-medium text-primary-700 dark:text-primary-300 hover:bg-primary-100 dark:hover:bg-primary-800 rounded-md transition-colors" data-action="start">
                            <i class="fas fa-play mr-1"></i>Start
                        </button>
                        <button type="button" class="bulk-action-btn px-3 py-1.5 text-xs font-medium text-primary-700 dark:text-primary-300 hover:bg-primary-100 dark:hover:bg-primary-800 rounded-md transition-colors" data-action="complete">
                            <i class="fas fa-check mr-1"></i>Complete
                        </button>
                        <button type="button" class="bulk-action-btn px-3 py-1.5 text-xs font-medium text-primary-700 dark:text-primary-300 hover:bg-primary-100 dark:hover:bg-primary-800 rounded-md transition-colors" data-action="assign">
                            <i class="fas fa-user mr-1"></i>Assign
                        </button>
                    </div>
                </div>
                <button type="button" id="clearSelection" class="text-xs text-neutral-500 dark:text-dark-400 hover:text-neutral-700 dark:hover:text-dark-200">
                    Clear selection
                </button>
            </div>
        </div>

        <!-- Tasks Table -->
        <div class="bg-white dark:bg-dark-800 border border-neutral-200 dark:border-dark-300 rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-neutral-50 dark:bg-dark-700 border-b border-neutral-200 dark:border-dark-300">
                        <tr>
                            <th class="w-8 px-4 py-3">
                                <input type="checkbox" id="selectAll" class="rounded border-neutral-300 dark:border-dark-400 text-primary-600 focus:ring-primary-500">
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wider">
                                <button type="button" class="flex items-center space-x-1 hover:text-neutral-700 dark:hover:text-dark-200" onclick="sortTable('title')">
                                    <span>Title</span>
                                    <i class="fas fa-sort text-xs"></i>
                                </button>
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wider">
                                <button type="button" class="flex items-center space-x-1 hover:text-neutral-700 dark:hover:text-dark-200" onclick="sortTable('status')">
                                    <span>Status</span>
                                    <i class="fas fa-sort text-xs"></i>
                                </button>
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wider">
                                <button type="button" class="flex items-center space-x-1 hover:text-neutral-700 dark:hover:text-dark-200" onclick="sortTable('priority')">
                                    <span>Priority</span>
                                    <i class="fas fa-sort text-xs"></i>
                                </button>
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wider">
                                <button type="button" class="flex items-center space-x-1 hover:text-neutral-700 dark:hover:text-dark-200" onclick="sortTable('project')">
                                    <span>Project</span>
                                    <i class="fas fa-sort text-xs"></i>
                                </button>
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wider">
                                <button type="button" class="flex items-center space-x-1 hover:text-neutral-700 dark:hover:text-dark-200" onclick="sortTable('assignee')">
                                    <span>Assignee</span>
                                    <i class="fas fa-sort text-xs"></i>
                                </button>
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wider">
                                <button type="button" class="flex items-center space-x-1 hover:text-neutral-700 dark:hover:text-dark-200" onclick="sortTable('dueDate')">
                                    <span>Due Date</span>
                                    <i class="fas fa-sort text-xs"></i>
                                </button>
                            </th>
                            <th class="px-4 py-3 text-center text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wider w-20">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-dark-800 divide-y divide-neutral-200 dark:divide-dark-300">
                        @foreach (var task in Model.Tasks)
                        {
                            <tr class="task-row hover:bg-neutral-50 dark:hover:bg-dark-700 transition-colors @(task.IsOverdue ? "bg-red-50 dark:bg-red-900/20" : "")" data-task-id="@task.Id">
                                <td class="px-4 py-3">
                                    <input type="checkbox" class="task-checkbox rounded border-neutral-300 dark:border-dark-400 text-primary-600 focus:ring-primary-500" value="@task.Id">
                                </td>
                                <td class="px-4 py-3">
                                    <div class="flex items-center space-x-3">
                                        <div class="flex-1 min-w-0">
                                            <a href="@Url.Action("Details", new { id = task.Id })" class="text-sm font-medium text-neutral-900 dark:text-dark-100 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                                                @task.Title
                                            </a>
                                            @if (!string.IsNullOrEmpty(task.Description))
                                            {
                                                <p class="text-xs text-neutral-500 dark:text-dark-400 truncate mt-1">@task.Description</p>
                                            }
                                            @if (task.SubTaskCount > 0)
                                            {
                                                <div class="flex items-center mt-1">
                                                    <i class="fas fa-tasks text-xs text-neutral-400 dark:text-dark-500 mr-1"></i>
                                                    <span class="text-xs text-neutral-500 dark:text-dark-400">@task.CompletedSubTasks/@task.SubTaskCount subtasks</span>
                                                    <div class="w-12 h-1 bg-neutral-200 dark:bg-dark-600 rounded-full ml-2">
                                                        <div class="h-1 bg-primary-500 rounded-full" style="width: @task.Progress%"></div>
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    @{
                                        var statusClass = task.Status.ToString().ToLower() switch {
                                            "todo" => "bg-neutral-100 text-neutral-800 dark:bg-dark-600 dark:text-dark-200",
                                            "inprogress" => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
                                            "done" => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
                                            "cancelled" => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
                                            _ => "bg-neutral-100 text-neutral-800 dark:bg-dark-600 dark:text-dark-200"
                                        };
                                    }
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium @statusClass">
                                        @task.Status.ToString()
                                    </span>
                                </td>
                                <td class="px-4 py-3">
                                    @{
                                        var priorityClass = task.Priority.ToString().ToLower() switch {
                                            "critical" => "text-red-600 dark:text-red-400",
                                            "high" => "text-orange-600 dark:text-orange-400",
                                            "medium" => "text-yellow-600 dark:text-yellow-400",
                                            "low" => "text-green-600 dark:text-green-400",
                                            _ => "text-neutral-600 dark:text-neutral-400"
                                        };

                                        var priorityIcon = task.Priority.ToString().ToLower() switch {
                                            "critical" => "fas fa-exclamation-triangle",
                                            "high" => "fas fa-flag",
                                            "medium" => "fas fa-minus",
                                            "low" => "fas fa-arrow-down",
                                            _ => "fas fa-minus"
                                        };
                                    }
                                    <div class="flex items-center space-x-1">
                                        <i class="@priorityIcon @priorityClass text-xs"></i>
                                        <span class="text-sm text-neutral-900 dark:text-dark-100">@task.Priority</span>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="text-sm text-neutral-900 dark:text-dark-100">@task.ProjectName</span>
                                </td>
                                <td class="px-4 py-3">
                                    @if (!string.IsNullOrEmpty(task.AssignedToName))
                                    {
                                        <div class="flex items-center space-x-2">
                                            <div class="w-6 h-6 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                                                <span class="text-xs font-medium text-primary-600 dark:text-primary-400">
                                                    @task.AssignedToName.First().ToString().ToUpper()
                                                </span>
                                            </div>
                                            <span class="text-sm text-neutral-900 dark:text-dark-100">@task.AssignedToName</span>
                                        </div>
                                    }
                                    else
                                    {
                                        <span class="text-sm text-neutral-500 dark:text-dark-400">Unassigned</span>
                                    }
                                </td>
                                <td class="px-4 py-3">
                                    @if (task.DueDate.HasValue)
                                    {
                                        <div class="flex items-center space-x-1 @(task.IsOverdue ? "text-red-600 dark:text-red-400" : "text-neutral-900 dark:text-dark-100")">
                                            <span class="text-sm">@task.DueDate.Value.ToString("MMM dd, yyyy")</span>
                                            @if (task.IsOverdue)
                                            {
                                                <i class="fas fa-exclamation-triangle text-xs"></i>
                                            }
                                        </div>
                                    }
                                    else
                                    {
                                        <span class="text-sm text-neutral-500 dark:text-dark-400">No due date</span>
                                    }
                                </td>
                                <td class="px-4 py-3">
                                    <div class="flex items-center justify-center space-x-1">
                                        <!-- Quick Status Actions -->
                                        @if (task.Status != TaskStatus.InProgress)
                                        {
                                            <button type="button" class="p-1 text-neutral-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                                                    onclick="updateTaskStatus(@task.Id, 'InProgress')"
                                                    title="Start Task">
                                                <i class="fas fa-play text-xs"></i>
                                            </button>
                                        }
                                        @if (task.Status != TaskStatus.Done)
                                        {
                                            <button type="button" class="p-1 text-neutral-400 hover:text-green-600 dark:hover:text-green-400 transition-colors"
                                                    onclick="updateTaskStatus(@task.Id, 'Done')"
                                                    title="Mark Complete">
                                                <i class="fas fa-check text-xs"></i>
                                            </button>
                                        }

                                        <!-- More Actions Dropdown -->
                                        <div class="relative">
                                            <button type="button" class="p-1 text-neutral-400 hover:text-neutral-600 dark:hover:text-dark-300 transition-colors"
                                                    onclick="toggleTaskActions(@task.Id)"
                                                    title="More Actions">
                                                <i class="fas fa-ellipsis-h text-xs"></i>
                                            </button>
                                            <div id="<EMAIL>" class="hidden absolute right-0 mt-1 w-48 bg-white dark:bg-dark-700 border border-neutral-200 dark:border-dark-300 rounded-md shadow-lg z-10">
                                                <div class="py-1">
                                                    <a href="@Url.Action("Edit", new { id = task.Id })" class="block px-4 py-2 text-sm text-neutral-700 dark:text-dark-300 hover:bg-neutral-100 dark:hover:bg-dark-600">
                                                        <i class="fas fa-edit mr-2"></i>Edit
                                                    </a>
                                                    <a href="@Url.Action("Details", new { id = task.Id })" class="block px-4 py-2 text-sm text-neutral-700 dark:text-dark-300 hover:bg-neutral-100 dark:hover:bg-dark-600">
                                                        <i class="fas fa-eye mr-2"></i>View Details
                                                    </a>
                                                    @if (task.ParentTaskId == null)
                                                    {
                                                        <a href="@Url.Action("Create", new { parentTaskId = task.Id })" class="block px-4 py-2 text-sm text-neutral-700 dark:text-dark-300 hover:bg-neutral-100 dark:hover:bg-dark-600">
                                                            <i class="fas fa-plus mr-2"></i>Add Subtask
                                                        </a>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        @if (Model.Pagination.TotalPages > 1)
        {
            <partial name="Partials/_MyTasksPagination" model="Model.Pagination" />
        }
    }
    else
    {
        <!-- Empty State -->
        <partial name="Partials/_MyTasksEmptyState" model="Model" />
    }
</div>
