@model MyTasksViewModel
@using PM.Tool.Core.Enums
@using TaskStatus = PM.Tool.Core.Enums.TaskStatus

<!-- Tasks List View -->
<div id="listView" class="view-content">
    @if (Model.Tasks.Any())
    {
        <div class="space-y-4">
            @foreach (var task in Model.Tasks)
            {
                <partial name="Partials/_TaskCard" model="task" />
            }
        </div>

        <!-- Pagination -->
        @if (Model.Pagination.TotalPages > 1)
        {
            <partial name="Partials/_MyTasksPagination" model="Model.Pagination" />
        }
    }
    else
    {
        <!-- Empty State -->
        <partial name="Partials/_MyTasksEmptyState" model="Model" />
    }
</div>
