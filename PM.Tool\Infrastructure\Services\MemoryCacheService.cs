using Microsoft.Extensions.Caching.Memory;
using PM.Tool.Core.Interfaces;

namespace PM.Tool.Infrastructure.Services
{
    public class MemoryCacheService : ICacheService
    {
        private readonly IMemoryCache _memoryCache;
        private readonly MemoryCacheEntryOptions _defaultOptions;

        public MemoryCacheService(IMemoryCache memoryCache)
        {
            _memoryCache = memoryCache;
            _defaultOptions = new MemoryCacheEntryOptions()
                .SetSlidingExpiration(TimeSpan.FromMinutes(30))
                .SetAbsoluteExpiration(TimeSpan.FromHours(12));
        }

        public T Get<T>(string key, Func<T> factory, TimeSpan? expiration = null)
        {
            return _memoryCache.GetOrCreate(key, (ICacheEntry entry) =>
            {
                entry.AbsoluteExpirationRelativeToNow = expiration ?? TimeSpan.FromMinutes(5);
                return factory();
            });
        }

        public async Task<T> GetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null)
        {
            return await _memoryCache.GetOrCreateAsync(key, async (ICacheEntry entry) =>
            {
                entry.AbsoluteExpirationRelativeToNow = expiration ?? TimeSpan.FromMinutes(5);
                return await factory();
            });
        }

        public T? Get<T>(string key)
        {
            return _memoryCache.Get<T>(key);
        }

        public void Set<T>(string key, T value, TimeSpan? expiration = null)
        {
            var options = expiration.HasValue
                ? new MemoryCacheEntryOptions().SetAbsoluteExpiration(expiration.Value)
                : _defaultOptions;

            _memoryCache.Set(key, value, options);
        }

        public void Remove(string key)
        {
            _memoryCache.Remove(key);
        }        public async Task<T> GetOrCreateAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null)
        {            return await _memoryCache.GetOrCreateAsync(key, async (ICacheEntry entry) =>
            {
                entry.SetOptions(expiration.HasValue
                    ? new MemoryCacheEntryOptions().SetAbsoluteExpiration(expiration.Value)
                    : _defaultOptions);

                return await factory();
            });
        }

        public Task<T?> GetAsync<T>(string key)
        {
            return Task.FromResult(Get<T>(key));
        }

        public Task SetAsync<T>(string key, T value, TimeSpan? expiration = null)
        {
            Set(key, value, expiration);
            return Task.CompletedTask;
        }

        public Task RemoveAsync(string key)
        {
            Remove(key);
            return Task.CompletedTask;
        }

        public Task RemoveByPrefixAsync(string prefix)
        {
            var cacheKeys = typeof(MemoryCache).GetProperty("EntriesCollection", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)?
                .GetValue(_memoryCache) as dynamic;

            var keys = ((IEnumerable<KeyValuePair<object, object>>)cacheKeys)
                        .Where(x => x.Key.ToString()!.StartsWith(prefix))
                        .Select(x => x.Key.ToString()!)
                        .ToList();


            if (keys != null)
            {
                foreach (var key in keys)
                {
                    Remove(key);
                }
            }

            return Task.CompletedTask;
        }
    }
}
