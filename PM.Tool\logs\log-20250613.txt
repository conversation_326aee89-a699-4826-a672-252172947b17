2025-06-13 22:37:52.467 +06:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-13 22:37:54.464 +06:00 [INF] Executed DbCommand (55ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-13 22:37:54.526 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-13 22:37:54.533 +06:00 [INF] Executed DbCommand (3ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-13 22:37:54.547 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__normalizedEmail_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
2025-06-13 22:37:54.911 +06:00 [INF] Executed DbCommand (14ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE t."DueDate" IS NOT NULL AND t."DueDate" <= @__upcomingDeadlines_0 AND t."DueDate" > now() AND t."Status" <> 4
2025-06-13 22:37:55.020 +06:00 [INF] Executed DbCommand (10ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", p0."Id", p0."IsActive", p0."JoinedAt", p0."ProjectId", p0."Role", p0."UserId"
FROM "Projects" AS p
LEFT JOIN "ProjectMembers" AS p0 ON p."Id" = p0."ProjectId"
WHERE p."EndDate" IS NOT NULL AND p."EndDate" <= @__upcomingDeadlines_0 AND p."EndDate" > now() AND p."Status" <> 5
ORDER BY p."Id"
2025-06-13 22:37:55.029 +06:00 [INF] Deadline check completed at: "2025-06-13T22:37:55.0283674+06:00"
2025-06-13 22:37:55.108 +06:00 [INF] Now listening on: https://localhost:7029
2025-06-13 22:37:55.111 +06:00 [INF] Now listening on: http://localhost:5045
2025-06-13 22:37:55.147 +06:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-13 22:37:55.149 +06:00 [INF] Hosting environment: Development
2025-06-13 22:37:55.151 +06:00 [INF] Content root path: D:\TGI\PM.Tool\PM.Tool
2025-06-13 22:37:56.070 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/ - null null
2025-06-13 22:37:56.320 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-13 22:37:56.336 +06:00 [INF] Route matched with {action = "Index", controller = "Home", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-13 22:37:56.385 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-13 22:37:56.424 +06:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-13 22:37:56.463 +06:00 [INF] Executed DbCommand (20ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-13 22:37:56.583 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-13 22:37:56.607 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" < now()
ORDER BY t."DueDate"
2025-06-13 22:37:56.628 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__dueDate_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
FROM "Tasks" AS t
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" <= @__dueDate_0
2025-06-13 22:37:56.659 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT n."Id", n."Title", n."Message", n."Type", n."IsRead", n."CreatedAt", n."RelatedProjectId", n."RelatedTaskId"
FROM "Notifications" AS n
WHERE n."UserId" = @__user_Id_0
ORDER BY n."CreatedAt" DESC
LIMIT @__p_1
2025-06-13 22:37:56.689 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."Name", m."Description", m."ProjectId", p."Name", m."DueDate", m."CompletedDate", m."IsCompleted", m."CreatedAt", m."IsDeleted", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."StartDate", p."Status", p."UpdatedAt"
FROM "Milestones" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
WHERE EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__user_Id_0 AND p0."IsActive") AND NOT (m."IsCompleted") AND m."DueDate" >= now()
ORDER BY m."DueDate"
LIMIT @__p_1
2025-06-13 22:37:56.701 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-13 22:37:56.826 +06:00 [INF] Executed ViewResult - view Index executed in 123.3975ms.
2025-06-13 22:37:56.836 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.Index (PM.Tool) in 497.9768ms
2025-06-13 22:37:56.840 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-13 22:37:56.851 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/ - 200 null text/html; charset=utf-8 790.0371ms
2025-06-13 22:37:56.879 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 22:37:56.879 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-13 22:37:56.891 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 11.9761ms
2025-06-13 22:37:56.923 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 44.3349ms
2025-06-13 22:39:01.916 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Risk - null null
2025-06-13 22:39:01.969 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-13 22:39:01.989 +06:00 [INF] Route matched with {action = "Index", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-13 22:39:02.333 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-13 22:39:02.440 +06:00 [INF] Executed ViewResult - view Index executed in 108.5501ms.
2025-06-13 22:39:02.445 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Index (PM.Tool) in 450.3431ms
2025-06-13 22:39:02.447 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-13 22:39:02.448 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Risk - 200 null text/html; charset=utf-8 532.1469ms
2025-06-13 22:39:02.459 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 22:39:02.459 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-13 22:39:02.461 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 2.412ms
2025-06-13 22:39:02.479 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Projects/GetProjects - null null
2025-06-13 22:39:02.527 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 67.7765ms
2025-06-13 22:39:02.547 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:39:02.555 +06:00 [INF] Route matched with {action = "GetProjects", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProjects() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-13 22:39:02.570 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-13 22:39:02.586 +06:00 [INF] Executed DbCommand (11ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-13 22:39:02.595 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType39`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-13 22:39:02.602 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool) in 44.5705ms
2025-06-13 22:39:02.604 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:39:02.605 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Projects/GetProjects - 200 null application/json; charset=utf-8 125.7549ms
2025-06-13 22:39:03.816 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Risk/Create - null null
2025-06-13 22:39:03.823 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Create (PM.Tool)'
2025-06-13 22:39:03.829 +06:00 [INF] Route matched with {action = "Create", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Create(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-13 22:39:03.887 +06:00 [INF] Executing ViewResult, running view Create.
2025-06-13 22:39:03.952 +06:00 [INF] Executed ViewResult - view Create executed in 65.3279ms.
2025-06-13 22:39:03.955 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Create (PM.Tool) in 122.298ms
2025-06-13 22:39:03.957 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Create (PM.Tool)'
2025-06-13 22:39:03.959 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Risk/Create - 200 null text/html; charset=utf-8 143.6904ms
2025-06-13 22:39:03.963 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 22:39:03.964 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-13 22:39:03.972 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 8.9428ms
2025-06-13 22:39:03.979 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 14.9227ms
2025-06-13 22:39:04.027 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Projects/GetUsers - null null
2025-06-13 22:39:04.027 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Projects/GetProjects - null null
2025-06-13 22:39:04.033 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetUsers (PM.Tool)'
2025-06-13 22:39:04.035 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:39:04.038 +06:00 [INF] Route matched with {action = "GetProjects", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProjects() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-13 22:39:04.039 +06:00 [INF] Route matched with {action = "GetUsers", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetUsers() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-13 22:39:04.047 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-13 22:39:04.114 +06:00 [ERR] Error loading users for dropdown
System.InvalidOperationException: The LINQ expression 'DbSet<ApplicationUser>()
    .Where(a => a.IsActive)
    .OrderBy(a => a.FullName)' could not be translated. Additional information: Translation of member 'FullName' on entity type 'ApplicationUser' failed. This commonly occurs when the specified member is unmapped. Either rewrite the query in a form that can be translated, or switch to client evaluation explicitly by inserting a call to 'AsEnumerable', 'AsAsyncEnumerable', 'ToList', or 'ToListAsync'. See https://go.microsoft.com/fwlink/?linkid=2101038 for more information.
   at Microsoft.EntityFrameworkCore.Query.QueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at PM.Tool.Controllers.ProjectsController.GetUsers() in D:\TGI\PM.Tool\PM.Tool\Controllers\ProjectsController.cs:line 453
2025-06-13 22:39:04.126 +06:00 [INF] Executed DbCommand (11ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-13 22:39:04.157 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-13 22:39:04.159 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetUsers (PM.Tool) in 114.2394ms
2025-06-13 22:39:04.159 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType39`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-13 22:39:04.161 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetUsers (PM.Tool)'
2025-06-13 22:39:04.162 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool) in 120.8603ms
2025-06-13 22:39:04.163 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Projects/GetUsers - 200 null application/json; charset=utf-8 135.6756ms
2025-06-13 22:39:04.164 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:39:04.169 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Projects/GetProjects - 200 null application/json; charset=utf-8 142.1231ms
2025-06-13 22:39:31.265 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Risk - null null
2025-06-13 22:39:31.309 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-13 22:39:31.315 +06:00 [INF] Route matched with {action = "Index", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-13 22:39:31.410 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-13 22:39:31.418 +06:00 [INF] Executed ViewResult - view Index executed in 8.0513ms.
2025-06-13 22:39:31.420 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Index (PM.Tool) in 100.9399ms
2025-06-13 22:39:31.423 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-13 22:39:31.425 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Risk - 200 null text/html; charset=utf-8 160.0938ms
2025-06-13 22:39:31.428 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 22:39:31.430 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-13 22:39:31.472 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 43.7625ms
2025-06-13 22:39:31.482 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Projects/GetProjects - null null
2025-06-13 22:39:31.559 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 129.4312ms
2025-06-13 22:39:31.562 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:39:31.573 +06:00 [INF] Route matched with {action = "GetProjects", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProjects() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-13 22:39:31.583 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-13 22:39:31.599 +06:00 [INF] Executed DbCommand (10ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-13 22:39:31.606 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType39`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-13 22:39:31.607 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool) in 30.8694ms
2025-06-13 22:39:31.608 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:39:31.610 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Projects/GetProjects - 200 null application/json; charset=utf-8 128.6071ms
2025-06-13 22:43:11.918 +06:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-13 22:43:13.736 +06:00 [INF] Executed DbCommand (44ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-13 22:43:13.800 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-13 22:43:13.809 +06:00 [INF] Executed DbCommand (3ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-13 22:43:13.826 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__normalizedEmail_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
2025-06-13 22:43:14.237 +06:00 [INF] Executed DbCommand (15ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE t."DueDate" IS NOT NULL AND t."DueDate" <= @__upcomingDeadlines_0 AND t."DueDate" > now() AND t."Status" <> 4
2025-06-13 22:43:14.350 +06:00 [INF] Executed DbCommand (10ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", p0."Id", p0."IsActive", p0."JoinedAt", p0."ProjectId", p0."Role", p0."UserId"
FROM "Projects" AS p
LEFT JOIN "ProjectMembers" AS p0 ON p."Id" = p0."ProjectId"
WHERE p."EndDate" IS NOT NULL AND p."EndDate" <= @__upcomingDeadlines_0 AND p."EndDate" > now() AND p."Status" <> 5
ORDER BY p."Id"
2025-06-13 22:43:14.395 +06:00 [INF] Deadline check completed at: "2025-06-13T22:43:14.3940399+06:00"
2025-06-13 22:43:14.475 +06:00 [INF] Now listening on: https://localhost:7029
2025-06-13 22:43:14.477 +06:00 [INF] Now listening on: http://localhost:5045
2025-06-13 22:43:14.525 +06:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-13 22:43:14.525 +06:00 [INF] Hosting environment: Development
2025-06-13 22:43:14.527 +06:00 [INF] Content root path: D:\TGI\PM.Tool\PM.Tool
2025-06-13 22:43:15.333 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/ - null null
2025-06-13 22:43:15.608 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-13 22:43:15.636 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__user_Id_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ClaimType", a."ClaimValue", a."UserId"
FROM "AspNetUserClaims" AS a
WHERE a."UserId" = @__user_Id_0
2025-06-13 22:43:15.659 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a0."Name"
FROM "AspNetUserRoles" AS a
INNER JOIN "AspNetRoles" AS a0 ON a."RoleId" = a0."Id"
WHERE a."UserId" = @__userId_0
2025-06-13 22:43:15.667 +06:00 [INF] Executed DbCommand (3ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-13 22:43:15.678 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__role_Id_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."ClaimType", a."ClaimValue"
FROM "AspNetRoleClaims" AS a
WHERE a."RoleId" = @__role_Id_0
2025-06-13 22:43:15.690 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-13 22:43:15.712 +06:00 [INF] Route matched with {action = "Index", controller = "Home", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-13 22:43:15.764 +06:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-13 22:43:15.816 +06:00 [INF] Executed DbCommand (34ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-13 22:43:15.935 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-13 22:43:15.960 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" < now()
ORDER BY t."DueDate"
2025-06-13 22:43:15.980 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__dueDate_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
FROM "Tasks" AS t
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" <= @__dueDate_0
2025-06-13 22:43:16.005 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT n."Id", n."Title", n."Message", n."Type", n."IsRead", n."CreatedAt", n."RelatedProjectId", n."RelatedTaskId"
FROM "Notifications" AS n
WHERE n."UserId" = @__user_Id_0
ORDER BY n."CreatedAt" DESC
LIMIT @__p_1
2025-06-13 22:43:16.030 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."Name", m."Description", m."ProjectId", p."Name", m."DueDate", m."CompletedDate", m."IsCompleted", m."CreatedAt", m."IsDeleted", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."StartDate", p."Status", p."UpdatedAt"
FROM "Milestones" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
WHERE EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__user_Id_0 AND p0."IsActive") AND NOT (m."IsCompleted") AND m."DueDate" >= now()
ORDER BY m."DueDate"
LIMIT @__p_1
2025-06-13 22:43:16.042 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-13 22:43:16.159 +06:00 [INF] Executed ViewResult - view Index executed in 117.4973ms.
2025-06-13 22:43:16.166 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.Index (PM.Tool) in 449.3374ms
2025-06-13 22:43:16.170 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-13 22:43:16.179 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/ - 200 null text/html; charset=utf-8 856.7657ms
2025-06-13 22:43:16.204 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 22:43:16.212 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-13 22:43:16.215 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 10.7414ms
2025-06-13 22:43:16.254 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 41.2492ms
2025-06-13 22:43:56.673 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Risk - null null
2025-06-13 22:43:56.726 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-13 22:43:56.746 +06:00 [INF] Route matched with {action = "Index", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-13 22:43:56.923 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-13 22:43:57.002 +06:00 [INF] Executed ViewResult - view Index executed in 80.3252ms.
2025-06-13 22:43:57.005 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Index (PM.Tool) in 251.2735ms
2025-06-13 22:43:57.006 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-13 22:43:57.008 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Risk - 200 null text/html; charset=utf-8 365.166ms
2025-06-13 22:43:57.021 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-13 22:43:57.021 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 22:43:57.032 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 10.8231ms
2025-06-13 22:43:57.046 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Projects/GetProjects - null null
2025-06-13 22:43:57.047 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 25.8453ms
2025-06-13 22:43:57.077 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:43:57.085 +06:00 [INF] Route matched with {action = "GetProjects", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProjects() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-13 22:43:57.102 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-13 22:43:57.117 +06:00 [INF] Executed DbCommand (10ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-13 22:43:57.124 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType39`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-13 22:43:57.134 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool) in 44.8588ms
2025-06-13 22:43:57.136 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:43:57.137 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Projects/GetProjects - 200 null application/json; charset=utf-8 91.7802ms
2025-06-13 22:44:01.713 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Risk/Create - null null
2025-06-13 22:44:01.723 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Create (PM.Tool)'
2025-06-13 22:44:01.733 +06:00 [INF] Route matched with {action = "Create", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Create(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-13 22:44:01.790 +06:00 [INF] Executing ViewResult, running view Create.
2025-06-13 22:44:01.845 +06:00 [INF] Executed ViewResult - view Create executed in 55.1993ms.
2025-06-13 22:44:01.849 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Create (PM.Tool) in 111.6081ms
2025-06-13 22:44:01.852 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Create (PM.Tool)'
2025-06-13 22:44:01.854 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Risk/Create - 200 null text/html; charset=utf-8 140.5255ms
2025-06-13 22:44:01.855 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 22:44:01.857 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-13 22:44:01.862 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 7.5792ms
2025-06-13 22:44:01.869 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 11.789ms
2025-06-13 22:44:01.900 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Projects/GetUsers - null null
2025-06-13 22:44:01.900 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Projects/GetProjects - null null
2025-06-13 22:44:01.906 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetUsers (PM.Tool)'
2025-06-13 22:44:01.909 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:44:01.910 +06:00 [INF] Route matched with {action = "GetProjects", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProjects() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-13 22:44:01.912 +06:00 [INF] Route matched with {action = "GetUsers", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetUsers() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-13 22:44:01.924 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-13 22:44:01.944 +06:00 [INF] Executed DbCommand (13ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-13 22:44:01.949 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType39`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-13 22:44:01.951 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool) in 38.9259ms
2025-06-13 22:44:01.953 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:44:01.954 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Projects/GetProjects - 200 null application/json; charset=utf-8 54.7153ms
2025-06-13 22:44:01.990 +06:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id" AS id, a."UserName" AS "userName", a."Email" AS email, a."FirstName" || ' ' || a."LastName" AS "fullName", a."FirstName" AS "firstName", a."LastName" AS "lastName"
FROM "AspNetUsers" AS a
WHERE a."IsActive"
ORDER BY a."FirstName", a."LastName"
2025-06-13 22:44:01.997 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType40`6[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-13 22:44:02.008 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetUsers (PM.Tool) in 93.0272ms
2025-06-13 22:44:02.013 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetUsers (PM.Tool)'
2025-06-13 22:44:02.016 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Projects/GetUsers - 200 null application/json; charset=utf-8 116.9363ms
2025-06-13 22:44:30.013 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Risk - null null
2025-06-13 22:44:30.029 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-13 22:44:30.032 +06:00 [INF] Route matched with {action = "Index", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-13 22:44:30.129 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-13 22:44:30.172 +06:00 [INF] Executed ViewResult - view Index executed in 42.8724ms.
2025-06-13 22:44:30.181 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Index (PM.Tool) in 145.9329ms
2025-06-13 22:44:30.185 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-13 22:44:30.187 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Risk - 200 null text/html; charset=utf-8 173.9969ms
2025-06-13 22:44:30.194 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 22:44:30.194 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-13 22:44:30.197 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 3.295ms
2025-06-13 22:44:30.206 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Projects/GetProjects - null null
2025-06-13 22:44:30.211 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 16.9045ms
2025-06-13 22:44:30.213 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:44:30.220 +06:00 [INF] Route matched with {action = "GetProjects", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProjects() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-13 22:44:30.230 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-13 22:44:30.251 +06:00 [INF] Executed DbCommand (17ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-13 22:44:30.256 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType39`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-13 22:44:30.259 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool) in 36.0526ms
2025-06-13 22:44:30.261 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:44:30.263 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Projects/GetProjects - 200 null application/json; charset=utf-8 57.2409ms
2025-06-13 22:46:11.774 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Risk/Matrix - null null
2025-06-13 22:46:11.792 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Matrix (PM.Tool)'
2025-06-13 22:46:11.798 +06:00 [INF] Route matched with {action = "Matrix", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Matrix(Int32) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-13 22:46:11.912 +06:00 [INF] Executing RedirectResult, redirecting to /Risk?projectId=0.
2025-06-13 22:46:11.917 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Matrix (PM.Tool) in 115.0374ms
2025-06-13 22:46:11.920 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Matrix (PM.Tool)'
2025-06-13 22:46:11.921 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Risk/Matrix - 302 0 null 147.1532ms
2025-06-13 22:46:11.925 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Risk?projectId=0 - null null
2025-06-13 22:46:11.930 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-13 22:46:11.931 +06:00 [INF] Route matched with {action = "Index", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-13 22:46:12.027 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-13 22:46:12.070 +06:00 [INF] Executed ViewResult - view Index executed in 43.0232ms.
2025-06-13 22:46:12.073 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Index (PM.Tool) in 140.2264ms
2025-06-13 22:46:12.076 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-13 22:46:12.077 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Risk?projectId=0 - 200 null text/html; charset=utf-8 151.4336ms
2025-06-13 22:46:12.086 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 22:46:12.086 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-13 22:46:12.090 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 3.9613ms
2025-06-13 22:46:12.101 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Projects/GetProjects - null null
2025-06-13 22:46:12.107 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 20.5499ms
2025-06-13 22:46:12.158 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:46:12.164 +06:00 [INF] Route matched with {action = "GetProjects", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProjects() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-13 22:46:12.173 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-13 22:46:12.187 +06:00 [INF] Executed DbCommand (11ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-13 22:46:12.200 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType39`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-13 22:46:12.204 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool) in 38.9851ms
2025-06-13 22:46:12.209 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:46:12.215 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Projects/GetProjects - 200 null application/json; charset=utf-8 113.6479ms
2025-06-13 22:46:16.324 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Risk/Analytics - null null
2025-06-13 22:46:16.332 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Analytics (PM.Tool)'
2025-06-13 22:46:16.341 +06:00 [INF] Route matched with {action = "Analytics", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Analytics(Int32) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-13 22:46:16.443 +06:00 [INF] Executed DbCommand (56ms) [Parameters=[@__projectId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Category", count(*)::int AS "Count"
FROM "Risks" AS r
WHERE r."ProjectId" = @__projectId_0 AND NOT (r."IsDeleted")
GROUP BY r."Category"
2025-06-13 22:46:16.475 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__projectId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."ActualResolutionDate", r."Category", r."ContingencyPlan", r."CreatedAt", r."CreatedByUserId", r."DeletedAt", r."Description", r."EstimatedCost", r."EstimatedDelayDays", r."IdentifiedDate", r."Impact", r."IsDeleted", r."MitigationPlan", r."OwnerId", r."Probability", r."ProjectId", r."Status", r."TargetResolutionDate", r."TaskId", r."Title", r."UpdatedAt"
FROM "Risks" AS r
WHERE r."ProjectId" = @__projectId_0 AND NOT (r."IsDeleted")
2025-06-13 22:46:16.503 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__projectId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."ActualResolutionDate", r."Category", r."ContingencyPlan", r."CreatedAt", r."CreatedByUserId", r."DeletedAt", r."Description", r."EstimatedCost", r."EstimatedDelayDays", r."IdentifiedDate", r."Impact", r."IsDeleted", r."MitigationPlan", r."OwnerId", r."Probability", r."ProjectId", r."Status", r."TargetResolutionDate", r."TaskId", r."Title", r."UpdatedAt"
FROM "Risks" AS r
WHERE r."ProjectId" = @__projectId_0 AND NOT (r."IsDeleted") AND r."Status" <> 5
2025-06-13 22:46:16.651 +06:00 [INF] Executing RedirectResult, redirecting to /Risk?projectId=0.
2025-06-13 22:46:16.655 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Analytics (PM.Tool) in 309.337ms
2025-06-13 22:46:16.660 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Analytics (PM.Tool)'
2025-06-13 22:46:16.662 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Risk/Analytics - 302 0 null 337.7864ms
2025-06-13 22:46:16.665 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Risk?projectId=0 - null null
2025-06-13 22:46:16.673 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-13 22:46:16.674 +06:00 [INF] Route matched with {action = "Index", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-13 22:46:16.766 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-13 22:46:16.773 +06:00 [INF] Executed ViewResult - view Index executed in 7.5928ms.
2025-06-13 22:46:16.776 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Index (PM.Tool) in 100.5616ms
2025-06-13 22:46:16.779 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-13 22:46:16.781 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Risk?projectId=0 - 200 null text/html; charset=utf-8 115.6699ms
2025-06-13 22:46:16.784 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 22:46:16.784 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-13 22:46:16.791 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 7.1545ms
2025-06-13 22:46:16.801 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 16.9263ms
2025-06-13 22:46:16.825 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Projects/GetProjects - null null
2025-06-13 22:46:16.830 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:46:16.833 +06:00 [INF] Route matched with {action = "GetProjects", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProjects() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-13 22:46:16.846 +06:00 [INF] Executed DbCommand (10ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-13 22:46:16.865 +06:00 [INF] Executed DbCommand (14ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-13 22:46:16.869 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType39`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-13 22:46:16.871 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool) in 35.6384ms
2025-06-13 22:46:16.873 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:46:16.875 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Projects/GetProjects - 200 null application/json; charset=utf-8 49.7575ms
2025-06-13 22:51:58.065 +06:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-13 22:51:59.984 +06:00 [INF] Executed DbCommand (45ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-13 22:52:00.046 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-13 22:52:00.055 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-13 22:52:00.070 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__normalizedEmail_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
2025-06-13 22:52:00.460 +06:00 [INF] Executed DbCommand (16ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE t."DueDate" IS NOT NULL AND t."DueDate" <= @__upcomingDeadlines_0 AND t."DueDate" > now() AND t."Status" <> 4
2025-06-13 22:52:00.509 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", p0."Id", p0."IsActive", p0."JoinedAt", p0."ProjectId", p0."Role", p0."UserId"
FROM "Projects" AS p
LEFT JOIN "ProjectMembers" AS p0 ON p."Id" = p0."ProjectId"
WHERE p."EndDate" IS NOT NULL AND p."EndDate" <= @__upcomingDeadlines_0 AND p."EndDate" > now() AND p."Status" <> 5
ORDER BY p."Id"
2025-06-13 22:52:00.546 +06:00 [INF] Deadline check completed at: "2025-06-13T22:52:00.5449600+06:00"
2025-06-13 22:52:00.604 +06:00 [INF] Now listening on: https://localhost:7029
2025-06-13 22:52:00.607 +06:00 [INF] Now listening on: http://localhost:5045
2025-06-13 22:52:00.646 +06:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-13 22:52:00.647 +06:00 [INF] Hosting environment: Development
2025-06-13 22:52:00.649 +06:00 [INF] Content root path: D:\TGI\PM.Tool\PM.Tool
2025-06-13 22:52:01.902 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/ - null null
2025-06-13 22:52:02.202 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-13 22:52:02.218 +06:00 [INF] Route matched with {action = "Index", controller = "Home", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-13 22:52:02.263 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-13 22:52:02.300 +06:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-13 22:52:02.331 +06:00 [INF] Executed DbCommand (14ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-13 22:52:02.450 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-13 22:52:02.478 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" < now()
ORDER BY t."DueDate"
2025-06-13 22:52:02.496 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__dueDate_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
FROM "Tasks" AS t
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" <= @__dueDate_0
2025-06-13 22:52:02.528 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT n."Id", n."Title", n."Message", n."Type", n."IsRead", n."CreatedAt", n."RelatedProjectId", n."RelatedTaskId"
FROM "Notifications" AS n
WHERE n."UserId" = @__user_Id_0
ORDER BY n."CreatedAt" DESC
LIMIT @__p_1
2025-06-13 22:52:02.556 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."Name", m."Description", m."ProjectId", p."Name", m."DueDate", m."CompletedDate", m."IsCompleted", m."CreatedAt", m."IsDeleted", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."StartDate", p."Status", p."UpdatedAt"
FROM "Milestones" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
WHERE EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__user_Id_0 AND p0."IsActive") AND NOT (m."IsCompleted") AND m."DueDate" >= now()
ORDER BY m."DueDate"
LIMIT @__p_1
2025-06-13 22:52:02.569 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-13 22:52:02.697 +06:00 [INF] Executed ViewResult - view Index executed in 123.2891ms.
2025-06-13 22:52:02.705 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.Index (PM.Tool) in 483.8943ms
2025-06-13 22:52:02.710 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-13 22:52:02.721 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/ - 200 null text/html; charset=utf-8 829.0401ms
2025-06-13 22:52:02.744 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 22:52:02.744 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-13 22:52:02.753 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 8.4924ms
2025-06-13 22:52:02.786 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 41.7621ms
2025-06-13 22:52:08.150 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Risk - null null
2025-06-13 22:52:08.170 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-13 22:52:08.189 +06:00 [INF] Route matched with {action = "Index", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-13 22:52:08.421 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-13 22:52:08.474 +06:00 [INF] Executed ViewResult - view Index executed in 55.0615ms.
2025-06-13 22:52:08.476 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Index (PM.Tool) in 283.1797ms
2025-06-13 22:52:08.477 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-13 22:52:08.480 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Risk - 200 null text/html; charset=utf-8 330.1669ms
2025-06-13 22:52:08.488 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 22:52:08.488 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-13 22:52:08.496 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 7.4722ms
2025-06-13 22:52:08.524 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 36.1022ms
2025-06-13 22:52:08.540 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Projects/GetProjects - null null
2025-06-13 22:52:08.560 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:52:08.567 +06:00 [INF] Route matched with {action = "GetProjects", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProjects() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-13 22:52:08.582 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-13 22:52:08.838 +06:00 [INF] Executed DbCommand (253ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-13 22:52:08.855 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType39`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-13 22:52:08.872 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool) in 302.3371ms
2025-06-13 22:52:08.877 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:52:08.882 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Projects/GetProjects - 200 null application/json; charset=utf-8 342.0936ms
2025-06-13 22:52:10.048 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Risk/Create - null null
2025-06-13 22:52:10.056 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Create (PM.Tool)'
2025-06-13 22:52:10.060 +06:00 [INF] Route matched with {action = "Create", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Create(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-13 22:52:10.136 +06:00 [INF] Executing ViewResult, running view Create.
2025-06-13 22:52:10.211 +06:00 [INF] Executed ViewResult - view Create executed in 76.3166ms.
2025-06-13 22:52:10.213 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Create (PM.Tool) in 149.3098ms
2025-06-13 22:52:10.216 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Create (PM.Tool)'
2025-06-13 22:52:10.218 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 22:52:10.219 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Risk/Create - 200 null text/html; charset=utf-8 170.9536ms
2025-06-13 22:52:10.221 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-13 22:52:10.222 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 3.9323ms
2025-06-13 22:52:10.237 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 16.4667ms
2025-06-13 22:52:10.268 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Projects/GetProjects - null null
2025-06-13 22:52:10.268 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Projects/GetUsers - null null
2025-06-13 22:52:10.276 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:52:10.280 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetUsers (PM.Tool)'
2025-06-13 22:52:10.280 +06:00 [INF] Route matched with {action = "GetProjects", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProjects() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-13 22:52:10.285 +06:00 [INF] Route matched with {action = "GetUsers", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetUsers() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-13 22:52:10.297 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-13 22:52:10.312 +06:00 [INF] Executed DbCommand (10ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-13 22:52:10.317 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType39`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-13 22:52:10.319 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool) in 36.137ms
2025-06-13 22:52:10.321 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:52:10.322 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Projects/GetProjects - 200 null application/json; charset=utf-8 53.8653ms
2025-06-13 22:52:10.406 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id" AS id, a."UserName" AS "userName", a."Email" AS email, a."FirstName" || ' ' || a."LastName" AS "fullName", a."FirstName" AS "firstName", a."LastName" AS "lastName"
FROM "AspNetUsers" AS a
WHERE a."IsActive"
ORDER BY a."FirstName", a."LastName"
2025-06-13 22:52:10.413 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType40`6[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-13 22:52:10.419 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetUsers (PM.Tool) in 127.2193ms
2025-06-13 22:52:10.425 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetUsers (PM.Tool)'
2025-06-13 22:52:10.428 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Projects/GetUsers - 200 null application/json; charset=utf-8 159.3264ms
2025-06-13 22:54:40.053 +06:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-13 22:57:35.898 +06:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-13 22:57:37.661 +06:00 [INF] Executed DbCommand (44ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-13 22:57:37.732 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-13 22:57:37.741 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-13 22:57:37.761 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__normalizedEmail_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
2025-06-13 22:57:38.125 +06:00 [INF] Executed DbCommand (14ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE t."DueDate" IS NOT NULL AND t."DueDate" <= @__upcomingDeadlines_0 AND t."DueDate" > now() AND t."Status" <> 4
2025-06-13 22:57:38.267 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", p0."Id", p0."IsActive", p0."JoinedAt", p0."ProjectId", p0."Role", p0."UserId"
FROM "Projects" AS p
LEFT JOIN "ProjectMembers" AS p0 ON p."Id" = p0."ProjectId"
WHERE p."EndDate" IS NOT NULL AND p."EndDate" <= @__upcomingDeadlines_0 AND p."EndDate" > now() AND p."Status" <> 5
ORDER BY p."Id"
2025-06-13 22:57:38.277 +06:00 [INF] Deadline check completed at: "2025-06-13T22:57:38.2758981+06:00"
2025-06-13 22:57:38.401 +06:00 [INF] Now listening on: https://localhost:7029
2025-06-13 22:57:38.404 +06:00 [INF] Now listening on: http://localhost:5045
2025-06-13 22:57:38.446 +06:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-13 22:57:38.448 +06:00 [INF] Hosting environment: Development
2025-06-13 22:57:38.451 +06:00 [INF] Content root path: D:\TGI\PM.Tool\PM.Tool
2025-06-13 22:57:39.234 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/ - null null
2025-06-13 22:57:39.433 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-13 22:57:39.450 +06:00 [INF] Route matched with {action = "Index", controller = "Home", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-13 22:57:39.500 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-13 22:57:39.543 +06:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-13 22:57:39.572 +06:00 [INF] Executed DbCommand (13ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-13 22:57:39.689 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-13 22:57:39.719 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" < now()
ORDER BY t."DueDate"
2025-06-13 22:57:39.738 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__dueDate_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
FROM "Tasks" AS t
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" <= @__dueDate_0
2025-06-13 22:57:39.764 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT n."Id", n."Title", n."Message", n."Type", n."IsRead", n."CreatedAt", n."RelatedProjectId", n."RelatedTaskId"
FROM "Notifications" AS n
WHERE n."UserId" = @__user_Id_0
ORDER BY n."CreatedAt" DESC
LIMIT @__p_1
2025-06-13 22:57:39.791 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."Name", m."Description", m."ProjectId", p."Name", m."DueDate", m."CompletedDate", m."IsCompleted", m."CreatedAt", m."IsDeleted", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."StartDate", p."Status", p."UpdatedAt"
FROM "Milestones" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
WHERE EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__user_Id_0 AND p0."IsActive") AND NOT (m."IsCompleted") AND m."DueDate" >= now()
ORDER BY m."DueDate"
LIMIT @__p_1
2025-06-13 22:57:39.805 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-13 22:57:39.938 +06:00 [INF] Executed ViewResult - view Index executed in 133.143ms.
2025-06-13 22:57:39.946 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.Index (PM.Tool) in 492.7459ms
2025-06-13 22:57:39.950 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-13 22:57:39.959 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/ - 200 null text/html; charset=utf-8 732.8961ms
2025-06-13 22:57:40.729 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 22:57:40.729 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-13 22:57:40.739 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 10.4837ms
2025-06-13 22:57:40.771 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 42.3606ms
2025-06-13 22:57:44.150 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Risk - null null
2025-06-13 22:57:44.174 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-13 22:57:44.190 +06:00 [INF] Route matched with {action = "Index", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-13 22:57:44.412 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-13 22:57:44.464 +06:00 [INF] Executed ViewResult - view Index executed in 54.522ms.
2025-06-13 22:57:44.466 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Index (PM.Tool) in 274.0816ms
2025-06-13 22:57:44.468 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-13 22:57:44.472 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Risk - 200 null text/html; charset=utf-8 321.5481ms
2025-06-13 22:57:44.480 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 22:57:44.480 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-13 22:57:44.489 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 8.9405ms
2025-06-13 22:57:44.588 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Projects/GetProjects - null null
2025-06-13 22:57:44.589 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 109.4602ms
2025-06-13 22:57:44.617 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:57:44.624 +06:00 [INF] Route matched with {action = "GetProjects", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProjects() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-13 22:57:44.642 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-13 22:57:44.660 +06:00 [INF] Executed DbCommand (14ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-13 22:57:44.667 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType39`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-13 22:57:44.679 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool) in 50.2574ms
2025-06-13 22:57:44.682 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:57:44.682 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Projects/GetProjects - 200 null application/json; charset=utf-8 94.0245ms
2025-06-13 22:57:46.367 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Risk/Create - null null
2025-06-13 22:57:46.374 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Create (PM.Tool)'
2025-06-13 22:57:46.383 +06:00 [INF] Route matched with {action = "Create", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Create(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-13 22:57:46.455 +06:00 [INF] Executing ViewResult, running view Create.
2025-06-13 22:57:46.515 +06:00 [INF] Executed ViewResult - view Create executed in 61.539ms.
2025-06-13 22:57:46.518 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Create (PM.Tool) in 129.3818ms
2025-06-13 22:57:46.521 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Create (PM.Tool)'
2025-06-13 22:57:46.521 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 22:57:46.523 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Risk/Create - 200 null text/html; charset=utf-8 156.6323ms
2025-06-13 22:57:46.523 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-13 22:57:46.526 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 4.7191ms
2025-06-13 22:57:46.544 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 21.065ms
2025-06-13 22:57:46.568 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Projects/GetProjects - null null
2025-06-13 22:57:46.568 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Projects/GetUsers - null null
2025-06-13 22:57:46.578 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:57:46.579 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetUsers (PM.Tool)'
2025-06-13 22:57:46.581 +06:00 [INF] Route matched with {action = "GetProjects", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProjects() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-13 22:57:46.591 +06:00 [INF] Route matched with {action = "GetUsers", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetUsers() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-13 22:57:46.598 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-13 22:57:46.622 +06:00 [INF] Executed DbCommand (14ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-13 22:57:46.626 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType39`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-13 22:57:46.628 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool) in 43.4981ms
2025-06-13 22:57:46.630 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:57:46.631 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Projects/GetProjects - 200 null application/json; charset=utf-8 62.5772ms
2025-06-13 22:57:46.662 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id" AS id, a."UserName" AS "userName", a."Email" AS email, a."FirstName" || ' ' || a."LastName" AS "fullName", a."FirstName" AS "firstName", a."LastName" AS "lastName"
FROM "AspNetUsers" AS a
WHERE a."IsActive"
ORDER BY a."FirstName", a."LastName"
2025-06-13 22:57:46.671 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType40`6[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-13 22:57:46.680 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetUsers (PM.Tool) in 84.8111ms
2025-06-13 22:57:46.683 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetUsers (PM.Tool)'
2025-06-13 22:57:46.685 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Projects/GetUsers - 200 null application/json; charset=utf-8 116.2839ms
2025-06-13 22:58:16.081 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Risk - null null
2025-06-13 22:58:16.090 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-13 22:58:16.094 +06:00 [INF] Route matched with {action = "Index", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-13 22:58:16.182 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-13 22:58:16.226 +06:00 [INF] Executed ViewResult - view Index executed in 44.2114ms.
2025-06-13 22:58:16.229 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Index (PM.Tool) in 133.243ms
2025-06-13 22:58:16.230 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-13 22:58:16.231 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Risk - 200 null text/html; charset=utf-8 149.4039ms
2025-06-13 22:58:16.241 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-13 22:58:16.241 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 22:58:16.247 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 5.791ms
2025-06-13 22:58:16.283 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Projects/GetProjects - null null
2025-06-13 22:58:16.284 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 43.0708ms
2025-06-13 22:58:16.294 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:58:16.299 +06:00 [INF] Route matched with {action = "GetProjects", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProjects() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-13 22:58:16.311 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-13 22:58:16.336 +06:00 [INF] Executed DbCommand (22ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-13 22:58:16.343 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType39`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-13 22:58:16.348 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool) in 44.592ms
2025-06-13 22:58:16.352 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-13 22:58:16.355 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Projects/GetProjects - 200 null application/json; charset=utf-8 72.3045ms
2025-06-13 23:12:27.605 +06:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-13 23:12:29.467 +06:00 [INF] Executed DbCommand (49ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-13 23:12:29.540 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-13 23:12:29.551 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-13 23:12:29.576 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__normalizedEmail_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
2025-06-13 23:12:30.048 +06:00 [INF] Executed DbCommand (12ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE t."DueDate" IS NOT NULL AND t."DueDate" <= @__upcomingDeadlines_0 AND t."DueDate" > now() AND t."Status" <> 4
2025-06-13 23:12:30.142 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", p0."Id", p0."IsActive", p0."JoinedAt", p0."ProjectId", p0."Role", p0."UserId"
FROM "Projects" AS p
LEFT JOIN "ProjectMembers" AS p0 ON p."Id" = p0."ProjectId"
WHERE p."EndDate" IS NOT NULL AND p."EndDate" <= @__upcomingDeadlines_0 AND p."EndDate" > now() AND p."Status" <> 5
ORDER BY p."Id"
2025-06-13 23:12:30.155 +06:00 [INF] Deadline check completed at: "2025-06-13T23:12:30.1530036+06:00"
2025-06-13 23:12:30.256 +06:00 [INF] Now listening on: https://localhost:7029
2025-06-13 23:12:30.258 +06:00 [INF] Now listening on: http://localhost:5045
2025-06-13 23:12:30.303 +06:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-13 23:12:30.306 +06:00 [INF] Hosting environment: Development
2025-06-13 23:12:30.307 +06:00 [INF] Content root path: D:\TGI\PM.Tool\PM.Tool
2025-06-13 23:12:31.164 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/ - null null
2025-06-13 23:12:31.451 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-13 23:12:31.465 +06:00 [INF] Route matched with {action = "Index", controller = "Home", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-13 23:12:31.514 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-13 23:12:31.566 +06:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-13 23:12:31.594 +06:00 [INF] Executed DbCommand (11ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-13 23:12:31.716 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-13 23:12:31.741 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" < now()
ORDER BY t."DueDate"
2025-06-13 23:12:31.763 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__dueDate_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
FROM "Tasks" AS t
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" <= @__dueDate_0
2025-06-13 23:12:31.802 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT n."Id", n."Title", n."Message", n."Type", n."IsRead", n."CreatedAt", n."RelatedProjectId", n."RelatedTaskId"
FROM "Notifications" AS n
WHERE n."UserId" = @__user_Id_0
ORDER BY n."CreatedAt" DESC
LIMIT @__p_1
2025-06-13 23:12:31.833 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."Name", m."Description", m."ProjectId", p."Name", m."DueDate", m."CompletedDate", m."IsCompleted", m."CreatedAt", m."IsDeleted", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."StartDate", p."Status", p."UpdatedAt"
FROM "Milestones" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
WHERE EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__user_Id_0 AND p0."IsActive") AND NOT (m."IsCompleted") AND m."DueDate" >= now()
ORDER BY m."DueDate"
LIMIT @__p_1
2025-06-13 23:12:31.845 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-13 23:12:31.985 +06:00 [INF] Executed ViewResult - view Index executed in 137.8551ms.
2025-06-13 23:12:31.986 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/js/tailwind-theme-manager.js?v=QS9OsoodixBTZAmxGj2gDzPtp60VU7kbda0nOueg4T0 - null null
2025-06-13 23:12:31.986 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/css/tailwind.css?v=Vk1BkLOsT7-Yh4o1pDln8tZyZ3_rFi9yXuRHMX2g50E - null null
2025-06-13 23:12:31.992 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.Index (PM.Tool) in 522.6361ms
2025-06-13 23:12:31.999 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-13 23:12:32.000 +06:00 [INF] The file /css/tailwind.css was not modified
2025-06-13 23:12:32.000 +06:00 [INF] The file /js/tailwind-theme-manager.js was not modified
2025-06-13 23:12:32.006 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/css/tailwind.css?v=Vk1BkLOsT7-Yh4o1pDln8tZyZ3_rFi9yXuRHMX2g50E - 304 null text/css 20.4383ms
2025-06-13 23:12:32.006 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/js/tailwind-theme-manager.js?v=QS9OsoodixBTZAmxGj2gDzPtp60VU7kbda0nOueg4T0 - 304 null text/javascript 20.4467ms
2025-06-13 23:12:32.012 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/ - 200 null text/html; charset=utf-8 857.157ms
2025-06-13 23:12:32.035 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-13 23:12:32.035 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-13 23:12:32.052 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 16.9861ms
2025-06-13 23:12:32.081 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 46.1494ms
