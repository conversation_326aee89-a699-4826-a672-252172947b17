/**
 * Modern Kanban Board Component
 * Professional-grade drag & drop kanban board with animations and accessibility
 */

class KanbanBoard {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            columns: [
                { id: 1, title: 'Backlog', color: '#64748b' },
                { id: 2, title: 'To Do', color: '#3b82f6' },
                { id: 3, title: 'In Progress', color: '#f59e0b' },
                { id: 4, title: 'Review', color: '#8b5cf6' },
                { id: 5, title: 'Done', color: '#10b981' }
            ],
            apiEndpoint: '/api/kanban',
            enableDragDrop: true,
            enableAnimations: true,
            showCardDetails: true,
            ...options
        };
        
        this.data = [];
        this.draggedCard = null;
        this.draggedOverColumn = null;
        
        this.init();
    }
    
    init() {
        this.render();
        this.attachEventListeners();
        this.loadData();
    }
    
    render() {
        this.container.innerHTML = `
            <div class="kanban-board" dir="${document.dir || 'ltr'}">
                <div class="kanban-header">
                    <h2 class="kanban-title">${this.options.title || 'Kanban Board'}</h2>
                    <div class="kanban-controls">
                        <button class="btn-modern btn-primary" onclick="kanban.addCard()">
                            <i class="fas fa-plus"></i>
                            <span data-i18n="Common.Create">Add Card</span>
                        </button>
                        <button class="btn-modern btn-secondary" onclick="kanban.refresh()">
                            <i class="fas fa-sync-alt"></i>
                            <span data-i18n="Common.Refresh">Refresh</span>
                        </button>
                    </div>
                </div>
                <div class="kanban-columns">
                    ${this.options.columns.map(column => this.renderColumn(column)).join('')}
                </div>
            </div>
        `;
    }
    
    renderColumn(column) {
        return `
            <div class="kanban-column" data-column-id="${column.id}">
                <div class="kanban-column-header" style="border-top: 3px solid ${column.color}">
                    <h3 class="kanban-column-title">${column.title}</h3>
                    <span class="kanban-column-count">0</span>
                </div>
                <div class="kanban-column-body" 
                     data-column-id="${column.id}"
                     ondrop="kanban.handleDrop(event)" 
                     ondragover="kanban.handleDragOver(event)"
                     ondragenter="kanban.handleDragEnter(event)"
                     ondragleave="kanban.handleDragLeave(event)">
                    <div class="kanban-cards-container">
                        <!-- Cards will be inserted here -->
                    </div>
                    <div class="kanban-add-card" onclick="kanban.addCardToColumn(${column.id})">
                        <i class="fas fa-plus"></i>
                        <span data-i18n="Common.Create">Add a card</span>
                    </div>
                </div>
            </div>
        `;
    }
    
    renderCard(card) {
        const priorityClass = this.getPriorityClass(card.priority);
        const statusClass = this.getStatusClass(card.status);
        
        return `
            <div class="kanban-card ${statusClass}" 
                 data-card-id="${card.id}"
                 draggable="${this.options.enableDragDrop}"
                 ondragstart="kanban.handleDragStart(event)"
                 ondragend="kanban.handleDragEnd(event)"
                 onclick="kanban.openCardDetails(${card.id})">
                
                <div class="kanban-card-header">
                    <span class="kanban-card-id">${card.key || `#${card.id}`}</span>
                    <div class="kanban-card-priority ${priorityClass}">
                        ${this.getPriorityIcon(card.priority)}
                    </div>
                </div>
                
                <div class="kanban-card-content">
                    <h4 class="kanban-card-title">${this.escapeHtml(card.title)}</h4>
                    ${card.description ? `<p class="kanban-card-description">${this.escapeHtml(card.description.substring(0, 100))}${card.description.length > 100 ? '...' : ''}</p>` : ''}
                </div>
                
                <div class="kanban-card-footer">
                    <div class="kanban-card-meta">
                        ${card.assignedTo ? `
                            <div class="kanban-card-assignee" title="${card.assignedTo.name}">
                                <img src="${card.assignedTo.avatar || '/images/default-avatar.png'}" 
                                     alt="${card.assignedTo.name}" 
                                     class="kanban-avatar">
                            </div>
                        ` : ''}
                        
                        ${card.storyPoints ? `
                            <span class="kanban-story-points" title="Story Points">
                                <i class="fas fa-chart-bar"></i>
                                ${card.storyPoints}
                            </span>
                        ` : ''}
                        
                        ${card.dueDate ? `
                            <span class="kanban-due-date ${this.isDueSoon(card.dueDate) ? 'due-soon' : ''}" 
                                  title="Due Date">
                                <i class="fas fa-calendar"></i>
                                ${this.formatDate(card.dueDate)}
                            </span>
                        ` : ''}
                    </div>
                    
                    ${card.tags && card.tags.length > 0 ? `
                        <div class="kanban-card-tags">
                            ${card.tags.slice(0, 2).map(tag => `
                                <span class="kanban-tag">${tag}</span>
                            `).join('')}
                            ${card.tags.length > 2 ? `<span class="kanban-tag-more">+${card.tags.length - 2}</span>` : ''}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }
    
    async loadData() {
        try {
            this.showLoading();
            const response = await fetch(`${this.options.apiEndpoint}/board`);
            const data = await response.json();
            this.data = data;
            this.updateBoard();
        } catch (error) {
            console.error('Error loading kanban data:', error);
            this.showError('Failed to load kanban board data');
        } finally {
            this.hideLoading();
        }
    }
    
    updateBoard() {
        this.options.columns.forEach(column => {
            const columnElement = this.container.querySelector(`[data-column-id="${column.id}"] .kanban-cards-container`);
            const cards = this.data.filter(card => card.kanbanColumn === column.id);
            
            columnElement.innerHTML = cards.map(card => this.renderCard(card)).join('');
            
            // Update column count
            const countElement = this.container.querySelector(`[data-column-id="${column.id}"] .kanban-column-count`);
            countElement.textContent = cards.length;
        });
        
        // Apply animations if enabled
        if (this.options.enableAnimations) {
            this.animateCards();
        }
    }
    
    // Drag and Drop Handlers
    handleDragStart(event) {
        this.draggedCard = event.target;
        event.target.classList.add('dragging');
        event.dataTransfer.effectAllowed = 'move';
        event.dataTransfer.setData('text/html', event.target.outerHTML);
    }
    
    handleDragEnd(event) {
        event.target.classList.remove('dragging');
        this.draggedCard = null;
        this.clearDragOverStates();
    }
    
    handleDragOver(event) {
        event.preventDefault();
        event.dataTransfer.dropEffect = 'move';
    }
    
    handleDragEnter(event) {
        event.preventDefault();
        const column = event.target.closest('.kanban-column-body');
        if (column) {
            column.classList.add('drag-over');
            this.draggedOverColumn = column;
        }
    }
    
    handleDragLeave(event) {
        const column = event.target.closest('.kanban-column-body');
        if (column && !column.contains(event.relatedTarget)) {
            column.classList.remove('drag-over');
        }
    }
    
    async handleDrop(event) {
        event.preventDefault();
        
        if (!this.draggedCard) return;
        
        const targetColumn = event.target.closest('.kanban-column-body');
        if (!targetColumn) return;
        
        const cardId = this.draggedCard.dataset.cardId;
        const newColumnId = parseInt(targetColumn.dataset.columnId);
        
        try {
            await this.moveCard(cardId, newColumnId);
            this.clearDragOverStates();
        } catch (error) {
            console.error('Error moving card:', error);
            this.showError('Failed to move card');
        }
    }
    
    async moveCard(cardId, newColumnId) {
        const response = await fetch(`${this.options.apiEndpoint}/move`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                cardId: cardId,
                columnId: newColumnId
            })
        });
        
        if (!response.ok) {
            throw new Error('Failed to move card');
        }
        
        // Update local data
        const card = this.data.find(c => c.id == cardId);
        if (card) {
            card.kanbanColumn = newColumnId;
            this.updateBoard();
        }
        
        this.showSuccess('Card moved successfully');
    }
    
    // Utility Methods
    getPriorityClass(priority) {
        const priorityMap = {
            'Critical': 'priority-critical',
            'High': 'priority-high',
            'Medium': 'priority-medium',
            'Low': 'priority-low'
        };
        return priorityMap[priority] || 'priority-medium';
    }
    
    getPriorityIcon(priority) {
        const iconMap = {
            'Critical': '<i class="fas fa-exclamation-triangle"></i>',
            'High': '<i class="fas fa-arrow-up"></i>',
            'Medium': '<i class="fas fa-minus"></i>',
            'Low': '<i class="fas fa-arrow-down"></i>'
        };
        return iconMap[priority] || '<i class="fas fa-minus"></i>';
    }
    
    getStatusClass(status) {
        return `status-${status.toLowerCase().replace(/\s+/g, '-')}`;
    }
    
    isDueSoon(dueDate) {
        const due = new Date(dueDate);
        const now = new Date();
        const diffDays = Math.ceil((due - now) / (1000 * 60 * 60 * 24));
        return diffDays <= 3 && diffDays >= 0;
    }
    
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    clearDragOverStates() {
        this.container.querySelectorAll('.drag-over').forEach(el => {
            el.classList.remove('drag-over');
        });
    }
    
    animateCards() {
        const cards = this.container.querySelectorAll('.kanban-card');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${index * 50}ms`;
            card.classList.add('fade-in');
        });
    }
    
    // UI Feedback Methods
    showLoading() {
        const loader = document.createElement('div');
        loader.className = 'kanban-loading';
        loader.innerHTML = '<div class="loading-spinner"></div><span>Loading...</span>';
        this.container.appendChild(loader);
    }
    
    hideLoading() {
        const loader = this.container.querySelector('.kanban-loading');
        if (loader) loader.remove();
    }
    
    showSuccess(message) {
        this.showToast(message, 'success');
    }
    
    showError(message) {
        this.showToast(message, 'error');
    }
    
    showToast(message, type) {
        const toast = document.createElement('div');
        toast.className = `kanban-toast toast-${type}`;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }
    
    // Public API Methods
    refresh() {
        this.loadData();
    }
    
    addCard() {
        // This would open a modal or navigate to add card page
        window.location.href = '/UserStory/Create';
    }
    
    addCardToColumn(columnId) {
        // This would open a modal to add card directly to specific column
        window.location.href = `/UserStory/Create?column=${columnId}`;
    }
    
    openCardDetails(cardId) {
        // This would open card details modal or navigate to details page
        window.location.href = `/UserStory/Details/${cardId}`;
    }
    
    attachEventListeners() {
        // Global event listeners for keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'r':
                        e.preventDefault();
                        this.refresh();
                        break;
                    case 'n':
                        e.preventDefault();
                        this.addCard();
                        break;
                }
            }
        });
    }
}

// Global kanban instance
let kanban;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('kanban-container')) {
        kanban = new KanbanBoard('kanban-container', {
            title: 'Project Kanban Board',
            apiEndpoint: '/api/agile'
        });
    }
});
