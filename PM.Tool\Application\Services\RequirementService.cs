using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;

namespace PM.Tool.Application.Services
{
    public class RequirementService : IRequirementService
    {
        private readonly ApplicationDbContext _context;

        public RequirementService(ApplicationDbContext context)
        {
            _context = context;
        }

        // Requirement Management
        public async Task<IEnumerable<Requirement>> GetAllRequirementsAsync()
        {
            return await _context.Requirements
                .Include(r => r.Project)
                .Include(r => r.Developer)
                .Include(r => r.Stakeholder)
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Requirement>> GetProjectRequirementsAsync(int projectId)
        {
            return await _context.Requirements
                .Include(r => r.Project)
                .Include(r => r.Developer)
                .Include(r => r.Stakeholder)
                .Where(r => r.ProjectId == projectId)
                .OrderBy(r => r.Priority)
                .ThenBy(r => r.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Requirement>> GetRequirementsByStatusAsync(RequirementStatus status, int? projectId = null)
        {
            var query = _context.Requirements
                .Include(r => r.Project)
                .Include(r => r.Developer)
                .Where(r => r.Status == status);

            if (projectId.HasValue)
                query = query.Where(r => r.ProjectId == projectId.Value);

            return await query.OrderBy(r => r.Priority).ToListAsync();
        }

        public async Task<Requirement?> GetRequirementByIdAsync(int id)
        {
            return await _context.Requirements
                .Include(r => r.Project)
                .Include(r => r.Developer)
                .Include(r => r.Stakeholder)
                .Include(r => r.ParentRequirement)
                .Include(r => r.ChildRequirements)
                .FirstOrDefaultAsync(r => r.Id == id);
        }

        public async Task<Requirement> CreateRequirementAsync(Requirement requirement)
        {
            requirement.CreatedAt = DateTime.UtcNow;
            requirement.UpdatedAt = DateTime.UtcNow;

            _context.Requirements.Add(requirement);
            await _context.SaveChangesAsync();
            return requirement;
        }

        public async Task<Requirement> UpdateRequirementAsync(Requirement requirement)
        {
            requirement.UpdatedAt = DateTime.UtcNow;

            _context.Requirements.Update(requirement);
            await _context.SaveChangesAsync();
            return requirement;
        }

        public async Task<bool> DeleteRequirementAsync(int id)
        {
            var requirement = await _context.Requirements.FindAsync(id);
            if (requirement == null) return false;

            _context.Requirements.Remove(requirement);
            return await _context.SaveChangesAsync() > 0;
        }

        // Requirement Hierarchy
        public async Task<IEnumerable<Requirement>> GetRootRequirementsAsync(int projectId)
        {
            return await _context.Requirements
                .Include(r => r.Developer)
                .Include(r => r.Stakeholder)
                .Where(r => r.ProjectId == projectId && r.ParentRequirementId == null)
                .OrderBy(r => r.Priority)
                .ToListAsync();
        }

        public async Task<IEnumerable<Requirement>> GetChildRequirementsAsync(int parentRequirementId)
        {
            return await _context.Requirements
                .Include(r => r.Developer)
                .Include(r => r.Stakeholder)
                .Where(r => r.ParentRequirementId == parentRequirementId)
                .OrderBy(r => r.Priority)
                .ToListAsync();
        }

        public async Task<bool> MoveRequirementAsync(int requirementId, int? newParentId)
        {
            var requirement = await _context.Requirements.FindAsync(requirementId);
            if (requirement == null) return false;

            requirement.ParentRequirementId = newParentId;
            requirement.UpdatedAt = DateTime.UtcNow;

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<IEnumerable<Requirement>> GetRequirementHierarchyAsync(int projectId)
        {
            return await _context.Requirements
                .Include(r => r.Developer)
                .Include(r => r.Stakeholder)
                .Include(r => r.ChildRequirements)
                .Where(r => r.ProjectId == projectId)
                .OrderBy(r => r.ParentRequirementId ?? 0)
                .ThenBy(r => r.Priority)
                .ToListAsync();
        }

        // Requirement Approval
        public async Task<bool> SubmitForApprovalAsync(int requirementId)
        {
            var requirement = await _context.Requirements.FindAsync(requirementId);
            if (requirement == null) return false;

            requirement.Status = RequirementStatus.UnderReview;
            requirement.UpdatedAt = DateTime.UtcNow;

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> ApproveRequirementAsync(int requirementId, string approverUserId, string? comments = null)
        {
            var requirement = await _context.Requirements.FindAsync(requirementId);
            if (requirement == null) return false;

            requirement.Status = RequirementStatus.Approved;
            requirement.ApprovedByUserId = approverUserId;
            requirement.ApprovedDate = DateTime.UtcNow;
            requirement.UpdatedAt = DateTime.UtcNow;

            if (!string.IsNullOrEmpty(comments))
            {
                await AddCommentAsync(requirementId, approverUserId, comments, CommentType.Approval);
            }

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> RejectRequirementAsync(int requirementId, string approverUserId, string reason)
        {
            var requirement = await _context.Requirements.FindAsync(requirementId);
            if (requirement == null) return false;

            requirement.Status = RequirementStatus.Rejected;
            requirement.UpdatedAt = DateTime.UtcNow;

            await AddCommentAsync(requirementId, approverUserId, reason, CommentType.Rejection);

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<IEnumerable<Requirement>> GetRequirementsPendingApprovalAsync(string approverUserId)
        {
            return await _context.Requirements
                .Include(r => r.Project)
                .Include(r => r.Developer)
                .Where(r => r.Status == RequirementStatus.UnderReview)
                .OrderBy(r => r.CreatedAt)
                .ToListAsync();
        }

        // Requirement Tracking
        public async Task<bool> UpdateRequirementStatusAsync(int requirementId, RequirementStatus status)
        {
            var requirement = await _context.Requirements.FindAsync(requirementId);
            if (requirement == null) return false;

            requirement.Status = status;
            requirement.UpdatedAt = DateTime.UtcNow;

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<IEnumerable<Requirement>> GetOverdueRequirementsAsync(int projectId)
        {
            return await _context.Requirements
                .Include(r => r.Developer)
                .Where(r => r.ProjectId == projectId &&
                           r.TargetDate.HasValue &&
                           r.TargetDate.Value < DateTime.UtcNow &&
                           r.Status != RequirementStatus.Completed)
                .OrderBy(r => r.TargetDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Requirement>> GetBlockedRequirementsAsync(int projectId)
        {
            return await _context.Requirements
                .Include(r => r.Developer)
                .Where(r => r.ProjectId == projectId && r.Status == RequirementStatus.OnHold)
                .OrderBy(r => r.UpdatedAt)
                .ToListAsync();
        }

        public async Task<bool> BlockRequirementAsync(int requirementId, string reason)
        {
            var requirement = await _context.Requirements.FindAsync(requirementId);
            if (requirement == null) return false;

            requirement.Status = RequirementStatus.OnHold;
            requirement.UpdatedAt = DateTime.UtcNow;

            // Add comment about blocking reason
            await AddCommentAsync(requirementId, "", reason, CommentType.Concern);

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> UnblockRequirementAsync(int requirementId)
        {
            var requirement = await _context.Requirements.FindAsync(requirementId);
            if (requirement == null) return false;

            requirement.Status = RequirementStatus.InProgress;
            requirement.UpdatedAt = DateTime.UtcNow;

            return await _context.SaveChangesAsync() > 0;
        }

        // Requirement Comments
        public async Task<IEnumerable<RequirementComment>> GetRequirementCommentsAsync(int requirementId)
        {
            return await _context.RequirementComments
                .Include(c => c.User)
                .Where(c => c.RequirementId == requirementId)
                .OrderByDescending(c => c.CreatedAt)
                .ToListAsync();
        }

        public async Task<RequirementComment> AddCommentAsync(int requirementId, string userId, string content, CommentType type = CommentType.General)
        {
            var comment = new RequirementComment
            {
                RequirementId = requirementId,
                UserId = userId,
                Content = content,
                Type = type,
                CreatedAt = DateTime.UtcNow
            };

            _context.RequirementComments.Add(comment);
            await _context.SaveChangesAsync();
            return comment;
        }

        public async Task<bool> DeleteCommentAsync(int commentId)
        {
            var comment = await _context.RequirementComments.FindAsync(commentId);
            if (comment == null) return false;

            _context.RequirementComments.Remove(comment);
            return await _context.SaveChangesAsync() > 0;
        }

        // Requirement Attachments
        public async Task<IEnumerable<RequirementAttachment>> GetRequirementAttachmentsAsync(int requirementId)
        {
            return await _context.RequirementAttachments
                .Include(a => a.UploadedBy)
                .Where(a => a.RequirementId == requirementId)
                .OrderByDescending(a => a.CreatedAt)
                .ToListAsync();
        }

        public async Task<RequirementAttachment> AddAttachmentAsync(int requirementId, string fileName, string filePath, string contentType, long fileSize, string uploadedByUserId, string? description = null)
        {
            var attachment = new RequirementAttachment
            {
                RequirementId = requirementId,
                FileName = fileName,
                FilePath = filePath,
                ContentType = contentType,
                FileSize = fileSize,
                UploadedByUserId = uploadedByUserId,
                Description = description,
                CreatedAt = DateTime.UtcNow
            };

            _context.RequirementAttachments.Add(attachment);
            await _context.SaveChangesAsync();
            return attachment;
        }

        public async Task<bool> RemoveAttachmentAsync(int attachmentId)
        {
            var attachment = await _context.RequirementAttachments.FindAsync(attachmentId);
            if (attachment == null) return false;

            _context.RequirementAttachments.Remove(attachment);
            return await _context.SaveChangesAsync() > 0;
        }

        // Change Management
        public async Task<IEnumerable<RequirementChange>> GetRequirementChangesAsync(int requirementId)
        {
            return await _context.RequirementChanges
                .Include(c => c.RequestedBy)
                .Include(c => c.ApprovedBy)
                .Where(c => c.RequirementId == requirementId)
                .OrderByDescending(c => c.CreatedAt)
                .ToListAsync();
        }

        public async Task<RequirementChange> RequestChangeAsync(RequirementChange change)
        {
            change.CreatedAt = DateTime.UtcNow;
            change.Status = ChangeStatus.Proposed;

            _context.RequirementChanges.Add(change);
            await _context.SaveChangesAsync();
            return change;
        }

        public async Task<bool> ApproveChangeAsync(int changeId, string approverUserId)
        {
            var change = await _context.RequirementChanges.FindAsync(changeId);
            if (change == null) return false;

            change.Status = ChangeStatus.Approved;
            change.ApprovedByUserId = approverUserId;
            change.ApprovedDate = DateTime.UtcNow;

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> RejectChangeAsync(int changeId, string approverUserId, string reason)
        {
            var change = await _context.RequirementChanges.FindAsync(changeId);
            if (change == null) return false;

            change.Status = ChangeStatus.Rejected;
            change.ApprovedByUserId = approverUserId;
            change.ApprovedDate = DateTime.UtcNow;
            // Note: RejectionReason property doesn't exist in entity, using ImpactAnalysis instead
            change.ImpactAnalysis = reason;

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<IEnumerable<RequirementChange>> GetPendingChangesAsync(int projectId)
        {
            return await _context.RequirementChanges
                .Include(c => c.Requirement)
                .Include(c => c.RequestedBy)
                .Where(c => c.Requirement.ProjectId == projectId && c.Status == ChangeStatus.Proposed)
                .OrderBy(c => c.CreatedAt)
                .ToListAsync();
        }

        // Requirement-Task Linking
        public async Task<IEnumerable<TaskEntity>> GetRequirementTasksAsync(int requirementId)
        {
            return await _context.RequirementTasks
                .Include(t => t.Task)
                .Where(t => t.RequirementId == requirementId)
                .Select(t => t.Task)
                .ToListAsync();
        }

        public async Task<IEnumerable<Requirement>> GetTaskRequirementsAsync(int taskId)
        {
            return await _context.RequirementTasks
                .Include(t => t.Requirement)
                .Where(t => t.TaskId == taskId)
                .Select(t => t.Requirement)
                .ToListAsync();
        }

        public async Task<bool> LinkTaskToRequirementAsync(int requirementId, int taskId)
        {
            var link = new RequirementTask
            {
                RequirementId = requirementId,
                TaskId = taskId,
                CreatedAt = DateTime.UtcNow
            };

            _context.RequirementTasks.Add(link);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> UnlinkTaskFromRequirementAsync(int requirementId, int taskId)
        {
            var link = await _context.RequirementTasks
                .FirstOrDefaultAsync(t => t.RequirementId == requirementId && t.TaskId == taskId);

            if (link == null) return false;

            _context.RequirementTasks.Remove(link);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<IEnumerable<Requirement>> GetRequirementsByTaskAsync(int taskId)
        {
            return await _context.RequirementTasks
                .Include(t => t.Requirement)
                .Where(t => t.TaskId == taskId)
                .Select(t => t.Requirement)
                .ToListAsync();
        }

        public async Task<Dictionary<string, object>> GetRequirementCoverageAsync(int projectId)
        {
            // TODO: Implement requirement coverage calculation
            return new Dictionary<string, object>();
        }

        public async Task<IEnumerable<Requirement>> SearchRequirementsAsync(string searchTerm, int? projectId = null)
        {
            var query = _context.Requirements
                .Include(r => r.Project)
                .Include(r => r.Developer)
                .Where(r => r.Title.Contains(searchTerm) || r.Description.Contains(searchTerm));

            if (projectId.HasValue)
                query = query.Where(r => r.ProjectId == projectId.Value);

            return await query.OrderBy(r => r.Priority).ToListAsync();
        }

        public async Task<IEnumerable<Requirement>> FilterRequirementsAsync(int projectId, RequirementType? type = null, RequirementPriority? priority = null, RequirementStatus? status = null, string? assignedToUserId = null)
        {
            var query = _context.Requirements
                .Include(r => r.Developer)
                .Where(r => r.ProjectId == projectId);

            if (type.HasValue)
                query = query.Where(r => r.Type == type.Value);

            if (priority.HasValue)
                query = query.Where(r => r.Priority == priority.Value);

            if (status.HasValue)
                query = query.Where(r => r.Status == status.Value);

            if (!string.IsNullOrEmpty(assignedToUserId))
                query = query.Where(r => r.DeveloperUserId == assignedToUserId);

            return await query.OrderBy(r => r.Priority).ToListAsync();
        }

        public async Task<Dictionary<string, object>> GetRequirementsReportAsync(int projectId)
        {
            // TODO: Implement requirements report
            return new Dictionary<string, object>();
        }

        public async Task<IEnumerable<object>> GetRequirementProgressReportAsync(int projectId)
        {
            // TODO: Implement progress report
            return new List<object>();
        }

        public async Task<IEnumerable<object>> GetRequirementVelocityReportAsync(int projectId, DateTime startDate, DateTime endDate)
        {
            // TODO: Implement velocity report
            return new List<object>();
        }

        // Missing interface methods
        public async Task<Dictionary<RequirementType, int>> GetRequirementTypeDistributionAsync(int projectId)
        {
            var requirements = await _context.Requirements
                .Where(r => r.ProjectId == projectId)
                .GroupBy(r => r.Type)
                .ToDictionaryAsync(g => g.Key, g => g.Count());

            return requirements;
        }

        public async Task<Dictionary<RequirementStatus, int>> GetRequirementStatusDistributionAsync(int projectId)
        {
            var requirements = await _context.Requirements
                .Where(r => r.ProjectId == projectId)
                .GroupBy(r => r.Status)
                .ToDictionaryAsync(g => g.Key, g => g.Count());

            return requirements;
        }

        public async Task<Dictionary<RequirementPriority, int>> GetRequirementPriorityDistributionAsync(int projectId)
        {
            var requirements = await _context.Requirements
                .Where(r => r.ProjectId == projectId)
                .GroupBy(r => r.Priority)
                .ToDictionaryAsync(g => g.Key, g => g.Count());

            return requirements;
        }

        public async Task<double> GetRequirementCompletionPercentageAsync(int projectId)
        {
            var totalRequirements = await _context.Requirements.CountAsync(r => r.ProjectId == projectId);
            var completedRequirements = await _context.Requirements.CountAsync(r => r.ProjectId == projectId && r.Status == RequirementStatus.Completed);

            return totalRequirements > 0 ? (double)completedRequirements / totalRequirements * 100 : 0;
        }

        public async Task<decimal> GetTotalEstimatedEffortAsync(int projectId)
        {
            return await _context.Requirements
                .Where(r => r.ProjectId == projectId)
                .SumAsync(r => r.EstimatedEffort);
        }

        public async Task<IEnumerable<Requirement>> GetHighPriorityRequirementsAsync(int projectId)
        {
            return await _context.Requirements
                .Include(r => r.Developer)
                .Where(r => r.ProjectId == projectId && r.Priority == RequirementPriority.Critical)
                .OrderBy(r => r.CreatedAt)
                .ToListAsync();
        }

        public async Task<Dictionary<string, object>> GetRequirementTraceabilityAsync(int requirementId)
        {
            var requirement = await GetRequirementByIdAsync(requirementId);
            if (requirement == null) return new Dictionary<string, object>();

            var childRequirements = await GetChildRequirementsAsync(requirementId);
            var tasks = await GetRequirementTasksAsync(requirementId);

            return new Dictionary<string, object>
            {
                ["Requirement"] = requirement,
                ["ChildRequirements"] = childRequirements,
                ["LinkedTasks"] = tasks
            };
        }

        public async Task<IEnumerable<object>> GetRequirementImpactAnalysisAsync(int requirementId)
        {
            // TODO: Implement impact analysis
            return new List<object>();
        }

        public async Task<bool> ValidateRequirementDependenciesAsync(int projectId)
        {
            // TODO: Implement dependency validation
            return true;
        }

        public async Task<bool> ImportRequirementsAsync(int projectId, IEnumerable<Requirement> requirements)
        {
            foreach (var requirement in requirements)
            {
                requirement.ProjectId = projectId;
                requirement.CreatedAt = DateTime.UtcNow;
                requirement.UpdatedAt = DateTime.UtcNow;
                _context.Requirements.Add(requirement);
            }

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<IEnumerable<Requirement>> ExportRequirementsAsync(int projectId, RequirementStatus? status = null)
        {
            var query = _context.Requirements
                .Include(r => r.Developer)
                .Where(r => r.ProjectId == projectId);

            if (status.HasValue)
                query = query.Where(r => r.Status == status.Value);

            return await query.OrderBy(r => r.Priority).ToListAsync();
        }
    }
}
