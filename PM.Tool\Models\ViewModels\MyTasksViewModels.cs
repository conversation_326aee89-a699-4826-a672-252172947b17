using PM.Tool.Core.Enums;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.ComponentModel.DataAnnotations;
using TaskStatus = PM.Tool.Core.Enums.TaskStatus;

namespace PM.Tool.Models.ViewModels
{
    /// <summary>
    /// Main ViewModel for MyTasks view
    /// </summary>
    public class MyTasksViewModel
    {
        public List<TaskViewModel> Tasks { get; set; } = new();
        public MyTasksStatsViewModel Stats { get; set; } = new();
        public MyTasksFiltersViewModel CurrentFilters { get; set; } = new();
        public PaginationViewModel Pagination { get; set; } = new();
        public List<SelectListItem> Projects { get; set; } = new();

        // Quick action properties
        public List<int> SelectedTaskIds { get; set; } = new();
        public string? BulkAction { get; set; }

        // View preferences
        public string ViewMode { get; set; } = "list"; // list, grid, calendar
        public bool ShowCompletedTasks { get; set; } = false;
        public bool GroupByProject { get; set; } = false;
        public bool ShowSubTasks { get; set; } = true;
    }

    /// <summary>
    /// Statistics for MyTasks dashboard
    /// </summary>
    public class MyTasksStatsViewModel
    {
        public int TotalTasks { get; set; }
        public int TodoTasks { get; set; }
        public int InProgressTasks { get; set; }
        public int CompletedTasks { get; set; }
        public int OverdueTasks { get; set; }
        public int HighPriorityTasks { get; set; }
        public int DueTodayTasks { get; set; }
        public int DueThisWeekTasks { get; set; }

        // Productivity metrics
        public double CompletionRate => TotalTasks > 0 ? (double)CompletedTasks / TotalTasks * 100 : 0;
        public double OverdueRate => TotalTasks > 0 ? (double)OverdueTasks / TotalTasks * 100 : 0;
        public int ActiveTasks => TodoTasks + InProgressTasks;

        // Time-based metrics
        public int TotalEstimatedHours { get; set; }
        public int TotalActualHours { get; set; }
        public double TimeEfficiency => TotalEstimatedHours > 0 ? (double)TotalActualHours / TotalEstimatedHours * 100 : 0;

        // Weekly progress
        public int TasksCompletedThisWeek { get; set; }
        public int TasksCreatedThisWeek { get; set; }
        public double WeeklyVelocity => TasksCompletedThisWeek;
    }

    /// <summary>
    /// Filter options for MyTasks view
    /// </summary>
    public class MyTasksFiltersViewModel
    {
        [Display(Name = "Status")]
        public TaskStatus? Status { get; set; }

        [Display(Name = "Priority")]
        public TaskPriority? Priority { get; set; }

        [Display(Name = "Project")]
        public int? ProjectId { get; set; }

        [Display(Name = "Search")]
        [StringLength(100)]
        public string? Search { get; set; }

        [Display(Name = "Sort By")]
        public string? SortBy { get; set; } = "dueDate";

        [Display(Name = "Sort Order")]
        public string? SortOrder { get; set; } = "asc";

        [Display(Name = "Show Overdue Only")]
        public bool? IsOverdue { get; set; }

        [Display(Name = "Due Date From")]
        [DataType(DataType.Date)]
        public DateTime? DueDateFrom { get; set; }

        [Display(Name = "Due Date To")]
        [DataType(DataType.Date)]
        public DateTime? DueDateTo { get; set; }

        [Display(Name = "Created Date From")]
        [DataType(DataType.Date)]
        public DateTime? CreatedDateFrom { get; set; }

        [Display(Name = "Created Date To")]
        [DataType(DataType.Date)]
        public DateTime? CreatedDateTo { get; set; }

        [Display(Name = "Assigned By Me")]
        public bool? AssignedByMe { get; set; }

        [Display(Name = "Has Sub Tasks")]
        public bool? HasSubTasks { get; set; }

        [Display(Name = "Estimated Hours Range")]
        public int? MinEstimatedHours { get; set; }
        public int? MaxEstimatedHours { get; set; }

        // Quick filter presets
        public string? QuickFilter { get; set; } // today, thisweek, overdue, highpriority, etc.
    }

    /// <summary>
    /// Pagination information
    /// </summary>
    public class PaginationViewModel
    {
        public int CurrentPage { get; set; } = 1;
        public int PageSize { get; set; } = 25;
        public int TotalItems { get; set; }
        public int TotalPages { get; set; }

        public bool HasPreviousPage => CurrentPage > 1;
        public bool HasNextPage => CurrentPage < TotalPages;
        public int StartItem => (CurrentPage - 1) * PageSize + 1;
        public int EndItem => Math.Min(CurrentPage * PageSize, TotalItems);

        // Page navigation helpers
        public List<int> GetPageNumbers()
        {
            var pages = new List<int>();
            var start = Math.Max(1, CurrentPage - 2);
            var end = Math.Min(TotalPages, CurrentPage + 2);

            for (int i = start; i <= end; i++)
            {
                pages.Add(i);
            }

            return pages;
        }
    }

    /// <summary>
    /// Task calendar event for calendar view
    /// </summary>
    public class TaskCalendarEventViewModel
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public DateTime Start { get; set; }
        public DateTime? End { get; set; }
        public string Color { get; set; } = "#3b82f6";
        public TaskStatus Status { get; set; }
        public TaskPriority Priority { get; set; }
        public string ProjectName { get; set; } = string.Empty;
        public bool IsOverdue { get; set; }
        public string Url { get; set; } = string.Empty;

        public string GetStatusColor()
        {
            return Status switch
            {
                TaskStatus.ToDo => "#6b7280",
                TaskStatus.InProgress => "#3b82f6",
                TaskStatus.Done => "#10b981",
                TaskStatus.Cancelled => "#ef4444",
                _ => "#6b7280"
            };
        }

        public string GetPriorityColor()
        {
            return Priority switch
            {
                TaskPriority.Critical => "#dc2626",
                TaskPriority.High => "#ea580c",
                TaskPriority.Medium => "#ca8a04",
                TaskPriority.Low => "#65a30d",
                _ => "#6b7280"
            };
        }
    }

    /// <summary>
    /// Bulk operation options
    /// </summary>
    public class TaskBulkOperationViewModel
    {
        public List<int> TaskIds { get; set; } = new();
        public string Operation { get; set; } = string.Empty;
        public TaskStatus? NewStatus { get; set; }
        public TaskPriority? NewPriority { get; set; }
        public string? NewAssigneeId { get; set; }
        public int? NewProjectId { get; set; }
        public DateTime? NewDueDate { get; set; }
        public bool DeleteTasks { get; set; }
    }

    /// <summary>
    /// Task workload analysis
    /// </summary>
    public class TaskWorkloadViewModel
    {
        public string Period { get; set; } = "week"; // day, week, month
        public List<WorkloadDataPoint> DataPoints { get; set; } = new();
        public int TotalHoursPlanned { get; set; }
        public int TotalHoursActual { get; set; }
        public double CapacityUtilization { get; set; }
        public List<string> Recommendations { get; set; } = new();
    }

    public class WorkloadDataPoint
    {
        public DateTime Date { get; set; }
        public int PlannedHours { get; set; }
        public int ActualHours { get; set; }
        public int TaskCount { get; set; }
        public string Label { get; set; } = string.Empty;
    }

    /// <summary>
    /// Task export options
    /// </summary>
    public class TaskExportViewModel
    {
        public string Format { get; set; } = "excel"; // excel, csv, pdf
        public List<int> TaskIds { get; set; } = new();
        public bool IncludeSubTasks { get; set; } = true;
        public bool IncludeComments { get; set; } = false;
        public bool IncludeTimeEntries { get; set; } = false;
        public List<string> SelectedFields { get; set; } = new();
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }
    }
}
