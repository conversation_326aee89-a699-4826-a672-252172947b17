using PM.Tool.Core.Entities;

namespace PM.Tool.Core.Interfaces
{
    public interface ITimeTrackingService
    {
        Task<TimeEntry> StartTimeTrackingAsync(int taskId, string userId, string? description);
        Task<TimeEntry> StopTimeTrackingAsync(int timeEntryId);
        Task<TimeEntry?> GetActiveTimeEntryAsync(string userId);
        Task<IEnumerable<TimeEntry>> GetUserTimeEntriesAsync(string userId, DateTime startDate, DateTime endDate);
        Task<IEnumerable<TimeEntry>> GetTaskTimeEntriesAsync(int taskId);
        Task<TimeEntry> UpdateTimeEntryAsync(int timeEntryId, DateTime? startTime, DateTime? endTime, string? description);
        Task<bool> DeleteTimeEntryAsync(int timeEntryId);
        Task<double> GetTotalTimeSpentAsync(int taskId);
        Task<IDictionary<string, double>> GetUserTimeReportAsync(string userId, DateTime startDate, DateTime endDate);
    }
}
