using System.Globalization;

namespace PM.Tool.Core.Localization
{
    public interface ILocalizationService
    {
        /// <summary>
        /// Get localized string by key
        /// </summary>
        string GetString(string key, params object[] args);

        /// <summary>
        /// Get localized string for specific culture
        /// </summary>
        string GetString(string key, CultureInfo culture, params object[] args);

        /// <summary>
        /// Get all available cultures
        /// </summary>
        IEnumerable<CultureInfo> GetSupportedCultures();

        /// <summary>
        /// Get current culture
        /// </summary>
        CultureInfo GetCurrentCulture();

        /// <summary>
        /// Set current culture
        /// </summary>
        void SetCurrentCulture(string cultureName);

        /// <summary>
        /// Get localized date format
        /// </summary>
        string GetDateFormat();

        /// <summary>
        /// Get localized time format
        /// </summary>
        string GetTimeFormat();

        /// <summary>
        /// Get localized currency format
        /// </summary>
        string GetCurrencyFormat();

        /// <summary>
        /// Get localized number format
        /// </summary>
        string GetNumberFormat();

        /// <summary>
        /// Format date according to current culture
        /// </summary>
        string FormatDate(DateTime date);

        /// <summary>
        /// Format currency according to current culture
        /// </summary>
        string FormatCurrency(decimal amount);

        /// <summary>
        /// Format number according to current culture
        /// </summary>
        string FormatNumber(decimal number);

        /// <summary>
        /// Get text direction (LTR/RTL)
        /// </summary>
        string GetTextDirection();

        /// <summary>
        /// Check if current culture is RTL
        /// </summary>
        bool IsRightToLeft();

        /// <summary>
        /// Get localized enum display name
        /// </summary>
        string GetEnumDisplayName<T>(T enumValue) where T : struct, Enum;

        /// <summary>
        /// Get all localized enum values
        /// </summary>
        Dictionary<T, string> GetEnumDisplayNames<T>() where T : struct, Enum;
    }
}
