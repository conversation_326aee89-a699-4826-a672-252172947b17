# Project Management Tool

A comprehensive ASP.NET Core MVC application for project management with PostgreSQL database backend.

## Features

### Core Functionality
- **User Authentication & Authorization** - ASP.NET Identity with role-based access control
- **Project Management** - Create, update, delete projects with status tracking
- **Task Management** - Full CRUD operations for tasks and subtasks
- **Team Collaboration** - Comment system on tasks
- **File Attachments** - Support for project and task file uploads
- **Milestone Tracking** - Project milestone management with deadlines
- **Dashboard** - Comprehensive overview with charts and statistics
- **Notification System** - In-app and email notifications
- **Audit Logging** - Track all critical actions
- **Reporting** - Basic charts for task progress and project status

### Technical Features
- **Clean Architecture** - Separation of concerns with layered architecture
- **Repository Pattern** - Data access abstraction
- **Dependency Injection** - Built-in ASP.NET Core DI container
- **Async/Await** - Asynchronous programming throughout
- **FluentValidation** - Input validation
- **AutoMapper** - Object-to-object mapping
- **MediatR** - CQRS pattern implementation
- **Serilog** - Structured logging
- **Swagger** - API documentation
- **Docker Support** - Containerized deployment

## Architecture

```
PM.Tool/
├── Core/
│   ├── Entities/          # Domain models
│   ├── Enums/            # Enumerations
│   └── Interfaces/       # Repository and service interfaces
├── Infrastructure/
│   ├── Repositories/     # Data access implementations
│   └── Services/         # External service implementations
├── Application/
│   ├── Services/         # Business logic services
│   ├── DTOs/            # Data transfer objects
│   └── Validators/       # FluentValidation validators
├── Controllers/          # MVC Controllers
├── Views/               # Razor views
├── Models/
│   └── ViewModels/      # View models
├── Data/                # Entity Framework DbContext
└── wwwroot/            # Static files
```

## Prerequisites

- .NET 9.0 SDK
- PostgreSQL 12+ (or Docker for containerized setup)
- Visual Studio 2022 or VS Code
- Node.js (for frontend package management, optional)

## Quick Start

### Option 1: Using Docker (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd PM.Tool
   ```

2. **Start services with Docker Compose**
   ```bash
   docker-compose up -d
   ```

3. **Access the application**
   - Application: http://localhost:5000
   - PgAdmin: http://localhost:8080 (<EMAIL> / admin123)

### Option 2: Local Development Setup

1. **Install PostgreSQL**
   - Download and install PostgreSQL from https://www.postgresql.org/
   - Create a database named `PMToolDB`

2. **Update Connection String**
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Host=localhost;Database=PMToolDB;Username=your_username;Password=your_password;Port=5432"
     }
   }
   ```

3. **Install Dependencies**
   ```bash
   cd PM.Tool
   dotnet restore
   ```

4. **Run Database Migrations**
   ```bash
   dotnet ef database update
   ```

5. **Run the Application**
   ```bash
   dotnet run
   ```

6. **Access the application**
   - Navigate to https://localhost:5001 or http://localhost:5000

## Default Users

The application seeds the following default users:

| Role | Email | Password | Description |
|------|-------|----------|-------------|
| Admin | <EMAIL> | Admin123! | System administrator |
| Project Manager | <EMAIL> | PM123! | Project manager |
| Team Member | <EMAIL> | Member123! | Team member |

## User Roles & Permissions

### Admin
- Full system access
- User management
- All project and task operations
- System configuration

### Project Manager
- Create and manage projects
- Assign team members to projects
- Create and assign tasks
- View project reports
- Manage project milestones

### Team Member
- View assigned projects
- Create and update assigned tasks
- Comment on tasks
- Upload attachments
- View project progress

## Database Schema

### Core Entities
- **ApplicationUser** - Extended Identity user with profile information
- **Project** - Project information with status, dates, and budget
- **TaskEntity** - Tasks with status, priority, and assignment
- **ProjectMember** - Project team membership with roles
- **Milestone** - Project milestones with due dates
- **TaskComment** - Task collaboration comments
- **TaskAttachment** / **ProjectAttachment** - File attachments
- **Notification** - System notifications
- **AuditLog** - Activity tracking

## API Documentation

When running in development mode, Swagger UI is available at:
- https://localhost:5001/swagger
- http://localhost:5000/swagger

## Configuration

### Email Settings
Configure SMTP settings in `appsettings.json`:
```json
{
  "EmailSettings": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "SmtpUsername": "<EMAIL>",
    "SmtpPassword": "your-app-password",
    "FromEmail": "<EMAIL>",
    "FromName": "PM Tool"
  }
}
```

### File Upload Settings
```json
{
  "FileUpload": {
    "MaxFileSize": 10485760,
    "AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx"],
    "UploadPath": "uploads"
  }
}
```

## Development

### Adding New Features

1. **Create Entity** in `Core/Entities/`
2. **Add Repository Interface** in `Core/Interfaces/`
3. **Implement Repository** in `Infrastructure/Repositories/`
4. **Create Service Interface** in `Core/Interfaces/`
5. **Implement Service** in `Application/Services/`
6. **Add Controller** in `Controllers/`
7. **Create Views** in `Views/`
8. **Register Services** in `Program.cs`

### Database Migrations

```bash
# Add new migration
dotnet ef migrations add MigrationName

# Update database
dotnet ef database update

# Remove last migration
dotnet ef migrations remove
```

### Running Tests

```bash
# Run all tests
dotnet test

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"
```

## Deployment

### Production Deployment

1. **Update Connection String** for production database
2. **Configure Email Settings** with production SMTP
3. **Set Environment Variables**
   ```bash
   export ASPNETCORE_ENVIRONMENT=Production
   export ConnectionStrings__DefaultConnection="your-production-connection-string"
   ```
4. **Build and Publish**
   ```bash
   dotnet publish -c Release -o ./publish
   ```

### Docker Deployment

```bash
# Build image
docker build -t pmtool:latest .

# Run container
docker run -d -p 5000:8080 --name pmtool pmtool:latest
```

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Verify PostgreSQL is running
   - Check connection string format
   - Ensure database exists

2. **Migration Issues**
   - Delete migrations folder and recreate
   - Check for conflicting entity configurations

3. **Authentication Issues**
   - Clear browser cookies
   - Check Identity configuration
   - Verify user roles are seeded

### Logs

Application logs are written to:
- Console (development)
- `logs/` directory (file logging)
- Database (structured logging)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the logs for error details
