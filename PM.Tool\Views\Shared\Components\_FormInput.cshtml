@*
    Form Input Component - Usage Examples:

    1. Text Input:
    @{
        ViewData["Label"] = "Project Name";
        ViewData["Name"] = "Name";
        ViewData["Type"] = "text";
        ViewData["Required"] = true;
        ViewData["Icon"] = "fas fa-tag";
    }
    <partial name="Components/_FormInput" view-data="ViewData" />

    2. Select Dropdown:
    @{
        var options = "<option value=\"\">Select Option</option>";
        options += "<option value=\"1\">Option 1</option>";
        options += "<option value=\"2\">Option 2</option>";

        ViewData["Label"] = "Status";
        ViewData["Name"] = "Status";
        ViewData["Type"] = "select";
        ViewData["Options"] = options;
        ViewData["Icon"] = "fas fa-list";
    }
    <partial name="Components/_FormInput" view-data="ViewData" />

    3. Textarea:
    @{
        ViewData["Label"] = "Description";
        ViewData["Name"] = "Description";
        ViewData["Type"] = "textarea";
        ViewData["Rows"] = 4;
        ViewData["Icon"] = "fas fa-align-left";
    }
    <partial name="Components/_FormInput" view-data="ViewData" />
*@

@{
    var label = ViewData["Label"]?.ToString();
    var name = ViewData["Name"]?.ToString() ?? "";
    var id = ViewData["Id"]?.ToString() ?? name;
    var type = ViewData["Type"]?.ToString() ?? "text";
    var placeholder = ViewData["Placeholder"]?.ToString();
    var value = ViewData["Value"]?.ToString();
    var required = ViewData["Required"] as bool? ?? false;
    var disabled = ViewData["Disabled"] as bool? ?? false;
    var readonly_ = ViewData["Readonly"] as bool? ?? false;
    var helpText = ViewData["HelpText"]?.ToString();
    var errorMessage = ViewData["ErrorMessage"]?.ToString();
    var icon = ViewData["Icon"]?.ToString();
    var iconPosition = ViewData["IconPosition"]?.ToString() ?? "left"; // left, right
    var additionalClasses = ViewData["AdditionalClasses"]?.ToString() ?? "";
    var containerClasses = ViewData["ContainerClasses"]?.ToString() ?? "";
    var rows = ViewData["Rows"] as int? ?? 3;
    var maxLength = ViewData["MaxLength"] as int?;
    var minLength = ViewData["MinLength"] as int?;
    var pattern = ViewData["Pattern"]?.ToString();
    var autocomplete = ViewData["Autocomplete"]?.ToString();
    var options = ViewData["Options"]?.ToString(); // For select dropdowns
    var useSelect2 = ViewData["UseSelect2"] as bool? ?? false;
    var select2Options = ViewData["Select2Options"]?.ToString() ?? "";

    var hasError = !string.IsNullOrEmpty(errorMessage);
    var inputClasses = hasError ?
        "form-input-custom border-danger-300 dark:border-danger-600 focus:border-danger-500 focus:ring-danger-500" :
        "form-input-custom";

    var selectClasses = hasError ?
        "form-select-custom border-danger-300 dark:border-danger-600 focus:border-danger-500 focus:ring-danger-500" :
        "form-select-custom";

    // Apply icon padding to both input and select elements
    if (!string.IsNullOrEmpty(icon))
    {
        var iconPadding = iconPosition == "left" ? " pl-10" : " pr-10";
        inputClasses += iconPadding;
        selectClasses += iconPadding;
    }

    inputClasses += " " + additionalClasses;
    selectClasses += " " + additionalClasses;
}

<div class="@containerClasses">
    @if (!string.IsNullOrEmpty(label))
    {
        <label for="@id" class="form-label-custom">
            @label
            @if (required)
            {
                <span class="text-danger-500 ml-1">*</span>
            }
        </label>
    }

    <div class="relative">
        @if (!string.IsNullOrEmpty(icon))
        {
            @if (type == "select" && iconPosition == "right")
            {
                <!-- For select with right icon, position it to the left of the dropdown arrow -->
                <div class="absolute inset-y-0 right-0 pr-12 flex items-center pointer-events-none z-10">
                    <i class="@icon text-neutral-400 dark:text-neutral-400 text-sm"></i>
                </div>
            }
            else
            {
                <!-- Standard icon positioning for inputs and left-positioned select icons -->
                <div class="absolute inset-y-0 @(iconPosition == "left" ? "left-0 pl-3" : "right-0 pr-3") flex items-center pointer-events-none z-10">
                    <i class="@icon text-neutral-400 dark:text-neutral-400 text-sm"></i>
                </div>
            }
        }

        @if (type == "textarea")
        {
            var textareaHtml = $"<textarea id=\"{id}\" name=\"{name}\" class=\"{inputClasses}\" rows=\"{rows}\"";
            if (!string.IsNullOrEmpty(placeholder)) textareaHtml += $" placeholder=\"{Html.Encode(placeholder)}\"";
            if (required) textareaHtml += " required";
            if (disabled) textareaHtml += " disabled";
            if (readonly_) textareaHtml += " readonly";
            if (maxLength.HasValue) textareaHtml += $" maxlength=\"{maxLength}\"";
            if (minLength.HasValue) textareaHtml += $" minlength=\"{minLength}\"";
            if (!string.IsNullOrEmpty(autocomplete)) textareaHtml += $" autocomplete=\"{Html.Encode(autocomplete)}\"";
            textareaHtml += $">{Html.Encode(value ?? "")}</textarea>";
            @Html.Raw(textareaHtml)
        }
        else if (type == "select")
        {
            var selectHtml = $"<select id=\"{id}\" name=\"{name}\" class=\"{selectClasses}\"";
            if (required) selectHtml += " required";
            if (disabled) selectHtml += " disabled";
            if (useSelect2)
            {
                selectHtml += " data-select2=\"true\"";
                // Add CSS class for icon positioning with Select2
                if (!string.IsNullOrEmpty(icon))
                {
                    selectHtml += $" data-icon-position=\"{iconPosition}\"";
                }
            }
            if (!string.IsNullOrEmpty(select2Options)) selectHtml += $" data-select2-options='{select2Options}'";
            selectHtml += ">";
            if (!string.IsNullOrEmpty(options)) selectHtml += options;
            selectHtml += "</select>";
            @Html.Raw(selectHtml)
        }
        else
        {
            var inputHtml = $"<input type=\"{type}\" id=\"{id}\" name=\"{name}\" class=\"{inputClasses}\"";
            if (!string.IsNullOrEmpty(value)) inputHtml += $" value=\"{Html.Encode(value)}\"";
            if (!string.IsNullOrEmpty(placeholder)) inputHtml += $" placeholder=\"{Html.Encode(placeholder)}\"";
            if (required) inputHtml += " required";
            if (disabled) inputHtml += " disabled";
            if (readonly_) inputHtml += " readonly";
            if (maxLength.HasValue) inputHtml += $" maxlength=\"{maxLength}\"";
            if (minLength.HasValue) inputHtml += $" minlength=\"{minLength}\"";
            if (!string.IsNullOrEmpty(pattern)) inputHtml += $" pattern=\"{Html.Encode(pattern)}\"";
            if (!string.IsNullOrEmpty(autocomplete)) inputHtml += $" autocomplete=\"{Html.Encode(autocomplete)}\"";
            inputHtml += " />";
            @Html.Raw(inputHtml)
        }
    </div>

    @if (!string.IsNullOrEmpty(helpText) && !hasError)
    {
        <p class="mt-2 text-sm text-neutral-500 dark:text-neutral-400">@helpText</p>
    }

    @if (hasError)
    {
        <p class="mt-2 text-sm text-danger-600 dark:text-danger-400 flex items-center">
            <i class="fas fa-exclamation-circle mr-1"></i>
            @errorMessage
        </p>
    }
</div>
