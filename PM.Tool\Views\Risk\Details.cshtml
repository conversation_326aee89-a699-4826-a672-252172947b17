@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer Localizer
@model PM.Tool.Core.Entities.Risk
@{
    ViewData["Title"] = "Risk Details";
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Risk Management", Href = Url.Action("Index", "Risk"), Icon = "fas fa-exclamation-triangle" },
        new { Text = Model.Title, Href = "", Icon = "fas fa-file-alt" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
        <div>
            <div class="flex items-center space-x-3 mb-2">
                @{
                    var riskLevelClass = GetRiskLevelTailwindClass(Model.RiskLevel);
                }
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @riskLevelClass">
                    @Model.RiskLevel Risk
                </span>
                @{
                    var statusClass = GetStatusTailwindClass(Model.Status);
                }
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @statusClass">
                    @Model.Status
                </span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200">
                    @Model.Category
                </span>
            </div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-exclamation-triangle mr-3 text-danger-600 dark:text-danger-400"></i>
                @Model.Title
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Risk Score: @((int)Model.Probability * (int)Model.Impact) • Created @Model.CreatedAt.ToString("MMM dd, yyyy")
            </p>
        </div>
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
            @if (Model.Status != PM.Tool.Core.Entities.RiskStatus.Resolved)
            {
                ViewData["Text"] = "Edit";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-edit";
                ViewData["Href"] = Url.Action("Edit", new { id = Model.Id });
                <partial name="Components/_Button" view-data="ViewData" />
            }

            @{
                ViewData["Text"] = "Back to Risks";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Index");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-6">
        <!-- Description -->
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-align-left text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Description</h3>
                    </div>
                </div>
            </div>
            <div class="card-body-custom">
                <div class="prose dark:prose-invert max-w-none">
                    <p class="text-neutral-700 dark:text-dark-300 leading-relaxed">@Model.Description</p>
                </div>
            </div>
        </div>

        <!-- Risk Assessment -->
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-bar text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Risk Assessment</h3>
                    </div>
                </div>
            </div>
            <div class="card-body-custom">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-info-100 dark:bg-info-900 rounded-full flex items-center justify-center mx-auto mb-3">
                            <span class="text-2xl font-bold text-info-600 dark:text-info-400">@((int)Model.Probability)</span>
                        </div>
                        <h4 class="font-medium text-neutral-900 dark:text-dark-100">Probability</h4>
                        <p class="text-sm text-neutral-500 dark:text-dark-400">@GetProbabilityDescription((int)Model.Probability)</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-warning-100 dark:bg-warning-900 rounded-full flex items-center justify-center mx-auto mb-3">
                            <span class="text-2xl font-bold text-warning-600 dark:text-warning-400">@((int)Model.Impact)</span>
                        </div>
                        <h4 class="font-medium text-neutral-900 dark:text-dark-100">Impact</h4>
                        <p class="text-sm text-neutral-500 dark:text-dark-400">@GetImpactDescription((int)Model.Impact)</p>
                    </div>
                    <div class="text-center">
                        @{
                            var riskScore = (int)Model.Probability * (int)Model.Impact;
                            var scoreColorClass = GetRiskScoreColorClass(riskScore);
                        }
                        <div class="w-16 h-16 @scoreColorClass rounded-full flex items-center justify-center mx-auto mb-3">
                            <span class="text-2xl font-bold text-white">@riskScore</span>
                        </div>
                        <h4 class="font-medium text-neutral-900 dark:text-dark-100">Risk Score</h4>
                        <p class="text-sm text-neutral-500 dark:text-dark-400">@GetRiskScoreDescription(riskScore)</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mitigation Plan -->
        @if (!string.IsNullOrEmpty(Model.MitigationPlan))
        {
            <div class="card-custom">
                <div class="card-header-custom">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                            <i class="fas fa-shield-alt text-primary-600 dark:text-primary-400 text-sm"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Mitigation Plan</h3>
                        </div>
                    </div>
                </div>
                <div class="card-body-custom">
                    <div class="prose dark:prose-invert max-w-none">
                        <p class="text-neutral-700 dark:text-dark-300 leading-relaxed">@Model.MitigationPlan</p>
                    </div>
                </div>
            </div>
        }

        <!-- Contingency Plan -->
        @if (!string.IsNullOrEmpty(Model.ContingencyPlan))
        {
            <div class="card-custom">
                <div class="card-header-custom">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                            <i class="fas fa-life-ring text-primary-600 dark:text-primary-400 text-sm"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Contingency Plan</h3>
                        </div>
                    </div>
                </div>
                <div class="card-body-custom">
                    <div class="prose dark:prose-invert max-w-none">
                        <p class="text-neutral-700 dark:text-dark-300 leading-relaxed">@Model.ContingencyPlan</p>
                    </div>
                </div>
            </div>
        }
    </div>

    <!-- Sidebar -->
    <div class="space-y-6">
        <!-- Quick Info -->
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-info-circle text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Quick Info</h3>
                    </div>
                </div>
            </div>
            <div class="card-body-custom">
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-neutral-500 dark:text-dark-400">Project</span>
                        <span class="text-sm text-neutral-900 dark:text-dark-100">@(Model.Project?.Name ?? "No Project")</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-neutral-500 dark:text-dark-400">Category</span>
                        <span class="text-sm text-neutral-900 dark:text-dark-100">@Model.Category</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-neutral-500 dark:text-dark-400">Owner</span>
                        <span class="text-sm text-neutral-900 dark:text-dark-100">@(Model.Owner?.UserName ?? "Not Assigned")</span>
                    </div>
                    @if (Model.TargetResolutionDate.HasValue)
                    {
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-neutral-500 dark:text-dark-400">Target Date</span>
                            <span class="text-sm text-neutral-900 dark:text-dark-100 @(Model.TargetResolutionDate.Value < DateTime.Now ? "text-danger-600 dark:text-danger-400" : "")">
                                @Model.TargetResolutionDate.Value.ToString("MMM dd, yyyy")
                            </span>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Status Actions -->
        @if (Model.Status != PM.Tool.Core.Entities.RiskStatus.Resolved)
        {
            <div class="card-custom">
                <div class="card-header-custom">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                            <i class="fas fa-tasks text-primary-600 dark:text-primary-400 text-sm"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Actions</h3>
                        </div>
                    </div>
                </div>
                <div class="card-body-custom">
                    <div class="space-y-3">
                        @if (Model.Status == PM.Tool.Core.Entities.RiskStatus.Identified)
                        {
                            <button onclick="updateRiskStatus(@Model.Id, 'Analyzing')" class="w-full btn-primary-custom">
                                <i class="fas fa-search mr-2"></i>
                                Start Analysis
                            </button>
                        }
                        @if (Model.Status == PM.Tool.Core.Entities.RiskStatus.Analyzing)
                        {
                            <button onclick="updateRiskStatus(@Model.Id, 'Mitigating')" class="w-full btn-success-custom">
                                <i class="fas fa-shield-alt mr-2"></i>
                                Start Mitigation
                            </button>
                        }
                        @if (Model.Status == PM.Tool.Core.Entities.RiskStatus.Mitigating)
                        {
                            <button onclick="updateRiskStatus(@Model.Id, 'Resolved')" class="w-full btn-success-custom">
                                <i class="fas fa-check-circle mr-2"></i>
                                Mark as Resolved
                            </button>
                        }
                    </div>
                </div>
            </div>
        }

        <!-- Timeline -->
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-history text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Timeline</h3>
                    </div>
                </div>
            </div>
            <div class="card-body-custom">
                <div class="space-y-3">
                    <div class="flex items-start space-x-3">
                        <div class="w-2 h-2 bg-primary-500 rounded-full mt-2"></div>
                        <div>
                            <p class="text-sm font-medium text-neutral-900 dark:text-dark-100">Created</p>
                            <p class="text-xs text-neutral-500 dark:text-dark-400">@Model.CreatedAt.ToString("MMM dd, yyyy")</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-2 h-2 bg-neutral-300 dark:bg-dark-600 rounded-full mt-2"></div>
                        <div>
                            <p class="text-sm font-medium text-neutral-900 dark:text-dark-100">Last Updated</p>
                            <p class="text-xs text-neutral-500 dark:text-dark-400">@Model.UpdatedAt?.ToString("MMM dd, yyyy")</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @Html.AntiForgeryToken()
    <script>
        function updateRiskStatus(riskId, newStatus) {
            if (confirm(`Change risk status to ${newStatus}?`)) {
                $.post('@Url.Action("UpdateStatus", "Risk")', {
                    riskId: riskId,
                    status: newStatus,
                    __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                })
                .done(function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.message || 'Failed to update risk status');
                    }
                })
                .fail(function() {
                    alert('Failed to update risk status. Please try again.');
                });
            }
        }
    </script>
}

@functions {
    string GetRiskLevelTailwindClass(PM.Tool.Core.Entities.RiskLevel riskLevel)
    {
        return riskLevel switch
        {
            PM.Tool.Core.Entities.RiskLevel.Critical => "bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200",
            PM.Tool.Core.Entities.RiskLevel.High => "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
            PM.Tool.Core.Entities.RiskLevel.Medium => "bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200",
            PM.Tool.Core.Entities.RiskLevel.Low => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
            _ => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200"
        };
    }

    string GetStatusTailwindClass(PM.Tool.Core.Entities.RiskStatus status)
    {
        return status switch
        {
            PM.Tool.Core.Entities.RiskStatus.Identified => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200",
            PM.Tool.Core.Entities.RiskStatus.Analyzing => "bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200",
            PM.Tool.Core.Entities.RiskStatus.Mitigating => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
            PM.Tool.Core.Entities.RiskStatus.Resolved => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200",
            _ => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200"
        };
    }

    string GetProbabilityDescription(int probability)
    {
        return probability switch
        {
            1 => "Very Low (0-10%)",
            2 => "Low (11-30%)",
            3 => "Medium (31-60%)",
            4 => "High (61-80%)",
            5 => "Very High (81-100%)",
            _ => "Unknown"
        };
    }

    string GetImpactDescription(int impact)
    {
        return impact switch
        {
            1 => "Very Low",
            2 => "Low",
            3 => "Medium",
            4 => "High",
            5 => "Very High",
            _ => "Unknown"
        };
    }

    string GetRiskScoreColorClass(int score)
    {
        return score switch
        {
            >= 20 => "bg-danger-500",
            >= 15 => "bg-warning-500",
            >= 10 => "bg-info-500",
            _ => "bg-success-500"
        };
    }

    string GetRiskScoreDescription(int score)
    {
        return score switch
        {
            >= 20 => "Critical Risk",
            >= 15 => "High Risk",
            >= 10 => "Medium Risk",
            _ => "Low Risk"
        };
    }
}
