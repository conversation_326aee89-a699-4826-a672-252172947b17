using System;
using System.Threading.Tasks;
using PM.Tool.Models.ViewModels;

namespace PM.Tool.Core.Interfaces
{
    public interface IReportingService
    {
        Task<byte[]> GenerateTasksReportAsync(DateTime? startDate = null, DateTime? endDate = null);
        Task<byte[]> GenerateProjectReportAsync(DateTime? startDate = null, DateTime? endDate = null);
        Task<VelocityMetricsViewModel> GetTeamVelocityMetricsAsync(string teamId, DateTime startDate, DateTime endDate);
        Task<ProductivityMetricsViewModel> GetTeamProductivityMetricsAsync(string teamId, DateTime startDate, DateTime endDate);
    }
}
