using Microsoft.AspNetCore.Mvc;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Models;
using System.Text.Json;

namespace PM.Tool.Controllers
{
    [Route("docs")]
    public class DocumentationController : Controller
    {
        private readonly IDocumentationService _documentationService;
        private readonly ILogger<DocumentationController> _logger;

        public DocumentationController(
            IDocumentationService documentationService,
            ILogger<DocumentationController> logger)
        {
            _documentationService = documentationService;
            _logger = logger;
        }

        // GET: /docs
        [HttpGet("")]
        public async Task<IActionResult> Index()
        {
            try
            {
                var sections = await _documentationService.GetAllSectionsAsync();
                var metadata = await _documentationService.GetMetadataAsync();
                var popularPages = await _documentationService.GetPopularPagesAsync(6);

                ViewBag.Metadata = metadata;
                ViewBag.PopularPages = popularPages;
                ViewBag.Configuration = _documentationService.GetConfiguration();

                return View(sections);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading documentation index");
                return View("Error");
            }
        }

        // GET: /docs/{sectionId}
        [HttpGet("{sectionId}")]
        public async Task<IActionResult> Section(string sectionId)
        {
            try
            {
                var sections = await _documentationService.GetAllSectionsAsync();
                var section = sections.FirstOrDefault(s => s.Id.Equals(sectionId, StringComparison.OrdinalIgnoreCase));

                if (section == null)
                {
                    return NotFound($"Documentation section '{sectionId}' not found.");
                }

                var pages = await _documentationService.GetSectionPagesAsync(sectionId);

                ViewBag.Section = section;
                ViewBag.AllSections = sections;
                ViewBag.Configuration = _documentationService.GetConfiguration();

                return View(pages);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading documentation section: {SectionId}", sectionId);
                return View("Error");
            }
        }

        // GET: /docs/{sectionId}/{pageId}
        [HttpGet("{sectionId}/{pageId}")]
        public async Task<IActionResult> Page(string sectionId, string pageId)
        {
            try
            {
                var page = await _documentationService.GetPageAsync(sectionId, pageId);

                if (page == null)
                {
                    return NotFound($"Documentation page '{pageId}' not found in section '{sectionId}'.");
                }

                // Track page view
                var userId = User.Identity?.IsAuthenticated == true ? User.Identity.Name : null;
                await _documentationService.TrackPageViewAsync(page.Id, userId);

                var sections = await _documentationService.GetAllSectionsAsync();
                var navigation = await _documentationService.GetNavigationAsync(page.Id);
                var tableOfContents = await _documentationService.GetTableOfContentsAsync(page.Id);
                var breadcrumb = await _documentationService.GetBreadcrumbAsync(page.Id);
                var relatedPages = await _documentationService.GetRelatedPagesAsync(page.Id);

                ViewBag.AllSections = sections;
                ViewBag.Navigation = navigation;
                ViewBag.TableOfContents = tableOfContents;
                ViewBag.Breadcrumb = breadcrumb;
                ViewBag.RelatedPages = relatedPages;
                ViewBag.Configuration = _documentationService.GetConfiguration();

                return View(page);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading documentation page: {SectionId}/{PageId}", sectionId, pageId);
                return View("Error");
            }
        }

        // GET: /docs/search
        [HttpGet("search")]
        public async Task<IActionResult> Search(string q, int page = 1, int pageSize = 10)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(q))
                {
                    ViewBag.Query = "";
                    ViewBag.Results = new List<DocumentationSearchResult>();
                    ViewBag.TotalResults = 0;
                    ViewBag.Configuration = _documentationService.GetConfiguration();
                    return View();
                }

                var results = await _documentationService.SearchAsync(q, pageSize * 3); // Get more results for pagination
                var totalResults = results.Count;
                var pagedResults = results.Skip((page - 1) * pageSize).Take(pageSize).ToList();

                ViewBag.Query = q;
                ViewBag.Results = pagedResults;
                ViewBag.TotalResults = totalResults;
                ViewBag.CurrentPage = page;
                ViewBag.PageSize = pageSize;
                ViewBag.TotalPages = (int)Math.Ceiling((double)totalResults / pageSize);
                ViewBag.Configuration = _documentationService.GetConfiguration();

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching documentation: {Query}", q);
                return View("Error");
            }
        }

        // GET: /docs/api/search
        [HttpGet("api/search")]
        public async Task<IActionResult> SearchApi(string q, int maxResults = 10)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(q))
                {
                    return Json(new { results = new List<DocumentationSearchResult>() });
                }

                var results = await _documentationService.SearchAsync(q, maxResults);
                return Json(new { results });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in search API: {Query}", q);
                return Json(new { error = "Search failed" });
            }
        }

        // GET: /docs/api/toc/{pageId}
        [HttpGet("api/toc/{pageId}")]
        public async Task<IActionResult> TableOfContentsApi(string pageId)
        {
            try
            {
                var toc = await _documentationService.GetTableOfContentsAsync(pageId);
                return Json(toc);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting table of contents: {PageId}", pageId);
                return Json(new { error = "Failed to load table of contents" });
            }
        }

        // POST: /docs/api/export/{pageId}
        [HttpPost("api/export/{pageId}")]
        public async Task<IActionResult> ExportPage(string pageId, [FromBody] ExportRequest request)
        {
            try
            {
                var format = request.Format?.ToLower() ?? "html";
                var data = await _documentationService.ExportPageAsync(pageId, format);

                var contentType = format switch
                {
                    "pdf" => "application/pdf",
                    "markdown" => "text/markdown",
                    _ => "text/html"
                };

                var fileName = $"pmtool-docs-{pageId}.{format}";
                return File(data, contentType, fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting page: {PageId}", pageId);
                return BadRequest("Export failed");
            }
        }

        // POST: /docs/api/export-all
        [HttpPost("api/export-all")]
        public async Task<IActionResult> ExportAll([FromBody] ExportRequest request)
        {
            try
            {
                var format = request.Format?.ToLower() ?? "html";
                var data = await _documentationService.ExportAllAsync(format);

                var contentType = format switch
                {
                    "pdf" => "application/pdf",
                    "markdown" => "text/markdown",
                    _ => "text/html"
                };

                var fileName = $"pmtool-docs-complete.{format}";
                return File(data, contentType, fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting all documentation");
                return BadRequest("Export failed");
            }
        }

        // POST: /docs/api/refresh-cache
        [HttpPost("api/refresh-cache")]
        public async Task<IActionResult> RefreshCache()
        {
            try
            {
                await _documentationService.RefreshCacheAsync();
                return Json(new { success = true, message = "Cache refreshed successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing documentation cache");
                return Json(new { success = false, message = "Cache refresh failed" });
            }
        }

        // GET: /docs/sitemap
        [HttpGet("sitemap")]
        public async Task<IActionResult> Sitemap()
        {
            try
            {
                var sections = await _documentationService.GetAllSectionsAsync();
                var sitemap = new List<SitemapItem>();

                foreach (var section in sections)
                {
                    sitemap.Add(new SitemapItem
                    {
                        Title = section.Title,
                        Url = Url.Action("Section", new { sectionId = section.Id }),
                        Type = "section",
                        Description = section.Description
                    });

                    foreach (var page in section.Pages)
                    {
                        sitemap.Add(new SitemapItem
                        {
                            Title = page.Title,
                            Url = Url.Action("Page", new { sectionId = section.Id, pageId = page.Id }),
                            Type = "page",
                            Section = section.Title,
                            LastModified = page.LastModified
                        });
                    }
                }

                ViewBag.Configuration = _documentationService.GetConfiguration();
                return View(sitemap);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating sitemap");
                return View("Error");
            }
        }

        // GET: /docs/print/{sectionId}/{pageId}
        [HttpGet("print/{sectionId}/{pageId}")]
        public async Task<IActionResult> Print(string sectionId, string pageId)
        {
            try
            {
                var page = await _documentationService.GetPageAsync(sectionId, pageId);

                if (page == null)
                {
                    return NotFound($"Documentation page '{pageId}' not found in section '{sectionId}'.");
                }

                ViewBag.Configuration = _documentationService.GetConfiguration();
                return View("PrintPage", page);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading print view: {SectionId}/{PageId}", sectionId, pageId);
                return View("Error");
            }
        }
    }

    public class ExportRequest
    {
        public string Format { get; set; } = "html";
    }

    public class SitemapItem
    {
        public string Title { get; set; } = string.Empty;
        public string? Url { get; set; }
        public string Type { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? Section { get; set; }
        public DateTime? LastModified { get; set; }
    }
}
