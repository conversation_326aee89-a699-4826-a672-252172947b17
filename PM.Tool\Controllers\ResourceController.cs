using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Enums;
using PM.Tool.Services;
using PM.Tool.Models.ViewModels;

namespace PM.Tool.Controllers
{
    public class ResourceController : SecureBaseController
    {
        private readonly IResourceService _resourceService;
        private readonly IFormHelperService _formHelper;
        private readonly ILogger<ResourceController> _logger;

        public ResourceController(
            IResourceService resourceService,
            IFormHelperService formHelper,
            IAuditService auditService,
            ILogger<ResourceController> logger)
            : base(auditService)
        {
            _resourceService = resourceService;
            _formHelper = formHelper;
            _logger = logger;
        }

        // GET: Resource
        public async Task<IActionResult> Index()
        {
            try
            {
                var resources = await _resourceService.GetAllResourcesAsync();
                return View(resources);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading resources.";
                return View(new List<Resource>());
            }
        }

        // GET: Resource/Details/5
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var resource = await _resourceService.GetResourceByIdAsync(id);
                if (resource == null) return NotFound();

                var allocations = await _resourceService.GetResourceAllocationsAsync(id);
                ViewBag.Allocations = allocations;

                return View(resource);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading resource details.";
                return RedirectToAction("Index");
            }
        }

        // GET: Resource/Create
        public async Task<IActionResult> Create()
        {
            await PopulateDropdowns();
            return View(new ResourceCreateViewModel());
        }

        // POST: Resource/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(ResourceCreateViewModel viewModel)
        {
            return await this.HandleCreateAsync<ResourceCreateViewModel, Resource>(
                viewModel,
                async (resource) => {
                    var createdResource = await _resourceService.CreateResourceAsync(resource);
                    await LogAuditAsync(AuditAction.Create, "Resource", createdResource.Id);
                    return createdResource;
                },
                resource => resource.Id,
                PopulateDropdowns,
                _formHelper,
                _logger,
                "Resource"
            );
        }

        // GET: Resource/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                var resource = await _resourceService.GetResourceByIdAsync(id);
                if (resource == null) return NotFound();

                await PopulateDropdowns();
                return View(resource);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading resource for editing.";
                return RedirectToAction("Index");
            }
        }

        // POST: Resource/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, Resource resource)
        {
            if (id != resource.Id) return NotFound();

            try
            {
                if (ModelState.IsValid)
                {
                    await _resourceService.UpdateResourceAsync(resource);
                    await LogAuditAsync(AuditAction.Update, "Resource", id);

                    TempData["Success"] = "Resource updated successfully.";
                    return RedirectToAction("Details", new { id });
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error updating resource.";
            }

            await PopulateDropdowns();
            return View(resource);
        }

        // POST: Resource/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var success = await _resourceService.DeleteResourceAsync(id);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Delete, "Resource", id);
                    TempData["Success"] = "Resource deleted successfully.";
                }
                else
                {
                    TempData["Error"] = "Failed to delete resource.";
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error deleting resource.";
            }

            return RedirectToAction("Index");
        }

        // GET: Resource/Allocations/5
        public async Task<IActionResult> Allocations(int id)
        {
            try
            {
                var resource = await _resourceService.GetResourceByIdAsync(id);
                if (resource == null) return NotFound();

                var allocations = await _resourceService.GetResourceAllocationsAsync(id);

                ViewBag.Resource = resource;
                return View(allocations);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading resource allocations.";
                return RedirectToAction("Details", new { id });
            }
        }

        // GET: Resource/CreateAllocation/5
        public async Task<IActionResult> CreateAllocation(int resourceId)
        {
            try
            {
                var resource = await _resourceService.GetResourceByIdAsync(resourceId);
                if (resource == null) return NotFound();

                var allocation = new ResourceAllocation
                {
                    ResourceId = resourceId,
                    StartDate = DateTime.Today,
                    EndDate = DateTime.Today.AddDays(7),
                    AllocationPercentage = 100
                };

                ViewBag.Resource = resource;
                await PopulateAllocationDropdowns();

                return View(allocation);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading allocation form.";
                return RedirectToAction("Details", new { id = resourceId });
            }
        }

        // POST: Resource/CreateAllocation
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateAllocation(ResourceAllocation allocation)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    // Check availability
                    var isAvailable = await _resourceService.IsResourceAvailableAsync(
                        allocation.ResourceId,
                        allocation.StartDate,
                        allocation.EndDate,
                        allocation.AllocatedHours);

                    if (!isAvailable)
                    {
                        ModelState.AddModelError("", "Resource is not available for the specified period.");
                    }
                    else
                    {
                        allocation.CreatedByUserId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "";

                        var createdAllocation = await _resourceService.CreateAllocationAsync(allocation);
                        await LogAuditAsync(AuditAction.Create, "ResourceAllocation", createdAllocation.Id);

                        TempData["Success"] = "Resource allocation created successfully.";
                        return RedirectToAction("Allocations", new { id = allocation.ResourceId });
                    }
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error creating resource allocation.";
            }

            var resource = await _resourceService.GetResourceByIdAsync(allocation.ResourceId);
            ViewBag.Resource = resource;
            await PopulateAllocationDropdowns();

            return View(allocation);
        }

        // GET: Resource/Utilization
        public async Task<IActionResult> Utilization(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                var start = startDate ?? DateTime.Today.AddDays(-30);
                var end = endDate ?? DateTime.Today;

                var resources = await _resourceService.GetActiveResourcesAsync();
                var resourceIds = resources.Select(r => r.Id);
                var utilization = await _resourceService.GetTeamUtilizationAsync(resourceIds, start, end);

                var utilizationData = resources.Select(r => new
                {
                    Resource = r,
                    Utilization = utilization.ContainsKey(r.Id) ? utilization[r.Id] : 0
                }).ToList();

                ViewBag.StartDate = start;
                ViewBag.EndDate = end;

                return View(utilizationData);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading utilization data.";
                return View(new List<object>());
            }
        }

        // GET: Resource/Available
        public async Task<IActionResult> Available(DateTime? startDate, DateTime? endDate, decimal? requiredHours)
        {
            try
            {
                var start = startDate ?? DateTime.Today;
                var end = endDate ?? DateTime.Today.AddDays(7);
                var hours = requiredHours ?? 8;

                var availableResources = await _resourceService.GetAvailableResourcesAsync(start, end, hours);

                ViewBag.StartDate = start;
                ViewBag.EndDate = end;
                ViewBag.RequiredHours = hours;

                return View(availableResources);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading available resources.";
                return View(new List<Resource>());
            }
        }

        // GET: Resource/Skills
        public async Task<IActionResult> Skills()
        {
            try
            {
                var skills = await _resourceService.GetAllSkillsAsync();
                return View(skills);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading skills.";
                return View(new List<Skill>());
            }
        }

        // POST: Resource/CreateSkill
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateSkill(Skill skill)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var createdSkill = await _resourceService.CreateSkillAsync(skill);
                    await LogAuditAsync(AuditAction.Create, "Skill", createdSkill.Id);

                    TempData["Success"] = "Skill created successfully.";
                }
                else
                {
                    TempData["Error"] = "Invalid skill data.";
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error creating skill.";
            }

            return RedirectToAction("Skills");
        }

        private async Task PopulateDropdowns()
        {
            ViewBag.ResourceTypes = Enum.GetValues<ResourceType>()
                .Select(rt => new SelectListItem
                {
                    Value = ((int)rt).ToString(),
                    Text = rt.ToString()
                });
        }

        private async Task PopulateAllocationDropdowns()
        {
            ViewBag.AllocationStatuses = Enum.GetValues<AllocationStatus>()
                .Select(status => new SelectListItem
                {
                    Value = ((int)status).ToString(),
                    Text = status.ToString()
                });
        }
    }
}
