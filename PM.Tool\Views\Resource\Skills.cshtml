@model IEnumerable<PM.Tool.Core.Entities.Skill>

@{
    ViewData["Title"] = "Skills Management";
    ViewData["PageTitle"] = "Skills Management";
    ViewData["PageDescription"] = "Manage skills and competencies for resource allocation";
    ViewData["BreadcrumbItems"] = new List<(string Text, string? Url)>
    {
        ("Resources", Url.Action("Index")),
        ("Skills", null)
    };
}

<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100">Skills Management</h1>
            <p class="text-neutral-600 dark:text-dark-300">Manage skills and competencies for your resources</p>
        </div>

        <div class="flex space-x-3">
            <button type="button" onclick="exportSkills()" class="btn-secondary">
                <i class="fas fa-download mr-2"></i>
                Export
            </button>
            <button type="button" onclick="openCreateModal()" class="btn-primary">
                <i class="fas fa-plus mr-2"></i>
                Add Skill
            </button>
        </div>
    </div>

    <!-- Skills Summary -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        @{
            var totalSkills = Model.Count();
            var technicalSkills = Model.Count(s => s.Category == "Technical");
            var softSkills = Model.Count(s => s.Category == "Soft");
            var certifications = Model.Count(s => s.Category == "Certification");
        }

        @{
            ViewData["Title"] = "Total Skills";
            ViewData["Icon"] = "fas fa-star";
            ViewData["IconColor"] = "bg-gradient-to-br from-blue-500 to-blue-600";
            ViewData["Value"] = totalSkills.ToString();
            ViewData["Description"] = "skills defined";
        }
        <partial name="Components/_StatsCard" view-data="ViewData" />

        @{
            ViewData["Title"] = "Technical";
            ViewData["Icon"] = "fas fa-code";
            ViewData["IconColor"] = "bg-gradient-to-br from-green-500 to-green-600";
            ViewData["Value"] = technicalSkills.ToString();
            ViewData["Description"] = "technical skills";
        }
        <partial name="Components/_StatsCard" view-data="ViewData" />

        @{
            ViewData["Title"] = "Soft Skills";
            ViewData["Icon"] = "fas fa-users";
            ViewData["IconColor"] = "bg-gradient-to-br from-purple-500 to-purple-600";
            ViewData["Value"] = softSkills.ToString();
            ViewData["Description"] = "soft skills";
        }
        <partial name="Components/_StatsCard" view-data="ViewData" />

        @{
            ViewData["Title"] = "Certifications";
            ViewData["Icon"] = "fas fa-certificate";
            ViewData["IconColor"] = "bg-gradient-to-br from-yellow-500 to-yellow-600";
            ViewData["Value"] = certifications.ToString();
            ViewData["Description"] = "certifications";
        }
        <partial name="Components/_StatsCard" view-data="ViewData" />
    </div>

    <!-- Filters -->
    <div class="card-custom">
        <div class="card-header-custom">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                <i class="fas fa-filter mr-2 text-primary-500"></i>
                Filters
            </h3>
        </div>
        <div class="card-body-custom">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="categoryFilter" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Category</label>
                    <select id="categoryFilter" class="form-select">
                        <option value="">All Categories</option>
                        <option value="Technical">Technical</option>
                        <option value="Soft">Soft Skills</option>
                        <option value="Certification">Certifications</option>
                        <option value="Language">Languages</option>
                    </select>
                </div>

                <div>
                    <label for="statusFilter" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Status</label>
                    <select id="statusFilter" class="form-select">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>

                <div>
                    <label for="searchSkills" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Search</label>
                    <input type="text" id="searchSkills" class="form-input" placeholder="Search skills..." />
                </div>

                <div class="flex items-end">
                    <button type="button" onclick="clearFilters()" class="btn-secondary w-full">
                        <i class="fas fa-times mr-2"></i>
                        Clear
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Skills List -->
    <div class="card-custom">
        <div class="card-header-custom">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                <i class="fas fa-list mr-2 text-primary-500"></i>
                Skills (@totalSkills)
            </h3>
        </div>
        <div class="card-body-custom">
            @if (Model.Any())
            {
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" id="skillsContainer">
                    @foreach (var skill in Model.OrderBy(s => s.Category).ThenBy(s => s.Name))
                    {
                        <div class="skill-card p-4 border border-neutral-200 dark:border-dark-600 rounded-lg hover:shadow-md transition-shadow"
                             data-category="@(skill.Category?.ToLower() ?? "")"
                             data-name="@skill.Name.ToLower()">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2 mb-2">
                                        <div class="w-8 h-8 @GetSkillCategoryIconBg(skill.Category) rounded-lg flex items-center justify-center">
                                            <i class="@GetSkillCategoryIcon(skill.Category) text-white text-sm"></i>
                                        </div>
                                        <h4 class="font-semibold text-neutral-900 dark:text-dark-100">@skill.Name</h4>
                                    </div>

                                    @if (!string.IsNullOrEmpty(skill.Description))
                                    {
                                        <p class="text-sm text-neutral-600 dark:text-dark-300 mb-3">@skill.Description</p>
                                    }

                                    <div class="flex items-center justify-between">
                                        <div class="flex space-x-2">
                                            @if (!string.IsNullOrEmpty(skill.Category))
                                            {
                                                <span class="@GetCategoryBadgeClass(skill.Category)">@skill.Category</span>
                                            }
                                            @if (skill.IsActive)
                                            {
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200">
                                                    Active
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200">
                                                    Inactive
                                                </span>
                                            }
                                        </div>

                                        <div class="flex space-x-1">
                                            <button type="button" onclick="editSkill(@skill.Id)"
                                                    class="text-yellow-600 hover:text-yellow-800 dark:text-yellow-400">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" onclick="deleteSkill(@skill.Id)"
                                                    class="text-red-600 hover:text-red-800 dark:text-red-400">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-12">
                    <i class="fas fa-star text-6xl text-neutral-400 dark:text-dark-500 mb-4"></i>
                    <h3 class="text-lg font-medium text-neutral-900 dark:text-dark-100 mb-2">No Skills Defined</h3>
                    <p class="text-neutral-600 dark:text-dark-300 mb-6">Start by adding skills that your resources can have.</p>
                    <button type="button" onclick="openCreateModal()" class="btn-primary">
                        <i class="fas fa-plus mr-2"></i>
                        Add First Skill
                    </button>
                </div>
            }
        </div>
    </div>
</div>

<!-- Create/Edit Skill Modal -->
<div id="skillModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white dark:bg-dark-800 rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 id="modalTitle" class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Add New Skill</h3>
                    <button type="button" onclick="closeModal()" class="text-neutral-400 hover:text-neutral-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form asp-action="CreateSkill" method="post" id="skillForm">
                    <input type="hidden" id="skillId" name="Id" />

                    <div class="space-y-4">
                        <div>
                            <label for="skillName" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Skill Name</label>
                            <input type="text" id="skillName" name="Name" class="form-input" required />
                        </div>

                        <div>
                            <label for="skillDescription" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Description</label>
                            <textarea id="skillDescription" name="Description" class="form-textarea" rows="3"></textarea>
                        </div>

                        <div>
                            <label for="skillCategory" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Category</label>
                            <select id="skillCategory" name="Category" class="form-select" required>
                                <option value="">Select category</option>
                                <option value="Technical">Technical</option>
                                <option value="Soft">Soft Skills</option>
                                <option value="Certification">Certification</option>
                                <option value="Language">Language</option>
                            </select>
                        </div>

                        <div>
                            <div class="flex items-center space-x-3">
                                <input type="checkbox" id="skillIsActive" name="IsActive" class="form-checkbox-custom" checked />
                                <label for="skillIsActive" class="form-label-custom">
                                    Skill is active
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" onclick="closeModal()" class="btn-secondary">Cancel</button>
                        <button type="submit" class="btn-primary">
                            <i class="fas fa-save mr-2"></i>
                            Save Skill
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Filter functionality
        function filterSkills() {
            const categoryFilter = document.getElementById('categoryFilter').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value.toLowerCase();
            const searchFilter = document.getElementById('searchSkills').value.toLowerCase();

            const cards = document.querySelectorAll('.skill-card');

            cards.forEach(card => {
                let show = true;

                if (categoryFilter && !card.dataset.category.includes(categoryFilter)) {
                    show = false;
                }

                if (statusFilter) {
                    const isActive = card.querySelector('.bg-success-100') !== null;
                    if (statusFilter === 'active' && !isActive) {
                        show = false;
                    } else if (statusFilter === 'inactive' && isActive) {
                        show = false;
                    }
                }

                if (searchFilter && !card.dataset.name.includes(searchFilter)) {
                    show = false;
                }

                card.style.display = show ? '' : 'none';
            });
        }

        function clearFilters() {
            document.getElementById('categoryFilter').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('searchSkills').value = '';
            filterSkills();
        }

        // Add event listeners
        document.getElementById('categoryFilter').addEventListener('change', filterSkills);
        document.getElementById('statusFilter').addEventListener('change', filterSkills);
        document.getElementById('searchSkills').addEventListener('input', filterSkills);

        // Modal functions
        function openCreateModal() {
            document.getElementById('modalTitle').textContent = 'Add New Skill';
            document.getElementById('skillForm').reset();
            document.getElementById('skillId').value = '';
            document.getElementById('skillModal').classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('skillModal').classList.add('hidden');
        }

        function editSkill(id) {
            // In a real implementation, you would fetch the skill data
            document.getElementById('modalTitle').textContent = 'Edit Skill';
            document.getElementById('skillId').value = id;
            document.getElementById('skillModal').classList.remove('hidden');
            // Populate form with existing data
        }

        function deleteSkill(id) {
            if (confirm('Are you sure you want to delete this skill?')) {
                // Implement delete functionality
                console.log('Delete skill:', id);
            }
        }

        function exportSkills() {
            // Implement export functionality
            alert('Export functionality would be implemented here');
        }

        // Close modal when clicking outside
        document.getElementById('skillModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
}

@functions {
    private string GetSkillCategoryIcon(string? category)
    {
        return category switch
        {
            "Technical" => "fas fa-code",
            "Soft" => "fas fa-users",
            "Certification" => "fas fa-certificate",
            "Language" => "fas fa-language",
            _ => "fas fa-star"
        };
    }

    private string GetSkillCategoryIconBg(string? category)
    {
        return category switch
        {
            "Technical" => "bg-gradient-to-br from-green-500 to-green-600",
            "Soft" => "bg-gradient-to-br from-purple-500 to-purple-600",
            "Certification" => "bg-gradient-to-br from-yellow-500 to-yellow-600",
            "Language" => "bg-gradient-to-br from-blue-500 to-blue-600",
            _ => "bg-gradient-to-br from-neutral-500 to-neutral-600"
        };
    }

    private string GetCategoryBadgeClass(string? category)
    {
        return category switch
        {
            "Technical" => "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
            "Soft" => "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200",
            "Certification" => "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
            "Language" => "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200",
            _ => "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-neutral-100 dark:bg-neutral-900 text-neutral-800 dark:text-neutral-200"
        };
    }
}
