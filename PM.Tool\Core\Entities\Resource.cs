using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities
{
    public class Resource : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(1000)]
        public string? Description { get; set; }

        public ResourceType Type { get; set; } = ResourceType.Human;

        [MaxLength(100)]
        public string? Department { get; set; }

        [MaxLength(100)]
        public string? Location { get; set; }

        public decimal HourlyRate { get; set; }

        public decimal Capacity { get; set; } = 8; // Hours per day

        public bool IsActive { get; set; } = true;

        [MaxLength(500)]
        public string? Skills { get; set; } // JSON array of skills

        [MaxLength(100)]
        public string? Email { get; set; }

        [MaxLength(20)]
        public string? Phone { get; set; }

        public string? UserId { get; set; } // Link to ApplicationUser if it's a person

        // Navigation properties
        public virtual ApplicationUser? User { get; set; }
        public virtual ICollection<ResourceAllocation> Allocations { get; set; } = new List<ResourceAllocation>();
        public virtual ICollection<ResourceSkill> ResourceSkills { get; set; } = new List<ResourceSkill>();
    }

    public enum ResourceType
    {
        Human = 1,
        Equipment = 2,
        Material = 3,
        Facility = 4
    }
}
