@model IEnumerable<PM.Tool.Core.Entities.Stakeholder>
@using PM.Tool.Core.Entities

@{
    ViewData["Title"] = "Stakeholder Management";
    ViewData["PageTitle"] = "Stakeholder Management";
    ViewData["PageDescription"] = "Manage project stakeholders, track influence and interest levels, and maintain stakeholder communications.";
}

@section Styles {
    <style>
        .stakeholder-card {
            transition: all 0.2s ease-in-out;
        }
        .stakeholder-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .influence-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
    </style>
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-users mr-3 text-primary-600 dark:text-primary-400"></i>
                @ViewData["PageTitle"]
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                @ViewData["PageDescription"]
            </p>
        </div>
        <div class="mt-4 sm:mt-0">
            @{
                ViewData["Text"] = "Add Stakeholder";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-plus";
                ViewData["Href"] = Url.Action("Create");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Stakeholder Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
    <!-- Total Stakeholders Card -->
    <div class="card-custom hover:shadow-medium transition-all duration-300">
        <div class="card-body-custom">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-neutral-500 dark:text-dark-400">Total Stakeholders</p>
                    <p class="text-3xl font-bold text-neutral-900 dark:text-dark-100">@Model.Count()</p>
                </div>
                <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-users text-primary-600 dark:text-primary-400 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Stakeholders Card -->
    <div class="card-custom hover:shadow-medium transition-all duration-300">
        <div class="card-body-custom">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-neutral-500 dark:text-dark-400">Active Stakeholders</p>
                    <p class="text-3xl font-bold text-neutral-900 dark:text-dark-100">@Model.Count(s => s.IsActive)</p>
                </div>
                <div class="w-12 h-12 bg-success-100 dark:bg-success-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-check-circle text-success-600 dark:text-success-400 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- High Influence Card -->
    <div class="card-custom hover:shadow-medium transition-all duration-300">
        <div class="card-body-custom">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-neutral-500 dark:text-dark-400">High Influence</p>
                    <p class="text-3xl font-bold text-neutral-900 dark:text-dark-100">@Model.Count(s => s.Influence == PM.Tool.Core.Entities.InfluenceLevel.High || s.Influence == PM.Tool.Core.Entities.InfluenceLevel.VeryHigh)</p>
                </div>
                <div class="w-12 h-12 bg-warning-100 dark:bg-warning-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-star text-warning-600 dark:text-warning-400 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Critical Priority Card -->
    <div class="card-custom hover:shadow-medium transition-all duration-300">
        <div class="card-body-custom">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-neutral-500 dark:text-dark-400">Critical Priority</p>
                    <p class="text-3xl font-bold text-neutral-900 dark:text-dark-100">@Model.Count(s => s.Priority == PM.Tool.Core.Entities.StakeholderPriority.Critical)</p>
                </div>
                <div class="w-12 h-12 bg-danger-100 dark:bg-danger-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle text-danger-600 dark:text-danger-400 text-xl"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card-custom mb-8">
    <div class="card-header-custom">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-filter text-primary-600 dark:text-primary-400 text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Filter Stakeholders</h3>
                </div>
            </div>
            <div>
                @{
                    ViewData["Text"] = "Clear Filters";
                    ViewData["Variant"] = "outline";
                    ViewData["Size"] = "sm";
                    ViewData["Icon"] = "fas fa-times";
                    ViewData["OnClick"] = "clearAllFilters()";
                    ViewData["Href"] = null;
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
        </div>
    </div>
    <div class="card-body-custom">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <!-- Search -->
            @{
                ViewData["Label"] = "Search";
                ViewData["Name"] = "searchInput";
                ViewData["Type"] = "text";
                ViewData["Icon"] = "fas fa-search";
                ViewData["Placeholder"] = "Search stakeholders...";
                ViewData["ContainerClasses"] = "";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

            <!-- Type Filter -->
            @{
                var typeOptions = "<option value=\"\">All Types</option>" +
                                "<option value=\"internal\">Internal</option>" +
                                "<option value=\"external\">External</option>" +
                                "<option value=\"customer\">Customer</option>" +
                                "<option value=\"vendor\">Vendor</option>" +
                                "<option value=\"regulatory\">Regulatory</option>" +
                                "<option value=\"partner\">Partner</option>";

                ViewData["Label"] = "Type";
                ViewData["Name"] = "typeFilter";
                ViewData["Type"] = "select";
                ViewData["Icon"] = "fas fa-tag";
                ViewData["Options"] = typeOptions;
                ViewData["ContainerClasses"] = "";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

            <!-- Influence Filter -->
            @{
                var influenceOptions = "<option value=\"\">All Levels</option>" +
                                     "<option value=\"verylow\">Very Low</option>" +
                                     "<option value=\"low\">Low</option>" +
                                     "<option value=\"medium\">Medium</option>" +
                                     "<option value=\"high\">High</option>" +
                                     "<option value=\"veryhigh\">Very High</option>";

                ViewData["Label"] = "Influence";
                ViewData["Name"] = "influenceFilter";
                ViewData["Type"] = "select";
                ViewData["Icon"] = "fas fa-star";
                ViewData["Options"] = influenceOptions;
                ViewData["ContainerClasses"] = "";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

            <!-- Status Filter -->
            @{
                var statusOptions = "<option value=\"\">All Statuses</option>" +
                                  "<option value=\"active\">Active</option>" +
                                  "<option value=\"inactive\">Inactive</option>";

                ViewData["Label"] = "Status";
                ViewData["Name"] = "statusFilter";
                ViewData["Type"] = "select";
                ViewData["Icon"] = "fas fa-toggle-on";
                ViewData["Options"] = statusOptions;
                ViewData["ContainerClasses"] = "";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Stakeholder Grid -->
<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6" id="stakeholderContainer">
    @if (Model != null && Model.Any())
    {
        @foreach (var stakeholder in Model)
        {
            <div class="stakeholder-card transition-all duration-200 hover:shadow-lg"
                 data-type="@stakeholder.Type.ToString().ToLower()"
                 data-influence="@stakeholder.Influence.ToString().ToLower()"
                 data-status="@(stakeholder.IsActive ? "active" : "inactive")"
                 data-search="@($"{stakeholder.Name} {stakeholder.Email} {stakeholder.Organization}".ToLower())">

                <div class="bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
                    <!-- Header -->
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 @GetStakeholderTypeIconBg(stakeholder.Type) rounded-lg flex items-center justify-center">
                                <i class="@GetStakeholderTypeIcon(stakeholder.Type) text-white text-lg"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-neutral-900 dark:text-white">@stakeholder.Name</h3>
                                <p class="text-sm text-neutral-600 dark:text-dark-400">@stakeholder.Title</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            @if (stakeholder.IsActive)
                            {
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200">
                                    Active
                                </span>
                            }
                            else
                            {
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200">
                                    Inactive
                                </span>
                            }
                        </div>
                    </div>

                    <!-- Details -->
                    <div class="space-y-3 mb-4">
                        <div class="flex items-center text-sm text-neutral-600 dark:text-dark-400">
                            <i class="fas fa-building w-4 mr-2"></i>
                            <span>@(stakeholder.Organization ?? "No organization")</span>
                        </div>
                        <div class="flex items-center text-sm text-neutral-600 dark:text-dark-400">
                            <i class="fas fa-envelope w-4 mr-2"></i>
                            <span>@stakeholder.Email</span>
                        </div>
                        @if (!string.IsNullOrEmpty(stakeholder.Phone))
                        {
                            <div class="flex items-center text-sm text-neutral-600 dark:text-dark-400">
                                <i class="fas fa-phone w-4 mr-2"></i>
                                <span>@stakeholder.Phone</span>
                            </div>
                        }
                    </div>

                    <!-- Influence & Interest Matrix -->
                    <div class="grid grid-cols-2 gap-3 mb-4">
                        <div class="text-center">
                            <div class="text-xs text-neutral-500 dark:text-dark-500 mb-1">Influence</div>
                            <span class="influence-badge @GetInfluenceBadgeClass(stakeholder.Influence) rounded-full">
                                @stakeholder.Influence
                            </span>
                        </div>
                        <div class="text-center">
                            <div class="text-xs text-neutral-500 dark:text-dark-500 mb-1">Interest</div>
                            <span class="influence-badge @GetInterestBadgeClass(stakeholder.Interest) rounded-full">
                                @stakeholder.Interest
                            </span>
                        </div>
                    </div>

                    <!-- Priority Badge -->
                    <div class="text-center mb-4">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium @GetPriorityBadgeClass(stakeholder.Priority)">
                            @stakeholder.Priority Priority
                        </span>
                    </div>

                    <!-- Actions -->
                    <div class="flex justify-between items-center pt-4 border-t border-neutral-200 dark:border-dark-600">
                        <div class="flex space-x-2">
                            <a href="@Url.Action("Details", new { id = stakeholder.Id })"
                               class="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="@Url.Action("Edit", new { id = stakeholder.Id })"
                               class="text-warning-600 hover:text-warning-700 dark:text-warning-400 dark:hover:text-warning-300">
                                <i class="fas fa-edit"></i>
                            </a>
                        </div>
                        <div class="text-xs text-neutral-500 dark:text-dark-500">
                            @stakeholder.Role
                        </div>
                    </div>
                </div>
            </div>
        }
    }
    else
    {
        <div class="col-span-full">
            <div class="card-custom">
                <div class="card-body-custom">
                    <div class="text-center py-12">
                        <div class="w-24 h-24 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-users text-4xl text-neutral-400 dark:text-dark-500"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-neutral-900 dark:text-dark-100 mb-2">No stakeholders found</h3>
                        <p class="text-neutral-600 dark:text-dark-400 mb-8 max-w-md mx-auto">
                            Get started by adding your first stakeholder to track influence, interest levels, and maintain stakeholder communications.
                        </p>
                        @{
                            ViewData["Text"] = "Add First Stakeholder";
                            ViewData["Variant"] = "primary";
                            ViewData["Icon"] = "fas fa-plus";
                            ViewData["Href"] = Url.Action("Create");
                            ViewData["Size"] = "lg";
                        }
                        <partial name="Components/_Button" view-data="ViewData" />
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            initializeStakeholderFilters();
        });

        function initializeStakeholderFilters() {
            const searchInput = $('input[name="searchInput"]');
            const typeFilter = $('select[name="typeFilter"]');
            const influenceFilter = $('select[name="influenceFilter"]');
            const statusFilter = $('select[name="statusFilter"]');

            function filterStakeholders() {
                const searchTerm = searchInput.val().toLowerCase();
                const typeValue = typeFilter.val();
                const influenceValue = influenceFilter.val();
                const statusValue = statusFilter.val();

                $('.stakeholder-card').each(function() {
                    const card = $(this);
                    const searchData = card.data('search') || '';
                    const typeData = card.data('type') || '';
                    const influenceData = card.data('influence') || '';
                    const statusData = card.data('status') || '';

                    const matchesSearch = searchData.includes(searchTerm);
                    const matchesType = !typeValue || typeData === typeValue;
                    const matchesInfluence = !influenceValue || influenceData === influenceValue;
                    const matchesStatus = !statusValue || statusData === statusValue;

                    if (matchesSearch && matchesType && matchesInfluence && matchesStatus) {
                        card.show();
                    } else {
                        card.hide();
                    }
                });

                // Show/hide "no results" message
                const visibleCards = $('.stakeholder-card:visible').length;
                let noResultsMessage = $('#noResultsMessage');

                if (visibleCards === 0 && $('.stakeholder-card').length > 0) {
                    if (noResultsMessage.length === 0) {
                        noResultsMessage = $(`
                            <div id="noResultsMessage" class="col-span-full">
                                <div class="card-custom">
                                    <div class="card-body-custom">
                                        <div class="text-center py-8">
                                            <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                                <i class="fas fa-search text-2xl text-neutral-400 dark:text-dark-500"></i>
                                            </div>
                                            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">No stakeholders match your filters</h3>
                                            <p class="text-neutral-600 dark:text-dark-400 mb-4">Try adjusting your search criteria or clearing some filters.</p>
                                            <button onclick="clearAllFilters()" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium">
                                                <i class="fas fa-times mr-1"></i>Clear all filters
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `);
                        $('#stakeholderContainer').append(noResultsMessage);
                    }
                    noResultsMessage.show();
                } else {
                    noResultsMessage.hide();
                }
            }

            window.clearAllFilters = function() {
                searchInput.val('');
                typeFilter.val('');
                influenceFilter.val('');
                statusFilter.val('');
                filterStakeholders();
            }

            searchInput.on('input', filterStakeholders);
            typeFilter.on('change', filterStakeholders);
            influenceFilter.on('change', filterStakeholders);
            statusFilter.on('change', filterStakeholders);
        }
    </script>
}

@functions {
    private string GetStakeholderTypeIcon(StakeholderType type)
    {
        return type switch
        {
            StakeholderType.Internal => "fas fa-user-tie",
            StakeholderType.External => "fas fa-user",
            StakeholderType.Customer => "fas fa-handshake",
            StakeholderType.Vendor => "fas fa-truck",
            StakeholderType.Regulatory => "fas fa-gavel",
            StakeholderType.Partner => "fas fa-users",
            _ => "fas fa-user"
        };
    }

    private string GetStakeholderTypeIconBg(StakeholderType type)
    {
        return type switch
        {
            StakeholderType.Internal => "bg-blue-500",
            StakeholderType.External => "bg-green-500",
            StakeholderType.Customer => "bg-purple-500",
            StakeholderType.Vendor => "bg-orange-500",
            StakeholderType.Regulatory => "bg-red-500",
            StakeholderType.Partner => "bg-indigo-500",
            _ => "bg-neutral-500"
        };
    }

    private string GetInfluenceBadgeClass(InfluenceLevel influence)
    {
        return influence switch
        {
            InfluenceLevel.VeryLow => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200",
            InfluenceLevel.Low => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            InfluenceLevel.Medium => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
            InfluenceLevel.High => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
            InfluenceLevel.VeryHigh => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200"
        };
    }

    private string GetInterestBadgeClass(InterestLevel interest)
    {
        return interest switch
        {
            InterestLevel.VeryLow => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200",
            InterestLevel.Low => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            InterestLevel.Medium => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
            InterestLevel.High => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            InterestLevel.VeryHigh => "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200"
        };
    }

    private string GetPriorityBadgeClass(StakeholderPriority priority)
    {
        return priority switch
        {
            StakeholderPriority.Low => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200",
            StakeholderPriority.Medium => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            StakeholderPriority.High => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
            StakeholderPriority.Critical => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200"
        };
    }
}
