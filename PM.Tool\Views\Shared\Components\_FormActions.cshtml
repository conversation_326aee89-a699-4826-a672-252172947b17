@*
    Form Actions Component - Usage Examples:

    1. Standard Create form:
    @{
        ViewData["SubmitText"] = "Create Risk";
        ViewData["SubmitIcon"] = "fas fa-plus";
        ViewData["CancelUrl"] = Url.Action("Index");
    }
    <partial name="Components/_FormActions" view-data="ViewData" />

    2. Standard Edit form:
    @{
        ViewData["SubmitText"] = "Update Risk";
        ViewData["SubmitIcon"] = "fas fa-save";
        ViewData["CancelUrl"] = Url.Action("Details", new { id = Model.Id });
    }
    <partial name="Components/_FormActions" view-data="ViewData" />

    3. Custom form with additional buttons:
    @{
        ViewData["SubmitText"] = "Save Draft";
        ViewData["SubmitIcon"] = "fas fa-save";
        ViewData["CancelUrl"] = Url.Action("Index");
        ViewData["ShowReset"] = true;
        ViewData["AdditionalButtons"] = "<button type='button' class='btn-outline-custom'>Preview</button>";
    }
    <partial name="Components/_FormActions" view-data="ViewData" />
*@

@{
    var submitText = ViewData["SubmitText"]?.ToString() ?? "Submit";
    var submitIcon = ViewData["SubmitIcon"]?.ToString() ?? "fas fa-check";
    var submitVariant = ViewData["SubmitVariant"]?.ToString() ?? "primary";
    var cancelText = ViewData["CancelText"]?.ToString() ?? "Cancel";
    var cancelUrl = ViewData["CancelUrl"]?.ToString();
    var cancelIcon = ViewData["CancelIcon"]?.ToString() ?? "fas fa-times";
    var showReset = ViewData["ShowReset"] as bool? ?? false;
    var resetText = ViewData["ResetText"]?.ToString() ?? "Reset";
    var resetIcon = ViewData["ResetIcon"]?.ToString() ?? "fas fa-undo";
    var additionalButtons = ViewData["AdditionalButtons"]?.ToString();
    var containerClass = ViewData["ContainerClass"]?.ToString() ?? "flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3 pt-6 border-t border-neutral-200 dark:border-dark-600";
    var submitDisabled = ViewData["SubmitDisabled"] as bool? ?? false;
    var submitLoading = ViewData["SubmitLoading"] as bool? ?? false;
}

<div class="@containerClass">
    @* Additional custom buttons *@
    @if (!string.IsNullOrEmpty(additionalButtons))
    {
        @Html.Raw(additionalButtons)
    }

    @* Reset button *@
    @if (showReset)
    {
        ViewData.Clear();
        ViewData["Text"] = resetText;
        ViewData["Type"] = "reset";
        ViewData["Variant"] = "outline";
        ViewData["Icon"] = resetIcon;
        <partial name="Components/_FormButton" view-data="ViewData" />
    }

    @* Cancel button *@
    @if (!string.IsNullOrEmpty(cancelUrl))
    {
        ViewData.Clear();
        ViewData["Text"] = cancelText;
        ViewData["Variant"] = "secondary";
        ViewData["Icon"] = cancelIcon;
        ViewData["OnClick"] = $"window.location.href='{cancelUrl}'";
        <partial name="Components/_FormButton" view-data="ViewData" />
    }

    @* Submit button *@
    @{
        ViewData.Clear();
        ViewData["Text"] = submitText;
        ViewData["Type"] = "submit";
        ViewData["Variant"] = submitVariant;
        ViewData["Icon"] = submitIcon;
        ViewData["Disabled"] = submitDisabled;
        ViewData["Loading"] = submitLoading;
    }
    <partial name="Components/_FormButton" view-data="ViewData" />
</div>
