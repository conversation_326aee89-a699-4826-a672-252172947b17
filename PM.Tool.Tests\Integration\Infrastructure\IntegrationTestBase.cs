using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using PM.Tool.Data;
using PM.Tool.Core.Entities;
using Microsoft.AspNetCore.Identity;
using System.Security.Claims;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;
using System.Text.Encodings.Web;
using Xunit;

namespace PM.Tool.Tests.Integration.Infrastructure
{
    public class IntegrationTestBase : IClassFixture<CustomWebApplicationFactory>, IDisposable
    {
        protected readonly CustomWebApplicationFactory _factory;
        protected readonly HttpClient _client;
        protected readonly ApplicationDbContext _context;
        protected readonly UserManager<ApplicationUser> _userManager;
        protected readonly ApplicationUser _testUser;

        public IntegrationTestBase(CustomWebApplicationFactory factory)
        {
            _factory = factory;
            _client = _factory.CreateClient();

            // Get services from the test container
            var scope = _factory.Services.CreateScope();
            _context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            _userManager = scope.ServiceProvider.GetRequiredService<UserManager<ApplicationUser>>();

            // Create test user
            _testUser = CreateTestUserAsync().GetAwaiter().GetResult();

            // Authenticate the client
            AuthenticateAsync().GetAwaiter().GetResult();
        }

        private async Task<ApplicationUser> CreateTestUserAsync()
        {
            var user = new ApplicationUser
            {
                Id = Guid.NewGuid().ToString(),
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Integration",
                LastName = "Test",
                IsActive = true,
                EmailConfirmed = true
            };

            var existingUser = await _userManager.FindByEmailAsync(user.Email);
            if (existingUser != null)
            {
                return existingUser;
            }

            var result = await _userManager.CreateAsync(user, "IntegrationTest123!");
            if (!result.Succeeded)
            {
                throw new Exception($"Failed to create test user: {string.Join(", ", result.Errors.Select(e => e.Description))}");
            }

            return user;
        }

        private Task AuthenticateAsync()
        {
            _client.DefaultRequestHeaders.Authorization =
                new System.Net.Http.Headers.AuthenticationHeaderValue("Test", _testUser.Id);
            return Task.CompletedTask;
        }

        protected async Task<Project> CreateTestProjectAsync(string name = "Test Project")
        {
            var project = new Project
            {
                Name = name,
                Description = "Integration test project",
                StartDate = DateTime.UtcNow,
                EndDate = DateTime.UtcNow.AddMonths(3),
                Status = PM.Tool.Core.Enums.ProjectStatus.Active,
                CreatedByUserId = _testUser.Id,
                ManagerId = _testUser.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.Projects.Add(project);
            await _context.SaveChangesAsync();
            return project;
        }

        protected async Task<TaskEntity> CreateTestTaskAsync(int projectId, string title = "Test Task")
        {
            var task = new TaskEntity
            {
                Title = title,
                Description = "Integration test task",
                ProjectId = projectId,
                AssignedToUserId = _testUser.Id,
                Status = PM.Tool.Core.Enums.TaskStatus.ToDo,
                Priority = PM.Tool.Core.Enums.TaskPriority.Medium,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.Tasks.Add(task);
            await _context.SaveChangesAsync();
            return task;
        }

        protected async Task<Requirement> CreateTestRequirementAsync(int projectId, string title = "Test Requirement")
        {
            var requirement = new Requirement
            {
                Title = title,
                Description = "Integration test requirement",
                ProjectId = projectId,
                Type = RequirementType.Functional,
                Priority = RequirementPriority.High,
                Status = RequirementStatus.Draft,
                Source = RequirementSource.Stakeholder,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.Requirements.Add(requirement);
            await _context.SaveChangesAsync();
            return requirement;
        }

        protected async Task ClearDatabaseAsync()
        {
            // Clear in reverse dependency order
            _context.RequirementTasks.RemoveRange(_context.RequirementTasks);
            _context.RequirementComments.RemoveRange(_context.RequirementComments);
            _context.RequirementAttachments.RemoveRange(_context.RequirementAttachments);
            _context.RequirementChanges.RemoveRange(_context.RequirementChanges);
            _context.Requirements.RemoveRange(_context.Requirements);

            _context.UserStoryComments.RemoveRange(_context.UserStoryComments);
            _context.UserStories.RemoveRange(_context.UserStories);
            _context.Epics.RemoveRange(_context.Epics);
            _context.Sprints.RemoveRange(_context.Sprints);

            _context.Tasks.RemoveRange(_context.Tasks);
            _context.Projects.RemoveRange(_context.Projects);

            await _context.SaveChangesAsync();
        }

        public virtual void Dispose()
        {
            ClearDatabaseAsync().GetAwaiter().GetResult();
            _context?.Dispose();
            _client?.Dispose();
        }
    }

    public class TestAuthenticationSchemeOptions : AuthenticationSchemeOptions { }

    public class TestAuthenticationHandler : AuthenticationHandler<TestAuthenticationSchemeOptions>
    {
        public TestAuthenticationHandler(IOptionsMonitor<TestAuthenticationSchemeOptions> options,
            ILoggerFactory logger, UrlEncoder encoder)
            : base(options, logger, encoder)
        {
        }

        protected override Task<AuthenticateResult> HandleAuthenticateAsync()
        {
            var authorizationHeader = Request.Headers["Authorization"].ToString();

            if (string.IsNullOrEmpty(authorizationHeader) || !authorizationHeader.StartsWith("Test "))
            {
                return Task.FromResult(AuthenticateResult.Fail("Missing or invalid authorization header"));
            }

            var userId = authorizationHeader.Substring("Test ".Length);

            var claims = new[]
            {
                new Claim(ClaimTypes.NameIdentifier, userId),
                new Claim(ClaimTypes.Name, "<EMAIL>"),
                new Claim(ClaimTypes.Email, "<EMAIL>")
            };

            var identity = new ClaimsIdentity(claims, "Test");
            var principal = new ClaimsPrincipal(identity);
            var ticket = new AuthenticationTicket(principal, "Test");

            return Task.FromResult(AuthenticateResult.Success(ticket));
        }
    }
}
