using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities
{
    public class TaskDependency : BaseEntity
    {        public int DependentTaskId { get; set; }
        public TaskEntity DependentTask { get; set; } = null!;
        
        public int DependencyTaskId { get; set; }
        public TaskEntity DependencyTask { get; set; } = null!;
        
        public DependencyType Type { get; set; }
    }

    public enum DependencyType
    {
        FinishToStart = 1,    // Task cannot start until dependency is finished
        StartToStart = 2,     // Task cannot start until dependency starts
        FinishToFinish = 3,   // Task cannot finish until dependency finishes
        StartToFinish = 4     // Task cannot finish until dependency starts
    }
}
