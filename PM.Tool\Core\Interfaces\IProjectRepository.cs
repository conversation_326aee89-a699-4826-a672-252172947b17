using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;

namespace PM.Tool.Core.Interfaces
{
    public interface IProjectRepository : IRepository<Project>
    {
        Task<IEnumerable<Project>> GetProjectsByUserIdAsync(string userId);
        Task<IEnumerable<Project>> GetProjectsByStatusAsync(ProjectStatus status);
        Task<Project?> GetProjectWithMembersAsync(int projectId);
        Task<Project?> GetProjectWithTasksAsync(int projectId);
        Task<Project?> GetProjectWithMilestonesAsync(int projectId);
        Task<IEnumerable<Project>> GetOverdueProjectsAsync();
        Task<IEnumerable<Project>> GetProjectsEndingInDaysAsync(int days);
    }
}
