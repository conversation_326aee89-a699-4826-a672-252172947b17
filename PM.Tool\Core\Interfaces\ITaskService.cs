using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;

namespace PM.Tool.Core.Interfaces
{
    public interface ITaskService
    {
        Task<IEnumerable<TaskEntity>> GetAllTasksAsync();
        Task<IEnumerable<TaskEntity>> GetProjectTasksAsync(int projectId);
        Task<IEnumerable<TaskEntity>> GetUserTasksAsync(string userId);
        IQueryable<TaskEntity> GetUserTasksQuery(string userId);
        Task<TaskEntity?> GetTaskByIdAsync(int id);
        Task<TaskEntity?> GetTaskWithDetailsAsync(int id);
        Task<TaskEntity> CreateTaskAsync(TaskEntity task);
        Task<TaskEntity> UpdateTaskAsync(TaskEntity task);
        Task<bool> DeleteTaskAsync(int id);
        Task<bool> AssignTaskAsync(int taskId, string userId);
        Task<bool> UpdateTaskStatusAsync(int taskId, Enums.TaskStatus status);
        Task<bool> UpdateTaskPriorityAsync(int taskId, TaskPriority priority);
        Task<IEnumerable<TaskEntity>> GetSubTasksAsync(int parentTaskId);
        Task<TaskEntity> CreateSubTaskAsync(int parentTaskId, TaskEntity subTask);
        Task<IEnumerable<TaskEntity>> GetOverdueTasksAsync();
        Task<IEnumerable<TaskEntity>> GetTasksDueSoonAsync(int days = 3);
        Task<bool> AddCommentToTaskAsync(int taskId, string userId, string content);
        Task<IEnumerable<TaskComment>> GetTaskCommentsAsync(int taskId);

        // Task management methods
        Task<IEnumerable<TaskEntity>> GetSiblingTasksAsync(int taskId);
        Task<bool> MoveTaskUpAsync(int taskId);
        Task<bool> MoveTaskDownAsync(int taskId);
        Task<TaskEntity> DuplicateTaskAsync(int taskId, string userId);
        Task<bool> ExportTaskAsync(int taskId, string format);
    }
}
