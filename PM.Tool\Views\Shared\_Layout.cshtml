﻿<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - PM <PERSON></title>

    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="~/css/tailwind.css" asp-append-version="true" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />

    <!-- Third-party CSS (Tailwind compatible) -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" />
    <link rel="stylesheet" href="~/css/select2-tailwind.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css" />

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Page-specific styles -->
    @await RenderSectionAsync("Styles", required: false)

    <!-- Fix for undefined colors and styles -->
    @await Html.PartialAsync("_UndefinedStylesFix")

    <!-- Immediate Theme Application -->
    <script>
        // Apply saved theme immediately to prevent flash
        (function() {
            try {
                const savedTheme = localStorage.getItem('pm-tool-theme') || 'light';
                if (savedTheme === 'dark') {
                    document.documentElement.classList.add('dark');
                } else {
                    document.documentElement.classList.remove('dark');
                }
            } catch (error) {
                console.warn('Theme initialization error:', error);
            }
        })();
    </script>


</head>
<body class="h-full bg-neutral-50 dark:bg-neutral-900 font-sans antialiased">
    <!-- Sidebar -->
    <partial name="_Sidebar" />

    <!-- Main Content Area -->
    <div class="lg:ml-64 flex flex-col min-h-screen">
        <!-- Topbar -->
        <partial name="_Topbar" />

        <!-- Main Content -->
        <main class="flex-1 pt-16 px-6 py-8">
            <!-- Alert Messages -->
            @if (TempData["Success"] != null)
            {
                <div class="alert-success-custom mb-6 animate-slide-in" role="alert">
                    <i class="fas fa-check-circle text-success-600 dark:text-success-400"></i>
                    <div>
                        <p class="font-medium">Success!</p>
                        <p class="text-sm">@TempData["Success"]</p>
                    </div>
                    <button type="button" class="ml-auto text-success-600 dark:text-success-400 hover:text-success-800 dark:hover:text-success-200" onclick="this.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            }
            @if (TempData["Error"] != null)
            {
                <div class="alert-danger-custom mb-6 animate-slide-in" role="alert">
                    <i class="fas fa-exclamation-circle text-danger-600 dark:text-danger-400"></i>
                    <div>
                        <p class="font-medium">Error!</p>
                        <p class="text-sm">@TempData["Error"]</p>
                    </div>
                    <button type="button" class="ml-auto text-danger-600 dark:text-danger-400 hover:text-danger-800 dark:hover:text-danger-200" onclick="this.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            }
            @if (TempData["Warning"] != null)
            {
                <div class="alert-warning-custom mb-6 animate-slide-in" role="alert">
                    <i class="fas fa-exclamation-triangle text-warning-600 dark:text-warning-400"></i>
                    <div>
                        <p class="font-medium">Warning!</p>
                        <p class="text-sm">@TempData["Warning"]</p>
                    </div>
                    <button type="button" class="ml-auto text-warning-600 dark:text-warning-400 hover:text-warning-800 dark:hover:text-warning-200" onclick="this.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            }
            @if (TempData["Info"] != null)
            {
                <div class="alert-info-custom mb-6 animate-slide-in" role="alert">
                    <i class="fas fa-info-circle text-info-600 dark:text-info-400"></i>
                    <div>
                        <p class="font-medium">Information</p>
                        <p class="text-sm">@TempData["Info"]</p>
                    </div>
                    <button type="button" class="ml-auto text-info-600 dark:text-info-400 hover:text-info-800 dark:hover:text-info-200" onclick="this.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            }

            <!-- Page Content -->
            <div class="max-w-7xl mx-auto">
                @RenderBody()
            </div>
        </main>

        <!-- Footer -->
        <partial name="_Footer" />
    </div>

    <!-- Project Selector Modal -->
    <div id="projectSelectorModal" class="modal-custom hidden">
        <div class="modal-backdrop" onclick="closeProjectModal()"></div>
        <div class="modal-content-custom">
            <div class="modal-header-custom">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-white" id="projectSelectorModalLabel">Select Project</h3>
                <button type="button" class="text-neutral-400 dark:text-gray-400 hover:text-neutral-600 dark:hover:text-gray-300" onclick="closeProjectModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body-custom">
                <p class="text-neutral-600 dark:text-gray-300 mb-4">Please select a project to view the chart:</p>
                <div id="projectList" class="space-y-2">
                    <!-- Projects will be loaded here -->
                </div>
            </div>
            <div class="modal-footer-custom">
                <button type="button" class="btn-secondary-custom" onclick="closeProjectModal()">Cancel</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="~/lib/jquery/dist/jquery.min.js"></script>

    <!-- Third-party Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>

    <!-- Application Scripts -->
    <script src="~/js/tailwind-theme-manager.js" asp-append-version="true"></script>
    <script src="~/js/select2-init.js" asp-append-version="true"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    <!-- Page-specific scripts -->
    @await RenderSectionAsync("Scripts", required: false)

    <!-- Initialize Application -->
    <script>
        // Global functions for modal management
        function closeProjectModal() {
            document.getElementById('projectSelectorModal').classList.add('hidden');
        }

        function showProjectModal(chartType) {
            const modal = document.getElementById('projectSelectorModal');
            const title = document.getElementById('projectSelectorModalLabel');
            const projectList = document.getElementById('projectList');

            title.textContent = 'Select Project for ' + chartType.charAt(0).toUpperCase() + chartType.slice(1) + ' Chart';

            // Show loading state
            projectList.innerHTML = `
                <div class="flex items-center justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                    <span class="ml-3 text-neutral-600 dark:text-gray-300">Loading projects...</span>
                </div>
            `;

            modal.classList.remove('hidden');

            // Simulate loading projects (replace with actual AJAX call)
            setTimeout(function() {
                projectList.innerHTML = `
                    <div class="alert-info-custom">
                        <i class="fas fa-info-circle text-info-600 dark:text-info-400"></i>
                        <div>
                            <p class="font-medium">Feature Coming Soon!</p>
                            <p class="text-sm">Charts will be available once projects are selected.</p>
                        </div>
                    </div>
                    <div class="text-center mt-4">
                        <a href="/Analytics" class="btn-primary-custom">
                            <i class="fas fa-chart-line mr-2"></i>
                            Go to Analytics Dashboard
                        </a>
                    </div>
                `;
            }, 1000);
        }

        $(document).ready(function() {
            // Initialize theme manager
            if (window.TailwindThemeManager) {
                console.log('Tailwind Theme Manager ready');
            } else {
                console.warn('Tailwind Theme Manager not found');
            }

            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                document.querySelectorAll('[class*="alert-"]').forEach(function(alert) {
                    alert.style.opacity = '0';
                    alert.style.transform = 'translateY(-10px)';
                    setTimeout(function() {
                        alert.remove();
                    }, 300);
                });
            }, 5000);

            // Initialize sidebar toggle functionality
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const sidebarClose = document.getElementById('sidebar-close');
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebar-overlay');

            function openSidebar() {
                sidebar.classList.remove('-translate-x-full');
                sidebarOverlay.classList.remove('hidden');
            }

            function closeSidebar() {
                sidebar.classList.add('-translate-x-full');
                sidebarOverlay.classList.add('hidden');
            }

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', openSidebar);
            }

            if (sidebarClose) {
                sidebarClose.addEventListener('click', closeSidebar);
            }

            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', closeSidebar);
            }

            // Initialize dropdown toggles
            const userMenuToggle = document.getElementById('user-menu-toggle');
            const userMenu = document.getElementById('user-menu');
            const quickActionsToggle = document.getElementById('quick-actions-toggle');
            const quickActionsMenu = document.getElementById('quick-actions-menu');
            const languageToggle = document.getElementById('language-toggle');
            const languageMenu = document.getElementById('language-menu');

            function toggleDropdown(toggle, menu) {
                if (toggle && menu) {
                    toggle.addEventListener('click', function(e) {
                        e.stopPropagation();
                        menu.classList.toggle('hidden');

                        // Close other dropdowns
                        document.querySelectorAll('[id$="-menu"]').forEach(function(otherMenu) {
                            if (otherMenu !== menu) {
                                otherMenu.classList.add('hidden');
                            }
                        });
                    });
                }
            }

            toggleDropdown(userMenuToggle, userMenu);
            toggleDropdown(quickActionsToggle, quickActionsMenu);
            toggleDropdown(languageToggle, languageMenu);

            // Close dropdowns when clicking outside
            document.addEventListener('click', function() {
                document.querySelectorAll('[id$="-menu"]').forEach(function(menu) {
                    menu.classList.add('hidden');
                });
            });

            // Add click handlers for language links with debugging
            document.querySelectorAll('#language-menu a').forEach(function(link) {
                link.addEventListener('click', function(e) {
                    console.log('Language link clicked:', this.href);
                    // Allow the default action to proceed
                });
            });

            // Debug current culture
            console.log('Current culture:', '@System.Globalization.CultureInfo.CurrentCulture.Name');
            console.log('Current UI culture:', '@System.Globalization.CultureInfo.CurrentUICulture.Name');
        });
    </script>
</body>
</html>
