<script>
    // Enhanced Export Functions
    function exportWbs(format) {
        const projectId = window.projectId;

        if (!projectId || projectId <= 0) {
            showAlert('Invalid project ID. Please refresh the page and try again.', 'danger');
            return;
        }

        switch(format.toLowerCase()) {
            case 'excel':
                exportToExcel(projectId);
                break;
            case 'pdf':
                exportToPdf(projectId);
                break;
            case 'csv':
                exportToCsv(projectId);
                break;
            case 'json':
                exportToJson(projectId);
                break;
            default:
                showAlert('Unsupported export format', 'danger');
        }
    }

    function exportToExcel(projectId) {
        if (!projectId || projectId <= 0) {
            showAlert('Invalid project ID for Excel export', 'danger');
            return;
        }
        const url = '@Url.Action("Export", "Wbs")?projectId=' + projectId + '&format=excel';
        window.open(url, '_blank');
        showAlert('Exporting WBS to Excel...', 'info');
    }

    function exportToPdf(projectId) {
        if (!projectId || projectId <= 0) {
            showAlert('Invalid project ID for PDF export', 'danger');
            return;
        }
        const url = '@Url.Action("Export", "Wbs")?projectId=' + projectId + '&format=pdf';
        window.open(url, '_blank');
        showAlert('Exporting WBS to PDF...', 'info');
    }

    function exportToCsv(projectId) {
        if (!projectId || projectId <= 0) {
            showAlert('Invalid project ID for CSV export', 'danger');
            return;
        }
        const url = '@Url.Action("Export", "Wbs")?projectId=' + projectId + '&format=csv';
        window.open(url, '_blank');
        showAlert('Exporting WBS to CSV...', 'info');
    }

    function exportToJson(projectId) {
        if (!projectId || projectId <= 0) {
            showAlert('Invalid project ID for JSON export', 'danger');
            return;
        }
        const url = '@Url.Action("Export", "Wbs")?projectId=' + projectId + '&format=json';
        window.open(url, '_blank');
        showAlert('Exporting WBS to JSON...', 'info');
    }

    function printWbs() {
        // Create a print-friendly version
        const printWindow = window.open('', '_blank');
        const printContent = generatePrintContent();

        const projectName = '@Html.Raw(ViewBag.ProjectName ?? "Unknown Project")';
        printWindow.document.write(
            '<html>' +
                '<head>' +
                    '<title>WBS - ' + projectName + '</title>' +
                    '<style>' +
                        'body {' +
                            'font-family: Arial, sans-serif;' +
                            'margin: 20px;' +
                            'line-height: 1.4;' +
                        '}' +
                        '.print-header {' +
                            'text-align: center;' +
                            'margin-bottom: 30px;' +
                            'border-bottom: 2px solid #333;' +
                            'padding-bottom: 20px;' +
                        '}' +
                        '.print-header h1 {' +
                            'margin: 0;' +
                            'color: #333;' +
                            'font-size: 24px;' +
                        '}' +
                        '.print-date {' +
                            'color: #666;' +
                            'font-size: 12px;' +
                            'margin-top: 10px;' +
                        '}' +
                        '.wbs-node {' +
                            'margin: 15px 0;' +
                            'padding: 15px;' +
                            'border: 1px solid #ddd;' +
                            'border-radius: 5px;' +
                            'background: #f9f9f9;' +
                        '}' +
                        '.task-title {' +
                            'font-weight: bold;' +
                            'font-size: 16px;' +
                            'color: #333;' +
                            'margin-bottom: 8px;' +
                        '}' +
                        '.task-details {' +
                            'margin-top: 8px;' +
                            'font-size: 12px;' +
                            'color: #666;' +
                        '}' +
                        '.wbs-children {' +
                            'margin-left: 30px;' +
                            'border-left: 2px solid #ddd;' +
                            'padding-left: 15px;' +
                        '}' +
                        '.task-meta {' +
                            'display: flex;' +
                            'gap: 15px;' +
                            'margin-top: 5px;' +
                            'font-size: 11px;' +
                        '}' +
                        '.task-meta span {' +
                            'background: #e9e9e9;' +
                            'padding: 2px 6px;' +
                            'border-radius: 3px;' +
                        '}' +
                    '</style>' +
                '</head>' +
                '<body>' +
                    '<div class="print-header">' +
                        '<h1>Work Breakdown Structure</h1>' +
                        '<h2>' + projectName + '</h2>' +
                        '<div class="print-date">Generated on: ' + new Date().toLocaleDateString() + ' at ' + new Date().toLocaleTimeString() + '</div>' +
                    '</div>' +
                    printContent +
                '</body>' +
            '</html>');

        printWindow.document.close();
        printWindow.print();
        showAlert('Opening print dialog...', 'info');
    }

    function generatePrintContent() {
        let content = '';

        function renderTaskForPrint(task, level = 0) {
            const indent = level * 20;
            content += '<div class="wbs-node" style="margin-left: ' + indent + 'px;">' +
                '<div class="task-title">' + (task.wbsCode || 'N/A') + ' - ' + task.title + '</div>' +
                (task.description ? '<div style="margin: 5px 0; font-style: italic;">' + task.description + '</div>' : '') +
                '<div class="task-meta">' +
                    '<span>Status: ' + (task.status || 'Not Started') + '</span>' +
                    '<span>Priority: ' + (task.priority || 'Medium') + '</span>' +
                    '<span>Progress: ' + (task.progress || 0) + '%</span>' +
                    (task.assignedTo ? '<span>Assigned: ' + task.assignedTo + '</span>' : '') +
                    (task.estimatedHours ? '<span>Est. Hours: ' + task.estimatedHours + 'h</span>' : '') +
                '</div>' +
                (task.startDate || task.dueDate ?
                    '<div class="task-details">' +
                        (task.startDate ? 'Start: ' + formatDate(task.startDate) : '') +
                        (task.startDate && task.dueDate ? ' | ' : '') +
                        (task.dueDate ? 'Due: ' + formatDate(task.dueDate) : '') +
                    '</div>' : '') +
                '</div>';

            if (task.children && task.children.length > 0) {
                content += '<div class="wbs-children">';
                task.children.forEach(child => renderTaskForPrint(child, level + 1));
                content += '</div>';
            }
        }

        if (wbsData && wbsData.length > 0) {
            wbsData.forEach(task => renderTaskForPrint(task));
        } else {
            content = '<div style="text-align: center; color: #666; font-style: italic; margin: 50px 0;">No tasks found in this project.</div>';
        }

        return content;
    }

    // Export individual task

    function exportTaskDetails(taskId) {
        const task = findTaskById(wbsData, taskId);
        if (!task) {
            showAlert('Task not found', 'danger');
            return;
        }

        const taskData = {
            title: task.title,
            wbsCode: task.wbsCode || 'N/A',
            description: task.description || 'No description',
            status: task.status || 'Not Started',
            priority: task.priority || 'Medium',
            progress: task.progress || 0,
            assignedTo: task.assignedTo || 'Unassigned',
            estimatedHours: task.estimatedHours || 'Not specified',
            startDate: task.startDate ? formatDate(task.startDate) : 'Not set',
            dueDate: task.dueDate ? formatDate(task.dueDate) : 'Not set',
            createdDate: task.createdDate ? formatDate(task.createdDate) : 'Unknown'
        };

        // Create downloadable JSON file
        const dataStr = JSON.stringify(taskData, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);

        const link = document.createElement('a');
        link.href = url;
        link.download = 'task-' + (task.wbsCode || taskId) + '-details.json';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        showAlert('Task details exported successfully', 'success');
    }

    // Bulk export functions
    function exportFilteredTasks() {
        const visibleTasks = [];
        $('.wbs-node:visible').each(function() {
            const taskId = $(this).data('task-id');
            const task = findTaskById(wbsData, taskId);
            if (task) {
                visibleTasks.push(task);
            }
        });

        if (visibleTasks.length === 0) {
            showAlert('No visible tasks to export', 'warning');
            return;
        }

        const dataStr = JSON.stringify(visibleTasks, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);

        const link = document.createElement('a');
        link.href = url;
        link.download = 'filtered-tasks-' + new Date().toISOString().split('T')[0] + '.json';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        showAlert('Exported ' + visibleTasks.length + ' filtered tasks', 'success');
    }
</script>
