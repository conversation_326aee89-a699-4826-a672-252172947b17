2025-06-17 04:31:34.972 +06:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-17 04:31:37.817 +06:00 [INF] Executed DbCommand (63ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE t."DueDate" IS NOT NULL AND t."DueDate" <= @__upcomingDeadlines_0 AND t."DueDate" > now() AND t."Status" <> 4
2025-06-17 04:31:37.906 +06:00 [INF] Executed DbCommand (41ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", p0."Id", p0."IsActive", p0."JoinedAt", p0."ProjectId", p0."Role", p0."UserId"
FROM "Projects" AS p
LEFT JOIN "ProjectMembers" AS p0 ON p."Id" = p0."ProjectId"
WHERE p."EndDate" IS NOT NULL AND p."EndDate" <= @__upcomingDeadlines_0 AND p."EndDate" > now() AND p."Status" <> 5
ORDER BY p."Id"
2025-06-17 04:31:37.915 +06:00 [INF] Deadline check completed at: "2025-06-17T04:31:37.9131264+06:00"
2025-06-17 04:31:38.285 +06:00 [INF] Now listening on: https://localhost:7029
2025-06-17 04:31:38.287 +06:00 [INF] Now listening on: http://localhost:5045
2025-06-17 04:31:38.589 +06:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-17 04:31:38.591 +06:00 [INF] Hosting environment: Development
2025-06-17 04:31:38.594 +06:00 [INF] Content root path: D:\TGI\PM.Tool\PM.Tool
2025-06-17 04:31:40.554 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/ - null null
2025-06-17 04:31:41.006 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-17 04:31:41.241 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__user_Id_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ClaimType", a."ClaimValue", a."UserId"
FROM "AspNetUserClaims" AS a
WHERE a."UserId" = @__user_Id_0
2025-06-17 04:31:41.269 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a0."Name"
FROM "AspNetUserRoles" AS a
INNER JOIN "AspNetRoles" AS a0 ON a."RoleId" = a0."Id"
WHERE a."UserId" = @__userId_0
2025-06-17 04:31:41.294 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-17 04:31:41.330 +06:00 [INF] Executed DbCommand (10ms) [Parameters=[@__role_Id_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."ClaimType", a."ClaimValue"
FROM "AspNetRoleClaims" AS a
WHERE a."RoleId" = @__role_Id_0
2025-06-17 04:31:41.350 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-17 04:31:41.437 +06:00 [INF] Route matched with {action = "Index", controller = "Home", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-17 04:31:41.534 +06:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-17 04:31:41.583 +06:00 [INF] Executed DbCommand (15ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-17 04:31:41.763 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-17 04:31:41.792 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" < now()
ORDER BY t."DueDate"
2025-06-17 04:31:41.816 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__dueDate_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
FROM "Tasks" AS t
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" <= @__dueDate_0
2025-06-17 04:31:41.851 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT n."Id", n."Title", n."Message", n."Type", n."IsRead", n."CreatedAt", n."RelatedProjectId", n."RelatedTaskId"
FROM "Notifications" AS n
WHERE n."UserId" = @__user_Id_0
ORDER BY n."CreatedAt" DESC
LIMIT @__p_1
2025-06-17 04:31:41.878 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."Name", m."Description", m."ProjectId", p."Name", m."DueDate", m."CompletedDate", m."IsCompleted", m."CreatedAt", m."IsDeleted", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."StartDate", p."Status", p."UpdatedAt"
FROM "Milestones" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
WHERE EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__user_Id_0 AND p0."IsActive") AND NOT (m."IsCompleted") AND m."DueDate" >= now()
ORDER BY m."DueDate"
LIMIT @__p_1
2025-06-17 04:31:41.900 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-17 04:31:42.128 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/css/tailwind.css?v=Vk1BkLOsT7-Yh4o1pDln8tZyZ3_rFi9yXuRHMX2g50E - null null
2025-06-17 04:31:42.132 +06:00 [INF] Executed ViewResult - view Index executed in 235.8711ms.
2025-06-17 04:31:42.139 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.Index (PM.Tool) in 696.1933ms
2025-06-17 04:31:42.142 +06:00 [INF] The file /css/tailwind.css was not modified
2025-06-17 04:31:42.142 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-17 04:31:42.148 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/css/tailwind.css?v=Vk1BkLOsT7-Yh4o1pDln8tZyZ3_rFi9yXuRHMX2g50E - 304 null text/css 19.094ms
2025-06-17 04:31:42.154 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/ - 200 null text/html; charset=utf-8 1610.5575ms
2025-06-17 04:31:42.188 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 04:31:42.188 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/js/site.js?v=gfa0xw_jEM_w5r3HMCk1LwRfuj7T6ktfvUiYt5ZeuS4 - null null
2025-06-17 04:31:42.188 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/lib/jquery/dist/jquery.min.js - null null
2025-06-17 04:31:42.262 +06:00 [INF] The file /js/site.js was not modified
2025-06-17 04:31:42.280 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 92.4148ms
2025-06-17 04:31:42.188 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/js/tailwind-theme-manager.js?v=QS9OsoodixBTZAmxGj2gDzPtp60VU7kbda0nOueg4T0 - null null
2025-06-17 04:31:42.279 +06:00 [INF] The file /lib/jquery/dist/jquery.min.js was not modified
2025-06-17 04:31:42.254 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-17 04:31:42.281 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/js/site.js?v=gfa0xw_jEM_w5r3HMCk1LwRfuj7T6ktfvUiYt5ZeuS4 - 304 null text/javascript 92.6865ms
2025-06-17 04:31:42.288 +06:00 [INF] The file /js/tailwind-theme-manager.js was not modified
2025-06-17 04:31:42.289 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/lib/jquery/dist/jquery.min.js - 304 null text/javascript 100.4955ms
2025-06-17 04:31:42.299 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/js/tailwind-theme-manager.js?v=QS9OsoodixBTZAmxGj2gDzPtp60VU7kbda0nOueg4T0 - 304 null text/javascript 110.7407ms
2025-06-17 04:31:42.417 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 192.1647ms
2025-06-17 04:31:51.186 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/docs - null null
2025-06-17 04:31:51.203 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.DocumentationController.Index (PM.Tool)'
2025-06-17 04:31:51.210 +06:00 [INF] Route matched with {action = "Index", controller = "Documentation", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.DocumentationController (PM.Tool).
2025-06-17 04:31:51.374 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-17 04:31:51.413 +06:00 [INF] Executed ViewResult - view Index executed in 55.5449ms.
2025-06-17 04:31:51.416 +06:00 [INF] Executed action PM.Tool.Controllers.DocumentationController.Index (PM.Tool) in 203.1544ms
2025-06-17 04:31:51.420 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.DocumentationController.Index (PM.Tool)'
2025-06-17 04:31:51.423 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/docs - 200 null text/html; charset=utf-8 236.6793ms
2025-06-17 04:31:51.431 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/css/output.css - null null
2025-06-17 04:31:51.431 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 04:31:51.432 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/js/theme.js - null null
2025-06-17 04:31:51.432 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/css/documentation.css - null null
2025-06-17 04:31:51.432 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-17 04:31:51.443 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 11.8256ms
2025-06-17 04:31:51.449 +06:00 [INF] The file /css/documentation.css was not modified
2025-06-17 04:31:51.499 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 66.2364ms
2025-06-17 04:31:51.500 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/css/documentation.css - 304 null text/css 67.4602ms
2025-06-17 04:31:51.500 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/css/output.css - 404 0 null 70.0264ms
2025-06-17 04:31:51.500 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/js/theme.js - 404 0 null 68.3891ms
2025-06-17 04:31:51.515 +06:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7029/css/output.css, Response status code: 404
2025-06-17 04:31:51.518 +06:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7029/js/theme.js, Response status code: 404
2025-06-17 04:31:54.101 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/ - null null
2025-06-17 04:31:54.109 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-17 04:31:54.112 +06:00 [INF] Route matched with {action = "Index", controller = "Home", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-17 04:31:54.335 +06:00 [INF] Executed DbCommand (131ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-17 04:31:54.421 +06:00 [INF] Executed DbCommand (82ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-17 04:31:54.438 +06:00 [INF] Executed DbCommand (11ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-17 04:31:54.445 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" < now()
ORDER BY t."DueDate"
2025-06-17 04:31:54.525 +06:00 [INF] Executed DbCommand (77ms) [Parameters=[@__dueDate_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
FROM "Tasks" AS t
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" <= @__dueDate_0
2025-06-17 04:31:54.650 +06:00 [INF] Executed DbCommand (114ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT n."Id", n."Title", n."Message", n."Type", n."IsRead", n."CreatedAt", n."RelatedProjectId", n."RelatedTaskId"
FROM "Notifications" AS n
WHERE n."UserId" = @__user_Id_0
ORDER BY n."CreatedAt" DESC
LIMIT @__p_1
2025-06-17 04:31:54.946 +06:00 [INF] Executed DbCommand (282ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."Name", m."Description", m."ProjectId", p."Name", m."DueDate", m."CompletedDate", m."IsCompleted", m."CreatedAt", m."IsDeleted", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."StartDate", p."Status", p."UpdatedAt"
FROM "Milestones" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
WHERE EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__user_Id_0 AND p0."IsActive") AND NOT (m."IsCompleted") AND m."DueDate" >= now()
ORDER BY m."DueDate"
LIMIT @__p_1
2025-06-17 04:31:54.949 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-17 04:31:54.959 +06:00 [INF] Executed ViewResult - view Index executed in 9.8485ms.
2025-06-17 04:31:54.962 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.Index (PM.Tool) in 848.6041ms
2025-06-17 04:31:54.964 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-17 04:31:54.967 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/ - 200 null text/html; charset=utf-8 866.192ms
2025-06-17 04:31:54.969 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 04:31:54.976 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 6.7774ms
2025-06-17 04:32:00.830 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Agile - null null
2025-06-17 04:32:00.844 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.AgileController.Index (PM.Tool)'
2025-06-17 04:32:00.849 +06:00 [INF] Route matched with {action = "Index", controller = "Agile", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.AgileController (PM.Tool).
2025-06-17 04:32:00.867 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-17 04:32:00.897 +06:00 [INF] Executed DbCommand (21ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-17 04:32:00.933 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__projectId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT e."Id", e."AcceptanceCriteria", e."ActualStoryPoints", e."BusinessValue", e."CompletedDate", e."CreatedAt", e."DeletedAt", e."Description", e."EpicKey", e."EstimatedStoryPoints", e."IsDeleted", e."OwnerId", e."Priority", e."ProjectId", e."SortOrder", e."Status", e."Tags", e."TargetDate", e."Title", e."UpdatedAt", u."Id", u."AcceptanceCriteria", u."AsA", u."AssignedToUserId", u."BacklogOrder", u."CompletedDate", u."CreatedAt", u."DeletedAt", u."Description", u."EpicId", u."IWant", u."IsDeleted", u."KanbanColumn", u."Priority", u."ProjectId", u."SoThat", u."SprintId", u."Status", u."StoryKey", u."StoryPoints", u."Tags", u."TargetDate", u."Title", u."UpdatedAt"
FROM "Epics" AS e
LEFT JOIN "UserStories" AS u ON e."Id" = u."EpicId"
WHERE e."ProjectId" = @__projectId_0
ORDER BY e."Priority", e."Id"
2025-06-17 04:32:00.944 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__projectId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT e."Id", e."AcceptanceCriteria", e."ActualStoryPoints", e."BusinessValue", e."CompletedDate", e."CreatedAt", e."DeletedAt", e."Description", e."EpicKey", e."EstimatedStoryPoints", e."IsDeleted", e."OwnerId", e."Priority", e."ProjectId", e."SortOrder", e."Status", e."Tags", e."TargetDate", e."Title", e."UpdatedAt", u."Id", u."AcceptanceCriteria", u."AsA", u."AssignedToUserId", u."BacklogOrder", u."CompletedDate", u."CreatedAt", u."DeletedAt", u."Description", u."EpicId", u."IWant", u."IsDeleted", u."KanbanColumn", u."Priority", u."ProjectId", u."SoThat", u."SprintId", u."Status", u."StoryKey", u."StoryPoints", u."Tags", u."TargetDate", u."Title", u."UpdatedAt"
FROM "Epics" AS e
LEFT JOIN "UserStories" AS u ON e."Id" = u."EpicId"
WHERE e."ProjectId" = @__projectId_0
ORDER BY e."Priority", e."Id"
2025-06-17 04:32:00.952 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__projectId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT e."Id", e."AcceptanceCriteria", e."ActualStoryPoints", e."BusinessValue", e."CompletedDate", e."CreatedAt", e."DeletedAt", e."Description", e."EpicKey", e."EstimatedStoryPoints", e."IsDeleted", e."OwnerId", e."Priority", e."ProjectId", e."SortOrder", e."Status", e."Tags", e."TargetDate", e."Title", e."UpdatedAt", u."Id", u."AcceptanceCriteria", u."AsA", u."AssignedToUserId", u."BacklogOrder", u."CompletedDate", u."CreatedAt", u."DeletedAt", u."Description", u."EpicId", u."IWant", u."IsDeleted", u."KanbanColumn", u."Priority", u."ProjectId", u."SoThat", u."SprintId", u."Status", u."StoryKey", u."StoryPoints", u."Tags", u."TargetDate", u."Title", u."UpdatedAt"
FROM "Epics" AS e
LEFT JOIN "UserStories" AS u ON e."Id" = u."EpicId"
WHERE e."ProjectId" = @__projectId_0
ORDER BY e."Priority", e."Id"
2025-06-17 04:32:00.967 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-17 04:32:01.035 +06:00 [INF] Executed ViewResult - view Index executed in 68.9342ms.
2025-06-17 04:32:01.039 +06:00 [INF] Executed action PM.Tool.Controllers.AgileController.Index (PM.Tool) in 186.6681ms
2025-06-17 04:32:01.042 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.AgileController.Index (PM.Tool)'
2025-06-17 04:32:01.044 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Agile - 200 null text/html; charset=utf-8 214.0829ms
2025-06-17 04:32:01.046 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 04:32:01.046 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-17 04:32:01.052 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 6.7213ms
2025-06-17 04:32:01.064 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 18.1378ms
2025-06-17 04:32:03.571 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Agile/GetSprints - null null
2025-06-17 04:32:03.581 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.AgileController.GetSprints (PM.Tool)'
2025-06-17 04:32:03.590 +06:00 [INF] Route matched with {action = "GetSprints", controller = "Agile", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetSprints() on controller PM.Tool.Controllers.AgileController (PM.Tool).
2025-06-17 04:32:03.603 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-17 04:32:03.619 +06:00 [INF] Executed DbCommand (13ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-17 04:32:03.637 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__projectId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT s."Id", s."CompletedStoryPoints", s."CreatedAt", s."DeletedAt", s."Description", s."EndDate", s."Goal", s."IsDeleted", s."Name", s."PlannedStoryPoints", s."ProjectId", s."ScrumMasterId", s."SprintKey", s."StartDate", s."Status", s."UpdatedAt"
FROM "Sprints" AS s
WHERE s."ProjectId" = @__projectId_0
ORDER BY s."StartDate" DESC
2025-06-17 04:32:03.653 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__projectId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT s."Id", s."CompletedStoryPoints", s."CreatedAt", s."DeletedAt", s."Description", s."EndDate", s."Goal", s."IsDeleted", s."Name", s."PlannedStoryPoints", s."ProjectId", s."ScrumMasterId", s."SprintKey", s."StartDate", s."Status", s."UpdatedAt"
FROM "Sprints" AS s
WHERE s."ProjectId" = @__projectId_0
ORDER BY s."StartDate" DESC
2025-06-17 04:32:03.665 +06:00 [INF] Executed DbCommand (3ms) [Parameters=[@__projectId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT s."Id", s."CompletedStoryPoints", s."CreatedAt", s."DeletedAt", s."Description", s."EndDate", s."Goal", s."IsDeleted", s."Name", s."PlannedStoryPoints", s."ProjectId", s."ScrumMasterId", s."SprintKey", s."StartDate", s."Status", s."UpdatedAt"
FROM "Sprints" AS s
WHERE s."ProjectId" = @__projectId_0
ORDER BY s."StartDate" DESC
2025-06-17 04:32:03.679 +06:00 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType29`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-17 04:32:03.694 +06:00 [INF] Executed action PM.Tool.Controllers.AgileController.GetSprints (PM.Tool) in 100.548ms
2025-06-17 04:32:03.698 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.AgileController.GetSprints (PM.Tool)'
2025-06-17 04:32:03.699 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Agile/GetSprints - 200 null application/json; charset=utf-8 127.4142ms
2025-06-17 04:32:05.481 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Agile/GetBacklog - null null
2025-06-17 04:32:05.504 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.AgileController.GetBacklog (PM.Tool)'
2025-06-17 04:32:05.512 +06:00 [INF] Route matched with {action = "GetBacklog", controller = "Agile", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetBacklog() on controller PM.Tool.Controllers.AgileController (PM.Tool).
2025-06-17 04:32:05.534 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-17 04:32:05.581 +06:00 [INF] Executed DbCommand (40ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-17 04:32:05.647 +06:00 [INF] Executed DbCommand (24ms) [Parameters=[@__projectId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AcceptanceCriteria", u."AsA", u."AssignedToUserId", u."BacklogOrder", u."CompletedDate", u."CreatedAt", u."DeletedAt", u."Description", u."EpicId", u."IWant", u."IsDeleted", u."KanbanColumn", u."Priority", u."ProjectId", u."SoThat", u."SprintId", u."Status", u."StoryKey", u."StoryPoints", u."Tags", u."TargetDate", u."Title", u."UpdatedAt", e."Id", e."AcceptanceCriteria", e."ActualStoryPoints", e."BusinessValue", e."CompletedDate", e."CreatedAt", e."DeletedAt", e."Description", e."EpicKey", e."EstimatedStoryPoints", e."IsDeleted", e."OwnerId", e."Priority", e."ProjectId", e."SortOrder", e."Status", e."Tags", e."TargetDate", e."Title", e."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "UserStories" AS u
LEFT JOIN "Epics" AS e ON u."EpicId" = e."Id"
LEFT JOIN "AspNetUsers" AS a ON u."AssignedToUserId" = a."Id"
WHERE u."ProjectId" = @__projectId_0 AND u."SprintId" IS NULL
ORDER BY u."Priority"
2025-06-17 04:32:05.663 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__projectId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AcceptanceCriteria", u."AsA", u."AssignedToUserId", u."BacklogOrder", u."CompletedDate", u."CreatedAt", u."DeletedAt", u."Description", u."EpicId", u."IWant", u."IsDeleted", u."KanbanColumn", u."Priority", u."ProjectId", u."SoThat", u."SprintId", u."Status", u."StoryKey", u."StoryPoints", u."Tags", u."TargetDate", u."Title", u."UpdatedAt", e."Id", e."AcceptanceCriteria", e."ActualStoryPoints", e."BusinessValue", e."CompletedDate", e."CreatedAt", e."DeletedAt", e."Description", e."EpicKey", e."EstimatedStoryPoints", e."IsDeleted", e."OwnerId", e."Priority", e."ProjectId", e."SortOrder", e."Status", e."Tags", e."TargetDate", e."Title", e."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "UserStories" AS u
LEFT JOIN "Epics" AS e ON u."EpicId" = e."Id"
LEFT JOIN "AspNetUsers" AS a ON u."AssignedToUserId" = a."Id"
WHERE u."ProjectId" = @__projectId_0 AND u."SprintId" IS NULL
ORDER BY u."Priority"
2025-06-17 04:32:05.677 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__projectId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT u."Id", u."AcceptanceCriteria", u."AsA", u."AssignedToUserId", u."BacklogOrder", u."CompletedDate", u."CreatedAt", u."DeletedAt", u."Description", u."EpicId", u."IWant", u."IsDeleted", u."KanbanColumn", u."Priority", u."ProjectId", u."SoThat", u."SprintId", u."Status", u."StoryKey", u."StoryPoints", u."Tags", u."TargetDate", u."Title", u."UpdatedAt", e."Id", e."AcceptanceCriteria", e."ActualStoryPoints", e."BusinessValue", e."CompletedDate", e."CreatedAt", e."DeletedAt", e."Description", e."EpicKey", e."EstimatedStoryPoints", e."IsDeleted", e."OwnerId", e."Priority", e."ProjectId", e."SortOrder", e."Status", e."Tags", e."TargetDate", e."Title", e."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "UserStories" AS u
LEFT JOIN "Epics" AS e ON u."EpicId" = e."Id"
LEFT JOIN "AspNetUsers" AS a ON u."AssignedToUserId" = a."Id"
WHERE u."ProjectId" = @__projectId_0 AND u."SprintId" IS NULL
ORDER BY u."Priority"
2025-06-17 04:32:05.682 +06:00 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType29`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-17 04:32:05.683 +06:00 [INF] Executed action PM.Tool.Controllers.AgileController.GetBacklog (PM.Tool) in 164.4377ms
2025-06-17 04:32:05.685 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.AgileController.GetBacklog (PM.Tool)'
2025-06-17 04:32:05.686 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Agile/GetBacklog - 200 null application/json; charset=utf-8 204.9766ms
2025-06-17 04:32:06.501 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Agile/GetBurndownData - null null
2025-06-17 04:32:06.508 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.AgileController.GetBurndownData (PM.Tool)'
2025-06-17 04:32:06.521 +06:00 [INF] Route matched with {action = "GetBurndownData", controller = "Agile", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetBurndownData(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.AgileController (PM.Tool).
2025-06-17 04:32:06.549 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-17 04:32:06.557 +06:00 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType30`2[[System.String[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[<>f__AnonymousType31`5[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]][], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-17 04:32:06.565 +06:00 [INF] Executed action PM.Tool.Controllers.AgileController.GetBurndownData (PM.Tool) in 39.8668ms
2025-06-17 04:32:06.567 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.AgileController.GetBurndownData (PM.Tool)'
2025-06-17 04:32:06.568 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Agile/GetBurndownData - 200 null application/json; charset=utf-8 66.766ms
2025-06-17 04:32:09.836 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Resource - null null
2025-06-17 04:32:09.844 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ResourceController.Index (PM.Tool)'
2025-06-17 04:32:09.849 +06:00 [INF] Route matched with {action = "Index", controller = "Resource", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.ResourceController (PM.Tool).
2025-06-17 04:32:09.886 +06:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."Capacity", r."CreatedAt", r."DeletedAt", r."Department", r."Description", r."Email", r."HourlyRate", r."IsActive", r."IsDeleted", r."Location", r."Name", r."Phone", r."Skills", r."Type", r."UpdatedAt", r."UserId", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", s0."Id", s0."CertificationDate", s0."CertificationExpiry", s0."CreatedAt", s0."DeletedAt", s0."IsDeleted", s0."Level", s0."Notes", s0."ResourceId", s0."SkillId", s0."UpdatedAt", s0."YearsOfExperience", s0."Id0", s0."Category", s0."CreatedAt0", s0."DeletedAt0", s0."Description", s0."IsActive", s0."IsDeleted0", s0."Name", s0."UpdatedAt0"
FROM "Resources" AS r
LEFT JOIN "AspNetUsers" AS a ON r."UserId" = a."Id"
LEFT JOIN (
    SELECT r0."Id", r0."CertificationDate", r0."CertificationExpiry", r0."CreatedAt", r0."DeletedAt", r0."IsDeleted", r0."Level", r0."Notes", r0."ResourceId", r0."SkillId", r0."UpdatedAt", r0."YearsOfExperience", s."Id" AS "Id0", s."Category", s."CreatedAt" AS "CreatedAt0", s."DeletedAt" AS "DeletedAt0", s."Description", s."IsActive", s."IsDeleted" AS "IsDeleted0", s."Name", s."UpdatedAt" AS "UpdatedAt0"
    FROM "ResourceSkills" AS r0
    INNER JOIN "Skills" AS s ON r0."SkillId" = s."Id"
) AS s0 ON r."Id" = s0."ResourceId"
WHERE NOT (r."IsDeleted")
ORDER BY r."Name", r."Id", a."Id", s0."Id"
2025-06-17 04:32:09.894 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-17 04:32:09.937 +06:00 [INF] Executed ViewResult - view Index executed in 43.8382ms.
2025-06-17 04:32:09.941 +06:00 [INF] Executed action PM.Tool.Controllers.ResourceController.Index (PM.Tool) in 90.5502ms
2025-06-17 04:32:09.943 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ResourceController.Index (PM.Tool)'
2025-06-17 04:32:09.945 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Resource - 200 null text/html; charset=utf-8 109.4013ms
2025-06-17 04:32:09.947 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 04:32:09.947 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-17 04:32:09.953 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 5.4375ms
2025-06-17 04:32:09.961 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 13.5928ms
2025-06-17 04:32:20.895 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Wbs - null null
2025-06-17 04:32:20.904 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.WbsController.Index (PM.Tool)'
2025-06-17 04:32:20.911 +06:00 [INF] Route matched with {action = "Index", controller = "Wbs", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index(Int32) on controller PM.Tool.Controllers.WbsController (PM.Tool).
2025-06-17 04:32:20.940 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt"
FROM "Projects" AS p
WHERE p."Id" = @__id_0 AND NOT (p."IsDeleted")
LIMIT 1
2025-06-17 04:32:20.957 +06:00 [INF] Executing StatusCodeResult, setting HTTP status code 404
2025-06-17 04:32:20.959 +06:00 [INF] Executed action PM.Tool.Controllers.WbsController.Index (PM.Tool) in 43.7139ms
2025-06-17 04:32:20.963 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.WbsController.Index (PM.Tool)'
2025-06-17 04:32:20.964 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Wbs - 404 0 null 68.3695ms
2025-06-17 04:32:24.200 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Resource - null null
2025-06-17 04:32:24.205 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ResourceController.Index (PM.Tool)'
2025-06-17 04:32:24.207 +06:00 [INF] Route matched with {action = "Index", controller = "Resource", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.ResourceController (PM.Tool).
2025-06-17 04:32:24.219 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."Capacity", r."CreatedAt", r."DeletedAt", r."Department", r."Description", r."Email", r."HourlyRate", r."IsActive", r."IsDeleted", r."Location", r."Name", r."Phone", r."Skills", r."Type", r."UpdatedAt", r."UserId", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", s0."Id", s0."CertificationDate", s0."CertificationExpiry", s0."CreatedAt", s0."DeletedAt", s0."IsDeleted", s0."Level", s0."Notes", s0."ResourceId", s0."SkillId", s0."UpdatedAt", s0."YearsOfExperience", s0."Id0", s0."Category", s0."CreatedAt0", s0."DeletedAt0", s0."Description", s0."IsActive", s0."IsDeleted0", s0."Name", s0."UpdatedAt0"
FROM "Resources" AS r
LEFT JOIN "AspNetUsers" AS a ON r."UserId" = a."Id"
LEFT JOIN (
    SELECT r0."Id", r0."CertificationDate", r0."CertificationExpiry", r0."CreatedAt", r0."DeletedAt", r0."IsDeleted", r0."Level", r0."Notes", r0."ResourceId", r0."SkillId", r0."UpdatedAt", r0."YearsOfExperience", s."Id" AS "Id0", s."Category", s."CreatedAt" AS "CreatedAt0", s."DeletedAt" AS "DeletedAt0", s."Description", s."IsActive", s."IsDeleted" AS "IsDeleted0", s."Name", s."UpdatedAt" AS "UpdatedAt0"
    FROM "ResourceSkills" AS r0
    INNER JOIN "Skills" AS s ON r0."SkillId" = s."Id"
) AS s0 ON r."Id" = s0."ResourceId"
WHERE NOT (r."IsDeleted")
ORDER BY r."Name", r."Id", a."Id", s0."Id"
2025-06-17 04:32:24.223 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-17 04:32:24.229 +06:00 [INF] Executed ViewResult - view Index executed in 5.4615ms.
2025-06-17 04:32:24.231 +06:00 [INF] Executed action PM.Tool.Controllers.ResourceController.Index (PM.Tool) in 20.625ms
2025-06-17 04:32:24.233 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ResourceController.Index (PM.Tool)'
2025-06-17 04:32:24.235 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Resource - 200 null text/html; charset=utf-8 34.9204ms
2025-06-17 04:32:24.244 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 04:32:24.257 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 13.2581ms
2025-06-17 04:32:27.451 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Resource/Create - null null
2025-06-17 04:32:27.461 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ResourceController.Create (PM.Tool)'
2025-06-17 04:32:27.465 +06:00 [INF] Route matched with {action = "Create", controller = "Resource", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Create() on controller PM.Tool.Controllers.ResourceController (PM.Tool).
2025-06-17 04:32:27.472 +06:00 [INF] Executing ViewResult, running view Create.
2025-06-17 04:32:27.558 +06:00 [INF] Executed ViewResult - view Create executed in 87.1066ms.
2025-06-17 04:32:27.562 +06:00 [INF] Executed action PM.Tool.Controllers.ResourceController.Create (PM.Tool) in 95.2897ms
2025-06-17 04:32:27.565 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ResourceController.Create (PM.Tool)'
2025-06-17 04:32:27.567 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Resource/Create - 200 null text/html; charset=utf-8 115.7297ms
2025-06-17 04:32:27.570 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 04:32:27.570 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - null null
2025-06-17 04:32:27.570 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/lib/jquery-validation/dist/jquery.validate.min.js - null null
2025-06-17 04:32:27.573 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-17 04:32:27.576 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 6.1178ms
2025-06-17 04:32:27.581 +06:00 [INF] The file /lib/jquery-validation/dist/jquery.validate.min.js was not modified
2025-06-17 04:32:27.579 +06:00 [INF] The file /lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js was not modified
2025-06-17 04:32:27.588 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/lib/jquery-validation/dist/jquery.validate.min.js - 304 null text/javascript 17.7134ms
2025-06-17 04:32:27.596 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 23.3942ms
2025-06-17 04:32:27.597 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - 304 null text/javascript 27.5944ms
2025-06-17 04:32:42.087 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Resource - null null
2025-06-17 04:32:42.094 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ResourceController.Index (PM.Tool)'
2025-06-17 04:32:42.097 +06:00 [INF] Route matched with {action = "Index", controller = "Resource", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.ResourceController (PM.Tool).
2025-06-17 04:32:42.107 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."Capacity", r."CreatedAt", r."DeletedAt", r."Department", r."Description", r."Email", r."HourlyRate", r."IsActive", r."IsDeleted", r."Location", r."Name", r."Phone", r."Skills", r."Type", r."UpdatedAt", r."UserId", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", s0."Id", s0."CertificationDate", s0."CertificationExpiry", s0."CreatedAt", s0."DeletedAt", s0."IsDeleted", s0."Level", s0."Notes", s0."ResourceId", s0."SkillId", s0."UpdatedAt", s0."YearsOfExperience", s0."Id0", s0."Category", s0."CreatedAt0", s0."DeletedAt0", s0."Description", s0."IsActive", s0."IsDeleted0", s0."Name", s0."UpdatedAt0"
FROM "Resources" AS r
LEFT JOIN "AspNetUsers" AS a ON r."UserId" = a."Id"
LEFT JOIN (
    SELECT r0."Id", r0."CertificationDate", r0."CertificationExpiry", r0."CreatedAt", r0."DeletedAt", r0."IsDeleted", r0."Level", r0."Notes", r0."ResourceId", r0."SkillId", r0."UpdatedAt", r0."YearsOfExperience", s."Id" AS "Id0", s."Category", s."CreatedAt" AS "CreatedAt0", s."DeletedAt" AS "DeletedAt0", s."Description", s."IsActive", s."IsDeleted" AS "IsDeleted0", s."Name", s."UpdatedAt" AS "UpdatedAt0"
    FROM "ResourceSkills" AS r0
    INNER JOIN "Skills" AS s ON r0."SkillId" = s."Id"
) AS s0 ON r."Id" = s0."ResourceId"
WHERE NOT (r."IsDeleted")
ORDER BY r."Name", r."Id", a."Id", s0."Id"
2025-06-17 04:32:42.110 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-17 04:32:42.116 +06:00 [INF] Executed ViewResult - view Index executed in 5.8762ms.
2025-06-17 04:32:42.118 +06:00 [INF] Executed action PM.Tool.Controllers.ResourceController.Index (PM.Tool) in 18.5463ms
2025-06-17 04:32:42.120 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ResourceController.Index (PM.Tool)'
2025-06-17 04:32:42.121 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Resource - 200 null text/html; charset=utf-8 34.6163ms
2025-06-17 04:32:42.127 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 04:32:42.127 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-17 04:32:42.130 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 3.3053ms
2025-06-17 04:32:42.136 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 9.3361ms
2025-06-17 04:32:45.400 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Resource/Capacity - null null
2025-06-17 04:32:45.408 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Resource/Capacity - 404 0 null 7.7893ms
2025-06-17 04:32:45.411 +06:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7029/Resource/Capacity, Response status code: 404
2025-06-17 04:32:47.788 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Resource - null null
2025-06-17 04:32:47.794 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ResourceController.Index (PM.Tool)'
2025-06-17 04:32:47.797 +06:00 [INF] Route matched with {action = "Index", controller = "Resource", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.ResourceController (PM.Tool).
2025-06-17 04:32:47.810 +06:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."Capacity", r."CreatedAt", r."DeletedAt", r."Department", r."Description", r."Email", r."HourlyRate", r."IsActive", r."IsDeleted", r."Location", r."Name", r."Phone", r."Skills", r."Type", r."UpdatedAt", r."UserId", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", s0."Id", s0."CertificationDate", s0."CertificationExpiry", s0."CreatedAt", s0."DeletedAt", s0."IsDeleted", s0."Level", s0."Notes", s0."ResourceId", s0."SkillId", s0."UpdatedAt", s0."YearsOfExperience", s0."Id0", s0."Category", s0."CreatedAt0", s0."DeletedAt0", s0."Description", s0."IsActive", s0."IsDeleted0", s0."Name", s0."UpdatedAt0"
FROM "Resources" AS r
LEFT JOIN "AspNetUsers" AS a ON r."UserId" = a."Id"
LEFT JOIN (
    SELECT r0."Id", r0."CertificationDate", r0."CertificationExpiry", r0."CreatedAt", r0."DeletedAt", r0."IsDeleted", r0."Level", r0."Notes", r0."ResourceId", r0."SkillId", r0."UpdatedAt", r0."YearsOfExperience", s."Id" AS "Id0", s."Category", s."CreatedAt" AS "CreatedAt0", s."DeletedAt" AS "DeletedAt0", s."Description", s."IsActive", s."IsDeleted" AS "IsDeleted0", s."Name", s."UpdatedAt" AS "UpdatedAt0"
    FROM "ResourceSkills" AS r0
    INNER JOIN "Skills" AS s ON r0."SkillId" = s."Id"
) AS s0 ON r."Id" = s0."ResourceId"
WHERE NOT (r."IsDeleted")
ORDER BY r."Name", r."Id", a."Id", s0."Id"
2025-06-17 04:32:47.814 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-17 04:32:47.817 +06:00 [INF] Executed ViewResult - view Index executed in 2.7743ms.
2025-06-17 04:32:47.819 +06:00 [INF] Executed action PM.Tool.Controllers.ResourceController.Index (PM.Tool) in 19.2801ms
2025-06-17 04:32:47.820 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ResourceController.Index (PM.Tool)'
2025-06-17 04:32:47.822 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Resource - 200 null text/html; charset=utf-8 34.1765ms
2025-06-17 04:32:47.829 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 04:32:47.844 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 14.496ms
2025-06-17 04:32:49.681 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Resource/Schedule - null null
2025-06-17 04:32:49.688 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Resource/Schedule - 404 0 null 7.2819ms
2025-06-17 04:32:49.692 +06:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7029/Resource/Schedule, Response status code: 404
2025-06-17 04:32:52.332 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Resource - null null
2025-06-17 04:32:52.338 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ResourceController.Index (PM.Tool)'
2025-06-17 04:32:52.341 +06:00 [INF] Route matched with {action = "Index", controller = "Resource", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.ResourceController (PM.Tool).
2025-06-17 04:32:52.354 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."Capacity", r."CreatedAt", r."DeletedAt", r."Department", r."Description", r."Email", r."HourlyRate", r."IsActive", r."IsDeleted", r."Location", r."Name", r."Phone", r."Skills", r."Type", r."UpdatedAt", r."UserId", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", s0."Id", s0."CertificationDate", s0."CertificationExpiry", s0."CreatedAt", s0."DeletedAt", s0."IsDeleted", s0."Level", s0."Notes", s0."ResourceId", s0."SkillId", s0."UpdatedAt", s0."YearsOfExperience", s0."Id0", s0."Category", s0."CreatedAt0", s0."DeletedAt0", s0."Description", s0."IsActive", s0."IsDeleted0", s0."Name", s0."UpdatedAt0"
FROM "Resources" AS r
LEFT JOIN "AspNetUsers" AS a ON r."UserId" = a."Id"
LEFT JOIN (
    SELECT r0."Id", r0."CertificationDate", r0."CertificationExpiry", r0."CreatedAt", r0."DeletedAt", r0."IsDeleted", r0."Level", r0."Notes", r0."ResourceId", r0."SkillId", r0."UpdatedAt", r0."YearsOfExperience", s."Id" AS "Id0", s."Category", s."CreatedAt" AS "CreatedAt0", s."DeletedAt" AS "DeletedAt0", s."Description", s."IsActive", s."IsDeleted" AS "IsDeleted0", s."Name", s."UpdatedAt" AS "UpdatedAt0"
    FROM "ResourceSkills" AS r0
    INNER JOIN "Skills" AS s ON r0."SkillId" = s."Id"
) AS s0 ON r."Id" = s0."ResourceId"
WHERE NOT (r."IsDeleted")
ORDER BY r."Name", r."Id", a."Id", s0."Id"
2025-06-17 04:32:52.358 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-17 04:32:52.363 +06:00 [INF] Executed ViewResult - view Index executed in 4.8937ms.
2025-06-17 04:32:52.364 +06:00 [INF] Executed action PM.Tool.Controllers.ResourceController.Index (PM.Tool) in 19.4862ms
2025-06-17 04:32:52.366 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ResourceController.Index (PM.Tool)'
2025-06-17 04:32:52.367 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Resource - 200 null text/html; charset=utf-8 35.2196ms
2025-06-17 04:32:52.377 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 04:32:52.386 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 9.107ms
2025-06-17 04:33:30.013 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Resource/Create - null null
2025-06-17 04:33:30.021 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ResourceController.Create (PM.Tool)'
2025-06-17 04:33:30.023 +06:00 [INF] Route matched with {action = "Create", controller = "Resource", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Create() on controller PM.Tool.Controllers.ResourceController (PM.Tool).
2025-06-17 04:33:30.026 +06:00 [INF] Executing ViewResult, running view Create.
2025-06-17 04:33:30.097 +06:00 [INF] Executed ViewResult - view Create executed in 70.9638ms.
2025-06-17 04:33:30.102 +06:00 [INF] Executed action PM.Tool.Controllers.ResourceController.Create (PM.Tool) in 75.9638ms
2025-06-17 04:33:30.107 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ResourceController.Create (PM.Tool)'
2025-06-17 04:33:30.109 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Resource/Create - 200 null text/html; charset=utf-8 96.6021ms
2025-06-17 04:33:30.127 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 04:33:30.128 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-17 04:33:30.131 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 4.2188ms
2025-06-17 04:33:30.138 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 10.1427ms
2025-06-17 04:33:35.430 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Resource - null null
2025-06-17 04:33:35.437 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ResourceController.Index (PM.Tool)'
2025-06-17 04:33:35.439 +06:00 [INF] Route matched with {action = "Index", controller = "Resource", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.ResourceController (PM.Tool).
2025-06-17 04:33:35.452 +06:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."Capacity", r."CreatedAt", r."DeletedAt", r."Department", r."Description", r."Email", r."HourlyRate", r."IsActive", r."IsDeleted", r."Location", r."Name", r."Phone", r."Skills", r."Type", r."UpdatedAt", r."UserId", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", s0."Id", s0."CertificationDate", s0."CertificationExpiry", s0."CreatedAt", s0."DeletedAt", s0."IsDeleted", s0."Level", s0."Notes", s0."ResourceId", s0."SkillId", s0."UpdatedAt", s0."YearsOfExperience", s0."Id0", s0."Category", s0."CreatedAt0", s0."DeletedAt0", s0."Description", s0."IsActive", s0."IsDeleted0", s0."Name", s0."UpdatedAt0"
FROM "Resources" AS r
LEFT JOIN "AspNetUsers" AS a ON r."UserId" = a."Id"
LEFT JOIN (
    SELECT r0."Id", r0."CertificationDate", r0."CertificationExpiry", r0."CreatedAt", r0."DeletedAt", r0."IsDeleted", r0."Level", r0."Notes", r0."ResourceId", r0."SkillId", r0."UpdatedAt", r0."YearsOfExperience", s."Id" AS "Id0", s."Category", s."CreatedAt" AS "CreatedAt0", s."DeletedAt" AS "DeletedAt0", s."Description", s."IsActive", s."IsDeleted" AS "IsDeleted0", s."Name", s."UpdatedAt" AS "UpdatedAt0"
    FROM "ResourceSkills" AS r0
    INNER JOIN "Skills" AS s ON r0."SkillId" = s."Id"
) AS s0 ON r."Id" = s0."ResourceId"
WHERE NOT (r."IsDeleted")
ORDER BY r."Name", r."Id", a."Id", s0."Id"
2025-06-17 04:33:35.459 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-17 04:33:35.462 +06:00 [INF] Executed ViewResult - view Index executed in 2.8393ms.
2025-06-17 04:33:35.464 +06:00 [INF] Executed action PM.Tool.Controllers.ResourceController.Index (PM.Tool) in 21.4183ms
2025-06-17 04:33:35.465 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ResourceController.Index (PM.Tool)'
2025-06-17 04:33:35.466 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Resource - 200 null text/html; charset=utf-8 36.0747ms
2025-06-17 04:33:35.471 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-17 04:33:35.471 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-17 04:33:35.474 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 2.8055ms
2025-06-17 04:33:35.479 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 8.1435ms
2025-06-17 05:28:35.276 +06:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-17 05:28:35.929 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);
2025-06-17 05:28:35.938 +06:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-06-17 05:28:35.945 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
LOCK TABLE "__EFMigrationsHistory" IN ACCESS EXCLUSIVE MODE
2025-06-17 05:28:35.953 +06:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-17 05:28:35.961 +06:00 [INF] Applying migration '20250616232727_AddAgileWorkItemHierarchy'.
2025-06-17 05:28:36.062 +06:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
ALTER TABLE "UserStories" ADD "FeatureId" integer;
2025-06-17 05:28:36.075 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
ALTER TABLE "MeetingAttendees" ADD "InvitedAt" timestamp with time zone NOT NULL DEFAULT TIMESTAMPTZ '-infinity';
2025-06-17 05:28:36.107 +06:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Features" (
    "Id" integer GENERATED BY DEFAULT AS IDENTITY,
    "Title" character varying(200) NOT NULL,
    "Description" character varying(5000) NOT NULL,
    "EpicId" integer NOT NULL,
    "ProjectId" integer NOT NULL,
    "FeatureKey" character varying(50) NOT NULL,
    "Status" integer NOT NULL,
    "Priority" integer NOT NULL,
    "BusinessValue" character varying(2000),
    "AcceptanceCriteria" character varying(2000),
    "EstimatedStoryPoints" numeric(18,2) NOT NULL,
    "ActualStoryPoints" numeric(18,2) NOT NULL,
    "OwnerId" text,
    "TargetDate" timestamp with time zone,
    "CompletedDate" timestamp with time zone,
    "Tags" character varying(500),
    "SortOrder" integer NOT NULL,
    "CreatedAt" timestamp with time zone NOT NULL,
    "UpdatedAt" timestamp with time zone,
    "DeletedAt" timestamp with time zone,
    "IsDeleted" boolean NOT NULL,
    CONSTRAINT "PK_Features" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_Features_AspNetUsers_OwnerId" FOREIGN KEY ("OwnerId") REFERENCES "AspNetUsers" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_Features_Epics_EpicId" FOREIGN KEY ("EpicId") REFERENCES "Epics" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_Features_Projects_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES "Projects" ("Id") ON DELETE CASCADE
);
2025-06-17 05:28:36.136 +06:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "Bugs" (
    "Id" integer GENERATED BY DEFAULT AS IDENTITY,
    "Title" character varying(200) NOT NULL,
    "Description" character varying(5000) NOT NULL,
    "ProjectId" integer NOT NULL,
    "UserStoryId" integer,
    "FeatureId" integer,
    "BugKey" character varying(50) NOT NULL,
    "Severity" integer NOT NULL,
    "Priority" integer NOT NULL,
    "Status" integer NOT NULL,
    "StepsToReproduce" character varying(2000) NOT NULL,
    "ExpectedResult" character varying(1000) NOT NULL,
    "ActualResult" character varying(1000) NOT NULL,
    "FoundInVersion" character varying(100),
    "FixedInVersion" character varying(100),
    "AssignedToUserId" text,
    "ReportedByUserId" text,
    "ReproducedDate" timestamp with time zone,
    "ResolvedDate" timestamp with time zone,
    "VerifiedDate" timestamp with time zone,
    "Tags" character varying(500),
    "EstimatedHours" numeric(18,2) NOT NULL,
    "ActualHours" numeric(18,2) NOT NULL,
    "TargetDate" timestamp with time zone,
    "CreatedAt" timestamp with time zone NOT NULL,
    "UpdatedAt" timestamp with time zone,
    "DeletedAt" timestamp with time zone,
    "IsDeleted" boolean NOT NULL,
    CONSTRAINT "PK_Bugs" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_Bugs_AspNetUsers_AssignedToUserId" FOREIGN KEY ("AssignedToUserId") REFERENCES "AspNetUsers" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_Bugs_AspNetUsers_ReportedByUserId" FOREIGN KEY ("ReportedByUserId") REFERENCES "AspNetUsers" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_Bugs_Features_FeatureId" FOREIGN KEY ("FeatureId") REFERENCES "Features" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_Bugs_Projects_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES "Projects" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_Bugs_UserStories_UserStoryId" FOREIGN KEY ("UserStoryId") REFERENCES "UserStories" ("Id") ON DELETE SET NULL
);
2025-06-17 05:28:36.183 +06:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "TestCases" (
    "Id" integer GENERATED BY DEFAULT AS IDENTITY,
    "Title" character varying(200) NOT NULL,
    "Description" character varying(2000),
    "ProjectId" integer NOT NULL,
    "UserStoryId" integer,
    "FeatureId" integer,
    "TestCaseKey" character varying(50) NOT NULL,
    "Type" integer NOT NULL,
    "Priority" integer NOT NULL,
    "Status" integer NOT NULL,
    "PreConditions" character varying(1000),
    "TestSteps" character varying(5000) NOT NULL,
    "ExpectedResult" character varying(2000) NOT NULL,
    "Tags" character varying(500),
    "CreatedByUserId" text,
    "AssignedToUserId" text,
    "EstimatedExecutionTime" numeric(18,2) NOT NULL,
    "CreatedAt" timestamp with time zone NOT NULL,
    "UpdatedAt" timestamp with time zone,
    "DeletedAt" timestamp with time zone,
    "IsDeleted" boolean NOT NULL,
    CONSTRAINT "PK_TestCases" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_TestCases_AspNetUsers_AssignedToUserId" FOREIGN KEY ("AssignedToUserId") REFERENCES "AspNetUsers" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_TestCases_AspNetUsers_CreatedByUserId" FOREIGN KEY ("CreatedByUserId") REFERENCES "AspNetUsers" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_TestCases_Features_FeatureId" FOREIGN KEY ("FeatureId") REFERENCES "Features" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_TestCases_Projects_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES "Projects" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_TestCases_UserStories_UserStoryId" FOREIGN KEY ("UserStoryId") REFERENCES "UserStories" ("Id") ON DELETE SET NULL
);
2025-06-17 05:28:36.204 +06:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "BugAttachments" (
    "Id" integer GENERATED BY DEFAULT AS IDENTITY,
    "BugId" integer NOT NULL,
    "FileName" character varying(255) NOT NULL,
    "FilePath" character varying(500) NOT NULL,
    "ContentType" character varying(100) NOT NULL,
    "FileSize" bigint NOT NULL,
    "UploadedByUserId" text NOT NULL,
    "CreatedAt" timestamp with time zone NOT NULL,
    "UpdatedAt" timestamp with time zone,
    "DeletedAt" timestamp with time zone,
    "IsDeleted" boolean NOT NULL,
    CONSTRAINT "PK_BugAttachments" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_BugAttachments_AspNetUsers_UploadedByUserId" FOREIGN KEY ("UploadedByUserId") REFERENCES "AspNetUsers" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_BugAttachments_Bugs_BugId" FOREIGN KEY ("BugId") REFERENCES "Bugs" ("Id") ON DELETE CASCADE
);
2025-06-17 05:28:36.225 +06:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "BugComments" (
    "Id" integer GENERATED BY DEFAULT AS IDENTITY,
    "BugId" integer NOT NULL,
    "Content" character varying(2000) NOT NULL,
    "UserId" text NOT NULL,
    "Type" integer NOT NULL,
    "CreatedAt" timestamp with time zone NOT NULL,
    "UpdatedAt" timestamp with time zone,
    "DeletedAt" timestamp with time zone,
    "IsDeleted" boolean NOT NULL,
    CONSTRAINT "PK_BugComments" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_BugComments_AspNetUsers_UserId" FOREIGN KEY ("UserId") REFERENCES "AspNetUsers" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_BugComments_Bugs_BugId" FOREIGN KEY ("BugId") REFERENCES "Bugs" ("Id") ON DELETE CASCADE
);
2025-06-17 05:28:36.249 +06:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "TestExecutions" (
    "Id" integer GENERATED BY DEFAULT AS IDENTITY,
    "TestCaseId" integer NOT NULL,
    "Result" integer NOT NULL,
    "ActualResult" character varying(2000),
    "Notes" character varying(2000),
    "ExecutedByUserId" text NOT NULL,
    "ExecutedAt" timestamp with time zone NOT NULL,
    "ExecutionTime" numeric(18,2) NOT NULL,
    "BugId" integer,
    "CreatedAt" timestamp with time zone NOT NULL,
    "UpdatedAt" timestamp with time zone,
    "DeletedAt" timestamp with time zone,
    "IsDeleted" boolean NOT NULL,
    CONSTRAINT "PK_TestExecutions" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_TestExecutions_AspNetUsers_ExecutedByUserId" FOREIGN KEY ("ExecutedByUserId") REFERENCES "AspNetUsers" ("Id") ON DELETE RESTRICT,
    CONSTRAINT "FK_TestExecutions_Bugs_BugId" FOREIGN KEY ("BugId") REFERENCES "Bugs" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_TestExecutions_TestCases_TestCaseId" FOREIGN KEY ("TestCaseId") REFERENCES "TestCases" ("Id") ON DELETE CASCADE
);
2025-06-17 05:28:36.262 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_UserStories_FeatureId" ON "UserStories" ("FeatureId");
2025-06-17 05:28:36.273 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_BugAttachments_BugId" ON "BugAttachments" ("BugId");
2025-06-17 05:28:36.283 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_BugAttachments_UploadedByUserId" ON "BugAttachments" ("UploadedByUserId");
2025-06-17 05:28:36.295 +06:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_BugComments_BugId" ON "BugComments" ("BugId");
2025-06-17 05:28:36.308 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_BugComments_CreatedAt" ON "BugComments" ("CreatedAt");
2025-06-17 05:28:36.319 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_BugComments_UserId" ON "BugComments" ("UserId");
2025-06-17 05:28:36.334 +06:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Bugs_AssignedToUserId" ON "Bugs" ("AssignedToUserId");
2025-06-17 05:28:36.346 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Bugs_BugKey" ON "Bugs" ("BugKey");
2025-06-17 05:28:36.361 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Bugs_FeatureId" ON "Bugs" ("FeatureId");
2025-06-17 05:28:36.370 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Bugs_Priority" ON "Bugs" ("Priority");
2025-06-17 05:28:36.382 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Bugs_ProjectId" ON "Bugs" ("ProjectId");
2025-06-17 05:28:36.391 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Bugs_ReportedByUserId" ON "Bugs" ("ReportedByUserId");
2025-06-17 05:28:36.404 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Bugs_Severity" ON "Bugs" ("Severity");
2025-06-17 05:28:36.421 +06:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Bugs_Status" ON "Bugs" ("Status");
2025-06-17 05:28:36.432 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Bugs_UserStoryId" ON "Bugs" ("UserStoryId");
2025-06-17 05:28:36.443 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Features_EpicId" ON "Features" ("EpicId");
2025-06-17 05:28:36.453 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Features_FeatureKey" ON "Features" ("FeatureKey");
2025-06-17 05:28:36.466 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Features_OwnerId" ON "Features" ("OwnerId");
2025-06-17 05:28:36.480 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Features_Priority" ON "Features" ("Priority");
2025-06-17 05:28:36.492 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Features_ProjectId" ON "Features" ("ProjectId");
2025-06-17 05:28:36.509 +06:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Features_Status" ON "Features" ("Status");
2025-06-17 05:28:36.520 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TestCases_AssignedToUserId" ON "TestCases" ("AssignedToUserId");
2025-06-17 05:28:36.533 +06:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TestCases_CreatedByUserId" ON "TestCases" ("CreatedByUserId");
2025-06-17 05:28:36.548 +06:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TestCases_FeatureId" ON "TestCases" ("FeatureId");
2025-06-17 05:28:36.562 +06:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TestCases_Priority" ON "TestCases" ("Priority");
2025-06-17 05:28:36.575 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TestCases_ProjectId" ON "TestCases" ("ProjectId");
2025-06-17 05:28:36.584 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TestCases_Status" ON "TestCases" ("Status");
2025-06-17 05:28:36.595 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_TestCases_TestCaseKey" ON "TestCases" ("TestCaseKey");
2025-06-17 05:28:36.607 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TestCases_Type" ON "TestCases" ("Type");
2025-06-17 05:28:36.624 +06:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TestCases_UserStoryId" ON "TestCases" ("UserStoryId");
2025-06-17 05:28:36.637 +06:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TestExecutions_BugId" ON "TestExecutions" ("BugId");
2025-06-17 05:28:36.647 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TestExecutions_ExecutedAt" ON "TestExecutions" ("ExecutedAt");
2025-06-17 05:28:36.658 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TestExecutions_ExecutedByUserId" ON "TestExecutions" ("ExecutedByUserId");
2025-06-17 05:28:36.670 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TestExecutions_Result" ON "TestExecutions" ("Result");
2025-06-17 05:28:36.683 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_TestExecutions_TestCaseId" ON "TestExecutions" ("TestCaseId");
2025-06-17 05:28:36.691 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
ALTER TABLE "UserStories" ADD CONSTRAINT "FK_UserStories_Features_FeatureId" FOREIGN KEY ("FeatureId") REFERENCES "Features" ("Id") ON DELETE SET NULL;
2025-06-17 05:28:36.701 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250616232727_AddAgileWorkItemHierarchy', '9.0.0');
