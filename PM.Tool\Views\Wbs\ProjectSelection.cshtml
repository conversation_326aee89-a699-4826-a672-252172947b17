@model PM.Tool.Models.ViewModels.ProjectSelectionViewModel
@{
    ViewData["Title"] = "Work Breakdown Structure - Select Project";
    ViewData["Breadcrumbs"] = new List<object>
    {
        new { Text = "Dashboard", Href = Url.Action("Index", "Dashboard"), Icon = "fas fa-home" },
        new { Text = "WBS", Href = "#", Icon = "fas fa-sitemap" }
    };
}

<!-- Page Header -->
<div class="card-custom mb-6">
    <div class="card-header-custom">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-sitemap text-primary-600 dark:text-primary-400 text-xl"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100">Work Breakdown Structure</h1>
                    <p class="text-neutral-600 dark:text-dark-400 mt-1">Select a project to view its WBS structure</p>
                </div>
            </div>
            @if (Model.TotalProjects > 0)
            {
                <div class="hidden md:flex items-center space-x-6">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">@Model.TotalProjects</div>
                        <div class="text-sm text-neutral-500 dark:text-dark-400">Total Projects</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-success-600 dark:text-success-400">@Model.ActiveProjects</div>
                        <div class="text-sm text-neutral-500 dark:text-dark-400">Active</div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Search and Filter Controls -->
<div class="card-custom mb-6">
    <div class="card-body-custom">
        <form method="get" class="space-y-4">
            <!-- Search and Filter Row -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Search Input -->
                <div class="lg:col-span-2">
                    <label for="searchTerm" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">
                        <i class="fas fa-search mr-2"></i>Search Projects
                    </label>
                    <input type="text"
                           id="searchTerm"
                           name="searchTerm"
                           value="@Model.SearchTerm"
                           placeholder="Search by name, description, or client..."
                           class="w-full px-4 py-2 border border-neutral-300 dark:border-dark-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-700 dark:text-white">
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="statusFilter" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">
                        <i class="fas fa-filter mr-2"></i>Status
                    </label>
                    <select id="statusFilter"
                            name="statusFilter"
                            class="select2-enabled w-full"
                            data-select2="true"
                            data-select2-options='{"placeholder": "All Statuses", "allowClear": true}'>
                        <option value="">All Statuses</option>
                        @foreach (var status in Model.AvailableStatuses)
                        {
                            <option value="@status" selected="@(Model.StatusFilter == status)">@status</option>
                        }
                    </select>
                </div>

                <!-- Sort Options -->
                <div>
                    <label for="sortBy" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">
                        <i class="fas fa-sort mr-2"></i>Sort By
                    </label>
                    <div class="flex space-x-2">
                        <select id="sortBy"
                                name="sortBy"
                                class="select2-enabled flex-1"
                                data-select2="true"
                                data-select2-options='{"minimumResultsForSearch": -1}'>
                            @foreach (var option in Model.SortOptions)
                            {
                                <option value="@option.Value" selected="@(Model.SortBy == option.Value)">@option.Text</option>
                            }
                        </select>
                        <select name="sortOrder"
                                class="select2-enabled"
                                data-select2="true"
                                data-select2-options='{"minimumResultsForSearch": -1}'>
                            <option value="asc" selected="@(Model.SortOrder == "asc")">↑ Ascending</option>
                            <option value="desc" selected="@(Model.SortOrder == "desc")">↓ Descending</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-between pt-4 border-t border-neutral-200 dark:border-dark-200">
                <div class="flex items-center space-x-4">
                    <button type="submit" class="btn-primary-custom">
                        <i class="fas fa-search mr-2"></i>Search
                    </button>
                    <a href="@Url.Action("Index")" class="btn-secondary-custom">
                        <i class="fas fa-times mr-2"></i>Clear
                    </a>
                </div>

                <!-- Page Size Selector -->
                <div class="flex items-center space-x-2">
                    <label for="pageSize" class="text-sm text-neutral-600 dark:text-dark-400">Show:</label>
                    <select id="pageSize"
                            name="pageSize"
                            onchange="this.form.submit()"
                            class="select2-enabled"
                            data-select2="true"
                            data-select2-options='{"minimumResultsForSearch": -1}'>
                        @foreach (var option in Model.PageSizeOptions)
                        {
                            <option value="@option.Value" selected="@(Model.PageSize == option.Value)">@option.Text</option>
                        }
                    </select>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Project Selection Content -->
<div class="card-custom">
    <div class="card-body-custom">
        @if (Model.Projects.Items.Any())
        {
            <!-- Projects Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                @foreach (var project in Model.Projects.Items)
                {
                    <!-- Project Card -->
                    <div class="project-selection-card group">
                        <!-- Card Header -->
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                                <i class="fas fa-project-diagram text-primary-600 dark:text-primary-400 text-xl"></i>
                            </div>
                            @{
                                var statusClass = project.Status.ToString().ToLower() switch
                                {
                                    "active" => "status-pill status-active",
                                    "completed" => "status-pill status-completed",
                                    "planning" => "status-pill status-planning",
                                    "onhold" => "status-pill status-on-hold",
                                    _ => "status-pill status-inactive"
                                };
                            }
                            <span class="@statusClass">
                                @project.Status
                            </span>
                        </div>

                        <!-- Project Title -->
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-3 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                            @project.Name
                        </h3>

                        <!-- Project Description -->
                        <div class="mb-4">
                            <p class="text-neutral-600 dark:text-dark-400 text-sm leading-relaxed line-clamp-3">
                                @if (!string.IsNullOrEmpty(project.Description))
                                {
                                    @project.Description
                                }
                                else
                                {
                                    <em class="text-neutral-500 dark:text-dark-500">No description available</em>
                                }
                            </p>
                        </div>

                        <!-- Project Meta -->
                        <div class="space-y-2 mb-6">
                            <div class="flex items-center text-xs text-neutral-500 dark:text-dark-400">
                                <i class="fas fa-calendar-alt w-4 mr-2"></i>
                                <span>Created @project.CreatedAt.ToString("MMM dd, yyyy")</span>
                            </div>
                            <div class="flex items-center text-xs text-neutral-500 dark:text-dark-400">
                                <i class="fas fa-clock w-4 mr-2"></i>
                                <span>@((DateTime.Now - project.CreatedAt).Days) days ago</span>
                            </div>
                        </div>

                        <!-- Action Button -->
                        <div class="mt-auto">
                            <a href="@Url.Action("ProjectWbs", new { projectId = project.Id })"
                               class="btn-primary-custom w-full text-center inline-flex items-center justify-center"
                               aria-label="View WBS for @project.Name">
                                <i class="fas fa-sitemap mr-2"></i>
                                View WBS
                            </a>
                        </div>
                    </div>
                }
            </div>

            <!-- Pagination Controls -->
            @if (Model.Projects.TotalPages > 1)
            {
                <div class="mt-8 flex items-center justify-between">
                    <!-- Results Info -->
                    <div class="text-sm text-neutral-600 dark:text-dark-400">
                        Showing @((Model.Projects.PageIndex - 1) * Model.PageSize + 1) to @(Math.Min(Model.Projects.PageIndex * Model.PageSize, Model.Projects.TotalCount)) of @Model.Projects.TotalCount projects
                    </div>

                    <!-- Pagination Links -->
                    <nav class="flex items-center space-x-2" aria-label="Pagination">
                        <!-- Previous Page -->
                        @if (Model.Projects.HasPreviousPage)
                        {
                            <a href="@Url.Action("Index", new {
                                searchTerm = Model.SearchTerm,
                                statusFilter = Model.StatusFilter,
                                sortBy = Model.SortBy,
                                sortOrder = Model.SortOrder,
                                pageSize = Model.PageSize,
                                page = Model.Projects.PageIndex - 1
                            })"
                               class="px-3 py-2 text-sm font-medium text-neutral-500 bg-white border border-neutral-300 rounded-lg hover:bg-neutral-50 hover:text-neutral-700 dark:bg-dark-700 dark:border-dark-300 dark:text-dark-400 dark:hover:bg-dark-600 dark:hover:text-dark-200">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        }
                        else
                        {
                            <span class="px-3 py-2 text-sm font-medium text-neutral-300 bg-neutral-100 border border-neutral-200 rounded-lg dark:bg-dark-600 dark:border-dark-500 dark:text-dark-500">
                                <i class="fas fa-chevron-left"></i>
                            </span>
                        }

                        <!-- Page Numbers -->
                        @{
                            var startPage = Math.Max(1, Model.Projects.PageIndex - 2);
                            var endPage = Math.Min(Model.Projects.TotalPages, Model.Projects.PageIndex + 2);
                        }

                        @if (startPage > 1)
                        {
                            <a href="@Url.Action("Index", new {
                                searchTerm = Model.SearchTerm,
                                statusFilter = Model.StatusFilter,
                                sortBy = Model.SortBy,
                                sortOrder = Model.SortOrder,
                                pageSize = Model.PageSize,
                                page = 1
                            })"
                               class="px-3 py-2 text-sm font-medium text-neutral-500 bg-white border border-neutral-300 rounded-lg hover:bg-neutral-50 hover:text-neutral-700 dark:bg-dark-700 dark:border-dark-300 dark:text-dark-400 dark:hover:bg-dark-600 dark:hover:text-dark-200">
                                1
                            </a>
                            @if (startPage > 2)
                            {
                                <span class="px-3 py-2 text-sm font-medium text-neutral-400 dark:text-dark-500">...</span>
                            }
                        }

                        @for (int i = startPage; i <= endPage; i++)
                        {
                            @if (i == Model.Projects.PageIndex)
                            {
                                <span class="px-3 py-2 text-sm font-medium text-white bg-primary-600 border border-primary-600 rounded-lg">
                                    @i
                                </span>
                            }
                            else
                            {
                                <a href="@Url.Action("Index", new {
                                    searchTerm = Model.SearchTerm,
                                    statusFilter = Model.StatusFilter,
                                    sortBy = Model.SortBy,
                                    sortOrder = Model.SortOrder,
                                    pageSize = Model.PageSize,
                                    page = i
                                })"
                                   class="px-3 py-2 text-sm font-medium text-neutral-500 bg-white border border-neutral-300 rounded-lg hover:bg-neutral-50 hover:text-neutral-700 dark:bg-dark-700 dark:border-dark-300 dark:text-dark-400 dark:hover:bg-dark-600 dark:hover:text-dark-200">
                                    @i
                                </a>
                            }
                        }

                        @if (endPage < Model.Projects.TotalPages)
                        {
                            @if (endPage < Model.Projects.TotalPages - 1)
                            {
                                <span class="px-3 py-2 text-sm font-medium text-neutral-400 dark:text-dark-500">...</span>
                            }
                            <a href="@Url.Action("Index", new {
                                searchTerm = Model.SearchTerm,
                                statusFilter = Model.StatusFilter,
                                sortBy = Model.SortBy,
                                sortOrder = Model.SortOrder,
                                pageSize = Model.PageSize,
                                page = Model.Projects.TotalPages
                            })"
                               class="px-3 py-2 text-sm font-medium text-neutral-500 bg-white border border-neutral-300 rounded-lg hover:bg-neutral-50 hover:text-neutral-700 dark:bg-dark-700 dark:border-dark-300 dark:text-dark-400 dark:hover:bg-dark-600 dark:hover:text-dark-200">
                                @Model.Projects.TotalPages
                            </a>
                        }

                        <!-- Next Page -->
                        @if (Model.Projects.HasNextPage)
                        {
                            <a href="@Url.Action("Index", new {
                                searchTerm = Model.SearchTerm,
                                statusFilter = Model.StatusFilter,
                                sortBy = Model.SortBy,
                                sortOrder = Model.SortOrder,
                                pageSize = Model.PageSize,
                                page = Model.Projects.PageIndex + 1
                            })"
                               class="px-3 py-2 text-sm font-medium text-neutral-500 bg-white border border-neutral-300 rounded-lg hover:bg-neutral-50 hover:text-neutral-700 dark:bg-dark-700 dark:border-dark-300 dark:text-dark-400 dark:hover:bg-dark-600 dark:hover:text-dark-200">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        }
                        else
                        {
                            <span class="px-3 py-2 text-sm font-medium text-neutral-300 bg-neutral-100 border border-neutral-200 rounded-lg dark:bg-dark-600 dark:border-dark-500 dark:text-dark-500">
                                <i class="fas fa-chevron-right"></i>
                            </span>
                        }
                    </nav>
                </div>
            }
        }
        else
        {
            <!-- Empty State -->
            <div class="text-center py-16">
                <div class="mx-auto mb-8 w-32 h-32">
                    <div class="relative">
                        <div class="w-32 h-32 mx-auto bg-gradient-to-br from-neutral-100 to-neutral-200 dark:from-dark-700 dark:to-dark-600 rounded-full flex items-center justify-center shadow-lg">
                            <i class="fas fa-project-diagram text-5xl text-neutral-400 dark:text-dark-400"></i>
                        </div>
                        <div class="absolute -top-2 -right-2 w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center shadow-md">
                            <i class="fas fa-plus text-primary-600 dark:text-primary-400"></i>
                        </div>
                    </div>
                </div>
                <h3 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 mb-4">No Projects Available</h3>
                <p class="text-neutral-600 dark:text-dark-400 mb-8 max-w-md mx-auto">
                    You don't have access to any projects yet. Create your first project to get started with Work Breakdown Structure management.
                </p>
                @{
                    ViewData["Text"] = "Create Your First Project";
                    ViewData["Variant"] = "primary";
                    ViewData["Icon"] = "fas fa-plus";
                    ViewData["Size"] = "lg";
                    ViewData["Href"] = Url.Action("Index", "Projects");
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
        }
    </div>
</div>

<!-- Include WBS Styles -->
<partial name="_WbsStyles" />

@section Scripts {
    <script>
        $(document).ready(function() {
            // Initialize Select2 for filter dropdowns
            setTimeout(function() {
                if (window.Select2Manager) {
                    window.Select2Manager.init();
                }
            }, 100);

            // Auto-submit form when filters change
            $('#statusFilter, #sortBy, select[name="sortOrder"]').on('select2:select select2:unselect change', function() {
                $(this).closest('form').submit();
            });

            // Debounced search
            let searchTimeout;
            $('#searchTerm').on('input', function() {
                clearTimeout(searchTimeout);
                const query = $(this).val();

                // Auto-submit after 500ms of no typing
                searchTimeout = setTimeout(() => {
                    $(this).closest('form').submit();
                }, 500);
            });

            // Clear search functionality
            $('.btn-secondary-custom').click(function(e) {
                e.preventDefault();
                $('#searchTerm').val('');
                $('#statusFilter').val('');
                $('#sortBy').val('Name');
                $('select[name="sortOrder"]').val('asc');
                $(this).closest('form').submit();
            });

            // Keyboard shortcuts
            $(document).keydown(function(e) {
                // Ctrl/Cmd + K to focus search
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    $('#searchTerm').focus();
                }

                // Escape to clear search
                if (e.key === 'Escape' && $('#searchTerm').is(':focus')) {
                    $('#searchTerm').val('').trigger('input');
                }
            });

            // Add loading state to search button
            $('form').submit(function() {
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.html('<i class="fas fa-spinner fa-spin mr-2"></i>Searching...');
                submitBtn.prop('disabled', true);

                // Re-enable after a short delay (in case of quick response)
                setTimeout(() => {
                    submitBtn.html(originalText);
                    submitBtn.prop('disabled', false);
                }, 1000);
            });

            // Animate project cards on load
            $('.project-selection-card').each(function(index) {
                $(this).css('animation-delay', (index * 0.1) + 's');
            });

            // Add hover effects to pagination links
            $('nav[aria-label="Pagination"] a').hover(
                function() {
                    $(this).addClass('transform scale-105 transition-transform duration-200');
                },
                function() {
                    $(this).removeClass('transform scale-105 transition-transform duration-200');
                }
            );
        });
    </script>
}
