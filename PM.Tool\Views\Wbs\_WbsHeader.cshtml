@model dynamic

<!-- <PERSON> Header -->
<div class="bg-white dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700 mb-6">
    <div class="px-6 py-4">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div class="mb-4 sm:mb-0">
                <h1 class="text-2xl font-bold text-neutral-900 dark:text-white flex items-center">
                    <i class="fas fa-sitemap text-primary-600 mr-3"></i>
                    Work Breakdown Structure
                </h1>
                <p class="mt-1 text-sm text-neutral-600 dark:text-neutral-400">
                    @ViewBag.ProjectName
                </p>
            </div>
            
            <!-- Action Buttons -->
            <div class="flex flex-wrap gap-2">
                <button type="button" 
                        onclick="showCreateTaskModal()" 
                        class="btn-primary-custom">
                    <i class="fas fa-plus mr-2"></i>
                    Add Task
                </button>
                
                <button type="button" 
                        onclick="generateWbsCodes()" 
                        class="btn-secondary-custom">
                    <i class="fas fa-code mr-2"></i>
                    Generate Codes
                </button>
                
                <!-- View Options Dropdown -->
                <div class="relative">
                    <button type="button" 
                            id="viewDropdownBtn"
                            onclick="toggleDropdown('viewDropdown')"
                            class="btn-secondary-custom">
                        <i class="fas fa-eye mr-2"></i>
                        View
                        <i class="fas fa-chevron-down ml-2 text-xs"></i>
                    </button>
                    
                    <div id="viewDropdown" class="dropdown-menu hidden">
                        <a href="#" onclick="expandAll(); hideDropdown('viewDropdown')" class="dropdown-item">
                            <i class="fas fa-expand-arrows-alt mr-3"></i>Expand All
                        </a>
                        <a href="#" onclick="collapseAll(); hideDropdown('viewDropdown')" class="dropdown-item">
                            <i class="fas fa-compress-arrows-alt mr-3"></i>Collapse All
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="#" onclick="toggleCompactView(); hideDropdown('viewDropdown')" class="dropdown-item">
                            <i class="fas fa-compress mr-3"></i>Compact View
                        </a>
                    </div>
                </div>
                
                <!-- Export Options Dropdown -->
                <div class="relative">
                    <button type="button" 
                            id="exportDropdownBtn"
                            onclick="toggleDropdown('exportDropdown')"
                            class="btn-secondary-custom">
                        <i class="fas fa-download mr-2"></i>
                        Export
                        <i class="fas fa-chevron-down ml-2 text-xs"></i>
                    </button>
                    
                    <div id="exportDropdown" class="dropdown-menu hidden">
                        <a href="#" onclick="exportWbs('excel'); hideDropdown('exportDropdown')" class="dropdown-item">
                            <i class="fas fa-file-excel mr-3 text-green-600"></i>Export to Excel
                        </a>
                        <a href="#" onclick="exportWbs('pdf'); hideDropdown('exportDropdown')" class="dropdown-item">
                            <i class="fas fa-file-pdf mr-3 text-red-600"></i>Export to PDF
                        </a>
                        <a href="#" onclick="exportWbs('csv'); hideDropdown('exportDropdown')" class="dropdown-item">
                            <i class="fas fa-file-csv mr-3 text-blue-600"></i>Export to CSV
                        </a>
                    </div>
                </div>
                
                <a href="@Url.Action("Details", "Projects", new { id = ViewBag.ProjectId })" 
                   class="btn-secondary-custom">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Project
                </a>
            </div>
        </div>
    </div>
</div>
