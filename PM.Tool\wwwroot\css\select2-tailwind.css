/* Modern Select2 Theme - Integrated with PM Tool Design System */

/* Single Selection Styling */
.select2-container--tailwind .select2-selection--single {
    height: 42px !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 0.5rem !important;
    background-color: #ffffff !important;
    color: #111827 !important;
    font-size: 0.875rem !important;
    font-weight: 400 !important;
    line-height: 1.5 !important;
    padding: 0 0.75rem !important;
    transition: all 0.2s ease-in-out !important;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
    display: flex !important;
    align-items: center !important;
}

/* Dark Mode Single Selection */
.dark .select2-container--tailwind .select2-selection--single {
    background-color: #1f2937 !important;
    border-color: #374151 !important;
    color: #f9fafb !important;
}

/* Focus States */
.select2-container--tailwind .select2-selection--single:focus,
.select2-container--tailwind.select2-container--focus .select2-selection--single {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    outline: none !important;
}

/* Dark Mode Focus */
.dark .select2-container--tailwind.select2-container--focus .select2-selection--single {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2) !important;
}

/* Rendered Text */
.select2-container--tailwind .select2-selection__rendered {
    color: #111827 !important;
    padding: 0 !important;
    line-height: 1.5 !important;
    font-weight: 400 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
}

.dark .select2-container--tailwind .select2-selection__rendered {
    color: #f9fafb !important;
}

/* Placeholder */
.select2-container--tailwind .select2-selection__placeholder {
    color: #9ca3af !important;
    font-style: normal !important;
}

.dark .select2-container--tailwind .select2-selection__placeholder {
    color: #6b7280 !important;
}

/* Arrow Styling */
.select2-container--tailwind .select2-selection__arrow {
    height: 40px !important;
    right: 8px !important;
    width: 20px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    top: 1px !important;
}

.select2-container--tailwind .select2-selection__arrow b {
    border-color: #6b7280 transparent transparent transparent !important;
    border-style: solid !important;
    border-width: 5px 4px 0 4px !important;
    height: 0 !important;
    width: 0 !important;
    margin: 0 !important;
    position: static !important;
    transition: all 0.2s ease-in-out !important;
}

.dark .select2-container--tailwind .select2-selection__arrow b {
    border-color: #9ca3af transparent transparent transparent !important;
}

/* Arrow on focus/open */
.select2-container--tailwind.select2-container--open .select2-selection__arrow b {
    border-color: transparent transparent #6b7280 transparent !important;
    border-width: 0 4px 5px 4px !important;
}

.dark .select2-container--tailwind.select2-container--open .select2-selection__arrow b {
    border-color: transparent transparent #9ca3af transparent !important;
}

/* Dropdown Styling */
.select2-container--tailwind .select2-dropdown {
    border: 1px solid #e5e7eb !important;
    border-radius: 0.5rem !important;
    background-color: #ffffff !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    margin-top: 4px !important;
    overflow: hidden !important;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
    z-index: 9999 !important;
}

.dark .select2-container--tailwind .select2-dropdown {
    background-color: #1f2937 !important;
    border-color: #374151 !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.8), 0 4px 6px -2px rgba(0, 0, 0, 0.7) !important;
}

/* Dropdown Options */
.select2-container--tailwind .select2-results__option {
    padding: 0.75rem 1rem !important;
    color: #111827 !important;
    font-size: 0.875rem !important;
    line-height: 1.5 !important;
    cursor: pointer !important;
    transition: all 0.15s ease-in-out !important;
    border-bottom: 1px solid #f3f4f6 !important;
}

.dark .select2-container--tailwind .select2-results__option {
    color: #f9fafb !important;
    border-bottom-color: #374151 !important;
}

.select2-container--tailwind .select2-results__option:last-child {
    border-bottom: none !important;
}

/* Highlighted Option (Hover) */
.select2-container--tailwind .select2-results__option--highlighted {
    background-color: #3b82f6 !important;
    color: white !important;
}

/* Selected Option */
.select2-container--tailwind .select2-results__option--selected {
    background-color: #dbeafe !important;
    color: #1e40af !important;
    font-weight: 500 !important;
}

/* Dark mode selected option */
.dark .select2-container--tailwind .select2-results__option--selected {
    background-color: rgba(59, 130, 246, 0.2) !important;
    color: #93c5fd !important;
}

/* Search in Dropdown */
.select2-container--tailwind .select2-search--dropdown {
    padding: 0.75rem !important;
    border-bottom: 1px solid #f3f4f6 !important;
    background-color: #f9fafb !important;
}

.dark .select2-container--tailwind .select2-search--dropdown {
    background-color: #374151 !important;
    border-bottom-color: #4b5563 !important;
}

.select2-container--tailwind .select2-search__field {
    border: 1px solid #e5e7eb !important;
    border-radius: 0.375rem !important;
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
    width: 100% !important;
    box-sizing: border-box !important;
    background-color: #ffffff !important;
    color: #111827 !important;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
}

.dark .select2-container--tailwind .select2-search__field {
    background-color: #1f2937 !important;
    border-color: #374151 !important;
    color: #f9fafb !important;
}

.select2-container--tailwind .select2-search__field:focus {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
    outline: none !important;
}

.select2-container--tailwind .select2-search__field::placeholder {
    color: #9ca3af !important;
}

.dark .select2-container--tailwind .select2-search__field::placeholder {
    color: #6b7280 !important;
}

/* Multiple Selection Styling */
.select2-container--tailwind .select2-selection--multiple {
    min-height: 42px !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 0.5rem !important;
    background-color: #ffffff !important;
    padding: 0.25rem !important;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

.dark .select2-container--tailwind .select2-selection--multiple {
    background-color: #1f2937 !important;
    border-color: #374151 !important;
}

.select2-container--tailwind.select2-container--focus .select2-selection--multiple {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* Multiple Selection Tags */
.select2-container--tailwind .select2-selection__choice {
    background-color: #3b82f6 !important;
    border: none !important;
    border-radius: 0.375rem !important;
    color: white !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    padding: 0.25rem 0.5rem !important;
    margin: 0.125rem !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 0.25rem !important;
}

.select2-container--tailwind .select2-selection__choice__remove {
    color: white !important;
    font-weight: bold !important;
    margin-right: 0 !important;
    margin-left: 0.25rem !important;
    cursor: pointer !important;
    transition: color 0.15s ease-in-out !important;
    font-size: 0.875rem !important;
}

.select2-container--tailwind .select2-selection__choice__remove:hover {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* Loading State */
.select2-container--tailwind .select2-results__option--loading {
    color: #9ca3af !important;
    font-style: italic !important;
    padding: 1rem !important;
    text-align: center !important;
}

.dark .select2-container--tailwind .select2-results__option--loading {
    color: #6b7280 !important;
}

/* No Results Message */
.select2-container--tailwind .select2-results__message {
    color: #9ca3af !important;
    font-style: italic !important;
    padding: 1rem !important;
    text-align: center !important;
    background-color: #f9fafb !important;
}

.dark .select2-container--tailwind .select2-results__message {
    color: #6b7280 !important;
    background-color: #374151 !important;
}

/* Disabled State */
.select2-container--tailwind.select2-container--disabled .select2-selection--single {
    background-color: #f9fafb !important;
    color: #9ca3af !important;
    cursor: not-allowed !important;
    opacity: 0.6 !important;
}

.dark .select2-container--tailwind.select2-container--disabled .select2-selection--single {
    background-color: #374151 !important;
    color: #6b7280 !important;
}

.select2-container--tailwind.select2-container--disabled .select2-selection__arrow b {
    border-color: #9ca3af transparent transparent transparent !important;
}

/* Error State */
.select2-container--tailwind.has-error .select2-selection--single {
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

.dark .select2-container--tailwind.has-error .select2-selection--single {
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2) !important;
}

/* Success State */
.select2-container--tailwind.has-success .select2-selection--single {
    border-color: #22c55e !important;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1) !important;
}

.dark .select2-container--tailwind.has-success .select2-selection--single {
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2) !important;
}

/* Layout and Positioning */
.select2-container--tailwind {
    width: 100% !important;
    display: block !important;
}

/* Icon Integration */
.select2-container--tailwind.has-icon-left .select2-selection__rendered {
    padding-left: 2.5rem !important;
}

.select2-container--tailwind.has-icon-right .select2-selection__rendered {
    padding-right: 3rem !important;
}

/* Ensure icons are visible above Select2 */
.relative .select2-container--tailwind {
    position: relative !important;
    z-index: 1 !important;
}

.relative .select2-container--tailwind + .absolute {
    z-index: 2 !important;
}

/* Enhanced Dropdown Animation */
.select2-container--tailwind .select2-dropdown {
    animation: select2-dropdown-fade-in 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes select2-dropdown-fade-in {
    from {
        opacity: 0;
        transform: translateY(-8px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Hover Effects */
.select2-container--tailwind .select2-selection--single:hover:not(.select2-container--disabled .select2-selection--single) {
    border-color: #d1d5db !important;
}

.dark .select2-container--tailwind .select2-selection--single:hover:not(.select2-container--disabled .select2-selection--single) {
    border-color: #4b5563 !important;
}

/* Size Variants */
.select2-container--tailwind.select2-sm .select2-selection--single {
    height: 36px !important;
    padding: 0 0.5rem !important;
    font-size: 0.8rem !important;
}

.select2-container--tailwind.select2-lg .select2-selection--single {
    height: 48px !important;
    padding: 0 1rem !important;
    font-size: 1rem !important;
}

/* Responsive Adjustments */
@media (max-width: 640px) {
    .select2-container--tailwind .select2-selection--single {
        height: 44px !important;
        font-size: 1rem !important; /* Prevent zoom on iOS */
    }

    .select2-container--tailwind .select2-dropdown {
        margin-top: 2px !important;
    }

    .select2-container--tailwind .select2-results__option {
        padding: 1rem !important;
        font-size: 1rem !important;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .select2-container--tailwind .select2-selection--single {
        border-width: 2px !important;
    }

    .select2-container--tailwind.select2-container--focus .select2-selection--single {
        border-width: 3px !important;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .select2-container--tailwind .select2-dropdown {
        animation: none !important;
    }

    .select2-container--tailwind .select2-selection--single,
    .select2-container--tailwind .select2-results__option {
        transition: none !important;
    }
}

/* Additional Modern Styling */
.select2-container--tailwind .select2-selection--single:not(.select2-container--disabled .select2-selection--single) {
    cursor: pointer !important;
}

.select2-container--tailwind .select2-selection--multiple:not(.select2-container--disabled .select2-selection--multiple) {
    cursor: text !important;
}

/* Improved spacing for multiple selection */
.select2-container--tailwind .select2-selection--multiple .select2-selection__rendered {
    padding: 0.25rem !important;
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 0.25rem !important;
}

/* Search input in multiple selection */
.select2-container--tailwind .select2-search--inline .select2-search__field {
    border: none !important;
    background: transparent !important;
    padding: 0.25rem 0.5rem !important;
    margin: 0 !important;
    min-width: 120px !important;
    font-size: 0.875rem !important;
}

.select2-container--tailwind .select2-search--inline .select2-search__field:focus {
    box-shadow: none !important;
    outline: none !important;
}
