using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Enums;
using PM.Tool.Services;
using PM.Tool.Models.ViewModels;

namespace PM.Tool.Controllers
{
    public class RequirementController : SecureBaseController
    {
        private readonly IRequirementService _requirementService;
        private readonly IProjectService _projectService;
        private readonly IFormHelperService _formHelper;
        private readonly IStakeholderService _stakeholderService;
        private readonly ILogger<RequirementController> _logger;

        public RequirementController(
            IRequirementService requirementService,
            IProjectService projectService,
            IFormHelperService formHelper,
            IStakeholderService stakeholderService,
            IAuditService auditService,
            ILogger<RequirementController> logger) : base(auditService)
        {
            _requirementService = requirementService;
            _projectService = projectService;
            _formHelper = formHelper;
            _stakeholderService = stakeholderService;
            _logger = logger;
        }

        // GET: Requirement
        public async Task<IActionResult> Index(int? projectId)
        {
            try
            {
                IEnumerable<Requirement> requirements;

                if (projectId.HasValue)
                {
                    requirements = await _requirementService.GetProjectRequirementsAsync(projectId.Value);
                    var project = await _projectService.GetProjectByIdAsync(projectId.Value);
                    ViewBag.ProjectName = project?.Name;
                    ViewBag.ProjectId = projectId.Value;
                }
                else
                {
                    requirements = await _requirementService.GetAllRequirementsAsync();
                }

                return View(requirements);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading requirements.";
                return View(new List<Requirement>());
            }
        }

        // GET: Requirement/Hierarchy/5
        public async Task<IActionResult> Hierarchy(int projectId)
        {
            try
            {
                var requirements = await _requirementService.GetRequirementHierarchyAsync(projectId);
                var project = await _projectService.GetProjectByIdAsync(projectId);

                ViewBag.ProjectName = project?.Name;
                ViewBag.ProjectId = projectId;

                return View(requirements);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading requirements hierarchy.";
                return RedirectToAction("Index", new { projectId });
            }
        }

        // GET: Requirement/Details/5
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var requirement = await _requirementService.GetRequirementByIdAsync(id);
                if (requirement == null) return NotFound();

                var comments = await _requirementService.GetRequirementCommentsAsync(id);
                var attachments = await _requirementService.GetRequirementAttachmentsAsync(id);
                var changes = await _requirementService.GetRequirementChangesAsync(id);
                var tasks = await _requirementService.GetRequirementTasksAsync(id);

                ViewBag.Comments = comments;
                ViewBag.Attachments = attachments;
                ViewBag.Changes = changes;
                ViewBag.Tasks = tasks;

                return View(requirement);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading requirement details.";
                return RedirectToAction("Index");
            }
        }

        // GET: Requirement/Create
        public async Task<IActionResult> Create(int? projectId, int? parentId)
        {
            try
            {
                var viewModel = new RequirementCreateViewModel();

                if (projectId.HasValue)
                {
                    viewModel.ProjectId = projectId.Value;
                    var project = await _projectService.GetProjectByIdAsync(projectId.Value);
                    ViewBag.ProjectName = project?.Name;
                }

                if (parentId.HasValue)
                {
                    viewModel.ParentRequirementId = parentId.Value;
                    var parent = await _requirementService.GetRequirementByIdAsync(parentId.Value);
                    ViewBag.ParentRequirement = parent;
                }

                await PopulateDropdowns();
                return View(viewModel);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading requirement creation form.";
                return RedirectToAction("Index");
            }
        }

        // POST: Requirement/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(RequirementCreateViewModel viewModel)
        {
            return await this.HandleCreateAsync<RequirementCreateViewModel, Requirement>(
                viewModel,
                async (requirement) => {
                    // Note: CreatedBy info is handled by BaseEntity
                    var createdRequirement = await _requirementService.CreateRequirementAsync(requirement);
                    await LogAuditAsync(AuditAction.Create, "Requirement", createdRequirement.Id);
                    return createdRequirement;
                },
                requirement => requirement.Id,
                PopulateDropdowns,
                _formHelper,
                _logger,
                "Requirement"
            );
        }

        // GET: Requirement/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                var requirement = await _requirementService.GetRequirementByIdAsync(id);
                if (requirement == null) return NotFound();

                await PopulateDropdowns();
                return View(requirement);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading requirement for editing.";
                return RedirectToAction("Index");
            }
        }

        // POST: Requirement/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, Requirement requirement)
        {
            if (id != requirement.Id) return NotFound();

            try
            {
                if (ModelState.IsValid)
                {
                    await _requirementService.UpdateRequirementAsync(requirement);
                    await LogAuditAsync(AuditAction.Update, "Requirement", id);

                    TempData["Success"] = "Requirement updated successfully.";
                    return RedirectToAction("Details", new { id });
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error updating requirement.";
            }

            await PopulateDropdowns();
            return View(requirement);
        }

        // POST: Requirement/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var success = await _requirementService.DeleteRequirementAsync(id);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Delete, "Requirement", id);
                    TempData["Success"] = "Requirement deleted successfully.";
                }
                else
                {
                    TempData["Error"] = "Failed to delete requirement.";
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error deleting requirement.";
            }

            return RedirectToAction("Index");
        }

        // POST: Requirement/UpdateStatus
        [HttpPost]
        public async Task<IActionResult> UpdateStatus(int requirementId, RequirementStatus status)
        {
            try
            {
                var success = await _requirementService.UpdateRequirementStatusAsync(requirementId, status);

                if (success)
                {
                    await LogAuditAsync(AuditAction.ChangeStatus, "Requirement", requirementId);
                    return Json(new { success = true });
                }

                return Json(new { success = false, message = "Failed to update requirement status." });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Error updating requirement status." });
            }
        }

        // POST: Requirement/Approve/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Approve(int id, string? comments)
        {
            try
            {
                var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "";
                var success = await _requirementService.ApproveRequirementAsync(id, userId, comments);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Approve, "Requirement", id);
                    TempData["Success"] = "Requirement approved successfully.";
                }
                else
                {
                    TempData["Error"] = "Failed to approve requirement.";
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error approving requirement.";
            }

            return RedirectToAction("Details", new { id });
        }

        // POST: Requirement/Reject/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Reject(int id, string reason)
        {
            try
            {
                var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "";
                var success = await _requirementService.RejectRequirementAsync(id, userId, reason);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Reject, "Requirement", id);
                    TempData["Success"] = "Requirement rejected.";
                }
                else
                {
                    TempData["Error"] = "Failed to reject requirement.";
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error rejecting requirement.";
            }

            return RedirectToAction("Details", new { id });
        }

        // GET: Requirement/Analytics/5
        public async Task<IActionResult> Analytics(int projectId)
        {
            try
            {
                var typeDistribution = await _requirementService.GetRequirementTypeDistributionAsync(projectId);
                var statusDistribution = await _requirementService.GetRequirementStatusDistributionAsync(projectId);
                var priorityDistribution = await _requirementService.GetRequirementPriorityDistributionAsync(projectId);
                var completionPercentage = await _requirementService.GetRequirementCompletionPercentageAsync(projectId);
                var totalEffort = await _requirementService.GetTotalEstimatedEffortAsync(projectId);

                var project = await _projectService.GetProjectByIdAsync(projectId);

                ViewBag.ProjectName = project?.Name;
                ViewBag.ProjectId = projectId;
                ViewBag.TypeDistribution = typeDistribution;
                ViewBag.StatusDistribution = statusDistribution;
                ViewBag.PriorityDistribution = priorityDistribution;
                ViewBag.CompletionPercentage = completionPercentage;
                ViewBag.TotalEffort = totalEffort;

                return View();
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading requirement analytics.";
                return RedirectToAction("Index", new { projectId });
            }
        }

        // GET: Requirement/PendingApproval
        public async Task<IActionResult> PendingApproval()
        {
            try
            {
                var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "";
                var pendingRequirements = await _requirementService.GetRequirementsPendingApprovalAsync(userId);

                return View(pendingRequirements);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading pending approvals.";
                return View(new List<Requirement>());
            }
        }

        // API: Get Stakeholders for dropdowns
        [HttpGet]
        public async Task<IActionResult> GetStakeholders()
        {
            try
            {
                var stakeholders = await _stakeholderService.GetActiveStakeholdersAsync();
                var result = stakeholders.Select(s => new {
                    id = s.Id,
                    name = s.Name,
                    email = s.Email,
                    organization = s.Organization,
                    fullContact = s.FullContact,
                    type = "stakeholder"
                });

                return Json(result);
            }
            catch (Exception ex)
            {
                return Json(new List<object>());
            }
        }

        // API: Get Project Requirements for dropdowns
        [HttpGet]
        public async Task<IActionResult> GetProjectRequirements(int projectId)
        {
            try
            {
                var requirements = await _requirementService.GetProjectRequirementsAsync(projectId);
                var result = requirements.Select(r => new {
                    id = r.Id,
                    requirementId = r.RequirementId,
                    title = r.Title,
                    description = r.Description,
                    status = r.Status.ToString(),
                    priority = r.Priority.ToString(),
                    type = r.Type.ToString()
                });

                return Json(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading project requirements for project {ProjectId}", projectId);
                return Json(new List<object>());
            }
        }

        private async Task PopulateDropdowns()
        {
            ViewBag.RequirementTypes = Enum.GetValues<RequirementType>()
                .Select(rt => new SelectListItem
                {
                    Value = ((int)rt).ToString(),
                    Text = rt.ToString()
                });

            ViewBag.RequirementPriorities = Enum.GetValues<RequirementPriority>()
                .Select(rp => new SelectListItem
                {
                    Value = ((int)rp).ToString(),
                    Text = rp.ToString()
                });

            ViewBag.RequirementStatuses = Enum.GetValues<RequirementStatus>()
                .Select(rs => new SelectListItem
                {
                    Value = ((int)rs).ToString(),
                    Text = rs.ToString()
                });

            ViewBag.RequirementSources = Enum.GetValues<RequirementSource>()
                .Select(rs => new SelectListItem
                {
                    Value = ((int)rs).ToString(),
                    Text = rs.ToString()
                });
        }
    }
}
