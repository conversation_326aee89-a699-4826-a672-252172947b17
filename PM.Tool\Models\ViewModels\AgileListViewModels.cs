using PM.Tool.Core.Entities.Agile;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Models.DTOs;

namespace PM.Tool.Models.ViewModels
{
    #region Feature List ViewModels

    public class FeatureListViewModel
    {
        public int ProjectId { get; set; }
        public string ProjectName { get; set; } = string.Empty;
        public IEnumerable<FeatureSummaryDto> Features { get; set; } = new List<FeatureSummaryDto>();
        public IEnumerable<Epic> Epics { get; set; } = new List<Epic>();
        public FeatureFilterOptions FilterOptions { get; set; } = new();
        public FeatureListMetrics Metrics { get; set; } = new();
    }

    public class FeatureFilterOptions
    {
        public int? EpicId { get; set; }
        public FeatureStatus? Status { get; set; }
        public FeaturePriority? Priority { get; set; }
        public string? SearchTerm { get; set; }
        public string? Owner { get; set; }
        public DateTime? TargetDateFrom { get; set; }
        public DateTime? TargetDateTo { get; set; }
    }

    public class FeatureListMetrics
    {
        public int TotalFeatures { get; set; }
        public int CompletedFeatures { get; set; }
        public int InProgressFeatures { get; set; }
        public int OverdueFeatures { get; set; }
        public decimal OverallProgress { get; set; }
        public decimal TotalStoryPoints { get; set; }
        public decimal CompletedStoryPoints { get; set; }
    }

    #endregion

    #region Bug List ViewModels

    public class BugListViewModel
    {
        public int ProjectId { get; set; }
        public string ProjectName { get; set; } = string.Empty;
        public IEnumerable<BugSummaryDto> Bugs { get; set; } = new List<BugSummaryDto>();
        public IEnumerable<Feature> Features { get; set; } = new List<Feature>();
        public IEnumerable<UserStory> UserStories { get; set; } = new List<UserStory>();
        public BugFilterOptions FilterOptions { get; set; } = new();
        public BugListMetrics Metrics { get; set; } = new();
    }

    public class BugFilterOptions
    {
        public int? FeatureId { get; set; }
        public int? UserStoryId { get; set; }
        public BugStatus? Status { get; set; }
        public BugSeverity? Severity { get; set; }
        public BugPriority? Priority { get; set; }
        public string? SearchTerm { get; set; }
        public string? AssignedTo { get; set; }
        public string? ReportedBy { get; set; }
        public DateTime? CreatedDateFrom { get; set; }
        public DateTime? CreatedDateTo { get; set; }
        public DateTime? TargetDateFrom { get; set; }
        public DateTime? TargetDateTo { get; set; }
        public bool? IsOverdue { get; set; }
    }

    public class BugListMetrics
    {
        public int TotalBugs { get; set; }
        public int OpenBugs { get; set; }
        public int InProgressBugs { get; set; }
        public int ResolvedBugs { get; set; }
        public int CriticalBugs { get; set; }
        public int HighPriorityBugs { get; set; }
        public int OverdueBugs { get; set; }
        public decimal ResolutionRate { get; set; }
        public double AverageResolutionTime { get; set; }
    }

    #endregion

    #region Test Case List ViewModels

    public class TestCaseListViewModel
    {
        public int ProjectId { get; set; }
        public string ProjectName { get; set; } = string.Empty;
        public IEnumerable<TestCaseSummaryDto> TestCases { get; set; } = new List<TestCaseSummaryDto>();
        public IEnumerable<Feature> Features { get; set; } = new List<Feature>();
        public IEnumerable<UserStory> UserStories { get; set; } = new List<UserStory>();
        public TestCaseFilterOptions FilterOptions { get; set; } = new();
        public TestCaseListMetrics Metrics { get; set; } = new();
    }

    public class TestCaseFilterOptions
    {
        public int? FeatureId { get; set; }
        public int? UserStoryId { get; set; }
        public TestCaseType? Type { get; set; }
        public TestCasePriority? Priority { get; set; }
        public TestCaseStatus? Status { get; set; }
        public TestExecutionResult? LastExecutionResult { get; set; }
        public string? SearchTerm { get; set; }
        public string? AssignedTo { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime? CreatedDateFrom { get; set; }
        public DateTime? CreatedDateTo { get; set; }
        public DateTime? LastExecutedFrom { get; set; }
        public DateTime? LastExecutedTo { get; set; }
        public bool? NeverExecuted { get; set; }
    }

    public class TestCaseListMetrics
    {
        public int TotalTestCases { get; set; }
        public int ExecutedTestCases { get; set; }
        public int PassedTestCases { get; set; }
        public int FailedTestCases { get; set; }
        public int NeverExecutedTestCases { get; set; }
        public decimal ExecutionRate { get; set; }
        public decimal PassRate { get; set; }
        public Dictionary<TestCaseType, int> TestCasesByType { get; set; } = new();
        public double TestCoveragePercentage { get; set; }
    }

    #endregion

    #region Dashboard ViewModels

    public class AgileDashboardViewModel
    {
        public int ProjectId { get; set; }
        public string ProjectName { get; set; } = string.Empty;
        public DashboardMetrics Metrics { get; set; } = new();
        public IEnumerable<FeatureSummaryDto> RecentFeatures { get; set; } = new List<FeatureSummaryDto>();
        public IEnumerable<BugSummaryDto> CriticalBugs { get; set; } = new List<BugSummaryDto>();
        public IEnumerable<TestCaseSummaryDto> FailedTestCases { get; set; } = new List<TestCaseSummaryDto>();
        public IEnumerable<SprintVelocityDto> VelocityTrends { get; set; } = new List<SprintVelocityDto>();
        public QualityMetrics QualityMetrics { get; set; } = new();
    }

    public class DashboardMetrics
    {
        public FeatureListMetrics Features { get; set; } = new();
        public BugListMetrics Bugs { get; set; } = new();
        public TestCaseListMetrics TestCases { get; set; } = new();
        public int ActiveSprints { get; set; }
        public int TotalUserStories { get; set; }
        public int CompletedUserStories { get; set; }
        public decimal CurrentVelocity { get; set; }
        public decimal PredictedVelocity { get; set; }
    }

    public class QualityMetrics
    {
        public decimal DefectDensity { get; set; }
        public decimal TestCoverage { get; set; }
        public decimal CodeQualityScore { get; set; }
        public int CriticalIssues { get; set; }
        public int HighPriorityIssues { get; set; }
        public decimal QualityTrend { get; set; }
    }

    #endregion

    #region Search and Filter ViewModels

    public class AgileSearchViewModel
    {
        public string SearchTerm { get; set; } = string.Empty;
        public int ProjectId { get; set; }
        public SearchResults Results { get; set; } = new();
    }

    public class SearchResults
    {
        public IEnumerable<FeatureSummaryDto> Features { get; set; } = new List<FeatureSummaryDto>();
        public IEnumerable<BugSummaryDto> Bugs { get; set; } = new List<BugSummaryDto>();
        public IEnumerable<TestCaseSummaryDto> TestCases { get; set; } = new List<TestCaseSummaryDto>();
        public IEnumerable<UserStory> UserStories { get; set; } = new List<UserStory>();
        public int TotalResults { get; set; }
    }

    public class FilterViewModel
    {
        public int ProjectId { get; set; }
        public string EntityType { get; set; } = string.Empty; // Feature, Bug, TestCase
        public Dictionary<string, object> Filters { get; set; } = new();
        public string SortBy { get; set; } = "CreatedAt";
        public string SortDirection { get; set; } = "desc";
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    #endregion

    #region Bulk Operations ViewModels

    public class BulkOperationViewModel
    {
        public IEnumerable<int> SelectedIds { get; set; } = new List<int>();
        public string Operation { get; set; } = string.Empty;
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    public class BulkUpdateFeatureViewModel : BulkOperationViewModel
    {
        public FeatureStatus? Status { get; set; }
        public FeaturePriority? Priority { get; set; }
        public int? EpicId { get; set; }
        public string? OwnerId { get; set; }
        public DateTime? TargetDate { get; set; }
        public string? Tags { get; set; }
    }

    public class BulkUpdateBugViewModel : BulkOperationViewModel
    {
        public BugStatus? Status { get; set; }
        public BugPriority? Priority { get; set; }
        public BugSeverity? Severity { get; set; }
        public string? AssignedToUserId { get; set; }
        public DateTime? TargetDate { get; set; }
        public string? Tags { get; set; }
    }

    public class BulkUpdateTestCaseViewModel : BulkOperationViewModel
    {
        public TestCaseStatus? Status { get; set; }
        public TestCasePriority? Priority { get; set; }
        public TestCaseType? Type { get; set; }
        public string? AssignedToUserId { get; set; }
        public string? Tags { get; set; }
    }

    #endregion
}
