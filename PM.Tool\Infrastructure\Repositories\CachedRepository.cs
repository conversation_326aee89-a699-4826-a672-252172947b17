using System.Linq.Expressions;
using PM.Tool.Core.Common;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Specifications;

namespace PM.Tool.Infrastructure.Repositories
{
    public class CachedRepository<T> : IRepository<T> where T : BaseEntity
    {
        private readonly IRepository<T> _repository;
        private readonly ICacheService _cache;
        private readonly string _entityName;
        private readonly TimeSpan _defaultExpiration = TimeSpan.FromMinutes(30);

        public CachedRepository(IRepository<T> repository, ICacheService cache)
        {
            _repository = repository;
            _cache = cache;
            _entityName = typeof(T).Name;
        }

        public async Task<T?> GetByIdAsync(int id)
        {
            var cacheKey = $"{_entityName}:Id:{id}";
            var cached = await _cache.GetAsync<T>(cacheKey);
            if (cached != null)
                return cached;

            var entity = await _repository.GetByIdAsync(id);
            if (entity != null)
                await _cache.SetAsync(cacheKey, entity, _defaultExpiration);
            
            return entity;
        }

        public async Task<T?> GetByIdAsync(string id)
        {
            var cacheKey = $"{_entityName}:StringId:{id}";
            var cached = await _cache.GetAsync<T>(cacheKey);
            if (cached != null)
                return cached;

            var entity = await _repository.GetByIdAsync(id);
            if (entity != null)
                await _cache.SetAsync(cacheKey, entity, _defaultExpiration);
            
            return entity;
        }

        public async Task<IEnumerable<T>> GetAllAsync()
        {
            var cacheKey = $"{_entityName}:All";
            var cached = await _cache.GetAsync<IEnumerable<T>>(cacheKey);
            if (cached != null)
                return cached;

            var entities = await _repository.GetAllAsync();
            await _cache.SetAsync(cacheKey, entities, _defaultExpiration);
            return entities;
        }

        public Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate)
        {
            // Don't cache dynamic queries
            return _repository.FindAsync(predicate);
        }

        public Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate)
        {
            // Don't cache dynamic queries
            return _repository.FirstOrDefaultAsync(predicate);
        }

        public Task<bool> AnyAsync(Expression<Func<T, bool>> predicate)
        {
            return _repository.AnyAsync(predicate);
        }

        public Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null)
        {
            return _repository.CountAsync(predicate);
        }

        public async Task<T> AddAsync(T entity)
        {
            var result = await _repository.AddAsync(entity);
            await InvalidateEntityCacheAsync();
            return result;
        }

        public async Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities)
        {
            var result = await _repository.AddRangeAsync(entities);
            await InvalidateEntityCacheAsync();
            return result;
        }

        public async Task UpdateAsync(T entity)
        {
            await _repository.UpdateAsync(entity);
            await InvalidateEntityCacheAsync(entity.Id);
        }

        public async Task UpdateRangeAsync(IEnumerable<T> entities)
        {
            await _repository.UpdateRangeAsync(entities);
            await InvalidateEntityCacheAsync();
        }

        public async Task DeleteAsync(T entity)
        {
            await _repository.DeleteAsync(entity);
            await InvalidateEntityCacheAsync(entity.Id);
        }

        public async Task DeleteRangeAsync(IEnumerable<T> entities)
        {
            await _repository.DeleteRangeAsync(entities);
            await InvalidateEntityCacheAsync();
        }

        public Task<bool> SaveChangesAsync()
        {
            return _repository.SaveChangesAsync();
        }

        public async Task<PaginatedList<T>> GetPagedAsync(PaginationParams pagination)
        {
            var cacheKey = $"{_entityName}:Page:{pagination.PageNumber}:{pagination.PageSize}";
            var cached = await _cache.GetAsync<PaginatedList<T>>(cacheKey);
            if (cached != null)
                return cached;

            var result = await _repository.GetPagedAsync(pagination);
            await _cache.SetAsync(cacheKey, result, _defaultExpiration);
            return result;
        }

        public Task<IEnumerable<T>> FindWithSpecificationAsync(ISpecification<T> spec)
        {
            // Don't cache specification-based queries as they can be complex and dynamic
            return _repository.FindWithSpecificationAsync(spec);
        }

        public Task<PaginatedList<T>> GetPagedWithSpecificationAsync(ISpecification<T> spec, PaginationParams pagination)
        {
            // Don't cache specification-based queries
            return _repository.GetPagedWithSpecificationAsync(spec, pagination);
        }

        public async Task<bool> SoftDeleteAsync(T entity)
        {
            var result = await _repository.SoftDeleteAsync(entity);
            if (result)
                await InvalidateEntityCacheAsync(entity.Id);
            return result;
        }

        public async Task<bool> RestoreAsync(T entity)
        {
            var result = await _repository.RestoreAsync(entity);
            if (result)
                await InvalidateEntityCacheAsync(entity.Id);
            return result;
        }

        public Task<IEnumerable<T>> GetAllIncludingDeletedAsync()
        {
            return _repository.GetAllIncludingDeletedAsync();
        }

        private async Task InvalidateEntityCacheAsync(int? id = null)
        {
            if (id.HasValue)
                await _cache.RemoveAsync($"{_entityName}:Id:{id}");
            
            await _cache.RemoveByPrefixAsync(_entityName);
        }
    }
}
