using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities
{
    public class TaskComment
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(2000)]
        public string Content { get; set; } = string.Empty;

        public int TaskId { get; set; }

        public string UserId { get; set; } = string.Empty;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public virtual TaskEntity Task { get; set; } = null!;
        public virtual ApplicationUser User { get; set; } = null!;
    }
}
