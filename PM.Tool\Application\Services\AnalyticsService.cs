using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;
using TaskStatus = PM.Tool.Core.Enums.TaskStatus;

namespace PM.Tool.Application.Services
{
    public class AnalyticsService : IAnalyticsService
    {
        private readonly ApplicationDbContext _context;

        public AnalyticsService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<ProjectMetrics> GetProjectMetricsAsync(int projectId, DateTime date)
        {
            var project = await _context.Projects
                .Include(p => p.Tasks)
                .Include(p => p.Members)
                .FirstOrDefaultAsync(p => p.Id == projectId);

            if (project == null)
                return new ProjectMetrics { ProjectId = projectId, Date = date };

            var tasks = project.Tasks.Where(t => !t.IsDeleted).ToList();
            var completedTasks = tasks.Where(t => t.Status == TaskStatus.Done).ToList();
            var inProgressTasks = tasks.Where(t => t.Status == TaskStatus.InProgress).ToList();
            var overdueTasks = tasks.Where(t => t.IsOverdue).ToList();

            var totalTimeSpent = tasks.Sum(t => t.TotalTimeSpent);
            var totalEstimatedTime = tasks.Sum(t => t.EstimatedHours);

            return new ProjectMetrics
            {
                ProjectId = projectId,
                Date = date,
                TotalTasks = tasks.Count,
                CompletedTasks = completedTasks.Count,
                InProgressTasks = inProgressTasks.Count,
                OverdueTasks = overdueTasks.Count,
                BudgetSpent = 0, // Calculate based on time entries and rates
                BudgetRemaining = project.Budget,
                ProgressPercentage = project.ProgressPercentage,
                Velocity = await CalculateVelocityAsync(projectId, date.AddDays(-30), date),
                TeamSize = project.Members.Count(m => m.IsActive),
                UtilizationRate = totalEstimatedTime > 0 ? totalTimeSpent / totalEstimatedTime * 100 : 0,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
        }

        public async Task<IEnumerable<ProjectMetrics>> GetProjectMetricsHistoryAsync(int projectId, DateTime startDate, DateTime endDate)
        {
            return await _context.ProjectMetrics
                .Where(pm => pm.ProjectId == projectId &&
                           pm.Date >= startDate &&
                           pm.Date <= endDate)
                .OrderBy(pm => pm.Date)
                .ToListAsync();
        }

        public async Task<Dictionary<string, object>> GetProjectDashboardDataAsync(int projectId)
        {
            var project = await _context.Projects
                .Include(p => p.Tasks)
                .Include(p => p.Members)
                .Include(p => p.Milestones)
                .FirstOrDefaultAsync(p => p.Id == projectId);

            if (project == null) return new Dictionary<string, object>();

            var tasks = project.Tasks.Where(t => !t.IsDeleted).ToList();
            var risks = await _context.Risks
                .Where(r => r.ProjectId == projectId && !r.IsDeleted)
                .ToListAsync();

            return new Dictionary<string, object>
            {
                ["totalTasks"] = tasks.Count,
                ["completedTasks"] = tasks.Count(t => t.Status == TaskStatus.Done),
                ["overdueTasks"] = tasks.Count(t => t.IsOverdue),
                ["progressPercentage"] = project.ProgressPercentage,
                ["teamSize"] = project.Members.Count(m => m.IsActive),
                ["totalMilestones"] = project.Milestones.Count,
                ["completedMilestones"] = project.Milestones.Count(m => m.IsCompleted),
                ["totalRisks"] = risks.Count,
                ["highRisks"] = risks.Count(r => r.RiskLevel == RiskLevel.High || r.RiskLevel == RiskLevel.Critical),
                ["budgetUtilization"] = project.Budget > 0 ? 0 : 0, // Calculate actual budget spent
                ["daysRemaining"] = project.EndDate.HasValue ? (project.EndDate.Value - DateTime.UtcNow).TotalDays : 0
            };
        }

        public async Task<Dictionary<string, int>> GetTaskStatusDistributionAsync(int projectId)
        {
            var tasks = await _context.Tasks
                .Where(t => t.ProjectId == projectId && !t.IsDeleted)
                .GroupBy(t => t.Status)
                .Select(g => new { Status = g.Key.ToString(), Count = g.Count() })
                .ToListAsync();

            return tasks.ToDictionary(t => t.Status, t => t.Count);
        }

        public async Task<Dictionary<string, int>> GetTaskPriorityDistributionAsync(int projectId)
        {
            var tasks = await _context.Tasks
                .Where(t => t.ProjectId == projectId && !t.IsDeleted)
                .GroupBy(t => t.Priority)
                .Select(g => new { Priority = g.Key.ToString(), Count = g.Count() })
                .ToListAsync();

            return tasks.ToDictionary(t => t.Priority, t => t.Count);
        }

        public async Task<IEnumerable<object>> GetTaskCompletionTrendAsync(int projectId, DateTime startDate, DateTime endDate)
        {
            var completedTasks = await _context.Tasks
                .Where(t => t.ProjectId == projectId &&
                           !t.IsDeleted &&
                           t.CompletedDate.HasValue &&
                           t.CompletedDate >= startDate &&
                           t.CompletedDate <= endDate)
                .GroupBy(t => t.CompletedDate.Value.Date)
                .Select(g => new { Date = g.Key, Count = g.Count() })
                .OrderBy(g => g.Date)
                .ToListAsync();

            return completedTasks.Select(ct => new { date = ct.Date, completed = ct.Count });
        }

        public async Task<double> GetProjectVelocityAsync(int projectId, DateTime startDate, DateTime endDate)
        {
            return await CalculateVelocityAsync(projectId, startDate, endDate);
        }

        private async Task<double> CalculateVelocityAsync(int projectId, DateTime startDate, DateTime endDate)
        {
            var completedTasks = await _context.Tasks
                .Where(t => t.ProjectId == projectId &&
                           !t.IsDeleted &&
                           t.Status == TaskStatus.Done &&
                           t.CompletedDate.HasValue &&
                           t.CompletedDate >= startDate &&
                           t.CompletedDate <= endDate)
                .CountAsync();

            var days = (endDate - startDate).TotalDays;
            return days > 0 ? completedTasks / days : 0;
        }

        public async Task<Dictionary<string, decimal>> GetTeamWorkloadAsync(int projectId)
        {
            var workload = await _context.Tasks
                .Where(t => t.ProjectId == projectId &&
                           !t.IsDeleted &&
                           t.AssignedToUserId != null)
                .Include(t => t.AssignedTo)
                .GroupBy(t => t.AssignedTo!.FullName)
                .Select(g => new { User = g.Key, Hours = g.Sum(t => t.EstimatedHours) })
                .ToListAsync();

            return workload.ToDictionary(w => w.User, w => (decimal)w.Hours);
        }

        public async Task<Dictionary<string, decimal>> GetTeamProductivityAsync(int projectId, DateTime startDate, DateTime endDate)
        {
            var productivity = await _context.Tasks
                .Where(t => t.ProjectId == projectId &&
                           !t.IsDeleted &&
                           t.AssignedToUserId != null &&
                           t.CompletedDate.HasValue &&
                           t.CompletedDate >= startDate &&
                           t.CompletedDate <= endDate)
                .Include(t => t.AssignedTo)
                .GroupBy(t => t.AssignedTo!.FullName)
                .Select(g => new {
                    User = g.Key,
                    Completed = g.Count(),
                    EstimatedHours = g.Sum(t => t.EstimatedHours),
                    ActualHours = g.Sum(t => t.ActualHours)
                })
                .ToListAsync();

            return productivity.ToDictionary(
                p => p.User,
                p => p.EstimatedHours > 0 ? (decimal)p.ActualHours / p.EstimatedHours : 0
            );
        }

        public async Task<IEnumerable<object>> GetTeamUtilizationTrendAsync(int projectId, DateTime startDate, DateTime endDate)
        {
            // This would require more complex calculation based on time entries
            // For now, return a simplified version
            var utilization = await _context.TimeEntries
                .Where(te => te.Task.ProjectId == projectId &&
                           te.StartTime >= startDate &&
                           te.StartTime <= endDate)
                .Include(te => te.User)
                .GroupBy(te => new { Date = te.StartTime.Date, User = te.User.FullName })
                .Select(g => new {
                    Date = g.Key.Date,
                    User = g.Key.User,
                    Hours = g.Sum(te => te.Duration)
                })
                .ToListAsync();

            return utilization.Select(u => new {
                date = u.Date,
                user = u.User,
                hours = u.Hours
            });
        }

        public async Task<IEnumerable<object>> GetBurndownChartDataAsync(int projectId, DateTime startDate, DateTime endDate)
        {
            var project = await _context.Projects
                .Include(p => p.Tasks)
                .FirstOrDefaultAsync(p => p.Id == projectId);

            if (project == null) return new List<object>();

            var totalTasks = project.Tasks.Count(t => !t.IsDeleted);
            var burndownData = new List<object>();

            for (var date = startDate.Date; date <= endDate.Date; date = date.AddDays(1))
            {
                var completedByDate = project.Tasks
                    .Count(t => !t.IsDeleted &&
                               t.CompletedDate.HasValue &&
                               t.CompletedDate.Value.Date <= date);

                var remaining = totalTasks - completedByDate;

                burndownData.Add(new {
                    date = date,
                    remaining = remaining,
                    ideal = CalculateIdealBurndown(totalTasks, startDate, endDate, date)
                });
            }

            return burndownData;
        }

        private int CalculateIdealBurndown(int totalTasks, DateTime startDate, DateTime endDate, DateTime currentDate)
        {
            var totalDays = (endDate - startDate).TotalDays;
            var daysPassed = (currentDate - startDate).TotalDays;

            if (totalDays <= 0) return totalTasks;

            var idealRemaining = totalTasks - (int)(totalTasks * daysPassed / totalDays);
            return Math.Max(0, idealRemaining);
        }

        public async Task<IEnumerable<object>> GetBurnupChartDataAsync(int projectId, DateTime startDate, DateTime endDate)
        {
            var project = await _context.Projects
                .Include(p => p.Tasks)
                .FirstOrDefaultAsync(p => p.Id == projectId);

            if (project == null) return new List<object>();

            var burnupData = new List<object>();

            for (var date = startDate.Date; date <= endDate.Date; date = date.AddDays(1))
            {
                var totalTasksByDate = project.Tasks
                    .Count(t => !t.IsDeleted && t.CreatedAt.Date <= date);

                var completedByDate = project.Tasks
                    .Count(t => !t.IsDeleted &&
                               t.CompletedDate.HasValue &&
                               t.CompletedDate.Value.Date <= date);

                burnupData.Add(new {
                    date = date,
                    total = totalTasksByDate,
                    completed = completedByDate
                });
            }

            return burnupData;
        }

        public async Task<Dictionary<string, decimal>> GetTimeSpentByTaskAsync(int projectId)
        {
            var timeSpent = await _context.Tasks
                .Where(t => t.ProjectId == projectId && !t.IsDeleted)
                .Include(t => t.TimeEntries)
                .Select(t => new {
                    Task = t.Title,
                    Hours = t.TimeEntries.Sum(te => te.Duration)
                })
                .ToListAsync();

            return timeSpent.ToDictionary(ts => ts.Task, ts => (decimal)ts.Hours);
        }

        public async Task<Dictionary<string, decimal>> GetTimeSpentByUserAsync(int projectId, DateTime startDate, DateTime endDate)
        {
            var timeSpent = await _context.TimeEntries
                .Where(te => te.Task.ProjectId == projectId &&
                           te.StartTime >= startDate &&
                           te.StartTime <= endDate)
                .Include(te => te.User)
                .GroupBy(te => te.User.FullName)
                .Select(g => new { User = g.Key, Hours = g.Sum(te => te.Duration) })
                .ToListAsync();

            return timeSpent.ToDictionary(ts => ts.User, ts => (decimal)ts.Hours);
        }

        public async Task<decimal> GetProjectTimeEfficiencyAsync(int projectId)
        {
            var tasks = await _context.Tasks
                .Where(t => t.ProjectId == projectId && !t.IsDeleted)
                .Include(t => t.TimeEntries)
                .ToListAsync();

            var totalEstimated = tasks.Sum(t => t.EstimatedHours);
            var totalActual = tasks.Sum(t => t.TimeEntries.Sum(te => te.Duration));

            return totalActual > 0 ? (decimal)totalEstimated / (decimal)totalActual * 100 : 0;
        }

        public async Task<Dictionary<string, decimal>> GetBudgetAnalysisAsync(int projectId)
        {
            var project = await _context.Projects.FindAsync(projectId);
            if (project == null) return new Dictionary<string, decimal>();

            // This would need to be calculated based on actual cost tracking
            // For now, return placeholder values
            return new Dictionary<string, decimal>
            {
                ["budgetAllocated"] = project.Budget,
                ["budgetSpent"] = 0, // Calculate from time entries and resource rates
                ["budgetRemaining"] = project.Budget,
                ["budgetUtilization"] = 0
            };
        }

        public async Task<IEnumerable<object>> GetBudgetTrendAsync(int projectId, DateTime startDate, DateTime endDate)
        {
            // This would track budget spending over time
            // For now, return placeholder data
            var budgetTrend = new List<object>();

            for (var date = startDate.Date; date <= endDate.Date; date = date.AddDays(7))
            {
                budgetTrend.Add(new {
                    date = date,
                    spent = 0, // Calculate actual spending
                    allocated = 0 // Calculate allocated budget by date
                });
            }

            return budgetTrend;
        }

        public async Task<Dictionary<string, int>> GetRiskDistributionAsync(int projectId)
        {
            var risks = await _context.Risks
                .Where(r => r.ProjectId == projectId && !r.IsDeleted)
                .ToListAsync();

            return risks
                .GroupBy(r => r.RiskLevel.ToString())
                .ToDictionary(g => g.Key, g => g.Count());
        }

        public async Task<decimal> GetProjectRiskIndexAsync(int projectId)
        {
            var risks = await _context.Risks
                .Where(r => r.ProjectId == projectId && !r.IsDeleted && r.Status != RiskStatus.Resolved)
                .ToListAsync();

            if (!risks.Any()) return 0;

            var totalScore = risks.Sum(r => r.RiskScore);
            var maxPossibleScore = risks.Count * 25; // Max score is 5 * 5 = 25

            return maxPossibleScore > 0 ? (decimal)totalScore / maxPossibleScore * 100 : 0;
        }

        public async Task<IEnumerable<object>> GetMilestoneProgressAsync(int projectId)
        {
            var milestones = await _context.Milestones
                .Where(m => m.ProjectId == projectId && !m.IsDeleted)
                .OrderBy(m => m.DueDate)
                .ToListAsync();

            return milestones.Select(m => new {
                id = m.Id,
                name = m.Name,
                dueDate = m.DueDate,
                isCompleted = m.IsCompleted,
                completedDate = m.CompletedDate,
                isOverdue = m.IsOverdue
            });
        }

        public async Task<Dictionary<string, object>> GetMilestonePerformanceAsync(int projectId)
        {
            var milestones = await _context.Milestones
                .Where(m => m.ProjectId == projectId && !m.IsDeleted)
                .ToListAsync();

            var total = milestones.Count;
            var completed = milestones.Count(m => m.IsCompleted);
            var overdue = milestones.Count(m => m.IsOverdue);
            var onTime = milestones.Count(m => m.IsCompleted &&
                                                m.CompletedDate.HasValue &&
                                                m.CompletedDate <= m.DueDate);

            return new Dictionary<string, object>
            {
                ["total"] = total,
                ["completed"] = completed,
                ["overdue"] = overdue,
                ["onTime"] = onTime,
                ["completionRate"] = total > 0 ? (double)completed / total * 100 : 0,
                ["onTimeRate"] = completed > 0 ? (double)onTime / completed * 100 : 0
            };
        }

        public async Task<IEnumerable<object>> CompareProjectPerformanceAsync(IEnumerable<int> projectIds, DateTime startDate, DateTime endDate)
        {
            var projects = await _context.Projects
                .Where(p => projectIds.Contains(p.Id))
                .Include(p => p.Tasks)
                .ToListAsync();

            return projects.Select(p => new {
                projectId = p.Id,
                projectName = p.Name,
                totalTasks = p.Tasks.Count(t => !t.IsDeleted),
                completedTasks = p.Tasks.Count(t => !t.IsDeleted && t.Status == TaskStatus.Done),
                progressPercentage = p.ProgressPercentage,
                isOverdue = p.IsOverdue,
                budget = p.Budget
            });
        }

        public async Task<Dictionary<string, object>> GetPortfolioOverviewAsync(string userId)
        {
            var userProjects = await _context.ProjectMembers
                .Where(pm => pm.UserId == userId && pm.IsActive)
                .Include(pm => pm.Project)
                    .ThenInclude(p => p.Tasks)
                .Select(pm => pm.Project)
                .ToListAsync();

            var totalProjects = userProjects.Count;
            var activeProjects = userProjects.Count(p => p.Status == Core.Enums.ProjectStatus.Active ||
                                                        p.Status == Core.Enums.ProjectStatus.InProgress);
            var totalTasks = userProjects.Sum(p => p.Tasks.Count(t => !t.IsDeleted));
            var completedTasks = userProjects.Sum(p => p.Tasks.Count(t => !t.IsDeleted && t.Status == TaskStatus.Done));

            return new Dictionary<string, object>
            {
                ["totalProjects"] = totalProjects,
                ["activeProjects"] = activeProjects,
                ["totalTasks"] = totalTasks,
                ["completedTasks"] = completedTasks,
                ["overallProgress"] = totalTasks > 0 ? (double)completedTasks / totalTasks * 100 : 0,
                ["totalBudget"] = userProjects.Sum(p => p.Budget)
            };
        }

        public async Task<DateTime?> PredictProjectCompletionAsync(int projectId)
        {
            var velocity = await GetProjectVelocityAsync(projectId, DateTime.UtcNow.AddDays(-30), DateTime.UtcNow);

            var remainingTasks = await _context.Tasks
                .CountAsync(t => t.ProjectId == projectId &&
                               !t.IsDeleted &&
                               t.Status != TaskStatus.Done);

            if (velocity <= 0 || remainingTasks == 0) return null;

            var daysToComplete = remainingTasks / velocity;
            return DateTime.UtcNow.AddDays(daysToComplete);
        }

        public async Task<decimal> PredictBudgetOverrunAsync(int projectId)
        {
            // This would require more sophisticated calculation based on current spending rate
            // For now, return a placeholder
            return 0;
        }

        public async Task<IEnumerable<object>> GetProjectHealthIndicatorsAsync(int projectId)
        {
            var project = await _context.Projects
                .Include(p => p.Tasks)
                .FirstOrDefaultAsync(p => p.Id == projectId);

            if (project == null) return new List<object>();

            var indicators = new List<object>();

            // Schedule Health
            var scheduleHealth = project.IsOverdue ? "Red" :
                               project.EndDate.HasValue &&
                               (project.EndDate.Value - DateTime.UtcNow).TotalDays < 7 ? "Yellow" : "Green";

            indicators.Add(new {
                indicator = "Schedule",
                status = scheduleHealth,
                value = project.ProgressPercentage
            });

            // Budget Health (placeholder)
            indicators.Add(new {
                indicator = "Budget",
                status = "Green",
                value = 0
            });

            // Quality Health (based on overdue tasks)
            var overdueTasks = project.Tasks.Count(t => t.IsOverdue);
            var qualityHealth = overdueTasks > 5 ? "Red" : overdueTasks > 2 ? "Yellow" : "Green";

            indicators.Add(new {
                indicator = "Quality",
                status = qualityHealth,
                value = overdueTasks
            });

            return indicators;
        }
    }
}
