@model dynamic

<div class="space-y-6">
    <!-- Task Header -->
    <div class="border-b border-neutral-200 dark:border-dark-200 pb-4">
        <div class="flex items-start justify-between">
            <div>
                <h4 class="text-xl font-semibold text-neutral-900 dark:text-dark-100 mb-2">
                    @Model.Task.Title
                </h4>
                @if (!string.IsNullOrEmpty(Model.Task.WbsCode))
                {
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200">
                        WBS: @Model.Task.WbsCode
                    </span>
                }
            </div>
            <div class="flex items-center space-x-2">
                @{
                    var statusClass = Model.Task.Status.ToString().ToLower() switch
                    {
                        "notstarted" => "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200",
                        "inprogress" => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
                        "inreview" => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
                        "done" => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
                        "blocked" => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
                        _ => "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200"
                    };

                    var priorityClass = Model.Task.Priority.ToString().ToLower() switch
                    {
                        "low" => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
                        "medium" => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
                        "high" => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
                        "critical" => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
                        _ => "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200"
                    };
                }
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @statusClass">
                    @Model.Task.Status
                </span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @priorityClass">
                    @Model.Task.Priority
                </span>
            </div>
        </div>
    </div>

    <!-- Task Details Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Left Column -->
        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Description</label>
                <div class="text-sm text-neutral-600 dark:text-dark-400">
                    @if (!string.IsNullOrEmpty(Model.Task.Description))
                    {
                        <p>@Model.Task.Description</p>
                    }
                    else
                    {
                        <p class="italic">No description provided</p>
                    }
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Assigned To</label>
                <div class="text-sm text-neutral-600 dark:text-dark-400">
                    @if (Model.Task.AssignedTo != null)
                    {
                        <div class="flex items-center space-x-2">
                            <div class="w-6 h-6 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-primary-600 dark:text-primary-400 text-xs"></i>
                            </div>
                            <span>@Model.Task.AssignedTo.FullName</span>
                        </div>
                    }
                    else
                    {
                        <span class="italic">Unassigned</span>
                    }
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Progress</label>
                <div class="flex items-center space-x-3">
                    <div class="flex-1 bg-neutral-200 dark:bg-dark-600 rounded-full h-2">
                        <div class="bg-primary-600 h-2 rounded-full transition-all duration-300" style="width: @(Model.Task.Progress ?? 0)%"></div>
                    </div>
                    <span class="text-sm font-medium text-neutral-700 dark:text-dark-300">@(Model.Task.Progress ?? 0)%</span>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Start Date</label>
                    <div class="text-sm text-neutral-600 dark:text-dark-400">
                        @if (Model.Task.StartDate.HasValue)
                        {
                            <span>@Model.Task.StartDate.Value.ToString("MMM dd, yyyy")</span>
                        }
                        else
                        {
                            <span class="italic">Not set</span>
                        }
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Due Date</label>
                    <div class="text-sm text-neutral-600 dark:text-dark-400">
                        @if (Model.Task.DueDate.HasValue)
                        {
                            <span>@Model.Task.DueDate.Value.ToString("MMM dd, yyyy")</span>
                            @if (Model.Task.DueDate.Value < DateTime.Now && Model.Task.Status.ToString() != "Done")
                            {
                                <span class="ml-2 text-red-600 dark:text-red-400 text-xs">(Overdue)</span>
                            }
                        }
                        else
                        {
                            <span class="italic">Not set</span>
                        }
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Estimated Hours</label>
                    <div class="text-sm text-neutral-600 dark:text-dark-400">
                        @if (Model.Task.EstimatedHours.HasValue)
                        {
                            <span>@Model.Task.EstimatedHours.Value hours</span>
                        }
                        else
                        {
                            <span class="italic">Not estimated</span>
                        }
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Actual Hours</label>
                    <div class="text-sm text-neutral-600 dark:text-dark-400">
                        @if (Model.Task.ActualHours.HasValue)
                        {
                            <span>@Model.Task.ActualHours.Value hours</span>
                        }
                        else
                        {
                            <span class="italic">Not tracked</span>
                        }
                    </div>
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Created</label>
                <div class="text-sm text-neutral-600 dark:text-dark-400">
                    <span>@Model.Task.CreatedAt.ToString("MMM dd, yyyy 'at' h:mm tt")</span>
                    @if (Model.Task.CreatedByUser != null)
                    {
                        <span> by @Model.Task.CreatedByUser.FullName</span>
                    }
                </div>
            </div>

            @if (Model.Task.UpdatedAt.HasValue)
            {
                <div>
                    <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Last Updated</label>
                    <div class="text-sm text-neutral-600 dark:text-dark-400">
                        <span>@Model.Task.UpdatedAt.Value.ToString("MMM dd, yyyy 'at' h:mm tt")</span>
                    </div>
                </div>
            }
        </div>
    </div>

    <!-- Comments Section -->
    @if (Model.Comments != null && Model.Comments.Any())
    {
        <div class="border-t border-neutral-200 dark:border-dark-200 pt-6">
            <h5 class="text-lg font-medium text-neutral-900 dark:text-dark-100 mb-4">
                Comments (@Model.Comments.Count())
            </h5>
            <div class="space-y-4 max-h-64 overflow-y-auto">
                @{
                    var sortedComments = ((IEnumerable<dynamic>)Model.Comments).OrderByDescending(c => c.CreatedAt);
                }
                @foreach (var comment in sortedComments)
                {
                    <div class="bg-neutral-50 dark:bg-dark-800 rounded-lg p-4">
                        <div class="flex items-start justify-between mb-2">
                            <div class="flex items-center space-x-2">
                                <div class="w-6 h-6 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-primary-600 dark:text-primary-400 text-xs"></i>
                                </div>
                                <span class="text-sm font-medium text-neutral-900 dark:text-dark-100">
                                    @(comment.User?.FullName ?? "Unknown User")
                                </span>
                            </div>
                            <span class="text-xs text-neutral-500 dark:text-dark-400">
                                @comment.CreatedAt.ToString("MMM dd, yyyy 'at' h:mm tt")
                            </span>
                        </div>
                        <p class="text-sm text-neutral-700 dark:text-dark-300">@comment.Content</p>
                    </div>
                }
            </div>
        </div>
    }
</div>
