@{
    ViewData["Title"] = "Advanced Reports";
    var projects = ViewBag.Projects as IEnumerable<PM.Tool.Core.Entities.Project> ?? new List<PM.Tool.Core.Entities.Project>();
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-file-alt mr-3 text-primary-600 dark:text-primary-400"></i>
                Advanced Reports
            </h1>
            <p class="mt-2 text-lg text-neutral-600 dark:text-dark-300">
                Generate comprehensive reports and export analytics data
            </p>
        </div>
        <div class="flex space-x-3">
            @{
                ViewData["Text"] = "Back to Dashboard";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Index");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Report Categories -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <!-- Project Reports -->
    <div class="card-custom h-full">
        <div class="card-body-custom text-center">
            <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-chart-line text-3xl text-primary-600 dark:text-primary-400"></i>
            </div>
            <h3 class="text-xl font-semibold text-neutral-900 dark:text-dark-100 mb-3">Project Reports</h3>
            <p class="text-neutral-600 dark:text-dark-300 mb-6">
                Detailed project performance, timeline analysis, and milestone tracking reports.
            </p>

            @if (projects.Any())
            {
                <div class="relative">
                    <button id="project-reports-toggle"
                            class="btn-primary-custom w-full flex items-center justify-center space-x-2">
                        <i class="fas fa-download"></i>
                        <span>Generate Report</span>
                        <i class="fas fa-chevron-down text-sm"></i>
                    </button>

                    <div id="project-reports-menu"
                         class="absolute left-0 right-0 mt-2 bg-white dark:bg-surface-dark rounded-lg shadow-strong border border-neutral-200 dark:border-dark-200 py-2 hidden max-h-48 overflow-y-auto z-10">
                        @foreach (var project in projects)
                        {
                            <button class="w-full text-left px-4 py-2 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors generate-report-btn"
                                    data-project-id="@project.Id" data-project-name="@project.Name">
                                <i class="fas fa-project-diagram mr-2 text-primary-500"></i>
                                @project.Name
                            </button>
                        }
                    </div>
                </div>
            }
            else
            {
                <div class="text-center py-4">
                    <p class="text-neutral-500 dark:text-dark-400 text-sm">No projects available</p>
                </div>
            }
        </div>
    </div>

    <!-- Team Reports -->
    <div class="card-custom h-full">
        <div class="card-body-custom text-center">
            <div class="w-16 h-16 bg-success-100 dark:bg-success-900 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-users text-3xl text-success-600 dark:text-success-400"></i>
            </div>
            <h3 class="text-xl font-semibold text-neutral-900 dark:text-dark-100 mb-3">Team Reports</h3>
            <p class="text-neutral-600 dark:text-dark-300 mb-6">
                Team productivity, resource utilization, and performance analysis reports.
            </p>

            @{
                ViewData["Text"] = "Generate Team Report";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-download";
                ViewData["Href"] = "#";
                ViewData["OnClick"] = "generateTeamReport()";
                ViewData["FullWidth"] = true;
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>

    <!-- Custom Reports -->
    <div class="card-custom h-full">
        <div class="card-body-custom text-center">
            <div class="w-16 h-16 bg-warning-100 dark:bg-warning-900 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-chart-bar text-3xl text-warning-600 dark:text-warning-400"></i>
            </div>
            <h3 class="text-xl font-semibold text-neutral-900 dark:text-dark-100 mb-3">Custom Reports</h3>
            <p class="text-neutral-600 dark:text-dark-300 mb-6">
                Build custom reports with specific metrics, date ranges, and filtering options.
            </p>

            @{
                ViewData["Text"] = "Build Custom Report";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-cogs";
                ViewData["Href"] = "#";
                ViewData["OnClick"] = "openCustomReportBuilder()";
                ViewData["FullWidth"] = true;
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Quick Export Options -->
<div class="card-custom">
    <div class="card-header-custom">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                <i class="fas fa-download text-primary-600 dark:text-primary-400 text-sm"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Quick Export Options</h3>
                <p class="text-sm text-neutral-500 dark:text-dark-400">Export data in multiple formats</p>
            </div>
        </div>
    </div>
    <div class="card-body-custom">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Project Data Export -->
            <div>
                <h4 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">Project Data Export</h4>
                @if (projects.Any())
                {
                    <div class="space-y-4">
                        @foreach (var project in projects)
                        {
                            <div class="flex items-center justify-between p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                                <div class="flex-1">
                                    <h5 class="text-sm font-semibold text-neutral-900 dark:text-dark-100">@project.Name</h5>
                                    <p class="text-xs text-neutral-500 dark:text-dark-400">
                                        @project.TotalTasks tasks • @project.ProgressPercentage.ToString("F0")% complete
                                    </p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button class="btn-outline-custom btn-sm export-btn" data-project-id="@project.Id" data-format="csv">
                                        CSV
                                    </button>
                                    <button class="btn-outline-custom btn-sm export-btn" data-project-id="@project.Id" data-format="xlsx">
                                        Excel
                                    </button>
                                    <button class="btn-outline-custom btn-sm export-btn" data-project-id="@project.Id" data-format="pdf">
                                        PDF
                                    </button>
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center py-12">
                        <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-inbox text-2xl text-neutral-400 dark:text-dark-500"></i>
                        </div>
                        <p class="text-neutral-500 dark:text-dark-400">No projects available for export</p>
                    </div>
                }
            </div>

            <!-- Analytics Charts -->
            <div>
                <h4 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">Analytics Charts</h4>
                <div class="space-y-4">
                    <!-- Burndown Charts -->
                    <div class="flex items-center justify-between p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                        <div class="flex-1">
                            <h5 class="text-sm font-semibold text-neutral-900 dark:text-dark-100">Burndown Charts</h5>
                            <p class="text-xs text-neutral-500 dark:text-dark-400">Project progress over time</p>
                        </div>
                        <div class="relative">
                            <button id="burndown-toggle"
                                    class="btn-outline-custom btn-sm flex items-center space-x-1">
                                <span>View</span>
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>

                            <div id="burndown-menu"
                                 class="absolute right-0 mt-2 w-48 bg-white dark:bg-surface-dark rounded-lg shadow-strong border border-neutral-200 dark:border-dark-200 py-2 hidden z-10">
                                @if (projects.Any())
                                {
                                    @foreach (var project in projects)
                                    {
                                        <a href="@Url.Action("Burndown", new { projectId = project.Id })"
                                           class="block px-4 py-2 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors">
                                            @project.Name
                                        </a>
                                    }
                                }
                                else
                                {
                                    <div class="px-4 py-2 text-sm text-neutral-500 dark:text-dark-400">
                                        No projects available
                                    </div>
                                }
                            </div>
                        </div>
                    </div>

                    <!-- Velocity Charts -->
                    <div class="flex items-center justify-between p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                        <div class="flex-1">
                            <h5 class="text-sm font-semibold text-neutral-900 dark:text-dark-100">Velocity Charts</h5>
                            <p class="text-xs text-neutral-500 dark:text-dark-400">Team velocity tracking</p>
                        </div>
                        <div class="relative">
                            <button id="velocity-toggle"
                                    class="btn-outline-custom btn-sm flex items-center space-x-1">
                                <span>View</span>
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>

                            <div id="velocity-menu"
                                 class="absolute right-0 mt-2 w-48 bg-white dark:bg-surface-dark rounded-lg shadow-strong border border-neutral-200 dark:border-dark-200 py-2 hidden z-10">
                                @if (projects.Any())
                                {
                                    @foreach (var project in projects)
                                    {
                                        <a href="@Url.Action("Velocity", new { projectId = project.Id })"
                                           class="block px-4 py-2 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors">
                                            @project.Name
                                        </a>
                                    }
                                }
                                else
                                {
                                    <div class="px-4 py-2 text-sm text-neutral-500 dark:text-dark-400">
                                        No projects available
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden form for CSRF token -->
<form id="hiddenTokenForm" style="display: none;">
    @Html.AntiForgeryToken()
</form>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Initialize dropdown toggles
            const dropdowns = [
                { toggle: 'project-reports-toggle', menu: 'project-reports-menu' },
                { toggle: 'burndown-toggle', menu: 'burndown-menu' },
                { toggle: 'velocity-toggle', menu: 'velocity-menu' }
            ];

            dropdowns.forEach(dropdown => {
                const toggle = document.getElementById(dropdown.toggle);
                const menu = document.getElementById(dropdown.menu);

                if (toggle && menu) {
                    toggle.addEventListener('click', function(e) {
                        e.stopPropagation();
                        menu.classList.toggle('hidden');

                        // Close other dropdowns
                        dropdowns.forEach(other => {
                            if (other.menu !== dropdown.menu) {
                                const otherMenu = document.getElementById(other.menu);
                                if (otherMenu) otherMenu.classList.add('hidden');
                            }
                        });
                    });
                }
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function() {
                dropdowns.forEach(dropdown => {
                    const menu = document.getElementById(dropdown.menu);
                    if (menu) menu.classList.add('hidden');
                });
            });

            // Event listeners for report generation
            $('.generate-report-btn').on('click', function(e) {
                e.preventDefault();
                const projectId = $(this).data('project-id');
                const projectName = $(this).data('project-name');
                generateProjectReport(projectId, projectName);
            });

            // Export button handlers
            $('.export-btn').on('click', function(e) {
                e.preventDefault();
                const projectId = $(this).data('project-id');
                const format = $(this).data('format');
                exportProjectData(projectId, format);
            });

            // Animate cards on load
            $('.card-custom').each(function(index) {
                $(this).delay(index * 150).queue(function() {
                    $(this).addClass('animate-fade-in-up').dequeue();
                });
            });
        });

        // Report generation functions
        function generateProjectReport(projectId, projectName) {
            showToast(`Generating report for ${projectName}...`, 'info');

            // Simulate report generation
            setTimeout(function() {
                showToast('Report generated successfully!', 'success');
                // In a real implementation, this would trigger a download
            }, 2000);
        }

        function generateTeamReport() {
            showToast('Generating team report...', 'info');

            setTimeout(function() {
                showToast('Team report generated successfully!', 'success');
            }, 2000);
        }

        function openCustomReportBuilder() {
            showToast('Custom report builder coming soon!', 'info');
        }

        function exportProjectData(projectId, format) {
            if (!projectId || !format) {
                showToast('Invalid export parameters.', 'danger');
                return;
            }

            try {
                // Show loading message
                showToast(`Preparing ${format.toUpperCase()} export...`, 'info');

                // Use a simple GET request approach instead of POST to avoid CSRF issues
                const exportUrl = '@Url.Action("ExportData", "Analytics")' +
                                 '?projectId=' + encodeURIComponent(projectId) +
                                 '&format=' + encodeURIComponent(format);

                // Create a temporary link to trigger download
                const link = document.createElement('a');
                link.href = exportUrl;
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Show success message after a delay
                setTimeout(function() {
                    showToast('Export initiated successfully!', 'success');
                }, 1000);

            } catch (error) {
                console.error('Export error:', error);
                showToast('Export failed. Please try again.', 'danger');
            }
        }

        function showToast(message, type) {
            // Create modern toast notification
            const toast = document.createElement('div');
            const typeClasses = {
                'success': 'bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200 border-success-200 dark:border-success-700',
                'info': 'bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200 border-info-200 dark:border-info-700',
                'danger': 'bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200 border-danger-200 dark:border-danger-700',
                'warning': 'bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200 border-warning-200 dark:border-warning-700'
            };

            const icons = {
                'success': 'fas fa-check-circle',
                'info': 'fas fa-info-circle',
                'danger': 'fas fa-exclamation-triangle',
                'warning': 'fas fa-exclamation-circle'
            };

            toast.className = `fixed top-4 right-4 z-50 flex items-center p-4 rounded-lg border shadow-lg transition-all duration-300 transform translate-x-full ${typeClasses[type] || typeClasses.info}`;
            toast.innerHTML = `
                <i class="${icons[type] || icons.info} mr-3"></i>
                <span class="flex-1">${message}</span>
                <button type="button" class="ml-3 text-current hover:opacity-75 transition-opacity" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `;

            document.body.appendChild(toast);

            // Animate in
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 100);

            // Auto remove after 5 seconds
            setTimeout(function() {
                if (toast.parentNode) {
                    toast.classList.add('translate-x-full');
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.parentNode.removeChild(toast);
                        }
                    }, 300);
                }
            }, 5000);
        }
    </script>
}
