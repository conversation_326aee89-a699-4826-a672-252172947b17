@model AttachmentDetailsViewModel
@{
    ViewData["Title"] = "Delete Document";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Delete Document</h2>
        <a asp-action="AttachmentDetails" asp-route-id="@Model.Id" asp-route-type="@Model.AttachmentType" 
           class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-2"></i>Back to Details
        </a>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card border-danger">
                <div class="card-body">
                    <div class="alert alert-danger">
                        <h5 class="alert-heading">
                            <i class="bi bi-exclamation-triangle me-2"></i>Warning!
                        </h5>
                        <p class="mb-0">
                            Are you sure you want to delete this document? This action cannot be undone and will permanently remove:
                        </p>
                        <ul class="mt-2 mb-0">
                            <li>The current document and all its versions</li>
                            <li>All associated metadata (descriptions, tags, etc.)</li>
                            <li>The file from storage</li>
                        </ul>
                    </div>

                    <div class="mt-4">
                        <h5>Document Details</h5>
                        <table class="table">
                            <tr>
                                <th style="width: 150px">Filename:</th>
                                <td>@Model.FileName</td>
                            </tr>
                            <tr>
                                <th>Size:</th>
                                <td>@Model.FormattedFileSize</td>
                            </tr>
                            <tr>
                                <th>Uploaded By:</th>
                                <td>@Model.UploadedByName</td>
                            </tr>
                            <tr>
                                <th>Uploaded On:</th>
                                <td>@Model.UploadedAt.ToString("MMM dd, yyyy HH:mm")</td>
                            </tr>
                            <tr>
                                <th>Category:</th>
                                <td>@(Model.CategoryName ?? "-")</td>
                            </tr>
                            <tr>
                                <th>Versions:</th>
                                <td>@Model.Versions.Count</td>
                            </tr>
                            @if (!string.IsNullOrEmpty(Model.Description))
                            {
                                <tr>
                                    <th>Description:</th>
                                    <td>@Model.Description</td>
                                </tr>
                            }
                            @if (!string.IsNullOrEmpty(Model.Tags))
                            {
                                <tr>
                                    <th>Tags:</th>
                                    <td>@Model.Tags</td>
                                </tr>
                            }
                        </table>
                    </div>

                    <form asp-action="Delete" method="post" class="mt-4">
                        <input type="hidden" name="id" value="@Model.Id" />
                        <input type="hidden" name="type" value="@Model.AttachmentType" />

                        <div class="d-flex justify-content-end gap-2">
                            <a asp-action="AttachmentDetails" asp-route-id="@Model.Id" asp-route-type="@Model.AttachmentType" 
                               class="btn btn-outline-secondary">Cancel</a>
                            <button type="submit" class="btn btn-danger">
                                <i class="bi bi-trash me-2"></i>Delete Document
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Important Notes</h5>
                    <ul class="list-unstyled">
                        <li class="mb-3">
                            <i class="bi bi-exclamation-circle text-warning me-2"></i>
                            This action is irreversible
                        </li>
                        <li class="mb-3">
                            <i class="bi bi-file-earmark-x text-danger me-2"></i>
                            All versions will be deleted
                        </li>
                        <li class="mb-3">
                            <i class="bi bi-link-45deg text-info me-2"></i>
                            Links to this document will no longer work
                        </li>
                        <li>
                            <i class="bi bi-cloud-download text-primary me-2"></i>
                            Consider downloading important files before deletion
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
