using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authorization;
using PM.Tool.Core.Entities.Agile;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Enums;
using PM.Tool.Services;
using PM.Tool.Models.ViewModels;
using PM.Tool.Models.DTOs;

namespace PM.Tool.Controllers
{
    [Authorize]
    public class BugController : SecureBaseController
    {
        private readonly IAgileService _agileService;
        private readonly IProjectService _projectService;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<BugController> _logger;
        private readonly IFormHelperService _formHelper;

        public BugController(
            IAgileService agileService,
            IProjectService projectService,
            UserManager<ApplicationUser> userManager,
            ILogger<BugController> logger,
            IFormHelperService formHelper,
            IAuditService auditService) : base(auditService)
        {
            _agileService = agileService;
            _projectService = projectService;
            _userManager = userManager;
            _logger = logger;
            _formHelper = formHelper;
        }

        #region List and Index

        // GET: Bug
        public async Task<IActionResult> Index(int projectId, int? featureId = null, int? userStoryId = null, 
            BugStatus? status = null, BugSeverity? severity = null, BugPriority? priority = null, 
            string? searchTerm = null, bool? isOverdue = null)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                var bugs = await _agileService.GetProjectBugsAsync(projectId);
                var features = await _agileService.GetProjectFeaturesAsync(projectId);
                var userStories = await _agileService.GetProjectUserStoriesAsync(projectId);

                // Apply filters
                if (featureId.HasValue)
                    bugs = bugs.Where(b => b.FeatureId == featureId.Value);
                
                if (userStoryId.HasValue)
                    bugs = bugs.Where(b => b.UserStoryId == userStoryId.Value);
                
                if (status.HasValue)
                    bugs = bugs.Where(b => b.Status == status.Value);
                
                if (severity.HasValue)
                    bugs = bugs.Where(b => b.Severity == severity.Value);
                
                if (priority.HasValue)
                    bugs = bugs.Where(b => b.Priority == priority.Value);
                
                if (isOverdue.HasValue && isOverdue.Value)
                    bugs = bugs.Where(b => b.IsOverdue);
                
                if (!string.IsNullOrEmpty(searchTerm))
                    bugs = bugs.Where(b => b.Title.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                                          b.Description.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                                          b.BugKey.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));

                var bugSummaries = bugs.Select(b => new BugSummaryDto
                {
                    Id = b.Id,
                    BugKey = b.BugKey,
                    Title = b.Title,
                    Status = b.Status,
                    Severity = b.Severity,
                    Priority = b.Priority,
                    AssignedToName = b.AssignedTo?.UserName,
                    FeatureTitle = b.Feature?.Title,
                    CreatedAt = b.CreatedAt,
                    TargetDate = b.TargetDate,
                    IsOverdue = b.IsOverdue
                });

                var viewModel = new BugListViewModel
                {
                    ProjectId = projectId,
                    ProjectName = project.Name,
                    Bugs = bugSummaries,
                    Features = features,
                    UserStories = userStories,
                    FilterOptions = new BugFilterOptions
                    {
                        FeatureId = featureId,
                        UserStoryId = userStoryId,
                        Status = status,
                        Severity = severity,
                        Priority = priority,
                        SearchTerm = searchTerm,
                        IsOverdue = isOverdue
                    },
                    Metrics = new BugListMetrics
                    {
                        TotalBugs = bugs.Count(),
                        OpenBugs = bugs.Count(b => !b.IsResolved),
                        InProgressBugs = bugs.Count(b => b.Status == BugStatus.InProgress),
                        ResolvedBugs = bugs.Count(b => b.IsResolved),
                        CriticalBugs = bugs.Count(b => b.Severity == BugSeverity.Critical),
                        HighPriorityBugs = bugs.Count(b => b.Priority == BugPriority.High),
                        OverdueBugs = bugs.Count(b => b.IsOverdue),
                        ResolutionRate = bugs.Any() ? (decimal)bugs.Count(b => b.IsResolved) / bugs.Count() * 100 : 0,
                        AverageResolutionTime = bugs.Where(b => b.ResolvedDate.HasValue)
                            .Select(b => (b.ResolvedDate!.Value - b.CreatedAt).TotalDays)
                            .DefaultIfEmpty(0)
                            .Average()
                    }
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading bugs for project {ProjectId}", projectId);
                TempData["Error"] = "An error occurred while loading bugs.";
                return RedirectToAction("Details", "Projects", new { id = projectId });
            }
        }

        #endregion

        #region Details

        // GET: Bug/Details/5
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var bug = await _agileService.GetBugByIdAsync(id);
                if (bug == null) return NotFound();

                var comments = await _agileService.GetBugCommentsAsync(id);

                var viewModel = new BugDetailsViewModel
                {
                    Bug = bug,
                    Comments = comments,
                    Attachments = bug.Attachments,
                    Metrics = new BugMetrics
                    {
                        TimeToResolve = bug.ResolvedDate.HasValue ? bug.ResolvedDate.Value - bug.CreatedAt : null,
                        TimeToVerify = bug.VerifiedDate.HasValue && bug.ResolvedDate.HasValue ? 
                            bug.VerifiedDate.Value - bug.ResolvedDate.Value : null,
                        TotalComments = comments.Count(),
                        TotalAttachments = bug.Attachments.Count(),
                        IsOverdue = bug.IsOverdue,
                        DaysOpen = bug.DaysOpen
                    }
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading bug details for {BugId}", id);
                TempData["Error"] = "An error occurred while loading bug details.";
                return RedirectToAction("Index");
            }
        }

        #endregion

        #region Create

        // GET: Bug/Create
        public async Task<IActionResult> Create(int projectId, int? featureId = null, int? userStoryId = null)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                var viewModel = new BugCreateViewModel
                {
                    ProjectId = projectId,
                    FeatureId = featureId,
                    UserStoryId = userStoryId
                };

                await PopulateDropdowns(projectId);
                ViewBag.Project = project;

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading create bug form for project {ProjectId}", projectId);
                TempData["Error"] = "An error occurred while loading the create form.";
                return RedirectToAction("Index", new { projectId });
            }
        }

        // POST: Bug/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(BugCreateViewModel viewModel)
        {
            return await this.HandleCreateAsync<BugCreateViewModel, Bug>(
                viewModel,
                async (bug) => {
                    var user = await _userManager.GetUserAsync(User);
                    bug.ReportedByUserId = user?.Id;

                    // Generate bug key
                    var existingBugs = await _agileService.GetProjectBugsAsync(bug.ProjectId);
                    bug.BugKey = $"BG-{existingBugs.Count() + 1:D3}";

                    var createdBug = await _agileService.CreateBugAsync(bug);
                    await LogAuditAsync(AuditAction.Create, "Bug", createdBug.Id);
                    return createdBug;
                },
                bug => bug.Id,
                async () => await PopulateDropdowns(viewModel.ProjectId),
                _formHelper,
                _logger,
                "Bug",
                "Details"
            );
        }

        #endregion

        #region Edit

        // GET: Bug/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                var bug = await _agileService.GetBugByIdAsync(id);
                if (bug == null) return NotFound();

                var viewModel = BugEditViewModel.FromEntity(bug);
                await PopulateDropdowns(bug.ProjectId);
                ViewBag.Project = await _projectService.GetProjectByIdAsync(bug.ProjectId);

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit form for bug {BugId}", id);
                TempData["Error"] = "An error occurred while loading the edit form.";
                return RedirectToAction("Details", new { id });
            }
        }

        // POST: Bug/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, BugEditViewModel viewModel)
        {
            if (id != viewModel.Id) return NotFound();

            return await this.HandleUpdateAsync<BugEditViewModel, Bug>(
                id,
                viewModel,
                async (bugId) => await _agileService.GetBugByIdAsync(bugId),
                async (bug) => {
                    var updatedBug = await _agileService.UpdateBugAsync(bug);
                    await LogAuditAsync(AuditAction.Update, "Bug", bug.Id);
                    return updatedBug;
                },
                async () => await PopulateDropdowns(viewModel.ProjectId),
                _formHelper,
                _logger,
                "Bug",
                "Details"
            );
        }

        #endregion

        #region Delete

        // GET: Bug/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var bug = await _agileService.GetBugByIdAsync(id);
                if (bug == null) return NotFound();

                return View(bug);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading delete confirmation for bug {BugId}", id);
                TempData["Error"] = "An error occurred while loading the delete confirmation.";
                return RedirectToAction("Details", new { id });
            }
        }

        // POST: Bug/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var bug = await _agileService.GetBugByIdAsync(id);
                if (bug == null) return NotFound();

                var projectId = bug.ProjectId;
                await _agileService.DeleteBugAsync(id);
                await LogAuditAsync(AuditAction.Delete, "Bug", id);

                TempData["Success"] = "Bug deleted successfully.";
                return RedirectToAction("Index", new { projectId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting bug {BugId}", id);
                TempData["Error"] = "An error occurred while deleting the bug.";
                return RedirectToAction("Details", new { id });
            }
        }

        #endregion

        #region Comments

        // POST: Bug/AddComment
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddComment(BugCommentCreateViewModel viewModel)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var user = await _userManager.GetUserAsync(User);
                    if (user != null)
                    {
                        await _agileService.AddBugCommentAsync(viewModel.BugId, user.Id, viewModel.Content, viewModel.Type);
                        await LogAuditAsync(AuditAction.Create, "BugComment", viewModel.BugId);
                        TempData["Success"] = "Comment added successfully.";
                    }
                }
                else
                {
                    TempData["Error"] = "Please provide a valid comment.";
                }

                return RedirectToAction("Details", new { id = viewModel.BugId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding comment to bug {BugId}", viewModel.BugId);
                TempData["Error"] = "An error occurred while adding the comment.";
                return RedirectToAction("Details", new { id = viewModel.BugId });
            }
        }

        #endregion

        #region Helper Methods

        private async Task PopulateDropdowns(int projectId)
        {
            var features = await _agileService.GetProjectFeaturesAsync(projectId);
            var userStories = await _agileService.GetProjectUserStoriesAsync(projectId);
            
            ViewBag.FeatureId = new SelectList(features, "Id", "Title");
            ViewBag.UserStoryId = new SelectList(userStories, "Id", "Title");
            
            // Add enum dropdowns
            ViewBag.Severity = new SelectList(Enum.GetValues<BugSeverity>());
            ViewBag.Priority = new SelectList(Enum.GetValues<BugPriority>());
            ViewBag.Status = new SelectList(Enum.GetValues<BugStatus>());
        }

        #endregion
    }
}
