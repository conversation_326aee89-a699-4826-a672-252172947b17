using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Tests.Integration.Infrastructure;
using System.Net;
using System.Net.Http.Json;
using System.Text;
using Xunit;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace PM.Tool.Tests.Integration.Controllers
{
    [Collection("Integration")]
    public class ProjectsControllerIntegrationTests : IntegrationTestBase
    {
        public ProjectsControllerIntegrationTests(CustomWebApplicationFactory factory) : base(factory)
        {
        }

        [Fact]
        public async Task Index_WithExistingProjects_ReturnsProjectsFromDatabase()
        {
            // Arrange
            await ClearDatabaseAsync();
            var project1 = await CreateTestProjectAsync("Project 1");
            var project2 = await CreateTestProjectAsync("Project 2");

            // Act
            var response = await _client.GetAsync("/Projects");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            var content = await response.Content.ReadAsStringAsync();
            content.Should().Contain("Project 1");
            content.Should().Contain("Project 2");
        }

        [Fact]
        public async Task Details_WithValidProjectId_ReturnsProjectDetails()
        {
            // Arrange
            await ClearDatabaseAsync();
            var project = await CreateTestProjectAsync("Test Project Details");
            var task = await CreateTestTaskAsync(project.Id, "Test Task");

            // Act
            var response = await _client.GetAsync($"/Projects/Details/{project.Id}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            var content = await response.Content.ReadAsStringAsync();
            content.Should().Contain("Test Project Details");
            content.Should().Contain("Test Task");
        }

        [Fact]
        public async Task Details_WithNonExistentProject_ReturnsNotFound()
        {
            // Arrange
            await ClearDatabaseAsync();
            var nonExistentId = 99999;

            // Act
            var response = await _client.GetAsync($"/Projects/Details/{nonExistentId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task Create_Get_ReturnsCreateForm()
        {
            // Act
            var response = await _client.GetAsync("/Projects/Create");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            var content = await response.Content.ReadAsStringAsync();
            content.Should().Contain("Create Project");
        }

        [Fact]
        public async Task Create_Post_WithValidProject_SavesProjectToDatabase()
        {
            // Arrange
            await ClearDatabaseAsync();
            var formData = new Dictionary<string, string>
            {
                ["Name"] = "Integration Test Project",
                ["Description"] = "This is an integration test project",
                ["StartDate"] = DateTime.Today.ToString("yyyy-MM-dd"),
                ["EndDate"] = DateTime.Today.AddMonths(6).ToString("yyyy-MM-dd"),
                ["Status"] = ((int)ProjectStatus.Active).ToString()
            };

            var formContent = new FormUrlEncodedContent(formData);

            // Act
            var response = await _client.PostAsync("/Projects/Create", formContent);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Redirect);

            // Verify project was saved to database
            var savedProject = await _context.Projects
                .FirstOrDefaultAsync(p => p.Name == "Integration Test Project");

            savedProject.Should().NotBeNull();
            savedProject!.Description.Should().Be("This is an integration test project");
            savedProject.CreatedByUserId.Should().Be(_testUser.Id);
            savedProject.ManagerId.Should().Be(_testUser.Id);
            savedProject.Status.Should().Be(ProjectStatus.Active);
        }

        [Fact]
        public async Task Create_Post_WithInvalidProject_ReturnsFormWithErrors()
        {
            // Arrange
            var formData = new Dictionary<string, string>
            {
                ["Name"] = "", // Invalid - empty name
                ["Description"] = "Test description",
                ["StartDate"] = DateTime.Today.ToString("yyyy-MM-dd"),
                ["EndDate"] = DateTime.Today.AddMonths(6).ToString("yyyy-MM-dd")
            };

            var formContent = new FormUrlEncodedContent(formData);

            // Act
            var response = await _client.PostAsync("/Projects/Create", formContent);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK); // Returns form with validation errors
            var content = await response.Content.ReadAsStringAsync();
            content.Should().Contain("field is required"); // Validation error message
        }

        [Fact]
        public async Task Edit_Get_WithValidProject_ReturnsEditForm()
        {
            // Arrange
            await ClearDatabaseAsync();
            var project = await CreateTestProjectAsync("Project to Edit");

            // Act
            var response = await _client.GetAsync($"/Projects/Edit/{project.Id}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            var content = await response.Content.ReadAsStringAsync();
            content.Should().Contain("Project to Edit");
            content.Should().Contain("Edit Project");
        }

        [Fact]
        public async Task Edit_Post_WithValidChanges_UpdatesProjectInDatabase()
        {
            // Arrange
            await ClearDatabaseAsync();
            var project = await CreateTestProjectAsync("Original Project Name");

            var formData = new Dictionary<string, string>
            {
                ["Id"] = project.Id.ToString(),
                ["Name"] = "Updated Project Name",
                ["Description"] = "Updated description",
                ["StartDate"] = project.StartDate.ToString("yyyy-MM-dd"),
                ["EndDate"] = project.EndDate?.ToString("yyyy-MM-dd") ?? "",
                ["Status"] = ((int)ProjectStatus.Completed).ToString()
            };

            var formContent = new FormUrlEncodedContent(formData);

            // Act
            var response = await _client.PostAsync($"/Projects/Edit/{project.Id}", formContent);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Redirect);

            // Verify project was updated in database
            var updatedProject = await _context.Projects.FindAsync(project.Id);
            updatedProject.Should().NotBeNull();
            updatedProject!.Name.Should().Be("Updated Project Name");
            updatedProject.Description.Should().Be("Updated description");
            updatedProject.Status.Should().Be(ProjectStatus.Completed);
        }

        [Fact]
        public async Task Delete_WithValidProject_RemovesProjectFromDatabase()
        {
            // Arrange
            await ClearDatabaseAsync();
            var project = await CreateTestProjectAsync("Project to Delete");

            var formData = new Dictionary<string, string>();
            var formContent = new FormUrlEncodedContent(formData);

            // Act
            var response = await _client.PostAsync($"/Projects/Delete/{project.Id}", formContent);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Redirect);

            // Verify project was deleted from database
            var deletedProject = await _context.Projects.FindAsync(project.Id);
            deletedProject.Should().BeNull();
        }

        [Fact]
        public async Task Archive_WithValidProject_UpdatesProjectStatusInDatabase()
        {
            // Arrange
            await ClearDatabaseAsync();
            var project = await CreateTestProjectAsync("Project to Archive");

            var formData = new Dictionary<string, string>();
            var formContent = new FormUrlEncodedContent(formData);

            // Act
            var response = await _client.PostAsync($"/Projects/Archive/{project.Id}", formContent);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Redirect);

            // Verify project status was updated in database
            var archivedProject = await _context.Projects.FindAsync(project.Id);
            archivedProject.Should().NotBeNull();
            archivedProject!.Status.Should().Be(ProjectStatus.Cancelled);
        }

        [Fact]
        public async Task GetProjectTasks_WithValidProject_ReturnsTasksAsJson()
        {
            // Arrange
            await ClearDatabaseAsync();
            var project = await CreateTestProjectAsync("Project with Tasks");
            var task1 = await CreateTestTaskAsync(project.Id, "Task 1");
            var task2 = await CreateTestTaskAsync(project.Id, "Task 2");

            // Act
            var response = await _client.GetAsync($"/Projects/GetProjectTasks/{project.Id}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            response.Content.Headers.ContentType?.MediaType.Should().Be("application/json");

            var jsonContent = await response.Content.ReadAsStringAsync();
            var tasks = JsonConvert.DeserializeObject<List<dynamic>>(jsonContent);
            tasks.Should().HaveCount(2);
        }

        [Fact]
        public async Task CompleteWorkflow_CreateProjectWithTasksAndRequirements_WorksEndToEnd()
        {
            // Arrange
            await ClearDatabaseAsync();

            // 1. Create Project
            var projectFormData = new Dictionary<string, string>
            {
                ["Name"] = "Complete Workflow Project",
                ["Description"] = "End-to-end integration test project",
                ["StartDate"] = DateTime.Today.ToString("yyyy-MM-dd"),
                ["EndDate"] = DateTime.Today.AddMonths(3).ToString("yyyy-MM-dd"),
                ["Status"] = ((int)ProjectStatus.Active).ToString()
            };

            var projectResponse = await _client.PostAsync("/Projects/Create",
                new FormUrlEncodedContent(projectFormData));
            projectResponse.StatusCode.Should().Be(HttpStatusCode.Redirect);

            // Verify project was created
            var createdProject = await _context.Projects
                .FirstOrDefaultAsync(p => p.Name == "Complete Workflow Project");
            createdProject.Should().NotBeNull();

            // 2. Add Task to Project
            var task = await CreateTestTaskAsync(createdProject!.Id, "Integration Test Task");

            // 3. Add Requirement to Project
            var requirement = await CreateTestRequirementAsync(createdProject.Id, "Integration Test Requirement");

            // 4. Verify Project Details shows both Task and Requirement
            var detailsResponse = await _client.GetAsync($"/Projects/Details/{createdProject.Id}");
            detailsResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            var detailsContent = await detailsResponse.Content.ReadAsStringAsync();
            detailsContent.Should().Contain("Complete Workflow Project");
            detailsContent.Should().Contain("Integration Test Task");

            // 5. Complete the project
            var completeFormData = new Dictionary<string, string>
            {
                ["Id"] = createdProject.Id.ToString(),
                ["Name"] = createdProject.Name,
                ["Description"] = createdProject.Description,
                ["StartDate"] = createdProject.StartDate.ToString("yyyy-MM-dd"),
                ["EndDate"] = createdProject.EndDate?.ToString("yyyy-MM-dd") ?? "",
                ["Status"] = ((int)ProjectStatus.Completed).ToString()
            };

            var completeResponse = await _client.PostAsync($"/Projects/Edit/{createdProject.Id}",
                new FormUrlEncodedContent(completeFormData));
            completeResponse.StatusCode.Should().Be(HttpStatusCode.Redirect);

            // Verify final state
            var finalProject = await _context.Projects
                .Include(p => p.Tasks)
                .FirstOrDefaultAsync(p => p.Id == createdProject.Id);

            var projectRequirements = await _context.Requirements
                .Where(r => r.ProjectId == createdProject.Id)
                .ToListAsync();

            finalProject.Should().NotBeNull();
            finalProject!.Status.Should().Be(ProjectStatus.Completed);
            finalProject.Tasks.Should().HaveCount(1);
            projectRequirements.Should().HaveCount(1);
        }
    }
}
