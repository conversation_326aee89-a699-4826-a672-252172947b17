using System;
using System.Collections.Generic;
using System.Collections.Generic;

namespace PM.Tool.Models.ViewModels
{
    public class ProductivityMetricsViewModel
    {
        public int TotalTasksCompleted { get; set; }
        public double TotalTimeSpent { get; set; }
        public List<UserProductivityData> UserProductivityData { get; set; } = new();
        public List<TeamMemberMetrics> TeamMembers { get; set; } = new();
        public double AverageTasksPerMember { get; set; }
        public double AverageEfficiencyRatio { get; set; }
        public Dictionary<string, double> MemberEfficiencyRatios { get; set; } = new();
    }
}
