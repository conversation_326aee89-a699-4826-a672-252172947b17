2025-06-30 11:19:45.937 +06:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-30 11:19:51.357 +06:00 [INF] Now listening on: https://localhost:7029
2025-06-30 11:19:51.359 +06:00 [INF] Now listening on: http://localhost:5045
2025-06-30 11:19:51.537 +06:00 [INF] Executed DbCommand (131ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE t."DueDate" IS NOT NULL AND t."DueDate" <= @__upcomingDeadlines_0 AND t."DueDate" > now() AND t."Status" <> 4
2025-06-30 11:19:51.593 +06:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-30 11:19:51.595 +06:00 [INF] Hosting environment: Development
2025-06-30 11:19:51.596 +06:00 [INF] Content root path: D:\TGI\PM.Tool\PM.Tool
2025-06-30 11:19:52.719 +06:00 [INF] Executed DbCommand (21ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?', @p2='?' (DbType = DateTime), @p3='?' (DbType = Int32), @p4='?' (DbType = Int32), @p5='?', @p6='?' (DbType = Int32), @p7='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Notifications" ("CreatedAt", "Message", "ReadAt", "RelatedProjectId", "RelatedTaskId", "Title", "Type", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "IsEmailSent", "IsRead";
2025-06-30 11:19:52.870 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", p0."Id", p0."IsActive", p0."JoinedAt", p0."ProjectId", p0."Role", p0."UserId"
FROM "Projects" AS p
LEFT JOIN "ProjectMembers" AS p0 ON p."Id" = p0."ProjectId"
WHERE p."EndDate" IS NOT NULL AND p."EndDate" <= @__upcomingDeadlines_0 AND p."EndDate" > now() AND p."Status" <> 5
ORDER BY p."Id"
2025-06-30 11:19:52.876 +06:00 [INF] Deadline check completed at: "2025-06-30T11:19:52.8758593+06:00"
2025-06-30 11:19:54.852 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/ - null null
2025-06-30 11:19:55.506 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-30 11:19:55.545 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__user_Id_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ClaimType", a."ClaimValue", a."UserId"
FROM "AspNetUserClaims" AS a
WHERE a."UserId" = @__user_Id_0
2025-06-30 11:19:55.576 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a0."Name"
FROM "AspNetUserRoles" AS a
INNER JOIN "AspNetRoles" AS a0 ON a."RoleId" = a0."Id"
WHERE a."UserId" = @__userId_0
2025-06-30 11:19:55.602 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-30 11:19:55.642 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__role_Id_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."ClaimType", a."ClaimValue"
FROM "AspNetRoleClaims" AS a
WHERE a."RoleId" = @__role_Id_0
2025-06-30 11:19:55.673 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-30 11:19:55.700 +06:00 [INF] Route matched with {action = "Index", controller = "Home", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-30 11:19:55.802 +06:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-30 11:19:55.844 +06:00 [INF] Executed DbCommand (13ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-30 11:19:55.929 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-30 11:19:55.974 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" < now()
ORDER BY t."DueDate"
2025-06-30 11:19:56.009 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__dueDate_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
FROM "Tasks" AS t
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" <= @__dueDate_0
2025-06-30 11:19:56.070 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT n."Id", n."Title", n."Message", n."Type", n."IsRead", n."CreatedAt", n."RelatedProjectId", n."RelatedTaskId"
FROM "Notifications" AS n
WHERE n."UserId" = @__user_Id_0
ORDER BY n."CreatedAt" DESC
LIMIT @__p_1
2025-06-30 11:19:56.134 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."Name", m."Description", m."ProjectId", p."Name", m."DueDate", m."CompletedDate", m."IsCompleted", m."CreatedAt", m."IsDeleted", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."StartDate", p."Status", p."UpdatedAt"
FROM "Milestones" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
WHERE EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__user_Id_0 AND p0."IsActive") AND NOT (m."IsCompleted") AND m."DueDate" >= now()
ORDER BY m."DueDate"
LIMIT @__p_1
2025-06-30 11:19:56.169 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-30 11:19:56.606 +06:00 [INF] Executed ViewResult - view Index executed in 444.9079ms.
2025-06-30 11:19:56.615 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/js/site.js?v=gfa0xw_jEM_w5r3HMCk1LwRfuj7T6ktfvUiYt5ZeuS4 - null null
2025-06-30 11:19:56.614 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/js/tailwind-theme-manager.js?v=QS9OsoodixBTZAmxGj2gDzPtp60VU7kbda0nOueg4T0 - null null
2025-06-30 11:19:56.614 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/lib/jquery/dist/jquery.min.js - null null
2025-06-30 11:19:56.614 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/css/tailwind.css?v=Vk1BkLOsT7-Yh4o1pDln8tZyZ3_rFi9yXuRHMX2g50E - null null
2025-06-30 11:19:56.614 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/css/select2-tailwind.css?v=2NhFN86Hz32C7Wo7TUbxJ3KO6-yNfjicKVTfnVWnK0U - null null
2025-06-30 11:19:56.660 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/js/select2-init.js?v=aKGb97ISwne7Mb1h1Tg8jyeYLi2LamRm54wdgjuWBrs - null null
2025-06-30 11:19:56.731 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:19:56.739 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.Index (PM.Tool) in 1032.8253ms
2025-06-30 11:19:56.752 +06:00 [INF] The file /js/tailwind-theme-manager.js was not modified
2025-06-30 11:19:56.774 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-30 11:19:56.753 +06:00 [INF] The file /js/site.js was not modified
2025-06-30 11:19:56.757 +06:00 [INF] The file /css/tailwind.css was not modified
2025-06-30 11:19:56.762 +06:00 [INF] The file /css/select2-tailwind.css was not modified
2025-06-30 11:19:56.764 +06:00 [INF] The file /js/select2-init.js was not modified
2025-06-30 11:19:56.752 +06:00 [INF] The file /lib/jquery/dist/jquery.min.js was not modified
2025-06-30 11:19:56.780 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/js/site.js?v=gfa0xw_jEM_w5r3HMCk1LwRfuj7T6ktfvUiYt5ZeuS4 - 304 null text/javascript 165.1531ms
2025-06-30 11:19:56.780 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 48.2988ms
2025-06-30 11:19:56.780 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/js/tailwind-theme-manager.js?v=QS9OsoodixBTZAmxGj2gDzPtp60VU7kbda0nOueg4T0 - 304 null text/javascript 165.9608ms
2025-06-30 11:19:56.782 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/css/tailwind.css?v=Vk1BkLOsT7-Yh4o1pDln8tZyZ3_rFi9yXuRHMX2g50E - 304 null text/css 167.6871ms
2025-06-30 11:19:56.784 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/css/select2-tailwind.css?v=2NhFN86Hz32C7Wo7TUbxJ3KO6-yNfjicKVTfnVWnK0U - 304 null text/css 169.7543ms
2025-06-30 11:19:56.786 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/js/select2-init.js?v=aKGb97ISwne7Mb1h1Tg8jyeYLi2LamRm54wdgjuWBrs - 304 null text/javascript 126.0588ms
2025-06-30 11:19:56.787 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/lib/jquery/dist/jquery.min.js - 304 null text/javascript 172.9342ms
2025-06-30 11:19:56.788 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/ - 200 null text/html; charset=utf-8 1952.32ms
2025-06-30 11:19:56.791 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:19:57.054 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 263.2025ms
2025-06-30 11:21:03.341 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Home/SetLanguage?culture=bn-BD&returnUrl=%2F - null null
2025-06-30 11:21:03.405 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.SetLanguage (PM.Tool)'
2025-06-30 11:21:03.418 +06:00 [INF] Route matched with {action = "SetLanguage", controller = "Home", page = "", area = ""}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult SetLanguage(System.String, System.String) on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-30 11:21:03.597 +06:00 [INF] Language set to bn-BD, Cookie: c=bn-BD|uic=bn-BD
2025-06-30 11:21:03.605 +06:00 [INF] Executing RedirectResult, redirecting to /.
2025-06-30 11:21:03.609 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.SetLanguage (PM.Tool) in 186.1692ms
2025-06-30 11:21:03.610 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.SetLanguage (PM.Tool)'
2025-06-30 11:21:03.612 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Home/SetLanguage?culture=bn-BD&returnUrl=%2F - 302 0 null 270.7987ms
2025-06-30 11:21:03.617 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/ - null null
2025-06-30 11:21:03.665 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-30 11:21:03.670 +06:00 [INF] Route matched with {action = "Index", controller = "Home", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-30 11:21:03.699 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-30 11:21:03.720 +06:00 [INF] Executed DbCommand (11ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-30 11:21:03.743 +06:00 [INF] Executed DbCommand (10ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-30 11:21:03.761 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" < now()
ORDER BY t."DueDate"
2025-06-30 11:21:03.776 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__dueDate_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
FROM "Tasks" AS t
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" <= @__dueDate_0
2025-06-30 11:21:03.796 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT n."Id", n."Title", n."Message", n."Type", n."IsRead", n."CreatedAt", n."RelatedProjectId", n."RelatedTaskId"
FROM "Notifications" AS n
WHERE n."UserId" = @__user_Id_0
ORDER BY n."CreatedAt" DESC
LIMIT @__p_1
2025-06-30 11:21:03.813 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."Name", m."Description", m."ProjectId", p."Name", m."DueDate", m."CompletedDate", m."IsCompleted", m."CreatedAt", m."IsDeleted", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."StartDate", p."Status", p."UpdatedAt"
FROM "Milestones" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
WHERE EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__user_Id_0 AND p0."IsActive") AND NOT (m."IsCompleted") AND m."DueDate" >= now()
ORDER BY m."DueDate"
LIMIT @__p_1
2025-06-30 11:21:03.823 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-30 11:21:03.841 +06:00 [INF] Executed ViewResult - view Index executed in 17.9739ms.
2025-06-30 11:21:03.845 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.Index (PM.Tool) in 170.263ms
2025-06-30 11:21:03.847 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-30 11:21:03.849 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/ - 200 null text/html; charset=utf-8 231.675ms
2025-06-30 11:21:03.856 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:21:03.857 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:21:03.865 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 9.1917ms
2025-06-30 11:21:03.881 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 24.5078ms
2025-06-30 11:21:08.982 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Home/SetLanguage?culture=en-GB&returnUrl=%2F - null null
2025-06-30 11:21:08.994 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.SetLanguage (PM.Tool)'
2025-06-30 11:21:08.997 +06:00 [INF] Route matched with {action = "SetLanguage", controller = "Home", page = "", area = ""}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult SetLanguage(System.String, System.String) on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-30 11:21:09.003 +06:00 [INF] Language set to en-GB, Cookie: c=en-GB|uic=en-GB
2025-06-30 11:21:09.006 +06:00 [INF] Executing RedirectResult, redirecting to /.
2025-06-30 11:21:09.008 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.SetLanguage (PM.Tool) in 7.5157ms
2025-06-30 11:21:09.010 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.SetLanguage (PM.Tool)'
2025-06-30 11:21:09.012 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Home/SetLanguage?culture=en-GB&returnUrl=%2F - 302 0 null 30.1567ms
2025-06-30 11:21:09.016 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/ - null null
2025-06-30 11:21:09.023 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-30 11:21:09.025 +06:00 [INF] Route matched with {action = "Index", controller = "Home", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-30 11:21:09.033 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-30 11:21:09.047 +06:00 [INF] Executed DbCommand (11ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-30 11:21:09.058 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-30 11:21:09.074 +06:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" < now()
ORDER BY t."DueDate"
2025-06-30 11:21:09.083 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__dueDate_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
FROM "Tasks" AS t
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" <= @__dueDate_0
2025-06-30 11:21:09.092 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT n."Id", n."Title", n."Message", n."Type", n."IsRead", n."CreatedAt", n."RelatedProjectId", n."RelatedTaskId"
FROM "Notifications" AS n
WHERE n."UserId" = @__user_Id_0
ORDER BY n."CreatedAt" DESC
LIMIT @__p_1
2025-06-30 11:21:09.101 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."Name", m."Description", m."ProjectId", p."Name", m."DueDate", m."CompletedDate", m."IsCompleted", m."CreatedAt", m."IsDeleted", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."StartDate", p."Status", p."UpdatedAt"
FROM "Milestones" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
WHERE EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__user_Id_0 AND p0."IsActive") AND NOT (m."IsCompleted") AND m."DueDate" >= now()
ORDER BY m."DueDate"
LIMIT @__p_1
2025-06-30 11:21:09.109 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-30 11:21:09.214 +06:00 [INF] Executed ViewResult - view Index executed in 105.3367ms.
2025-06-30 11:21:09.216 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.Index (PM.Tool) in 188.7141ms
2025-06-30 11:21:09.217 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-30 11:21:09.219 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/ - 200 null text/html; charset=utf-8 203.5257ms
2025-06-30 11:21:09.226 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:21:09.226 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:21:09.241 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 14.8185ms
2025-06-30 11:21:09.244 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 18.2719ms
2025-06-30 11:21:14.265 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Home/SetLanguage?culture=en-US&returnUrl=%2F - null null
2025-06-30 11:21:14.272 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.SetLanguage (PM.Tool)'
2025-06-30 11:21:14.274 +06:00 [INF] Route matched with {action = "SetLanguage", controller = "Home", page = "", area = ""}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult SetLanguage(System.String, System.String) on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-30 11:21:14.276 +06:00 [INF] Language set to en-US, Cookie: c=en-US|uic=en-US
2025-06-30 11:21:14.278 +06:00 [INF] Executing RedirectResult, redirecting to /.
2025-06-30 11:21:14.280 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.SetLanguage (PM.Tool) in 4.1739ms
2025-06-30 11:21:14.282 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.SetLanguage (PM.Tool)'
2025-06-30 11:21:14.283 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Home/SetLanguage?culture=en-US&returnUrl=%2F - 302 0 null 18.1981ms
2025-06-30 11:21:14.287 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/ - null null
2025-06-30 11:21:14.292 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-30 11:21:14.294 +06:00 [INF] Route matched with {action = "Index", controller = "Home", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-30 11:21:14.303 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-30 11:21:14.317 +06:00 [INF] Executed DbCommand (10ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-30 11:21:14.329 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-30 11:21:14.338 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" < now()
ORDER BY t."DueDate"
2025-06-30 11:21:14.347 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__dueDate_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
FROM "Tasks" AS t
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" <= @__dueDate_0
2025-06-30 11:21:14.357 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT n."Id", n."Title", n."Message", n."Type", n."IsRead", n."CreatedAt", n."RelatedProjectId", n."RelatedTaskId"
FROM "Notifications" AS n
WHERE n."UserId" = @__user_Id_0
ORDER BY n."CreatedAt" DESC
LIMIT @__p_1
2025-06-30 11:21:14.367 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."Name", m."Description", m."ProjectId", p."Name", m."DueDate", m."CompletedDate", m."IsCompleted", m."CreatedAt", m."IsDeleted", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."StartDate", p."Status", p."UpdatedAt"
FROM "Milestones" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
WHERE EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__user_Id_0 AND p0."IsActive") AND NOT (m."IsCompleted") AND m."DueDate" >= now()
ORDER BY m."DueDate"
LIMIT @__p_1
2025-06-30 11:21:14.374 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-30 11:21:14.384 +06:00 [INF] Executed ViewResult - view Index executed in 10.8108ms.
2025-06-30 11:21:14.386 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.Index (PM.Tool) in 89.538ms
2025-06-30 11:21:14.389 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-30 11:21:14.392 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/ - 200 null text/html; charset=utf-8 104.6689ms
2025-06-30 11:21:14.396 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:21:14.396 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:21:14.403 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 6.6586ms
2025-06-30 11:21:14.411 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 15.3245ms
2025-06-30 11:21:19.213 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Home/SetLanguage?culture=it-IT&returnUrl=%2F - null null
2025-06-30 11:21:19.223 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.SetLanguage (PM.Tool)'
2025-06-30 11:21:19.225 +06:00 [INF] Route matched with {action = "SetLanguage", controller = "Home", page = "", area = ""}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult SetLanguage(System.String, System.String) on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-30 11:21:19.230 +06:00 [INF] Language set to it-IT, Cookie: c=it-IT|uic=it-IT
2025-06-30 11:21:19.233 +06:00 [INF] Executing RedirectResult, redirecting to /.
2025-06-30 11:21:19.235 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.SetLanguage (PM.Tool) in 6.6315ms
2025-06-30 11:21:19.238 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.SetLanguage (PM.Tool)'
2025-06-30 11:21:19.241 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Home/SetLanguage?culture=it-IT&returnUrl=%2F - 302 0 null 27.6418ms
2025-06-30 11:21:19.245 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/ - null null
2025-06-30 11:21:19.253 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-30 11:21:19.256 +06:00 [INF] Route matched with {action = "Index", controller = "Home", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-30 11:21:19.266 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-30 11:21:19.284 +06:00 [INF] Executed DbCommand (11ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-30 11:21:19.317 +06:00 [INF] Executed DbCommand (26ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-30 11:21:19.327 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" < now()
ORDER BY t."DueDate"
2025-06-30 11:21:19.335 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__dueDate_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
FROM "Tasks" AS t
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" <= @__dueDate_0
2025-06-30 11:21:19.344 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT n."Id", n."Title", n."Message", n."Type", n."IsRead", n."CreatedAt", n."RelatedProjectId", n."RelatedTaskId"
FROM "Notifications" AS n
WHERE n."UserId" = @__user_Id_0
ORDER BY n."CreatedAt" DESC
LIMIT @__p_1
2025-06-30 11:21:19.352 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."Name", m."Description", m."ProjectId", p."Name", m."DueDate", m."CompletedDate", m."IsCompleted", m."CreatedAt", m."IsDeleted", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."StartDate", p."Status", p."UpdatedAt"
FROM "Milestones" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
WHERE EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__user_Id_0 AND p0."IsActive") AND NOT (m."IsCompleted") AND m."DueDate" >= now()
ORDER BY m."DueDate"
LIMIT @__p_1
2025-06-30 11:21:19.359 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-30 11:21:19.437 +06:00 [INF] Executed ViewResult - view Index executed in 79.9257ms.
2025-06-30 11:21:19.439 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.Index (PM.Tool) in 180.0061ms
2025-06-30 11:21:19.442 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-30 11:21:19.444 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/ - 200 null text/html; charset=utf-8 199.1919ms
2025-06-30 11:21:19.450 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:21:19.450 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:21:19.456 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 6.7493ms
2025-06-30 11:21:19.474 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 24.8158ms
2025-06-30 11:21:29.617 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Home/SetLanguage?culture=en-US&returnUrl=%2F - null null
2025-06-30 11:21:29.625 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.SetLanguage (PM.Tool)'
2025-06-30 11:21:29.628 +06:00 [INF] Route matched with {action = "SetLanguage", controller = "Home", page = "", area = ""}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult SetLanguage(System.String, System.String) on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-30 11:21:29.631 +06:00 [INF] Language set to en-US, Cookie: c=en-US|uic=en-US
2025-06-30 11:21:29.633 +06:00 [INF] Executing RedirectResult, redirecting to /.
2025-06-30 11:21:29.635 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.SetLanguage (PM.Tool) in 4.8478ms
2025-06-30 11:21:29.637 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.SetLanguage (PM.Tool)'
2025-06-30 11:21:29.639 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Home/SetLanguage?culture=en-US&returnUrl=%2F - 302 0 null 22.6332ms
2025-06-30 11:21:29.643 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/ - null null
2025-06-30 11:21:29.652 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-30 11:21:29.653 +06:00 [INF] Route matched with {action = "Index", controller = "Home", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-30 11:21:29.662 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-30 11:21:29.700 +06:00 [INF] Executed DbCommand (31ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-30 11:21:29.717 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-30 11:21:29.748 +06:00 [INF] Executed DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" < now()
ORDER BY t."DueDate"
2025-06-30 11:21:29.761 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__dueDate_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
FROM "Tasks" AS t
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" <= @__dueDate_0
2025-06-30 11:21:29.772 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT n."Id", n."Title", n."Message", n."Type", n."IsRead", n."CreatedAt", n."RelatedProjectId", n."RelatedTaskId"
FROM "Notifications" AS n
WHERE n."UserId" = @__user_Id_0
ORDER BY n."CreatedAt" DESC
LIMIT @__p_1
2025-06-30 11:21:29.782 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."Name", m."Description", m."ProjectId", p."Name", m."DueDate", m."CompletedDate", m."IsCompleted", m."CreatedAt", m."IsDeleted", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."StartDate", p."Status", p."UpdatedAt"
FROM "Milestones" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
WHERE EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__user_Id_0 AND p0."IsActive") AND NOT (m."IsCompleted") AND m."DueDate" >= now()
ORDER BY m."DueDate"
LIMIT @__p_1
2025-06-30 11:21:29.786 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-30 11:21:29.792 +06:00 [INF] Executed ViewResult - view Index executed in 6.0597ms.
2025-06-30 11:21:29.795 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.Index (PM.Tool) in 139.1917ms
2025-06-30 11:21:29.798 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-30 11:21:29.799 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/ - 200 null text/html; charset=utf-8 155.9695ms
2025-06-30 11:21:29.803 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:21:29.803 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:21:29.807 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 4.26ms
2025-06-30 11:21:29.814 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 11.1069ms
2025-06-30 11:22:12.864 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Tasks/Details/2 - null null
2025-06-30 11:22:12.891 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.TasksController.Details (PM.Tool)'
2025-06-30 11:22:12.898 +06:00 [INF] Route matched with {action = "Details", controller = "Tasks", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Details(Int32) on controller PM.Tool.Controllers.TasksController (PM.Tool).
2025-06-30 11:22:12.917 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-30 11:22:12.954 +06:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-30 11:22:13.008 +06:00 [INF] Executed DbCommand (18ms) [Parameters=[@__taskId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", t0."Id", t0."Content", t0."CreatedAt", t0."TaskId", t0."UpdatedAt", t0."UserId", t1."Id", t1."CategoryId", t1."ContentType", t1."CurrentVersion", t1."Description", t1."FileName", t1."FilePath", t1."FileSize", t1."LastModifiedAt", t1."Tags", t1."TaskId", t1."UploadedAt", t1."UploadedByUserId", t2."Id", t2."ActualHours", t2."AssignedToUserId", t2."CompletedDate", t2."CreatedAt", t2."CreatedByUserId", t2."DeletedAt", t2."Description", t2."DueDate", t2."EstimatedHours", t2."IsDeleted", t2."IsRecurring", t2."IsTemplate", t2."ParentTaskId", t2."Priority", t2."ProjectId", t2."RecurrenceEndDate", t2."RecurrencePattern", t2."SortOrder", t2."StartDate", t2."Status", t2."TemplateId", t2."Title", t2."UpdatedAt", t2."UserStoryId", t2."WbsCode", t2."WbsLevel"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON t."CreatedByUserId" = a0."Id"
LEFT JOIN "TaskComments" AS t0 ON t."Id" = t0."TaskId"
LEFT JOIN "TaskAttachments" AS t1 ON t."Id" = t1."TaskId"
LEFT JOIN "Tasks" AS t2 ON t."Id" = t2."ParentTaskId"
WHERE NOT (t."IsDeleted") AND t."Id" = @__taskId_0
ORDER BY t."Id", p."Id", a."Id", a0."Id", t0."Id", t1."Id"
2025-06-30 11:22:13.049 +06:00 [INF] Executed DbCommand (10ms) [Parameters=[@__projectId_0='?' (DbType = Int32), @__userId_1='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."IsActive", p."JoinedAt", p."ProjectId", p."Role", p."UserId"
FROM "ProjectMembers" AS p
WHERE p."ProjectId" = @__projectId_0 AND p."UserId" = @__userId_1 AND p."IsActive"
LIMIT 1
2025-06-30 11:22:13.070 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt"
FROM "Projects" AS p
WHERE p."Id" = @__id_0 AND NOT (p."IsDeleted")
LIMIT 1
2025-06-30 11:22:13.103 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__taskId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."Content", t."CreatedAt", t."TaskId", t."UpdatedAt", t."UserId", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "TaskComments" AS t
INNER JOIN "AspNetUsers" AS a ON t."UserId" = a."Id"
WHERE t."TaskId" = @__taskId_0
ORDER BY t."CreatedAt" DESC
2025-06-30 11:22:13.115 +06:00 [INF] Executing ViewResult, running view Details.
2025-06-30 11:22:13.280 +06:00 [INF] Executed ViewResult - view Details executed in 166.2917ms.
2025-06-30 11:22:13.282 +06:00 [INF] Executed action PM.Tool.Controllers.TasksController.Details (PM.Tool) in 380.0365ms
2025-06-30 11:22:13.283 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.TasksController.Details (PM.Tool)'
2025-06-30 11:22:13.285 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Tasks/Details/2 - 200 null text/html; charset=utf-8 421.4852ms
2025-06-30 11:22:13.322 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:22:13.322 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:22:13.325 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 3.3498ms
2025-06-30 11:22:13.344 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 21.8891ms
2025-06-30 11:22:43.296 +06:00 [INF] Request starting HTTP/2 POST https://localhost:7029/Comment/Create - application/x-www-form-urlencoded; charset=UTF-8 274
2025-06-30 11:22:43.310 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.CommentController.Create (PM.Tool)'
2025-06-30 11:22:43.325 +06:00 [INF] Route matched with {action = "Create", controller = "Comment", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Create(PM.Tool.Models.ViewModels.CommentViewModel) on controller PM.Tool.Controllers.CommentController (PM.Tool).
2025-06-30 11:22:43.390 +06:00 [INF] Executed DbCommand (10ms) [Parameters=[@__model_TaskId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt"
FROM "Tasks" AS t
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
WHERE t."Id" = @__model_TaskId_0
LIMIT 1
2025-06-30 11:22:43.437 +06:00 [INF] Executed DbCommand (21ms) [Parameters=[@p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?' (DbType = DateTime), @p4='?' (DbType = Boolean), @p5='?' (DbType = Int32), @p6='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Comments" ("AuthorId", "Content", "CreatedAt", "DeletedAt", "IsDeleted", "TaskId", "UpdatedAt")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6)
RETURNING "Id";
2025-06-30 11:22:43.519 +06:00 [INF] Executed DbCommand (12ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = DateTime), @p2='?' (DbType = Int32), @p3='?', @p4='?', @p5='?', @p6='?', @p7='?', @p8='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AuditLogs" ("Action", "CreatedAt", "EntityId", "EntityName", "IpAddress", "NewValues", "OldValues", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
RETURNING "Id";
2025-06-30 11:22:43.533 +06:00 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType91`4[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-30 11:22:43.561 +06:00 [INF] Executed action PM.Tool.Controllers.CommentController.Create (PM.Tool) in 232.583ms
2025-06-30 11:22:43.565 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.CommentController.Create (PM.Tool)'
2025-06-30 11:22:43.569 +06:00 [INF] Request finished HTTP/2 POST https://localhost:7029/Comment/Create - 200 null application/json; charset=utf-8 273.3075ms
2025-06-30 11:22:59.299 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Tasks/Details/2 - null null
2025-06-30 11:22:59.309 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.TasksController.Details (PM.Tool)'
2025-06-30 11:22:59.311 +06:00 [INF] Route matched with {action = "Details", controller = "Tasks", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Details(Int32) on controller PM.Tool.Controllers.TasksController (PM.Tool).
2025-06-30 11:22:59.320 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-30 11:22:59.350 +06:00 [INF] Executed DbCommand (25ms) [Parameters=[@__taskId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", t0."Id", t0."Content", t0."CreatedAt", t0."TaskId", t0."UpdatedAt", t0."UserId", t1."Id", t1."CategoryId", t1."ContentType", t1."CurrentVersion", t1."Description", t1."FileName", t1."FilePath", t1."FileSize", t1."LastModifiedAt", t1."Tags", t1."TaskId", t1."UploadedAt", t1."UploadedByUserId", t2."Id", t2."ActualHours", t2."AssignedToUserId", t2."CompletedDate", t2."CreatedAt", t2."CreatedByUserId", t2."DeletedAt", t2."Description", t2."DueDate", t2."EstimatedHours", t2."IsDeleted", t2."IsRecurring", t2."IsTemplate", t2."ParentTaskId", t2."Priority", t2."ProjectId", t2."RecurrenceEndDate", t2."RecurrencePattern", t2."SortOrder", t2."StartDate", t2."Status", t2."TemplateId", t2."Title", t2."UpdatedAt", t2."UserStoryId", t2."WbsCode", t2."WbsLevel"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON t."CreatedByUserId" = a0."Id"
LEFT JOIN "TaskComments" AS t0 ON t."Id" = t0."TaskId"
LEFT JOIN "TaskAttachments" AS t1 ON t."Id" = t1."TaskId"
LEFT JOIN "Tasks" AS t2 ON t."Id" = t2."ParentTaskId"
WHERE NOT (t."IsDeleted") AND t."Id" = @__taskId_0
ORDER BY t."Id", p."Id", a."Id", a0."Id", t0."Id", t1."Id"
2025-06-30 11:22:59.361 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__projectId_0='?' (DbType = Int32), @__userId_1='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."IsActive", p."JoinedAt", p."ProjectId", p."Role", p."UserId"
FROM "ProjectMembers" AS p
WHERE p."ProjectId" = @__projectId_0 AND p."UserId" = @__userId_1 AND p."IsActive"
LIMIT 1
2025-06-30 11:22:59.373 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt"
FROM "Projects" AS p
WHERE p."Id" = @__id_0 AND NOT (p."IsDeleted")
LIMIT 1
2025-06-30 11:22:59.382 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__taskId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."Content", t."CreatedAt", t."TaskId", t."UpdatedAt", t."UserId", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "TaskComments" AS t
INNER JOIN "AspNetUsers" AS a ON t."UserId" = a."Id"
WHERE t."TaskId" = @__taskId_0
ORDER BY t."CreatedAt" DESC
2025-06-30 11:22:59.387 +06:00 [INF] Executing ViewResult, running view Details.
2025-06-30 11:22:59.397 +06:00 [INF] Executed ViewResult - view Details executed in 9.8648ms.
2025-06-30 11:22:59.403 +06:00 [INF] Executed action PM.Tool.Controllers.TasksController.Details (PM.Tool) in 89.0392ms
2025-06-30 11:22:59.405 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.TasksController.Details (PM.Tool)'
2025-06-30 11:22:59.406 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:22:59.406 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:22:59.407 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Tasks/Details/2 - 200 null text/html; charset=utf-8 108.0892ms
2025-06-30 11:22:59.420 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 14.2197ms
2025-06-30 11:22:59.422 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 16.3988ms
2025-06-30 11:22:59.490 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/favicon.ico - null null
2025-06-30 11:22:59.510 +06:00 [INF] Sending file. Request path: '/favicon.ico'. Physical path: 'D:\TGI\PM.Tool\PM.Tool\wwwroot\favicon.ico'
2025-06-30 11:22:59.513 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/favicon.ico - 200 5430 image/x-icon 22.4207ms
2025-06-30 11:23:08.554 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Tasks/Details/2 - null null
2025-06-30 11:23:08.561 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.TasksController.Details (PM.Tool)'
2025-06-30 11:23:08.564 +06:00 [INF] Route matched with {action = "Details", controller = "Tasks", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Details(Int32) on controller PM.Tool.Controllers.TasksController (PM.Tool).
2025-06-30 11:23:08.573 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-30 11:23:08.592 +06:00 [INF] Executed DbCommand (12ms) [Parameters=[@__taskId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", t0."Id", t0."Content", t0."CreatedAt", t0."TaskId", t0."UpdatedAt", t0."UserId", t1."Id", t1."CategoryId", t1."ContentType", t1."CurrentVersion", t1."Description", t1."FileName", t1."FilePath", t1."FileSize", t1."LastModifiedAt", t1."Tags", t1."TaskId", t1."UploadedAt", t1."UploadedByUserId", t2."Id", t2."ActualHours", t2."AssignedToUserId", t2."CompletedDate", t2."CreatedAt", t2."CreatedByUserId", t2."DeletedAt", t2."Description", t2."DueDate", t2."EstimatedHours", t2."IsDeleted", t2."IsRecurring", t2."IsTemplate", t2."ParentTaskId", t2."Priority", t2."ProjectId", t2."RecurrenceEndDate", t2."RecurrencePattern", t2."SortOrder", t2."StartDate", t2."Status", t2."TemplateId", t2."Title", t2."UpdatedAt", t2."UserStoryId", t2."WbsCode", t2."WbsLevel"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON t."CreatedByUserId" = a0."Id"
LEFT JOIN "TaskComments" AS t0 ON t."Id" = t0."TaskId"
LEFT JOIN "TaskAttachments" AS t1 ON t."Id" = t1."TaskId"
LEFT JOIN "Tasks" AS t2 ON t."Id" = t2."ParentTaskId"
WHERE NOT (t."IsDeleted") AND t."Id" = @__taskId_0
ORDER BY t."Id", p."Id", a."Id", a0."Id", t0."Id", t1."Id"
2025-06-30 11:23:08.602 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__projectId_0='?' (DbType = Int32), @__userId_1='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."IsActive", p."JoinedAt", p."ProjectId", p."Role", p."UserId"
FROM "ProjectMembers" AS p
WHERE p."ProjectId" = @__projectId_0 AND p."UserId" = @__userId_1 AND p."IsActive"
LIMIT 1
2025-06-30 11:23:08.610 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt"
FROM "Projects" AS p
WHERE p."Id" = @__id_0 AND NOT (p."IsDeleted")
LIMIT 1
2025-06-30 11:23:08.619 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__taskId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."Content", t."CreatedAt", t."TaskId", t."UpdatedAt", t."UserId", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "TaskComments" AS t
INNER JOIN "AspNetUsers" AS a ON t."UserId" = a."Id"
WHERE t."TaskId" = @__taskId_0
ORDER BY t."CreatedAt" DESC
2025-06-30 11:23:08.621 +06:00 [INF] Executing ViewResult, running view Details.
2025-06-30 11:23:08.625 +06:00 [INF] Executed ViewResult - view Details executed in 3.411ms.
2025-06-30 11:23:08.626 +06:00 [INF] Executed action PM.Tool.Controllers.TasksController.Details (PM.Tool) in 60.6078ms
2025-06-30 11:23:08.627 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.TasksController.Details (PM.Tool)'
2025-06-30 11:23:08.629 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Tasks/Details/2 - 200 null text/html; charset=utf-8 75.5376ms
2025-06-30 11:23:08.637 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:23:08.637 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:23:08.643 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 6.0855ms
2025-06-30 11:23:08.690 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 53.5218ms
2025-06-30 11:24:29.097 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Meeting - null null
2025-06-30 11:24:29.117 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.MeetingController.Index (PM.Tool)'
2025-06-30 11:24:29.123 +06:00 [INF] Route matched with {action = "Index", controller = "Meeting", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.MeetingController (PM.Tool).
2025-06-30 11:24:29.189 +06:00 [INF] Executed DbCommand (20ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."ActionItems", m."ActualEndTime", m."ActualStartTime", m."Agenda", m."CancellationReason", m."CreatedAt", m."DeletedAt", m."Description", m."DurationMinutes", m."IsDeleted", m."IsRecurring", m."Location", m."MeetingLink", m."Minutes", m."OrganizerUserId", m."ParentMeetingId", m."ProjectId", m."RecurrencePattern", m."ScheduledDate", m."SendReminders", m."Status", m."Title", m."Type", m."UpdatedAt", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Meetings" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON m."OrganizerUserId" = a."Id"
WHERE m."OrganizerUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "MeetingAttendees" AS m0
    WHERE m."Id" = m0."MeetingId" AND m0."UserId" = @__userId_0)
ORDER BY m."ScheduledDate" DESC
2025-06-30 11:24:29.198 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-30 11:24:29.242 +06:00 [INF] Executed ViewResult - view Index executed in 45.831ms.
2025-06-30 11:24:29.245 +06:00 [INF] Executed action PM.Tool.Controllers.MeetingController.Index (PM.Tool) in 117.7669ms
2025-06-30 11:24:29.247 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.MeetingController.Index (PM.Tool)'
2025-06-30 11:24:29.251 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Meeting - 200 null text/html; charset=utf-8 153.4114ms
2025-06-30 11:24:29.286 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:24:29.286 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:24:29.291 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 5.1438ms
2025-06-30 11:24:29.298 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 11.8585ms
2025-06-30 11:24:29.303 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Meeting/GetUpcomingMeetings - null null
2025-06-30 11:24:29.308 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Meeting/GetUpcomingMeetings - 404 0 null 5.1764ms
2025-06-30 11:24:29.312 +06:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7029/Meeting/GetUpcomingMeetings, Response status code: 404
2025-06-30 11:24:36.593 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Wbs - null null
2025-06-30 11:24:36.600 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.WbsController.Index (PM.Tool)'
2025-06-30 11:24:36.612 +06:00 [INF] Route matched with {action = "Index", controller = "Wbs", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index(System.String, System.Nullable`1[PM.Tool.Core.Enums.ProjectStatus], System.String, System.String, Int32, Int32) on controller PM.Tool.Controllers.WbsController (PM.Tool).
2025-06-30 11:24:36.640 +06:00 [INF] Executed DbCommand (16ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-30 11:24:36.749 +06:00 [INF] Executing ViewResult, running view ProjectSelection.
2025-06-30 11:24:36.812 +06:00 [INF] Executed ViewResult - view ProjectSelection executed in 78.7778ms.
2025-06-30 11:24:36.817 +06:00 [INF] Executed action PM.Tool.Controllers.WbsController.Index (PM.Tool) in 201.1341ms
2025-06-30 11:24:36.819 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.WbsController.Index (PM.Tool)'
2025-06-30 11:24:36.821 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Wbs - 200 null text/html; charset=utf-8 228.7078ms
2025-06-30 11:24:36.824 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:24:36.824 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:24:36.829 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 4.6051ms
2025-06-30 11:24:36.890 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 65.2553ms
2025-06-30 11:24:45.780 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Requirement - null null
2025-06-30 11:24:45.808 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RequirementController.Index (PM.Tool)'
2025-06-30 11:24:45.817 +06:00 [INF] Route matched with {action = "Index", controller = "Requirement", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RequirementController (PM.Tool).
2025-06-30 11:24:45.895 +06:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."AcceptanceCriteria", r."AnalystUserId", r."ApprovedByUserId", r."ApprovedDate", r."BlockedReason", r."BusinessJustification", r."CreatedAt", r."DeletedAt", r."Description", r."DeveloperUserId", r."EstimatedEffort", r."IsBlocked", r."IsDeleted", r."Notes", r."ParentRequirementId", r."Priority", r."ProjectId", r."RequirementId", r."Source", r."StakeholderUserId", r."Status", r."Tags", r."TargetDate", r."TestCases", r."TesterUserId", r."Title", r."Type", r."UpdatedAt", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName"
FROM "Requirements" AS r
INNER JOIN "Projects" AS p ON r."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON r."DeveloperUserId" = a."Id"
LEFT JOIN "AspNetUsers" AS a0 ON r."StakeholderUserId" = a0."Id"
ORDER BY r."CreatedAt" DESC
2025-06-30 11:24:45.910 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-30 11:24:45.993 +06:00 [INF] Executed ViewResult - view Index executed in 84.2809ms.
2025-06-30 11:24:46.005 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:24:46.005 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:24:46.072 +06:00 [INF] Executed action PM.Tool.Controllers.RequirementController.Index (PM.Tool) in 249.3193ms
2025-06-30 11:24:46.076 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 70.3649ms
2025-06-30 11:24:46.098 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 92.0141ms
2025-06-30 11:24:46.114 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RequirementController.Index (PM.Tool)'
2025-06-30 11:24:46.141 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Requirement - 200 null text/html; charset=utf-8 360.8938ms
2025-06-30 11:24:46.155 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Project/GetProjects - null null
2025-06-30 11:24:46.178 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Project/GetProjects - 404 0 null 23.4616ms
2025-06-30 11:24:46.192 +06:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7029/Project/GetProjects, Response status code: 404
2025-06-30 11:25:12.751 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Requirement/GetRequirementHierarchy - null null
2025-06-30 11:25:12.759 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Requirement/GetRequirementHierarchy - 404 0 null 8.0443ms
2025-06-30 11:25:12.766 +06:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7029/Requirement/GetRequirementHierarchy, Response status code: 404
2025-06-30 11:25:15.327 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Requirement/GetRequirementMatrix - null null
2025-06-30 11:25:15.335 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Requirement/GetRequirementMatrix - 404 0 null 8.3963ms
2025-06-30 11:25:15.341 +06:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7029/Requirement/GetRequirementMatrix, Response status code: 404
2025-06-30 11:25:18.070 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Requirement/GetRequirementHierarchy - null null
2025-06-30 11:25:18.077 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Requirement/GetRequirementHierarchy - 404 0 null 7.917ms
2025-06-30 11:25:18.085 +06:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7029/Requirement/GetRequirementHierarchy, Response status code: 404
2025-06-30 11:25:19.913 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Requirement/GetRequirementMatrix - null null
2025-06-30 11:25:19.919 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Requirement/GetRequirementMatrix - 404 0 null 5.775ms
2025-06-30 11:25:19.922 +06:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7029/Requirement/GetRequirementMatrix, Response status code: 404
2025-06-30 11:25:43.896 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Stakeholder - null null
2025-06-30 11:25:43.906 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.StakeholderController.Index (PM.Tool)'
2025-06-30 11:25:43.912 +06:00 [INF] Route matched with {action = "Index", controller = "Stakeholder", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.StakeholderController (PM.Tool).
2025-06-30 11:25:44.011 +06:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT s."Id", s."CommunicationPreferences", s."CreatedAt", s."DeletedAt", s."Department", s."Email", s."Influence", s."Interest", s."IsActive", s."IsDeleted", s."Name", s."Notes", s."Organization", s."Phone", s."Role", s."Title", s."Type", s."UpdatedAt", s."UserId"
FROM "Stakeholders" AS s
ORDER BY s."Name"
2025-06-30 11:25:44.018 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-30 11:25:44.050 +06:00 [INF] Executed ViewResult - view Index executed in 32.6968ms.
2025-06-30 11:25:44.053 +06:00 [INF] Executed action PM.Tool.Controllers.StakeholderController.Index (PM.Tool) in 134.6637ms
2025-06-30 11:25:44.055 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.StakeholderController.Index (PM.Tool)'
2025-06-30 11:25:44.056 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Stakeholder - 200 null text/html; charset=utf-8 160.0543ms
2025-06-30 11:25:44.061 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:25:44.061 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:25:44.143 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 81.3316ms
2025-06-30 11:25:44.143 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 81.5523ms
2025-06-30 11:27:29.495 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Tasks - null null
2025-06-30 11:27:29.515 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.TasksController.Index (PM.Tool)'
2025-06-30 11:27:29.521 +06:00 [INF] Route matched with {action = "Index", controller = "Tasks", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index(System.Nullable`1[System.Int32], System.Nullable`1[PM.Tool.Core.Enums.TaskStatus], System.String) on controller PM.Tool.Controllers.TasksController (PM.Tool).
2025-06-30 11:27:29.540 +06:00 [INF] Executed DbCommand (10ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-30 11:27:29.563 +06:00 [INF] Executed DbCommand (16ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-30 11:27:29.588 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt"
FROM "Projects" AS p
WHERE NOT (p."IsDeleted")
2025-06-30 11:27:29.605 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."IsActive"
2025-06-30 11:27:29.617 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-30 11:27:29.688 +06:00 [INF] Executed ViewResult - view Index executed in 71.3539ms.
2025-06-30 11:27:29.689 +06:00 [INF] Executed action PM.Tool.Controllers.TasksController.Index (PM.Tool) in 165.5269ms
2025-06-30 11:27:29.690 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.TasksController.Index (PM.Tool)'
2025-06-30 11:27:29.691 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Tasks - 200 null text/html; charset=utf-8 196.0785ms
2025-06-30 11:27:29.702 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:27:29.702 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:27:29.705 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 3.5507ms
2025-06-30 11:27:29.768 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 65.722ms
2025-06-30 11:27:31.842 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/ - null null
2025-06-30 11:27:31.850 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-30 11:27:31.853 +06:00 [INF] Route matched with {action = "Index", controller = "Home", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-30 11:27:31.863 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-30 11:27:31.878 +06:00 [INF] Executed DbCommand (12ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-30 11:27:31.891 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-30 11:27:31.903 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" < now()
ORDER BY t."DueDate"
2025-06-30 11:27:31.913 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__dueDate_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
FROM "Tasks" AS t
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" <= @__dueDate_0
2025-06-30 11:27:31.922 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT n."Id", n."Title", n."Message", n."Type", n."IsRead", n."CreatedAt", n."RelatedProjectId", n."RelatedTaskId"
FROM "Notifications" AS n
WHERE n."UserId" = @__user_Id_0
ORDER BY n."CreatedAt" DESC
LIMIT @__p_1
2025-06-30 11:27:31.931 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."Name", m."Description", m."ProjectId", p."Name", m."DueDate", m."CompletedDate", m."IsCompleted", m."CreatedAt", m."IsDeleted", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."StartDate", p."Status", p."UpdatedAt"
FROM "Milestones" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
WHERE EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__user_Id_0 AND p0."IsActive") AND NOT (m."IsCompleted") AND m."DueDate" >= now()
ORDER BY m."DueDate"
LIMIT @__p_1
2025-06-30 11:27:31.935 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-30 11:27:31.941 +06:00 [INF] Executed ViewResult - view Index executed in 6.1503ms.
2025-06-30 11:27:31.943 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.Index (PM.Tool) in 86.8686ms
2025-06-30 11:27:31.947 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-30 11:27:31.949 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/ - 200 null text/html; charset=utf-8 106.3121ms
2025-06-30 11:27:31.953 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:27:31.953 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:27:31.964 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 10.7872ms
2025-06-30 11:27:31.971 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 17.8419ms
2025-06-30 11:27:36.378 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/ - null null
2025-06-30 11:27:36.386 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-30 11:27:36.392 +06:00 [INF] Route matched with {action = "Index", controller = "Home", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-30 11:27:36.402 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-30 11:27:36.429 +06:00 [INF] Executed DbCommand (18ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-30 11:27:36.450 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-30 11:27:36.465 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" < now()
ORDER BY t."DueDate"
2025-06-30 11:27:36.476 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__dueDate_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
FROM "Tasks" AS t
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" <= @__dueDate_0
2025-06-30 11:27:36.492 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT n."Id", n."Title", n."Message", n."Type", n."IsRead", n."CreatedAt", n."RelatedProjectId", n."RelatedTaskId"
FROM "Notifications" AS n
WHERE n."UserId" = @__user_Id_0
ORDER BY n."CreatedAt" DESC
LIMIT @__p_1
2025-06-30 11:27:36.511 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."Name", m."Description", m."ProjectId", p."Name", m."DueDate", m."CompletedDate", m."IsCompleted", m."CreatedAt", m."IsDeleted", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."StartDate", p."Status", p."UpdatedAt"
FROM "Milestones" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
WHERE EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__user_Id_0 AND p0."IsActive") AND NOT (m."IsCompleted") AND m."DueDate" >= now()
ORDER BY m."DueDate"
LIMIT @__p_1
2025-06-30 11:27:36.519 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-30 11:27:36.536 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Tasks/Details/2 - null null
2025-06-30 11:27:36.551 +06:00 [INF] Executed ViewResult - view Index executed in 35.9084ms.
2025-06-30 11:27:36.576 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.Index (PM.Tool) in 181.1082ms
2025-06-30 11:27:36.577 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.TasksController.Details (PM.Tool)'
2025-06-30 11:27:36.579 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-30 11:27:36.587 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/ - 200 null text/html; charset=utf-8 208.7936ms
2025-06-30 11:27:36.589 +06:00 [INF] Route matched with {action = "Details", controller = "Tasks", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Details(Int32) on controller PM.Tool.Controllers.TasksController (PM.Tool).
2025-06-30 11:27:36.601 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-30 11:27:36.617 +06:00 [INF] Executed DbCommand (14ms) [Parameters=[@__taskId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", t0."Id", t0."Content", t0."CreatedAt", t0."TaskId", t0."UpdatedAt", t0."UserId", t1."Id", t1."CategoryId", t1."ContentType", t1."CurrentVersion", t1."Description", t1."FileName", t1."FilePath", t1."FileSize", t1."LastModifiedAt", t1."Tags", t1."TaskId", t1."UploadedAt", t1."UploadedByUserId", t2."Id", t2."ActualHours", t2."AssignedToUserId", t2."CompletedDate", t2."CreatedAt", t2."CreatedByUserId", t2."DeletedAt", t2."Description", t2."DueDate", t2."EstimatedHours", t2."IsDeleted", t2."IsRecurring", t2."IsTemplate", t2."ParentTaskId", t2."Priority", t2."ProjectId", t2."RecurrenceEndDate", t2."RecurrencePattern", t2."SortOrder", t2."StartDate", t2."Status", t2."TemplateId", t2."Title", t2."UpdatedAt", t2."UserStoryId", t2."WbsCode", t2."WbsLevel"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON t."CreatedByUserId" = a0."Id"
LEFT JOIN "TaskComments" AS t0 ON t."Id" = t0."TaskId"
LEFT JOIN "TaskAttachments" AS t1 ON t."Id" = t1."TaskId"
LEFT JOIN "Tasks" AS t2 ON t."Id" = t2."ParentTaskId"
WHERE NOT (t."IsDeleted") AND t."Id" = @__taskId_0
ORDER BY t."Id", p."Id", a."Id", a0."Id", t0."Id", t1."Id"
2025-06-30 11:27:36.624 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__projectId_0='?' (DbType = Int32), @__userId_1='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."IsActive", p."JoinedAt", p."ProjectId", p."Role", p."UserId"
FROM "ProjectMembers" AS p
WHERE p."ProjectId" = @__projectId_0 AND p."UserId" = @__userId_1 AND p."IsActive"
LIMIT 1
2025-06-30 11:27:36.632 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt"
FROM "Projects" AS p
WHERE p."Id" = @__id_0 AND NOT (p."IsDeleted")
LIMIT 1
2025-06-30 11:27:36.643 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__taskId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."Content", t."CreatedAt", t."TaskId", t."UpdatedAt", t."UserId", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "TaskComments" AS t
INNER JOIN "AspNetUsers" AS a ON t."UserId" = a."Id"
WHERE t."TaskId" = @__taskId_0
ORDER BY t."CreatedAt" DESC
2025-06-30 11:27:36.651 +06:00 [INF] Executing ViewResult, running view Details.
2025-06-30 11:27:36.804 +06:00 [INF] Executed ViewResult - view Details executed in 157.0323ms.
2025-06-30 11:27:36.808 +06:00 [INF] Executed action PM.Tool.Controllers.TasksController.Details (PM.Tool) in 213.9427ms
2025-06-30 11:27:36.811 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.TasksController.Details (PM.Tool)'
2025-06-30 11:27:36.813 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Tasks/Details/2 - 200 null text/html; charset=utf-8 277.6058ms
2025-06-30 11:27:36.819 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:27:36.820 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:27:36.831 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 12.2064ms
2025-06-30 11:27:36.838 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 18.6238ms
2025-06-30 11:27:40.423 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Tasks/Details/2 - null null
2025-06-30 11:27:40.433 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.TasksController.Details (PM.Tool)'
2025-06-30 11:27:40.436 +06:00 [INF] Route matched with {action = "Details", controller = "Tasks", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Details(Int32) on controller PM.Tool.Controllers.TasksController (PM.Tool).
2025-06-30 11:27:40.447 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-30 11:27:40.462 +06:00 [INF] Executed DbCommand (12ms) [Parameters=[@__taskId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", t0."Id", t0."Content", t0."CreatedAt", t0."TaskId", t0."UpdatedAt", t0."UserId", t1."Id", t1."CategoryId", t1."ContentType", t1."CurrentVersion", t1."Description", t1."FileName", t1."FilePath", t1."FileSize", t1."LastModifiedAt", t1."Tags", t1."TaskId", t1."UploadedAt", t1."UploadedByUserId", t2."Id", t2."ActualHours", t2."AssignedToUserId", t2."CompletedDate", t2."CreatedAt", t2."CreatedByUserId", t2."DeletedAt", t2."Description", t2."DueDate", t2."EstimatedHours", t2."IsDeleted", t2."IsRecurring", t2."IsTemplate", t2."ParentTaskId", t2."Priority", t2."ProjectId", t2."RecurrenceEndDate", t2."RecurrencePattern", t2."SortOrder", t2."StartDate", t2."Status", t2."TemplateId", t2."Title", t2."UpdatedAt", t2."UserStoryId", t2."WbsCode", t2."WbsLevel"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON t."CreatedByUserId" = a0."Id"
LEFT JOIN "TaskComments" AS t0 ON t."Id" = t0."TaskId"
LEFT JOIN "TaskAttachments" AS t1 ON t."Id" = t1."TaskId"
LEFT JOIN "Tasks" AS t2 ON t."Id" = t2."ParentTaskId"
WHERE NOT (t."IsDeleted") AND t."Id" = @__taskId_0
ORDER BY t."Id", p."Id", a."Id", a0."Id", t0."Id", t1."Id"
2025-06-30 11:27:40.472 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__projectId_0='?' (DbType = Int32), @__userId_1='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."IsActive", p."JoinedAt", p."ProjectId", p."Role", p."UserId"
FROM "ProjectMembers" AS p
WHERE p."ProjectId" = @__projectId_0 AND p."UserId" = @__userId_1 AND p."IsActive"
LIMIT 1
2025-06-30 11:27:40.485 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt"
FROM "Projects" AS p
WHERE p."Id" = @__id_0 AND NOT (p."IsDeleted")
LIMIT 1
2025-06-30 11:27:40.496 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__taskId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."Content", t."CreatedAt", t."TaskId", t."UpdatedAt", t."UserId", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "TaskComments" AS t
INNER JOIN "AspNetUsers" AS a ON t."UserId" = a."Id"
WHERE t."TaskId" = @__taskId_0
ORDER BY t."CreatedAt" DESC
2025-06-30 11:27:40.499 +06:00 [INF] Executing ViewResult, running view Details.
2025-06-30 11:27:40.504 +06:00 [INF] Executed ViewResult - view Details executed in 5.6068ms.
2025-06-30 11:27:40.506 +06:00 [INF] Executed action PM.Tool.Controllers.TasksController.Details (PM.Tool) in 66.3088ms
2025-06-30 11:27:40.509 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.TasksController.Details (PM.Tool)'
2025-06-30 11:27:40.516 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Tasks/Details/2 - 200 null text/html; charset=utf-8 93.4587ms
2025-06-30 11:27:40.517 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:27:40.517 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:27:40.523 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 5.6235ms
2025-06-30 11:27:40.532 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 15.3165ms
2025-06-30 11:28:01.103 +06:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-30 11:28:04.121 +06:00 [INF] Now listening on: https://localhost:7029
2025-06-30 11:28:04.122 +06:00 [INF] Now listening on: http://localhost:5045
2025-06-30 11:28:04.222 +06:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-30 11:28:04.224 +06:00 [INF] Hosting environment: Development
2025-06-30 11:28:04.225 +06:00 [INF] Content root path: D:\TGI\PM.Tool\PM.Tool
2025-06-30 11:28:04.273 +06:00 [INF] Executed DbCommand (81ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE t."DueDate" IS NOT NULL AND t."DueDate" <= @__upcomingDeadlines_0 AND t."DueDate" > now() AND t."Status" <> 4
2025-06-30 11:28:04.941 +06:00 [INF] Executed DbCommand (11ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?', @p2='?' (DbType = DateTime), @p3='?' (DbType = Int32), @p4='?' (DbType = Int32), @p5='?', @p6='?' (DbType = Int32), @p7='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Notifications" ("CreatedAt", "Message", "ReadAt", "RelatedProjectId", "RelatedTaskId", "Title", "Type", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "IsEmailSent", "IsRead";
2025-06-30 11:28:05.019 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", p0."Id", p0."IsActive", p0."JoinedAt", p0."ProjectId", p0."Role", p0."UserId"
FROM "Projects" AS p
LEFT JOIN "ProjectMembers" AS p0 ON p."Id" = p0."ProjectId"
WHERE p."EndDate" IS NOT NULL AND p."EndDate" <= @__upcomingDeadlines_0 AND p."EndDate" > now() AND p."Status" <> 5
ORDER BY p."Id"
2025-06-30 11:28:05.022 +06:00 [INF] Deadline check completed at: "2025-06-30T11:28:05.0225603+06:00"
2025-06-30 11:28:05.854 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/ - null null
2025-06-30 11:28:06.357 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-30 11:28:06.386 +06:00 [INF] Route matched with {action = "Index", controller = "Home", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-30 11:28:06.461 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-30 11:28:06.522 +06:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-30 11:28:06.564 +06:00 [INF] Executed DbCommand (15ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-30 11:28:06.648 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-30 11:28:06.706 +06:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" < now()
ORDER BY t."DueDate"
2025-06-30 11:28:06.736 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__dueDate_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
FROM "Tasks" AS t
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" <= @__dueDate_0
2025-06-30 11:28:06.779 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT n."Id", n."Title", n."Message", n."Type", n."IsRead", n."CreatedAt", n."RelatedProjectId", n."RelatedTaskId"
FROM "Notifications" AS n
WHERE n."UserId" = @__user_Id_0
ORDER BY n."CreatedAt" DESC
LIMIT @__p_1
2025-06-30 11:28:06.823 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."Name", m."Description", m."ProjectId", p."Name", m."DueDate", m."CompletedDate", m."IsCompleted", m."CreatedAt", m."IsDeleted", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."StartDate", p."Status", p."UpdatedAt"
FROM "Milestones" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
WHERE EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__user_Id_0 AND p0."IsActive") AND NOT (m."IsCompleted") AND m."DueDate" >= now()
ORDER BY m."DueDate"
LIMIT @__p_1
2025-06-30 11:28:06.846 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-30 11:28:07.140 +06:00 [INF] Executed ViewResult - view Index executed in 297.9375ms.
2025-06-30 11:28:07.155 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.Index (PM.Tool) in 765.3145ms
2025-06-30 11:28:07.159 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-30 11:28:07.180 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/ - 200 null text/html; charset=utf-8 1341.8693ms
2025-06-30 11:28:07.251 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:28:07.251 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:28:07.306 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 55.1003ms
2025-06-30 11:28:07.357 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 106.1579ms
2025-06-30 11:28:25.493 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Tasks/Details/1 - null null
2025-06-30 11:28:25.515 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.TasksController.Details (PM.Tool)'
2025-06-30 11:28:25.531 +06:00 [INF] Route matched with {action = "Details", controller = "Tasks", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Details(Int32) on controller PM.Tool.Controllers.TasksController (PM.Tool).
2025-06-30 11:28:25.656 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-30 11:28:25.701 +06:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-30 11:28:25.750 +06:00 [INF] Executed DbCommand (16ms) [Parameters=[@__taskId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", t0."Id", t0."Content", t0."CreatedAt", t0."TaskId", t0."UpdatedAt", t0."UserId", t1."Id", t1."CategoryId", t1."ContentType", t1."CurrentVersion", t1."Description", t1."FileName", t1."FilePath", t1."FileSize", t1."LastModifiedAt", t1."Tags", t1."TaskId", t1."UploadedAt", t1."UploadedByUserId", t2."Id", t2."ActualHours", t2."AssignedToUserId", t2."CompletedDate", t2."CreatedAt", t2."CreatedByUserId", t2."DeletedAt", t2."Description", t2."DueDate", t2."EstimatedHours", t2."IsDeleted", t2."IsRecurring", t2."IsTemplate", t2."ParentTaskId", t2."Priority", t2."ProjectId", t2."RecurrenceEndDate", t2."RecurrencePattern", t2."SortOrder", t2."StartDate", t2."Status", t2."TemplateId", t2."Title", t2."UpdatedAt", t2."UserStoryId", t2."WbsCode", t2."WbsLevel"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON t."CreatedByUserId" = a0."Id"
LEFT JOIN "TaskComments" AS t0 ON t."Id" = t0."TaskId"
LEFT JOIN "TaskAttachments" AS t1 ON t."Id" = t1."TaskId"
LEFT JOIN "Tasks" AS t2 ON t."Id" = t2."ParentTaskId"
WHERE NOT (t."IsDeleted") AND t."Id" = @__taskId_0
ORDER BY t."Id", p."Id", a."Id", a0."Id", t0."Id", t1."Id"
2025-06-30 11:28:25.794 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__projectId_0='?' (DbType = Int32), @__userId_1='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."IsActive", p."JoinedAt", p."ProjectId", p."Role", p."UserId"
FROM "ProjectMembers" AS p
WHERE p."ProjectId" = @__projectId_0 AND p."UserId" = @__userId_1 AND p."IsActive"
LIMIT 1
2025-06-30 11:28:25.818 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt"
FROM "Projects" AS p
WHERE p."Id" = @__id_0 AND NOT (p."IsDeleted")
LIMIT 1
2025-06-30 11:28:25.850 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__taskId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."Content", t."CreatedAt", t."TaskId", t."UpdatedAt", t."UserId", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "TaskComments" AS t
INNER JOIN "AspNetUsers" AS a ON t."UserId" = a."Id"
WHERE t."TaskId" = @__taskId_0
ORDER BY t."CreatedAt" DESC
2025-06-30 11:28:25.868 +06:00 [INF] Executing ViewResult, running view Details.
2025-06-30 11:28:26.065 +06:00 [INF] Executed ViewResult - view Details executed in 199.6676ms.
2025-06-30 11:28:26.069 +06:00 [INF] Executed action PM.Tool.Controllers.TasksController.Details (PM.Tool) in 534.0346ms
2025-06-30 11:28:26.072 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.TasksController.Details (PM.Tool)'
2025-06-30 11:28:26.074 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Tasks/Details/1 - 200 null text/html; charset=utf-8 581.7362ms
2025-06-30 11:28:26.077 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:28:26.077 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:28:26.083 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 5.7723ms
2025-06-30 11:28:26.093 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 14.8088ms
2025-06-30 11:28:48.106 +06:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-30 11:28:51.167 +06:00 [INF] Now listening on: https://localhost:7029
2025-06-30 11:28:51.170 +06:00 [INF] Now listening on: http://localhost:5045
2025-06-30 11:28:51.265 +06:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-30 11:28:51.268 +06:00 [INF] Hosting environment: Development
2025-06-30 11:28:51.269 +06:00 [INF] Content root path: D:\TGI\PM.Tool\PM.Tool
2025-06-30 11:28:51.313 +06:00 [INF] Executed DbCommand (78ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE t."DueDate" IS NOT NULL AND t."DueDate" <= @__upcomingDeadlines_0 AND t."DueDate" > now() AND t."Status" <> 4
2025-06-30 11:28:51.997 +06:00 [INF] Executed DbCommand (17ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?', @p2='?' (DbType = DateTime), @p3='?' (DbType = Int32), @p4='?' (DbType = Int32), @p5='?', @p6='?' (DbType = Int32), @p7='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Notifications" ("CreatedAt", "Message", "ReadAt", "RelatedProjectId", "RelatedTaskId", "Title", "Type", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "IsEmailSent", "IsRead";
2025-06-30 11:28:52.108 +06:00 [INF] Executed DbCommand (11ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", p0."Id", p0."IsActive", p0."JoinedAt", p0."ProjectId", p0."Role", p0."UserId"
FROM "Projects" AS p
LEFT JOIN "ProjectMembers" AS p0 ON p."Id" = p0."ProjectId"
WHERE p."EndDate" IS NOT NULL AND p."EndDate" <= @__upcomingDeadlines_0 AND p."EndDate" > now() AND p."Status" <> 5
ORDER BY p."Id"
2025-06-30 11:28:52.191 +06:00 [INF] Deadline check completed at: "2025-06-30T11:28:52.1904645+06:00"
2025-06-30 11:28:52.517 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/ - null null
2025-06-30 11:28:52.906 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-30 11:28:52.936 +06:00 [INF] Route matched with {action = "Index", controller = "Home", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-30 11:28:53.018 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-30 11:28:53.089 +06:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-30 11:28:53.127 +06:00 [INF] Executed DbCommand (14ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-30 11:28:53.213 +06:00 [INF] Executed DbCommand (11ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-30 11:28:53.242 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" < now()
ORDER BY t."DueDate"
2025-06-30 11:28:53.263 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__dueDate_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
FROM "Tasks" AS t
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" <= @__dueDate_0
2025-06-30 11:28:53.310 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT n."Id", n."Title", n."Message", n."Type", n."IsRead", n."CreatedAt", n."RelatedProjectId", n."RelatedTaskId"
FROM "Notifications" AS n
WHERE n."UserId" = @__user_Id_0
ORDER BY n."CreatedAt" DESC
LIMIT @__p_1
2025-06-30 11:28:53.350 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."Name", m."Description", m."ProjectId", p."Name", m."DueDate", m."CompletedDate", m."IsCompleted", m."CreatedAt", m."IsDeleted", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."StartDate", p."Status", p."UpdatedAt"
FROM "Milestones" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
WHERE EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__user_Id_0 AND p0."IsActive") AND NOT (m."IsCompleted") AND m."DueDate" >= now()
ORDER BY m."DueDate"
LIMIT @__p_1
2025-06-30 11:28:53.380 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-30 11:28:53.716 +06:00 [INF] Executed ViewResult - view Index executed in 341.1875ms.
2025-06-30 11:28:53.728 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.Index (PM.Tool) in 787.6557ms
2025-06-30 11:28:53.733 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-30 11:28:53.748 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/ - 200 null text/html; charset=utf-8 1241.1199ms
2025-06-30 11:28:53.771 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:28:53.771 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:28:53.790 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 14.7139ms
2025-06-30 11:28:53.836 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 65.5675ms
2025-06-30 11:30:22.989 +06:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-30 11:30:25.668 +06:00 [INF] Now listening on: https://localhost:7029
2025-06-30 11:30:25.670 +06:00 [INF] Now listening on: http://localhost:5045
2025-06-30 11:30:25.762 +06:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-30 11:30:25.763 +06:00 [INF] Hosting environment: Development
2025-06-30 11:30:25.766 +06:00 [INF] Content root path: D:\TGI\PM.Tool\PM.Tool
2025-06-30 11:30:25.794 +06:00 [INF] Executed DbCommand (66ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE t."DueDate" IS NOT NULL AND t."DueDate" <= @__upcomingDeadlines_0 AND t."DueDate" > now() AND t."Status" <> 4
2025-06-30 11:30:26.544 +06:00 [INF] Executed DbCommand (18ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?', @p2='?' (DbType = DateTime), @p3='?' (DbType = Int32), @p4='?' (DbType = Int32), @p5='?', @p6='?' (DbType = Int32), @p7='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Notifications" ("CreatedAt", "Message", "ReadAt", "RelatedProjectId", "RelatedTaskId", "Title", "Type", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "IsEmailSent", "IsRead";
2025-06-30 11:30:26.639 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", p0."Id", p0."IsActive", p0."JoinedAt", p0."ProjectId", p0."Role", p0."UserId"
FROM "Projects" AS p
LEFT JOIN "ProjectMembers" AS p0 ON p."Id" = p0."ProjectId"
WHERE p."EndDate" IS NOT NULL AND p."EndDate" <= @__upcomingDeadlines_0 AND p."EndDate" > now() AND p."Status" <> 5
ORDER BY p."Id"
2025-06-30 11:30:26.643 +06:00 [INF] Deadline check completed at: "2025-06-30T11:30:26.6430433+06:00"
2025-06-30 11:30:27.364 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/ - null null
2025-06-30 11:30:27.726 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-30 11:30:27.752 +06:00 [INF] Route matched with {action = "Index", controller = "Home", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-30 11:30:27.809 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-30 11:30:27.849 +06:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-30 11:30:27.897 +06:00 [INF] Executed DbCommand (32ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-30 11:30:27.950 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-30 11:30:27.975 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" < now()
ORDER BY t."DueDate"
2025-06-30 11:30:27.992 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__dueDate_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
FROM "Tasks" AS t
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" <= @__dueDate_0
2025-06-30 11:30:28.021 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT n."Id", n."Title", n."Message", n."Type", n."IsRead", n."CreatedAt", n."RelatedProjectId", n."RelatedTaskId"
FROM "Notifications" AS n
WHERE n."UserId" = @__user_Id_0
ORDER BY n."CreatedAt" DESC
LIMIT @__p_1
2025-06-30 11:30:28.050 +06:00 [INF] Executed DbCommand (11ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."Name", m."Description", m."ProjectId", p."Name", m."DueDate", m."CompletedDate", m."IsCompleted", m."CreatedAt", m."IsDeleted", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."StartDate", p."Status", p."UpdatedAt"
FROM "Milestones" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
WHERE EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__user_Id_0 AND p0."IsActive") AND NOT (m."IsCompleted") AND m."DueDate" >= now()
ORDER BY m."DueDate"
LIMIT @__p_1
2025-06-30 11:30:28.063 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-30 11:30:28.217 +06:00 [INF] Executed ViewResult - view Index executed in 152.0996ms.
2025-06-30 11:30:28.228 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.Index (PM.Tool) in 472.7319ms
2025-06-30 11:30:28.231 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-30 11:30:28.244 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/ - 200 null text/html; charset=utf-8 890.9561ms
2025-06-30 11:30:28.276 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:30:28.276 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:30:28.287 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 10.9966ms
2025-06-30 11:30:28.323 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 47.3068ms
2025-06-30 11:30:36.876 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Tasks/Details/2 - null null
2025-06-30 11:30:36.895 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.TasksController.Details (PM.Tool)'
2025-06-30 11:30:36.912 +06:00 [INF] Route matched with {action = "Details", controller = "Tasks", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Details(Int32) on controller PM.Tool.Controllers.TasksController (PM.Tool).
2025-06-30 11:30:37.036 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-30 11:30:37.091 +06:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-30 11:30:37.154 +06:00 [INF] Executed DbCommand (24ms) [Parameters=[@__taskId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", t0."Id", t0."Content", t0."CreatedAt", t0."TaskId", t0."UpdatedAt", t0."UserId", t1."Id", t1."CategoryId", t1."ContentType", t1."CurrentVersion", t1."Description", t1."FileName", t1."FilePath", t1."FileSize", t1."LastModifiedAt", t1."Tags", t1."TaskId", t1."UploadedAt", t1."UploadedByUserId", t2."Id", t2."ActualHours", t2."AssignedToUserId", t2."CompletedDate", t2."CreatedAt", t2."CreatedByUserId", t2."DeletedAt", t2."Description", t2."DueDate", t2."EstimatedHours", t2."IsDeleted", t2."IsRecurring", t2."IsTemplate", t2."ParentTaskId", t2."Priority", t2."ProjectId", t2."RecurrenceEndDate", t2."RecurrencePattern", t2."SortOrder", t2."StartDate", t2."Status", t2."TemplateId", t2."Title", t2."UpdatedAt", t2."UserStoryId", t2."WbsCode", t2."WbsLevel"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON t."CreatedByUserId" = a0."Id"
LEFT JOIN "TaskComments" AS t0 ON t."Id" = t0."TaskId"
LEFT JOIN "TaskAttachments" AS t1 ON t."Id" = t1."TaskId"
LEFT JOIN "Tasks" AS t2 ON t."Id" = t2."ParentTaskId"
WHERE NOT (t."IsDeleted") AND t."Id" = @__taskId_0
ORDER BY t."Id", p."Id", a."Id", a0."Id", t0."Id", t1."Id"
2025-06-30 11:30:37.203 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__projectId_0='?' (DbType = Int32), @__userId_1='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."IsActive", p."JoinedAt", p."ProjectId", p."Role", p."UserId"
FROM "ProjectMembers" AS p
WHERE p."ProjectId" = @__projectId_0 AND p."UserId" = @__userId_1 AND p."IsActive"
LIMIT 1
2025-06-30 11:30:37.235 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt"
FROM "Projects" AS p
WHERE p."Id" = @__id_0 AND NOT (p."IsDeleted")
LIMIT 1
2025-06-30 11:30:37.270 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__taskId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."Content", t."CreatedAt", t."TaskId", t."UpdatedAt", t."UserId", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "TaskComments" AS t
INNER JOIN "AspNetUsers" AS a ON t."UserId" = a."Id"
WHERE t."TaskId" = @__taskId_0
ORDER BY t."CreatedAt" DESC
2025-06-30 11:30:37.285 +06:00 [INF] Executing ViewResult, running view Details.
2025-06-30 11:30:37.461 +06:00 [INF] Executed ViewResult - view Details executed in 179.921ms.
2025-06-30 11:30:37.464 +06:00 [INF] Executed action PM.Tool.Controllers.TasksController.Details (PM.Tool) in 546.9231ms
2025-06-30 11:30:37.466 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.TasksController.Details (PM.Tool)'
2025-06-30 11:30:37.469 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Tasks/Details/2 - 200 null text/html; charset=utf-8 593.2625ms
2025-06-30 11:30:37.474 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:30:37.474 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:30:37.480 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 6.4198ms
2025-06-30 11:30:37.501 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 27.3842ms
2025-06-30 11:32:55.578 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Tasks/Details/2 - null null
2025-06-30 11:32:55.608 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.TasksController.Details (PM.Tool)'
2025-06-30 11:32:55.615 +06:00 [INF] Route matched with {action = "Details", controller = "Tasks", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Details(Int32) on controller PM.Tool.Controllers.TasksController (PM.Tool).
2025-06-30 11:32:55.633 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-30 11:32:55.655 +06:00 [INF] Executed DbCommand (16ms) [Parameters=[@__taskId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", t0."Id", t0."Content", t0."CreatedAt", t0."TaskId", t0."UpdatedAt", t0."UserId", t1."Id", t1."CategoryId", t1."ContentType", t1."CurrentVersion", t1."Description", t1."FileName", t1."FilePath", t1."FileSize", t1."LastModifiedAt", t1."Tags", t1."TaskId", t1."UploadedAt", t1."UploadedByUserId", t2."Id", t2."ActualHours", t2."AssignedToUserId", t2."CompletedDate", t2."CreatedAt", t2."CreatedByUserId", t2."DeletedAt", t2."Description", t2."DueDate", t2."EstimatedHours", t2."IsDeleted", t2."IsRecurring", t2."IsTemplate", t2."ParentTaskId", t2."Priority", t2."ProjectId", t2."RecurrenceEndDate", t2."RecurrencePattern", t2."SortOrder", t2."StartDate", t2."Status", t2."TemplateId", t2."Title", t2."UpdatedAt", t2."UserStoryId", t2."WbsCode", t2."WbsLevel"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON t."CreatedByUserId" = a0."Id"
LEFT JOIN "TaskComments" AS t0 ON t."Id" = t0."TaskId"
LEFT JOIN "TaskAttachments" AS t1 ON t."Id" = t1."TaskId"
LEFT JOIN "Tasks" AS t2 ON t."Id" = t2."ParentTaskId"
WHERE NOT (t."IsDeleted") AND t."Id" = @__taskId_0
ORDER BY t."Id", p."Id", a."Id", a0."Id", t0."Id", t1."Id"
2025-06-30 11:32:55.665 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__projectId_0='?' (DbType = Int32), @__userId_1='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."IsActive", p."JoinedAt", p."ProjectId", p."Role", p."UserId"
FROM "ProjectMembers" AS p
WHERE p."ProjectId" = @__projectId_0 AND p."UserId" = @__userId_1 AND p."IsActive"
LIMIT 1
2025-06-30 11:32:55.676 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt"
FROM "Projects" AS p
WHERE p."Id" = @__id_0 AND NOT (p."IsDeleted")
LIMIT 1
2025-06-30 11:32:55.690 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__taskId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."Content", t."CreatedAt", t."TaskId", t."UpdatedAt", t."UserId", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "TaskComments" AS t
INNER JOIN "AspNetUsers" AS a ON t."UserId" = a."Id"
WHERE t."TaskId" = @__taskId_0
ORDER BY t."CreatedAt" DESC
2025-06-30 11:32:55.699 +06:00 [INF] Executing ViewResult, running view Details.
2025-06-30 11:32:55.761 +06:00 [INF] Executed ViewResult - view Details executed in 65.5073ms.
2025-06-30 11:32:55.765 +06:00 [INF] Executed action PM.Tool.Controllers.TasksController.Details (PM.Tool) in 146.3736ms
2025-06-30 11:32:55.770 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.TasksController.Details (PM.Tool)'
2025-06-30 11:32:55.772 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Tasks/Details/2 - 200 null text/html; charset=utf-8 194.8282ms
2025-06-30 11:32:55.779 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:32:55.779 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:32:55.786 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 6.8318ms
2025-06-30 11:32:55.811 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 31.294ms
2025-06-30 11:33:07.118 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Tasks/Details/2 - null null
2025-06-30 11:33:07.126 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.TasksController.Details (PM.Tool)'
2025-06-30 11:33:07.129 +06:00 [INF] Route matched with {action = "Details", controller = "Tasks", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Details(Int32) on controller PM.Tool.Controllers.TasksController (PM.Tool).
2025-06-30 11:33:07.140 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-30 11:33:07.165 +06:00 [INF] Executed DbCommand (21ms) [Parameters=[@__taskId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", t0."Id", t0."Content", t0."CreatedAt", t0."TaskId", t0."UpdatedAt", t0."UserId", t1."Id", t1."CategoryId", t1."ContentType", t1."CurrentVersion", t1."Description", t1."FileName", t1."FilePath", t1."FileSize", t1."LastModifiedAt", t1."Tags", t1."TaskId", t1."UploadedAt", t1."UploadedByUserId", t2."Id", t2."ActualHours", t2."AssignedToUserId", t2."CompletedDate", t2."CreatedAt", t2."CreatedByUserId", t2."DeletedAt", t2."Description", t2."DueDate", t2."EstimatedHours", t2."IsDeleted", t2."IsRecurring", t2."IsTemplate", t2."ParentTaskId", t2."Priority", t2."ProjectId", t2."RecurrenceEndDate", t2."RecurrencePattern", t2."SortOrder", t2."StartDate", t2."Status", t2."TemplateId", t2."Title", t2."UpdatedAt", t2."UserStoryId", t2."WbsCode", t2."WbsLevel"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON t."CreatedByUserId" = a0."Id"
LEFT JOIN "TaskComments" AS t0 ON t."Id" = t0."TaskId"
LEFT JOIN "TaskAttachments" AS t1 ON t."Id" = t1."TaskId"
LEFT JOIN "Tasks" AS t2 ON t."Id" = t2."ParentTaskId"
WHERE NOT (t."IsDeleted") AND t."Id" = @__taskId_0
ORDER BY t."Id", p."Id", a."Id", a0."Id", t0."Id", t1."Id"
2025-06-30 11:33:07.180 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__projectId_0='?' (DbType = Int32), @__userId_1='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."IsActive", p."JoinedAt", p."ProjectId", p."Role", p."UserId"
FROM "ProjectMembers" AS p
WHERE p."ProjectId" = @__projectId_0 AND p."UserId" = @__userId_1 AND p."IsActive"
LIMIT 1
2025-06-30 11:33:07.194 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt"
FROM "Projects" AS p
WHERE p."Id" = @__id_0 AND NOT (p."IsDeleted")
LIMIT 1
2025-06-30 11:33:07.252 +06:00 [INF] Executed DbCommand (52ms) [Parameters=[@__taskId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."Content", t."CreatedAt", t."TaskId", t."UpdatedAt", t."UserId", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "TaskComments" AS t
INNER JOIN "AspNetUsers" AS a ON t."UserId" = a."Id"
WHERE t."TaskId" = @__taskId_0
ORDER BY t."CreatedAt" DESC
2025-06-30 11:33:07.257 +06:00 [INF] Executing ViewResult, running view Details.
2025-06-30 11:33:07.263 +06:00 [INF] Executed ViewResult - view Details executed in 6.0719ms.
2025-06-30 11:33:07.266 +06:00 [INF] Executed action PM.Tool.Controllers.TasksController.Details (PM.Tool) in 136.1746ms
2025-06-30 11:33:07.269 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.TasksController.Details (PM.Tool)'
2025-06-30 11:33:07.271 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Tasks/Details/2 - 200 null text/html; charset=utf-8 153.2264ms
2025-06-30 11:33:07.275 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:33:07.275 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:33:07.279 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 4.2209ms
2025-06-30 11:33:07.286 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 11.2759ms
2025-06-30 11:33:48.388 +06:00 [INF] Request starting HTTP/2 POST https://localhost:7029/Comment/Create - application/x-www-form-urlencoded; charset=UTF-8 275
2025-06-30 11:33:48.393 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.CommentController.Create (PM.Tool)'
2025-06-30 11:33:48.406 +06:00 [INF] Route matched with {action = "Create", controller = "Comment", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Create(PM.Tool.Models.ViewModels.CommentViewModel) on controller PM.Tool.Controllers.CommentController (PM.Tool).
2025-06-30 11:33:48.508 +06:00 [INF] Executed DbCommand (19ms) [Parameters=[@__model_TaskId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt"
FROM "Tasks" AS t
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
WHERE t."Id" = @__model_TaskId_0
LIMIT 1
2025-06-30 11:33:48.571 +06:00 [INF] Executed DbCommand (14ms) [Parameters=[@p0='?', @p1='?', @p2='?' (DbType = DateTime), @p3='?' (DbType = DateTime), @p4='?' (DbType = Boolean), @p5='?' (DbType = Int32), @p6='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Comments" ("AuthorId", "Content", "CreatedAt", "DeletedAt", "IsDeleted", "TaskId", "UpdatedAt")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6)
RETURNING "Id";
2025-06-30 11:33:48.671 +06:00 [INF] Executed DbCommand (15ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = DateTime), @p2='?' (DbType = Int32), @p3='?', @p4='?', @p5='?', @p6='?', @p7='?', @p8='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AuditLogs" ("Action", "CreatedAt", "EntityId", "EntityName", "IpAddress", "NewValues", "OldValues", "UserAgent", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8)
RETURNING "Id";
2025-06-30 11:33:48.704 +06:00 [INF] Executing JsonResult, writing value of type '<>f__AnonymousType91`4[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-30 11:33:48.745 +06:00 [INF] Executed action PM.Tool.Controllers.CommentController.Create (PM.Tool) in 337.1367ms
2025-06-30 11:33:48.750 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.CommentController.Create (PM.Tool)'
2025-06-30 11:33:48.754 +06:00 [INF] Request finished HTTP/2 POST https://localhost:7029/Comment/Create - 200 null application/json; charset=utf-8 366.3137ms
2025-06-30 11:33:59.698 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Tasks/Details/2 - null null
2025-06-30 11:33:59.707 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.TasksController.Details (PM.Tool)'
2025-06-30 11:33:59.710 +06:00 [INF] Route matched with {action = "Details", controller = "Tasks", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Details(Int32) on controller PM.Tool.Controllers.TasksController (PM.Tool).
2025-06-30 11:33:59.725 +06:00 [INF] Executed DbCommand (11ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-30 11:33:59.765 +06:00 [INF] Executed DbCommand (36ms) [Parameters=[@__taskId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", t0."Id", t0."Content", t0."CreatedAt", t0."TaskId", t0."UpdatedAt", t0."UserId", t1."Id", t1."CategoryId", t1."ContentType", t1."CurrentVersion", t1."Description", t1."FileName", t1."FilePath", t1."FileSize", t1."LastModifiedAt", t1."Tags", t1."TaskId", t1."UploadedAt", t1."UploadedByUserId", t2."Id", t2."ActualHours", t2."AssignedToUserId", t2."CompletedDate", t2."CreatedAt", t2."CreatedByUserId", t2."DeletedAt", t2."Description", t2."DueDate", t2."EstimatedHours", t2."IsDeleted", t2."IsRecurring", t2."IsTemplate", t2."ParentTaskId", t2."Priority", t2."ProjectId", t2."RecurrenceEndDate", t2."RecurrencePattern", t2."SortOrder", t2."StartDate", t2."Status", t2."TemplateId", t2."Title", t2."UpdatedAt", t2."UserStoryId", t2."WbsCode", t2."WbsLevel"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON t."CreatedByUserId" = a0."Id"
LEFT JOIN "TaskComments" AS t0 ON t."Id" = t0."TaskId"
LEFT JOIN "TaskAttachments" AS t1 ON t."Id" = t1."TaskId"
LEFT JOIN "Tasks" AS t2 ON t."Id" = t2."ParentTaskId"
WHERE NOT (t."IsDeleted") AND t."Id" = @__taskId_0
ORDER BY t."Id", p."Id", a."Id", a0."Id", t0."Id", t1."Id"
2025-06-30 11:33:59.780 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__projectId_0='?' (DbType = Int32), @__userId_1='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."IsActive", p."JoinedAt", p."ProjectId", p."Role", p."UserId"
FROM "ProjectMembers" AS p
WHERE p."ProjectId" = @__projectId_0 AND p."UserId" = @__userId_1 AND p."IsActive"
LIMIT 1
2025-06-30 11:33:59.792 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__id_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt"
FROM "Projects" AS p
WHERE p."Id" = @__id_0 AND NOT (p."IsDeleted")
LIMIT 1
2025-06-30 11:33:59.801 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__taskId_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."Content", t."CreatedAt", t."TaskId", t."UpdatedAt", t."UserId", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "TaskComments" AS t
INNER JOIN "AspNetUsers" AS a ON t."UserId" = a."Id"
WHERE t."TaskId" = @__taskId_0
ORDER BY t."CreatedAt" DESC
2025-06-30 11:33:59.805 +06:00 [INF] Executing ViewResult, running view Details.
2025-06-30 11:33:59.810 +06:00 [INF] Executed ViewResult - view Details executed in 5.0055ms.
2025-06-30 11:33:59.821 +06:00 [INF] Executed action PM.Tool.Controllers.TasksController.Details (PM.Tool) in 108.7083ms
2025-06-30 11:33:59.822 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.TasksController.Details (PM.Tool)'
2025-06-30 11:33:59.822 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 11:33:59.823 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Tasks/Details/2 - 200 null text/html; charset=utf-8 124.8562ms
2025-06-30 11:33:59.822 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 11:33:59.834 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 11.8029ms
2025-06-30 11:33:59.844 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 22.1154ms
2025-06-30 14:07:00.537 +06:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-30 14:07:03.672 +06:00 [INF] Now listening on: https://localhost:7029
2025-06-30 14:07:03.674 +06:00 [INF] Now listening on: http://localhost:5045
2025-06-30 14:07:03.772 +06:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-30 14:07:03.774 +06:00 [INF] Hosting environment: Development
2025-06-30 14:07:03.775 +06:00 [INF] Content root path: D:\TGI\PM.Tool\PM.Tool
2025-06-30 14:07:03.800 +06:00 [INF] Executed DbCommand (66ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE t."DueDate" IS NOT NULL AND t."DueDate" <= @__upcomingDeadlines_0 AND t."DueDate" > now() AND t."Status" <> 4
2025-06-30 14:07:04.372 +06:00 [INF] Executed DbCommand (12ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?', @p2='?' (DbType = DateTime), @p3='?' (DbType = Int32), @p4='?' (DbType = Int32), @p5='?', @p6='?' (DbType = Int32), @p7='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "Notifications" ("CreatedAt", "Message", "ReadAt", "RelatedProjectId", "RelatedTaskId", "Title", "Type", "UserId")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7)
RETURNING "Id", "IsEmailSent", "IsRead";
2025-06-30 14:07:04.455 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", p0."Id", p0."IsActive", p0."JoinedAt", p0."ProjectId", p0."Role", p0."UserId"
FROM "Projects" AS p
LEFT JOIN "ProjectMembers" AS p0 ON p."Id" = p0."ProjectId"
WHERE p."EndDate" IS NOT NULL AND p."EndDate" <= @__upcomingDeadlines_0 AND p."EndDate" > now() AND p."Status" <> 5
ORDER BY p."Id"
2025-06-30 14:07:04.458 +06:00 [INF] Deadline check completed at: "2025-06-30T14:07:04.4585490+06:00"
2025-06-30 14:07:05.365 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/ - null null
2025-06-30 14:07:05.817 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-30 14:07:05.851 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__user_Id_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ClaimType", a."ClaimValue", a."UserId"
FROM "AspNetUserClaims" AS a
WHERE a."UserId" = @__user_Id_0
2025-06-30 14:07:05.873 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a0."Name"
FROM "AspNetUserRoles" AS a
INNER JOIN "AspNetRoles" AS a0 ON a."RoleId" = a0."Id"
WHERE a."UserId" = @__userId_0
2025-06-30 14:07:05.891 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-30 14:07:05.917 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__role_Id_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."ClaimType", a."ClaimValue"
FROM "AspNetRoleClaims" AS a
WHERE a."RoleId" = @__role_Id_0
2025-06-30 14:07:05.938 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-30 14:07:05.957 +06:00 [INF] Route matched with {action = "Index", controller = "Home", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-30 14:07:06.007 +06:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-30 14:07:06.042 +06:00 [INF] Executed DbCommand (17ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-30 14:07:06.096 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-30 14:07:06.144 +06:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" < now()
ORDER BY t."DueDate"
2025-06-30 14:07:06.163 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__dueDate_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
FROM "Tasks" AS t
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" <= @__dueDate_0
2025-06-30 14:07:06.193 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT n."Id", n."Title", n."Message", n."Type", n."IsRead", n."CreatedAt", n."RelatedProjectId", n."RelatedTaskId"
FROM "Notifications" AS n
WHERE n."UserId" = @__user_Id_0
ORDER BY n."CreatedAt" DESC
LIMIT @__p_1
2025-06-30 14:07:06.216 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."Name", m."Description", m."ProjectId", p."Name", m."DueDate", m."CompletedDate", m."IsCompleted", m."CreatedAt", m."IsDeleted", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."StartDate", p."Status", p."UpdatedAt"
FROM "Milestones" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
WHERE EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__user_Id_0 AND p0."IsActive") AND NOT (m."IsCompleted") AND m."DueDate" >= now()
ORDER BY m."DueDate"
LIMIT @__p_1
2025-06-30 14:07:06.231 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-30 14:07:06.421 +06:00 [INF] Executed ViewResult - view Index executed in 191.0551ms.
2025-06-30 14:07:06.436 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.Index (PM.Tool) in 475.0642ms
2025-06-30 14:07:06.439 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-30 14:07:06.451 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/ - 200 null text/html; charset=utf-8 1098.2731ms
2025-06-30 14:07:06.490 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-30 14:07:06.490 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-30 14:07:06.544 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 54.5262ms
2025-06-30 14:07:06.594 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 104.2438ms
