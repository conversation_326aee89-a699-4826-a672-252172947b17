@model PM.Tool.Core.Entities.Agile.Epic
@{
    ViewData["Title"] = $"Epic: {Model.Title}";
    var project = ViewBag.Project as PM.Tool.Core.Entities.Project;
    var userStories = ViewBag.UserStories as IEnumerable<PM.Tool.Core.Entities.Agile.UserStory> ?? new List<PM.Tool.Core.Entities.Agile.UserStory>();
    var totalStoryPoints = ViewBag.TotalStoryPoints as decimal? ?? 0;
    var completedStoryPoints = ViewBag.CompletedStoryPoints as decimal? ?? 0;
    var progressPercentage = ViewBag.ProgressPercentage as decimal? ?? 0;
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="@Url.Action("Index", "Projects")" class="text-neutral-500 hover:text-neutral-700 dark:text-dark-400 dark:hover:text-dark-200">
                            <i class="fas fa-project-diagram mr-2"></i>Projects
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-neutral-400 dark:text-dark-500 mx-2"></i>
                            <a href="@Url.Action("Details", "Projects", new { id = project?.Id })" class="text-neutral-500 hover:text-neutral-700 dark:text-dark-400 dark:hover:text-dark-200">
                                @project?.Name
                            </a>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-neutral-400 dark:text-dark-500 mx-2"></i>
                            <a href="@Url.Action("Epics", new { projectId = project?.Id })" class="text-neutral-500 hover:text-neutral-700 dark:text-dark-400 dark:hover:text-dark-200">
                                Epics
                            </a>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-neutral-400 dark:text-dark-500 mx-2"></i>
                            <span class="text-neutral-700 dark:text-dark-300">@Model.EpicKey</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <div class="flex items-center space-x-4 mb-2">
                <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100">@Model.Title</h1>
                @{
                    var statusColor = Model.Status.ToString().ToLower() switch {
                        "draft" => "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300",
                        "ready" => "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300",
                        "inprogress" => "bg-warning-100 text-warning-700 dark:bg-warning-900 dark:text-warning-300",
                        "done" => "bg-success-100 text-success-700 dark:bg-success-900 dark:text-success-300",
                        "cancelled" => "bg-danger-100 text-danger-700 dark:bg-danger-900 dark:text-danger-300",
                        _ => "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300"
                    };
                    var priorityColor = Model.Priority.ToString().ToLower() switch {
                        "critical" => "bg-danger-100 text-danger-700 dark:bg-danger-900 dark:text-danger-300",
                        "high" => "bg-warning-100 text-warning-700 dark:bg-warning-900 dark:text-warning-300",
                        "medium" => "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300",
                        "low" => "bg-success-100 text-success-700 dark:bg-success-900 dark:text-success-300",
                        _ => "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300"
                    };
                }
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @statusColor">
                    @Model.Status
                </span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @priorityColor">
                    @Model.Priority Priority
                </span>
            </div>

            <p class="text-sm text-neutral-500 dark:text-dark-400">
                Epic @Model.EpicKey • Created @Model.CreatedAt.ToString("MMM dd, yyyy")
                @if (Model.TargetDate.HasValue)
                {
                    <span> • Target: @Model.TargetDate.Value.ToString("MMM dd, yyyy")</span>
                }
            </p>
        </div>

        <div class="mt-4 sm:mt-0 flex space-x-3">
            @{
                ViewData["Text"] = "Edit Epic";
                ViewData["Variant"] = "outline";
                ViewData["Icon"] = "fas fa-edit";
                ViewData["Href"] = Url.Action("EditEpic", new { id = Model.Id });
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "Add User Story";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-plus";
                ViewData["Href"] = Url.Action("CreateUserStory", new { projectId = Model.ProjectId, epicId = Model.Id });
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Epic Overview Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <!-- Progress Card -->
    <div class="bg-white dark:bg-surface-dark border border-neutral-200 dark:border-dark-200 rounded-xl p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-sm font-medium text-neutral-500 dark:text-dark-400">Progress</h3>
            <i class="fas fa-chart-line text-primary-600 dark:text-primary-400"></i>
        </div>
        <div class="text-2xl font-bold text-neutral-900 dark:text-dark-100 mb-2">@progressPercentage.ToString("F1")%</div>
        <div class="w-full bg-neutral-200 dark:bg-dark-600 rounded-full h-2">
            <div class="bg-primary-600 h-2 rounded-full transition-all duration-300" style="width: @progressPercentage%"></div>
        </div>
    </div>

    <!-- Story Points Card -->
    <div class="bg-white dark:bg-surface-dark border border-neutral-200 dark:border-dark-200 rounded-xl p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-sm font-medium text-neutral-500 dark:text-dark-400">Story Points</h3>
            <i class="fas fa-tasks text-success-600 dark:text-success-400"></i>
        </div>
        <div class="text-2xl font-bold text-neutral-900 dark:text-dark-100">@completedStoryPoints/@totalStoryPoints</div>
        <p class="text-sm text-neutral-500 dark:text-dark-400">Completed/Total</p>
    </div>

    <!-- User Stories Card -->
    <div class="bg-white dark:bg-surface-dark border border-neutral-200 dark:border-dark-200 rounded-xl p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-sm font-medium text-neutral-500 dark:text-dark-400">User Stories</h3>
            <i class="fas fa-list text-warning-600 dark:text-warning-400"></i>
        </div>
        <div class="text-2xl font-bold text-neutral-900 dark:text-dark-100">@userStories.Count(us => us.IsCompleted)/@userStories.Count()</div>
        <p class="text-sm text-neutral-500 dark:text-dark-400">Completed/Total</p>
    </div>

    <!-- Business Value Card -->
    <div class="bg-white dark:bg-surface-dark border border-neutral-200 dark:border-dark-200 rounded-xl p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-sm font-medium text-neutral-500 dark:text-dark-400">Business Value</h3>
            <i class="fas fa-dollar-sign text-info-600 dark:text-info-400"></i>
        </div>
        <div class="text-2xl font-bold text-neutral-900 dark:text-dark-100">
            @(Model.EstimatedStoryPoints > 0 ? Model.EstimatedStoryPoints.ToString("F0") : "TBD")
        </div>
        <p class="text-sm text-neutral-500 dark:text-dark-400">Estimated Points</p>
    </div>
</div>

<!-- Epic Details -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-8">
        <!-- Description -->
        @{
            ViewData["Title"] = "Description";
            ViewData["Icon"] = "fas fa-align-left";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="prose dark:prose-invert max-w-none">
                @if (!string.IsNullOrEmpty(Model.Description))
                {
                    <p class="text-neutral-700 dark:text-dark-300 leading-relaxed">@Model.Description</p>
                }
                else
                {
                    <p class="text-neutral-500 dark:text-dark-400 italic">No description provided.</p>
                }
            </div>
        </partial>

        <!-- Acceptance Criteria -->
        @if (!string.IsNullOrEmpty(Model.AcceptanceCriteria))
        {
            ViewData["Title"] = "Acceptance Criteria";
            ViewData["Icon"] = "fas fa-check-circle";
            <partial name="Components/_Card" view-data="ViewData">
                <div class="prose dark:prose-invert max-w-none">
                    <p class="text-neutral-700 dark:text-dark-300 leading-relaxed">@Model.AcceptanceCriteria</p>
                </div>
            </partial>
        }

        <!-- Business Value -->
        @if (!string.IsNullOrEmpty(Model.BusinessValue))
        {
            ViewData["Title"] = "Business Value";
            ViewData["Icon"] = "fas fa-chart-line";
            <partial name="Components/_Card" view-data="ViewData">
                <div class="prose dark:prose-invert max-w-none">
                    <p class="text-neutral-700 dark:text-dark-300 leading-relaxed">@Model.BusinessValue</p>
                </div>
            </partial>
        }

        <!-- User Stories -->
        @{
            ViewData["Title"] = $"User Stories ({userStories.Count()})";
            ViewData["Icon"] = "fas fa-list";
        }
        <partial name="Components/_Card" view-data="ViewData">
            @if (userStories.Any())
            {
                <div class="space-y-4">
                    @foreach (var story in userStories.OrderBy(us => us.Priority))
                    {
                        <div class="border border-neutral-200 dark:border-dark-200 rounded-lg p-4 hover:shadow-sm transition-shadow">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3 mb-2">
                                        <h4 class="font-medium text-neutral-900 dark:text-dark-100">@story.Title</h4>
                                        @{
                                            var storyStatusColor = story.Status.ToString().ToLower() switch {
                                                "backlog" => "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300",
                                                "ready" => "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300",
                                                "inprogress" => "bg-warning-100 text-warning-700 dark:bg-warning-900 dark:text-warning-300",
                                                "review" => "bg-info-100 text-info-700 dark:bg-info-900 dark:text-info-300",
                                                "done" => "bg-success-100 text-success-700 dark:bg-success-900 dark:text-success-300",
                                                _ => "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300"
                                            };
                                        }
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium @storyStatusColor">
                                            @story.Status
                                        </span>
                                        @if (story.StoryPoints > 0)
                                        {
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300">
                                                @story.StoryPoints SP
                                            </span>
                                        }
                                    </div>
                                    <p class="text-sm text-neutral-600 dark:text-dark-400 mb-2">@story.UserStoryFormat</p>
                                    @if (story.AssignedTo != null)
                                    {
                                        <p class="text-xs text-neutral-500 dark:text-dark-400">
                                            Assigned to: @story.AssignedTo.UserName
                                        </p>
                                    }
                                </div>
                                <div class="flex space-x-2">
                                    @{
                                        ViewData["Text"] = "";
                                        ViewData["Variant"] = "outline";
                                        ViewData["Size"] = "sm";
                                        ViewData["Icon"] = "fas fa-eye";
                                        ViewData["Href"] = Url.Action("UserStoryDetails", new { id = story.Id });
                                        ViewData["AriaLabel"] = "View User Story";
                                        ViewData["Title"] = "View Details";
                                    }
                                    <partial name="Components/_Button" view-data="ViewData" />
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-list text-2xl text-neutral-400 dark:text-dark-500"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">No User Stories</h3>
                    <p class="text-neutral-500 dark:text-dark-400 mb-6">This epic doesn't have any user stories yet.</p>
                    @{
                        ViewData["Text"] = "Add First User Story";
                        ViewData["Variant"] = "primary";
                        ViewData["Icon"] = "fas fa-plus";
                        ViewData["Href"] = Url.Action("CreateUserStory", new { projectId = Model.ProjectId, epicId = Model.Id });
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </div>
            }
        </partial>
    </div>

    <!-- Sidebar -->
    <div class="space-y-6">
        <!-- Epic Information -->
        @{
            ViewData["Title"] = "Epic Information";
            ViewData["Icon"] = "fas fa-info-circle";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <dl class="space-y-4">
                <div>
                    <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">Epic Key</dt>
                    <dd class="text-sm text-neutral-900 dark:text-dark-100 font-mono">@Model.EpicKey</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">Status</dt>
                    <dd class="text-sm text-neutral-900 dark:text-dark-100">@Model.Status</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">Priority</dt>
                    <dd class="text-sm text-neutral-900 dark:text-dark-100">@Model.Priority</dd>
                </div>
                @if (Model.Owner != null)
                {
                    <div>
                        <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">Owner</dt>
                        <dd class="text-sm text-neutral-900 dark:text-dark-100">@Model.Owner.UserName</dd>
                    </div>
                }
                <div>
                    <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">Created</dt>
                    <dd class="text-sm text-neutral-900 dark:text-dark-100">@Model.CreatedAt.ToString("MMM dd, yyyy")</dd>
                </div>
                @if (Model.UpdatedAt.HasValue)
                {
                    <div>
                        <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">Last Updated</dt>
                        <dd class="text-sm text-neutral-900 dark:text-dark-100">@Model.UpdatedAt.Value.ToString("MMM dd, yyyy")</dd>
                    </div>
                }
                @if (Model.TargetDate.HasValue)
                {
                    <div>
                        <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">Target Date</dt>
                        <dd class="text-sm text-neutral-900 dark:text-dark-100">@Model.TargetDate.Value.ToString("MMM dd, yyyy")</dd>
                    </div>
                }
            </dl>
        </partial>

        <!-- Actions -->
        @{
            ViewData["Title"] = "Actions";
            ViewData["Icon"] = "fas fa-cog";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="space-y-3">
                @{
                    ViewData["Text"] = "Edit Epic";
                    ViewData["Variant"] = "outline";
                    ViewData["Icon"] = "fas fa-edit";
                    ViewData["Href"] = Url.Action("EditEpic", new { id = Model.Id });
                    ViewData["Classes"] = "w-full justify-center";
                }
                <partial name="Components/_Button" view-data="ViewData" />

                @{
                    ViewData["Text"] = "Add User Story";
                    ViewData["Variant"] = "primary";
                    ViewData["Icon"] = "fas fa-plus";
                    ViewData["Href"] = Url.Action("CreateUserStory", new { projectId = Model.ProjectId, epicId = Model.Id });
                    ViewData["Classes"] = "w-full justify-center";
                }
                <partial name="Components/_Button" view-data="ViewData" />

                <form asp-action="DeleteEpic" asp-route-id="@Model.Id" method="post" class="delete-form">
                    @Html.AntiForgeryToken()
                    @{
                        ViewData["Text"] = "Delete Epic";
                        ViewData["Variant"] = "danger";
                        ViewData["Icon"] = "fas fa-trash";
                        ViewData["Type"] = "submit";
                        ViewData["Classes"] = "w-full justify-center";
                        ViewData["OnClick"] = "return confirm('Are you sure you want to delete this epic? This action cannot be undone.')";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </form>
            </div>
        </partial>
    </div>
</div>
