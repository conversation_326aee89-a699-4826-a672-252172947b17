@model dynamic

@{
    var modalId = ViewData["ModalId"]?.ToString() ?? "modal";
    var title = ViewData["ModalTitle"]?.ToString() ?? "Modal";
    var size = ViewData["ModalSize"]?.ToString() ?? "md"; // sm, md, lg, xl
    var showFooter = ViewData["ShowFooter"] as bool? ?? true;
    var footerContent = ViewData["FooterContent"]?.ToString();
    var bodyContent = ViewData["BodyContent"]?.ToString() ?? "";

    var sizeClasses = size switch {
        "sm" => "max-w-md",
        "md" => "max-w-lg",
        "lg" => "max-w-2xl",
        "xl" => "max-w-4xl",
        "full" => "max-w-7xl",
        _ => "max-w-lg"
    };
}

<!-- Modal -->
<div id="@modalId" class="modal-custom hidden" role="dialog" aria-labelledby="@(modalId)-title" aria-hidden="true">
    <!-- Backdrop -->
    <div class="modal-backdrop" onclick="closeModal('@modalId')"></div>

    <!-- Modal Content -->
    <div class="fixed inset-0 z-50 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4">
            <div class="@sizeClasses w-full bg-white dark:bg-surface-dark rounded-xl shadow-strong border border-neutral-200 dark:border-dark-200 transform transition-all duration-300 scale-95 opacity-0" id="@(modalId)-content">

                <!-- Header -->
                <div class="modal-header-custom">
                    <h3 id="@(modalId)-title" class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                        @title
                    </h3>
                    <button type="button"
                            class="text-neutral-400 dark:text-dark-500 hover:text-neutral-600 dark:hover:text-dark-300 transition-colors"
                            onclick="closeModal('@modalId')"
                            aria-label="Close modal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <!-- Body -->
                <div class="modal-body-custom">
                    @if (!string.IsNullOrEmpty(bodyContent))
                    {
                        @Html.Raw(bodyContent)
                    }
                </div>

                <!-- Footer -->
                @if (showFooter)
                {
                    <div class="modal-footer-custom">
                        @if (!string.IsNullOrEmpty(footerContent))
                        {
                            @Html.Raw(footerContent)
                        }
                        else
                        {
                            <button type="button" class="btn-secondary-custom" onclick="closeModal('@modalId')">
                                Cancel
                            </button>
                            <button type="button" class="btn-primary-custom">
                                Confirm
                            </button>
                        }
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<script>
    function openModal(modalId) {
        const modal = document.getElementById(modalId);
        const content = document.getElementById(modalId + '-content');

        if (modal && content) {
            modal.classList.remove('hidden');
            modal.setAttribute('aria-hidden', 'false');

            // Trigger animation
            setTimeout(() => {
                content.classList.remove('scale-95', 'opacity-0');
                content.classList.add('scale-100', 'opacity-100');
            }, 10);

            // Focus management
            const firstFocusable = content.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
            if (firstFocusable) {
                firstFocusable.focus();
            }

            // Prevent body scroll
            document.body.style.overflow = 'hidden';
        }
    }

    function closeModal(modalId) {
        const modal = document.getElementById(modalId);
        const content = document.getElementById(modalId + '-content');

        if (modal && content) {
            // Trigger animation
            content.classList.add('scale-95', 'opacity-0');
            content.classList.remove('scale-100', 'opacity-100');

            setTimeout(() => {
                modal.classList.add('hidden');
                modal.setAttribute('aria-hidden', 'true');

                // Restore body scroll
                document.body.style.overflow = '';
            }, 300);
        }
    }

    // Close modal on Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const openModals = document.querySelectorAll('.modal-custom:not(.hidden)');
            openModals.forEach(modal => {
                closeModal(modal.id);
            });
        }
    });
</script>
