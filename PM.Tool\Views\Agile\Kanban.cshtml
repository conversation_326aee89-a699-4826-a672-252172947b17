@model IEnumerable<PM.Tool.Core.Entities.Agile.UserStory>
@{
    ViewData["Title"] = "Kanban Board";
    var project = ViewBag.Project as PM.Tool.Core.Entities.Project;
    var projectId = ViewBag.ProjectId as int? ?? 0;
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="@Url.Action("Index", "Projects")" class="text-neutral-500 hover:text-neutral-700 dark:text-dark-400 dark:hover:text-dark-200">
                            <i class="fas fa-project-diagram mr-2"></i>Projects
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-neutral-400 dark:text-dark-500 mx-2"></i>
                            <a href="@Url.Action("Details", "Projects", new { id = project?.Id })" class="text-neutral-500 hover:text-neutral-700 dark:text-dark-400 dark:hover:text-dark-200">
                                @project?.Name
                            </a>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-neutral-400 dark:text-dark-500 mx-2"></i>
                            <span class="text-neutral-700 dark:text-dark-300">Kanban Board</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-columns mr-3 text-primary-600 dark:text-primary-400"></i>
                Kanban Board
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Visualize and manage work in progress for @project?.Name
            </p>
        </div>

        <div class="mt-4 sm:mt-0 flex space-x-3">
            @{
                ViewData["Text"] = "Add User Story";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-plus";
                ViewData["Href"] = Url.Action("CreateUserStory", new { projectId });
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "Board Settings";
                ViewData["Variant"] = "outline";
                ViewData["Icon"] = "fas fa-cog";
                ViewData["OnClick"] = "openBoardSettings()";
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Sprint Selection -->
@{
    ViewData["Title"] = "Sprint Selection";
    ViewData["Icon"] = "fas fa-clock";
}
<partial name="Components/_Card" view-data="ViewData">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 items-center">
        <div>
            <label for="sprintSelect" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">
                Current Sprint:
            </label>
            <select id="sprintSelect" class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-800 dark:text-dark-100">
                <option value="">Select Sprint...</option>
                <!-- Options will be populated via AJAX -->
            </select>
        </div>
        <div class="flex items-center justify-end space-x-4">
            <div class="text-right">
                <p class="text-sm font-medium text-neutral-700 dark:text-dark-300">Sprint Progress</p>
                <p class="text-lg font-bold text-primary-600 dark:text-primary-400" id="sprintProgress">0%</p>
            </div>
            <div class="w-32">
                <div class="w-full bg-neutral-200 dark:bg-dark-600 rounded-full h-2">
                    <div id="sprintProgressBar" class="bg-primary-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
            </div>
        </div>
    </div>
</partial>

<!-- Kanban Board -->
<div class="kanban-board overflow-x-auto">
    <div class="flex space-x-6 min-w-max pb-6">
        <!-- Backlog Column -->
        <div class="kanban-column flex-shrink-0 w-80" data-status="Backlog">
            <div class="bg-white dark:bg-surface-dark border border-neutral-200 dark:border-dark-200 rounded-t-xl p-4">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="font-semibold text-neutral-900 dark:text-dark-100 flex items-center">
                        <div class="w-3 h-3 rounded-full mr-2 bg-neutral-500"></div>
                        Backlog
                    </h3>
                    <span class="text-sm text-neutral-500 dark:text-dark-400 bg-neutral-100 dark:bg-dark-700 px-2 py-1 rounded-full" id="backlogCount">0</span>
                </div>
                <p class="text-sm text-neutral-600 dark:text-dark-400">Stories ready for development</p>
            </div>
            <div class="kanban-cards bg-neutral-50 dark:bg-dark-800 border-l border-r border-b border-neutral-200 dark:border-dark-200 rounded-b-xl min-h-96 p-4 space-y-4"
                 id="backlogColumn" data-status="Backlog">
                <!-- User stories will be loaded here -->
            </div>
        </div>

        <!-- Ready Column -->
        <div class="kanban-column flex-shrink-0 w-80" data-status="Ready">
            <div class="bg-white dark:bg-surface-dark border border-neutral-200 dark:border-dark-200 rounded-t-xl p-4">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="font-semibold text-neutral-900 dark:text-dark-100 flex items-center">
                        <div class="w-3 h-3 rounded-full mr-2 bg-primary-500"></div>
                        Ready
                    </h3>
                    <span class="text-sm text-neutral-500 dark:text-dark-400 bg-neutral-100 dark:bg-dark-700 px-2 py-1 rounded-full" id="readyCount">0</span>
                </div>
                <p class="text-sm text-neutral-600 dark:text-dark-400">Stories ready to start</p>
            </div>
            <div class="kanban-cards bg-neutral-50 dark:bg-dark-800 border-l border-r border-b border-neutral-200 dark:border-dark-200 rounded-b-xl min-h-96 p-4 space-y-4"
                 id="readyColumn" data-status="Ready">
                <!-- User stories will be loaded here -->
            </div>
        </div>

        <!-- In Progress Column -->
        <div class="kanban-column flex-shrink-0 w-80" data-status="InProgress">
            <div class="bg-white dark:bg-surface-dark border border-neutral-200 dark:border-dark-200 rounded-t-xl p-4">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="font-semibold text-neutral-900 dark:text-dark-100 flex items-center">
                        <div class="w-3 h-3 rounded-full mr-2 bg-warning-500"></div>
                        In Progress
                    </h3>
                    <span class="text-sm text-neutral-500 dark:text-dark-400 bg-neutral-100 dark:bg-dark-700 px-2 py-1 rounded-full" id="inprogressCount">0</span>
                </div>
                <p class="text-sm text-neutral-600 dark:text-dark-400">Work in progress</p>
                <div class="mt-2">
                    <div class="flex items-center justify-between text-xs">
                        <span class="text-neutral-500 dark:text-dark-400">WIP Limit</span>
                        <span class="text-neutral-600 dark:text-dark-400" id="wipStatus">0 / 3</span>
                    </div>
                    <div class="w-full bg-neutral-200 dark:bg-dark-600 rounded-full h-1 mt-1">
                        <div id="wipProgressBar" class="bg-warning-500 h-1 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                </div>
            </div>
            <div class="kanban-cards bg-neutral-50 dark:bg-dark-800 border-l border-r border-b border-neutral-200 dark:border-dark-200 rounded-b-xl min-h-96 p-4 space-y-4"
                 id="inprogressColumn" data-status="InProgress">
                <!-- User stories will be loaded here -->
            </div>
        </div>

        <!-- Review Column -->
        <div class="kanban-column flex-shrink-0 w-80" data-status="Review">
            <div class="bg-white dark:bg-surface-dark border border-neutral-200 dark:border-dark-200 rounded-t-xl p-4">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="font-semibold text-neutral-900 dark:text-dark-100 flex items-center">
                        <div class="w-3 h-3 rounded-full mr-2 bg-info-500"></div>
                        Review
                    </h3>
                    <span class="text-sm text-neutral-500 dark:text-dark-400 bg-neutral-100 dark:bg-dark-700 px-2 py-1 rounded-full" id="reviewCount">0</span>
                </div>
                <p class="text-sm text-neutral-600 dark:text-dark-400">Under review/testing</p>
            </div>
            <div class="kanban-cards bg-neutral-50 dark:bg-dark-800 border-l border-r border-b border-neutral-200 dark:border-dark-200 rounded-b-xl min-h-96 p-4 space-y-4"
                 id="reviewColumn" data-status="Review">
                <!-- User stories will be loaded here -->
            </div>
        </div>

        <!-- Done Column -->
        <div class="kanban-column flex-shrink-0 w-80" data-status="Done">
            <div class="bg-white dark:bg-surface-dark border border-neutral-200 dark:border-dark-200 rounded-t-xl p-4">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="font-semibold text-neutral-900 dark:text-dark-100 flex items-center">
                        <div class="w-3 h-3 rounded-full mr-2 bg-success-500"></div>
                        Done
                    </h3>
                    <span class="text-sm text-neutral-500 dark:text-dark-400 bg-neutral-100 dark:bg-dark-700 px-2 py-1 rounded-full" id="doneCount">0</span>
                </div>
                <p class="text-sm text-neutral-600 dark:text-dark-400">Completed stories</p>
            </div>
            <div class="kanban-cards bg-neutral-50 dark:bg-dark-800 border-l border-r border-b border-neutral-200 dark:border-dark-200 rounded-b-xl min-h-96 p-4 space-y-4"
                 id="doneColumn" data-status="Done">
                <!-- User stories will be loaded here -->
            </div>
        </div>
    </div>
</div>
        </div>
    </div>
</div>

<!-- User Story Modal -->
<div id="userStoryModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white dark:bg-surface-dark rounded-xl max-w-2xl w-full max-h-96 overflow-y-auto">
        <div id="userStoryModalContent">
            <!-- Content will be loaded dynamically -->
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
    <script>
        $(document).ready(function() {
            loadSprints();
            initializeKanban();

            $('#sprintSelect').on('change', function() {
                const sprintId = $(this).val();
                if (sprintId) {
                    loadUserStories(sprintId);
                }
            });
        });

        function loadSprints() {
            $.get('@Url.Action("GetActiveSprints", "Agile")')
                .done(function(data) {
                    const select = $('#sprintSelect');
                    select.empty().append('<option value="">Select Sprint...</option>');

                    data.forEach(function(sprint) {
                        select.append(`<option value="${sprint.id}">${sprint.name}</option>`);
                    });

                    // Auto-select current sprint if available
                    if (data.length > 0) {
                        const currentSprint = data.find(s => s.status === 'Active');
                        if (currentSprint) {
                            select.val(currentSprint.id).trigger('change');
                        }
                    }
                })
                .fail(function() {
                    console.error('Failed to load sprints');
                });
        }

        function loadUserStories(sprintId) {
            // Clear all columns
            $('.kanban-body').empty();

            $.get('@Url.Action("GetSprintUserStories", "Agile")', { sprintId: sprintId })
                .done(function(data) {
                    data.forEach(function(story) {
                        const storyCard = createUserStoryCard(story);
                        $(`#${story.status.toLowerCase()}Column`).append(storyCard);
                    });

                    updateColumnCounts();
                    updateSprintProgress(data);
                })
                .fail(function() {
                    console.error('Failed to load user stories');
                });
        }

        function createUserStoryCard(story) {
            const priorityClasses = {
                'critical': 'bg-danger-100 text-danger-700 dark:bg-danger-900 dark:text-danger-300',
                'high': 'bg-warning-100 text-warning-700 dark:bg-warning-900 dark:text-warning-300',
                'medium': 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300',
                'low': 'bg-success-100 text-success-700 dark:bg-success-900 dark:text-success-300'
            };

            const priorityClass = priorityClasses[story.priority.toLowerCase()] || 'bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300';
            const storyPointsBadge = story.storyPoints ?
                `<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300">${story.storyPoints}</span>` : '';

            const epicBadge = story.epic ?
                `<span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300" title="Epic: ${story.epic.title}">
                    <i class="fas fa-mountain mr-1"></i>${story.epic.epicKey}
                </span>` : '';

            const assigneeBadge = story.assignedTo ?
                `<div class="w-6 h-6 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center" title="Assigned to: ${story.assignedTo.userName}">
                    <span class="text-xs font-medium text-primary-600 dark:text-primary-400">${story.assignedTo.userName.substring(0, 1).toUpperCase()}</span>
                </div>` : '';

            return `
                <div class="kanban-card bg-white dark:bg-surface-dark border border-neutral-200 dark:border-dark-200 rounded-lg p-4 cursor-move shadow-sm hover:shadow-md transition-shadow"
                     draggable="true"
                     ondragstart="drag(event)"
                     data-story-id="${story.id}"
                     onclick="openUserStoryModal(${story.id})">

                    <!-- Card Header -->
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex items-center space-x-2">
                            <span class="text-xs font-mono text-neutral-500 dark:text-dark-400">${story.storyKey || '#' + story.id}</span>
                            <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium ${priorityClass}">
                                ${story.priority.substring(0, 1)}
                            </span>
                        </div>
                        ${storyPointsBadge}
                    </div>

                    <!-- Card Content -->
                    <h4 class="font-medium text-neutral-900 dark:text-dark-100 mb-2 line-clamp-2">
                        ${story.title}
                    </h4>

                    ${story.userStoryFormat ? `<p class="text-sm text-neutral-600 dark:text-dark-400 mb-3 line-clamp-2">${story.userStoryFormat}</p>` : ''}

                    <!-- Card Footer -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            ${epicBadge}
                        </div>
                        ${assigneeBadge}
                    </div>
                </div>
            `;
        }

        function initializeKanban() {
            // Make columns sortable
            $('.kanban-cards').each(function() {
                new Sortable(this, {
                    group: 'kanban',
                    animation: 150,
                    ghostClass: 'sortable-ghost',
                    chosenClass: 'sortable-chosen',
                    dragClass: 'sortable-drag',
                    onEnd: function(evt) {
                        const storyId = $(evt.item).data('story-id');
                        const newStatus = $(evt.to).data('status');

                        updateUserStoryStatus(storyId, newStatus);
                        updateColumnCounts();
                        updateWipLimit();
                    }
                });
            });
        }

        // Drag and Drop functionality
        function allowDrop(ev) {
            ev.preventDefault();
        }

        function drag(ev) {
            ev.dataTransfer.setData("text", ev.target.getAttribute('data-story-id'));
            ev.target.style.opacity = '0.5';
        }

        function drop(ev) {
            ev.preventDefault();
            const storyId = ev.dataTransfer.getData("text");
            const columnElement = ev.currentTarget.closest('.kanban-column');
            const newStatus = columnElement.getAttribute('data-status');

            // Reset opacity
            document.querySelector(`[data-story-id="${storyId}"]`).style.opacity = '1';

            // Move the card
            updateUserStoryStatus(storyId, newStatus);
        }

        async function updateUserStoryStatus(storyId, newStatus) {
            try {
                const response = await fetch('@Url.Action("MoveCard")', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    },
                    body: JSON.stringify({
                        cardId: parseInt(storyId),
                        columnId: newStatus
                    })
                });

                const result = await response.json();

                if (result.success) {
                    updateColumnCounts();
                    updateWipLimit();
                } else {
                    console.error('Failed to update user story status:', result.message);
                    location.reload(); // Reload to reset the UI
                }
            } catch (error) {
                console.error('Error updating user story status:', error);
                location.reload();
            }
        }

        function updateColumnCounts() {
            $('#backlogCount').text($('#backlogColumn .kanban-card').length);
            $('#readyCount').text($('#readyColumn .kanban-card').length);
            $('#inprogressCount').text($('#inprogressColumn .kanban-card').length);
            $('#reviewCount').text($('#reviewColumn .kanban-card').length);
            $('#doneCount').text($('#doneColumn .kanban-card').length);
        }

        function updateWipLimit() {
            const wipLimit = 3; // This could be configurable
            const inProgressCount = $('#inprogressColumn .kanban-card').length;
            const wipPercentage = Math.min((inProgressCount / wipLimit) * 100, 100);

            $('#wipStatus').text(`${inProgressCount} / ${wipLimit}`);
            $('#wipProgressBar').css('width', wipPercentage + '%');

            // Change color based on WIP limit
            const progressBar = $('#wipProgressBar');
            progressBar.removeClass('bg-success-500 bg-warning-500 bg-danger-500');

            if (wipPercentage > 100) {
                progressBar.addClass('bg-danger-500');
            } else if (wipPercentage > 80) {
                progressBar.addClass('bg-warning-500');
            } else {
                progressBar.addClass('bg-success-500');
            }
        }

        function updateSprintProgress(stories) {
            const totalStories = stories.length;
            const doneStories = stories.filter(s => s.status === 'Done').length;
            const progress = totalStories > 0 ? Math.round((doneStories / totalStories) * 100) : 0;

            $('#sprintProgress').text(progress + '%');
            $('#sprintProgressBar').css('width', progress + '%');
        }

        async function openUserStoryModal(storyId) {
            const modal = document.getElementById('userStoryModal');
            const content = document.getElementById('userStoryModalContent');

            // Show loading
            content.innerHTML = `
                <div class="p-8 text-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
                    <p class="mt-4 text-neutral-600 dark:text-dark-400">Loading...</p>
                </div>
            `;
            modal.classList.remove('hidden');

            try {
                const response = await fetch(`@Url.Action("UserStoryDetails")/${storyId}`);
                const html = await response.text();
                content.innerHTML = html;
            } catch (error) {
                content.innerHTML = `
                    <div class="p-8 text-center">
                        <i class="fas fa-exclamation-triangle text-danger-600 text-2xl mb-4"></i>
                        <p class="text-danger-600">Failed to load user story details.</p>
                        <button onclick="closeUserStoryModal()" class="mt-4 px-4 py-2 bg-neutral-200 text-neutral-800 rounded-lg hover:bg-neutral-300">Close</button>
                    </div>
                `;
            }
        }

        function closeUserStoryModal() {
            document.getElementById('userStoryModal').classList.add('hidden');
        }

        function openBoardSettings() {
            // Implementation for board settings
            alert('Board settings functionality to be implemented');
        }

        // Close modal when clicking outside
        document.getElementById('userStoryModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeUserStoryModal();
            }
        });

        // Prevent drag events from bubbling up
        document.addEventListener('dragend', function(e) {
            e.target.style.opacity = '1';
        });
    </script>
}

@section Styles {
    <style>
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .kanban-card:hover {
            transform: translateY(-1px);
        }

        .kanban-cards {
            min-height: 400px;
        }

        .kanban-column {
            min-width: 320px;
        }

        .sortable-ghost {
            opacity: 0.4;
        }

        .sortable-chosen {
            transform: scale(1.02);
        }

        .sortable-drag {
            transform: rotate(2deg);
            z-index: 1000;
        }

        .kanban-board {
            min-height: 600px;
        }
    </style>
}
