@model AttachmentDetailsViewModel
@{
    ViewData["Title"] = "File Details";
}

<div class="container-fluid">
    <div class="mb-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    @if (Model.AttachmentType == "Project")
                    {
                        <a asp-controller="Projects" asp-action="Details" asp-route-id="@Model.ParentId">@Model.ParentName</a>
                    }
                    else
                    {
                        <a asp-controller="Tasks" asp-action="Details" asp-route-id="@Model.ParentId">@Model.ParentName</a>
                    }
                </li>
                <li class="breadcrumb-item active">@Model.FileName</li>
            </ol>
        </nav>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">File Information</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4 text-muted">File Name</div>
                        <div class="col-md-8">
                            <i class="bi bi-file-earmark me-2"></i>@Model.FileName
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 text-muted">Size</div>
                        <div class="col-md-8">@Model.FormattedFileSize</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 text-muted">Uploaded By</div>
                        <div class="col-md-8">@Model.UploadedByName on @Model.UploadedAt.ToString("MMM dd, yyyy HH:mm")</div>
                    </div>
                    @if (Model.LastModifiedAt.HasValue)
                    {
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Last Modified</div>
                            <div class="col-md-8">@Model.LastModifiedAt.Value.ToString("MMM dd, yyyy HH:mm")</div>
                        </div>
                    }
                    <div class="row mb-3">
                        <div class="col-md-4 text-muted">Category</div>
                        <div class="col-md-8">@(Model.CategoryName ?? "Uncategorized")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 text-muted">Description</div>
                        <div class="col-md-8">@(Model.Description ?? "No description")</div>
                    </div>
                    @if (!string.IsNullOrEmpty(Model.Tags))
                    {
                        <div class="row mb-3">
                            <div class="col-md-4 text-muted">Tags</div>
                            <div class="col-md-8">
                                @foreach (var tag in Model.Tags.Split(',', StringSplitOptions.RemoveEmptyEntries))
                                {
                                    <span class="badge bg-secondary me-1">@tag.Trim()</span>
                                }
                            </div>
                        </div>
                    }
                </div>
            </div>

            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Version History</h5>
                    <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#uploadVersionModal">
                        <i class="bi bi-cloud-upload me-2"></i>Upload New Version
                    </button>
                </div>
                <div class="card-body">
                    @if (!Model.Versions.Any())
                    {
                        <p class="text-muted">No version history available</p>
                    }
                    else
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Version</th>
                                        <th>File Name</th>
                                        <th>Size</th>
                                        <th>Uploaded By</th>
                                        <th>Uploaded At</th>
                                        <th>Change Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var version in Model.Versions.OrderByDescending(v => v.Version))
                                    {
                                        <tr>
                                            <td>v@(version.Version)</td>
                                            <td>
                                                <a href="@Url.Content($"~/{version.FilePath}")" target="_blank">
                                                    <i class="bi bi-file-earmark me-2"></i>@version.FileName
                                                </a>
                                            </td>
                                            <td>@version.FormattedFileSize</td>
                                            <td>@version.UploadedByName</td>
                                            <td>@version.UploadedAt.ToString("g")</td>
                                            <td>@(version.ChangeDescription ?? "-")</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Update File Details</h5>
                </div>
                <div class="card-body">
                    <form asp-action="UpdateMetadata" method="post">
                        @Html.AntiForgeryToken()
                        <input type="hidden" name="Id" value="@Model.Id">
                        <input type="hidden" name="AttachmentType" value="@Model.AttachmentType">

                        <div class="mb-3">
                            <label for="category" class="form-label">Category</label>
                            <select class="form-select" id="category" name="CategoryId">
                                <option value="">-- Uncategorized --</option>
                                @foreach (var category in Model.AvailableCategories)
                                {
                                    <option value="@category.Id" selected="@(category.Id == Model.CategoryId)">
                                        @category.Name
                                    </option>
                                }
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="Description" rows="3" maxlength="500">@Model.Description</textarea>
                        </div>

                        <div class="mb-3">
                            <label for="tags" class="form-label">Tags</label>
                            <input type="text" class="form-control" id="tags" name="Tags" value="@Model.Tags" maxlength="100">
                            <small class="text-muted">Separate tags with commas</small>
                        </div>

                        <button type="submit" class="btn btn-primary">Save Changes</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Upload New Version Modal -->
<div class="modal fade" id="uploadVersionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upload New Version</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form asp-action="AddVersion" method="post" enctype="multipart/form-data">
                @Html.AntiForgeryToken()
                <input type="hidden" name="AttachmentId" value="@Model.Id">
                <input type="hidden" name="AttachmentType" value="@Model.AttachmentType">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="file" class="form-label">Select File</label>
                        <input type="file" class="form-control" id="file" name="File" required>
                    </div>
                    <div class="mb-3">
                        <label for="changeDescription" class="form-label">Change Description</label>
                        <textarea class="form-control" id="changeDescription" name="ChangeDescription" maxlength="500" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Upload</button>
                </div>
            </form>
        </div>
    </div>
</div>
