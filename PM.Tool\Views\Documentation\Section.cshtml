@using PM.Tool.Core.Models
@model List<DocumentationPage>
@{
    var section = ViewBag.Section as DocumentationSection;
    ViewData["Title"] = section?.Title ?? "Documentation Section";
    ViewData["Description"] = section?.Description ?? "Documentation section overview";
    Layout = "_DocumentationLayout";

    var config = ViewBag.Configuration as DocumentationConfiguration;
}

<div class="container">
    <div class="row">
        <div class="col-12">
            <h1>
                <i class="@(section?.Icon ?? "fas fa-folder")"></i>
                @(section?.Title ?? "Documentation Section")
            </h1>

            @if (!string.IsNullOrEmpty(section?.Description))
            {
                <p class="lead">@(section.Description)</p>
            }

            <p class="text-muted">@Model.Count page@(Model.Count != 1 ? "s" : "") in this section</p>

            @if (Model.Any())
            {
                <div class="row">
                    @foreach (var page in Model.OrderBy(p => p.Order))
                    {
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <a href="@Url.Action("Page", new { sectionId = section?.Id, pageId = page.Id })" class="text-decoration-none">
                                            @(page.Title)
                                        </a>
                                    </h5>

                                    @if (page.Tags?.Any() == true)
                                    {
                                        <div class="mb-2">
                                            @foreach (var tag in page.Tags.Take(3))
                                            {
                                                <span class="badge bg-light text-dark me-1">@tag</span>
                                            }
                                        </div>
                                    }

                                    <div class="text-muted small">
                                        @if (page.EstimatedReadTime.TotalMinutes > 0)
                                        {
                                            <span class="me-3">
                                                <i class="fas fa-clock"></i>
                                                @((int)page.EstimatedReadTime.TotalMinutes) min read
                                            </span>
                                        }
                                        @if (page.LastModified != default)
                                        {
                                            <span>
                                                <i class="fas fa-calendar-alt"></i>
                                                @(page.LastModified.ToString("MMM dd"))
                                            </span>
                                        }
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <a href="@Url.Action("Page", new { sectionId = section?.Id, pageId = page.Id })" class="btn btn-primary btn-sm">
                                        Read More
                                        <i class="fas fa-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                    <h3>No Pages Available</h3>
                    <p class="text-muted">This section doesn't have any pages yet. Check back later for updates.</p>
                    <a href="@Url.Action("Index")" class="btn btn-primary">
                        <i class="fas fa-arrow-left"></i>
                        Back to Documentation
                    </a>
                </div>
            }
        </div>
    </div>
</div>
