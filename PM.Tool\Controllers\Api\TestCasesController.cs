using Microsoft.AspNetCore.Mvc;
using PM.Tool.Core.Entities.Agile;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Enums;
using PM.Tool.Models.Api;
using PM.Tool.Models.DTOs;
using System.ComponentModel.DataAnnotations;
using Asp.Versioning;

namespace PM.Tool.Controllers.Api
{
    /// <summary>
    /// API controller for managing test cases
    /// </summary>
    [ApiVersion("1.0")]
    [Tags("TestCases")]
    public class TestCasesController : BaseApiController
    {
        private readonly IAgileService _agileService;
        private readonly IProjectService _projectService;

        public TestCasesController(
            IAgileService agileService,
            IProjectService projectService,
            IAuditService auditService,
            ILogger<TestCasesController> logger)
            : base(auditService, logger)
        {
            _agileService = agileService;
            _projectService = projectService;
        }

        /// <summary>
        /// Get all test cases for a project
        /// </summary>
        /// <param name="projectId">Project ID</param>
        /// <param name="request">Query parameters</param>
        /// <returns>Paginated list of test cases</returns>
        [HttpGet("projects/{projectId:int}/test-cases")]
        [ProducesResponseType(typeof(ApiPagedResponse<TestCaseSummaryDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetProjectTestCases(
            [FromRoute] int projectId,
            [FromQuery] TestCaseQueryRequest request)
        {
            try
            {
                var validationResult = ValidateModelState();
                if (validationResult != null) return validationResult;

                // Check project access
                if (!await HasProjectAccessAsync(projectId, _projectService))
                {
                    return NotFound(NotFoundError("Project", projectId));
                }

                var testCases = await _agileService.GetProjectTestCasesAsync(projectId);

                // Apply filters
                if (request.FeatureId.HasValue)
                    testCases = testCases.Where(tc => tc.FeatureId == request.FeatureId.Value);

                if (request.UserStoryId.HasValue)
                    testCases = testCases.Where(tc => tc.UserStoryId == request.UserStoryId.Value);

                if (request.Type.HasValue)
                    testCases = testCases.Where(tc => tc.Type == request.Type.Value);

                if (request.Priority.HasValue)
                    testCases = testCases.Where(tc => tc.Priority == request.Priority.Value);

                if (request.Status.HasValue)
                    testCases = testCases.Where(tc => tc.Status == request.Status.Value);

                if (request.LastExecutionResult.HasValue)
                    testCases = testCases.Where(tc => tc.LastExecutionResult == request.LastExecutionResult.Value);

                if (request.NeverExecuted.HasValue && request.NeverExecuted.Value)
                    testCases = testCases.Where(tc => !tc.TestExecutions.Any());

                if (!string.IsNullOrEmpty(request.AssignedToId))
                    testCases = testCases.Where(tc => tc.AssignedToUserId == request.AssignedToId);

                if (!string.IsNullOrEmpty(request.Search))
                {
                    var searchTerm = request.Search.ToLower();
                    testCases = testCases.Where(tc =>
                        tc.Title.ToLower().Contains(searchTerm) ||
                        (tc.Description != null && tc.Description.ToLower().Contains(searchTerm)) ||
                        tc.TestCaseKey.ToLower().Contains(searchTerm));
                }

                // Apply sorting
                testCases = request.SortBy?.ToLower() switch
                {
                    "title" => request.SortOrder == "desc" ? testCases.OrderByDescending(tc => tc.Title) : testCases.OrderBy(tc => tc.Title),
                    "type" => request.SortOrder == "desc" ? testCases.OrderByDescending(tc => tc.Type) : testCases.OrderBy(tc => tc.Type),
                    "priority" => request.SortOrder == "desc" ? testCases.OrderByDescending(tc => tc.Priority) : testCases.OrderBy(tc => tc.Priority),
                    "status" => request.SortOrder == "desc" ? testCases.OrderByDescending(tc => tc.Status) : testCases.OrderBy(tc => tc.Status),
                    "created" => request.SortOrder == "desc" ? testCases.OrderByDescending(tc => tc.CreatedAt) : testCases.OrderBy(tc => tc.CreatedAt),
                    _ => testCases.OrderBy(tc => tc.TestCaseKey)
                };

                var totalCount = testCases.Count();
                var pagedTestCases = testCases
                    .Skip((request.Page - 1) * request.PageSize)
                    .Take(request.PageSize);

                var testCaseDtos = pagedTestCases.Select(tc => new TestCaseSummaryDto
                {
                    Id = tc.Id,
                    TestCaseKey = tc.TestCaseKey,
                    Title = tc.Title,
                    Type = tc.Type,
                    Priority = tc.Priority,
                    Status = tc.Status,
                    LastExecutionResult = tc.LastExecutionResult,
                    LastExecutedAt = tc.TestExecutions?.OrderByDescending(te => te.ExecutedAt).FirstOrDefault()?.ExecutedAt,
                    AssignedToName = tc.AssignedTo?.UserName,
                    FeatureTitle = tc.Feature?.Title,
                    ExecutionCount = tc.TestExecutions?.Count() ?? 0,
                    PassRate = tc.TestExecutions?.Any() == true ?
                        (decimal)tc.TestExecutions.Count(te => te.Result == TestExecutionResult.Passed) / tc.TestExecutions.Count() * 100 : 0
                });

                return Ok(PagedSuccess(testCaseDtos, totalCount, request.Page, request.PageSize));
            }
            catch (Exception ex)
            {
                return HandleException(ex, $"GetProjectTestCases for project {projectId}");
            }
        }

        /// <summary>
        /// Get a specific test case by ID
        /// </summary>
        /// <param name="id">Test case ID</param>
        /// <returns>Test case details</returns>
        [HttpGet("{id:int}")]
        [ProducesResponseType(typeof(ApiResponse<TestCaseDetailDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetTestCase([FromRoute] int id)
        {
            try
            {
                var testCase = await _agileService.GetTestCaseByIdAsync(id);
                if (testCase == null)
                {
                    return NotFound(NotFoundError("TestCase", id));
                }

                // Check project access
                if (!await HasProjectAccessAsync(testCase.ProjectId, _projectService))
                {
                    return NotFound(NotFoundError("TestCase", id));
                }

                var testCaseDto = new TestCaseDetailDto
                {
                    Id = testCase.Id,
                    TestCaseKey = testCase.TestCaseKey,
                    Title = testCase.Title,
                    Description = testCase.Description,
                    Preconditions = testCase.PreConditions,
                    TestSteps = testCase.TestSteps,
                    ExpectedResult = testCase.ExpectedResult,
                    Type = testCase.Type,
                    Priority = testCase.Priority,
                    Status = testCase.Status,
                    LastExecutionResult = testCase.LastExecutionResult,
                    EstimatedExecutionTime = (int?)testCase.EstimatedExecutionTime,
                    Tags = testCase.Tags,
                    ProjectId = testCase.ProjectId,
                    FeatureId = testCase.FeatureId,
                    UserStoryId = testCase.UserStoryId,
                    FeatureTitle = testCase.Feature?.Title,
                    UserStoryTitle = testCase.UserStory?.Title,
                    CreatedByName = testCase.CreatedBy?.UserName,
                    AssignedToName = testCase.AssignedTo?.UserName,
                    ExecutionCount = testCase.TestExecutions?.Count() ?? 0,
                    PassRate = testCase.TestExecutions?.Any() == true ?
                        (decimal)testCase.TestExecutions.Count(te => te.Result == TestExecutionResult.Passed) / testCase.TestExecutions.Count() * 100 : 0,
                    CreatedAt = testCase.CreatedAt,
                    UpdatedAt = testCase.UpdatedAt
                };

                return Ok(Success(testCaseDto));
            }
            catch (Exception ex)
            {
                return HandleException(ex, $"GetTestCase {id}");
            }
        }

        /// <summary>
        /// Create a new test case
        /// </summary>
        /// <param name="request">Test case creation data</param>
        /// <returns>Created test case</returns>
        [HttpPost]
        [ProducesResponseType(typeof(ApiResponse<TestCaseDetailDto>), StatusCodes.Status201Created)]
        [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> CreateTestCase([FromBody] CreateTestCaseRequest request)
        {
            try
            {
                var validationResult = ValidateModelState();
                if (validationResult != null) return validationResult;

                // Check project access
                if (!await HasProjectAccessAsync(request.ProjectId, _projectService))
                {
                    return NotFound(NotFoundError("Project", request.ProjectId));
                }

                // Generate test case key
                var existingTestCases = await _agileService.GetProjectTestCasesAsync(request.ProjectId);
                var testCaseKey = $"TC-{existingTestCases.Count() + 1:D3}";

                var testCase = new TestCase
                {
                    TestCaseKey = testCaseKey,
                    Title = request.Title,
                    Description = request.Description,
                    PreConditions = request.Preconditions,
                    TestSteps = request.TestSteps,
                    ExpectedResult = request.ExpectedResult,
                    Type = request.Type,
                    Priority = request.Priority,
                    Status = request.Status,
                    EstimatedExecutionTime = request.EstimatedExecutionTime ?? 0,
                    Tags = request.Tags,
                    ProjectId = request.ProjectId,
                    FeatureId = request.FeatureId,
                    UserStoryId = request.UserStoryId,
                    CreatedByUserId = CurrentUserId,
                    AssignedToUserId = request.AssignedToUserId
                };

                var createdTestCase = await _agileService.CreateTestCaseAsync(testCase);
                await LogAuditAsync(AuditAction.Create, "TestCase", createdTestCase.Id);

                var testCaseDto = new TestCaseDetailDto
                {
                    Id = createdTestCase.Id,
                    TestCaseKey = createdTestCase.TestCaseKey,
                    Title = createdTestCase.Title,
                    Description = createdTestCase.Description,
                    Preconditions = createdTestCase.PreConditions,
                    TestSteps = createdTestCase.TestSteps,
                    ExpectedResult = createdTestCase.ExpectedResult,
                    Type = createdTestCase.Type,
                    Priority = createdTestCase.Priority,
                    Status = createdTestCase.Status,
                    EstimatedExecutionTime = (int?)createdTestCase.EstimatedExecutionTime,
                    Tags = createdTestCase.Tags,
                    ProjectId = createdTestCase.ProjectId,
                    FeatureId = createdTestCase.FeatureId,
                    UserStoryId = createdTestCase.UserStoryId,
                    CreatedAt = createdTestCase.CreatedAt,
                    UpdatedAt = createdTestCase.UpdatedAt
                };

                return CreatedWithLocation(nameof(GetTestCase), new { id = createdTestCase.Id }, testCaseDto, "Test case created successfully");
            }
            catch (Exception ex)
            {
                return HandleException(ex, "CreateTestCase");
            }
        }

        /// <summary>
        /// Update an existing test case
        /// </summary>
        /// <param name="id">Test case ID</param>
        /// <param name="request">Test case update data</param>
        /// <returns>Updated test case</returns>
        [HttpPut("{id:int}")]
        [ProducesResponseType(typeof(ApiResponse<TestCaseDetailDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateTestCase([FromRoute] int id, [FromBody] UpdateTestCaseRequest request)
        {
            try
            {
                if (id != request.Id)
                {
                    return BadRequest(ValidationError("Route ID does not match request ID"));
                }

                var validationResult = ValidateModelState();
                if (validationResult != null) return validationResult;

                var existingTestCase = await _agileService.GetTestCaseByIdAsync(id);
                if (existingTestCase == null)
                {
                    return NotFound(NotFoundError("TestCase", id));
                }

                // Check project access
                if (!await HasProjectAccessAsync(existingTestCase.ProjectId, _projectService))
                {
                    return NotFound(NotFoundError("TestCase", id));
                }

                // Update properties
                existingTestCase.Title = request.Title;
                existingTestCase.Description = request.Description;
                existingTestCase.PreConditions = request.Preconditions;
                existingTestCase.TestSteps = request.TestSteps;
                existingTestCase.ExpectedResult = request.ExpectedResult;
                existingTestCase.Type = request.Type;
                existingTestCase.Priority = request.Priority;
                existingTestCase.Status = request.Status;
                existingTestCase.EstimatedExecutionTime = request.EstimatedExecutionTime ?? 0;
                existingTestCase.Tags = request.Tags;
                existingTestCase.FeatureId = request.FeatureId;
                existingTestCase.UserStoryId = request.UserStoryId;
                existingTestCase.AssignedToUserId = request.AssignedToUserId;

                var updatedTestCase = await _agileService.UpdateTestCaseAsync(existingTestCase);
                await LogAuditAsync(AuditAction.Update, "TestCase", id);

                var testCaseDto = new TestCaseDetailDto
                {
                    Id = updatedTestCase.Id,
                    TestCaseKey = updatedTestCase.TestCaseKey,
                    Title = updatedTestCase.Title,
                    Description = updatedTestCase.Description,
                    Preconditions = updatedTestCase.PreConditions,
                    TestSteps = updatedTestCase.TestSteps,
                    ExpectedResult = updatedTestCase.ExpectedResult,
                    Type = updatedTestCase.Type,
                    Priority = updatedTestCase.Priority,
                    Status = updatedTestCase.Status,
                    EstimatedExecutionTime = (int?)updatedTestCase.EstimatedExecutionTime,
                    Tags = updatedTestCase.Tags,
                    ProjectId = updatedTestCase.ProjectId,
                    FeatureId = updatedTestCase.FeatureId,
                    UserStoryId = updatedTestCase.UserStoryId,
                    CreatedAt = updatedTestCase.CreatedAt,
                    UpdatedAt = updatedTestCase.UpdatedAt
                };

                return Ok(Success(testCaseDto, "Test case updated successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex, $"UpdateTestCase {id}");
            }
        }

        /// <summary>
        /// Delete a test case
        /// </summary>
        /// <param name="id">Test case ID</param>
        /// <returns>Success confirmation</returns>
        [HttpDelete("{id:int}")]
        [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteTestCase([FromRoute] int id)
        {
            try
            {
                var testCase = await _agileService.GetTestCaseByIdAsync(id);
                if (testCase == null)
                {
                    return NotFound(NotFoundError("TestCase", id));
                }

                // Check project access
                if (!await HasProjectAccessAsync(testCase.ProjectId, _projectService))
                {
                    return NotFound(NotFoundError("TestCase", id));
                }

                await _agileService.DeleteTestCaseAsync(id);
                await LogAuditAsync(AuditAction.Delete, "TestCase", id);

                return Ok(Success("Test case deleted successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex, $"DeleteTestCase {id}");
            }
        }

        /// <summary>
        /// Execute a test case
        /// </summary>
        /// <param name="id">Test case ID</param>
        /// <param name="request">Test execution data</param>
        /// <returns>Test execution result</returns>
        [HttpPost("{id:int}/execute")]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status201Created)]
        [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status404NotFound)]
        public async Task<IActionResult> ExecuteTestCase([FromRoute] int id, [FromBody] ExecuteTestCaseRequest request)
        {
            try
            {
                var validationResult = ValidateModelState();
                if (validationResult != null) return validationResult;

                var testCase = await _agileService.GetTestCaseByIdAsync(id);
                if (testCase == null)
                {
                    return NotFound(NotFoundError("TestCase", id));
                }

                // Check project access
                if (!await HasProjectAccessAsync(testCase.ProjectId, _projectService))
                {
                    return NotFound(NotFoundError("TestCase", id));
                }

                var execution = new TestExecution
                {
                    TestCaseId = id,
                    Result = request.Result,
                    ExecutedAt = DateTime.UtcNow,
                    ExecutionTime = request.ExecutionTime ?? 0,
                    Notes = request.Notes,
                    ExecutedByUserId = CurrentUserId!
                };

                await _agileService.CreateTestExecutionAsync(execution);
                await LogAuditAsync(AuditAction.Create, "TestExecution", id);

                var result = new
                {
                    ExecutionId = execution.Id,
                    Result = execution.Result,
                    ExecutedAt = execution.ExecutedAt,
                    ExecutionTime = execution.ExecutionTime,
                    Notes = execution.Notes
                };

                return Ok(Success(result, "Test case executed successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex, $"ExecuteTestCase {id}");
            }
        }

        /// <summary>
        /// Get execution history for a test case
        /// </summary>
        /// <param name="id">Test case ID</param>
        /// <returns>List of test executions</returns>
        [HttpGet("{id:int}/executions")]
        [ProducesResponseType(typeof(ApiResponse<IEnumerable<object>>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetTestCaseExecutions([FromRoute] int id)
        {
            try
            {
                var testCase = await _agileService.GetTestCaseByIdAsync(id);
                if (testCase == null)
                {
                    return NotFound(NotFoundError("TestCase", id));
                }

                // Check project access
                if (!await HasProjectAccessAsync(testCase.ProjectId, _projectService))
                {
                    return NotFound(NotFoundError("TestCase", id));
                }

                var executions = await _agileService.GetTestCaseExecutionsAsync(id);
                var executionDtos = executions.Select(e => new
                {
                    e.Id,
                    e.Result,
                    e.ExecutedAt,
                    e.ExecutionTime,
                    e.Notes,
                    ExecutedByName = e.ExecutedBy?.UserName
                });

                return Ok(Success(executionDtos));
            }
            catch (Exception ex)
            {
                return HandleException(ex, $"GetTestCaseExecutions {id}");
            }
        }
    }

    /// <summary>
    /// Request model for querying test cases
    /// </summary>
    public class TestCaseQueryRequest : BaseApiRequest
    {
        /// <summary>
        /// Filter by feature ID
        /// </summary>
        public int? FeatureId { get; set; }

        /// <summary>
        /// Filter by user story ID
        /// </summary>
        public int? UserStoryId { get; set; }

        /// <summary>
        /// Filter by test case type
        /// </summary>
        public TestCaseType? Type { get; set; }

        /// <summary>
        /// Filter by test case priority
        /// </summary>
        public TestCasePriority? Priority { get; set; }

        /// <summary>
        /// Filter by test case status
        /// </summary>
        public TestCaseStatus? Status { get; set; }

        /// <summary>
        /// Filter by last execution result
        /// </summary>
        public TestExecutionResult? LastExecutionResult { get; set; }

        /// <summary>
        /// Filter for test cases that have never been executed
        /// </summary>
        public bool? NeverExecuted { get; set; }

        /// <summary>
        /// Filter by assigned user ID
        /// </summary>
        public string? AssignedToId { get; set; }
    }

    /// <summary>
    /// Request model for creating a test case
    /// </summary>
    public class CreateTestCaseRequest : CreateApiRequest
    {
        /// <summary>
        /// Test case title
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Test case description
        /// </summary>
        [StringLength(2000)]
        public string? Description { get; set; }

        /// <summary>
        /// Test preconditions
        /// </summary>
        [StringLength(1000)]
        public string? Preconditions { get; set; }

        /// <summary>
        /// Test steps
        /// </summary>
        [Required]
        [StringLength(2000)]
        public string TestSteps { get; set; } = string.Empty;

        /// <summary>
        /// Expected result
        /// </summary>
        [Required]
        [StringLength(1000)]
        public string ExpectedResult { get; set; } = string.Empty;

        /// <summary>
        /// Test case type
        /// </summary>
        [Required]
        public TestCaseType Type { get; set; }

        /// <summary>
        /// Test case priority
        /// </summary>
        [Required]
        public TestCasePriority Priority { get; set; }

        /// <summary>
        /// Test case status
        /// </summary>
        [Required]
        public TestCaseStatus Status { get; set; }

        /// <summary>
        /// Estimated execution time in minutes
        /// </summary>
        [Range(0, int.MaxValue)]
        public int? EstimatedExecutionTime { get; set; }

        /// <summary>
        /// Tags (comma-separated)
        /// </summary>
        [StringLength(500)]
        public string? Tags { get; set; }

        /// <summary>
        /// Project ID
        /// </summary>
        [Required]
        public int ProjectId { get; set; }

        /// <summary>
        /// Feature ID (optional)
        /// </summary>
        public int? FeatureId { get; set; }

        /// <summary>
        /// User story ID (optional)
        /// </summary>
        public int? UserStoryId { get; set; }

        /// <summary>
        /// Assigned user ID (optional)
        /// </summary>
        public string? AssignedToUserId { get; set; }
    }

    /// <summary>
    /// Request model for updating a test case
    /// </summary>
    public class UpdateTestCaseRequest : UpdateApiRequest
    {
        /// <summary>
        /// Test case title
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Test case description
        /// </summary>
        [StringLength(2000)]
        public string? Description { get; set; }

        /// <summary>
        /// Test preconditions
        /// </summary>
        [StringLength(1000)]
        public string? Preconditions { get; set; }

        /// <summary>
        /// Test steps
        /// </summary>
        [Required]
        [StringLength(2000)]
        public string TestSteps { get; set; } = string.Empty;

        /// <summary>
        /// Expected result
        /// </summary>
        [Required]
        [StringLength(1000)]
        public string ExpectedResult { get; set; } = string.Empty;

        /// <summary>
        /// Test case type
        /// </summary>
        [Required]
        public TestCaseType Type { get; set; }

        /// <summary>
        /// Test case priority
        /// </summary>
        [Required]
        public TestCasePriority Priority { get; set; }

        /// <summary>
        /// Test case status
        /// </summary>
        [Required]
        public TestCaseStatus Status { get; set; }

        /// <summary>
        /// Estimated execution time in minutes
        /// </summary>
        [Range(0, int.MaxValue)]
        public int? EstimatedExecutionTime { get; set; }

        /// <summary>
        /// Tags (comma-separated)
        /// </summary>
        [StringLength(500)]
        public string? Tags { get; set; }

        /// <summary>
        /// Feature ID (optional)
        /// </summary>
        public int? FeatureId { get; set; }

        /// <summary>
        /// User story ID (optional)
        /// </summary>
        public int? UserStoryId { get; set; }

        /// <summary>
        /// Assigned user ID (optional)
        /// </summary>
        public string? AssignedToUserId { get; set; }
    }

    /// <summary>
    /// Request model for executing a test case
    /// </summary>
    public class ExecuteTestCaseRequest
    {
        /// <summary>
        /// Test execution result
        /// </summary>
        [Required]
        public TestExecutionResult Result { get; set; }

        /// <summary>
        /// Execution time in minutes
        /// </summary>
        [Range(0, int.MaxValue)]
        public int? ExecutionTime { get; set; }

        /// <summary>
        /// Execution notes
        /// </summary>
        [StringLength(2000)]
        public string? Notes { get; set; }
    }
}
