using System.ComponentModel.DataAnnotations;

using PM.Tool.Core.Entities;

namespace PM.Tool.Models.ViewModels
{
    public class RiskCreateViewModel : BaseCreateViewModel, IEntityViewModel<Risk>
    {
        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [MaxLength(2000)]
        public string Description { get; set; } = string.Empty;

        [Required]
        public int ProjectId { get; set; }

        public int? TaskId { get; set; }

        public RiskCategory Category { get; set; } = RiskCategory.Technical;

        public RiskProbability Probability { get; set; } = RiskProbability.Medium;

        public RiskImpact Impact { get; set; } = RiskImpact.Medium;

        public RiskStatus Status { get; set; } = RiskStatus.Identified;

        public string? OwnerId { get; set; }

        public DateTime? TargetResolutionDate { get; set; }

        [MaxLength(2000)]
        public string? MitigationPlan { get; set; }

        [MaxLength(2000)]
        public string? ContingencyPlan { get; set; }

        public decimal EstimatedCost { get; set; }

        public int EstimatedDelayDays { get; set; }

        // Convert to Risk entity
        public Risk ToEntity()
        {
            return new Risk
            {
                Title = Title,
                Description = Description,
                ProjectId = ProjectId,
                TaskId = TaskId,
                Category = Category,
                Probability = Probability,
                Impact = Impact,
                Status = Status,
                OwnerId = OwnerId,
                TargetResolutionDate = TargetResolutionDate,
                MitigationPlan = MitigationPlan,
                ContingencyPlan = ContingencyPlan,
                EstimatedCost = EstimatedCost,
                EstimatedDelayDays = EstimatedDelayDays
            };
        }

        // For backward compatibility
        public Risk ToRisk() => ToEntity();

        // Update existing entity
        public void UpdateEntity(Risk risk)
        {
            risk.Title = Title;
            risk.Description = Description;
            risk.ProjectId = ProjectId;
            risk.TaskId = TaskId;
            risk.Category = Category;
            risk.Probability = Probability;
            risk.Impact = Impact;
            risk.Status = Status;
            risk.OwnerId = OwnerId;
            risk.TargetResolutionDate = TargetResolutionDate;
            risk.MitigationPlan = MitigationPlan;
            risk.ContingencyPlan = ContingencyPlan;
            risk.EstimatedCost = EstimatedCost;
            risk.EstimatedDelayDays = EstimatedDelayDays;
        }
    }

    public class RiskEditViewModel : RiskCreateViewModel, IFromEntity<Risk, RiskEditViewModel>
    {
        public int Id { get; set; }

        public DateTime? IdentifiedDate { get; set; }

        public DateTime? ActualResolutionDate { get; set; }

        public string CreatedByUserId { get; set; } = string.Empty;

        // Convert from Risk entity
        public static RiskEditViewModel FromEntity(Risk risk)
        {
            return new RiskEditViewModel
            {
                Id = risk.Id,
                Title = risk.Title,
                Description = risk.Description,
                ProjectId = risk.ProjectId,
                TaskId = risk.TaskId,
                Category = risk.Category,
                Probability = risk.Probability,
                Impact = risk.Impact,
                Status = risk.Status,
                OwnerId = risk.OwnerId,
                TargetResolutionDate = risk.TargetResolutionDate,
                MitigationPlan = risk.MitigationPlan,
                ContingencyPlan = risk.ContingencyPlan,
                EstimatedCost = risk.EstimatedCost,
                EstimatedDelayDays = risk.EstimatedDelayDays,
                IdentifiedDate = risk.IdentifiedDate,
                ActualResolutionDate = risk.ActualResolutionDate,
                CreatedByUserId = risk.CreatedByUserId
            };
        }

        // For backward compatibility
        public static RiskEditViewModel FromRisk(Risk risk) => FromEntity(risk);

        // Update Risk entity (inherited from base class)
        public void UpdateRisk(Risk risk) => UpdateEntity(risk);
    }
}
