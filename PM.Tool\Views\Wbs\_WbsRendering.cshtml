<script>
    // WBS Rendering JavaScript - Tree rendering and task display

    // Render WBS tree
    function renderWbsTree(tasks, parentElement = null) {
        const container = parentElement || $('#wbs-tree');

        tasks.forEach(task => {
            const taskElement = createTaskElement(task);
            container.append(taskElement);

            if (task.children && task.children.length > 0) {
                const childContainer = $('<div class="wbs-children ml-6 mt-2"></div>');
                taskElement.append(childContainer);
                renderWbsTree(task.children, childContainer);
            }
        });
    }

    // Create task element
    function createTaskElement(task) {
        const progressColor = getProgressColor(task.progress || 0);
        const priorityColor = getPriorityColor(task.priority);
        const statusColor = getStatusColor(task.status);
        const isOverdue = task.dueDate && new Date(task.dueDate) < new Date() && task.status !== 'Done';

        return $(`
            <div class="wbs-node ${isOverdue ? 'border-l-4 border-red-500' : ''}" data-task-id="${task.id}">
                <!-- Task Header -->
                <div class="flex items-start justify-between">
                    <div class="flex items-start flex-1">
                        <!-- Expand/Collapse Icon -->
                        <div class="flex-shrink-0 mr-3 mt-1">
                            ${task.children && task.children.length > 0 ?
                                `<i class="fas fa-chevron-down toggle-icon cursor-pointer text-gray-400 hover:text-blue-600 transition-colors text-sm" onclick="toggleNode(this)"></i>` :
                                `<div class="w-4 h-4 rounded-full bg-gray-200 dark:bg-gray-600"></div>`
                            }
                        </div>

                        <!-- Task Content -->
                        <div class="flex-1 min-w-0">
                            <!-- Badges Row -->
                            <div class="flex items-center flex-wrap gap-2 mb-3">
                                <span class="wbs-code ${priorityColor.bg} ${priorityColor.text}">${task.wbsCode || 'N/A'}</span>
                                <span class="priority-badge ${priorityColor.badge}">${task.priority || 'Medium'}</span>
                                <span class="status-badge ${statusColor.badge}">${task.status || 'ToDo'}</span>
                                ${isOverdue ? '<span class="overdue-badge"><i class="fas fa-exclamation-triangle mr-1"></i>Overdue</span>' : ''}
                            </div>

                            <!-- Task Title -->
                            <h3 class="task-title">${task.title}</h3>

                            <!-- Task Description -->
                            ${task.description ? `<div class="task-description">${task.description}</div>` : ''}
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="task-actions">
                        <button onclick="viewTask(${task.id})" class="action-btn text-blue-600" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button onclick="editTask(${task.id})" class="action-btn text-green-600" title="Edit Task">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="duplicateTask(${task.id})" class="action-btn text-purple-600" title="Duplicate">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button onclick="addChildTask(${task.id})" class="action-btn text-indigo-600" title="Add Child">
                            <i class="fas fa-plus"></i>
                        </button>
                        <div class="relative">
                            <button onclick="toggleTaskMenu(${task.id})" class="action-btn text-gray-600" title="More Actions">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div id="taskMenu${task.id}" class="task-menu hidden">
                                <button onclick="moveTask(${task.id}, 'up')" class="menu-item">
                                    <i class="fas fa-arrow-up mr-3 text-blue-500"></i>Move Up
                                </button>
                                <button onclick="moveTask(${task.id}, 'down')" class="menu-item">
                                    <i class="fas fa-arrow-down mr-3 text-blue-500"></i>Move Down
                                </button>
                                <div class="menu-divider"></div>
                                <button onclick="changeTaskStatus(${task.id}, 'InProgress')" class="menu-item">
                                    <i class="fas fa-play mr-3 text-blue-500"></i>Start Task
                                </button>
                                <button onclick="changeTaskStatus(${task.id}, 'InReview')" class="menu-item">
                                    <i class="fas fa-eye mr-3 text-purple-500"></i>Mark In Review
                                </button>
                                <button onclick="changeTaskStatus(${task.id}, 'Done')" class="menu-item">
                                    <i class="fas fa-check mr-3 text-green-500"></i>Mark Complete
                                </button>
                                <button onclick="changeTaskStatus(${task.id}, 'Cancelled')" class="menu-item">
                                    <i class="fas fa-times mr-3 text-red-500"></i>Cancel Task
                                </button>
                                <div class="menu-divider"></div>
                                <button onclick="exportTask(${task.id})" class="menu-item">
                                    <i class="fas fa-download mr-3 text-gray-500"></i>Export Task
                                </button>
                                <button onclick="deleteTask(${task.id})" class="menu-item text-red-600">
                                    <i class="fas fa-trash mr-3"></i>Delete Task
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Task Details -->
                <div class="task-details">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center gap-6">
                            <div class="flex items-center gap-2">
                                <i class="fas fa-chart-line text-blue-500"></i>
                                <span class="font-medium">Progress: ${task.progress || 0}%</span>
                            </div>
                            ${task.assignedTo ? `
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-user text-green-500"></i>
                                    <span class="font-medium">${task.assignedTo}</span>
                                </div>
                            ` : '<span class="text-gray-500 italic flex items-center gap-2"><i class="fas fa-user-slash"></i>Unassigned</span>'}
                            ${task.estimatedHours ? `
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-clock text-orange-500"></i>
                                    <span class="font-medium">${task.estimatedHours}h</span>
                                </div>
                            ` : ''}
                        </div>
                    </div>

                    <!-- Progress Bar -->
                    <div class="progress-container mb-3">
                        <div class="progress-bar ${progressColor} h-2 rounded-full transition-all duration-500 ease-out" style="width: ${task.progress || 0}%"></div>
                    </div>

                    <!-- Dates -->
                    ${task.startDate || task.dueDate ? `
                        <div class="flex items-center justify-between text-sm">
                            ${task.startDate ? `
                                <div class="flex items-center gap-2 text-green-600">
                                    <i class="fas fa-play"></i>
                                    <span>Start: ${formatDate(task.startDate)}</span>
                                </div>
                            ` : '<div></div>'}
                            ${task.dueDate ? `
                                <div class="flex items-center gap-2 ${isOverdue ? 'text-red-600 font-semibold' : 'text-blue-600'}">
                                    <i class="fas fa-flag"></i>
                                    <span>Due: ${formatDate(task.dueDate)}</span>
                                </div>
                            ` : '<div></div>'}
                        </div>
                    ` : ''}
                </div>
            </div>
        `);
    }

    // Toggle node expansion
    function toggleNode(element) {
        const $icon = $(element);
        const $children = $icon.closest('.wbs-node').find('.wbs-children').first();

        if ($children.is(':visible')) {
            $children.slideUp();
            $icon.removeClass('fa-chevron-down').addClass('fa-chevron-right');
        } else {
            $children.slideDown();
            $icon.removeClass('fa-chevron-right').addClass('fa-chevron-down');
        }
    }

    // Expand/Collapse all nodes
    function expandAll() {
        $('.wbs-children').slideDown();
        $('.toggle-icon').removeClass('fa-chevron-right').addClass('fa-chevron-down');
    }

    function collapseAll() {
        $('.wbs-children').slideUp();
        $('.toggle-icon').removeClass('fa-chevron-down').addClass('fa-chevron-right');
    }
</script>
