@model PM.Tool.Core.Entities.Resource

@{
    ViewData["Title"] = "Edit Resource";
    ViewData["Subtitle"] = "Update resource information";
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-edit mr-3 text-primary-600 dark:text-primary-400"></i>
                Edit @Model.Name
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Update resource information and settings
            </p>
        </div>
        <div class="flex space-x-3">
            @{
                ViewData["Text"] = "View Details";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-eye";
                ViewData["Href"] = Url.Action("Details", new { id = Model.Id });
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "Back to List";
                ViewData["Variant"] = "outline";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Index");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Edit Resource Form -->
<div class="card-custom">
    <div class="card-header-custom">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                <i class="fas fa-user-edit text-primary-600 dark:text-primary-400 text-sm"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Resource Information</h3>
            </div>
        </div>
    </div>
    <div class="card-body-custom">
        <form asp-action="Edit" class="space-y-6">
            <input asp-for="Id" type="hidden" />
            <input asp-for="CreatedAt" type="hidden" />
            <input asp-for="UpdatedAt" type="hidden" />

            <div asp-validation-summary="ModelOnly" class="alert-danger-custom"></div>

        <!-- Basic Information -->
        <div class="card-custom">
            <div class="card-header-custom">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                    <i class="fas fa-info-circle mr-2 text-primary-500"></i>
                    Basic Information
                </h3>
            </div>
            <div class="card-body-custom">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="form-group">
                        <label asp-for="Name" class="form-label"></label>
                        <input asp-for="Name" class="form-input" placeholder="Enter resource name" />
                        <span asp-validation-for="Name" class="form-error"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Type" class="form-label"></label>
                        <select asp-for="Type" asp-items="@ViewBag.ResourceTypes" class="form-select">
                            <option value="">Select resource type</option>
                        </select>
                        <span asp-validation-for="Type" class="form-error"></span>
                    </div>

                    <div class="form-group md:col-span-2">
                        <label asp-for="Description" class="form-label"></label>
                        <textarea asp-for="Description" class="form-textarea" rows="3" placeholder="Enter resource description"></textarea>
                        <span asp-validation-for="Description" class="form-error"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Location & Department -->
        <div class="card-custom">
            <div class="card-header-custom">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                    <i class="fas fa-map-marker-alt mr-2 text-primary-500"></i>
                    Location & Department
                </h3>
            </div>
            <div class="card-body-custom">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="form-group">
                        <label asp-for="Department" class="form-label"></label>
                        <input asp-for="Department" class="form-input" placeholder="Enter department" />
                        <span asp-validation-for="Department" class="form-error"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Location" class="form-label"></label>
                        <input asp-for="Location" class="form-input" placeholder="Enter location" />
                        <span asp-validation-for="Location" class="form-error"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Capacity & Rates -->
        <div class="card-custom">
            <div class="card-header-custom">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                    <i class="fas fa-chart-line mr-2 text-primary-500"></i>
                    Capacity & Rates
                </h3>
            </div>
            <div class="card-body-custom">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="form-group">
                        <label asp-for="HourlyRate" class="form-label"></label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-500">$</span>
                            <input asp-for="HourlyRate" class="form-input pl-8" placeholder="0.00" step="0.01" />
                        </div>
                        <span asp-validation-for="HourlyRate" class="form-error"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Capacity" class="form-label"></label>
                        <div class="relative">
                            <input asp-for="Capacity" class="form-input pr-16" placeholder="8.0" step="0.5" />
                            <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500">hours/day</span>
                        </div>
                        <span asp-validation-for="Capacity" class="form-error"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="card-custom">
            <div class="card-header-custom">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                    <i class="fas fa-address-book mr-2 text-primary-500"></i>
                    Contact Information
                </h3>
            </div>
            <div class="card-body-custom">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="form-group">
                        <label asp-for="Email" class="form-label"></label>
                        <input asp-for="Email" class="form-input" placeholder="Enter email address" />
                        <span asp-validation-for="Email" class="form-error"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Phone" class="form-label"></label>
                        <input asp-for="Phone" class="form-input" placeholder="Enter phone number" />
                        <span asp-validation-for="Phone" class="form-error"></span>
                    </div>

                    <div class="form-group md:col-span-2">
                        <label asp-for="Skills" class="form-label"></label>
                        <textarea asp-for="Skills" class="form-textarea" rows="2" placeholder="Enter skills (comma-separated)"></textarea>
                        <span asp-validation-for="Skills" class="form-error"></span>
                        <p class="form-help">List skills separated by commas (e.g., "JavaScript, React, Node.js")</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status & Settings -->
        <div class="card-custom">
            <div class="card-header-custom">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                    <i class="fas fa-cog mr-2 text-primary-500"></i>
                    Status & Settings
                </h3>
            </div>
            <div class="card-body-custom">
                <div class="form-group">
                    <div class="flex items-center">
                        <input asp-for="IsActive" class="form-checkbox" />
                        <label asp-for="IsActive" class="form-checkbox-label">Resource is active and available for allocation</label>
                    </div>
                    <span asp-validation-for="IsActive" class="form-error"></span>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-between items-center pt-6 border-t border-neutral-200 dark:border-dark-600">
            <div>
                <form asp-action="Delete" asp-route-id="@Model.Id" method="post" class="inline"
                      onsubmit="return confirm('Are you sure you want to delete this resource? This action cannot be undone.');">
                    <button type="submit" class="btn-danger">
                        <i class="fas fa-trash mr-2"></i>
                        Delete Resource
                    </button>
                </form>
            </div>

            <div class="flex space-x-4">
                <a asp-action="Details" asp-route-id="@Model.Id" class="btn-secondary">
                    <i class="fas fa-times mr-2"></i>
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    <i class="fas fa-save mr-2"></i>
                    Update Resource
                </button>
            </div>
        </div>
    </form>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />

    <script>
        // Auto-format hourly rate input
        document.getElementById('HourlyRate').addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^\d.]/g, '');
            if (value.includes('.')) {
                let parts = value.split('.');
                if (parts[1] && parts[1].length > 2) {
                    parts[1] = parts[1].substring(0, 2);
                    value = parts.join('.');
                }
            }
            e.target.value = value;
        });

        // Auto-format capacity input
        document.getElementById('Capacity').addEventListener('input', function(e) {
            let value = parseFloat(e.target.value);
            if (value > 24) {
                e.target.value = '24';
            } else if (value < 0) {
                e.target.value = '0';
            }
        });
    </script>
}
