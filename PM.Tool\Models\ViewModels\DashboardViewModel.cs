using PM.Tool.Core.Enums;

using System;
using System.Collections.Generic;

namespace PM.Tool.Models.ViewModels
{
    public class DashboardViewModel
    {
        public DashboardStatsViewModel Stats { get; set; } = new();
        public ChartDataViewModel Charts { get; set; } = new();
        public List<ProjectSummaryViewModel> RecentProjects { get; set; } = new();
        public List<TaskSummaryViewModel> MyTasks { get; set; } = new();
        public List<TaskSummaryViewModel> OverdueTasks { get; set; } = new();
        public List<TaskSummaryViewModel> UpcomingTasks { get; set; } = new();
        public List<NotificationViewModel> RecentNotifications { get; set; } = new();
        public List<MilestoneViewModel> UpcomingMilestones { get; set; } = new();
        public VelocityMetricsViewModel VelocityMetrics { get; set; } = new();
        public ProductivityMetricsViewModel ProductivityMetrics { get; set; } = new();
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
    }

    public class DashboardStatsViewModel
    {
        public int TotalProjects { get; set; }
        public int ActiveProjects { get; set; }
        public int CompletedProjects { get; set; }
        public int TotalTasks { get; set; }
        public int MyTasks { get; set; }
        public int CompletedTasks { get; set; }
        public int OverdueTasks { get; set; }
        public double OverallProgress { get; set; }
    }
}
