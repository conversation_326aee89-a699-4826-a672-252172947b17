@model PM.Tool.Core.Entities.Meeting
@using PM.Tool.Core.Enums
@{
    ViewData["Title"] = "Delete Meeting";
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Meetings", Href = Url.Action("Index", "Meeting"), Icon = "fas fa-calendar" },
        new { Text = Model.Title, Href = Url.Action("Details", new { id = Model.Id }), Icon = "fas fa-info-circle" },
        new { Text = "Delete", Href = "", Icon = "fas fa-trash" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Page Header -->
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-exclamation-triangle mr-3 text-danger-600 dark:text-danger-400"></i>
                Delete Meeting
            </h1>
            <p class="mt-1 text-sm text-danger-600 dark:text-danger-400">
                Are you sure you want to delete this meeting? This action cannot be undone.
            </p>
        </div>
        @{
            ViewData["Text"] = "Back to Meeting";
            ViewData["Variant"] = "secondary";
            ViewData["Icon"] = "fas fa-arrow-left";
            ViewData["Href"] = Url.Action("Details", new { id = Model.Id });
        }
        <partial name="Components/_Button" view-data="ViewData" />
    </div>
</div>

<!-- Delete Confirmation Card -->
<div class="max-w-4xl mx-auto">
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-danger-100 dark:bg-danger-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-calendar text-danger-600 dark:text-danger-400 text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Meeting Details</h3>
                    <p class="text-sm text-neutral-500 dark:text-dark-400">Review the meeting information before deletion</p>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <!-- Warning Alert -->
            <div class="alert-danger-custom mb-6">
                <i class="fas fa-exclamation-triangle text-danger-600 dark:text-danger-400"></i>
                <div>
                    <p class="font-medium">Permanent Deletion Warning</p>
                    <p class="text-sm">This meeting and all associated data (attendees, action items, documents, minutes) will be permanently deleted. This action cannot be undone.</p>
                </div>
            </div>

            <!-- Meeting Information -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Basic Information -->
                <div class="space-y-6">
                    <div>
                        <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Meeting Title</h4>
                        <p class="text-lg font-semibold text-neutral-900 dark:text-dark-100">@Model.Title</p>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.Description))
                    {
                        <div>
                            <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Description</h4>
                            <p class="text-neutral-700 dark:text-dark-200 bg-neutral-50 dark:bg-dark-700 p-3 rounded-lg">@Model.Description</p>
                        </div>
                    }

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Type</h4>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-neutral-100 dark:bg-dark-600 text-neutral-800 dark:text-dark-200">
                                @Model.Type
                            </span>
                        </div>

                        <div>
                            <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Status</h4>
                            @{
                                var statusClass = Model.Status.ToString().ToLower() switch {
                                    "scheduled" => "bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200",
                                    "inprogress" => "bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200",
                                    "completed" => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
                                    "cancelled" => "bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200",
                                    _ => "bg-neutral-100 dark:bg-dark-600 text-neutral-800 dark:text-dark-200"
                                };
                            }
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium @statusClass">
                                @Model.Status
                            </span>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.Location))
                    {
                        <div>
                            <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Location</h4>
                            <p class="text-neutral-900 dark:text-dark-100 font-medium">
                                <i class="fas fa-map-marker-alt mr-2 text-neutral-400 dark:text-dark-500"></i>
                                @Model.Location
                            </p>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.MeetingLink))
                    {
                        <div>
                            <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Meeting Link</h4>
                            <p class="text-neutral-900 dark:text-dark-100 font-medium break-all">
                                <i class="fas fa-link mr-2 text-neutral-400 dark:text-dark-500"></i>
                                @Model.MeetingLink
                            </p>
                        </div>
                    }
                </div>

                <!-- Date and Additional Info -->
                <div class="space-y-6">
                    <div>
                        <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Scheduled Date & Time</h4>
                        <p class="text-neutral-900 dark:text-dark-100 font-medium">
                            <i class="fas fa-calendar mr-2 text-neutral-400 dark:text-dark-500"></i>
                            @Model.ScheduledDate.ToString("dddd, MMMM dd, yyyy")
                        </p>
                        <p class="text-neutral-900 dark:text-dark-100 font-medium mt-1">
                            <i class="fas fa-clock mr-2 text-neutral-400 dark:text-dark-500"></i>
                            @Model.ScheduledDate.ToString("HH:mm") - @Model.EndTime.ToString("HH:mm") (@Model.DurationMinutes min)
                        </p>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Organizer</h4>
                        <p class="text-neutral-900 dark:text-dark-100 font-medium">
                            <i class="fas fa-user mr-2 text-neutral-400 dark:text-dark-500"></i>
                            @Model.Organizer?.UserName
                        </p>
                    </div>

                    @if (Model.Project != null)
                    {
                        <div>
                            <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Project</h4>
                            <p class="text-neutral-900 dark:text-dark-100 font-medium">
                                <i class="fas fa-project-diagram mr-2 text-neutral-400 dark:text-dark-500"></i>
                                @Model.Project.Name
                            </p>
                        </div>
                    }

                    <div>
                        <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Attendees</h4>
                        <p class="text-neutral-900 dark:text-dark-100 font-medium">
                            <i class="fas fa-users mr-2 text-neutral-400 dark:text-dark-500"></i>
                            @Model.Attendees.Count() participants
                        </p>
                    </div>

                    <div class="space-y-4">
                        <div>
                            <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Created</h4>
                            <p class="text-neutral-900 dark:text-dark-100 font-medium">
                                <i class="fas fa-calendar-plus mr-2 text-neutral-400 dark:text-dark-500"></i>
                                @Model.CreatedAt.ToString("MMM dd, yyyy HH:mm")
                            </p>
                        </div>

                        @if (Model.UpdatedAt.HasValue)
                        {
                            <div>
                                <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Last Updated</h4>
                                <p class="text-neutral-900 dark:text-dark-100 font-medium">
                                    <i class="fas fa-edit mr-2 text-neutral-400 dark:text-dark-500"></i>
                                    @Model.UpdatedAt.Value.ToString("MMM dd, yyyy HH:mm")
                                </p>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Associated Data Warning -->
            @if (Model.Attendees.Any() || Model.MeetingActionItems.Any() || Model.Documents.Any())
            {
                <div class="mt-8 p-4 bg-warning-50 dark:bg-warning-900/20 border border-warning-200 dark:border-warning-800 rounded-lg">
                    <h4 class="text-sm font-medium text-warning-800 dark:text-warning-200 mb-2">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        Associated Data Will Be Deleted
                    </h4>
                    <div class="text-sm text-warning-700 dark:text-warning-300 space-y-1">
                        @if (Model.Attendees.Any())
                        {
                            <p>• @Model.Attendees.Count() attendee records</p>
                        }
                        @if (Model.MeetingActionItems.Any())
                        {
                            <p>• @Model.MeetingActionItems.Count() action items</p>
                        }
                        @if (Model.Documents.Any())
                        {
                            <p>• @Model.Documents.Count() documents</p>
                        }
                        @if (!string.IsNullOrEmpty(Model.Minutes))
                        {
                            <p>• Meeting minutes and notes</p>
                        }
                    </div>
                </div>
            }

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0 pt-8 border-t border-neutral-200 dark:border-dark-200">
                <div>
                    @{
                        ViewData["Text"] = "Cancel";
                        ViewData["Variant"] = "secondary";
                        ViewData["Icon"] = "fas fa-times";
                        ViewData["Href"] = Url.Action("Details", new { id = Model.Id });
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </div>
                <div>
                    <form asp-action="Delete" method="post" class="inline">
                        <input type="hidden" asp-for="Id" />
                        @Html.AntiForgeryToken()
                        @{
                            ViewData["Text"] = "Confirm Delete";
                            ViewData["Variant"] = "danger";
                            ViewData["Icon"] = "fas fa-trash";
                            ViewData["Type"] = "submit";
                            ViewData["Href"] = null;
                            ViewData["OnClick"] = "return confirm('Are you absolutely sure you want to delete this meeting?\\n\\nThis will permanently delete:\\n• The meeting and all its details\\n• All attendee records\\n• All action items\\n• All associated documents\\n• Meeting minutes and notes\\n\\nThis action cannot be undone.')";
                        }
                        <partial name="Components/_Button" view-data="ViewData" />
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
