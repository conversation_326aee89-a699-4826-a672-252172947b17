@model PM.Tool.Core.Entities.Person
@using PM.Tool.Core.Entities

@{
    ViewData["Title"] = "Person Details";
    ViewData["PageTitle"] = $"{Model.FullName} - Details";
    ViewData["PageDescription"] = $"Detailed information for {Model.FullName}";
}

@section Styles {
    <style>
        .detail-card {
            transition: all 0.2s ease-in-out;
        }
        .detail-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.75rem;
        }
    </style>
}

<!-- Page Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8">
    <div class="flex items-center space-x-4">
        <div class="w-16 h-16 @GetPersonTypeIconBg(Model.Type) rounded-xl flex items-center justify-center">
            <i class="@GetPersonTypeIcon(Model.Type) text-white text-2xl"></i>
        </div>
        <div>
            <h1 class="text-3xl font-bold text-neutral-900 dark:text-white mb-1">@Model.FullName</h1>
            <div class="flex items-center space-x-3">
                <span class="text-neutral-600 dark:text-dark-400">@Model.PersonCode</span>
                <span class="status-badge @GetPersonStatusBadgeClass(Model.Status) rounded-full">
                    @Model.Status
                </span>
                <span class="status-badge @GetPersonTypeBadgeClass(Model.Type) rounded-full">
                    @Model.Type
                </span>
            </div>
        </div>
    </div>
    <div class="flex space-x-3 mt-4 lg:mt-0">
        <a href="@Url.Action("Edit", new { id = Model.Id })" class="btn-primary">
            <i class="fas fa-edit mr-2"></i>
            Edit Person
        </a>
        <a href="@Url.Action("Index")" class="btn-secondary">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to People
        </a>
    </div>
</div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Left Column - Main Information -->
    <div class="lg:col-span-2 space-y-6">
        <!-- Basic Information -->
        <div class="detail-card bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
            <h2 class="text-xl font-semibold text-neutral-900 dark:text-white mb-6">Basic Information</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Full Name</label>
                    <p class="text-neutral-900 dark:text-white">@Model.FullName</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Email</label>
                    <p class="text-neutral-900 dark:text-white">
                        <a href="mailto:@Model.Email" class="text-primary-600 hover:text-primary-700">@Model.Email</a>
                    </p>
                </div>

                @if (!string.IsNullOrEmpty(Model.Phone))
                {
                    <div>
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Phone</label>
                        <p class="text-neutral-900 dark:text-white">
                            <a href="tel:@Model.Phone" class="text-primary-600 hover:text-primary-700">@Model.Phone</a>
                        </p>
                    </div>
                }

                @if (!string.IsNullOrEmpty(Model.Title))
                {
                    <div>
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Job Title</label>
                        <p class="text-neutral-900 dark:text-white">@Model.Title</p>
                    </div>
                }

                @if (!string.IsNullOrEmpty(Model.Department))
                {
                    <div>
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Department</label>
                        <p class="text-neutral-900 dark:text-white">@Model.Department</p>
                    </div>
                }

                @if (!string.IsNullOrEmpty(Model.Organization))
                {
                    <div>
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Organization</label>
                        <p class="text-neutral-900 dark:text-white">@Model.Organization</p>
                    </div>
                }

                @if (!string.IsNullOrEmpty(Model.Location))
                {
                    <div>
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Location</label>
                        <p class="text-neutral-900 dark:text-white">@Model.Location</p>
                    </div>
                }

                @if (!string.IsNullOrEmpty(Model.EmployeeId))
                {
                    <div>
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Employee ID</label>
                        <p class="text-neutral-900 dark:text-white">@Model.EmployeeId</p>
                    </div>
                }
            </div>
        </div>

        <!-- System Access & Dates -->
        <div class="detail-card bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
            <h2 class="text-xl font-semibold text-neutral-900 dark:text-white mb-6">System & Employment Information</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">System Access</label>
                    <div class="flex items-center space-x-2">
                        @if (Model.HasSystemAccess)
                        {
                            <i class="fas fa-check-circle text-success-500"></i>
                            <span class="text-success-600 dark:text-success-400">Has Access</span>
                        }
                        else
                        {
                            <i class="fas fa-times-circle text-neutral-400"></i>
                            <span class="text-neutral-600 dark:text-dark-400">No Access</span>
                        }
                    </div>
                </div>

                @if (Model.HireDate.HasValue)
                {
                    <div>
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Hire Date</label>
                        <p class="text-neutral-900 dark:text-white">@Model.HireDate.Value.ToString("MMMM dd, yyyy")</p>
                    </div>
                }

                @if (!string.IsNullOrEmpty(Model.TimeZone))
                {
                    <div>
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Time Zone</label>
                        <p class="text-neutral-900 dark:text-white">@Model.TimeZone</p>
                    </div>
                }

                @if (!string.IsNullOrEmpty(Model.WorkingHours))
                {
                    <div>
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Working Hours</label>
                        <p class="text-neutral-900 dark:text-white">@Model.WorkingHours</p>
                    </div>
                }
            </div>
        </div>

        <!-- Bio & Notes -->
        @if (!string.IsNullOrEmpty(Model.Bio) || !string.IsNullOrEmpty(Model.Notes))
        {
            <div class="detail-card bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
                <h2 class="text-xl font-semibold text-neutral-900 dark:text-white mb-6">Additional Information</h2>

                @if (!string.IsNullOrEmpty(Model.Bio))
                {
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Bio</label>
                        <p class="text-neutral-900 dark:text-white leading-relaxed">@Model.Bio</p>
                    </div>
                }

                @if (!string.IsNullOrEmpty(Model.Notes))
                {
                    <div>
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Notes</label>
                        <p class="text-neutral-900 dark:text-white leading-relaxed">@Model.Notes</p>
                    </div>
                }
            </div>
        }
    </div>

    <!-- Right Column - Quick Actions & Summary -->
    <div class="space-y-6">
        <!-- Quick Actions -->
        <div class="detail-card bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-white mb-4">Quick Actions</h3>

            <div class="space-y-3">
                <a href="@Url.Action("Edit", new { id = Model.Id })" class="w-full btn-primary text-center">
                    <i class="fas fa-edit mr-2"></i>
                    Edit Person
                </a>

                @if (Model.HasSystemAccess)
                {
                    <button type="button" class="w-full btn-warning" onclick="toggleSystemAccess(@Model.Id, false)">
                        <i class="fas fa-user-slash mr-2"></i>
                        Revoke Access
                    </button>
                }
                else
                {
                    <button type="button" class="w-full btn-success" onclick="toggleSystemAccess(@Model.Id, true)">
                        <i class="fas fa-user-check mr-2"></i>
                        Grant Access
                    </button>
                }

                @if (Model.Status == PersonStatus.Active)
                {
                    <button type="button" class="w-full btn-secondary" onclick="togglePersonStatus(@Model.Id, 'Inactive')">
                        <i class="fas fa-user-times mr-2"></i>
                        Deactivate
                    </button>
                }
                else
                {
                    <button type="button" class="w-full btn-success" onclick="togglePersonStatus(@Model.Id, 'Active')">
                        <i class="fas fa-user-check mr-2"></i>
                        Activate
                    </button>
                }
            </div>
        </div>

        <!-- Summary Stats -->
        <div class="detail-card bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-white mb-4">Summary</h3>

            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-neutral-600 dark:text-dark-400">Person Code</span>
                    <span class="text-sm font-medium text-neutral-900 dark:text-white">@Model.PersonCode</span>
                </div>

                <div class="flex justify-between items-center">
                    <span class="text-sm text-neutral-600 dark:text-dark-400">Type</span>
                    <span class="text-sm font-medium text-neutral-900 dark:text-white">@Model.Type</span>
                </div>

                <div class="flex justify-between items-center">
                    <span class="text-sm text-neutral-600 dark:text-dark-400">Status</span>
                    <span class="text-sm font-medium text-neutral-900 dark:text-white">@Model.Status</span>
                </div>

                <div class="flex justify-between items-center">
                    <span class="text-sm text-neutral-600 dark:text-dark-400">Created</span>
                    <span class="text-sm font-medium text-neutral-900 dark:text-white">@Model.CreatedAt.ToString("MMM dd, yyyy")</span>
                </div>

                @if (Model.UpdatedAt.HasValue)
                {
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-neutral-600 dark:text-dark-400">Last Updated</span>
                        <span class="text-sm font-medium text-neutral-900 dark:text-white">@Model.UpdatedAt.Value.ToString("MMM dd, yyyy")</span>
                    </div>
                }
            </div>
        </div>

        <!-- Communication Preferences -->
        @if (!string.IsNullOrEmpty(Model.CommunicationPreferences))
        {
            <div class="detail-card bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-white mb-4">Communication</h3>
                <p class="text-sm text-neutral-600 dark:text-dark-400 leading-relaxed">@Model.CommunicationPreferences</p>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        function toggleSystemAccess(personId, hasAccess) {
            if (confirm(`Are you sure you want to ${hasAccess ? 'grant' : 'revoke'} system access for this person?`)) {
                $.post('@Url.Action("ToggleSystemAccess")', { id: personId, hasAccess: hasAccess })
                    .done(function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('Error updating system access: ' + (response.message || 'Unknown error'));
                        }
                    })
                    .fail(function() {
                        alert('Error updating system access');
                    });
            }
        }

        function togglePersonStatus(personId, status) {
            if (confirm(`Are you sure you want to ${status.toLowerCase()} this person?`)) {
                $.post('@Url.Action("ToggleStatus")', { id: personId, status: status })
                    .done(function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('Error updating person status: ' + (response.message || 'Unknown error'));
                        }
                    })
                    .fail(function() {
                        alert('Error updating person status');
                    });
            }
        }
    </script>
}

@functions {
    private string GetPersonTypeIcon(PersonType type)
    {
        return type switch
        {
            PersonType.Internal => "fas fa-id-badge",
            PersonType.Contractor => "fas fa-user-tie",
            PersonType.External => "fas fa-user",
            PersonType.Customer => "fas fa-handshake",
            PersonType.Vendor => "fas fa-truck",
            PersonType.Partner => "fas fa-users",
            _ => "fas fa-user"
        };
    }

    private string GetPersonTypeIconBg(PersonType type)
    {
        return type switch
        {
            PersonType.Internal => "bg-blue-500",
            PersonType.Contractor => "bg-purple-500",
            PersonType.External => "bg-green-500",
            PersonType.Customer => "bg-pink-500",
            PersonType.Vendor => "bg-orange-500",
            PersonType.Partner => "bg-indigo-500",
            _ => "bg-neutral-500"
        };
    }

    private string GetPersonTypeBadgeClass(PersonType type)
    {
        return type switch
        {
            PersonType.Internal => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
            PersonType.Contractor => "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
            PersonType.External => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            PersonType.Customer => "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200",
            PersonType.Vendor => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
            PersonType.Partner => "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200"
        };
    }

    private string GetPersonStatusBadgeClass(PersonStatus status)
    {
        return status switch
        {
            PersonStatus.Active => "bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200",
            PersonStatus.Inactive => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200",
            PersonStatus.Terminated => "bg-danger-100 text-danger-800 dark:bg-danger-900 dark:text-danger-200",
            PersonStatus.OnLeave => "bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200"
        };
    }
}
