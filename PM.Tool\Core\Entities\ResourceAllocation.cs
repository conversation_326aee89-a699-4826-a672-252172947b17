using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities
{
    public class ResourceAllocation : BaseEntity
    {
        public int ResourceId { get; set; }
        
        public int? ProjectId { get; set; }
        
        public int? TaskId { get; set; }

        private DateTime _startDate;
        public DateTime StartDate
        {
            get => _startDate;
            set => _startDate = DateTime.SpecifyKind(value, DateTimeKind.Utc);
        }

        private DateTime _endDate;
        public DateTime EndDate
        {
            get => _endDate;
            set => _endDate = DateTime.SpecifyKind(value, DateTimeKind.Utc);
        }

        public decimal AllocatedHours { get; set; }

        public decimal AllocationPercentage { get; set; } = 100;

        [MaxLength(500)]
        public string? Notes { get; set; }

        public AllocationStatus Status { get; set; } = AllocationStatus.Planned;

        public string CreatedByUserId { get; set; } = string.Empty;

        // Navigation properties
        public virtual Resource Resource { get; set; } = null!;
        public virtual Project? Project { get; set; }
        public virtual TaskEntity? Task { get; set; }
        public virtual ApplicationUser CreatedBy { get; set; } = null!;

        // Computed properties
        public bool IsActive => Status == AllocationStatus.Active;
        public bool IsOverlapping(ResourceAllocation other) => 
            StartDate < other.EndDate && EndDate > other.StartDate;
        public decimal TotalDays => (decimal)(EndDate - StartDate).TotalDays + 1;
        public decimal DailyHours => AllocatedHours / TotalDays;
    }

    public enum AllocationStatus
    {
        Planned = 1,
        Active = 2,
        Completed = 3,
        Cancelled = 4
    }
}
