using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Models;
using Markdig;
using System.Text.RegularExpressions;
using System.Text.Json;
using Microsoft.Extensions.Caching.Memory;

namespace PM.Tool.Infrastructure.Services
{
    public class DocumentationService : IDocumentationService
    {
        private readonly IWebHostEnvironment _environment;
        private readonly IMemoryCache _cache;
        private readonly ILogger<DocumentationService> _logger;
        private readonly MarkdownPipeline _markdownPipeline;
        private readonly DocumentationConfiguration _configuration;

        private const string CACHE_KEY_SECTIONS = "docs_sections";
        private const string CACHE_KEY_PAGE_PREFIX = "docs_page_";
        private const int CACHE_DURATION_MINUTES = 30;

        public DocumentationService(
            IWebHostEnvironment environment,
            IMemoryCache cache,
            ILogger<DocumentationService> logger)
        {
            _environment = environment;
            _cache = cache;
            _logger = logger;

            _markdownPipeline = new MarkdownPipelineBuilder()
                .UseAdvancedExtensions()
                .Build();

            _configuration = new DocumentationConfiguration();
        }

        public async Task<List<DocumentationSection>> GetAllSectionsAsync()
        {
            if (_cache.TryGetValue(CACHE_KEY_SECTIONS, out List<DocumentationSection>? cachedSections))
            {
                return cachedSections ?? new List<DocumentationSection>();
            }

            var sections = await LoadDocumentationStructureAsync();

            _cache.Set(CACHE_KEY_SECTIONS, sections, TimeSpan.FromMinutes(CACHE_DURATION_MINUTES));

            return sections;
        }

        public async Task<DocumentationPage?> GetPageAsync(string pageId)
        {
            var cacheKey = $"{CACHE_KEY_PAGE_PREFIX}{pageId}";

            if (_cache.TryGetValue(cacheKey, out DocumentationPage? cachedPage))
            {
                return cachedPage;
            }

            var sections = await GetAllSectionsAsync();
            var page = sections
                .SelectMany(s => s.Pages)
                .FirstOrDefault(p => p.Id.Equals(pageId, StringComparison.OrdinalIgnoreCase));

            if (page != null)
            {
                await LoadPageContentAsync(page);
                _cache.Set(cacheKey, page, TimeSpan.FromMinutes(CACHE_DURATION_MINUTES));
            }

            return page;
        }

        public async Task<DocumentationPage?> GetPageAsync(string sectionId, string pageName)
        {
            var sections = await GetAllSectionsAsync();
            var section = sections.FirstOrDefault(s => s.Id.Equals(sectionId, StringComparison.OrdinalIgnoreCase));

            if (section == null) return null;

            var page = section.Pages.FirstOrDefault(p =>
                p.FileName.Equals($"{pageName}.md", StringComparison.OrdinalIgnoreCase) ||
                p.Id.Equals(pageName, StringComparison.OrdinalIgnoreCase));

            if (page != null)
            {
                await LoadPageContentAsync(page);
            }

            return page;
        }

        public async Task<List<DocumentationPage>> GetSectionPagesAsync(string sectionId)
        {
            var sections = await GetAllSectionsAsync();
            var section = sections.FirstOrDefault(s => s.Id.Equals(sectionId, StringComparison.OrdinalIgnoreCase));

            if (section == null) return new List<DocumentationPage>();

            foreach (var page in section.Pages)
            {
                await LoadPageContentAsync(page);
            }

            return section.Pages.OrderBy(p => p.Order).ToList();
        }

        public async Task<List<DocumentationSearchResult>> SearchAsync(string query, int maxResults = 10)
        {
            if (string.IsNullOrWhiteSpace(query)) return new List<DocumentationSearchResult>();

            var sections = await GetAllSectionsAsync();
            var results = new List<DocumentationSearchResult>();
            var searchTerms = query.ToLower().Split(' ', StringSplitOptions.RemoveEmptyEntries);

            foreach (var section in sections)
            {
                foreach (var page in section.Pages)
                {
                    await LoadPageContentAsync(page);

                    var relevance = CalculateRelevance(page, searchTerms);
                    if (relevance > 0)
                    {
                        var excerpt = ExtractExcerpt(page.Content, searchTerms);

                        results.Add(new DocumentationSearchResult
                        {
                            PageId = page.Id,
                            PageTitle = page.Title,
                            SectionTitle = section.Title,
                            Excerpt = excerpt,
                            Url = $"/docs/{section.Id}/{page.Id}",
                            Relevance = relevance,
                            MatchedTerms = searchTerms.Where(term =>
                                page.Content.Contains(term, StringComparison.OrdinalIgnoreCase) ||
                                page.Title.Contains(term, StringComparison.OrdinalIgnoreCase)).ToList()
                        });
                    }
                }
            }

            return results
                .OrderByDescending(r => r.Relevance)
                .Take(maxResults)
                .ToList();
        }

        public async Task<DocumentationNavigation> GetNavigationAsync(string pageId)
        {
            var sections = await GetAllSectionsAsync();
            var allPages = sections.SelectMany(s => s.Pages).OrderBy(p => p.Order).ToList();

            var currentPageIndex = allPages.FindIndex(p => p.Id.Equals(pageId, StringComparison.OrdinalIgnoreCase));

            if (currentPageIndex == -1)
            {
                return new DocumentationNavigation();
            }

            return new DocumentationNavigation
            {
                Previous = currentPageIndex > 0 ? allPages[currentPageIndex - 1] : null,
                Next = currentPageIndex < allPages.Count - 1 ? allPages[currentPageIndex + 1] : null,
                Siblings = allPages.Where(p => p.SectionId == allPages[currentPageIndex].SectionId).ToList()
            };
        }

        public async Task<DocumentationTableOfContents> GetTableOfContentsAsync(string pageId)
        {
            var page = await GetPageAsync(pageId);
            if (page == null) return new DocumentationTableOfContents();

            var toc = new DocumentationTableOfContents();
            var tocItems = new List<DocumentationTocItem>();
            var stack = new Stack<DocumentationTocItem>();

            foreach (var heading in page.Headings)
            {
                var tocItem = new DocumentationTocItem
                {
                    Id = heading.Id,
                    Text = heading.Text,
                    Anchor = heading.Anchor,
                    Level = heading.Level
                };

                while (stack.Count > 0 && stack.Peek().Level >= heading.Level)
                {
                    stack.Pop();
                }

                if (stack.Count == 0)
                {
                    tocItems.Add(tocItem);
                }
                else
                {
                    stack.Peek().Children.Add(tocItem);
                }

                stack.Push(tocItem);
            }

            toc.Items = tocItems;
            return toc;
        }

        public async Task<DocumentationMetadata> GetMetadataAsync()
        {
            var sections = await GetAllSectionsAsync();
            var allPages = sections.SelectMany(s => s.Pages).ToList();

            return new DocumentationMetadata
            {
                TotalPages = allPages.Count,
                TotalSections = sections.Count,
                LastUpdated = allPages.Any() ? allPages.Max(p => p.LastModified) : DateTime.UtcNow,
                Version = _configuration.Version,
                Contributors = new List<string> { "PM Tool Team" },
                Statistics = new Dictionary<string, int>
                {
                    ["total_pages"] = allPages.Count,
                    ["total_sections"] = sections.Count,
                    ["total_headings"] = allPages.Sum(p => p.Headings.Count),
                    ["estimated_read_time_minutes"] = (int)allPages.Sum(p => p.EstimatedReadTime.TotalMinutes)
                }
            };
        }

        public DocumentationConfiguration GetConfiguration()
        {
            return _configuration;
        }

        public string ConvertMarkdownToHtml(string markdown)
        {
            if (string.IsNullOrWhiteSpace(markdown)) return string.Empty;

            return Markdown.ToHtml(markdown, _markdownPipeline);
        }

        public List<DocumentationHeading> ExtractHeadings(string markdown)
        {
            var headings = new List<DocumentationHeading>();
            var lines = markdown.Split('\n');

            for (int i = 0; i < lines.Length; i++)
            {
                var line = lines[i].Trim();
                if (line.StartsWith('#'))
                {
                    var level = line.TakeWhile(c => c == '#').Count();
                    var text = line.Substring(level).Trim();
                    var anchor = GenerateAnchor(text);

                    headings.Add(new DocumentationHeading
                    {
                        Id = $"heading-{i}",
                        Text = text,
                        Level = level,
                        Anchor = anchor
                    });
                }
            }

            return headings;
        }

        public TimeSpan CalculateReadingTime(string content)
        {
            if (string.IsNullOrWhiteSpace(content)) return TimeSpan.Zero;

            // Average reading speed: 200 words per minute
            var wordCount = content.Split(' ', StringSplitOptions.RemoveEmptyEntries).Length;
            var minutes = Math.Max(1, wordCount / 200.0);

            return TimeSpan.FromMinutes(minutes);
        }

        public async Task<List<DocumentationPage>> GetBreadcrumbAsync(string pageId)
        {
            var page = await GetPageAsync(pageId);
            if (page == null) return new List<DocumentationPage>();

            var sections = await GetAllSectionsAsync();
            var section = sections.FirstOrDefault(s => s.Id == page.SectionId);

            var breadcrumb = new List<DocumentationPage>();

            if (section != null)
            {
                // Add section as breadcrumb item
                breadcrumb.Add(new DocumentationPage
                {
                    Id = section.Id,
                    Title = section.Title,
                    SectionId = section.Id
                });
            }

            breadcrumb.Add(page);
            return breadcrumb;
        }

        public async Task<List<DocumentationPage>> GetRelatedPagesAsync(string pageId, int maxResults = 5)
        {
            var currentPage = await GetPageAsync(pageId);
            if (currentPage == null) return new List<DocumentationPage>();

            var sections = await GetAllSectionsAsync();
            var allPages = sections.SelectMany(s => s.Pages)
                .Where(p => p.Id != pageId)
                .ToList();

            // Simple relatedness based on shared tags and section
            var relatedPages = allPages
                .Where(p => p.SectionId == currentPage.SectionId ||
                           p.Tags.Any(tag => currentPage.Tags.Contains(tag)))
                .Take(maxResults)
                .ToList();

            return relatedPages;
        }

        public async Task<byte[]> ExportPageAsync(string pageId, string format)
        {
            var page = await GetPageAsync(pageId);
            if (page == null) throw new ArgumentException($"Page not found: {pageId}");

            return format.ToLower() switch
            {
                "html" => System.Text.Encoding.UTF8.GetBytes(page.HtmlContent),
                "markdown" => System.Text.Encoding.UTF8.GetBytes(page.Content),
                "pdf" => await ConvertToPdfAsync(page.HtmlContent),
                _ => throw new ArgumentException($"Unsupported format: {format}")
            };
        }

        public async Task<byte[]> ExportAllAsync(string format)
        {
            var sections = await GetAllSectionsAsync();
            var allContent = new List<string>();

            foreach (var section in sections)
            {
                allContent.Add($"# {section.Title}\n\n{section.Description}\n\n");

                foreach (var page in section.Pages.OrderBy(p => p.Order))
                {
                    await LoadPageContentAsync(page);
                    allContent.Add(page.Content);
                    allContent.Add("\n\n---\n\n");
                }
            }

            var combinedContent = string.Join("", allContent);

            return format.ToLower() switch
            {
                "html" => System.Text.Encoding.UTF8.GetBytes(ConvertMarkdownToHtml(combinedContent)),
                "markdown" => System.Text.Encoding.UTF8.GetBytes(combinedContent),
                "pdf" => await ConvertToPdfAsync(ConvertMarkdownToHtml(combinedContent)),
                _ => throw new ArgumentException($"Unsupported format: {format}")
            };
        }

        public async Task RefreshCacheAsync()
        {
            _cache.Remove(CACHE_KEY_SECTIONS);

            var sections = await GetAllSectionsAsync();
            foreach (var section in sections)
            {
                foreach (var page in section.Pages)
                {
                    _cache.Remove($"{CACHE_KEY_PAGE_PREFIX}{page.Id}");
                }
            }

            _logger.LogInformation("Documentation cache refreshed");
        }

        public async Task<List<DocumentationPage>> GetPopularPagesAsync(int maxResults = 10)
        {
            // This would typically come from analytics data
            // For now, return the first few pages from each section
            var sections = await GetAllSectionsAsync();
            return sections
                .SelectMany(s => s.Pages.Take(2))
                .Take(maxResults)
                .ToList();
        }

        public async Task TrackPageViewAsync(string pageId, string? userId = null)
        {
            // Implementation would track page views in database or analytics service
            _logger.LogInformation("Page view tracked: {PageId} by {UserId}", pageId, userId ?? "anonymous");
            await Task.CompletedTask;
        }

        // Private helper methods
        private async Task<List<DocumentationSection>> LoadDocumentationStructureAsync()
        {
            var sections = new List<DocumentationSection>
            {
                new DocumentationSection
                {
                    Id = "overview",
                    Title = "System Overview",
                    Description = "Comprehensive system architecture and features overview",
                    Icon = "fas fa-home",
                    Order = 1,
                    Pages = new List<DocumentationPage>
                    {
                        new DocumentationPage
                        {
                            Id = "system-overview",
                            Title = "System Overview",
                            FileName = "SYSTEM_OVERVIEW.md",
                            FilePath = "Documentation/SYSTEM_OVERVIEW.md",
                            SectionId = "overview",
                            Order = 1,
                            Tags = new List<string> { "overview", "architecture", "features" }
                        }
                    }
                },
                new DocumentationSection
                {
                    Id = "users",
                    Title = "User Guides",
                    Description = "Complete guides for end users and project managers",
                    Icon = "fas fa-users",
                    Order = 2,
                    Pages = new List<DocumentationPage>
                    {
                        new DocumentationPage
                        {
                            Id = "user-guide",
                            Title = "User Guide",
                            FileName = "USER_GUIDE.md",
                            FilePath = "Documentation/USER_GUIDE.md",
                            SectionId = "users",
                            Order = 1,
                            Tags = new List<string> { "user", "guide", "tutorial" }
                        },
                        new DocumentationPage
                        {
                            Id = "project-manager-guide",
                            Title = "Project Manager Guide",
                            FileName = "PROJECT_MANAGER_GUIDE.md",
                            FilePath = "Documentation/PROJECT_MANAGER_GUIDE.md",
                            SectionId = "users",
                            Order = 2,
                            Tags = new List<string> { "project-manager", "advanced", "guide" }
                        }
                    }
                },
                new DocumentationSection
                {
                    Id = "administration",
                    Title = "Administration",
                    Description = "System administration and deployment guides",
                    Icon = "fas fa-cogs",
                    Order = 3,
                    Pages = new List<DocumentationPage>
                    {
                        new DocumentationPage
                        {
                            Id = "system-admin-guide",
                            Title = "System Administrator Guide",
                            FileName = "SYSTEM_ADMIN_GUIDE.md",
                            FilePath = "Documentation/SYSTEM_ADMIN_GUIDE.md",
                            SectionId = "administration",
                            Order = 1,
                            Tags = new List<string> { "admin", "configuration", "maintenance" }
                        },
                        new DocumentationPage
                        {
                            Id = "deployment-guide",
                            Title = "Deployment Guide",
                            FileName = "DEPLOYMENT_GUIDE.md",
                            FilePath = "Documentation/DEPLOYMENT_GUIDE.md",
                            SectionId = "administration",
                            Order = 2,
                            Tags = new List<string> { "deployment", "docker", "cloud" }
                        }
                    }
                },
                new DocumentationSection
                {
                    Id = "development",
                    Title = "Development",
                    Description = "Developer guides and API documentation",
                    Icon = "fas fa-code",
                    Order = 4,
                    Pages = new List<DocumentationPage>
                    {
                        new DocumentationPage
                        {
                            Id = "developer-guide",
                            Title = "Developer Guide",
                            FileName = "DEVELOPER_GUIDE.md",
                            FilePath = "Documentation/DEVELOPER_GUIDE.md",
                            SectionId = "development",
                            Order = 1,
                            Tags = new List<string> { "developer", "architecture", "coding" }
                        },
                        new DocumentationPage
                        {
                            Id = "api-documentation",
                            Title = "API Documentation",
                            FileName = "API_DOCUMENTATION.md",
                            FilePath = "Documentation/API_DOCUMENTATION.md",
                            SectionId = "development",
                            Order = 2,
                            Tags = new List<string> { "api", "rest", "integration" }
                        }
                    }
                },
                new DocumentationSection
                {
                    Id = "reference",
                    Title = "Reference",
                    Description = "Additional reference materials and reviews",
                    Icon = "fas fa-book",
                    Order = 5,
                    Pages = new List<DocumentationPage>
                    {
                        new DocumentationPage
                        {
                            Id = "final-review",
                            Title = "Final System Review",
                            FileName = "FINAL_REVIEW.md",
                            FilePath = "Documentation/FINAL_REVIEW.md",
                            SectionId = "reference",
                            Order = 1,
                            Tags = new List<string> { "review", "assessment", "completeness" }
                        }
                    }
                }
            };

            return sections;
        }

        private async Task LoadPageContentAsync(DocumentationPage page)
        {
            if (!string.IsNullOrEmpty(page.Content)) return; // Already loaded

            try
            {
                var filePath = Path.Combine(_environment.ContentRootPath, page.FilePath);

                if (File.Exists(filePath))
                {
                    page.Content = await File.ReadAllTextAsync(filePath);
                    page.HtmlContent = ConvertMarkdownToHtml(page.Content);
                    page.Headings = ExtractHeadings(page.Content);
                    page.EstimatedReadTime = CalculateReadingTime(page.Content);
                    page.LastModified = File.GetLastWriteTime(filePath);
                }
                else
                {
                    _logger.LogWarning("Documentation file not found: {FilePath}", filePath);
                    page.Content = $"# {page.Title}\n\nDocumentation file not found: {page.FilePath}";
                    page.HtmlContent = ConvertMarkdownToHtml(page.Content);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading documentation page: {PageId}", page.Id);
                page.Content = $"# {page.Title}\n\nError loading documentation content.";
                page.HtmlContent = ConvertMarkdownToHtml(page.Content);
            }
        }

        private double CalculateRelevance(DocumentationPage page, string[] searchTerms)
        {
            double relevance = 0;

            foreach (var term in searchTerms)
            {
                // Title matches are more relevant
                if (page.Title.Contains(term, StringComparison.OrdinalIgnoreCase))
                {
                    relevance += 10;
                }

                // Content matches
                var contentMatches = Regex.Matches(page.Content, Regex.Escape(term), RegexOptions.IgnoreCase).Count;
                relevance += contentMatches * 1;

                // Tag matches
                if (page.Tags.Any(tag => tag.Contains(term, StringComparison.OrdinalIgnoreCase)))
                {
                    relevance += 5;
                }
            }

            return relevance;
        }

        private string ExtractExcerpt(string content, string[] searchTerms)
        {
            const int excerptLength = 200;

            // Find the first occurrence of any search term
            var firstMatch = int.MaxValue;
            foreach (var term in searchTerms)
            {
                var index = content.IndexOf(term, StringComparison.OrdinalIgnoreCase);
                if (index >= 0 && index < firstMatch)
                {
                    firstMatch = index;
                }
            }

            if (firstMatch == int.MaxValue)
            {
                return content.Length > excerptLength ? content.Substring(0, excerptLength) + "..." : content;
            }

            var start = Math.Max(0, firstMatch - 50);
            var length = Math.Min(excerptLength, content.Length - start);
            var excerpt = content.Substring(start, length);

            if (start > 0) excerpt = "..." + excerpt;
            if (start + length < content.Length) excerpt += "...";

            return excerpt;
        }

        private string GenerateAnchor(string text)
        {
            return text.ToLower()
                .Replace(" ", "-")
                .Replace(".", "")
                .Replace(",", "")
                .Replace(":", "")
                .Replace(";", "")
                .Replace("!", "")
                .Replace("?", "")
                .Replace("(", "")
                .Replace(")", "")
                .Replace("[", "")
                .Replace("]", "")
                .Replace("{", "")
                .Replace("}", "");
        }

        private async Task<byte[]> ConvertToPdfAsync(string htmlContent)
        {
            // This would require a PDF generation library like PuppeteerSharp or iTextSharp
            // For now, return the HTML content as bytes
            await Task.CompletedTask;
            return System.Text.Encoding.UTF8.GetBytes(htmlContent);
        }
    }
}
