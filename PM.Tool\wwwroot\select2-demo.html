<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Select2 Demo - PM Tool</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/saas-theme.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/tailwind.css">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="css/select2-tailwind.css">
    
    <style>
        body {
            font-family: var(--font-family);
            background-color: var(--color-bg);
            color: var(--color-text-primary);
            margin: 0;
            padding: 2rem;
            transition: all 0.3s ease;
        }
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .demo-section {
            margin-bottom: 3rem;
        }
        .demo-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--color-text-primary);
        }
        .demo-subtitle {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--color-text-secondary);
        }
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .theme-toggle {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="theme-toggle">
        <button id="themeToggle" class="btn-enterprise btn-primary-enterprise">
            🌙 Toggle Dark Mode
        </button>
    </div>

    <div class="demo-container">
        <header class="demo-section">
            <h1 class="demo-title">Enhanced Select2 Components</h1>
            <p class="demo-subtitle">Professional, accessible, and theme-aware dropdown components</p>
        </header>

        <!-- Basic Select2 Examples -->
        <section class="demo-section">
            <h2 class="demo-subtitle">Basic Select2 Dropdowns</h2>
            <div class="demo-grid">
                <div class="card-enterprise">
                    <div class="card-header-enterprise">
                        <h3 class="card-title-enterprise">Standard Dropdown</h3>
                    </div>
                    <div class="card-body-enterprise">
                        <div class="form-group-enterprise">
                            <label class="form-label-enterprise" for="basicSelect">Choose an Option</label>
                            <select id="basicSelect" class="select2-enabled" style="width: 100%;">
                                <option value="">Select an option...</option>
                                <option value="option1">Option 1</option>
                                <option value="option2">Option 2</option>
                                <option value="option3">Option 3</option>
                                <option value="option4">Option 4</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="card-enterprise">
                    <div class="card-header-enterprise">
                        <h3 class="card-title-enterprise">Person Type Filter</h3>
                    </div>
                    <div class="card-body-enterprise">
                        <div class="form-group-enterprise">
                            <label class="form-label-enterprise" for="personType">Person Type</label>
                            <select id="personType" class="select2-enabled" style="width: 100%;">
                                <option value="">All Types</option>
                                <option value="Internal">Employees</option>
                                <option value="Contractor">Contractors</option>
                                <option value="External">Stakeholders</option>
                                <option value="Vendor">Vendors</option>
                                <option value="Customer">Customers</option>
                                <option value="Partner">Partners</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Multiple Selection -->
        <section class="demo-section">
            <h2 class="demo-subtitle">Multiple Selection</h2>
            <div class="demo-grid">
                <div class="card-enterprise">
                    <div class="card-header-enterprise">
                        <h3 class="card-title-enterprise">Multi-Select Tags</h3>
                    </div>
                    <div class="card-body-enterprise">
                        <div class="form-group-enterprise">
                            <label class="form-label-enterprise" for="multiSelect">Select Multiple Options</label>
                            <select id="multiSelect" class="select2-enabled" multiple style="width: 100%;">
                                <option value="tag1">Project Management</option>
                                <option value="tag2">Development</option>
                                <option value="tag3">Design</option>
                                <option value="tag4">Testing</option>
                                <option value="tag5">Documentation</option>
                                <option value="tag6">Marketing</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="card-enterprise">
                    <div class="card-header-enterprise">
                        <h3 class="card-title-enterprise">Team Members</h3>
                    </div>
                    <div class="card-body-enterprise">
                        <div class="form-group-enterprise">
                            <label class="form-label-enterprise" for="teamMembers">Assign Team Members</label>
                            <select id="teamMembers" class="select2-enabled" multiple style="width: 100%;">
                                <option value="user1">John Doe</option>
                                <option value="user2">Jane Smith</option>
                                <option value="user3">Mike Johnson</option>
                                <option value="user4">Sarah Wilson</option>
                                <option value="user5">David Brown</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Different States -->
        <section class="demo-section">
            <h2 class="demo-subtitle">Different States</h2>
            <div class="demo-grid">
                <div class="card-enterprise">
                    <div class="card-header-enterprise">
                        <h3 class="card-title-enterprise">Validation States</h3>
                    </div>
                    <div class="card-body-enterprise">
                        <div class="form-group-enterprise">
                            <label class="form-label-enterprise" for="successSelect">Success State</label>
                            <select id="successSelect" class="select2-enabled has-success" style="width: 100%;">
                                <option value="">Select an option...</option>
                                <option value="valid" selected>Valid Selection</option>
                            </select>
                        </div>
                        
                        <div class="form-group-enterprise">
                            <label class="form-label-enterprise" for="errorSelect">Error State</label>
                            <select id="errorSelect" class="select2-enabled has-error" style="width: 100%;">
                                <option value="">Select an option...</option>
                                <option value="option1">Option 1</option>
                                <option value="option2">Option 2</option>
                            </select>
                        </div>
                        
                        <div class="form-group-enterprise">
                            <label class="form-label-enterprise" for="disabledSelect">Disabled State</label>
                            <select id="disabledSelect" class="select2-enabled" disabled style="width: 100%;">
                                <option value="">Cannot select...</option>
                                <option value="option1">Option 1</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="card-enterprise">
                    <div class="card-header-enterprise">
                        <h3 class="card-title-enterprise">Size Variants</h3>
                    </div>
                    <div class="card-body-enterprise">
                        <div class="form-group-enterprise">
                            <label class="form-label-enterprise" for="smallSelect">Small Size</label>
                            <select id="smallSelect" class="select2-enabled select2-sm" style="width: 100%;">
                                <option value="">Small dropdown...</option>
                                <option value="option1">Option 1</option>
                                <option value="option2">Option 2</option>
                            </select>
                        </div>
                        
                        <div class="form-group-enterprise">
                            <label class="form-label-enterprise" for="normalSelect">Normal Size</label>
                            <select id="normalSelect" class="select2-enabled" style="width: 100%;">
                                <option value="">Normal dropdown...</option>
                                <option value="option1">Option 1</option>
                                <option value="option2">Option 2</option>
                            </select>
                        </div>
                        
                        <div class="form-group-enterprise">
                            <label class="form-label-enterprise" for="largeSelect">Large Size</label>
                            <select id="largeSelect" class="select2-enabled select2-lg" style="width: 100%;">
                                <option value="">Large dropdown...</option>
                                <option value="option1">Option 1</option>
                                <option value="option2">Option 2</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Searchable Options -->
        <section class="demo-section">
            <h2 class="demo-subtitle">Searchable Dropdowns</h2>
            <div class="demo-grid">
                <div class="card-enterprise">
                    <div class="card-header-enterprise">
                        <h3 class="card-title-enterprise">Large Dataset</h3>
                    </div>
                    <div class="card-body-enterprise">
                        <div class="form-group-enterprise">
                            <label class="form-label-enterprise" for="searchableSelect">Search Countries</label>
                            <select id="searchableSelect" class="select2-enabled" style="width: 100%;">
                                <option value="">Search for a country...</option>
                                <option value="us">United States</option>
                                <option value="ca">Canada</option>
                                <option value="uk">United Kingdom</option>
                                <option value="de">Germany</option>
                                <option value="fr">France</option>
                                <option value="jp">Japan</option>
                                <option value="au">Australia</option>
                                <option value="br">Brazil</option>
                                <option value="in">India</option>
                                <option value="cn">China</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="js/select2-init.js"></script>
    
    <script>
        // Theme toggle functionality
        const themeToggle = document.getElementById('themeToggle');
        const html = document.documentElement;
        
        themeToggle.addEventListener('click', () => {
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            html.setAttribute('data-theme', newTheme);
            themeToggle.textContent = newTheme === 'dark' ? '☀️ Toggle Light Mode' : '🌙 Toggle Dark Mode';
            
            // Save preference
            localStorage.setItem('theme-preference', newTheme);
        });
        
        // Load saved theme
        const savedTheme = localStorage.getItem('theme-preference') || 'light';
        html.setAttribute('data-theme', savedTheme);
        themeToggle.textContent = savedTheme === 'dark' ? '☀️ Toggle Light Mode' : '🌙 Toggle Dark Mode';
        
        // Initialize Select2 when document is ready
        $(document).ready(function() {
            // The select2-init.js will automatically initialize all .select2-enabled elements
            console.log('Select2 demo page loaded');
        });
    </script>
</body>
</html>
