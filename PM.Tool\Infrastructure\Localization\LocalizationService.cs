using System.Globalization;
using System.Resources;
using Microsoft.Extensions.Localization;
using PM.Tool.Core.Localization;
using System.ComponentModel.DataAnnotations;
using System.Reflection;

namespace PM.Tool.Infrastructure.Localization
{
    public class LocalizationService : ILocalizationService
    {
        private readonly IStringLocalizer _localizer;
        private readonly IServiceProvider _serviceProvider;

        private static readonly CultureInfo[] SupportedCultures = new[]
        {
            new CultureInfo("en-US"), // English (US)
            new CultureInfo("en-GB"), // English (UK)
            new CultureInfo("es-ES"), // Spanish (Spain)
            new CultureInfo("es-MX"), // Spanish (Mexico)
            new CultureInfo("fr-FR"), // French (France)
            new CultureInfo("de-DE"), // German (Germany)
            new CultureInfo("it-IT"), // Italian (Italy)
            new CultureInfo("pt-BR"), // Portuguese (Brazil)
            new CultureInfo("pt-PT"), // Portuguese (Portugal)
            new CultureInfo("ru-RU"), // Russian (Russia)
            new CultureInfo("zh-CN"), // Chinese (Simplified)
            new CultureInfo("zh-TW"), // Chinese (Traditional)
            new CultureInfo("ja-JP"), // Japanese (Japan)
            new CultureInfo("ko-KR"), // Korean (Korea)
            new CultureInfo("ar-SA"), // Arabic (Saudi Arabia)
            new CultureInfo("he-IL"), // Hebrew (Israel)
            new CultureInfo("hi-IN"), // Hindi (India)
            new CultureInfo("bn-BD"), // Bengali (Bangladesh)
            new CultureInfo("th-TH"), // Thai (Thailand)
            new CultureInfo("vi-VN"), // Vietnamese (Vietnam)
            new CultureInfo("tr-TR"), // Turkish (Turkey)
            new CultureInfo("pl-PL"), // Polish (Poland)
            new CultureInfo("nl-NL"), // Dutch (Netherlands)
            new CultureInfo("sv-SE"), // Swedish (Sweden)
            new CultureInfo("da-DK"), // Danish (Denmark)
            new CultureInfo("no-NO"), // Norwegian (Norway)
            new CultureInfo("fi-FI"), // Finnish (Finland)
        };

        public LocalizationService(IStringLocalizer<LocalizationService> localizer, IServiceProvider serviceProvider)
        {
            _localizer = localizer;
            _serviceProvider = serviceProvider;
        }

        public string GetString(string key, params object[] args)
        {
            var localizedString = _localizer[key, args];
            return localizedString.ResourceNotFound ? key : localizedString.Value;
        }

        public string GetString(string key, CultureInfo culture, params object[] args)
        {
            var currentCulture = CultureInfo.CurrentCulture;
            var currentUICulture = CultureInfo.CurrentUICulture;

            try
            {
                CultureInfo.CurrentCulture = culture;
                CultureInfo.CurrentUICulture = culture;

                return GetString(key, args);
            }
            finally
            {
                CultureInfo.CurrentCulture = currentCulture;
                CultureInfo.CurrentUICulture = currentUICulture;
            }
        }

        public IEnumerable<CultureInfo> GetSupportedCultures()
        {
            return SupportedCultures;
        }

        public CultureInfo GetCurrentCulture()
        {
            return CultureInfo.CurrentCulture;
        }

        public void SetCurrentCulture(string cultureName)
        {
            var culture = SupportedCultures.FirstOrDefault(c =>
                c.Name.Equals(cultureName, StringComparison.OrdinalIgnoreCase));

            if (culture != null)
            {
                CultureInfo.CurrentCulture = culture;
                CultureInfo.CurrentUICulture = culture;
            }
        }

        public string GetDateFormat()
        {
            return CultureInfo.CurrentCulture.DateTimeFormat.ShortDatePattern;
        }

        public string GetTimeFormat()
        {
            return CultureInfo.CurrentCulture.DateTimeFormat.ShortTimePattern;
        }

        public string GetCurrencyFormat()
        {
            return CultureInfo.CurrentCulture.NumberFormat.CurrencySymbol;
        }

        public string GetNumberFormat()
        {
            return CultureInfo.CurrentCulture.NumberFormat.NumberDecimalSeparator;
        }

        public string FormatDate(DateTime date)
        {
            return date.ToString("d", CultureInfo.CurrentCulture);
        }

        public string FormatCurrency(decimal amount)
        {
            return amount.ToString("C", CultureInfo.CurrentCulture);
        }

        public string FormatNumber(decimal number)
        {
            return number.ToString("N", CultureInfo.CurrentCulture);
        }

        public string GetTextDirection()
        {
            return IsRightToLeft() ? "rtl" : "ltr";
        }

        public bool IsRightToLeft()
        {
            return CultureInfo.CurrentCulture.TextInfo.IsRightToLeft;
        }

        public string GetEnumDisplayName<T>(T enumValue) where T : struct, Enum
        {
            var field = enumValue.GetType().GetField(enumValue.ToString());
            var displayAttribute = field?.GetCustomAttribute<DisplayAttribute>();

            if (displayAttribute?.Name != null)
            {
                return GetString(displayAttribute.Name);
            }

            // Fallback to enum name with localization key
            var key = $"Enum.{typeof(T).Name}.{enumValue}";
            return GetString(key);
        }

        public Dictionary<T, string> GetEnumDisplayNames<T>() where T : struct, Enum
        {
            var result = new Dictionary<T, string>();
            var enumValues = Enum.GetValues<T>();

            foreach (var enumValue in enumValues)
            {
                result[enumValue] = GetEnumDisplayName(enumValue);
            }

            return result;
        }
    }
}
