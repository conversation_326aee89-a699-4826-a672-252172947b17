using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.Extensions.Logging;
using Moq;
using PM.Tool.Controllers;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Interfaces;
using PM.Tool.Models.ViewModels;
using PM.Tool.Services;
using PM.Tool.Tests.Helpers;
using Xunit;
using FluentAssertions;

using TaskStatus = PM.Tool.Core.Enums.TaskStatus;

namespace PM.Tool.Tests.Unit.Controllers
{
    public class TasksControllerTests
    {
        private readonly Mock<ITaskService> _mockTaskService;
        private readonly Mock<IProjectService> _mockProjectService;
        private readonly Mock<UserManager<ApplicationUser>> _mockUserManager;
        private readonly Mock<IFormHelperService> _mockFormHelper;
        private readonly Mock<IAuditService> _mockAuditService;
        private readonly Mock<ILogger<TasksController>> _mockLogger;
        private readonly TasksController _controller;
        private readonly ApplicationUser _testUser;

        public TasksControllerTests()
        {
            _mockTaskService = new Mock<ITaskService>();
            _mockProjectService = new Mock<IProjectService>();
            _mockUserManager = MockHelper.CreateMockUserManager();
            _mockFormHelper = new Mock<IFormHelperService>();
            _mockAuditService = MockHelper.CreateMockAuditService();
            _mockLogger = MockHelper.CreateMockLogger<TasksController>();

            _controller = new TasksController(
                _mockTaskService.Object,
                _mockProjectService.Object,
                _mockUserManager.Object,
                _mockFormHelper.Object,
                _mockAuditService.Object,
                _mockLogger.Object);

            _testUser = new ApplicationUser
            {
                Id = "test-user-1",
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Test",
                LastName = "User",
                IsActive = true
            };

            MockHelper.SetupControllerContext(_controller);

            // Setup TempData
            _controller.TempData = new TempDataDictionary(_controller.ControllerContext.HttpContext, Mock.Of<ITempDataProvider>());
        }

        [Fact]
        public async Task Index_WithoutProjectId_ReturnsUserTasks()
        {
            // Arrange
            var project = new Project { Id = 1, Name = "Test Project" };
            var tasks = new List<TaskEntity>
            {
                new TaskEntity
                {
                    Id = 1,
                    Title = "Test Task 1",
                    Description = "Test Description 1",
                    Status = TaskStatus.ToDo,
                    Priority = TaskPriority.Medium,
                    ProjectId = 1,
                    Project = project,
                    CreatedBy = _testUser,
                    CreatedByUserId = _testUser.Id,
                    CreatedAt = DateTime.UtcNow.AddDays(-5),
                    UpdatedAt = DateTime.UtcNow.AddDays(-5)
                },
                new TaskEntity
                {
                    Id = 2,
                    Title = "Test Task 2",
                    Description = "Test Description 2",
                    Status = TaskStatus.InProgress,
                    Priority = TaskPriority.High,
                    ProjectId = 1,
                    Project = project,
                    CreatedBy = _testUser,
                    CreatedByUserId = _testUser.Id,
                    CreatedAt = DateTime.UtcNow.AddDays(-3),
                    UpdatedAt = DateTime.UtcNow.AddDays(-3)
                }
            };

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockTaskService.Setup(x => x.GetUserTasksAsync(_testUser.Id))
                .ReturnsAsync(tasks);

            // Act
            var result = await _controller.Index(null, null, null);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<List<TaskViewModel>>();
            var model = viewResult.Model as List<TaskViewModel>;
            model!.Should().HaveCount(2);
            model[0].Title.Should().Be("Test Task 1");
            model[1].Title.Should().Be("Test Task 2");
        }

        [Fact]
        public async Task Index_WithProjectId_ReturnsProjectTasks()
        {
            // Arrange
            var projectId = 1;
            var project = new Project { Id = projectId, Name = "Test Project", CreatedByUserId = _testUser.Id };
            var tasks = new List<TaskEntity>
            {
                new TaskEntity
                {
                    Id = 1,
                    Title = "Project Task",
                    ProjectId = projectId,
                    Project = project,
                    CreatedBy = _testUser,
                    CreatedByUserId = _testUser.Id,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            };

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockProjectService.Setup(x => x.GetUserRoleInProjectAsync(projectId, _testUser.Id))
                .ReturnsAsync(UserRole.ProjectManager);
            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync(project);
            _mockTaskService.Setup(x => x.GetProjectTasksAsync(projectId))
                .ReturnsAsync(tasks);

            // Act
            var result = await _controller.Index(projectId, null, null);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<List<TaskViewModel>>();
            var model = viewResult.Model as List<TaskViewModel>;
            model!.Should().HaveCount(1);
            model[0].Title.Should().Be("Project Task");
        }

        [Fact]
        public async Task Index_WithUnauthorizedProject_ReturnsForbid()
        {
            // Arrange
            var projectId = 1;
            var project = new Project { Id = projectId, CreatedByUserId = "other-user-id" };

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockProjectService.Setup(x => x.GetUserRoleInProjectAsync(projectId, _testUser.Id))
                .ReturnsAsync((UserRole?)null);
            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync(project);

            // Act
            var result = await _controller.Index(projectId, null, null);

            // Assert
            result.Should().BeOfType<ForbidResult>();
        }

        [Fact]
        public async Task Index_WithStatusFilter_ReturnsFilteredTasks()
        {
            // Arrange
            var project = new Project { Id = 1, Name = "Test Project" };
            var tasks = new List<TaskEntity>
            {
                new TaskEntity
                {
                    Id = 1,
                    Title = "Todo Task",
                    Status = TaskStatus.ToDo,
                    Project = project,
                    CreatedBy = _testUser,
                    CreatedByUserId = _testUser.Id,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new TaskEntity
                {
                    Id = 2,
                    Title = "In Progress Task",
                    Status = TaskStatus.InProgress,
                    Project = project,
                    CreatedBy = _testUser,
                    CreatedByUserId = _testUser.Id,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            };

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockTaskService.Setup(x => x.GetUserTasksAsync(_testUser.Id))
                .ReturnsAsync(tasks);

            // Act
            var result = await _controller.Index(null, TaskStatus.ToDo, null);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<List<TaskViewModel>>();
            var model = viewResult.Model as List<TaskViewModel>;
            model!.Should().HaveCount(1);
            model[0].Title.Should().Be("Todo Task");
            model[0].Status.Should().Be(TaskStatus.ToDo);
        }

        [Fact]
        public async Task Index_WithNullUser_RedirectsToLogin()
        {
            // Arrange
            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync((ApplicationUser)null);

            // Act
            var result = await _controller.Index(null, null, null);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult.ActionName.Should().Be("Login");
            redirectResult.ControllerName.Should().Be("Account");
        }

        [Fact]
        public async Task Index_WithException_ReturnsEmptyView()
        {
            // Arrange
            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockTaskService.Setup(x => x.GetUserTasksAsync(_testUser.Id))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.Index(null, null, null);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult.Model.Should().BeOfType<List<TaskViewModel>>();
            var model = viewResult.Model as List<TaskViewModel>;
            model.Should().BeEmpty();
        }

        [Fact]
        public async Task Details_WithValidTask_ReturnsViewWithDetails()
        {
            // Arrange
            var taskId = 1;
            var project = new Project { Id = 1, Name = "Test Project", CreatedByUserId = _testUser.Id };
            var task = new TaskEntity
            {
                Id = taskId,
                Title = "Test Task",
                Description = "Test Description",
                Status = TaskStatus.ToDo,
                Priority = TaskPriority.Medium,
                ProjectId = 1,
                Project = project,
                CreatedBy = _testUser,
                CreatedByUserId = _testUser.Id,
                SubTasks = new List<TaskEntity>(),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            var comments = new List<TaskComment>();

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockTaskService.Setup(x => x.GetTaskWithDetailsAsync(taskId))
                .ReturnsAsync(task);
            _mockProjectService.Setup(x => x.GetUserRoleInProjectAsync(task.ProjectId, _testUser.Id))
                .ReturnsAsync(UserRole.ProjectManager);
            _mockProjectService.Setup(x => x.GetProjectByIdAsync(task.ProjectId))
                .ReturnsAsync(project);
            _mockTaskService.Setup(x => x.GetTaskCommentsAsync(taskId))
                .ReturnsAsync(comments);

            // Act
            var result = await _controller.Details(taskId);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<TaskDetailsViewModel>();
            var model = viewResult.Model as TaskDetailsViewModel;
            model!.Id.Should().Be(taskId);
            model.Title.Should().Be("Test Task");
            model.CanEdit.Should().BeTrue();
        }

        [Fact]
        public async Task Details_WithNonExistentTask_ReturnsNotFound()
        {
            // Arrange
            var taskId = 999;
            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockTaskService.Setup(x => x.GetTaskWithDetailsAsync(taskId))
                .ReturnsAsync((TaskEntity)null);

            // Act
            var result = await _controller.Details(taskId);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task Create_WithProjectId_ReturnsViewWithProjectInfo()
        {
            // Arrange
            var projectId = 1;
            var project = new Project
            {
                Id = projectId,
                Name = "Test Project",
                CreatedByUserId = _testUser.Id
            };

            var members = new List<ProjectMember>
            {
                new ProjectMember
                {
                    UserId = _testUser.Id,
                    User = _testUser
                }
            };

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync(project);
            _mockProjectService.Setup(x => x.GetUserRoleInProjectAsync(projectId, _testUser.Id))
                .ReturnsAsync(UserRole.ProjectManager);
            _mockProjectService.Setup(x => x.GetProjectMembersAsync(projectId))
                .ReturnsAsync(members);

            // Act
            var result = await _controller.Create(projectId, null);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult.Model.Should().BeOfType<TaskCreateViewModel>();
            var model = viewResult.Model as TaskCreateViewModel;
            model.ProjectId.Should().Be(projectId);
            model.ProjectName.Should().Be("Test Project");
            model.AvailableUsers.Should().HaveCount(1);
        }

        // Tests for missing POST Create method
        [Fact]
        public async Task Create_Post_WithValidModel_CreatesTaskAndRedirects()
        {
            // Arrange
            var model = new TaskCreateViewModelEnhanced
            {
                Title = "New Task",
                Description = "New Description",
                ProjectId = 1,
                Priority = TaskPriority.Medium,
                AssignedToUserId = _testUser.Id,
                DueDate = DateTime.UtcNow.AddDays(7)
            };

            var project = new Project { Id = 1, Name = "Test Project", CreatedByUserId = _testUser.Id };
            var createdTask = new TaskEntity
            {
                Id = 1,
                Title = model.Title,
                Description = model.Description,
                ProjectId = model.ProjectId,
                CreatedByUserId = _testUser.Id
            };

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockProjectService.Setup(x => x.GetUserRoleInProjectAsync(model.ProjectId, _testUser.Id))
                .ReturnsAsync(UserRole.ProjectManager);
            _mockProjectService.Setup(x => x.GetProjectByIdAsync(model.ProjectId))
                .ReturnsAsync(project);
            _mockProjectService.Setup(x => x.GetProjectMembersAsync(model.ProjectId))
                .ReturnsAsync(new List<ProjectMember>());
            _mockTaskService.Setup(x => x.CreateTaskAsync(It.IsAny<TaskEntity>()))
                .ReturnsAsync(createdTask);

            // Act
            var result = await _controller.Create(model);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("Details");
            redirectResult.RouteValues!["id"].Should().Be(1);
        }

        [Fact]
        public async Task Create_Post_WithInvalidModel_ReturnsViewWithModel()
        {
            // Arrange
            var model = new TaskCreateViewModelEnhanced
            {
                // Missing Title - this will make ModelState invalid
                ProjectId = 1
            };
            _controller.ModelState.AddModelError("Title", "Title is required");

            var project = new Project { Id = 1, Name = "Test Project", CreatedByUserId = _testUser.Id };
            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockProjectService.Setup(x => x.GetUserRoleInProjectAsync(model.ProjectId, _testUser.Id))
                .ReturnsAsync(UserRole.ProjectManager);
            _mockProjectService.Setup(x => x.GetProjectByIdAsync(model.ProjectId))
                .ReturnsAsync(project);

            // Act
            var result = await _controller.Create(model);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().Be(model);
        }

        // Tests for missing Edit GET method
        [Fact]
        public async Task Edit_Get_WithValidTask_ReturnsViewWithModel()
        {
            // Arrange
            var taskId = 1;
            var task = new TaskEntity
            {
                Id = taskId,
                Title = "Test Task",
                Description = "Test Description",
                ProjectId = 1,
                CreatedByUserId = _testUser.Id,
                Status = TaskStatus.ToDo,
                Priority = TaskPriority.Medium
            };

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockTaskService.Setup(x => x.GetTaskByIdAsync(taskId))
                .ReturnsAsync(task);
            _mockProjectService.Setup(x => x.GetUserRoleInProjectAsync(task.ProjectId, _testUser.Id))
                .ReturnsAsync(UserRole.ProjectManager);

            // Act
            var result = await _controller.Edit(taskId);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult.Model.Should().BeOfType<TaskEditViewModel>();
            var model = viewResult.Model as TaskEditViewModel;
            model.Id.Should().Be(taskId);
            model.Title.Should().Be("Test Task");
        }

        [Fact]
        public async Task Edit_Get_WithNonExistentTask_ReturnsNotFound()
        {
            // Arrange
            var taskId = 999;
            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockTaskService.Setup(x => x.GetTaskByIdAsync(taskId))
                .ReturnsAsync((TaskEntity)null);

            // Act
            var result = await _controller.Edit(taskId);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        // Tests for missing Edit POST method
        [Fact]
        public async Task Edit_Post_WithValidModel_UpdatesTaskAndRedirects()
        {
            // Arrange
            var taskId = 1;
            var model = new TaskEditViewModelEnhanced
            {
                Id = taskId,
                Title = "Updated Task",
                Description = "Updated Description",
                Status = TaskStatus.InProgress,
                Priority = TaskPriority.High,
                ProjectId = 1
            };

            var project = new Project { Id = 1, Name = "Test Project", CreatedByUserId = _testUser.Id };
            var task = new TaskEntity
            {
                Id = taskId,
                Title = "Original Task",
                CreatedByUserId = _testUser.Id,
                ProjectId = 1
            };

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockTaskService.Setup(x => x.GetTaskByIdAsync(taskId))
                .ReturnsAsync(task);
            _mockProjectService.Setup(x => x.GetUserRoleInProjectAsync(task.ProjectId, _testUser.Id))
                .ReturnsAsync(UserRole.ProjectManager);
            _mockProjectService.Setup(x => x.GetProjectByIdAsync(task.ProjectId))
                .ReturnsAsync(project);
            _mockProjectService.Setup(x => x.GetProjectMembersAsync(task.ProjectId))
                .ReturnsAsync(new List<ProjectMember>());
            _mockTaskService.Setup(x => x.UpdateTaskAsync(It.IsAny<TaskEntity>()))
                .ReturnsAsync(task);

            // Act
            var result = await _controller.Edit(taskId, model);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("Details");
            redirectResult.RouteValues!["id"].Should().Be(taskId);
        }

        // Tests for missing Delete GET method
        [Fact]
        public async Task Delete_Get_WithValidTask_ReturnsViewWithModel()
        {
            // Arrange
            var taskId = 1;
            var task = new TaskEntity
            {
                Id = taskId,
                Title = "Test Task",
                Description = "Test Description",
                CreatedByUserId = _testUser.Id,
                ProjectId = 1,
                Project = new Project { Name = "Test Project" }
            };

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockTaskService.Setup(x => x.GetTaskWithDetailsAsync(taskId))
                .ReturnsAsync(task);
            _mockProjectService.Setup(x => x.GetUserRoleInProjectAsync(task.ProjectId, _testUser.Id))
                .ReturnsAsync(UserRole.ProjectManager);

            // Act
            var result = await _controller.Delete(taskId);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult.Model.Should().BeOfType<TaskViewModel>();
            var model = viewResult.Model as TaskViewModel;
            model.Id.Should().Be(taskId);
            model.Title.Should().Be("Test Task");
        }

        // Tests for missing Delete POST method
        [Fact]
        public async Task DeleteConfirmed_WithValidTask_DeletesTaskAndRedirects()
        {
            // Arrange
            var taskId = 1;
            var project = new Project { Id = 1, Name = "Test Project", CreatedByUserId = _testUser.Id };
            var task = new TaskEntity
            {
                Id = taskId,
                CreatedByUserId = _testUser.Id,
                ProjectId = 1
            };

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockTaskService.Setup(x => x.GetTaskByIdAsync(taskId))
                .ReturnsAsync(task);
            _mockProjectService.Setup(x => x.GetUserRoleInProjectAsync(task.ProjectId, _testUser.Id))
                .ReturnsAsync(UserRole.ProjectManager);
            _mockProjectService.Setup(x => x.GetProjectByIdAsync(task.ProjectId))
                .ReturnsAsync(project);
            _mockTaskService.Setup(x => x.DeleteTaskAsync(taskId))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.DeleteConfirmed(taskId);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("Index");
        }
    }
}
