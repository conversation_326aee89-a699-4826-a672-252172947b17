using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities
{
    public class DocumentVersion
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(255)]
        public string FileName { get; set; } = string.Empty;

        [Required]
        [MaxLength(500)]
        public string FilePath { get; set; } = string.Empty;

        [MaxLength(100)]
        public string? ContentType { get; set; }

        public long FileSize { get; set; }

        public int Version { get; set; }

        [MaxLength(500)]
        public string? ChangeDescription { get; set; }

        public int AttachmentId { get; set; }
        public string AttachmentType { get; set; } = string.Empty; // "Project" or "Task"

        public string UploadedByUserId { get; set; } = string.Empty;

        public DateTime UploadedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual ApplicationUser UploadedBy { get; set; } = null!;

        // Project or Task attachment references
        public virtual ProjectAttachment? ProjectAttachment { get; set; }
        public virtual TaskAttachment? TaskAttachment { get; set; }
    }
}
