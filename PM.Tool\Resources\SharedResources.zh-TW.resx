﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>[text/microsoft-resx - $($lang.Name)]</value>
  </resheader>
  <resheader name="version">
    <value>[2.0 - $($lang.Name)]</value>
  </resheader>
  <resheader name="reader">
    <value>[System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 - $($lang.Name)]</value>
  </resheader>
  <resheader name="writer">
    <value>[System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 - $($lang.Name)]</value>
  </resheader>
  <!-- Common UI Elements - Chinese (Traditional) -->
  <data name="Common.Save" xml:space="preserve">
    <value>[Save - $($lang.Name)]</value>
  </data>
  <data name="Common.Cancel" xml:space="preserve">
    <value>[Cancel - $($lang.Name)]</value>
  </data>
  <data name="Common.Delete" xml:space="preserve">
    <value>[Delete - $($lang.Name)]</value>
  </data>
  <data name="Common.Edit" xml:space="preserve">
    <value>[Edit - $($lang.Name)]</value>
  </data>
  <data name="Common.Create" xml:space="preserve">
    <value>[Create - $($lang.Name)]</value>
  </data>
  <data name="Common.Update" xml:space="preserve">
    <value>[Update - $($lang.Name)]</value>
  </data>
  <data name="Common.Details" xml:space="preserve">
    <value>[Details - $($lang.Name)]</value>
  </data>
  <data name="Common.Back" xml:space="preserve">
    <value>[Back - $($lang.Name)]</value>
  </data>
  <data name="Common.Next" xml:space="preserve">
    <value>[Next - $($lang.Name)]</value>
  </data>
  <data name="Common.Previous" xml:space="preserve">
    <value>[Previous - $($lang.Name)]</value>
  </data>
  <data name="Common.Search" xml:space="preserve">
    <value>[Search - $($lang.Name)]</value>
  </data>
  <data name="Common.Filter" xml:space="preserve">
    <value>[Filter - $($lang.Name)]</value>
  </data>
  <data name="Common.Export" xml:space="preserve">
    <value>[Export - $($lang.Name)]</value>
  </data>
  <data name="Common.Import" xml:space="preserve">
    <value>[Import - $($lang.Name)]</value>
  </data>
  <data name="Common.Print" xml:space="preserve">
    <value>[Print - $($lang.Name)]</value>
  </data>
  <data name="Common.Download" xml:space="preserve">
    <value>[Download - $($lang.Name)]</value>
  </data>
  <data name="Common.Upload" xml:space="preserve">
    <value>[Upload - $($lang.Name)]</value>
  </data>
  <data name="Common.Yes" xml:space="preserve">
    <value>[Yes - $($lang.Name)]</value>
  </data>
  <data name="Common.No" xml:space="preserve">
    <value>[No - $($lang.Name)]</value>
  </data>
  <data name="Common.OK" xml:space="preserve">
    <value>[OK - $($lang.Name)]</value>
  </data>
  <data name="Common.Close" xml:space="preserve">
    <value>[Close - $($lang.Name)]</value>
  </data>
  <data name="Common.Loading" xml:space="preserve">
    <value>[Loading... - $($lang.Name)]</value>
  </data>
  <data name="Common.NoData" xml:space="preserve">
    <value>[No data available - $($lang.Name)]</value>
  </data>
  <data name="Common.Error" xml:space="preserve">
    <value>[Error - $($lang.Name)]</value>
  </data>
  <data name="Common.Success" xml:space="preserve">
    <value>[Success - $($lang.Name)]</value>
  </data>
  <data name="Common.Warning" xml:space="preserve">
    <value>[Warning - $($lang.Name)]</value>
  </data>
  <data name="Common.Information" xml:space="preserve">
    <value>[Information - $($lang.Name)]</value>
  </data>
  <!-- Navigation -->
  <data name="Nav.Dashboard" xml:space="preserve">
    <value>[Dashboard - $($lang.Name)]</value>
  </data>
  <data name="Nav.Projects" xml:space="preserve">
    <value>[Projects - $($lang.Name)]</value>
  </data>
  <data name="Nav.Tasks" xml:space="preserve">
    <value>[Tasks - $($lang.Name)]</value>
  </data>
  <data name="Nav.Management" xml:space="preserve">
    <value>[Management - $($lang.Name)]</value>
  </data>
  <data name="Nav.Analytics" xml:space="preserve">
    <value>[Analytics - $($lang.Name)]</value>
  </data>
  <data name="Nav.Resources" xml:space="preserve">
    <value>[Resources - $($lang.Name)]</value>
  </data>
  <data name="Nav.Risks" xml:space="preserve">
    <value>[Risks - $($lang.Name)]</value>
  </data>
  <data name="Nav.Meetings" xml:space="preserve">
    <value>[Meetings - $($lang.Name)]</value>
  </data>
  <data name="Nav.Requirements" xml:space="preserve">
    <value>[Requirements - $($lang.Name)]</value>
  </data>
  <data name="Nav.Backlog" xml:space="preserve">
    <value>[Backlog - $($lang.Name)]</value>
  </data>
  <data name="Nav.Kanban" xml:space="preserve">
    <value>[Kanban Board - $($lang.Name)]</value>
  </data>
  <data name="Nav.Documentation" xml:space="preserve">
    <value>[Documentation - $($lang.Name)]</value>
  </data>
  <data name="Nav.SkillsManagement" xml:space="preserve">
    <value>[Skills Management - $($lang.Name)]</value>
  </data>
  <data name="Nav.ResourceUtilization" xml:space="preserve">
    <value>[Resource Utilization - $($lang.Name)]</value>
  </data>
  <data name="Nav.MeetingCalendar" xml:space="preserve">
    <value>[Meeting Calendar - $($lang.Name)]</value>
  </data>
  <data name="Nav.ActionItems" xml:space="preserve">
    <value>[Action Items - $($lang.Name)]</value>
  </data>
  <data name="Nav.AnalyticsDashboard" xml:space="preserve">
    <value>[Analytics Dashboard - $($lang.Name)]</value>
  </data>
  <data name="Nav.AdvancedReports" xml:space="preserve">
    <value>[Advanced Reports - $($lang.Name)]</value>
  </data>
  <data name="Nav.TeamAnalytics" xml:space="preserve">
    <value>[Team Analytics - $($lang.Name)]</value>
  </data>
  <data name="Nav.BurndownCharts" xml:space="preserve">
    <value>[Burndown Charts - $($lang.Name)]</value>
  </data>
  <data name="Nav.VelocityCharts" xml:space="preserve">
    <value>[Velocity Charts - $($lang.Name)]</value>
  </data>
  <data name="Nav.ExportData" xml:space="preserve">
    <value>[Export Data - $($lang.Name)]</value>
  </data>
  <!-- Project Management -->
  <data name="Project.Title" xml:space="preserve">
    <value>[Title - $($lang.Name)]</value>
  </data>
  <data name="Project.Description" xml:space="preserve">
    <value>[Description - $($lang.Name)]</value>
  </data>
  <data name="Project.StartDate" xml:space="preserve">
    <value>[Start Date - $($lang.Name)]</value>
  </data>
  <data name="Project.EndDate" xml:space="preserve">
    <value>[End Date - $($lang.Name)]</value>
  </data>
  <data name="Project.Status" xml:space="preserve">
    <value>[Status - $($lang.Name)]</value>
  </data>
  <data name="Project.Priority" xml:space="preserve">
    <value>[Priority - $($lang.Name)]</value>
  </data>
  <data name="Project.Budget" xml:space="preserve">
    <value>[Budget - $($lang.Name)]</value>
  </data>
  <data name="Project.Progress" xml:space="preserve">
    <value>[Progress - $($lang.Name)]</value>
  </data>
  <data name="Project.Manager" xml:space="preserve">
    <value>[Project Manager - $($lang.Name)]</value>
  </data>
  <data name="Project.Team" xml:space="preserve">
    <value>[Team - $($lang.Name)]</value>
  </data>
  <!-- Task Management -->
  <data name="Task.Title" xml:space="preserve">
    <value>[Task Title - $($lang.Name)]</value>
  </data>
  <data name="Task.Description" xml:space="preserve">
    <value>[Task Description - $($lang.Name)]</value>
  </data>
  <data name="Task.AssignedTo" xml:space="preserve">
    <value>[Assigned To - $($lang.Name)]</value>
  </data>
  <data name="Task.DueDate" xml:space="preserve">
    <value>[Due Date - $($lang.Name)]</value>
  </data>
  <data name="Task.EstimatedHours" xml:space="preserve">
    <value>[Estimated Hours - $($lang.Name)]</value>
  </data>
  <data name="Task.ActualHours" xml:space="preserve">
    <value>[Actual Hours - $($lang.Name)]</value>
  </data>
  <data name="Task.StoryPoints" xml:space="preserve">
    <value>[Story Points - $($lang.Name)]</value>
  </data>
  <!-- Agile Terms -->
  <data name="Agile.Epic" xml:space="preserve">
    <value>[Epic - $($lang.Name)]</value>
  </data>
  <data name="Agile.UserStory" xml:space="preserve">
    <value>[User Story - $($lang.Name)]</value>
  </data>
  <data name="Agile.Sprint" xml:space="preserve">
    <value>[Sprint - $($lang.Name)]</value>
  </data>
  <data name="Agile.Backlog" xml:space="preserve">
    <value>[Backlog - $($lang.Name)]</value>
  </data>
  <data name="Agile.Kanban" xml:space="preserve">
    <value>[Kanban - $($lang.Name)]</value>
  </data>
  <data name="Agile.Scrum" xml:space="preserve">
    <value>[Scrum - $($lang.Name)]</value>
  </data>
  <data name="Agile.Velocity" xml:space="preserve">
    <value>[Velocity - $($lang.Name)]</value>
  </data>
  <data name="Agile.Burndown" xml:space="preserve">
    <value>[Burndown - $($lang.Name)]</value>
  </data>
  <!-- Status Values -->
  <data name="Status.Active" xml:space="preserve">
    <value>[Active - $($lang.Name)]</value>
  </data>
  <data name="Status.Inactive" xml:space="preserve">
    <value>[Inactive - $($lang.Name)]</value>
  </data>
  <data name="Status.Completed" xml:space="preserve">
    <value>[Completed - $($lang.Name)]</value>
  </data>
  <data name="Status.InProgress" xml:space="preserve">
    <value>[In Progress - $($lang.Name)]</value>
  </data>
  <data name="Status.Pending" xml:space="preserve">
    <value>[Pending - $($lang.Name)]</value>
  </data>
  <data name="Status.Cancelled" xml:space="preserve">
    <value>[Cancelled - $($lang.Name)]</value>
  </data>
  <!-- Priority Values -->
  <data name="Priority.Critical" xml:space="preserve">
    <value>[Critical - $($lang.Name)]</value>
  </data>
  <data name="Priority.High" xml:space="preserve">
    <value>[High - $($lang.Name)]</value>
  </data>
  <data name="Priority.Medium" xml:space="preserve">
    <value>[Medium - $($lang.Name)]</value>
  </data>
  <data name="Priority.Low" xml:space="preserve">
    <value>[Low - $($lang.Name)]</value>
  </data>
  <!-- Messages -->
  <data name="Message.SaveSuccess" xml:space="preserve">
    <value>[Item saved successfully - $($lang.Name)]</value>
  </data>
  <data name="Message.DeleteSuccess" xml:space="preserve">
    <value>[Item deleted successfully - $($lang.Name)]</value>
  </data>
  <data name="Message.UpdateSuccess" xml:space="preserve">
    <value>[Item updated successfully - $($lang.Name)]</value>
  </data>
  <data name="Message.ErrorOccurred" xml:space="preserve">
    <value>[An error occurred. Please try again. - $($lang.Name)]</value>
  </data>
  <data name="Message.ConfirmDelete" xml:space="preserve">
    <value>[Are you sure you want to delete this item? - $($lang.Name)]</value>
  </data>
  <!-- Meeting Management -->
  <data name="Meeting.Title" xml:space="preserve">
    <value>[Meeting Title - $($lang.Name)]</value>
  </data>
  <data name="Meeting.Description" xml:space="preserve">
    <value>[Description - $($lang.Name)]</value>
  </data>
  <data name="Meeting.StartTime" xml:space="preserve">
    <value>[Start Time - $($lang.Name)]</value>
  </data>
  <data name="Meeting.EndTime" xml:space="preserve">
    <value>[End Time - $($lang.Name)]</value>
  </data>
  <data name="Meeting.Location" xml:space="preserve">
    <value>[Location - $($lang.Name)]</value>
  </data>
  <data name="Meeting.Type" xml:space="preserve">
    <value>[Meeting Type - $($lang.Name)]</value>
  </data>
  <data name="Meeting.Status" xml:space="preserve">
    <value>[Status - $($lang.Name)]</value>
  </data>
  <data name="Meeting.Organizer" xml:space="preserve">
    <value>[Organizer - $($lang.Name)]</value>
  </data>
  <data name="Meeting.Attendees" xml:space="preserve">
    <value>[Attendees - $($lang.Name)]</value>
  </data>
  <data name="Meeting.ActionItems" xml:space="preserve">
    <value>[Action Items - $($lang.Name)]</value>
  </data>
  <data name="Meeting.Documents" xml:space="preserve">
    <value>[Documents - $($lang.Name)]</value>
  </data>
  <data name="Meeting.Minutes" xml:space="preserve">
    <value>[Meeting Minutes - $($lang.Name)]</value>
  </data>
  <data name="Meeting.Agenda" xml:space="preserve">
    <value>[Agenda - $($lang.Name)]</value>
  </data>
  <!-- Requirements Management -->
  <data name="Requirement.Title" xml:space="preserve">
    <value>[Requirement Title - $($lang.Name)]</value>
  </data>
  <data name="Requirement.Description" xml:space="preserve">
    <value>[Description - $($lang.Name)]</value>
  </data>
  <data name="Requirement.Type" xml:space="preserve">
    <value>[Type - $($lang.Name)]</value>
  </data>
  <data name="Requirement.Priority" xml:space="preserve">
    <value>[Priority - $($lang.Name)]</value>
  </data>
  <data name="Requirement.Status" xml:space="preserve">
    <value>[Status - $($lang.Name)]</value>
  </data>
  <data name="Requirement.Source" xml:space="preserve">
    <value>[Source - $($lang.Name)]</value>
  </data>
  <data name="Requirement.Stakeholder" xml:space="preserve">
    <value>[Stakeholder - $($lang.Name)]</value>
  </data>
  <data name="Requirement.AcceptanceCriteria" xml:space="preserve">
    <value>[Acceptance Criteria - $($lang.Name)]</value>
  </data>
  <data name="Requirement.BusinessValue" xml:space="preserve">
    <value>[Business Value - $($lang.Name)]</value>
  </data>
  <data name="Requirement.Comments" xml:space="preserve">
    <value>[Comments - $($lang.Name)]</value>
  </data>
  <data name="Requirement.Attachments" xml:space="preserve">
    <value>[Attachments - $($lang.Name)]</value>
  </data>
  <data name="Requirement.Changes" xml:space="preserve">
    <value>[Change History - $($lang.Name)]</value>
  </data>
  <data name="Requirement.Tasks" xml:space="preserve">
    <value>[Related Tasks - $($lang.Name)]</value>
  </data>
  <!-- Risk Management -->
  <data name="Risk.Title" xml:space="preserve">
    <value>[Risk Title - $($lang.Name)]</value>
  </data>
  <data name="Risk.Description" xml:space="preserve">
    <value>[Description - $($lang.Name)]</value>
  </data>
  <data name="Risk.Category" xml:space="preserve">
    <value>[Category - $($lang.Name)]</value>
  </data>
  <data name="Risk.Probability" xml:space="preserve">
    <value>[Probability - $($lang.Name)]</value>
  </data>
  <data name="Risk.Impact" xml:space="preserve">
    <value>[Impact - $($lang.Name)]</value>
  </data>
  <data name="Risk.Score" xml:space="preserve">
    <value>[Risk Score - $($lang.Name)]</value>
  </data>
  <data name="Risk.Status" xml:space="preserve">
    <value>[Status - $($lang.Name)]</value>
  </data>
  <data name="Risk.Owner" xml:space="preserve">
    <value>[Risk Owner - $($lang.Name)]</value>
  </data>
  <data name="Risk.MitigationPlan" xml:space="preserve">
    <value>[Mitigation Plan - $($lang.Name)]</value>
  </data>
  <data name="Risk.MitigationActions" xml:space="preserve">
    <value>[Mitigation Actions - $($lang.Name)]</value>
  </data>
  <data name="Risk.ContingencyPlan" xml:space="preserve">
    <value>[Contingency Plan - $($lang.Name)]</value>
  </data>
  <!-- Resource Management -->
  <data name="Resource.Name" xml:space="preserve">
    <value>[Resource Name - $($lang.Name)]</value>
  </data>
  <data name="Resource.Type" xml:space="preserve">
    <value>[Type - $($lang.Name)]</value>
  </data>
  <data name="Resource.Department" xml:space="preserve">
    <value>[Department - $($lang.Name)]</value>
  </data>
  <data name="Resource.Location" xml:space="preserve">
    <value>[Location - $($lang.Name)]</value>
  </data>
  <data name="Resource.HourlyRate" xml:space="preserve">
    <value>[Hourly Rate - $($lang.Name)]</value>
  </data>
  <data name="Resource.Capacity" xml:space="preserve">
    <value>[Capacity - $($lang.Name)]</value>
  </data>
  <data name="Resource.Skills" xml:space="preserve">
    <value>[Skills - $($lang.Name)]</value>
  </data>
  <data name="Resource.Availability" xml:space="preserve">
    <value>[Availability - $($lang.Name)]</value>
  </data>
  <data name="Resource.Utilization" xml:space="preserve">
    <value>[Utilization - $($lang.Name)]</value>
  </data>
  <data name="Resource.Allocation" xml:space="preserve">
    <value>[Allocation - $($lang.Name)]</value>
  </data>
  <!-- Agile/Scrum Terms -->
  <data name="Agile.SprintPlanning" xml:space="preserve">
    <value>[Sprint Planning - $($lang.Name)]</value>
  </data>
  <data name="Agile.SprintReview" xml:space="preserve">
    <value>[Sprint Review - $($lang.Name)]</value>
  </data>
  <data name="Agile.SprintRetrospective" xml:space="preserve">
    <value>[Sprint Retrospective - $($lang.Name)]</value>
  </data>
  <data name="Agile.DailyStandup" xml:space="preserve">
    <value>[Daily Standup - $($lang.Name)]</value>
  </data>
  <data name="Agile.ProductBacklog" xml:space="preserve">
    <value>[Product Backlog - $($lang.Name)]</value>
  </data>
  <data name="Agile.SprintBacklog" xml:space="preserve">
    <value>[Sprint Backlog - $($lang.Name)]</value>
  </data>
  <data name="Agile.Definition" xml:space="preserve">
    <value>[Definition of Done - $($lang.Name)]</value>
  </data>
  <data name="Agile.StoryPoints" xml:space="preserve">
    <value>[Story Points - $($lang.Name)]</value>
  </data>
  <data name="Agile.SprintStarted" xml:space="preserve">
    <value>[Sprint started successfully - $($lang.Name)]</value>
  </data>
  <data name="Agile.SprintCompleted" xml:space="preserve">
    <value>[Sprint completed successfully - $($lang.Name)]</value>
  </data>
  <!-- Enum Values - Meeting Types -->
  <data name="Enum.MeetingType.General" xml:space="preserve">
    <value>[General - $($lang.Name)]</value>
  </data>
  <data name="Enum.MeetingType.Standup" xml:space="preserve">
    <value>[Daily Standup - $($lang.Name)]</value>
  </data>
  <data name="Enum.MeetingType.Planning" xml:space="preserve">
    <value>[Planning - $($lang.Name)]</value>
  </data>
  <data name="Enum.MeetingType.Review" xml:space="preserve">
    <value>[Review - $($lang.Name)]</value>
  </data>
  <data name="Enum.MeetingType.Retrospective" xml:space="preserve">
    <value>[Retrospective - $($lang.Name)]</value>
  </data>
  <data name="Enum.MeetingType.Stakeholder" xml:space="preserve">
    <value>[Stakeholder - $($lang.Name)]</value>
  </data>
  <data name="Enum.MeetingType.Requirements" xml:space="preserve">
    <value>[Requirements - $($lang.Name)]</value>
  </data>
  <data name="Enum.MeetingType.Technical" xml:space="preserve">
    <value>[Technical - $($lang.Name)]</value>
  </data>
  <data name="Enum.MeetingType.Status" xml:space="preserve">
    <value>[Status - $($lang.Name)]</value>
  </data>
  <!-- Enum Values - Meeting Status -->
  <data name="Enum.MeetingStatus.Scheduled" xml:space="preserve">
    <value>[Scheduled - $($lang.Name)]</value>
  </data>
  <data name="Enum.MeetingStatus.InProgress" xml:space="preserve">
    <value>[In Progress - $($lang.Name)]</value>
  </data>
  <data name="Enum.MeetingStatus.Completed" xml:space="preserve">
    <value>[Completed - $($lang.Name)]</value>
  </data>
  <data name="Enum.MeetingStatus.Cancelled" xml:space="preserve">
    <value>[Cancelled - $($lang.Name)]</value>
  </data>
  <data name="Enum.MeetingStatus.Postponed" xml:space="preserve">
    <value>[Postponed - $($lang.Name)]</value>
  </data>
  <!-- Enum Values - Requirement Types -->
  <data name="Enum.RequirementType.Functional" xml:space="preserve">
    <value>[Functional - $($lang.Name)]</value>
  </data>
  <data name="Enum.RequirementType.NonFunctional" xml:space="preserve">
    <value>[Non-Functional - $($lang.Name)]</value>
  </data>
  <data name="Enum.RequirementType.Business" xml:space="preserve">
    <value>[Business - $($lang.Name)]</value>
  </data>
  <data name="Enum.RequirementType.Technical" xml:space="preserve">
    <value>[Technical - $($lang.Name)]</value>
  </data>
  <data name="Enum.RequirementType.Performance" xml:space="preserve">
    <value>[Performance - $($lang.Name)]</value>
  </data>
  <data name="Enum.RequirementType.Security" xml:space="preserve">
    <value>[Security - $($lang.Name)]</value>
  </data>
  <data name="Enum.RequirementType.Usability" xml:space="preserve">
    <value>[Usability - $($lang.Name)]</value>
  </data>
  <data name="Enum.RequirementType.Compliance" xml:space="preserve">
    <value>[Compliance - $($lang.Name)]</value>
  </data>
  <data name="Enum.RequirementType.Integration" xml:space="preserve">
    <value>[Integration - $($lang.Name)]</value>
  </data>
  <!-- Enum Values - Risk Categories -->
  <data name="Enum.RiskCategory.Technical" xml:space="preserve">
    <value>[Technical - $($lang.Name)]</value>
  </data>
  <data name="Enum.RiskCategory.Schedule" xml:space="preserve">
    <value>[Schedule - $($lang.Name)]</value>
  </data>
  <data name="Enum.RiskCategory.Budget" xml:space="preserve">
    <value>[Budget - $($lang.Name)]</value>
  </data>
  <data name="Enum.RiskCategory.Resource" xml:space="preserve">
    <value>[Resource - $($lang.Name)]</value>
  </data>
  <data name="Enum.RiskCategory.Quality" xml:space="preserve">
    <value>[Quality - $($lang.Name)]</value>
  </data>
  <data name="Enum.RiskCategory.External" xml:space="preserve">
    <value>[External - $($lang.Name)]</value>
  </data>
  <data name="Enum.RiskCategory.Organizational" xml:space="preserve">
    <value>[Organizational - $($lang.Name)]</value>
  </data>
  <!-- Enum Values - Resource Types -->
  <data name="Enum.ResourceType.Human" xml:space="preserve">
    <value>[Human Resource - $($lang.Name)]</value>
  </data>
  <data name="Enum.ResourceType.Equipment" xml:space="preserve">
    <value>[Equipment - $($lang.Name)]</value>
  </data>
  <data name="Enum.ResourceType.Material" xml:space="preserve">
    <value>[Material - $($lang.Name)]</value>
  </data>
  <data name="Enum.ResourceType.Software" xml:space="preserve">
    <value>[Software - $($lang.Name)]</value>
  </data>
  <data name="Enum.ResourceType.Facility" xml:space="preserve">
    <value>[Facility - $($lang.Name)]</value>
  </data>
  <data name="WBS.ErrorLoadingStructure" xml:space="preserve">
    <value>[Error loading WBS structure - zh-TW]</value>
  </data>
  <data name="WBS.TaskCreatedSuccessfully" xml:space="preserve">
    <value>[Task created successfully - zh-TW]</value>
  </data>
  <data name="WBS.TaskDeletedSuccessfully" xml:space="preserve">
    <value>[Task deleted successfully - zh-TW]</value>
  </data>
  <data name="WBS.TaskDuplicatedSuccessfully" xml:space="preserve">
    <value>[Task duplicated successfully - zh-TW]</value>
  </data>
  <data name="WBS.TaskMovedSuccessfully" xml:space="preserve">
    <value>[Task moved {0} successfully - zh-TW]</value>
  </data>
  <data name="WBS.TaskStatusUpdated" xml:space="preserve">
    <value>[Task status updated to {0} - zh-TW]</value>
  </data>
  <data name="WBS.WbsCodesGenerated" xml:space="preserve">
    <value>[WBS codes generated successfully - zh-TW]</value>
  </data>
  <data name="WBS.ExportingTo" xml:space="preserve">
    <value>[Exporting WBS to {0}... - zh-TW]</value>
  </data>
  <data name="WBS.ExportingTask" xml:space="preserve">
    <value>[Exporting task... - zh-TW]</value>
  </data>
  <data name="WBS.PrintingWbs" xml:space="preserve">
    <value>[Opening print dialog... - zh-TW]</value>
  </data>
  <data name="WBS.CompactViewEnabled" xml:space="preserve">
    <value>[Compact view enabled - zh-TW]</value>
  </data>
  <data name="WBS.NormalViewEnabled" xml:space="preserve">
    <value>[Normal view enabled - zh-TW]</value>
  </data>
  <data name="WBS.DetailedViewEnabled" xml:space="preserve">
    <value>[Detailed view enabled - zh-TW]</value>
  </data>
  <data name="WBS.RefreshingView" xml:space="preserve">
    <value>[Refreshing WBS view... - zh-TW]</value>
  </data>
  <data name="WBS.FilteredToShow" xml:space="preserve">
    <value>[Filtered to show {0} tasks - zh-TW]</value>
  </data>
  <data name="WBS.ShowingAllTasks" xml:space="preserve">
    <value>[Showing all tasks - zh-TW]</value>
  </data>
  <data name="WBS.FilteredOverdue" xml:space="preserve">
    <value>[Filtered to show overdue tasks - zh-TW]</value>
  </data>
  <data name="WBS.TaskTitleRequired" xml:space="preserve">
    <value>[Task title is required - zh-TW]</value>
  </data>
  <data name="WBS.ProjectIdMissing" xml:space="preserve">
    <value>[Project ID is missing - zh-TW]</value>
  </data>
  <data name="WBS.ValidationError" xml:space="preserve">
    <value>[Validation error. Please check your input. - zh-TW]</value>
  </data>
  <data name="WBS.AccessDenied" xml:space="preserve">
    <value>[Access denied. Please refresh the page and try again. - zh-TW]</value>
  </data>
  <data name="WBS.RequestFormatError" xml:space="preserve">
    <value>[Request format error. Please try again. - zh-TW]</value>
  </data>
  <data name="WBS.ErrorCreatingTask" xml:space="preserve">
    <value>[Error creating task - zh-TW]</value>
  </data>
  <data name="WBS.ErrorLoadingTaskDetails" xml:space="preserve">
    <value>[Error loading task details - zh-TW]</value>
  </data>
  <data name="WBS.ErrorDeletingTask" xml:space="preserve">
    <value>[Error deleting task - zh-TW]</value>
  </data>
  <data name="WBS.ErrorDuplicatingTask" xml:space="preserve">
    <value>[Error duplicating task - zh-TW]</value>
  </data>
  <data name="WBS.ErrorMovingTask" xml:space="preserve">
    <value>[Error moving task {0} - zh-TW]</value>
  </data>
  <data name="WBS.ErrorUpdatingTaskStatus" xml:space="preserve">
    <value>[Error updating task status - zh-TW]</value>
  </data>
  <data name="WBS.ErrorGeneratingCodes" xml:space="preserve">
    <value>[Error generating WBS codes - zh-TW]</value>
  </data>
  <data name="WBS.ConfirmDeleteTask" xml:space="preserve">
    <value>[Are you sure you want to delete this task? This action cannot be undone. - zh-TW]</value>
  </data>
  <data name="WBS.ConfirmDuplicateTask" xml:space="preserve">
    <value>[Create a duplicate of this task? - zh-TW]</value>
  </data>
  <data name="WBS.ConfirmGenerateCodes" xml:space="preserve">
    <value>[This will regenerate all WBS codes. Continue? - zh-TW]</value>
  </data>
  <data name="WBS.CreateNewTask" xml:space="preserve">
    <value>[Create New Task - zh-TW]</value>
  </data>
  <data name="WBS.CreateChildTaskFor" xml:space="preserve">
    <value>[Create Child Task for: {0} - zh-TW]</value>
  </data>
  <data name="WBS.TaskNotFound" xml:space="preserve">
    <value>[Task not found - zh-TW]</value>
  </data>
  <data name="WBS.UnsupportedExportFormat" xml:space="preserve">
    <value>[Unsupported export format - zh-TW]</value>
  </data>
  <data name="WBS.GanttViewComingSoon" xml:space="preserve">
    <value>[Gantt view feature coming soon! - zh-TW]</value>
  </data>
  <data name="WBS.Action.ViewDetails" xml:space="preserve">
    <value>[View Details - zh-TW]</value>
  </data>
  <data name="WBS.Action.EditTask" xml:space="preserve">
    <value>[Edit Task - zh-TW]</value>
  </data>
  <data name="WBS.Action.Duplicate" xml:space="preserve">
    <value>[Duplicate - zh-TW]</value>
  </data>
  <data name="WBS.Action.AddChild" xml:space="preserve">
    <value>[Add Child - zh-TW]</value>
  </data>
  <data name="WBS.Action.MoreActions" xml:space="preserve">
    <value>[More Actions - zh-TW]</value>
  </data>
  <data name="WBS.Action.MoveUp" xml:space="preserve">
    <value>[Move Up - zh-TW]</value>
  </data>
  <data name="WBS.Action.MoveDown" xml:space="preserve">
    <value>[Move Down - zh-TW]</value>
  </data>
  <data name="WBS.Action.StartTask" xml:space="preserve">
    <value>[Start Task - zh-TW]</value>
  </data>
  <data name="WBS.Action.MarkInReview" xml:space="preserve">
    <value>[Mark In Review - zh-TW]</value>
  </data>
  <data name="WBS.Action.MarkComplete" xml:space="preserve">
    <value>[Mark Complete - zh-TW]</value>
  </data>
  <data name="WBS.Action.CancelTask" xml:space="preserve">
    <value>[Cancel Task - zh-TW]</value>
  </data>
  <data name="WBS.Action.ExportTask" xml:space="preserve">
    <value>[Export Task - zh-TW]</value>
  </data>
  <data name="WBS.Action.DeleteTask" xml:space="preserve">
    <value>[Delete Task - zh-TW]</value>
  </data>
  <data name="WBS.Label.Progress" xml:space="preserve">
    <value>[Progress - zh-TW]</value>
  </data>
  <data name="WBS.Label.Unassigned" xml:space="preserve">
    <value>[Unassigned - zh-TW]</value>
  </data>
  <data name="WBS.Label.Overdue" xml:space="preserve">
    <value>[Overdue - zh-TW]</value>
  </data>
  <data name="WBS.Label.Start" xml:space="preserve">
    <value>[Start - zh-TW]</value>
  </data>
  <data name="WBS.Label.Due" xml:space="preserve">
    <value>[Due - zh-TW]</value>
  </data>
  <data name="WBS.Label.GeneratedOn" xml:space="preserve">
    <value>[Generated on - zh-TW]</value>
  </data>
  <data name="WBS.Label.WorkBreakdownStructure" xml:space="preserve">
    <value>[Work Breakdown Structure - zh-TW]</value>
  </data>
</root>