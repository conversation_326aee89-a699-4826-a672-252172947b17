@model PM.Tool.Core.Entities.Agile.Feature
@using PM.Tool.Core.Entities.Agile

@{
    ViewData["Title"] = "Delete Feature";
    ViewData["PageTitle"] = $"Delete Feature - {Model.Title}";
    ViewData["PageDescription"] = "Confirm deletion of this feature and all related data.";
}

@section Styles {
    <style>
        .danger-zone {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border: 2px solid #fecaca;
        }
        .dark .danger-zone {
            background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%);
            border: 2px solid #dc2626;
        }
        .warning-icon {
            animation: pulse 2s infinite;
        }
    </style>
    <style>
        @@keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <div class="flex items-center space-x-3 mb-2">
                <span class="text-sm font-mono text-neutral-500 dark:text-dark-400 bg-neutral-100 dark:bg-dark-300 px-3 py-1 rounded">
                    @Model.FeatureKey
                </span>
                <span class="px-3 py-1 text-sm font-medium rounded-full @GetStatusBadgeClass(Model.Status)">
                    @Model.Status
                </span>
                <span class="px-3 py-1 text-sm font-medium rounded-full @GetPriorityBadgeClass(Model.Priority)">
                    @Model.Priority
                </span>
            </div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-trash-alt mr-3 text-red-600 dark:text-red-400"></i>
                Delete Feature
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Permanently remove this feature from the project
            </p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
            @{
                ViewData["Text"] = "View Details";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-eye";
                ViewData["Href"] = Url.Action("Details", new { id = Model.Id });
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "Back to List";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Index", new { projectId = Model.ProjectId });
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Warning Banner -->
<div class="danger-zone rounded-lg p-6 mb-8">
    <div class="flex items-start space-x-4">
        <div class="flex-shrink-0">
            <i class="fas fa-exclamation-triangle text-3xl text-red-600 dark:text-red-400 warning-icon"></i>
        </div>
        <div class="flex-1">
            <h3 class="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
                Warning: This action cannot be undone
            </h3>
            <p class="text-red-700 dark:text-red-300 mb-4">
                Deleting this feature will permanently remove all associated data including:
            </p>
            <ul class="text-red-700 dark:text-red-300 list-disc list-inside space-y-1 mb-4">
                <li>All user stories linked to this feature</li>
                <li>All bugs reported against this feature</li>
                <li>All test cases associated with this feature</li>
                <li>Feature progress and metrics history</li>
                <li>All comments and attachments</li>
            </ul>
            <p class="text-red-700 dark:text-red-300 font-medium">
                Consider archiving or marking the feature as cancelled instead of deleting it.
            </p>
        </div>
    </div>
</div>

<!-- Feature Details -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Feature Information -->
    @{
        ViewData["Title"] = "Feature Details";
        ViewData["Icon"] = "fas fa-puzzle-piece";
    }
    <partial name="Components/_Card" view-data="ViewData">
        <div class="space-y-4">
            <div>
                <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-100 mb-1">Title</h4>
                <p class="text-neutral-600 dark:text-dark-300">@Model.Title</p>
            </div>

            <div>
                <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-100 mb-1">Description</h4>
                <p class="text-neutral-600 dark:text-dark-300">@Model.Description</p>
            </div>

            @if (!string.IsNullOrEmpty(Model.BusinessValue))
            {
                <div>
                    <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-100 mb-1">Business Value</h4>
                    <p class="text-neutral-600 dark:text-dark-300">@Model.BusinessValue</p>
                </div>
            }

            <div class="grid grid-cols-2 gap-4">
                <div>
                    <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-100 mb-1">Epic</h4>
                    <p class="text-neutral-600 dark:text-dark-300">@(Model.Epic?.Title ?? "No Epic")</p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-100 mb-1">Owner</h4>
                    <p class="text-neutral-600 dark:text-dark-300">@(Model.Owner?.UserName ?? "Unassigned")</p>
                </div>
            </div>

            @if (Model.TargetDate.HasValue)
            {
                <div>
                    <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-100 mb-1">Target Date</h4>
                    <p class="text-neutral-600 dark:text-dark-300 @(Model.TargetDate < DateTime.Now ? "text-red-600 dark:text-red-400" : "")">
                        @Model.TargetDate.Value.ToString("MMMM dd, yyyy")
                    </p>
                </div>
            }

            @if (!string.IsNullOrEmpty(Model.Tags))
            {
                <div>
                    <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-100 mb-2">Tags</h4>
                    <div class="flex flex-wrap gap-2">
                        @foreach (var tag in Model.Tags.Split(',', StringSplitOptions.RemoveEmptyEntries))
                        {
                            <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full">
                                @tag.Trim()
                            </span>
                        }
                    </div>
                </div>
            }
        </div>
    </partial>

    <!-- Impact Summary -->
    @{
        ViewData["Title"] = "Deletion Impact";
        ViewData["Icon"] = "fas fa-chart-bar";
    }
    <partial name="Components/_Card" view-data="ViewData">
        <div class="space-y-4">
            <div class="flex justify-between items-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-book text-red-600 dark:text-red-400"></i>
                    <span class="text-sm font-medium text-red-800 dark:text-red-200">User Stories</span>
                </div>
                <span class="text-lg font-bold text-red-600 dark:text-red-400">@Model.UserStoryCount</span>
            </div>

            <div class="flex justify-between items-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-calculator text-red-600 dark:text-red-400"></i>
                    <span class="text-sm font-medium text-red-800 dark:text-red-200">Story Points</span>
                </div>
                <span class="text-lg font-bold text-red-600 dark:text-red-400">@Model.EstimatedStoryPoints</span>
            </div>

            <div class="flex justify-between items-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-chart-pie text-red-600 dark:text-red-400"></i>
                    <span class="text-sm font-medium text-red-800 dark:text-red-200">Progress</span>
                </div>
                <span class="text-lg font-bold text-red-600 dark:text-red-400">@Model.ProgressPercentage.ToString("F1")%</span>
            </div>

            <div class="border-t border-red-200 dark:border-red-800 pt-4 mt-4">
                <div class="text-center">
                    <p class="text-sm text-red-700 dark:text-red-300 font-medium">
                        All this progress and data will be permanently lost
                    </p>
                </div>
            </div>
        </div>
    </partial>
</div>

<!-- Alternative Actions -->
@{
    ViewData["Title"] = "Alternative Actions";
    ViewData["Icon"] = "fas fa-lightbulb";
}
<partial name="Components/_Card" view-data="ViewData">
    <div class="space-y-4">
        <p class="text-neutral-600 dark:text-dark-300 mb-4">
            Instead of deleting this feature, consider these alternatives:
        </p>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="p-4 border border-neutral-200 dark:border-dark-200 rounded-lg">
                <div class="flex items-center space-x-3 mb-2">
                    <i class="fas fa-pause-circle text-orange-600 dark:text-orange-400"></i>
                    <h4 class="font-medium text-neutral-900 dark:text-dark-100">Put On Hold</h4>
                </div>
                <p class="text-sm text-neutral-600 dark:text-dark-300 mb-3">
                    Temporarily pause development while preserving all data.
                </p>
                @{
                    ViewData["Text"] = "Put On Hold";
                    ViewData["Variant"] = "warning";
                    ViewData["Size"] = "sm";
                    ViewData["Icon"] = "fas fa-pause";
                    ViewData["Href"] = Url.Action("Edit", new { id = Model.Id }) + "?status=" + (int)FeatureStatus.OnHold;
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>

            <div class="p-4 border border-neutral-200 dark:border-dark-200 rounded-lg">
                <div class="flex items-center space-x-3 mb-2">
                    <i class="fas fa-ban text-red-600 dark:text-red-400"></i>
                    <h4 class="font-medium text-neutral-900 dark:text-dark-100">Cancel Feature</h4>
                </div>
                <p class="text-sm text-neutral-600 dark:text-dark-300 mb-3">
                    Mark as cancelled while keeping historical data for reference.
                </p>
                @{
                    ViewData["Text"] = "Cancel Feature";
                    ViewData["Variant"] = "danger";
                    ViewData["Size"] = "sm";
                    ViewData["Icon"] = "fas fa-ban";
                    ViewData["Href"] = Url.Action("Edit", new { id = Model.Id }) + "?status=" + (int)FeatureStatus.Cancelled;
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>

            <div class="p-4 border border-neutral-200 dark:border-dark-200 rounded-lg">
                <div class="flex items-center space-x-3 mb-2">
                    <i class="fas fa-edit text-blue-600 dark:text-blue-400"></i>
                    <h4 class="font-medium text-neutral-900 dark:text-dark-100">Edit Feature</h4>
                </div>
                <p class="text-sm text-neutral-600 dark:text-dark-300 mb-3">
                    Modify the feature details, scope, or timeline instead.
                </p>
                @{
                    ViewData["Text"] = "Edit Feature";
                    ViewData["Variant"] = "primary";
                    ViewData["Size"] = "sm";
                    ViewData["Icon"] = "fas fa-edit";
                    ViewData["Href"] = Url.Action("Edit", new { id = Model.Id });
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
        </div>
    </div>
</partial>

<!-- Confirmation Form -->
@{
    ViewData["Title"] = "Confirm Deletion";
    ViewData["Icon"] = "fas fa-exclamation-triangle";
    ViewData["CardClass"] = "border-red-200 dark:border-red-800";
}
<partial name="Components/_Card" view-data="ViewData">
    <form asp-action="Delete" method="post" id="deleteForm" class="space-y-6">
        <input type="hidden" asp-for="Id" />

        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div class="flex items-start space-x-3">
                <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400 mt-1"></i>
                <div>
                    <h4 class="text-sm font-medium text-red-800 dark:text-red-200 mb-2">
                        Type the feature title to confirm deletion
                    </h4>
                    <p class="text-sm text-red-700 dark:text-red-300 mb-3">
                        Please type <strong>"@Model.Title"</strong> to confirm you want to permanently delete this feature.
                    </p>

                    @{
                        ViewData["Label"] = "Confirmation";
                        ViewData["Name"] = "confirmationText";
                        ViewData["Type"] = "text";
                        ViewData["Required"] = true;
                        ViewData["Icon"] = "fas fa-keyboard";
                        ViewData["Placeholder"] = "Type the feature title here...";
                        ViewData["ContainerClasses"] = "mb-0";
                    }
                    <partial name="Components/_FormInput" view-data="ViewData" />
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0 pt-6 border-t border-neutral-200 dark:border-dark-200">
            <div>
                @{
                    ViewData["Text"] = "Cancel";
                    ViewData["Variant"] = "secondary";
                    ViewData["Icon"] = "fas fa-times";
                    ViewData["Href"] = Url.Action("Details", new { id = Model.Id });
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
            <div>
                @{
                    ViewData["Text"] = "Delete Feature";
                    ViewData["Variant"] = "danger";
                    ViewData["Icon"] = "fas fa-trash-alt";
                    ViewData["Type"] = "submit";
                    ViewData["Href"] = null;
                    ViewData["Id"] = "deleteButton";
                    ViewData["Disabled"] = true;
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
        </div>
    </form>
</partial>

@functions {
    private string GetStatusBadgeClass(FeatureStatus status)
    {
        return status switch
        {
            FeatureStatus.Draft => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100",
            FeatureStatus.InProgress => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
            FeatureStatus.Testing => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            FeatureStatus.Completed => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            FeatureStatus.OnHold => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
            FeatureStatus.Cancelled => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100"
        };
    }

    private string GetPriorityBadgeClass(FeaturePriority priority)
    {
        return priority switch
        {
            FeaturePriority.Low => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            FeaturePriority.Medium => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            FeaturePriority.High => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
            FeaturePriority.Critical => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100"
        };
    }
}

@section Scripts {
    <script>
        $(document).ready(function() {
            setupConfirmationValidation();
            setupFormSubmission();
        });

        function setupConfirmationValidation() {
            const requiredText = '@Model.Title';
            const $confirmationInput = $('input[name="confirmationText"]');
            const $deleteButton = $('#deleteButton');

            $confirmationInput.on('input', function() {
                const inputText = $(this).val().trim();
                const isMatch = inputText === requiredText;

                $deleteButton.prop('disabled', !isMatch);

                if (isMatch) {
                    $deleteButton.removeClass('opacity-50 cursor-not-allowed');
                    $(this).removeClass('border-red-300 dark:border-red-600')
                           .addClass('border-green-300 dark:border-green-600');
                } else {
                    $deleteButton.addClass('opacity-50 cursor-not-allowed');
                    $(this).removeClass('border-green-300 dark:border-green-600');

                    if (inputText.length > 0) {
                        $(this).addClass('border-red-300 dark:border-red-600');
                    } else {
                        $(this).removeClass('border-red-300 dark:border-red-600');
                    }
                }
            });
        }

        function setupFormSubmission() {
            $('#deleteForm').on('submit', function(e) {
                const confirmationText = $('input[name="confirmationText"]').val().trim();
                const requiredText = '@Model.Title';

                if (confirmationText !== requiredText) {
                    e.preventDefault();
                    showError('Please type the exact feature title to confirm deletion.');
                    return false;
                }

                // Show final confirmation dialog
                if (!confirm('Are you absolutely sure you want to delete this feature? This action cannot be undone.')) {
                    e.preventDefault();
                    return false;
                }

                // Show loading state
                const $deleteButton = $('#deleteButton');
                $deleteButton.prop('disabled', true)
                            .html('<i class="fas fa-spinner fa-spin mr-2"></i>Deleting...');

                return true;
            });
        }

        function showError(message) {
            $('.error-message').remove();

            const errorHtml = `
                <div class="error-message bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3 mb-4">
                    <div class="flex">
                        <i class="fas fa-exclamation-circle text-red-600 dark:text-red-400 mt-0.5 mr-2"></i>
                        <p class="text-sm text-red-800 dark:text-red-200">${message}</p>
                    </div>
                </div>
            `;

            $('#deleteForm').prepend(errorHtml);
            $('html, body').animate({ scrollTop: $('#deleteForm').offset().top - 100 }, 300);
        }
    </script>
}
