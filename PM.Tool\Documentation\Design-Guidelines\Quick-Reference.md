# PM.Tool Design System - Quick Reference

## Essential Patterns for AI Agents

This quick reference provides the most commonly used patterns from the UI Design System for rapid implementation.

## 1. Page Header Template

```html
<div class="mb-8 page-header rounded-xl border border-neutral-200 dark:border-neutral-700">
    <div class="flex items-center justify-between px-8 py-6 border-b border-neutral-200 dark:border-neutral-700">
        <div class="flex items-center space-x-8">
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-white tracking-tight">[PAGE_TITLE]</h1>
            <div class="hidden md:flex items-center space-x-4">
                <!-- Stats badges -->
                <span class="stats-badge inline-flex items-center px-3 py-1.5 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200 rounded-full text-xs font-medium">
                    <i class="fas fa-[ICON] mr-2 text-blue-600 dark:text-blue-400"></i>
                    [NUMBER] [LABEL]
                </span>
            </div>
        </div>
        <div class="flex items-center space-x-4">
            <!-- Action buttons -->
            <a href="[URL]" class="inline-flex items-center px-5 py-2.5 text-sm font-semibold text-white bg-primary-600 hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg shadow-sm transition-all duration-200 hover:shadow-md">
                <i class="fas fa-[ICON] mr-2.5"></i>[BUTTON_TEXT]
            </a>
        </div>
    </div>
</div>
```

## 2. Filter Bar Template

```html
<div class="filter-bar bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl shadow-sm mb-8">
    <form class="px-6 py-5">
        <div class="flex flex-wrap items-center gap-6">
            <!-- Quick filters -->
            <div class="flex items-center gap-3">
                <span class="text-sm font-semibold text-neutral-700 dark:text-neutral-300 mr-3">Quick:</span>
                <div class="flex items-center gap-2">
                    <button type="button" class="quick-filter-btn px-4 py-2 text-sm font-medium rounded-lg border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 transition-all duration-200" data-filter="[VALUE]">
                        <i class="fas fa-[ICON] mr-2 text-[COLOR]-500"></i>[LABEL]
                    </button>
                </div>
            </div>
            
            <div class="h-8 w-px bg-neutral-300 dark:bg-neutral-600"></div>
            
            <!-- Main filters -->
            <div class="flex items-center gap-4 flex-1">
                <!-- Search -->
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <i class="fas fa-search text-neutral-400 dark:text-neutral-500 text-sm"></i>
                    </div>
                    <input type="text" placeholder="Search..." class="w-64 pl-11 pr-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200">
                </div>
                
                <!-- Filter controls -->
                <select class="px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[110px]">
                    <option value="">All [TYPE]</option>
                </select>
            </div>
            
            <!-- Actions -->
            <div class="flex items-center gap-3 ml-auto">
                <button type="submit" class="inline-flex items-center px-5 py-2.5 text-sm font-semibold text-white bg-primary-600 hover:bg-primary-700 rounded-lg transition-all duration-200">
                    <i class="fas fa-filter mr-2.5"></i>Apply
                </button>
                <a href="[CLEAR_URL]" class="inline-flex items-center px-4 py-2.5 text-sm font-medium text-neutral-600 dark:text-neutral-400 border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 rounded-lg transition-all duration-200">
                    <i class="fas fa-times mr-1"></i>Clear
                </a>
            </div>
        </div>
    </form>
</div>
```

## 3. View Controls Template

```html
<div class="flex items-center justify-between py-4 mb-6 border-b border-neutral-200 dark:border-neutral-700">
    <div class="flex items-center space-x-5">
        <span class="text-sm font-semibold text-neutral-700 dark:text-neutral-300">View:</span>
        <div class="flex rounded-lg border border-neutral-300 dark:border-neutral-600 bg-neutral-50 dark:bg-neutral-800 p-1">
            <button type="button" class="px-4 py-2 text-sm font-medium rounded-md view-toggle-btn active bg-white dark:bg-neutral-700 text-primary-600 dark:text-primary-400 shadow-sm" data-view="list">
                <i class="fas fa-list mr-2"></i>List
            </button>
            <button type="button" class="px-4 py-2 text-sm font-medium rounded-md view-toggle-btn text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-all duration-200" data-view="grid">
                <i class="fas fa-th mr-2"></i>Grid
            </button>
        </div>
    </div>
    <div class="flex items-center space-x-6">
        <span class="text-sm font-medium text-neutral-600 dark:text-neutral-400">
            Showing [COUNT] of [TOTAL] items
        </span>
    </div>
</div>
```

## 4. Spacing Quick Reference

```css
/* Use these spacing classes consistently */
mb-8        /* 32px - Major section margins */
mb-6        /* 24px - Standard section margins */
mb-4        /* 16px - Minor section margins */

px-8 py-6   /* 32px/24px - Header padding */
px-6 py-5   /* 24px/20px - Filter bar padding */
px-4 py-2.5 /* 16px/10px - Form control padding */
px-5 py-2.5 /* 20px/10px - Button padding */

space-x-8   /* 32px - Major element gaps */
space-x-6   /* 24px - Section gaps */
space-x-4   /* 16px - Standard gaps */
space-x-3   /* 12px - Small gaps */
gap-6       /* 24px - Flex/grid gaps */
gap-4       /* 16px - Standard flex/grid gaps */
```

## 5. Icon Spacing Standards

```css
/* Icon margins by context */
mr-2        /* 8px  - Badge icons, small buttons */
mr-2.5      /* 10px - Regular buttons */
mr-3        /* 12px - Headers, list items */
```

## 6. Color Classes Quick Reference

### Stats Badge Colors
```html
<!-- Blue: General metrics -->
bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200

<!-- Amber: Time-sensitive -->
bg-amber-100 dark:bg-amber-900/50 text-amber-800 dark:text-amber-200

<!-- Red: Critical/Overdue -->
bg-red-100 dark:bg-red-900/50 text-red-800 dark:text-red-200

<!-- Emerald: Success/Completion -->
bg-emerald-100 dark:bg-emerald-900/50 text-emerald-800 dark:text-emerald-200

<!-- Purple: Analytics -->
bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-200
```

### Button Colors
```html
<!-- Primary button -->
bg-primary-600 hover:bg-primary-700 text-white

<!-- Secondary button -->
bg-white dark:bg-neutral-800 border border-neutral-300 dark:border-neutral-600 text-neutral-700 dark:text-neutral-300
```

## 7. Typography Quick Reference

```html
<!-- Page titles -->
<h1 class="text-2xl font-bold text-neutral-900 dark:text-white tracking-tight">

<!-- Section titles -->
<h2 class="text-xl font-semibold text-neutral-900 dark:text-white">

<!-- Labels -->
<span class="text-sm font-semibold text-neutral-700 dark:text-neutral-300">

<!-- Body text -->
<p class="text-sm text-neutral-600 dark:text-neutral-400">

<!-- Small text -->
<span class="text-xs font-medium text-neutral-500 dark:text-neutral-500">
```

## 8. Common Component Classes

```css
/* Page header */
.page-header {
    background: linear-gradient(135deg, rgba(255,255,255,0.8), rgba(248,250,252,0.9));
    backdrop-filter: blur(10px);
}

/* Filter bar */
.filter-bar {
    background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(248,250,252,0.98));
    backdrop-filter: blur(8px);
}

/* Stats badge */
.stats-badge {
    transition: all 0.2s ease;
}

/* Quick filter button */
.quick-filter-btn {
    transition: all 0.2s ease;
}

/* View toggle button */
.view-toggle-btn {
    transition: all 0.2s ease;
}
```

## 9. Responsive Breakpoints

```css
/* Mobile first approach */
/* Base: Mobile (< 768px) */
md:         /* Tablet (768px+) */
lg:         /* Desktop (1024px+) */
xl:         /* Large desktop (1280px+) */
```

## 10. Implementation Checklist

When creating a new page/view:

- [ ] Use page header template with inline stats
- [ ] Implement compact filter bar if filtering needed
- [ ] Add view controls if multiple views available
- [ ] Apply consistent spacing scale throughout
- [ ] Use semantic colors for different data types
- [ ] Ensure proper icon spacing (mr-2, mr-2.5, mr-3)
- [ ] Test responsive behavior on all breakpoints
- [ ] Validate accessibility (contrast, touch targets)
- [ ] Add hover/focus states to interactive elements

---

**Quick Tip**: When in doubt, follow the MyTasks module patterns - they represent the current design system standard.
