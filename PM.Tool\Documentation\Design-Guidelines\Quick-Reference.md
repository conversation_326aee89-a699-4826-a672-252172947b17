# PM.Tool Design System - Quick Reference

## Essential Patterns for AI Agents

This quick reference provides the most commonly used patterns from the UI Design System for rapid implementation.

## 1. Page Header Template

```html
<div class="mb-8 page-header rounded-xl border border-neutral-200 dark:border-neutral-700">
    <div class="px-8 py-6 border-b border-neutral-200 dark:border-neutral-700">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-8 flex-1 min-w-0 pr-8">
                <h1 class="text-2xl font-bold text-neutral-900 dark:text-white tracking-tight">[PAGE_TITLE]</h1>
                <div class="hidden md:flex items-center space-x-4">
                    <!-- Stats badges -->
                    <span class="stats-badge inline-flex items-center px-3 py-1.5 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200 rounded-full text-xs font-medium">
                        <i class="fas fa-[ICON] mr-2 text-blue-600 dark:text-blue-400"></i>
                        [NUMBER] [LABEL]
                    </span>
                </div>
            </div>
            <div class="flex items-center space-x-4 flex-shrink-0">
                <!-- Action buttons -->
                <a href="[URL]" class="btn-primary-enhanced inline-flex items-center px-5 py-2.5 text-sm text-white rounded-lg focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                    <i class="fas fa-[ICON] mr-2.5"></i>[BUTTON_TEXT]
                </a>
            </div>
        </div>
    </div>
</div>
```

## 2. Filter Bar Template

```html
<div class="filter-bar bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl shadow-sm mb-8">
    <div class="px-8 py-6">
        <form>
            <div class="flex flex-wrap items-center gap-6">
            <!-- Quick filters -->
            <div class="flex items-center gap-3">
                <span class="text-sm font-semibold text-neutral-700 dark:text-neutral-300 mr-3">Quick:</span>
                <div class="flex items-center gap-2">
                    <button type="button" class="quick-filter-btn px-4 py-2 text-sm font-medium rounded-lg border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 transition-all duration-200" data-filter="[VALUE]">
                        <i class="fas fa-[ICON] mr-2 text-[COLOR]-500"></i>[LABEL]
                    </button>
                </div>
            </div>

            <div class="h-8 w-px bg-neutral-300 dark:bg-neutral-600"></div>

            <!-- Main filters -->
            <div class="flex items-center gap-4 flex-1">
                <!-- Search -->
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <i class="fas fa-search text-neutral-400 dark:text-neutral-500 text-sm"></i>
                    </div>
                    <input type="text" placeholder="Search..." class="w-64 pl-11 pr-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200">
                </div>

                <!-- Filter controls -->
                <select class="px-4 py-2.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[110px]">
                    <option value="">All [TYPE]</option>
                </select>
            </div>

            <!-- Actions -->
            <div class="flex items-center gap-3 ml-auto">
                <button type="submit" class="inline-flex items-center px-5 py-2.5 text-sm font-semibold text-white bg-primary-600 hover:bg-primary-700 rounded-lg transition-all duration-200">
                    <i class="fas fa-filter mr-2.5"></i>Apply
                </button>
                <a href="[CLEAR_URL]" class="inline-flex items-center px-4 py-2.5 text-sm font-medium text-neutral-600 dark:text-neutral-400 border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 rounded-lg transition-all duration-200">
                    <i class="fas fa-times mr-1"></i>Clear
                </a>
            </div>
        </div>
    </form>
</div>
```

## 3. View Controls Template

```html
<!-- Separate View Controls Section -->
<div class="bg-white dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700 mb-6">
    <div class="px-8 py-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-5">
                <span class="text-sm font-semibold text-neutral-700 dark:text-neutral-300">View:</span>
                <div class="flex rounded-lg border border-neutral-300 dark:border-neutral-600 bg-neutral-50 dark:bg-neutral-800 p-1">
                    <button type="button" class="px-4 py-2 text-sm font-medium rounded-md view-toggle-btn active bg-white dark:bg-neutral-700 text-primary-600 dark:text-primary-400 shadow-sm" data-view="grid">
                        <i class="fas fa-th mr-2"></i>Grid
                    </button>
                    <button type="button" class="px-4 py-2 text-sm font-medium rounded-md view-toggle-btn text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-all duration-200" data-view="list">
                        <i class="fas fa-list mr-2"></i>List
                    </button>
                </div>
            </div>
            <div class="flex items-center space-x-6">
                <span class="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                    Showing <span id="visibleCount">[COUNT]</span> of [TOTAL] items
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Container -->
<div class="px-8">
    <div class="space-y-6">
        <!-- Content goes here -->
    </div>
</div>
```

## 4. Container Padding Standards

```css
/* Container padding by section type */
.page-header     { padding: 32px; }           /* px-8 py-6 */
.filter-bar      { padding: 32px 32px 24px; } /* px-8 py-6 */
.view-controls   { padding: 32px 16px; }      /* px-8 py-4 */
.main-content    { padding: 0 32px; }         /* px-8 */

/* Button container spacing */
.button-group    { gap: 16px; }               /* space-x-4 */
.action-buttons  { gap: 16px; }               /* space-x-4 */

/* Layout spacing */
mb-8        /* 32px - Major section margins */
mb-6        /* 24px - Standard section margins */
mb-4        /* 16px - Minor section margins */

/* Flexbox layout classes */
.flex-1.min-w-0.pr-8    /* Content area with right padding */
.flex-shrink-0          /* Button container - prevents compression */
```

## 4.1. Spacing Quick Reference

```css
/* Use these spacing classes consistently */
px-8 py-6   /* 32px/24px - Header padding */
px-8 py-6   /* 32px/24px - Filter bar padding (updated) */
px-8 py-4   /* 32px/16px - View controls padding */
px-8        /* 32px - Main content horizontal padding */

px-5 py-2.5 /* 20px/10px - Button padding */
px-4 py-2.5 /* 16px/10px - Form control padding */
px-3 py-1.5 /* 12px/6px - Small button padding */

space-x-8   /* 32px - Major element gaps */
space-x-6   /* 24px - Section gaps */
space-x-4   /* 16px - Standard gaps */
space-x-3   /* 12px - Small gaps */
gap-6       /* 24px - Grid gaps */
gap-4       /* 16px - Standard flex/grid gaps */
```

## 5. Enhanced Icon Spacing Standards

```css
/* Icon spacing by button size */
.btn-large i    { margin-right: 12px; }    /* mr-3 - Large buttons */
.btn-medium i   { margin-right: 10px; }    /* mr-2.5 - Medium buttons (default) */
.btn-small i    { margin-right: 8px; }     /* mr-2 - Small buttons */
.btn-xs i       { margin-right: 6px; }     /* mr-1.5 - Extra small buttons */

/* Icon spacing by context */
.badge i        { margin-right: 8px; }     /* mr-2 - Badge icons */
.header i       { margin-right: 12px; }    /* mr-3 - Header icons */
.list-item i    { margin-right: 12px; }    /* mr-3 - List item icons */
.form-label i   { margin-right: 8px; }     /* mr-2 - Form label icons */
.nav-item i     { margin-right: 10px; }    /* mr-2.5 - Navigation icons */

/* Icon sizes by context */
.btn-large i    { font-size: 18px; }       /* text-lg */
.btn-medium i   { font-size: 14px; }       /* text-sm (default) */
.btn-small i    { font-size: 14px; }       /* text-sm */
.btn-xs i       { font-size: 12px; }       /* text-xs */
```

## 5.1 Enhanced Button Padding Standards

```css
/* Button padding by size - generous spacing for professional look */
.btn-large-enhanced    { padding: 0.75rem 2rem; }    /* 12px 32px */
.btn-medium-enhanced   { padding: 0.625rem 1.5rem; } /* 10px 24px */
.btn-small-enhanced    { padding: 0.5rem 1.25rem; }  /* 8px 20px */
.btn-xs-enhanced       { padding: 0.375rem 1rem; }   /* 6px 16px */
```

## 5.2 Button Implementation Examples

```html
<!-- Large Button with Enhanced Padding -->
<button class="btn-primary-enhanced btn-large-enhanced inline-flex items-center text-base font-semibold text-white rounded-lg">
    <i class="fas fa-plus mr-3 text-lg"></i>Create New Item
</button>

<!-- Medium Button with Enhanced Padding (Default) -->
<button class="btn-primary-enhanced btn-medium-enhanced inline-flex items-center text-sm font-semibold text-white rounded-lg">
    <i class="fas fa-save mr-2.5"></i>Save Changes
</button>

<!-- Small Button with Enhanced Padding -->
<button class="btn-secondary-enhanced btn-small-enhanced inline-flex items-center text-sm font-medium rounded-md">
    <i class="fas fa-edit mr-2"></i>Edit
</button>

<!-- Extra Small Button with Enhanced Padding -->
<button class="btn-tertiary-enhanced btn-xs-enhanced inline-flex items-center text-xs font-medium rounded">
    <i class="fas fa-eye mr-1.5 text-xs"></i>View
</button>
```

## 5.2 Button Anti-Patterns to Avoid

```html
<!-- ❌ WRONG: Inconsistent icon spacing -->
<button class="px-4 py-2 text-sm">
    <i class="fas fa-save mr-1"></i>Save  <!-- Too little spacing -->
</button>

<!-- ❌ WRONG: Missing inline-flex for icon alignment -->
<button class="px-4 py-2 text-sm">
    <i class="fas fa-save mr-2"></i>Save  <!-- Icons may not align properly -->
</button>

<!-- ❌ WRONG: Inconsistent padding for same button size -->
<button class="px-3 py-2 text-sm">Save</button>
<button class="px-5 py-2 text-sm">Cancel</button>  <!-- Different padding -->

<!-- ❌ WRONG: Missing hover states -->
<button class="px-4 py-2 text-sm bg-primary-600 text-white">
    Save  <!-- No hover or focus states -->
</button>

<!-- ✅ CORRECT: Proper button implementation -->
<button class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-md shadow-sm transition-all duration-200 hover:shadow-md hover:-translate-y-0.5">
    <i class="fas fa-save mr-2"></i>Save
</button>
```

## 6. Color Classes Quick Reference

### Stats Badge Colors
```html
<!-- Blue: General metrics -->
bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200

<!-- Amber: Time-sensitive -->
bg-amber-100 dark:bg-amber-900/50 text-amber-800 dark:text-amber-200

<!-- Red: Critical/Overdue -->
bg-red-100 dark:bg-red-900/50 text-red-800 dark:text-red-200

<!-- Emerald: Success/Completion -->
bg-emerald-100 dark:bg-emerald-900/50 text-emerald-800 dark:text-emerald-200

<!-- Purple: Analytics -->
bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-200
```

### Enhanced Button System

#### Professional Button Templates with Enhanced Padding
```html
<!-- Primary Button (Large) - Hero sections, main CTAs -->
<button class="btn-primary-enhanced btn-large-enhanced inline-flex items-center text-base font-semibold text-white rounded-lg focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
    <i class="fas fa-[icon] mr-3"></i>[Button Text]
</button>
<!-- CSS: padding: 0.75rem 2rem (12px 32px) -->

<!-- Primary Button (Medium - Default) - Standard actions -->
<button class="btn-primary-enhanced btn-medium-enhanced inline-flex items-center text-sm font-semibold text-white rounded-lg focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
    <i class="fas fa-[icon] mr-2.5"></i>[Button Text]
</button>
<!-- CSS: padding: 0.625rem 1.5rem (10px 24px) -->

<!-- Primary Button (Small) - Compact layouts -->
<button class="btn-primary-enhanced btn-small-enhanced inline-flex items-center text-sm font-medium text-white rounded-md focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
    <i class="fas fa-[icon] mr-2"></i>[Button Text]
</button>
<!-- CSS: padding: 0.5rem 1.25rem (8px 20px) -->

<!-- Secondary Button (Medium) - Alternative actions -->
<button class="btn-secondary-medium inline-flex items-center px-5 py-2.5 text-sm font-medium text-neutral-700 dark:text-neutral-200 bg-white dark:bg-neutral-800 rounded-lg focus:ring-2 focus:ring-primary-500 focus:ring-offset-2" style="border: 1.5px solid #e5e7eb; box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06); transition: all 0.2s ease;">
    <i class="fas fa-[icon] mr-2.5"></i>[Button Text]
</button>

<!-- Tertiary/Ghost Button - Subtle actions -->
<button class="btn-tertiary inline-flex items-center px-4 py-2 text-sm font-medium text-neutral-600 dark:text-neutral-400 bg-transparent rounded-lg focus:ring-2 focus:ring-primary-500 focus:ring-offset-2" style="border: 1px solid transparent; transition: all 0.2s ease;">
    <i class="fas fa-[icon] mr-2"></i>[Button Text]
</button>

<!-- Danger Button - Destructive actions -->
<button class="btn-danger inline-flex items-center px-5 py-2.5 text-sm font-semibold text-white rounded-lg focus:ring-2 focus:ring-red-500 focus:ring-offset-2" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); border: 1px solid #dc2626; box-shadow: 0 4px 14px 0 rgba(220, 38, 38, 0.25); transition: all 0.2s ease;">
    <i class="fas fa-[icon] mr-2.5"></i>[Button Text]
</button>

<!-- Success Button - Positive actions -->
<button class="btn-success inline-flex items-center px-5 py-2.5 text-sm font-semibold text-white rounded-lg focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); border: 1px solid #059669; box-shadow: 0 4px 14px 0 rgba(5, 150, 105, 0.25); transition: all 0.2s ease;">
    <i class="fas fa-[icon] mr-2.5"></i>[Button Text]
</button>
```

#### Enhanced Button CSS Classes
```css
/* Add these classes to your stylesheet for professional buttons */
.btn-primary-large:hover,
.btn-primary-medium:hover,
.btn-primary-small:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
    border-color: #1d4ed8 !important;
    box-shadow: 0 6px 20px 0 rgba(37, 99, 235, 0.4) !important;
    transform: translateY(-2px);
}

.btn-secondary-medium:hover {
    background: #f9fafb !important;
    border-color: #d1d5db !important;
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1) !important;
    transform: translateY(-1px);
}

.btn-tertiary:hover {
    color: #374151 !important;
    background: rgba(0, 0, 0, 0.04) !important;
    border-color: #e5e7eb !important;
    transform: translateY(-1px);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%) !important;
    border-color: #b91c1c !important;
    box-shadow: 0 6px 20px 0 rgba(220, 38, 38, 0.4) !important;
    transform: translateY(-2px);
}

.btn-success:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%) !important;
    border-color: #047857 !important;
    box-shadow: 0 6px 20px 0 rgba(5, 150, 105, 0.4) !important;
    transform: translateY(-2px);
}
```

#### Icon-Only Buttons
```html
<!-- Large Icon Button -->
<button class="inline-flex items-center justify-center w-12 h-12 text-white bg-primary-600 hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg shadow-sm transition-all duration-200 hover:shadow-md hover:-translate-y-0.5" title="[Action Description]">
    <i class="fas fa-[icon] text-lg"></i>
</button>

<!-- Medium Icon Button -->
<button class="inline-flex items-center justify-center w-10 h-10 text-white bg-primary-600 hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg shadow-sm transition-all duration-200 hover:shadow-md hover:-translate-y-0.5" title="[Action Description]">
    <i class="fas fa-[icon]"></i>
</button>

<!-- Small Icon Button -->
<button class="inline-flex items-center justify-center w-8 h-8 text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-1 rounded-md transition-all duration-200" title="[Action Description]">
    <i class="fas fa-[icon] text-sm"></i>
</button>
```

#### Button Groups
```html
<!-- Horizontal Button Group -->
<div class="inline-flex rounded-lg shadow-sm" role="group">
    <button class="px-4 py-2 text-sm font-medium text-neutral-700 bg-white border border-neutral-300 rounded-l-lg hover:bg-neutral-50 focus:ring-2 focus:ring-primary-500">
        <i class="fas fa-[icon] mr-2"></i>Option 1
    </button>
    <button class="px-4 py-2 text-sm font-medium text-neutral-700 bg-white border-t border-b border-neutral-300 hover:bg-neutral-50 focus:ring-2 focus:ring-primary-500">
        <i class="fas fa-[icon] mr-2"></i>Option 2
    </button>
    <button class="px-4 py-2 text-sm font-medium text-neutral-700 bg-white border border-neutral-300 rounded-r-lg hover:bg-neutral-50 focus:ring-2 focus:ring-primary-500">
        <i class="fas fa-[icon] mr-2"></i>Option 3
    </button>
</div>
```

## 7. Typography Quick Reference

```html
<!-- Page titles -->
<h1 class="text-2xl font-bold text-neutral-900 dark:text-white tracking-tight">

<!-- Section titles -->
<h2 class="text-xl font-semibold text-neutral-900 dark:text-white">

<!-- Labels -->
<span class="text-sm font-semibold text-neutral-700 dark:text-neutral-300">

<!-- Body text -->
<p class="text-sm text-neutral-600 dark:text-neutral-400">

<!-- Small text -->
<span class="text-xs font-medium text-neutral-500 dark:text-neutral-500">
```

## 8. Common Component Classes

```css
/* Page header */
.page-header {
    background: linear-gradient(135deg, rgba(255,255,255,0.8), rgba(248,250,252,0.9));
    backdrop-filter: blur(10px);
}

/* Filter bar */
.filter-bar {
    background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(248,250,252,0.98));
    backdrop-filter: blur(8px);
}

/* Stats badge */
.stats-badge {
    transition: all 0.2s ease;
}

/* Quick filter button */
.quick-filter-btn {
    transition: all 0.2s ease;
}

/* View toggle button */
.view-toggle-btn {
    transition: all 0.2s ease;
}
```

## 9. Responsive Breakpoints

```css
/* Mobile first approach */
/* Base: Mobile (< 768px) */
md:         /* Tablet (768px+) */
lg:         /* Desktop (1024px+) */
xl:         /* Large desktop (1280px+) */
```

## 10. Enhanced Implementation Checklist

When creating a new page/view:

### Layout & Structure
- [ ] Use page header template with inline stats
- [ ] Implement compact filter bar if filtering needed
- [ ] Add view controls if multiple views available
- [ ] Apply consistent spacing scale throughout
- [ ] Ensure main content gets 70-80% of screen space

### Button Implementation
- [ ] Use appropriate button sizes (large/medium/small/xs)
- [ ] Apply correct icon spacing by button size
- [ ] Include `inline-flex items-center` for icon alignment
- [ ] Add proper hover states (`hover:-translate-y-0.5`)
- [ ] Include focus rings for accessibility
- [ ] Use semantic button variants (primary/secondary/tertiary)
- [ ] Ensure minimum 44px touch targets on mobile

### Visual Design
- [ ] Use semantic colors for different data types
- [ ] Apply proper typography hierarchy
- [ ] Include loading and error states
- [ ] Add smooth transitions (200ms duration)
- [ ] Implement hover/focus states consistently

### Accessibility & Performance
- [ ] Test responsive behavior on all breakpoints
- [ ] Validate color contrast ratios (4.5:1 minimum)
- [ ] Ensure keyboard navigation works
- [ ] Add proper ARIA labels and titles
- [ ] Test with screen readers
- [ ] Optimize animations for performance

---

**Quick Tip**: When in doubt, follow the MyTasks module patterns - they represent the current design system standard.
