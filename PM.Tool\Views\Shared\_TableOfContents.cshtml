@* Table of Contents for Documentation *@

<nav class="nav flex-column">
    <div class="nav-section">
        <div class="nav-section-title">Getting Started</div>
        <a class="nav-link" href="@Url.Action("Index", "Documentation")">
            <i class="fas fa-home me-2"></i>
            Overview
        </a>
        <a class="nav-link" href="@Url.Action("View", "Documentation", new { path = "getting-started" })">
            <i class="fas fa-play me-2"></i>
            Quick Start
        </a>
        <a class="nav-link" href="@Url.Action("View", "Documentation", new { path = "installation" })">
            <i class="fas fa-download me-2"></i>
            Installation
        </a>
    </div>
    
    <div class="nav-section">
        <div class="nav-section-title">User Guide</div>
        <a class="nav-link" href="@Url.Action("View", "Documentation", new { path = "projects" })">
            <i class="fas fa-folder-open me-2"></i>
            Projects
        </a>
        <a class="nav-link" href="@Url.Action("View", "Documentation", new { path = "tasks" })">
            <i class="fas fa-tasks me-2"></i>
            Tasks
        </a>
        <a class="nav-link" href="@Url.Action("View", "Documentation", new { path = "agile" })">
            <i class="fas fa-columns me-2"></i>
            Agile & Kanban
        </a>
        <a class="nav-link" href="@Url.Action("View", "Documentation", new { path = "resources" })">
            <i class="fas fa-users me-2"></i>
            Resource Management
        </a>
        <a class="nav-link" href="@Url.Action("View", "Documentation", new { path = "meetings" })">
            <i class="fas fa-video me-2"></i>
            Meetings
        </a>
    </div>
    
    <div class="nav-section">
        <div class="nav-section-title">Advanced Features</div>
        <a class="nav-link" href="@Url.Action("View", "Documentation", new { path = "analytics" })">
            <i class="fas fa-chart-line me-2"></i>
            Analytics
        </a>
        <a class="nav-link" href="@Url.Action("View", "Documentation", new { path = "automation" })">
            <i class="fas fa-robot me-2"></i>
            Workflow Automation
        </a>
        <a class="nav-link" href="@Url.Action("View", "Documentation", new { path = "integrations" })">
            <i class="fas fa-plug me-2"></i>
            Integrations
        </a>
        <a class="nav-link" href="@Url.Action("View", "Documentation", new { path = "risk-management" })">
            <i class="fas fa-exclamation-triangle me-2"></i>
            Risk Management
        </a>
    </div>
    
    <div class="nav-section">
        <div class="nav-section-title">API Reference</div>
        <a class="nav-link" href="@Url.Action("View", "Documentation", new { path = "api-overview" })">
            <i class="fas fa-code me-2"></i>
            API Overview
        </a>
        <a class="nav-link" href="@Url.Action("View", "Documentation", new { path = "authentication" })">
            <i class="fas fa-key me-2"></i>
            Authentication
        </a>
        <a class="nav-link" href="@Url.Action("View", "Documentation", new { path = "endpoints" })">
            <i class="fas fa-list me-2"></i>
            Endpoints
        </a>
    </div>
    
    <div class="nav-section">
        <div class="nav-section-title">Support</div>
        <a class="nav-link" href="@Url.Action("View", "Documentation", new { path = "faq" })">
            <i class="fas fa-question-circle me-2"></i>
            FAQ
        </a>
        <a class="nav-link" href="@Url.Action("View", "Documentation", new { path = "troubleshooting" })">
            <i class="fas fa-wrench me-2"></i>
            Troubleshooting
        </a>
        <a class="nav-link" href="@Url.Action("View", "Documentation", new { path = "support" })">
            <i class="fas fa-life-ring me-2"></i>
            Contact Support
        </a>
    </div>
</nav>
