using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace PM.Tool.Core.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection Decorate<TService, TDecorator>(this IServiceCollection services)
            where TService : class
            where TDecorator : class, TService
        {
            var descriptor = services.FirstOrDefault(d => d.ServiceType == typeof(TService));
            if (descriptor == null)
                throw new InvalidOperationException($"Service type {typeof(TService).Name} not registered.");

            services.Remove(descriptor);

            if (descriptor.ImplementationInstance != null)
            {
                var factory = ActivatorUtilities.CreateFactory(
                    typeof(TDecorator),
                    new[] { typeof(TService) });

                services.Add(new ServiceDescriptor(
                    typeof(TService),
                    sp => factory(sp, new[] { descriptor.ImplementationInstance }),
                    descriptor.Lifetime));
            }
            else if (descriptor.ImplementationFactory != null)
            {
                var factory = descriptor.ImplementationFactory;
                services.Add(new ServiceDescriptor(
                    typeof(TService),
                    sp => ActivatorUtilities.CreateInstance<TDecorator>(sp, factory(sp)),
                    descriptor.Lifetime));
            }
            else
            {
                services.Add(new ServiceDescriptor(
                    typeof(TService),
                    sp => ActivatorUtilities.CreateInstance<TDecorator>(
                        sp,
                        ActivatorUtilities.CreateInstance(sp, descriptor.ImplementationType!)),
                    descriptor.Lifetime));
            }

            return services;
        }
    }
}
