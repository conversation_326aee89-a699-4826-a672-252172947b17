# 🔧 Troubleshooting Guide

## ✅ **Issue Fixed: Identity Service Registration**

### **Problem**
```
System.InvalidOperationException: No service for type 'Microsoft.AspNetCore.Identity.UserManager`1[Microsoft.AspNetCore.Identity.IdentityUser]' has been registered.
```

### **Root Cause**
The Tailwind components were trying to inject `UserManager<IdentityUser>` and `SignInManager<IdentityUser>`, but the application is configured to use `ApplicationUser` instead.

### **Solution Applied**
1. **Updated Sidebar Component** (`Views/Shared/_Sidebar.cshtml`):
   ```csharp
   @using PM.Tool.Core.Entities
   @inject SignInManager<ApplicationUser> SignInManager
   @inject UserManager<ApplicationUser> UserManager
   ```

2. **Updated Topbar Component** (`Views/Shared/_Topbar.cshtml`):
   ```csharp
   @using PM.Tool.Core.Entities
   @inject SignInManager<ApplicationUser> SignInManager
   @inject UserManager<ApplicationUser> UserManager
   ```

3. **Fixed Async Email Access**:
   ```csharp
   // Before (problematic)
   @(UserManager.GetEmailAsync(UserManager.GetUserAsync(User).Result).Result ?? "")
   
   // After (fixed)
   @(User.FindFirst(System.Security.Claims.ClaimTypes.Email)?.Value ?? "")
   ```

4. **Cleaned up Program.cs**:
   - Removed redundant Identity service registrations
   - `AddDefaultIdentity<ApplicationUser>()` already registers all necessary services

## 🚀 **Testing the Application**

### **1. Build Tailwind CSS**
```bash
cd PM.Tool
npm run build-css
```

### **2. Run the Application**
```bash
dotnet run
```

### **3. Test Features**
- ✅ **Theme Toggle**: Click the moon/sun icon in the topbar
- ✅ **Responsive Design**: Resize browser window
- ✅ **Sidebar Navigation**: Test collapsible sidebar on mobile
- ✅ **Dashboard**: Check stats cards and charts
- ✅ **Dark Mode**: Verify all components work in both themes

## 🔍 **Common Issues & Solutions**

### **Issue 1: CSS Not Loading**
**Symptoms**: Page appears unstyled or with default browser styles
**Solution**:
```bash
# Rebuild Tailwind CSS
cd PM.Tool
npx tailwindcss -i ./wwwroot/css/input.css -o ./wwwroot/css/tailwind.css
```

### **Issue 2: Theme Toggle Not Working**
**Symptoms**: Theme toggle button doesn't switch themes
**Solution**: Check browser console for JavaScript errors and ensure `tailwind-theme-manager.js` is loaded

### **Issue 3: Sidebar Not Responsive**
**Symptoms**: Sidebar doesn't collapse on mobile
**Solution**: Verify JavaScript initialization in `_Layout.cshtml` is working

### **Issue 4: Charts Not Displaying**
**Symptoms**: Chart containers are empty
**Solution**: 
1. Check Chart.js is loaded
2. Verify data is being passed from the controller
3. Check browser console for JavaScript errors

### **Issue 5: Components Not Rendering**
**Symptoms**: Partial views show errors
**Solution**: 
1. Verify ViewData parameters are correctly set
2. Check component file paths are correct
3. Ensure all required using statements are present

## 📋 **Verification Checklist**

### **Layout & Navigation**
- [ ] Sidebar displays correctly
- [ ] Topbar shows user information
- [ ] Navigation links work
- [ ] Mobile responsiveness works
- [ ] Footer displays properly

### **Theme System**
- [ ] Light theme displays correctly
- [ ] Dark theme displays correctly
- [ ] Theme toggle works
- [ ] Theme persists on page reload
- [ ] Charts adapt to theme changes

### **Components**
- [ ] Buttons render with correct styles
- [ ] Cards display properly
- [ ] Forms have proper styling
- [ ] Alerts show correctly
- [ ] Modals work (if implemented)

### **Dashboard**
- [ ] Stats cards display metrics
- [ ] Charts render correctly
- [ ] Tables are responsive
- [ ] Date picker works
- [ ] Export buttons function

## 🛠️ **Development Commands**

### **CSS Development**
```bash
# Watch mode (rebuilds on changes)
npm run build-css

# Production build (minified)
npm run build-css-prod

# Manual build
npx tailwindcss -i ./wwwroot/css/input.css -o ./wwwroot/css/tailwind.css
```

### **Debugging**
```bash
# Run with detailed logging
dotnet run --verbosity detailed

# Check for compilation errors
dotnet build

# Clear and rebuild
dotnet clean && dotnet build
```

## 📞 **Support**

If you encounter any issues not covered in this guide:

1. **Check Browser Console**: Look for JavaScript errors
2. **Verify File Paths**: Ensure all referenced files exist
3. **Check Dependencies**: Verify npm packages are installed
4. **Review Logs**: Check application logs for errors
5. **Test Incrementally**: Disable features to isolate issues

## ✅ **Migration Status**

- ✅ **Identity Services**: Fixed and working
- ✅ **Tailwind CSS**: Compiled and ready
- ✅ **Components**: Created and functional
- ✅ **Theme System**: Implemented and tested
- ✅ **Layout**: Modern and responsive
- ✅ **Dashboard**: Modernized and enhanced

**Status**: 🎉 **READY FOR TESTING**
