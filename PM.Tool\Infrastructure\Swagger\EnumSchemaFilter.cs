using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.ComponentModel;
using System.Reflection;

namespace PM.Tool.Infrastructure.Swagger
{
    /// <summary>
    /// Swagger schema filter to enhance enum documentation
    /// </summary>
    public class EnumSchemaFilter : ISchemaFilter
    {
        public void Apply(OpenApiSchema schema, SchemaFilterContext context)
        {
            if (context.Type.IsEnum)
            {
                schema.Enum.Clear();
                schema.Type = "string";
                schema.Format = null;

                var enumValues = new List<IOpenApiAny>();
                var enumDescriptions = new List<string>();

                foreach (var enumValue in Enum.GetValues(context.Type))
                {
                    var enumName = enumValue.ToString();
                    var enumMember = context.Type.GetMember(enumName!).FirstOrDefault();
                    var descriptionAttribute = enumMember?.GetCustomAttribute<DescriptionAttribute>();
                    
                    enumValues.Add(new OpenApiString(enumName));
                    
                    if (descriptionAttribute != null)
                    {
                        enumDescriptions.Add($"{enumName}: {descriptionAttribute.Description}");
                    }
                    else
                    {
                        enumDescriptions.Add(enumName!);
                    }
                }

                schema.Enum = enumValues;
                
                if (enumDescriptions.Any())
                {
                    schema.Description = string.Join(", ", enumDescriptions);
                }

                // Add example value
                if (enumValues.Any())
                {
                    schema.Example = enumValues.First();
                }
            }
        }
    }
}
