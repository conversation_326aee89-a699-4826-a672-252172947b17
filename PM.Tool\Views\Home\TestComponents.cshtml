@{
    ViewData["Title"] = "Test Components";
}

<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-neutral-900 dark:text-dark-100 mb-8">Component Test Page</h1>

    <!-- Test Form Input Component -->
    <div class="mb-8">
        <h2 class="text-2xl font-semibold text-neutral-900 dark:text-dark-100 mb-4">Form Input Component Test</h2>

        <form class="space-y-6">
            <!-- Text Input -->
            @{
                ViewData["Label"] = "Project Name";
                ViewData["Name"] = "projectName";
                ViewData["Type"] = "text";
                ViewData["Placeholder"] = "Enter project name";
                ViewData["Required"] = true;
                ViewData["Icon"] = "fas fa-tag";
                ViewData["HelpText"] = "This is a required field";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

            <!-- Email Input -->
            @{
                ViewData["Label"] = "Email Address";
                ViewData["Name"] = "email";
                ViewData["Type"] = "email";
                ViewData["Placeholder"] = "Enter email address";
                ViewData["Required"] = true;
                ViewData["Icon"] = "fas fa-envelope";
                ViewData["HelpText"] = "We'll never share your email";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

            <!-- Textarea -->
            @{
                ViewData["Label"] = "Description";
                ViewData["Name"] = "description";
                ViewData["Type"] = "textarea";
                ViewData["Placeholder"] = "Enter description";
                ViewData["Rows"] = 4;
                ViewData["Icon"] = "fas fa-align-left";
                ViewData["HelpText"] = "Provide a detailed description";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

            <!-- Select Dropdown -->
            @{
                var options = "<option value=\"\">Select Priority</option>";
                options += "<option value=\"low\">Low</option>";
                options += "<option value=\"medium\">Medium</option>";
                options += "<option value=\"high\">High</option>";
                options += "<option value=\"critical\">Critical</option>";

                ViewData["Label"] = "Priority";
                ViewData["Name"] = "priority";
                ViewData["Type"] = "select";
                ViewData["Options"] = options;
                ViewData["Required"] = true;
                ViewData["HelpText"] = "Select the priority level";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />
        </form>
    </div>

    <!-- Test Button Component -->
    <div class="mb-8">
        <h2 class="text-2xl font-semibold text-neutral-900 dark:text-dark-100 mb-4">Button Component Test</h2>

        <div class="flex flex-wrap gap-4">
            <!-- Primary Button -->
            @{
                ViewData["Text"] = "Primary Button";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-plus";
                ViewData["Href"] = "#";
            }
            <partial name="Components/_Button" view-data="ViewData" />

            <!-- Secondary Button -->
            @{
                ViewData["Text"] = "Secondary Button";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-edit";
                ViewData["Href"] = "#";
            }
            <partial name="Components/_Button" view-data="ViewData" />

            <!-- Danger Button -->
            @{
                ViewData["Text"] = "Delete";
                ViewData["Variant"] = "danger";
                ViewData["Icon"] = "fas fa-trash";
                ViewData["Type"] = "button";
                ViewData["OnClick"] = "alert('Delete clicked!')";
                ViewData["Href"] = null;
            }
            <partial name="Components/_Button" view-data="ViewData" />

            <!-- Loading Button -->
            @{
                ViewData["Text"] = "Loading...";
                ViewData["Variant"] = "primary";
                ViewData["Loading"] = true;
                ViewData["Disabled"] = true;
                ViewData["Type"] = "button";
                ViewData["Href"] = null;
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>

    <!-- Test Modal Component -->
    <div class="mb-8">
        <h2 class="text-2xl font-semibold text-neutral-900 dark:text-dark-100 mb-4">Modal Component Test</h2>

        <button onclick="openModal('testModal')" class="btn-primary-custom">
            Open Test Modal
        </button>

        @{
            ViewData["ModalId"] = "testModal";
            ViewData["ModalTitle"] = "Test Modal";
            ViewData["ModalSize"] = "md";
            ViewData["BodyContent"] = @"
                <div class=""text-center py-4"">
                    <p class=""text-neutral-600 dark:text-dark-300 mb-4"">This is a test modal to verify component rendering.</p>
                    <p class=""text-sm text-neutral-500 dark:text-dark-400"">If you can see this text properly, the modal component is working correctly.</p>
                </div>";
        }
        <partial name="Components/_Modal" view-data="ViewData" />
    </div>

    <!-- Debug Information -->
    <div class="mb-8">
        <h2 class="text-2xl font-semibold text-neutral-900 dark:text-dark-100 mb-4">Debug Information</h2>

        <div class="bg-neutral-100 dark:bg-dark-700 p-4 rounded-lg">
            <p><strong>Current Culture:</strong> @System.Globalization.CultureInfo.CurrentCulture.Name</p>
            <p><strong>Current UI Culture:</strong> @System.Globalization.CultureInfo.CurrentUICulture.Name</p>
            <p><strong>Request Path:</strong> @Context.Request.Path</p>
            <p><strong>User Agent:</strong> @Context.Request.Headers["User-Agent"]</p>
        </div>
    </div>
</div>

<script>
    console.log('Component test page loaded');
    console.log('Current culture:', '@System.Globalization.CultureInfo.CurrentCulture.Name');

    // Test form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        e.preventDefault();
        alert('Form submission prevented for testing');
    });
</script>
