@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer Localizer
@model PM.Tool.Core.Entities.Risk
@{
    ViewData["Title"] = "Delete Risk";
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Risk Management", Href = Url.Action("Index", "Risk"), Icon = "fas fa-exclamation-triangle" },
        new { Text = "Delete Risk", Href = "", Icon = "fas fa-trash" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Page Header -->
<div class="mb-8">
    <div class="flex items-center space-x-3">
        <div class="w-12 h-12 bg-danger-100 dark:bg-danger-900 rounded-lg flex items-center justify-center">
            <i class="fas fa-trash text-danger-600 dark:text-danger-400 text-xl"></i>
        </div>
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100">Delete Risk</h1>
            <p class="text-sm text-neutral-500 dark:text-dark-400">This action cannot be undone</p>
        </div>
    </div>
</div>

<!-- Confirmation Card -->
<div class="max-w-2xl">
    <div class="card-custom border-danger-200 dark:border-danger-800">
        <div class="card-header-custom bg-danger-50 dark:bg-danger-900/20 border-b border-danger-200 dark:border-danger-800">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-danger-100 dark:bg-danger-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle text-danger-600 dark:text-danger-400 text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-danger-900 dark:text-danger-100">Confirm Deletion</h3>
                    <p class="text-sm text-danger-700 dark:text-danger-300">Are you sure you want to delete this risk?</p>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <!-- Risk Details -->
            <div class="space-y-4 mb-6">
                <div class="flex items-center justify-between py-2 border-b border-neutral-200 dark:border-dark-600">
                    <span class="text-sm font-medium text-neutral-500 dark:text-dark-400">Title</span>
                    <span class="text-sm text-neutral-900 dark:text-dark-100 font-medium">@Model.Title</span>
                </div>
                <div class="flex items-center justify-between py-2 border-b border-neutral-200 dark:border-dark-600">
                    <span class="text-sm font-medium text-neutral-500 dark:text-dark-400">Project</span>
                    <span class="text-sm text-neutral-900 dark:text-dark-100">@(Model.Project?.Name ?? "No Project")</span>
                </div>
                <div class="flex items-center justify-between py-2 border-b border-neutral-200 dark:border-dark-600">
                    <span class="text-sm font-medium text-neutral-500 dark:text-dark-400">Risk Level</span>
                    @{
                        var riskLevelClass = GetRiskLevelTailwindClass(Model.RiskLevel);
                    }
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @riskLevelClass">
                        @Model.RiskLevel
                    </span>
                </div>
                <div class="flex items-center justify-between py-2 border-b border-neutral-200 dark:border-dark-600">
                    <span class="text-sm font-medium text-neutral-500 dark:text-dark-400">Status</span>
                    @{
                        var statusClass = GetStatusTailwindClass(Model.Status);
                    }
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @statusClass">
                        @Model.Status
                    </span>
                </div>
                <div class="flex items-center justify-between py-2">
                    <span class="text-sm font-medium text-neutral-500 dark:text-dark-400">Created</span>
                    <span class="text-sm text-neutral-900 dark:text-dark-100">@Model.CreatedAt.ToString("MMM dd, yyyy")</span>
                </div>
            </div>

            <!-- Warning Message -->
            <div class="bg-danger-50 dark:bg-danger-900/20 border border-danger-200 dark:border-danger-800 rounded-lg p-4 mb-6">
                <div class="flex items-start space-x-3">
                    <i class="fas fa-exclamation-triangle text-danger-600 dark:text-danger-400 mt-0.5"></i>
                    <div>
                        <h4 class="text-sm font-medium text-danger-900 dark:text-danger-100 mb-1">Warning</h4>
                        <p class="text-sm text-danger-700 dark:text-danger-300">
                            Deleting this risk will permanently remove it from the system. This action cannot be undone.
                            All associated mitigation actions will also be deleted.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <form asp-action="Delete" method="post" class="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3">
                @Html.AntiForgeryToken()
                <input type="hidden" asp-for="Id" />
                
                @{
                    ViewData["Text"] = "Cancel";
                    ViewData["Variant"] = "secondary";
                    ViewData["Href"] = Url.Action("Details", new { id = Model.Id });
                }
                <partial name="Components/_Button" view-data="ViewData" />

                @{
                    ViewData["Text"] = "Delete Risk";
                    ViewData["Variant"] = "danger";
                    ViewData["Type"] = "submit";
                    ViewData["Icon"] = "fas fa-trash";
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </form>
        </div>
    </div>
</div>

@functions {
    string GetRiskLevelTailwindClass(PM.Tool.Core.Entities.RiskLevel riskLevel)
    {
        return riskLevel switch
        {
            PM.Tool.Core.Entities.RiskLevel.Critical => "bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200",
            PM.Tool.Core.Entities.RiskLevel.High => "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
            PM.Tool.Core.Entities.RiskLevel.Medium => "bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200",
            PM.Tool.Core.Entities.RiskLevel.Low => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
            _ => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200"
        };
    }

    string GetStatusTailwindClass(PM.Tool.Core.Entities.RiskStatus status)
    {
        return status switch
        {
            PM.Tool.Core.Entities.RiskStatus.Identified => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200",
            PM.Tool.Core.Entities.RiskStatus.Analyzing => "bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200",
            PM.Tool.Core.Entities.RiskStatus.Mitigating => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
            PM.Tool.Core.Entities.RiskStatus.Resolved => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200",
            _ => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200"
        };
    }
}
