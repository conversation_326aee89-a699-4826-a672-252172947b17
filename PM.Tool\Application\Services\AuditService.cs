using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;
using System.Security.Claims;

namespace PM.Tool.Application.Services
{
    public class AuditService : IAuditService
    {
        private readonly ApplicationDbContext _context;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<AuditService> _logger;

        public AuditService(
            ApplicationDbContext context,
            IHttpContextAccessor httpContextAccessor,
            ILogger<AuditService> logger)
        {
            _context = context;
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
        }        public async Task LogAsync(AuditAction action, string entityName, int? entityId, string userId, string? oldValues = null, string? newValues = null, string? ipAddress = null, string? userAgent = null)
        {
            try
            {                var auditLog = new AuditLog
                {
                    UserId = userId,
                    Action = action,
                    EntityName = entityName,
                    EntityId = entityId,
                    OldValues = oldValues,
                    NewValues = newValues,
                    CreatedAt = DateTime.UtcNow,
                    IpAddress = ipAddress ?? _httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString(),
                    UserAgent = userAgent ?? _httpContextAccessor.HttpContext?.Request.Headers["User-Agent"].ToString()
                };

                _context.AuditLogs.Add(auditLog);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error logging audit trail");
                throw;
            }
        }

        public async Task<IEnumerable<AuditLog>> GetAuditLogsAsync(string? entityName = null, int? entityId = null, string? userId = null, DateTime? fromDate = null, DateTime? toDate = null)
        {            var query = _context.AuditLogs
                .Include(a => a.User)
                .AsQueryable();

            if (!string.IsNullOrEmpty(entityName))
                query = query.Where(a => a.EntityName == entityName);

            if (entityId.HasValue)
                query = query.Where(a => a.EntityId == entityId);

            if (!string.IsNullOrEmpty(userId))
                query = query.Where(a => a.UserId == userId);

            if (fromDate.HasValue)
                query = query.Where(a => a.CreatedAt >= fromDate);

            if (toDate.HasValue)
                query = query.Where(a => a.CreatedAt <= toDate);

            return await query.OrderByDescending(a => a.CreatedAt).ToListAsync();
        }

        public async Task<IEnumerable<AuditLog>> GetEntityHistoryAsync(string entityName, int entityId)
        {            return await _context.AuditLogs
                .Include(a => a.User)
                .Where(a => a.EntityName == entityName && a.EntityId == entityId)
                .OrderByDescending(a => a.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<AuditLog>> GetUserActivityAsync(string userId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.AuditLogs
                .Include(a => a.User)
                .Where(a => a.UserId == userId);            if (fromDate.HasValue)
                query = query.Where(a => a.CreatedAt >= fromDate);

            if (toDate.HasValue)
                query = query.Where(a => a.CreatedAt <= toDate);

            return await query.OrderByDescending(a => a.CreatedAt).ToListAsync();
        }
    }
}
