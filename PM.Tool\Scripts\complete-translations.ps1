# PowerShell script to complete all missing translations for all cultures
# This script adds proper translations for all missing keys in all resource files

$translations = @{
    "es" = @{
        # Meeting Management
        "Meeting.Title" = "Título de la reunión"
        "Meeting.Description" = "Descripción"
        "Meeting.StartTime" = "Hora de inicio"
        "Meeting.EndTime" = "Hora de fin"
        "Meeting.Location" = "Ubicación"
        "Meeting.Type" = "Tipo de reunión"
        "Meeting.Status" = "Estado"
        "Meeting.Organizer" = "Organizador"
        "Meeting.Attendees" = "Asistentes"
        "Meeting.ActionItems" = "Elementos de acción"
        "Meeting.Documents" = "Documentos"
        "Meeting.Minutes" = "Actas"
        "Meeting.Agenda" = "Agenda"

        # Requirements Management
        "Requirement.Title" = "Título del requisito"
        "Requirement.Description" = "Descripción"
        "Requirement.Type" = "Tipo"
        "Requirement.Priority" = "Prioridad"
        "Requirement.Status" = "Estado"
        "Requirement.Source" = "Fuente"
        "Requirement.Stakeholder" = "Interesado"
        "Requirement.AcceptanceCriteria" = "Criterios de aceptación"
        "Requirement.BusinessValue" = "Valor de negocio"
        "Requirement.Comments" = "Comentarios"
        "Requirement.Attachments" = "Adjuntos"
        "Requirement.Changes" = "Historial de cambios"
        "Requirement.Tasks" = "Tareas relacionadas"

        # Risk Management
        "Risk.Title" = "Título del riesgo"
        "Risk.Description" = "Descripción"
        "Risk.Category" = "Categoría"
        "Risk.Probability" = "Probabilidad"
        "Risk.Impact" = "Impacto"
        "Risk.Score" = "Puntuación de riesgo"
        "Risk.Status" = "Estado"
        "Risk.Owner" = "Propietario del riesgo"
        "Risk.MitigationPlan" = "Plan de mitigación"
        "Risk.MitigationActions" = "Acciones de mitigación"
        "Risk.ContingencyPlan" = "Plan de contingencia"

        # Resource Management
        "Resource.Name" = "Nombre del recurso"
        "Resource.Type" = "Tipo"
        "Resource.Department" = "Departamento"
        "Resource.Location" = "Ubicación"
        "Resource.HourlyRate" = "Tarifa por hora"
        "Resource.Capacity" = "Capacidad"
        "Resource.Skills" = "Habilidades"
        "Resource.Availability" = "Disponibilidad"
        "Resource.Utilization" = "Utilización"
        "Resource.Allocation" = "Asignación"

        # Agile/Scrum Terms
        "Agile.SprintPlanning" = "Planificación de Sprint"
        "Agile.SprintReview" = "Revisión de Sprint"
        "Agile.SprintRetrospective" = "Retrospectiva de Sprint"
        "Agile.DailyStandup" = "Reunión diaria"
        "Agile.ProductBacklog" = "Backlog del producto"
        "Agile.SprintBacklog" = "Backlog del sprint"
        "Agile.Definition" = "Definición de terminado"
        "Agile.StoryPoints" = "Puntos de historia"
        "Agile.SprintStarted" = "Sprint iniciado exitosamente"
        "Agile.SprintCompleted" = "Sprint completado exitosamente"

        # WBS Specific Messages
        "WBS.ErrorLoadingStructure" = "Error al cargar la estructura WBS"
        "WBS.TaskCreatedSuccessfully" = "Tarea creada exitosamente"
        "WBS.TaskDeletedSuccessfully" = "Tarea eliminada exitosamente"
        "WBS.TaskDuplicatedSuccessfully" = "Tarea duplicada exitosamente"
        "WBS.TaskMovedSuccessfully" = "Tarea movida {0} exitosamente"
        "WBS.TaskStatusUpdated" = "Estado de tarea actualizado a {0}"
        "WBS.WbsCodesGenerated" = "Códigos WBS generados exitosamente"
        "WBS.ExportingTo" = "Exportando WBS a {0}..."
        "WBS.ExportingTask" = "Exportando tarea..."
        "WBS.PrintingWbs" = "Abriendo diálogo de impresión..."
        "WBS.CompactViewEnabled" = "Vista compacta habilitada"
        "WBS.NormalViewEnabled" = "Vista normal habilitada"
        "WBS.DetailedViewEnabled" = "Vista detallada habilitada"
        "WBS.RefreshingView" = "Actualizando vista WBS..."
        "WBS.FilteredToShow" = "Filtrado para mostrar tareas {0}"
        "WBS.ShowingAllTasks" = "Mostrando todas las tareas"
        "WBS.FilteredOverdue" = "Filtrado para mostrar tareas vencidas"
        "WBS.TaskTitleRequired" = "El título de la tarea es requerido"
        "WBS.ProjectIdMissing" = "Falta el ID del proyecto"
        "WBS.ValidationError" = "Error de validación. Por favor verifique su entrada."
        "WBS.AccessDenied" = "Acceso denegado. Por favor actualice la página e intente de nuevo."
        "WBS.RequestFormatError" = "Error de formato de solicitud. Por favor intente de nuevo."
        "WBS.ErrorCreatingTask" = "Error al crear la tarea"
        "WBS.ErrorLoadingTaskDetails" = "Error al cargar los detalles de la tarea"
        "WBS.ErrorDeletingTask" = "Error al eliminar la tarea"
        "WBS.ErrorDuplicatingTask" = "Error al duplicar la tarea"
        "WBS.ErrorMovingTask" = "Error al mover la tarea {0}"
        "WBS.ErrorUpdatingTaskStatus" = "Error al actualizar el estado de la tarea"
        "WBS.ErrorGeneratingCodes" = "Error al generar códigos WBS"
        "WBS.ConfirmDeleteTask" = "¿Está seguro de que desea eliminar esta tarea? Esta acción no se puede deshacer."
        "WBS.ConfirmDuplicateTask" = "¿Crear un duplicado de esta tarea?"
        "WBS.ConfirmGenerateCodes" = "Esto regenerará todos los códigos WBS. ¿Continuar?"
        "WBS.CreateNewTask" = "Crear nueva tarea"
        "WBS.CreateChildTaskFor" = "Crear tarea hija para: {0}"
        "WBS.TaskNotFound" = "Tarea no encontrada"
        "WBS.UnsupportedExportFormat" = "Formato de exportación no soportado"
        "WBS.GanttViewComingSoon" = "¡La vista Gantt llegará pronto!"

        # WBS Task Actions
        "WBS.Action.ViewDetails" = "Ver detalles"
        "WBS.Action.EditTask" = "Editar tarea"
        "WBS.Action.Duplicate" = "Duplicar"
        "WBS.Action.AddChild" = "Agregar hijo"
        "WBS.Action.MoreActions" = "Más acciones"
        "WBS.Action.MoveUp" = "Mover arriba"
        "WBS.Action.MoveDown" = "Mover abajo"
        "WBS.Action.StartTask" = "Iniciar tarea"
        "WBS.Action.MarkInReview" = "Marcar en revisión"
        "WBS.Action.MarkComplete" = "Marcar como completo"
        "WBS.Action.CancelTask" = "Cancelar tarea"
        "WBS.Action.ExportTask" = "Exportar tarea"
        "WBS.Action.DeleteTask" = "Eliminar tarea"

        # WBS Labels
        "WBS.Label.Progress" = "Progreso"
        "WBS.Label.Unassigned" = "Sin asignar"
        "WBS.Label.Overdue" = "Vencido"
        "WBS.Label.Start" = "Inicio"
        "WBS.Label.Due" = "Vencimiento"
        "WBS.Label.GeneratedOn" = "Generado el"
        "WBS.Label.WorkBreakdownStructure" = "Estructura de Desglose del Trabajo"
    }

    "zh-CN" = @{
        # WBS Specific Messages
        "WBS.ErrorLoadingStructure" = "加载WBS结构时出错"
        "WBS.TaskCreatedSuccessfully" = "任务创建成功"
        "WBS.TaskDeletedSuccessfully" = "任务删除成功"
        "WBS.TaskDuplicatedSuccessfully" = "任务复制成功"
        "WBS.TaskMovedSuccessfully" = "任务{0}移动成功"
        "WBS.TaskStatusUpdated" = "任务状态已更新为{0}"
        "WBS.WbsCodesGenerated" = "WBS代码生成成功"
        "WBS.ExportingTo" = "正在导出WBS到{0}..."
        "WBS.ExportingTask" = "正在导出任务..."
        "WBS.PrintingWbs" = "正在打开打印对话框..."
        "WBS.CompactViewEnabled" = "紧凑视图已启用"
        "WBS.NormalViewEnabled" = "正常视图已启用"
        "WBS.DetailedViewEnabled" = "详细视图已启用"
        "WBS.RefreshingView" = "正在刷新WBS视图..."
        "WBS.FilteredToShow" = "已筛选显示{0}任务"
        "WBS.ShowingAllTasks" = "显示所有任务"
        "WBS.FilteredOverdue" = "已筛选显示逾期任务"
        "WBS.TaskTitleRequired" = "任务标题是必需的"
        "WBS.ProjectIdMissing" = "项目ID缺失"
        "WBS.ValidationError" = "验证错误。请检查您的输入。"
        "WBS.AccessDenied" = "访问被拒绝。请刷新页面后重试。"
        "WBS.RequestFormatError" = "请求格式错误。请重试。"
        "WBS.ErrorCreatingTask" = "创建任务时出错"
        "WBS.ErrorLoadingTaskDetails" = "加载任务详情时出错"
        "WBS.ErrorDeletingTask" = "删除任务时出错"
        "WBS.ErrorDuplicatingTask" = "复制任务时出错"
        "WBS.ErrorMovingTask" = "移动任务{0}时出错"
        "WBS.ErrorUpdatingTaskStatus" = "更新任务状态时出错"
        "WBS.ErrorGeneratingCodes" = "生成WBS代码时出错"
        "WBS.ConfirmDeleteTask" = "您确定要删除此任务吗？此操作无法撤销。"
        "WBS.ConfirmDuplicateTask" = "创建此任务的副本？"
        "WBS.ConfirmGenerateCodes" = "这将重新生成所有WBS代码。继续？"
        "WBS.CreateNewTask" = "创建新任务"
        "WBS.CreateChildTaskFor" = "为{0}创建子任务"
        "WBS.TaskNotFound" = "未找到任务"
        "WBS.UnsupportedExportFormat" = "不支持的导出格式"
        "WBS.GanttViewComingSoon" = "甘特图视图即将推出！"

        # WBS Task Actions
        "WBS.Action.ViewDetails" = "查看详情"
        "WBS.Action.EditTask" = "编辑任务"
        "WBS.Action.Duplicate" = "复制"
        "WBS.Action.AddChild" = "添加子项"
        "WBS.Action.MoreActions" = "更多操作"
        "WBS.Action.MoveUp" = "上移"
        "WBS.Action.MoveDown" = "下移"
        "WBS.Action.StartTask" = "开始任务"
        "WBS.Action.MarkInReview" = "标记为审核中"
        "WBS.Action.MarkComplete" = "标记为完成"
        "WBS.Action.CancelTask" = "取消任务"
        "WBS.Action.ExportTask" = "导出任务"
        "WBS.Action.DeleteTask" = "删除任务"

        # WBS Labels
        "WBS.Label.Progress" = "进度"
        "WBS.Label.Unassigned" = "未分配"
        "WBS.Label.Overdue" = "逾期"
        "WBS.Label.Start" = "开始"
        "WBS.Label.Due" = "截止"
        "WBS.Label.GeneratedOn" = "生成于"
        "WBS.Label.WorkBreakdownStructure" = "工作分解结构"
    }

    "ja-JP" = @{
        # WBS Specific Messages
        "WBS.ErrorLoadingStructure" = "WBS構造の読み込みエラー"
        "WBS.TaskCreatedSuccessfully" = "タスクが正常に作成されました"
        "WBS.TaskDeletedSuccessfully" = "タスクが正常に削除されました"
        "WBS.TaskDuplicatedSuccessfully" = "タスクが正常に複製されました"
        "WBS.TaskMovedSuccessfully" = "タスクが{0}に正常に移動されました"
        "WBS.TaskStatusUpdated" = "タスクステータスが{0}に更新されました"
        "WBS.WbsCodesGenerated" = "WBSコードが正常に生成されました"
        "WBS.ExportingTo" = "WBSを{0}にエクスポート中..."
        "WBS.ExportingTask" = "タスクをエクスポート中..."
        "WBS.PrintingWbs" = "印刷ダイアログを開いています..."
        "WBS.CompactViewEnabled" = "コンパクトビューが有効になりました"
        "WBS.NormalViewEnabled" = "通常ビューが有効になりました"
        "WBS.DetailedViewEnabled" = "詳細ビューが有効になりました"
        "WBS.RefreshingView" = "WBSビューを更新中..."
        "WBS.FilteredToShow" = "{0}タスクを表示するようにフィルタリングされました"
        "WBS.ShowingAllTasks" = "すべてのタスクを表示"
        "WBS.FilteredOverdue" = "期限切れタスクを表示するようにフィルタリングされました"
        "WBS.TaskTitleRequired" = "タスクタイトルが必要です"
        "WBS.ProjectIdMissing" = "プロジェクトIDが不足しています"
        "WBS.ValidationError" = "検証エラー。入力を確認してください。"
        "WBS.AccessDenied" = "アクセスが拒否されました。ページを更新して再試行してください。"
        "WBS.RequestFormatError" = "リクエスト形式エラー。再試行してください。"
        "WBS.ErrorCreatingTask" = "タスク作成エラー"
        "WBS.ErrorLoadingTaskDetails" = "タスク詳細の読み込みエラー"
        "WBS.ErrorDeletingTask" = "タスク削除エラー"
        "WBS.ErrorDuplicatingTask" = "タスク複製エラー"
        "WBS.ErrorMovingTask" = "タスク{0}移動エラー"
        "WBS.ErrorUpdatingTaskStatus" = "タスクステータス更新エラー"
        "WBS.ErrorGeneratingCodes" = "WBSコード生成エラー"
        "WBS.ConfirmDeleteTask" = "このタスクを削除してもよろしいですか？この操作は元に戻せません。"
        "WBS.ConfirmDuplicateTask" = "このタスクの複製を作成しますか？"
        "WBS.ConfirmGenerateCodes" = "これによりすべてのWBSコードが再生成されます。続行しますか？"
        "WBS.CreateNewTask" = "新しいタスクを作成"
        "WBS.CreateChildTaskFor" = "{0}の子タスクを作成"
        "WBS.TaskNotFound" = "タスクが見つかりません"
        "WBS.UnsupportedExportFormat" = "サポートされていないエクスポート形式"
        "WBS.GanttViewComingSoon" = "ガントビュー機能は近日公開予定です！"

        # WBS Task Actions
        "WBS.Action.ViewDetails" = "詳細を表示"
        "WBS.Action.EditTask" = "タスクを編集"
        "WBS.Action.Duplicate" = "複製"
        "WBS.Action.AddChild" = "子を追加"
        "WBS.Action.MoreActions" = "その他のアクション"
        "WBS.Action.MoveUp" = "上に移動"
        "WBS.Action.MoveDown" = "下に移動"
        "WBS.Action.StartTask" = "タスクを開始"
        "WBS.Action.MarkInReview" = "レビュー中としてマーク"
        "WBS.Action.MarkComplete" = "完了としてマーク"
        "WBS.Action.CancelTask" = "タスクをキャンセル"
        "WBS.Action.ExportTask" = "タスクをエクスポート"
        "WBS.Action.DeleteTask" = "タスクを削除"

        # WBS Labels
        "WBS.Label.Progress" = "進捗"
        "WBS.Label.Unassigned" = "未割り当て"
        "WBS.Label.Overdue" = "期限切れ"
        "WBS.Label.Start" = "開始"
        "WBS.Label.Due" = "期限"
        "WBS.Label.GeneratedOn" = "生成日"
        "WBS.Label.WorkBreakdownStructure" = "作業分解構造"
    }

    "ru-RU" = @{
        # WBS Specific Messages
        "WBS.ErrorLoadingStructure" = "Ошибка загрузки структуры WBS"
        "WBS.TaskCreatedSuccessfully" = "Задача успешно создана"
        "WBS.TaskDeletedSuccessfully" = "Задача успешно удалена"
        "WBS.TaskDuplicatedSuccessfully" = "Задача успешно дублирована"
        "WBS.TaskMovedSuccessfully" = "Задача успешно перемещена {0}"
        "WBS.TaskStatusUpdated" = "Статус задачи обновлен на {0}"
        "WBS.WbsCodesGenerated" = "Коды WBS успешно сгенерированы"
        "WBS.ExportingTo" = "Экспорт WBS в {0}..."
        "WBS.ExportingTask" = "Экспорт задачи..."
        "WBS.PrintingWbs" = "Открытие диалога печати..."
        "WBS.CompactViewEnabled" = "Компактный вид включен"
        "WBS.NormalViewEnabled" = "Обычный вид включен"
        "WBS.DetailedViewEnabled" = "Подробный вид включен"
        "WBS.RefreshingView" = "Обновление вида WBS..."
        "WBS.FilteredToShow" = "Отфильтровано для показа задач {0}"
        "WBS.ShowingAllTasks" = "Показаны все задачи"
        "WBS.FilteredOverdue" = "Отфильтровано для показа просроченных задач"
        "WBS.TaskTitleRequired" = "Название задачи обязательно"
        "WBS.ProjectIdMissing" = "Отсутствует ID проекта"
        "WBS.ValidationError" = "Ошибка валидации. Проверьте ввод."
        "WBS.AccessDenied" = "Доступ запрещен. Обновите страницу и попробуйте снова."
        "WBS.RequestFormatError" = "Ошибка формата запроса. Попробуйте снова."
        "WBS.ErrorCreatingTask" = "Ошибка создания задачи"
        "WBS.ErrorLoadingTaskDetails" = "Ошибка загрузки деталей задачи"
        "WBS.ErrorDeletingTask" = "Ошибка удаления задачи"
        "WBS.ErrorDuplicatingTask" = "Ошибка дублирования задачи"
        "WBS.ErrorMovingTask" = "Ошибка перемещения задачи {0}"
        "WBS.ErrorUpdatingTaskStatus" = "Ошибка обновления статуса задачи"
        "WBS.ErrorGeneratingCodes" = "Ошибка генерации кодов WBS"
        "WBS.ConfirmDeleteTask" = "Вы уверены, что хотите удалить эту задачу? Это действие нельзя отменить."
        "WBS.ConfirmDuplicateTask" = "Создать дубликат этой задачи?"
        "WBS.ConfirmGenerateCodes" = "Это перегенерирует все коды WBS. Продолжить?"
        "WBS.CreateNewTask" = "Создать новую задачу"
        "WBS.CreateChildTaskFor" = "Создать дочернюю задачу для: {0}"
        "WBS.TaskNotFound" = "Задача не найдена"
        "WBS.UnsupportedExportFormat" = "Неподдерживаемый формат экспорта"
        "WBS.GanttViewComingSoon" = "Вид Гантта скоро появится!"

        # WBS Task Actions
        "WBS.Action.ViewDetails" = "Просмотр деталей"
        "WBS.Action.EditTask" = "Редактировать задачу"
        "WBS.Action.Duplicate" = "Дублировать"
        "WBS.Action.AddChild" = "Добавить дочернюю"
        "WBS.Action.MoreActions" = "Больше действий"
        "WBS.Action.MoveUp" = "Переместить вверх"
        "WBS.Action.MoveDown" = "Переместить вниз"
        "WBS.Action.StartTask" = "Начать задачу"
        "WBS.Action.MarkInReview" = "Отметить на проверке"
        "WBS.Action.MarkComplete" = "Отметить завершенной"
        "WBS.Action.CancelTask" = "Отменить задачу"
        "WBS.Action.ExportTask" = "Экспортировать задачу"
        "WBS.Action.DeleteTask" = "Удалить задачу"

        # WBS Labels
        "WBS.Label.Progress" = "Прогресс"
        "WBS.Label.Unassigned" = "Не назначено"
        "WBS.Label.Overdue" = "Просрочено"
        "WBS.Label.Start" = "Начало"
        "WBS.Label.Due" = "Срок"
        "WBS.Label.GeneratedOn" = "Сгенерировано"
        "WBS.Label.WorkBreakdownStructure" = "Структура декомпозиции работ"
    }

    "it-IT" = @{
        # Meeting Management
        "Meeting.Title" = "Titolo della riunione"
        "Meeting.Description" = "Descrizione"
        "Meeting.StartTime" = "Ora di inizio"
        "Meeting.EndTime" = "Ora di fine"
        "Meeting.Location" = "Posizione"
        "Meeting.Type" = "Tipo di riunione"
        "Meeting.Status" = "Stato"
        "Meeting.Organizer" = "Organizzatore"
        "Meeting.Attendees" = "Partecipanti"
        "Meeting.ActionItems" = "Elementi d'azione"
        "Meeting.Documents" = "Documenti"
        "Meeting.Minutes" = "Verbale"
        "Meeting.Agenda" = "Agenda"

        # WBS Specific Messages
        "WBS.ErrorLoadingStructure" = "Errore nel caricamento della struttura WBS"
        "WBS.TaskCreatedSuccessfully" = "Attività creata con successo"
        "WBS.TaskDeletedSuccessfully" = "Attività eliminata con successo"
        "WBS.TaskDuplicatedSuccessfully" = "Attività duplicata con successo"
        "WBS.TaskMovedSuccessfully" = "Attività spostata {0} con successo"
        "WBS.TaskStatusUpdated" = "Stato dell'attività aggiornato a {0}"
        "WBS.WbsCodesGenerated" = "Codici WBS generati con successo"
        "WBS.ExportingTo" = "Esportazione WBS in {0}..."
        "WBS.ExportingTask" = "Esportazione attività..."
        "WBS.PrintingWbs" = "Apertura finestra di stampa..."
        "WBS.CompactViewEnabled" = "Vista compatta abilitata"
        "WBS.NormalViewEnabled" = "Vista normale abilitata"
        "WBS.DetailedViewEnabled" = "Vista dettagliata abilitata"
        "WBS.RefreshingView" = "Aggiornamento vista WBS..."
        "WBS.FilteredToShow" = "Filtrato per mostrare attività {0}"
        "WBS.ShowingAllTasks" = "Mostrando tutte le attività"
        "WBS.FilteredOverdue" = "Filtrato per mostrare attività scadute"
        "WBS.TaskTitleRequired" = "Il titolo dell'attività è richiesto"
        "WBS.ProjectIdMissing" = "ID progetto mancante"
        "WBS.ValidationError" = "Errore di validazione. Controllare l'input."
        "WBS.AccessDenied" = "Accesso negato. Aggiornare la pagina e riprovare."
        "WBS.RequestFormatError" = "Errore formato richiesta. Riprovare."
        "WBS.ErrorCreatingTask" = "Errore nella creazione dell'attività"
        "WBS.ErrorLoadingTaskDetails" = "Errore nel caricamento dei dettagli dell'attività"
        "WBS.ErrorDeletingTask" = "Errore nell'eliminazione dell'attività"
        "WBS.ErrorDuplicatingTask" = "Errore nella duplicazione dell'attività"
        "WBS.ErrorMovingTask" = "Errore nello spostamento dell'attività {0}"
        "WBS.ErrorUpdatingTaskStatus" = "Errore nell'aggiornamento dello stato dell'attività"
        "WBS.ErrorGeneratingCodes" = "Errore nella generazione dei codici WBS"
        "WBS.ConfirmDeleteTask" = "Sei sicuro di voler eliminare questa attività? Questa azione non può essere annullata."
        "WBS.ConfirmDuplicateTask" = "Creare un duplicato di questa attività?"
        "WBS.ConfirmGenerateCodes" = "Questo rigenererà tutti i codici WBS. Continuare?"
        "WBS.CreateNewTask" = "Crea nuova attività"
        "WBS.CreateChildTaskFor" = "Crea attività figlia per: {0}"
        "WBS.TaskNotFound" = "Attività non trovata"
        "WBS.UnsupportedExportFormat" = "Formato di esportazione non supportato"
        "WBS.GanttViewComingSoon" = "La vista Gantt arriverà presto!"

        # WBS Task Actions
        "WBS.Action.ViewDetails" = "Visualizza dettagli"
        "WBS.Action.EditTask" = "Modifica attività"
        "WBS.Action.Duplicate" = "Duplica"
        "WBS.Action.AddChild" = "Aggiungi figlio"
        "WBS.Action.MoreActions" = "Altre azioni"
        "WBS.Action.MoveUp" = "Sposta su"
        "WBS.Action.MoveDown" = "Sposta giù"
        "WBS.Action.StartTask" = "Avvia attività"
        "WBS.Action.MarkInReview" = "Segna in revisione"
        "WBS.Action.MarkComplete" = "Segna come completato"
        "WBS.Action.CancelTask" = "Annulla attività"
        "WBS.Action.ExportTask" = "Esporta attività"
        "WBS.Action.DeleteTask" = "Elimina attività"

        # WBS Labels
        "WBS.Label.Progress" = "Progresso"
        "WBS.Label.Unassigned" = "Non assegnato"
        "WBS.Label.Overdue" = "Scaduto"
        "WBS.Label.Start" = "Inizio"
        "WBS.Label.Due" = "Scadenza"
        "WBS.Label.GeneratedOn" = "Generato il"
        "WBS.Label.WorkBreakdownStructure" = "Struttura di Scomposizione del Lavoro"
    }
}

$resourcesPath = "PM.Tool/Resources"
$templateFile = "$resourcesPath/SharedResources.resx"

Write-Host "Completing translations for all cultures..." -ForegroundColor Green

# Get the main resource file content to extract all keys
[xml]$mainContent = Get-Content $templateFile

# Extract all data keys from the main file
$allKeys = $mainContent.root.data | ForEach-Object { $_.name }

Write-Host "Found $($allKeys.Count) keys in main resource file" -ForegroundColor Cyan

# Process each culture file
$cultureFiles = Get-ChildItem "$resourcesPath/SharedResources.*.resx" | Where-Object { $_.Name -ne "SharedResources.resx" }

foreach ($cultureFile in $cultureFiles) {
    $cultureName = $cultureFile.BaseName -replace "SharedResources\.", ""
    Write-Host "Processing culture: $cultureName" -ForegroundColor Yellow

    try {
        [xml]$cultureContent = Get-Content $cultureFile.FullName

        # Get existing keys in this culture file
        $existingKeys = @()
        $existingData = @{}
        if ($cultureContent.root.data) {
            $cultureContent.root.data | ForEach-Object {
                $existingKeys += $_.name
                $existingData[$_.name] = $_
            }
        }

        $missingKeys = $allKeys | Where-Object { $_ -notin $existingKeys }
        $placeholderKeys = @()

        # Check for placeholder values that need to be replaced
        foreach ($key in $existingKeys) {
            $currentValue = $existingData[$key].value
            if ($currentValue -match "^\[.*- $cultureName\]$") {
                $placeholderKeys += $key
            }
        }

        $totalUpdates = $missingKeys.Count + $placeholderKeys.Count

        if ($totalUpdates -gt 0) {
            Write-Host "  Adding $($missingKeys.Count) missing keys, updating $($placeholderKeys.Count) placeholder values" -ForegroundColor Cyan

            # Add missing keys
            foreach ($key in $missingKeys) {
                $originalValue = ($mainContent.root.data | Where-Object { $_.name -eq $key }).value

                # Check if we have a specific translation for this culture and key
                $translatedValue = $originalValue
                if ($translations.ContainsKey($cultureName) -and $translations[$cultureName].ContainsKey($key)) {
                    $translatedValue = $translations[$cultureName][$key]
                } elseif ($translations.ContainsKey($cultureName.Split('-')[0]) -and $translations[$cultureName.Split('-')[0]].ContainsKey($key)) {
                    $translatedValue = $translations[$cultureName.Split('-')[0]][$key]
                } else {
                    # Use placeholder format for keys we don't have translations for
                    $translatedValue = "[$originalValue - $cultureName]"
                }

                # Create new data element
                $newData = $cultureContent.CreateElement("data")
                $newData.SetAttribute("name", $key)
                $newData.SetAttribute("xml:space", "preserve")

                $valueElement = $cultureContent.CreateElement("value")
                $valueElement.InnerText = $translatedValue
                $newData.AppendChild($valueElement)

                $cultureContent.root.AppendChild($newData)
            }

            # Update placeholder values
            foreach ($key in $placeholderKeys) {
                $originalValue = ($mainContent.root.data | Where-Object { $_.name -eq $key }).value

                # Check if we have a specific translation for this culture and key
                $translatedValue = $originalValue
                if ($translations.ContainsKey($cultureName) -and $translations[$cultureName].ContainsKey($key)) {
                    $translatedValue = $translations[$cultureName][$key]
                } elseif ($translations.ContainsKey($cultureName.Split('-')[0]) -and $translations[$cultureName.Split('-')[0]].ContainsKey($key)) {
                    $translatedValue = $translations[$cultureName.Split('-')[0]][$key]
                } else {
                    # Keep placeholder format for keys we don't have translations for
                    $translatedValue = "[$originalValue - $cultureName]"
                }

                # Update existing element
                $existingData[$key].value = $translatedValue
            }

            # Save the updated file
            $cultureContent.Save($cultureFile.FullName)
            Write-Host "  Updated $($cultureFile.Name)" -ForegroundColor Green
        } else {
            Write-Host "  No updates needed" -ForegroundColor Green
        }
    }
    catch {
        Write-Host "  Error processing $($cultureFile.Name): $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`nTranslation completion finished!" -ForegroundColor Green
