using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Enums;
using PM.Tool.Services;
using PM.Tool.Models.ViewModels;

namespace PM.Tool.Controllers
{
    public class MeetingController : SecureBaseController
    {
        private readonly IMeetingService _meetingService;
        private readonly IProjectService _projectService;
        private readonly IFormHelperService _formHelper;
        private readonly ILogger<MeetingController> _logger;

        public MeetingController(
            IMeetingService meetingService,
            IProjectService projectService,
            IFormHelperService formHelper,
            IAuditService auditService,
            ILogger<MeetingController> logger) : base(auditService)
        {
            _meetingService = meetingService;
            _projectService = projectService;
            _formHelper = formHelper;
            _logger = logger;
        }

        // GET: Meeting
        public async Task<IActionResult> Index(int? projectId)
        {
            try
            {
                IEnumerable<Meeting> meetings;

                if (projectId.HasValue)
                {
                    meetings = await _meetingService.GetProjectMeetingsAsync(projectId.Value);
                    var project = await _projectService.GetProjectByIdAsync(projectId.Value);
                    ViewBag.ProjectName = project?.Name;
                    ViewBag.ProjectId = projectId.Value;
                }
                else
                {
                    var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "";
                    meetings = await _meetingService.GetUserMeetingsAsync(userId);
                }

                return View(meetings);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading meetings.";
                return View(new List<Meeting>());
            }
        }

        // GET: Meeting/Calendar
        public async Task<IActionResult> Calendar(int? projectId, DateTime? date)
        {
            try
            {
                var selectedDate = date ?? DateTime.Today;
                var startDate = selectedDate.AddDays(-7);
                var endDate = selectedDate.AddDays(21); // Show 4 weeks

                var meetings = await _meetingService.GetMeetingsByDateRangeAsync(startDate, endDate, projectId);

                ViewBag.SelectedDate = selectedDate;
                ViewBag.ProjectId = projectId;

                if (projectId.HasValue)
                {
                    var project = await _projectService.GetProjectByIdAsync(projectId.Value);
                    ViewBag.ProjectName = project?.Name;
                }

                return View(meetings);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading meeting calendar.";
                return View(new List<Meeting>());
            }
        }

        // GET: Meeting/Details/5
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var meeting = await _meetingService.GetMeetingByIdAsync(id);
                if (meeting == null) return NotFound();

                var attendees = await _meetingService.GetMeetingAttendeesAsync(id);
                var actionItems = await _meetingService.GetMeetingActionItemsAsync(id);
                var documents = await _meetingService.GetMeetingDocumentsAsync(id);

                ViewBag.Attendees = attendees;
                ViewBag.ActionItems = actionItems;
                ViewBag.Documents = documents;

                return View(meeting);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading meeting details.";
                return RedirectToAction("Index");
            }
        }

        // GET: Meeting/Create
        public async Task<IActionResult> Create(int? projectId)
        {
            try
            {
                var viewModel = new MeetingCreateViewModel();

                if (projectId.HasValue)
                {
                    viewModel.ProjectId = projectId.Value;
                    var project = await _projectService.GetProjectByIdAsync(projectId.Value);
                    ViewBag.ProjectName = project?.Name;
                }

                await PopulateDropdowns();
                return View(viewModel);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading meeting creation form.";
                return RedirectToAction("Index");
            }
        }

        // POST: Meeting/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(MeetingCreateViewModel viewModel)
        {
            return await this.HandleCreateAsync<MeetingCreateViewModel, Meeting>(
                viewModel,
                async (meeting) => {
                    meeting.OrganizerUserId = _formHelper.GetCurrentUserId(User);
                    var createdMeeting = await _meetingService.CreateMeetingAsync(meeting);
                    await LogAuditAsync(AuditAction.Create, "Meeting", createdMeeting.Id);
                    return createdMeeting;
                },
                meeting => meeting.Id,
                PopulateDropdowns,
                _formHelper,
                _logger,
                "Meeting"
            );
        }

        // GET: Meeting/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                var meeting = await _meetingService.GetMeetingByIdAsync(id);
                if (meeting == null) return NotFound();

                var viewModel = MeetingEditViewModel.FromEntity(meeting);
                await PopulateDropdowns();
                return View(viewModel);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading meeting for editing.";
                return RedirectToAction("Index");
            }
        }

        // POST: Meeting/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, MeetingEditViewModel viewModel)
        {
            if (id != viewModel.Id) return NotFound();

            return await this.HandleUpdateAsync<MeetingEditViewModel, Meeting>(
                id,
                viewModel,
                async (meetingId) => await _meetingService.GetMeetingByIdAsync(meetingId),
                async (meeting) => {
                    var success = await _meetingService.UpdateMeetingAsync(meeting);
                    if (success)
                    {
                        await LogAuditAsync(AuditAction.Update, "Meeting", id);
                        return meeting;
                    }
                    throw new InvalidOperationException("Failed to update meeting");
                },
                PopulateDropdowns,
                _formHelper,
                _logger,
                "Meeting"
            );
        }

        // POST: Meeting/Cancel/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Cancel(int id, string reason)
        {
            try
            {
                var success = await _meetingService.CancelMeetingAsync(id, reason);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Update, "Meeting", id);
                    TempData["Success"] = "Meeting cancelled successfully.";
                }
                else
                {
                    TempData["Error"] = "Failed to cancel meeting.";
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error cancelling meeting.";
            }

            return RedirectToAction("Details", new { id });
        }

        // POST: Meeting/Start/5
        [HttpPost]
        public async Task<IActionResult> Start(int id)
        {
            try
            {
                var success = await _meetingService.StartMeetingAsync(id);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Update, "Meeting", id);
                    return Json(new { success = true });
                }

                return Json(new { success = false, message = "Failed to start meeting." });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Error starting meeting." });
            }
        }

        // POST: Meeting/End/5
        [HttpPost]
        public async Task<IActionResult> End(int id)
        {
            try
            {
                var success = await _meetingService.EndMeetingAsync(id);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Update, "Meeting", id);
                    return Json(new { success = true });
                }

                return Json(new { success = false, message = "Failed to end meeting." });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Error ending meeting." });
            }
        }

        // GET: Meeting/Upcoming
        public async Task<IActionResult> Upcoming()
        {
            try
            {
                var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "";
                var upcomingMeetings = await _meetingService.GetUpcomingMeetingsAsync(userId, 14); // Next 2 weeks

                return View(upcomingMeetings);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading upcoming meetings.";
                return View(new List<Meeting>());
            }
        }

        // GET: Meeting/ActionItems
        public async Task<IActionResult> ActionItems()
        {
            try
            {
                var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "";
                var actionItems = await _meetingService.GetUserActionItemsAsync(userId, false);

                return View(actionItems);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading action items.";
                return View(new List<MeetingActionItem>());
            }
        }

        // POST: Meeting/CompleteActionItem/5
        [HttpPost]
        public async Task<IActionResult> CompleteActionItem(int id)
        {
            try
            {
                var success = await _meetingService.CompleteActionItemAsync(id);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Update, "MeetingActionItem", id);
                    return Json(new { success = true });
                }

                return Json(new { success = false, message = "Failed to complete action item." });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Error completing action item." });
            }
        }

        // GET: Meeting/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var meeting = await _meetingService.GetMeetingByIdAsync(id);
                if (meeting == null) return NotFound();

                return View(meeting);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading meeting for deletion.";
                return RedirectToAction("Index");
            }
        }

        // POST: Meeting/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var success = await _meetingService.DeleteMeetingAsync(id);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Delete, "Meeting", id);
                    TempData["Success"] = "Meeting deleted successfully.";
                }
                else
                {
                    TempData["Error"] = "Failed to delete meeting.";
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error deleting meeting.";
            }

            return RedirectToAction("Index");
        }

        // POST: Meeting/StartMeeting/5
        [HttpPost]
        public async Task<IActionResult> StartMeeting(int id)
        {
            try
            {
                var success = await _meetingService.StartMeetingAsync(id);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Update, "Meeting", id);
                    TempData["Success"] = "Meeting started successfully.";
                }
                else
                {
                    TempData["Error"] = "Failed to start meeting.";
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error starting meeting.";
            }

            return RedirectToAction("Details", new { id });
        }

        // POST: Meeting/EndMeeting/5
        [HttpPost]
        public async Task<IActionResult> EndMeeting(int id)
        {
            try
            {
                var success = await _meetingService.EndMeetingAsync(id);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Update, "Meeting", id);
                    TempData["Success"] = "Meeting ended successfully.";
                }
                else
                {
                    TempData["Error"] = "Failed to end meeting.";
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error ending meeting.";
            }

            return RedirectToAction("Details", new { id });
        }

        // POST: Meeting/AddAttendee
        [HttpPost]
        public async Task<IActionResult> AddAttendee(int meetingId, string userId, AttendeeRole role, bool isRequired)
        {
            try
            {
                var success = await _meetingService.AddAttendeeAsync(meetingId, userId, role, isRequired);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Create, "MeetingAttendee", meetingId);
                    return Json(new { success = true, message = "Attendee added successfully." });
                }

                return Json(new { success = false, message = "Failed to add attendee." });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Error adding attendee." });
            }
        }

        // POST: Meeting/RespondToInvite
        [HttpPost]
        public async Task<IActionResult> RespondToInvite(int meetingId, AttendanceStatus response)
        {
            try
            {
                var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "";
                var success = await _meetingService.RespondToMeetingInviteAsync(meetingId, userId, response);

                if (success)
                {
                    await LogAuditAsync(AuditAction.Update, "MeetingAttendee", meetingId);
                    TempData["Success"] = $"Response recorded: {response}";
                }
                else
                {
                    TempData["Error"] = "Failed to record response.";
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error recording response.";
            }

            return RedirectToAction("Details", new { id = meetingId });
        }

        private async Task PopulateDropdowns()
        {
            ViewBag.MeetingTypes = Enum.GetValues<MeetingType>()
                .Select(mt => new SelectListItem
                {
                    Value = ((int)mt).ToString(),
                    Text = mt.ToString()
                });

            ViewBag.MeetingStatuses = Enum.GetValues<MeetingStatus>()
                .Select(ms => new SelectListItem
                {
                    Value = ((int)ms).ToString(),
                    Text = ms.ToString()
                });
        }
    }
}
