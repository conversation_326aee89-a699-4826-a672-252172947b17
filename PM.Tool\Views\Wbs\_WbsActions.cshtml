<script>
    // WBS Actions JavaScript - Task actions and menu handling

    // Task action functions
    function viewTask(taskId) {
        selectedTaskId = taskId;

        $.get(`@Url.Action("GetTaskDetails", "Wbs")?taskId=${taskId}`)
            .done(function(data) {
                $('#task-details-content').html(data);
                showModal('taskDetailsModal');
            })
            .fail(function() {
                showAlert('Error loading task details', 'danger');
            });
    }

    function editTask(taskId) {
        const task = findTaskById(wbsData, taskId);
        if (task) {
            const newTitle = prompt('Edit task title:', task.title);
            if (newTitle && newTitle !== task.title) {
                // Update task title via API
                $.post('@Url.Action("UpdateTask", "Wbs")', {
                    taskId: taskId,
                    title: newTitle
                })
                .done(function(response) {
                    if (response.success) {
                        showAlert('Task updated successfully', 'success');
                        loadWbsStructure();
                        updateWbsStatistics();
                    } else {
                        showAlert(response.message || 'Error updating task', 'danger');
                    }
                })
                .fail(function() {
                    showAlert('Error updating task', 'danger');
                });
            }
        } else {
            showAlert('Task not found', 'danger');
        }
    }

    function duplicateTask(taskId) {
        $.post('@Url.Action("DuplicateTask", "Wbs")', { taskId: taskId })
        .done(function(response) {
            if (response.success) {
                showAlert('Task duplicated successfully', 'success');
                loadWbsStructure();
                updateWbsStatistics();
            } else {
                showAlert(response.message || 'Error duplicating task', 'danger');
            }
        })
        .fail(function() {
            showAlert('Error duplicating task', 'danger');
        });
    }

    function addChildTask(parentTaskId) {
        showCreateTaskModal(parentTaskId);
    }

    function deleteTask(taskId) {
        if (confirm('Are you sure you want to delete this task? This action cannot be undone.')) {
            $.post('@Url.Action("DeleteTask", "Tasks")', { id: taskId })
                .done(function(response) {
                    if (response.success) {
                        showAlert('Task deleted successfully', 'success');
                        loadWbsStructure();
                        updateWbsStatistics();
                    } else {
                        showAlert(response.message || 'Error deleting task', 'danger');
                    }
                })
                .fail(function() {
                    showAlert('Error deleting task', 'danger');
                });
        }
        hideTaskMenu(taskId);
    }

    function moveTask(taskId, direction) {
        hideTaskMenu(taskId);

        $.post('@Url.Action("MoveTask", "Wbs")', {
            taskId: taskId,
            direction: direction
        })
        .done(function(response) {
            if (response.success) {
                showAlert(`Task moved ${direction} successfully`, 'success');
                loadWbsStructure();
                updateWbsStatistics();
            } else {
                showAlert(response.message || `Failed to move task ${direction}`, 'danger');
            }
        })
        .fail(function() {
            showAlert(`Error moving task ${direction}`, 'danger');
        });
    }

    function changeTaskStatus(taskId, status) {
        hideTaskMenu(taskId);

        $.post('@Url.Action("UpdateTaskStatus", "Wbs")', {
            taskId: taskId,
            status: status
        })
        .done(function(response) {
            if (response.success) {
                showAlert(`Task status updated to ${status}`, 'success');
                loadWbsStructure();
                updateWbsStatistics();
            } else {
                showAlert(response.message || 'Failed to update task status', 'danger');
            }
        })
        .fail(function() {
            showAlert('Error updating task status', 'danger');
        });
    }

    function exportTask(taskId) {
        const projectId = window.projectId || @ViewBag.ProjectId;
        const url = `@Url.Action("ExportTask", "Wbs")?taskId=${taskId}&projectId=${projectId}`;
        window.open(url, '_blank');
        showAlert('Exporting task...', 'info');
        hideTaskMenu(taskId);
    }

    // Task menu management
    function toggleTaskMenu(taskId) {
        // Hide all other menus first and remove menu-active class
        $('.task-menu').addClass('hidden');
        $('.wbs-node').removeClass('menu-active');

        // Toggle the clicked menu
        const menu = $(`#taskMenu${taskId}`);
        const node = $(`.wbs-node[data-task-id="${taskId}"]`);

        menu.toggleClass('hidden');

        // Add menu-active class to ensure proper z-index
        if (!menu.hasClass('hidden')) {
            node.addClass('menu-active');

            // Close menu when clicking outside
            $(document).one('click', function(e) {
                if (!$(e.target).closest(`#taskMenu${taskId}, [onclick*="toggleTaskMenu(${taskId})"]`).length) {
                    menu.addClass('hidden');
                    node.removeClass('menu-active');
                }
            });
        }
    }

    function hideTaskMenu(taskId) {
        $(`#taskMenu${taskId}`).addClass('hidden');
        $(`.wbs-node[data-task-id="${taskId}"]`).removeClass('menu-active');
    }

    // Create task modal and form handling
    function showCreateTaskModal(parentTaskId = null) {
        // Prevent automatic opening during page load or before WBS is initialized
        if (!document.readyState || document.readyState !== 'complete' || !window.wbsInitialized) {
            console.warn('Preventing modal opening during page load or before WBS initialization');
            return;
        }

        // Reset form
        try {
            $('#createTaskForm')[0].reset();
        } catch (e) {
            console.warn('Error resetting form:', e);
        }

        // Set parent task ID if provided
        if (parentTaskId) {
            $('#parentTaskId').val(parentTaskId);
            const parentTask = findTaskById(wbsData, parentTaskId);
            if (parentTask) {
                $('#createTaskModalTitle').text(`Create Child Task for: ${parentTask.title}`);
            }
        } else {
            $('#parentTaskId').val('');
            $('#createTaskModalTitle').text('Create New Task');
        }

        showModal('createTaskModal');
    }

    function createTask() {
        // Validate required fields
        const title = $('#taskTitle').val();
        if (!title || title.trim() === '') {
            showAlert('Task title is required', 'danger');
            return false;
        }

        const projectId = window.projectId || @ViewBag.ProjectId;
        if (!projectId) {
            showAlert('Project ID is missing', 'danger');
            return false;
        }

        // Build the request data
        const formData = {
            title: title.trim(),
            description: $('#taskDescription').val() || '',
            priority: $('#taskPriority').val() || 'Medium',
            projectId: projectId,
            assignedToUserId: $('#taskAssignedTo').val() || null,
            parentTaskId: $('#parentTaskId').val() || null,
            startDate: $('#taskStartDate').val() || null,
            dueDate: $('#taskDueDate').val() || null,
            estimatedHours: $('#taskEstimatedHours').val() || null
        };

        // Submit the form
        $.post('@Url.Action("CreateTask", "Wbs")', formData)
            .done(function(response) {
                if (response.success) {
                    showAlert('Task created successfully', 'success');
                    hideModal('createTaskModal');
                    loadWbsStructure();
                    updateWbsStatistics();
                } else {
                    showAlert(response.message || 'Error creating task', 'danger');
                }
            })
            .fail(function(xhr) {
                console.error('Create task error:', xhr.responseText);
                if (xhr.status === 400) {
                    showAlert('Validation error. Please check your input.', 'danger');
                } else if (xhr.status === 403) {
                    showAlert('Access denied. Please refresh the page and try again.', 'danger');
                } else {
                    showAlert('Error creating task', 'danger');
                }
            });

        return false; // Prevent form submission
    }
</script>
