using PM.Tool.Core.Entities.Agile;

namespace PM.Tool.Core.Interfaces
{
    public interface IAgileService
    {
        // Epic Management
        Task<IEnumerable<Epic>> GetProjectEpicsAsync(int projectId);
        Task<Epic?> GetEpicByIdAsync(int id);
        Task<Epic> CreateEpicAsync(Epic epic);
        Task<Epic> UpdateEpicAsync(Epic epic);
        Task<bool> DeleteEpicAsync(int id);
        Task<bool> ReorderEpicsAsync(int projectId, List<int> epicIds);

        // User Story Management
        Task<IEnumerable<UserStory>> GetProjectUserStoriesAsync(int projectId);
        Task<IEnumerable<UserStory>> GetEpicUserStoriesAsync(int epicId);
        Task<IEnumerable<UserStory>> GetSprintUserStoriesAsync(int sprintId);
        Task<IEnumerable<UserStory>> GetBacklogUserStoriesAsync(int projectId);
        Task<UserStory?> GetUserStoryByIdAsync(int id);
        Task<UserStory> CreateUserStoryAsync(UserStory userStory);
        Task<UserStory> UpdateUserStoryAsync(UserStory userStory);
        Task<bool> DeleteUserStoryAsync(int id);
        Task<bool> ReorderBacklogAsync(int projectId, List<int> userStoryIds);

        // Sprint Management
        Task<IEnumerable<Sprint>> GetProjectSprintsAsync(int projectId);
        Task<Sprint?> GetActiveSprintAsync(int projectId);
        Task<Sprint?> GetSprintByIdAsync(int id);
        Task<Sprint> CreateSprintAsync(Sprint sprint);
        Task<Sprint> UpdateSprintAsync(Sprint sprint);
        Task<bool> DeleteSprintAsync(int id);
        Task<bool> StartSprintAsync(int sprintId);
        Task<bool> CompleteSprintAsync(int sprintId);

        // Kanban Board
        Task<Dictionary<int, IEnumerable<UserStory>>> GetKanbanBoardAsync(int projectId);
        Task<bool> MoveUserStoryToColumnAsync(int userStoryId, int columnId);
        Task<bool> ReorderKanbanColumnAsync(int projectId, int columnId, List<int> userStoryIds);

        // Sprint Planning
        Task<bool> AddUserStoryToSprintAsync(int userStoryId, int sprintId);
        Task<bool> RemoveUserStoryFromSprintAsync(int userStoryId);
        Task<IEnumerable<UserStory>> GetAvailableUserStoriesForSprintAsync(int projectId);
        Task<decimal> GetSprintCapacityAsync(int sprintId);

        // Agile Metrics
        Task<Dictionary<string, object>> GetSprintMetricsAsync(int sprintId);
        Task<IEnumerable<object>> GetBurndownDataAsync(int sprintId);
        Task<IEnumerable<object>> GetVelocityDataAsync(int projectId, int numberOfSprints = 6);
        Task<Dictionary<string, object>> GetAgileAnalyticsAsync(int projectId);

        // Story Point Estimation
        Task<bool> EstimateUserStoryAsync(int userStoryId, decimal storyPoints);
        Task<IEnumerable<UserStory>> GetUnestimatedUserStoriesAsync(int projectId);
        Task<decimal> GetAverageVelocityAsync(int projectId, int numberOfSprints = 3);

        // User Story Comments
        Task<IEnumerable<UserStoryComment>> GetUserStoryCommentsAsync(int userStoryId);
        Task<UserStoryComment> AddCommentAsync(int userStoryId, string userId, string content, CommentType type = CommentType.General);
        Task<bool> DeleteCommentAsync(int commentId);

        // Agile Reporting
        Task<Dictionary<string, object>> GetEpicProgressReportAsync(int projectId);
        Task<Dictionary<string, object>> GetSprintReportAsync(int sprintId);
        Task<IEnumerable<object>> GetCumulativeFlowDiagramDataAsync(int projectId, DateTime startDate, DateTime endDate);

        // Backlog Grooming
        Task<IEnumerable<UserStory>> GetUserStoriesNeedingGroomingAsync(int projectId);
        Task<bool> MarkUserStoryAsReadyAsync(int userStoryId);
        Task<IEnumerable<UserStory>> GetReadyUserStoriesAsync(int projectId);

        // Feature Management
        Task<IEnumerable<Feature>> GetProjectFeaturesAsync(int projectId);
        Task<IEnumerable<Feature>> GetEpicFeaturesAsync(int epicId);
        Task<Feature?> GetFeatureByIdAsync(int featureId);
        Task<Feature> CreateFeatureAsync(Feature feature);
        Task<Feature> UpdateFeatureAsync(Feature feature);
        Task<bool> DeleteFeatureAsync(int featureId);
        Task<bool> MoveFeatureToEpicAsync(int featureId, int epicId);
        Task<IEnumerable<Feature>> SearchFeaturesAsync(int projectId, string searchTerm);
        Task<IEnumerable<Feature>> GetFeaturesByStatusAsync(int projectId, FeatureStatus status);
        Task<IEnumerable<Feature>> GetFeaturesByPriorityAsync(int projectId, FeaturePriority priority);

        // Bug Management
        Task<IEnumerable<Bug>> GetProjectBugsAsync(int projectId);
        Task<IEnumerable<Bug>> GetUserStoryBugsAsync(int userStoryId);
        Task<IEnumerable<Bug>> GetFeatureBugsAsync(int featureId);
        Task<Bug?> GetBugByIdAsync(int bugId);
        Task<Bug> CreateBugAsync(Bug bug);
        Task<Bug> UpdateBugAsync(Bug bug);
        Task<bool> DeleteBugAsync(int bugId);
        Task<bool> AssignBugAsync(int bugId, string userId);
        Task<bool> ResolveBugAsync(int bugId, string resolution);
        Task<IEnumerable<Bug>> SearchBugsAsync(int projectId, string searchTerm);
        Task<IEnumerable<Bug>> GetBugsBySeverityAsync(int projectId, BugSeverity severity);
        Task<IEnumerable<Bug>> GetBugsByStatusAsync(int projectId, BugStatus status);

        // Bug Comments
        Task<IEnumerable<BugComment>> GetBugCommentsAsync(int bugId);
        Task<BugComment> AddBugCommentAsync(int bugId, string userId, string content, PM.Tool.Core.Entities.Agile.CommentType type = PM.Tool.Core.Entities.Agile.CommentType.General);
        Task<bool> DeleteBugCommentAsync(int commentId);

        // Test Case Management
        Task<IEnumerable<TestCase>> GetProjectTestCasesAsync(int projectId);
        Task<IEnumerable<TestCase>> GetUserStoryTestCasesAsync(int userStoryId);
        Task<IEnumerable<TestCase>> GetFeatureTestCasesAsync(int featureId);
        Task<TestCase?> GetTestCaseByIdAsync(int testCaseId);
        Task<TestCase> CreateTestCaseAsync(TestCase testCase);
        Task<TestCase> UpdateTestCaseAsync(TestCase testCase);
        Task<bool> DeleteTestCaseAsync(int testCaseId);
        Task<IEnumerable<TestCase>> SearchTestCasesAsync(int projectId, string searchTerm);
        Task<IEnumerable<TestCase>> GetTestCasesByTypeAsync(int projectId, TestCaseType type);
        Task<IEnumerable<TestCase>> GetTestCasesByStatusAsync(int projectId, TestCaseStatus status);

        // Test Execution Management
        Task<IEnumerable<TestExecution>> GetTestCaseExecutionsAsync(int testCaseId);
        Task<TestExecution> CreateTestExecutionAsync(TestExecution testExecution);
        Task<TestExecution> UpdateTestExecutionAsync(TestExecution testExecution);
        Task<bool> DeleteTestExecutionAsync(int executionId);
        Task<TestExecution?> GetLatestTestExecutionAsync(int testCaseId);

        // Enhanced Reporting & Analytics
        Task<IEnumerable<object>> GetVelocityTrendsAsync(int projectId, int numberOfSprints = 6);
        Task<IEnumerable<object>> GetSprintBurndownDataAsync(int sprintId);
        Task<IEnumerable<object>> GetReleaseBurndownDataAsync(int projectId);
        Task<Dictionary<string, object>> GetTeamPerformanceAsync(int projectId, DateTime startDate, DateTime endDate);
        Task<Dictionary<string, object>> GetFeatureProgressReportAsync(int projectId);
        Task<Dictionary<string, object>> GetBugAnalyticsAsync(int projectId);
        Task<Dictionary<string, object>> GetTestCoverageReportAsync(int projectId);
    }
}
