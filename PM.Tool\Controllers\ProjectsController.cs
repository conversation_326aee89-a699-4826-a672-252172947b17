using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Interfaces;
using PM.Tool.Models.ViewModels;

namespace PM.Tool.Controllers
{
    [Authorize]
    public class ProjectsController : Controller
    {
        private readonly IProjectService _projectService;
        private readonly ITaskService _taskService;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<ProjectsController> _logger;

        public ProjectsController(
            IProjectService projectService,
            ITaskService taskService,
            UserManager<ApplicationUser> userManager,
            ILogger<ProjectsController> logger)
        {
            _projectService = projectService;
            _taskService = taskService;
            _userManager = userManager;
            _logger = logger;
        }

        // GET: Projects
        public async Task<IActionResult> Index()
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null) return RedirectToAction("Login", "Account");

                _logger.LogInformation("Loading projects for user {UserId} ({UserEmail})", user.Id, user.Email);

                // First, let's check if there are any projects at all in the database
                var allProjects = await _projectService.GetAllProjectsAsync();
                _logger.LogInformation("Total projects in database: {ProjectCount}", allProjects.Count());

                var projects = await _projectService.GetUserProjectsAsync(user.Id);
                _logger.LogInformation("Projects found for user {UserId}: {UserProjectCount}", user.Id, projects.Count());

                // Log details about each project for debugging
                foreach (var project in allProjects.Take(5)) // Log first 5 projects
                {
                    _logger.LogInformation("Project: {ProjectId} - {ProjectName} - Status: {Status} - CreatedBy: {CreatedBy} - IsDeleted: {IsDeleted}",
                        project.Id, project.Name, project.Status, project.CreatedByUserId, project.IsDeleted);
                }

                var projectViewModels = projects.Select(p => new ProjectViewModel
                {
                    Id = p.Id,
                    Name = p.Name,
                    Description = p.Description,
                    Status = p.Status,
                    StartDate = p.StartDate,
                    EndDate = p.EndDate,
                    Budget = p.Budget,
                    ClientName = p.ClientName,
                    CreatedByUserId = p.CreatedByUserId,
                    CreatedByName = p.CreatedBy?.FullName ?? "Unknown",
                    CreatedAt = p.CreatedAt,
                    UpdatedAt = p.UpdatedAt ?? DateTime.UtcNow,
                    TotalTasks = p.TotalTasks,
                    CompletedTasks = p.CompletedTasks,
                    ProgressPercentage = p.ProgressPercentage,
                    IsOverdue = p.IsOverdue,
                    MemberCount = p.Members?.Count ?? 0
                }).ToList();

                _logger.LogInformation("Returning {ViewModelCount} project view models", projectViewModels.Count);
                return View(projectViewModels);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading projects for user");
                return View(new List<ProjectViewModel>());
            }
        }

        // GET: Projects/Details/5
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null) return RedirectToAction("Login", "Account");

                var project = await _projectService.GetProjectWithDetailsAsync(id);
                if (project == null) return NotFound();

                // Check if user has access to this project
                var userRole = await _projectService.GetUserRoleInProjectAsync(id, user.Id);
                if (userRole == null && project.CreatedByUserId != user.Id)
                {
                    return Forbid();
                }

                var projectTasks = await _taskService.GetProjectTasksAsync(id);
                var projectMembers = await _projectService.GetProjectMembersAsync(id);

                var viewModel = new ProjectDetailsViewModel
                {
                    Id = project.Id,
                    Name = project.Name,
                    Description = project.Description,
                    Status = project.Status,
                    StartDate = project.StartDate,
                    EndDate = project.EndDate,
                    Budget = project.Budget,
                    ClientName = project.ClientName,
                    CreatedByUserId = project.CreatedByUserId,
                    CreatedByName = project.CreatedBy.FullName,
                    CreatedAt = project.CreatedAt,
                    UpdatedAt = (DateTime)project.UpdatedAt,
                    TotalTasks = project.TotalTasks,
                    CompletedTasks = project.CompletedTasks,
                    ProgressPercentage = project.ProgressPercentage,
                    IsOverdue = project.IsOverdue,
                    MemberCount = project.Members.Count,
                    CurrentUserRole = userRole,
                    CanEdit = userRole == UserRole.ProjectManager || userRole == UserRole.Admin || project.CreatedByUserId == user.Id,
                    CanDelete = userRole == UserRole.Admin || project.CreatedByUserId == user.Id,
                    Members = projectMembers.Select(m => new ProjectMemberViewModel
                    {
                        Id = m.Id,
                        UserId = m.UserId,
                        UserName = m.User.UserName ?? "",
                        FullName = m.User.FullName,
                        Email = m.User.Email ?? "",
                        Role = m.Role,
                        JoinedAt = m.JoinedAt,
                        IsActive = m.IsActive
                    }).ToList(),
                    RecentTasks = projectTasks.Take(10).Select(t => new TaskSummaryViewModel
                    {
                        Id = t.Id,
                        Title = t.Title,
                        Status = t.Status,
                        Priority = t.Priority,
                        AssignedToName = t.AssignedTo?.FullName,
                        DueDate = t.DueDate,
                        IsOverdue = t.IsOverdue,
                        ProjectName = project.Name
                    }).ToList()
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading project details for project {ProjectId}", id);
                return NotFound();
            }
        }

        // GET: Projects/Create
        public async Task<IActionResult> Create()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null) return RedirectToAction("Login", "Account");

            // Get users that can be assigned as project managers
            var users = await _userManager.Users
                .Where(u => u.IsActive)
                .ToListAsync();

            var selectList = users
                .OrderBy(u => u.FullName)
                .Select(u => new SelectListItem
                {
                    Value = u.Id,
                    Text = u.FullName
                })
                .ToList();

            ViewBag.ProjectManagers = selectList;
            return View(new ProjectCreateViewModel());
        }

        // POST: Projects/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(ProjectCreateViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null) return RedirectToAction("Login", "Account");

                var project = new Project
                {
                    Name = model.Name,
                    Description = model.Description,
                    StartDate = model.StartDate,
                    EndDate = model.EndDate,
                    Budget = model.Budget,
                    ClientName = model.ClientName,
                    CreatedByUserId = user.Id,
                    ManagerId = model.ManagerId, // Set the selected manager's ID
                    Status = ProjectStatus.Planning
                };

                var createdProject = await _projectService.CreateProjectAsync(project);

                // Add creator as project manager
                await _projectService.AddMemberToProjectAsync(createdProject.Id, user.Id, UserRole.ProjectManager);

                TempData["SuccessMessage"] = "Project created successfully!";
                return RedirectToAction(nameof(Details), new { id = createdProject.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating project");
                ModelState.AddModelError("", "An error occurred while creating the project. Please try again.");
                return View(model);
            }
        }

        // GET: Projects/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null) return RedirectToAction("Login", "Account");

                var project = await _projectService.GetProjectByIdAsync(id);
                if (project == null) return NotFound();

                // Check permissions
                var userRole = await _projectService.GetUserRoleInProjectAsync(id, user.Id);
                if (userRole != UserRole.ProjectManager && userRole != UserRole.Admin && project.CreatedByUserId != user.Id)
                {
                    return Forbid();
                }

                var viewModel = new ProjectEditViewModel
                {
                    Id = project.Id,
                    Name = project.Name,
                    Description = project.Description,
                    Status = project.Status,
                    StartDate = project.StartDate,
                    EndDate = project.EndDate,
                    ActualEndDate = project.ActualEndDate,
                    Budget = project.Budget,
                    ClientName = project.ClientName
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading project for edit {ProjectId}", id);
                return NotFound();
            }
        }

        // POST: Projects/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, ProjectEditViewModel model)
        {
            if (id != model.Id)
            {
                return NotFound();
            }

            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null) return RedirectToAction("Login", "Account");

                var project = await _projectService.GetProjectByIdAsync(id);
                if (project == null) return NotFound();

                // Check permissions
                var userRole = await _projectService.GetUserRoleInProjectAsync(id, user.Id);
                if (userRole != UserRole.ProjectManager && userRole != UserRole.Admin && project.CreatedByUserId != user.Id)
                {
                    return Forbid();
                }

                // Update project properties
                project.Name = model.Name;
                project.Description = model.Description;
                project.Status = model.Status;
                project.StartDate = model.StartDate;
                project.EndDate = model.EndDate;
                project.ActualEndDate = model.ActualEndDate;
                project.Budget = model.Budget;
                project.ClientName = model.ClientName;

                await _projectService.UpdateProjectAsync(project);

                TempData["SuccessMessage"] = "Project updated successfully!";
                return RedirectToAction(nameof(Details), new { id = project.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating project {ProjectId}", id);
                ModelState.AddModelError("", "An error occurred while updating the project. Please try again.");
                return View(model);
            }
        }

        // GET: Projects/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null) return RedirectToAction("Login", "Account");

                var project = await _projectService.GetProjectByIdAsync(id);
                if (project == null) return NotFound();

                // Check permissions - only admin or creator can delete
                var userRole = await _projectService.GetUserRoleInProjectAsync(id, user.Id);
                if (userRole != UserRole.Admin && project.CreatedByUserId != user.Id)
                {
                    return Forbid();
                }

                var viewModel = new ProjectViewModel
                {
                    Id = project.Id,
                    Name = project.Name,
                    Description = project.Description,
                    Status = project.Status,
                    StartDate = project.StartDate,
                    EndDate = project.EndDate,
                    Budget = project.Budget,
                    ClientName = project.ClientName,
                    CreatedByName = project.CreatedBy.FullName,
                    CreatedAt = project.CreatedAt,
                    TotalTasks = project.TotalTasks,
                    CompletedTasks = project.CompletedTasks,
                    ProgressPercentage = project.ProgressPercentage,
                    MemberCount = project.Members.Count
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading project for delete {ProjectId}", id);
                return NotFound();
            }
        }

        // POST: Projects/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null) return RedirectToAction("Login", "Account");

                var project = await _projectService.GetProjectByIdAsync(id);
                if (project == null) return NotFound();

                // Check permissions - only admin or creator can delete
                var userRole = await _projectService.GetUserRoleInProjectAsync(id, user.Id);
                if (userRole != UserRole.Admin && project.CreatedByUserId != user.Id)
                {
                    return Forbid();
                }

                await _projectService.DeleteProjectAsync(id);

                TempData["SuccessMessage"] = "Project deleted successfully!";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting project {ProjectId}", id);
                TempData["ErrorMessage"] = "An error occurred while deleting the project. Please try again.";
                return RedirectToAction(nameof(Delete), new { id });
            }
        }

        // API: Get Projects for dropdowns
        [HttpGet]
        public async Task<IActionResult> GetProjects()
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null) return Unauthorized();

                var projects = await _projectService.GetUserProjectsAsync(user.Id);
                var projectList = projects.Select(p => new { id = p.Id, name = p.Name }).ToList();

                return Json(projectList);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading projects for dropdown");
                return Json(new List<object>());
            }
        }

        // API: Get Active Projects
        [HttpGet]
        public async Task<IActionResult> GetActiveProjects()
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null) return Unauthorized();

                var projects = await _projectService.GetUserProjectsAsync(user.Id);
                var activeProjects = projects.Where(p => p.Status == ProjectStatus.Active)
                                           .Select(p => new { id = p.Id, name = p.Name })
                                           .ToList();

                return Json(activeProjects);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading active projects");
                return Json(new List<object>());
            }
        }

        // API: Get Users for dropdowns
        [HttpGet]
        public async Task<IActionResult> GetUsers()
        {
            try
            {
                var users = await _userManager.Users
                    .Where(u => u.IsActive)
                    .OrderBy(u => u.FirstName)
                    .ThenBy(u => u.LastName)
                    .Select(u => new {
                        id = u.Id,
                        userName = u.UserName,
                        email = u.Email,
                        fullName = u.FirstName + " " + u.LastName,
                        firstName = u.FirstName,
                        lastName = u.LastName
                    })
                    .ToListAsync();

                return Json(users);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading users for dropdown");
                return Json(new List<object>());
            }
        }
    }
}
