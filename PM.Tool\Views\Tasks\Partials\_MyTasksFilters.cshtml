@model MyTasksViewModel
@using PM.Tool.Core.Enums
@using TaskStatus = PM.Tool.Core.Enums.TaskStatus

<!-- Ultra-Compact Filter Bar -->
<div class="bg-white dark:bg-dark-800 border border-neutral-200 dark:border-dark-300 rounded-lg p-3 mb-4">
    <form id="filterForm" method="get" class="space-y-3">
        <!-- Single Row Layout -->
        <div class="flex flex-wrap items-center gap-2">
            <!-- Quick Filters -->
            <div class="flex items-center gap-1">
                <span class="text-xs font-medium text-neutral-600 dark:text-dark-400 mr-1">Quick:</span>
                <button type="button" class="quick-filter-btn px-2 py-1 text-xs font-medium rounded border border-neutral-300 dark:border-dark-400 hover:bg-neutral-50 dark:hover:bg-dark-700 transition-colors" data-filter="today">
                    <i class="fas fa-calendar-day mr-1"></i>Today
                </button>
                <button type="button" class="quick-filter-btn px-2 py-1 text-xs font-medium rounded border border-neutral-300 dark:border-dark-400 hover:bg-neutral-50 dark:hover:bg-dark-700 transition-colors" data-filter="thisweek">
                    <i class="fas fa-calendar-week mr-1"></i>Week
                </button>
                <button type="button" class="quick-filter-btn px-2 py-1 text-xs font-medium rounded border border-neutral-300 dark:border-dark-400 hover:bg-neutral-50 dark:hover:bg-dark-700 transition-colors" data-filter="overdue">
                    <i class="fas fa-exclamation-triangle mr-1"></i>Overdue
                </button>
                <button type="button" class="quick-filter-btn px-2 py-1 text-xs font-medium rounded border border-neutral-300 dark:border-dark-400 hover:bg-neutral-50 dark:hover:bg-dark-700 transition-colors" data-filter="highpriority">
                    <i class="fas fa-flag mr-1"></i>High
                </button>
                <button type="button" class="quick-filter-btn px-2 py-1 text-xs font-medium rounded border border-neutral-300 dark:border-dark-400 hover:bg-neutral-50 dark:hover:bg-dark-700 transition-colors" data-filter="assigned">
                    <i class="fas fa-user mr-1"></i>Mine
                </button>
            </div>

            <!-- Separator -->
            <div class="h-4 w-px bg-neutral-300 dark:bg-dark-400 mx-1"></div>

            <!-- Main Filters -->
            <div class="flex items-center gap-2 flex-1">
                <!-- Search -->
                <input type="text"
                       name="search"
                       value="@Model.CurrentFilters.Search"
                       placeholder="Search tasks..."
                       class="w-48 px-2 py-1 text-sm border border-neutral-300 dark:border-dark-400 rounded focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-700 dark:text-dark-100">

                <!-- Status Filter -->
                <select name="status" class="w-24 px-2 py-1 text-sm border border-neutral-300 dark:border-dark-400 rounded focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-700 dark:text-dark-100">
                    <option value="">Status</option>
                    <option value="@TaskStatus.ToDo" selected="@(Model.CurrentFilters.Status == TaskStatus.ToDo)">To Do</option>
                    <option value="@TaskStatus.InProgress" selected="@(Model.CurrentFilters.Status == TaskStatus.InProgress)">In Progress</option>
                    <option value="@TaskStatus.Done" selected="@(Model.CurrentFilters.Status == TaskStatus.Done)">Done</option>
                    <option value="@TaskStatus.Cancelled" selected="@(Model.CurrentFilters.Status == TaskStatus.Cancelled)">Cancelled</option>
                </select>

                <!-- Priority Filter -->
                <select name="priority" class="w-24 px-2 py-1 text-sm border border-neutral-300 dark:border-dark-400 rounded focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-700 dark:text-dark-100">
                    <option value="">Priority</option>
                    <option value="@TaskPriority.Critical" selected="@(Model.CurrentFilters.Priority == TaskPriority.Critical)">Critical</option>
                    <option value="@TaskPriority.High" selected="@(Model.CurrentFilters.Priority == TaskPriority.High)">High</option>
                    <option value="@TaskPriority.Medium" selected="@(Model.CurrentFilters.Priority == TaskPriority.Medium)">Medium</option>
                    <option value="@TaskPriority.Low" selected="@(Model.CurrentFilters.Priority == TaskPriority.Low)">Low</option>
                </select>

                <!-- Project Filter -->
                <select name="projectId" class="w-32 px-2 py-1 text-sm border border-neutral-300 dark:border-dark-400 rounded focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-700 dark:text-dark-100">
                    <option value="">Project</option>
                    @foreach (var project in Model.Projects)
                    {
                        <option value="@project.Value" selected="@project.Selected">@project.Text</option>
                    }
                </select>

                <!-- Sort By -->
                <select name="sortBy" class="w-24 px-2 py-1 text-sm border border-neutral-300 dark:border-dark-400 rounded focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-700 dark:text-dark-100">
                    <option value="dueDate" selected="@(Model.CurrentFilters.SortBy == "dueDate")">Due Date</option>
                    <option value="title" selected="@(Model.CurrentFilters.SortBy == "title")">Title</option>
                    <option value="priority" selected="@(Model.CurrentFilters.SortBy == "priority")">Priority</option>
                    <option value="status" selected="@(Model.CurrentFilters.SortBy == "status")">Status</option>
                    <option value="project" selected="@(Model.CurrentFilters.SortBy == "project")">Project</option>
                </select>

                <!-- Actions -->
                <button type="submit" class="px-3 py-1 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded transition-colors">
                    <i class="fas fa-filter mr-1"></i>Apply
                </button>
                <a href="@Url.Action("MyTasks")" class="px-2 py-1 text-sm font-medium text-neutral-600 dark:text-dark-300 hover:text-neutral-800 dark:hover:text-dark-100 border border-neutral-300 dark:border-dark-400 rounded hover:bg-neutral-50 dark:hover:bg-dark-700 transition-colors">
                    <i class="fas fa-times"></i>
                </a>
            </div>
        </div>

    </form>
</div>
