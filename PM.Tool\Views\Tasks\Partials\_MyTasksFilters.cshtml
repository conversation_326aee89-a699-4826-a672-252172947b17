@model MyTasksViewModel
@using PM.Tool.Core.Enums
@using TaskStatus = PM.Tool.Core.Enums.TaskStatus

<div class="card-custom filter-sidebar">
    <div class="card-header-custom">
        <h3 class="text-lg font-semibold text-neutral-900 dark:text-white">Filters</h3>
    </div>
    <div class="card-body-custom space-y-4">
        <form id="filterForm" method="get">
            <!-- Quick Filters -->
            <div>
                <label class="form-label-custom">Quick Filters</label>
                <div class="space-y-2">
                    <button type="button" class="w-full text-left px-3 py-2 text-sm rounded-lg hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors quick-filter-btn" data-filter="today">
                        <i class="fas fa-calendar-day mr-2"></i>Due Today
                    </button>
                    <button type="button" class="w-full text-left px-3 py-2 text-sm rounded-lg hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors quick-filter-btn" data-filter="thisweek">
                        <i class="fas fa-calendar-week mr-2"></i>Due This Week
                    </button>
                    <button type="button" class="w-full text-left px-3 py-2 text-sm rounded-lg hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors quick-filter-btn" data-filter="overdue">
                        <i class="fas fa-exclamation-triangle mr-2"></i>Overdue
                    </button>
                    <button type="button" class="w-full text-left px-3 py-2 text-sm rounded-lg hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors quick-filter-btn" data-filter="highpriority">
                        <i class="fas fa-flag mr-2"></i>High Priority
                    </button>
                </div>
            </div>

            <hr class="border-neutral-200 dark:border-dark-600">

            <!-- Search -->
            @{
                ViewData["Name"] = "search";
                ViewData["Label"] = "Search Tasks";
                ViewData["Value"] = Model.CurrentFilters.Search;
                ViewData["Placeholder"] = "Search by title or description...";
                ViewData["Icon"] = "fas fa-search";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

            <!-- Status Filter -->
            <div>
                <label class="form-label-custom">Status</label>
                <select name="status" class="form-input-custom">
                    <option value="">All Statuses</option>
                    <option value="@TaskStatus.ToDo" selected="@(Model.CurrentFilters.Status == TaskStatus.ToDo)">To Do</option>
                    <option value="@TaskStatus.InProgress" selected="@(Model.CurrentFilters.Status == TaskStatus.InProgress)">In Progress</option>
                    <option value="@TaskStatus.Done" selected="@(Model.CurrentFilters.Status == TaskStatus.Done)">Completed</option>
                    <option value="@TaskStatus.Cancelled" selected="@(Model.CurrentFilters.Status == TaskStatus.Cancelled)">Cancelled</option>
                </select>
            </div>

            <!-- Priority Filter -->
            <div>
                <label class="form-label-custom">Priority</label>
                <select name="priority" class="form-input-custom">
                    <option value="">All Priorities</option>
                    <option value="@TaskPriority.Critical" selected="@(Model.CurrentFilters.Priority == TaskPriority.Critical)">Critical</option>
                    <option value="@TaskPriority.High" selected="@(Model.CurrentFilters.Priority == TaskPriority.High)">High</option>
                    <option value="@TaskPriority.Medium" selected="@(Model.CurrentFilters.Priority == TaskPriority.Medium)">Medium</option>
                    <option value="@TaskPriority.Low" selected="@(Model.CurrentFilters.Priority == TaskPriority.Low)">Low</option>
                </select>
            </div>

            <!-- Project Filter -->
            <div>
                <label class="form-label-custom">Project</label>
                <select name="projectId" class="form-input-custom">
                    <option value="">All Projects</option>
                    @foreach (var project in Model.Projects)
                    {
                        <option value="@project.Value" selected="@project.Selected">@project.Text</option>
                    }
                </select>
            </div>

            <!-- Date Range -->
            <div>
                <label class="form-label-custom">Due Date Range</label>
                <div class="space-y-2">
                    <input type="date" name="dueDateFrom" value="@Model.CurrentFilters.DueDateFrom?.ToString("yyyy-MM-dd")" class="form-input-custom" placeholder="From">
                    <input type="date" name="dueDateTo" value="@Model.CurrentFilters.DueDateTo?.ToString("yyyy-MM-dd")" class="form-input-custom" placeholder="To">
                </div>
            </div>

            <!-- Sort Options -->
            <div>
                <label class="form-label-custom">Sort By</label>
                <select name="sortBy" class="form-input-custom">
                    <option value="dueDate" selected="@(Model.CurrentFilters.SortBy == "dueDate")">Due Date</option>
                    <option value="title" selected="@(Model.CurrentFilters.SortBy == "title")">Title</option>
                    <option value="priority" selected="@(Model.CurrentFilters.SortBy == "priority")">Priority</option>
                    <option value="status" selected="@(Model.CurrentFilters.SortBy == "status")">Status</option>
                    <option value="project" selected="@(Model.CurrentFilters.SortBy == "project")">Project</option>
                    <option value="created" selected="@(Model.CurrentFilters.SortBy == "created")">Created Date</option>
                </select>
            </div>

            <div>
                <label class="form-label-custom">Sort Order</label>
                <select name="sortOrder" class="form-input-custom">
                    <option value="asc" selected="@(Model.CurrentFilters.SortOrder == "asc")">Ascending</option>
                    <option value="desc" selected="@(Model.CurrentFilters.SortOrder == "desc")">Descending</option>
                </select>
            </div>

            <!-- Filter Actions -->
            <div class="flex space-x-2">
                <button type="submit" class="btn-primary-custom flex-1">
                    <i class="fas fa-filter mr-2"></i>Apply Filters
                </button>
                <a href="@Url.Action("MyTasks")" class="btn-secondary-custom">
                    <i class="fas fa-times"></i>
                </a>
            </div>
        </form>
    </div>
</div>
