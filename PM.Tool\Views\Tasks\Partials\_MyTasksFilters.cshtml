@model MyTasksViewModel
@using PM.Tool.Core.Enums
@using TaskStatus = PM.Tool.Core.Enums.TaskStatus

<!-- Refined Filter Bar -->
<div class="filter-bar bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl shadow-sm mb-6">
    <form id="filterForm" method="get" class="p-4">
        <!-- Single Row Layout -->
        <div class="flex flex-wrap items-center gap-4">
            <!-- Quick Filters Section -->
            <div class="flex items-center gap-2">
                <span class="text-sm font-semibold text-neutral-700 dark:text-neutral-300 mr-2">Quick:</span>
                <div class="flex items-center gap-1">
                    <button type="button" class="quick-filter-btn px-3 py-1.5 text-xs font-medium rounded-lg border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-1" data-filter="today">
                        <i class="fas fa-calendar-day mr-1.5 text-blue-500"></i>Today
                    </button>
                    <button type="button" class="quick-filter-btn px-3 py-1.5 text-xs font-medium rounded-lg border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-1" data-filter="thisweek">
                        <i class="fas fa-calendar-week mr-1.5 text-indigo-500"></i>Week
                    </button>
                    <button type="button" class="quick-filter-btn px-3 py-1.5 text-xs font-medium rounded-lg border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-1" data-filter="overdue">
                        <i class="fas fa-exclamation-triangle mr-1.5 text-red-500"></i>Overdue
                    </button>
                    <button type="button" class="quick-filter-btn px-3 py-1.5 text-xs font-medium rounded-lg border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-1" data-filter="highpriority">
                        <i class="fas fa-flag mr-1.5 text-orange-500"></i>High
                    </button>
                    <button type="button" class="quick-filter-btn px-3 py-1.5 text-xs font-medium rounded-lg border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 focus:ring-2 focus:ring-primary-500 focus:ring-offset-1" data-filter="assigned">
                        <i class="fas fa-user mr-1.5 text-emerald-500"></i>Mine
                    </button>
                </div>
            </div>

            <!-- Separator -->
            <div class="h-6 w-px bg-neutral-300 dark:bg-neutral-600"></div>

            <!-- Main Filters Section -->
            <div class="flex items-center gap-3 flex-1">
                <!-- Search -->
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-neutral-400 dark:text-neutral-500 text-sm"></i>
                    </div>
                    <input type="text"
                           name="search"
                           value="@Model.CurrentFilters.Search"
                           placeholder="Search tasks..."
                           class="w-56 pl-10 pr-4 py-2 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 placeholder-neutral-500 dark:placeholder-neutral-400 transition-all duration-200">
                </div>

                <!-- Filter Controls -->
                <div class="flex items-center gap-2">

                    <!-- Status Filter -->
                    <select name="status" class="px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[100px]">
                        <option value="">All Status</option>
                        <option value="@TaskStatus.ToDo" selected="@(Model.CurrentFilters.Status == TaskStatus.ToDo)">To Do</option>
                        <option value="@TaskStatus.InProgress" selected="@(Model.CurrentFilters.Status == TaskStatus.InProgress)">In Progress</option>
                        <option value="@TaskStatus.Done" selected="@(Model.CurrentFilters.Status == TaskStatus.Done)">Done</option>
                        <option value="@TaskStatus.Cancelled" selected="@(Model.CurrentFilters.Status == TaskStatus.Cancelled)">Cancelled</option>
                    </select>

                    <!-- Priority Filter -->
                    <select name="priority" class="px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[100px]">
                        <option value="">All Priority</option>
                        <option value="@TaskPriority.Critical" selected="@(Model.CurrentFilters.Priority == TaskPriority.Critical)">Critical</option>
                        <option value="@TaskPriority.High" selected="@(Model.CurrentFilters.Priority == TaskPriority.High)">High</option>
                        <option value="@TaskPriority.Medium" selected="@(Model.CurrentFilters.Priority == TaskPriority.Medium)">Medium</option>
                        <option value="@TaskPriority.Low" selected="@(Model.CurrentFilters.Priority == TaskPriority.Low)">Low</option>
                    </select>

                    <!-- Project Filter -->
                    <select name="projectId" class="px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[120px]">
                        <option value="">All Projects</option>
                        @foreach (var project in Model.Projects)
                        {
                            <option value="@project.Value" selected="@project.Selected">@project.Text</option>
                        }
                    </select>

                    <!-- Sort By -->
                    <select name="sortBy" class="px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200 min-w-[100px]">
                        <option value="dueDate" selected="@(Model.CurrentFilters.SortBy == "dueDate")">Due Date</option>
                        <option value="title" selected="@(Model.CurrentFilters.SortBy == "title")">Title</option>
                        <option value="priority" selected="@(Model.CurrentFilters.SortBy == "priority")">Priority</option>
                        <option value="status" selected="@(Model.CurrentFilters.SortBy == "status")">Status</option>
                        <option value="project" selected="@(Model.CurrentFilters.SortBy == "project")">Project</option>
                    </select>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center gap-2 ml-auto">
                    <button type="submit" class="inline-flex items-center px-4 py-2 text-sm font-semibold text-white bg-primary-600 hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg shadow-sm transition-all duration-200 hover:shadow-md">
                        <i class="fas fa-filter mr-2"></i>Apply
                    </button>
                    <a href="@Url.Action("MyTasks")" class="inline-flex items-center px-3 py-2 text-sm font-medium text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg shadow-sm transition-all duration-200 hover:shadow-md">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </div>
        </div>

    </form>
</div>
