@model MyTasksViewModel
@using PM.Tool.Core.Enums
@using TaskStatus = PM.Tool.Core.Enums.TaskStatus

<!-- Compact Horizontal Filter Bar -->
<div class="bg-white dark:bg-dark-800 border border-neutral-200 dark:border-dark-300 rounded-lg p-4 mb-6">
    <form id="filterForm" method="get" class="space-y-4">
        <!-- Quick Filters Row -->
        <div class="flex flex-wrap items-center gap-2">
            <span class="text-sm font-medium text-neutral-700 dark:text-dark-300 mr-2">Quick:</span>
            <button type="button" class="quick-filter-btn px-3 py-1.5 text-xs font-medium rounded-md border border-neutral-300 dark:border-dark-400 hover:bg-neutral-50 dark:hover:bg-dark-700 transition-colors" data-filter="today">
                <i class="fas fa-calendar-day mr-1"></i>Due Today
            </button>
            <button type="button" class="quick-filter-btn px-3 py-1.5 text-xs font-medium rounded-md border border-neutral-300 dark:border-dark-400 hover:bg-neutral-50 dark:hover:bg-dark-700 transition-colors" data-filter="thisweek">
                <i class="fas fa-calendar-week mr-1"></i>This Week
            </button>
            <button type="button" class="quick-filter-btn px-3 py-1.5 text-xs font-medium rounded-md border border-neutral-300 dark:border-dark-400 hover:bg-neutral-50 dark:hover:bg-dark-700 transition-colors" data-filter="overdue">
                <i class="fas fa-exclamation-triangle mr-1"></i>Overdue
            </button>
            <button type="button" class="quick-filter-btn px-3 py-1.5 text-xs font-medium rounded-md border border-neutral-300 dark:border-dark-400 hover:bg-neutral-50 dark:hover:bg-dark-700 transition-colors" data-filter="highpriority">
                <i class="fas fa-flag mr-1"></i>High Priority
            </button>
            <button type="button" class="quick-filter-btn px-3 py-1.5 text-xs font-medium rounded-md border border-neutral-300 dark:border-dark-400 hover:bg-neutral-50 dark:hover:bg-dark-700 transition-colors" data-filter="assigned">
                <i class="fas fa-user mr-1"></i>Assigned to Me
            </button>
        </div>

        <!-- Main Filters Row -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-3 items-end">
            <!-- Search -->
            <div class="lg:col-span-2">
                <input type="text"
                       name="search"
                       value="@Model.CurrentFilters.Search"
                       placeholder="Search tasks..."
                       class="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-dark-400 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-700 dark:text-dark-100">
            </div>

            <!-- Status Filter -->
            <div>
                <select name="status" class="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-dark-400 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-700 dark:text-dark-100">
                    <option value="">All Statuses</option>
                    <option value="@TaskStatus.ToDo" selected="@(Model.CurrentFilters.Status == TaskStatus.ToDo)">To Do</option>
                    <option value="@TaskStatus.InProgress" selected="@(Model.CurrentFilters.Status == TaskStatus.InProgress)">In Progress</option>
                    <option value="@TaskStatus.Done" selected="@(Model.CurrentFilters.Status == TaskStatus.Done)">Completed</option>
                    <option value="@TaskStatus.Cancelled" selected="@(Model.CurrentFilters.Status == TaskStatus.Cancelled)">Cancelled</option>
                </select>
            </div>

            <!-- Priority Filter -->
            <div>
                <select name="priority" class="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-dark-400 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-700 dark:text-dark-100">
                    <option value="">All Priorities</option>
                    <option value="@TaskPriority.Critical" selected="@(Model.CurrentFilters.Priority == TaskPriority.Critical)">Critical</option>
                    <option value="@TaskPriority.High" selected="@(Model.CurrentFilters.Priority == TaskPriority.High)">High</option>
                    <option value="@TaskPriority.Medium" selected="@(Model.CurrentFilters.Priority == TaskPriority.Medium)">Medium</option>
                    <option value="@TaskPriority.Low" selected="@(Model.CurrentFilters.Priority == TaskPriority.Low)">Low</option>
                </select>
            </div>

            <!-- Project Filter -->
            <div>
                <select name="projectId" class="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-dark-400 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-700 dark:text-dark-100">
                    <option value="">All Projects</option>
                    @foreach (var project in Model.Projects)
                    {
                        <option value="@project.Value" selected="@project.Selected">@project.Text</option>
                    }
                </select>
            </div>

            <!-- Sort By -->
            <div>
                <select name="sortBy" class="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-dark-400 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-700 dark:text-dark-100">
                    <option value="dueDate" selected="@(Model.CurrentFilters.SortBy == "dueDate")">Due Date</option>
                    <option value="title" selected="@(Model.CurrentFilters.SortBy == "title")">Title</option>
                    <option value="priority" selected="@(Model.CurrentFilters.SortBy == "priority")">Priority</option>
                    <option value="status" selected="@(Model.CurrentFilters.SortBy == "status")">Status</option>
                    <option value="project" selected="@(Model.CurrentFilters.SortBy == "project")">Project</option>
                    <option value="created" selected="@(Model.CurrentFilters.SortBy == "created")">Created Date</option>
                </select>
            </div>

            <!-- Actions -->
            <div class="flex space-x-2">
                <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md transition-colors">
                    <i class="fas fa-filter mr-1"></i>Filter
                </button>
                <a href="@Url.Action("MyTasks")" class="px-3 py-2 text-sm font-medium text-neutral-600 dark:text-dark-300 hover:text-neutral-800 dark:hover:text-dark-100 border border-neutral-300 dark:border-dark-400 rounded-md hover:bg-neutral-50 dark:hover:bg-dark-700 transition-colors">
                    <i class="fas fa-times"></i>
                </a>
            </div>
        </div>

        <!-- Advanced Filters (Collapsible) -->
        <div id="advancedFilters" class="hidden border-t border-neutral-200 dark:border-dark-300 pt-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                <!-- Date Range -->
                <div>
                    <label class="block text-xs font-medium text-neutral-700 dark:text-dark-300 mb-1">Due Date From</label>
                    <input type="date" name="dueDateFrom" value="@Model.CurrentFilters.DueDateFrom?.ToString("yyyy-MM-dd")" class="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-dark-400 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-700 dark:text-dark-100">
                </div>
                <div>
                    <label class="block text-xs font-medium text-neutral-700 dark:text-dark-300 mb-1">Due Date To</label>
                    <input type="date" name="dueDateTo" value="@Model.CurrentFilters.DueDateTo?.ToString("yyyy-MM-dd")" class="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-dark-400 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-700 dark:text-dark-100">
                </div>
                <div>
                    <label class="block text-xs font-medium text-neutral-700 dark:text-dark-300 mb-1">Sort Order</label>
                    <select name="sortOrder" class="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-dark-400 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-700 dark:text-dark-100">
                        <option value="asc" selected="@(Model.CurrentFilters.SortOrder == "asc")">Ascending</option>
                        <option value="desc" selected="@(Model.CurrentFilters.SortOrder == "desc")">Descending</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Toggle Advanced Filters -->
        <div class="flex justify-center">
            <button type="button" id="toggleAdvanced" class="text-xs text-neutral-500 dark:text-dark-400 hover:text-neutral-700 dark:hover:text-dark-200 transition-colors">
                <i class="fas fa-chevron-down mr-1"></i>
                <span>More Filters</span>
            </button>
        </div>
    </form>
</div>
