using Microsoft.AspNetCore.Authorization;
using PM.Tool.Core.Constants;

namespace PM.Tool.Core.Authorization
{
    public static class Policies
    {
        public const string RequireAdmin = "RequireAdmin";
        public const string RequireProjectManager = "RequireProjectManager";
        public const string RequireTeamMember = "RequireTeamMember";

        public static void ConfigurePolicies(AuthorizationOptions options)
        {
            options.AddPolicy(RequireAdmin, policy =>
                policy.RequireRole(Roles.Admin));

            options.AddPolicy(RequireProjectManager, policy =>
                policy.RequireRole(Roles.Admin, Roles.ProjectManager));

            options.AddPolicy(RequireTeamMember, policy =>
                policy.RequireRole(Roles.All));
        }
    }
}
