using System.ComponentModel.DataAnnotations;
using PM.Tool.Core.Enums;

namespace PM.Tool.Core.Entities
{
    public class Meeting : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        [MaxLength(2000)]
        public string? Description { get; set; }

        public int ProjectId { get; set; }

        public MeetingType Type { get; set; } = MeetingType.General;

        private DateTime _scheduledDate;
        public DateTime ScheduledDate
        {
            get => _scheduledDate;
            set => _scheduledDate = DateTime.SpecifyKind(value, DateTimeKind.Utc);
        }

        public int DurationMinutes { get; set; } = 60;

        [MaxLength(500)]
        public string? Location { get; set; }

        [MaxLength(500)]
        public string? MeetingLink { get; set; } // For virtual meetings

        public MeetingStatus Status { get; set; } = MeetingStatus.Scheduled;

        public string OrganizerUserId { get; set; } = string.Empty;

        [MaxLength(2000)]
        public string? Agenda { get; set; }

        [MaxLength(5000)]
        public string? Minutes { get; set; }

        [MaxLength(2000)]
        public string? ActionItems { get; set; }

        private DateTime? _actualStartTime;
        public DateTime? ActualStartTime
        {
            get => _actualStartTime;
            set => _actualStartTime = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        private DateTime? _actualEndTime;
        public DateTime? ActualEndTime
        {
            get => _actualEndTime;
            set => _actualEndTime = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        public bool IsRecurring { get; set; } = false;

        [MaxLength(100)]
        public string? RecurrencePattern { get; set; } // JSON pattern for recurring meetings

        public int? ParentMeetingId { get; set; } // For recurring meetings

        [MaxLength(1000)]
        public string? CancellationReason { get; set; }

        public bool SendReminders { get; set; } = true;

        // Navigation properties
        public virtual Project Project { get; set; } = null!;
        public virtual ApplicationUser Organizer { get; set; } = null!;
        public virtual ICollection<MeetingAttendee> Attendees { get; set; } = new List<MeetingAttendee>();
        public virtual ICollection<MeetingActionItem> MeetingActionItems { get; set; } = new List<MeetingActionItem>();
        public virtual ICollection<MeetingDocument> Documents { get; set; } = new List<MeetingDocument>();

        // Computed properties
        public DateTime EndTime => ScheduledDate.AddMinutes(DurationMinutes);
        public bool IsCompleted => Status == MeetingStatus.Completed;
        public bool IsOverdue => Status == MeetingStatus.Scheduled && DateTime.UtcNow > EndTime;
        public int ActualDurationMinutes => ActualStartTime.HasValue && ActualEndTime.HasValue
            ? (int)(ActualEndTime.Value - ActualStartTime.Value).TotalMinutes
            : 0;
    }

    public class MeetingAttendee : BaseEntity
    {
        public int MeetingId { get; set; }

        public string UserId { get; set; } = string.Empty;

        public AttendeeRole Role { get; set; } = AttendeeRole.Attendee;

        public AttendanceStatus Status { get; set; } = AttendanceStatus.Invited;

        private DateTime? _responseDate;
        public DateTime? ResponseDate
        {
            get => _responseDate;
            set => _responseDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        private DateTime _invitedAt = DateTime.UtcNow;
        public DateTime InvitedAt
        {
            get => _invitedAt;
            set => _invitedAt = DateTime.SpecifyKind(value, DateTimeKind.Utc);
        }

        [MaxLength(500)]
        public string? Notes { get; set; }

        public bool IsRequired { get; set; } = true;

        // Navigation properties
        public virtual Meeting Meeting { get; set; } = null!;
        public virtual ApplicationUser User { get; set; } = null!;
    }

    public class MeetingActionItem : BaseEntity
    {
        public int MeetingId { get; set; }

        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;

        public string? AssignedToUserId { get; set; }

        private DateTime? _dueDate;
        public DateTime? DueDate
        {
            get => _dueDate;
            set => _dueDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        public ActionItemStatus Status { get; set; } = ActionItemStatus.Open;

        [MaxLength(1000)]
        public string? Notes { get; set; }

        private DateTime? _completedDate;
        public DateTime? CompletedDate
        {
            get => _completedDate;
            set => _completedDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        public int Priority { get; set; } = 3; // 1-5 scale

        // Navigation properties
        public virtual Meeting Meeting { get; set; } = null!;
        public virtual ApplicationUser? AssignedTo { get; set; }

        // Computed properties
        public bool IsOverdue => DueDate.HasValue && DateTime.UtcNow > DueDate.Value && Status != ActionItemStatus.Completed;
        public bool IsCompleted => Status == ActionItemStatus.Completed;
    }

    public class MeetingDocument : BaseEntity
    {
        public int MeetingId { get; set; }

        [Required]
        [MaxLength(255)]
        public string FileName { get; set; } = string.Empty;

        [Required]
        [MaxLength(500)]
        public string FilePath { get; set; } = string.Empty;

        [MaxLength(100)]
        public string? ContentType { get; set; }

        public long FileSize { get; set; }

        public DocumentType Type { get; set; } = DocumentType.Agenda;

        [MaxLength(500)]
        public string? Notes { get; set; }

        public string UploadedByUserId { get; set; } = string.Empty;

        // Navigation properties
        public virtual Meeting Meeting { get; set; } = null!;
        public virtual ApplicationUser UploadedBy { get; set; } = null!;
    }
}
