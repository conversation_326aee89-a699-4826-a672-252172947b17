using PM.Tool.Core.Enums;
using System.ComponentModel.DataAnnotations;

using TaskStatus = PM.Tool.Core.Enums.TaskStatus;

namespace PM.Tool.Core.Entities
{
    public class TaskEntity : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        [MaxLength(2000)]
        public string? Description { get; set; }

        public TaskStatus Status { get; set; } = TaskStatus.ToDo;

        public TaskPriority Priority { get; set; } = TaskPriority.Medium;

        public int ProjectId { get; set; }

        public string? AssignedToUserId { get; set; }

        public int? ParentTaskId { get; set; }

        // WBS Properties
        [MaxLength(50)]
        public string? WbsCode { get; set; }

        public int WbsLevel { get; set; } = 1;

        public int SortOrder { get; set; } = 0;

        private DateTime? _startDate;
        public DateTime? StartDate
        {
            get => _startDate;
            set => _startDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        private DateTime? _dueDate;
        public DateTime? DueDate
        {
            get => _dueDate;
            set => _dueDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        private DateTime? _completedDate;
        public DateTime? CompletedDate
        {
            get => _completedDate;
            set => _completedDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        public int EstimatedHours { get; set; }

        public int ActualHours { get; set; }

        public string CreatedByUserId { get; set; } = string.Empty;

        public bool IsRecurring { get; set; }
        public RecurrencePattern? RecurrencePattern { get; set; }

        private DateTime? _recurrenceEndDate;
        public DateTime? RecurrenceEndDate
        {
            get => _recurrenceEndDate;
            set => _recurrenceEndDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        public int? TemplateId { get; set; }
        public bool IsTemplate { get; set; }
        public ICollection<TaskDependency> Dependencies { get; set; } = new List<TaskDependency>();
        public ICollection<TimeEntry> TimeEntries { get; set; } = new List<TimeEntry>();

        // Navigation properties
        public virtual Project Project { get; set; } = null!;
        public virtual ApplicationUser? AssignedTo { get; set; }
        public virtual ApplicationUser CreatedBy { get; set; } = null!;
        public virtual TaskEntity? ParentTask { get; set; }
        public virtual ICollection<TaskEntity> SubTasks { get; set; } = new List<TaskEntity>();
        public virtual ICollection<TaskComment> Comments { get; set; } = new List<TaskComment>();
        public virtual ICollection<TaskAttachment> Attachments { get; set; } = new List<TaskAttachment>();

        // Computed properties
        public double TotalTimeSpent => TimeEntries?.Sum(te => te.Duration) ?? 0;
        public bool HasDependencies => Dependencies?.Any() ?? false;
        public bool IsOverdue => DueDate.HasValue && DateTime.UtcNow > DueDate.Value && Status != TaskStatus.Done;
        public bool IsSubTask => ParentTaskId.HasValue;
        public int SubTaskCount => SubTasks?.Count ?? 0;
        public int CompletedSubTasks => SubTasks?.Count(t => t.Status == TaskStatus.Done) ?? 0;
        public double SubTaskProgress => SubTaskCount == 0 ? 0 : (double)CompletedSubTasks / SubTaskCount * 100;
        public double Progress => SubTaskCount == 0 ?
            (Status == TaskStatus.Done ? 100 : Status == TaskStatus.InProgress ? 50 : 0) :
            SubTaskProgress;

        // WBS Computed Properties
        public string WbsPath => GetWbsPath();
        public bool IsRootTask => !ParentTaskId.HasValue;
        public int Depth => WbsLevel - 1;

        private string GetWbsPath()
        {
            if (string.IsNullOrEmpty(WbsCode)) return "";
            return WbsCode;
        }
    }
}
