@model PaginationViewModel

<div class="mt-6">
    <nav class="flex items-center justify-between">
        <div class="flex-1 flex justify-between sm:hidden">
            @if (Model.HasPreviousPage)
            {
                <a href="?page=@(Model.CurrentPage - 1)&@Html.Raw(Context.Request.QueryString)" class="btn-secondary-custom">Previous</a>
            }
            @if (Model.HasNextPage)
            {
                <a href="?page=@(Model.CurrentPage + 1)&@Html.Raw(Context.Request.QueryString)" class="btn-secondary-custom">Next</a>
            }
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
                <p class="text-sm text-neutral-700 dark:text-neutral-300">
                    Showing <span class="font-medium">@Model.StartItem</span> to <span class="font-medium">@Model.EndItem</span> of <span class="font-medium">@Model.TotalItems</span> tasks
                </p>
            </div>
            <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                    @if (Model.HasPreviousPage)
                    {
                        <a href="?page=@(Model.CurrentPage - 1)&@Html.Raw(Context.Request.QueryString)" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-neutral-300 dark:border-dark-600 bg-white dark:bg-dark-800 text-sm font-medium text-neutral-500 dark:text-neutral-400 hover:bg-neutral-50 dark:hover:bg-dark-700">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    }
                    
                    @foreach (var pageNum in Model.GetPageNumbers())
                    {
                        @if (pageNum == Model.CurrentPage)
                        {
                            <span class="relative inline-flex items-center px-4 py-2 border border-primary-500 bg-primary-50 dark:bg-primary-900 text-sm font-medium text-primary-600 dark:text-primary-400">
                                @pageNum
                            </span>
                        }
                        else
                        {
                            <a href="?page=@pageNum&@Html.Raw(Context.Request.QueryString)" class="relative inline-flex items-center px-4 py-2 border border-neutral-300 dark:border-dark-600 bg-white dark:bg-dark-800 text-sm font-medium text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-dark-700">
                                @pageNum
                            </a>
                        }
                    }
                    
                    @if (Model.HasNextPage)
                    {
                        <a href="?page=@(Model.CurrentPage + 1)&@Html.Raw(Context.Request.QueryString)" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-neutral-300 dark:border-dark-600 bg-white dark:bg-dark-800 text-sm font-medium text-neutral-500 dark:text-neutral-400 hover:bg-neutral-50 dark:hover:bg-dark-700">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    }
                </nav>
            </div>
        </div>
    </nav>
</div>
