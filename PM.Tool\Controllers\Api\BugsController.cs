using Microsoft.AspNetCore.Mvc;
using PM.Tool.Core.Entities.Agile;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Enums;
using PM.Tool.Models.Api;
using PM.Tool.Models.DTOs;
using System.ComponentModel.DataAnnotations;
using Asp.Versioning;

namespace PM.Tool.Controllers.Api
{
    /// <summary>
    /// API controller for managing bugs
    /// </summary>
    [ApiVersion("1.0")]
    [Tags("Bugs")]
    public class BugsController : BaseApiController
    {
        private readonly IAgileService _agileService;
        private readonly IProjectService _projectService;

        public BugsController(
            IAgileService agileService,
            IProjectService projectService,
            IAuditService auditService,
            ILogger<BugsController> logger)
            : base(auditService, logger)
        {
            _agileService = agileService;
            _projectService = projectService;
        }

        /// <summary>
        /// Get all bugs for a project
        /// </summary>
        /// <param name="projectId">Project ID</param>
        /// <param name="request">Query parameters</param>
        /// <returns>Paginated list of bugs</returns>
        [HttpGet("projects/{projectId:int}/bugs")]
        [ProducesResponseType(typeof(ApiPagedResponse<BugSummaryDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetProjectBugs(
            [FromRoute] int projectId,
            [FromQuery] BugQueryRequest request)
        {
            try
            {
                var validationResult = ValidateModelState();
                if (validationResult != null) return validationResult;

                // Check project access
                if (!await HasProjectAccessAsync(projectId, _projectService))
                {
                    return NotFound(NotFoundError("Project", projectId));
                }

                var bugs = await _agileService.GetProjectBugsAsync(projectId);

                // Apply filters
                if (request.FeatureId.HasValue)
                    bugs = bugs.Where(b => b.FeatureId == request.FeatureId.Value);

                if (request.UserStoryId.HasValue)
                    bugs = bugs.Where(b => b.UserStoryId == request.UserStoryId.Value);

                if (request.Status.HasValue)
                    bugs = bugs.Where(b => b.Status == request.Status.Value);

                if (request.Severity.HasValue)
                    bugs = bugs.Where(b => b.Severity == request.Severity.Value);

                if (request.Priority.HasValue)
                    bugs = bugs.Where(b => b.Priority == request.Priority.Value);

                if (request.IsOverdue.HasValue)
                    bugs = bugs.Where(b => b.IsOverdue == request.IsOverdue.Value);

                if (!string.IsNullOrEmpty(request.AssignedToId))
                    bugs = bugs.Where(b => b.AssignedToUserId == request.AssignedToId);

                if (!string.IsNullOrEmpty(request.Search))
                {
                    var searchTerm = request.Search.ToLower();
                    bugs = bugs.Where(b =>
                        b.Title.ToLower().Contains(searchTerm) ||
                        b.Description.ToLower().Contains(searchTerm) ||
                        b.BugKey.ToLower().Contains(searchTerm));
                }

                // Apply sorting
                bugs = request.SortBy?.ToLower() switch
                {
                    "title" => request.SortOrder == "desc" ? bugs.OrderByDescending(b => b.Title) : bugs.OrderBy(b => b.Title),
                    "status" => request.SortOrder == "desc" ? bugs.OrderByDescending(b => b.Status) : bugs.OrderBy(b => b.Status),
                    "severity" => request.SortOrder == "desc" ? bugs.OrderByDescending(b => b.Severity) : bugs.OrderBy(b => b.Severity),
                    "priority" => request.SortOrder == "desc" ? bugs.OrderByDescending(b => b.Priority) : bugs.OrderBy(b => b.Priority),
                    "created" => request.SortOrder == "desc" ? bugs.OrderByDescending(b => b.CreatedAt) : bugs.OrderBy(b => b.CreatedAt),
                    "targetdate" => request.SortOrder == "desc" ? bugs.OrderByDescending(b => b.TargetDate) : bugs.OrderBy(b => b.TargetDate),
                    _ => bugs.OrderByDescending(b => b.CreatedAt)
                };

                var totalCount = bugs.Count();
                var pagedBugs = bugs
                    .Skip((request.Page - 1) * request.PageSize)
                    .Take(request.PageSize);

                var bugDtos = pagedBugs.Select(b => new BugSummaryDto
                {
                    Id = b.Id,
                    BugKey = b.BugKey,
                    Title = b.Title,
                    Status = b.Status,
                    Severity = b.Severity,
                    Priority = b.Priority,
                    AssignedToName = b.AssignedTo?.UserName,
                    FeatureTitle = b.Feature?.Title,
                    CreatedAt = b.CreatedAt,
                    TargetDate = b.TargetDate,
                    IsOverdue = b.IsOverdue
                });

                return Ok(PagedSuccess(bugDtos, totalCount, request.Page, request.PageSize));
            }
            catch (Exception ex)
            {
                return HandleException(ex, $"GetProjectBugs for project {projectId}");
            }
        }

        /// <summary>
        /// Get a specific bug by ID
        /// </summary>
        /// <param name="id">Bug ID</param>
        /// <returns>Bug details</returns>
        [HttpGet("{id:int}")]
        [ProducesResponseType(typeof(ApiResponse<BugDetailDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetBug([FromRoute] int id)
        {
            try
            {
                var bug = await _agileService.GetBugByIdAsync(id);
                if (bug == null)
                {
                    return NotFound(NotFoundError("Bug", id));
                }

                // Check project access
                if (!await HasProjectAccessAsync(bug.ProjectId, _projectService))
                {
                    return NotFound(NotFoundError("Bug", id));
                }

                var bugDto = new BugDetailDto
                {
                    Id = bug.Id,
                    BugKey = bug.BugKey,
                    Title = bug.Title,
                    Description = bug.Description,
                    StepsToReproduce = bug.StepsToReproduce,
                    ExpectedResult = bug.ExpectedResult,
                    ActualResult = bug.ActualResult,
                    Status = bug.Status,
                    Severity = bug.Severity,
                    Priority = bug.Priority,
                    FoundInVersion = bug.FoundInVersion,
                    FixedInVersion = bug.FixedInVersion,
                    EstimatedHours = (double?)bug.EstimatedHours,
                    ActualHours = (double?)bug.ActualHours,
                    TargetDate = bug.TargetDate,
                    ResolvedDate = bug.ResolvedDate,
                    VerifiedDate = bug.VerifiedDate,
                    Tags = bug.Tags,
                    ProjectId = bug.ProjectId,
                    FeatureId = bug.FeatureId,
                    UserStoryId = bug.UserStoryId,
                    FeatureTitle = bug.Feature?.Title,
                    UserStoryTitle = bug.UserStory?.Title,
                    ReportedByName = bug.ReportedBy?.UserName,
                    AssignedToName = bug.AssignedTo?.UserName,
                    IsOverdue = bug.IsOverdue,
                    DaysOpen = (DateTime.UtcNow - bug.CreatedAt).Days,
                    CreatedAt = bug.CreatedAt,
                    UpdatedAt = bug.UpdatedAt
                };

                return Ok(Success(bugDto));
            }
            catch (Exception ex)
            {
                return HandleException(ex, $"GetBug {id}");
            }
        }

        /// <summary>
        /// Create a new bug
        /// </summary>
        /// <param name="request">Bug creation data</param>
        /// <returns>Created bug</returns>
        [HttpPost]
        [ProducesResponseType(typeof(ApiResponse<BugDetailDto>), StatusCodes.Status201Created)]
        [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> CreateBug([FromBody] CreateBugRequest request)
        {
            try
            {
                var validationResult = ValidateModelState();
                if (validationResult != null) return validationResult;

                // Check project access
                if (!await HasProjectAccessAsync(request.ProjectId, _projectService))
                {
                    return NotFound(NotFoundError("Project", request.ProjectId));
                }

                // Generate bug key
                var existingBugs = await _agileService.GetProjectBugsAsync(request.ProjectId);
                var bugKey = $"BG-{existingBugs.Count() + 1:D3}";

                var bug = new Bug
                {
                    BugKey = bugKey,
                    Title = request.Title,
                    Description = request.Description,
                    StepsToReproduce = request.StepsToReproduce,
                    ExpectedResult = request.ExpectedResult,
                    ActualResult = request.ActualResult,
                    Status = request.Status,
                    Severity = request.Severity,
                    Priority = request.Priority,
                    FoundInVersion = request.FoundInVersion,
                    EstimatedHours = request.EstimatedHours.HasValue ? (decimal)request.EstimatedHours.Value : 0,
                    TargetDate = request.TargetDate,
                    Tags = request.Tags,
                    ProjectId = request.ProjectId,
                    FeatureId = request.FeatureId,
                    UserStoryId = request.UserStoryId,
                    ReportedByUserId = CurrentUserId,
                    AssignedToUserId = request.AssignedToUserId
                };

                var createdBug = await _agileService.CreateBugAsync(bug);
                await LogAuditAsync(AuditAction.Create, "Bug", createdBug.Id);

                var bugDto = new BugDetailDto
                {
                    Id = createdBug.Id,
                    BugKey = createdBug.BugKey,
                    Title = createdBug.Title,
                    Description = createdBug.Description,
                    StepsToReproduce = createdBug.StepsToReproduce,
                    ExpectedResult = createdBug.ExpectedResult,
                    ActualResult = createdBug.ActualResult,
                    Status = createdBug.Status,
                    Severity = createdBug.Severity,
                    Priority = createdBug.Priority,
                    FoundInVersion = createdBug.FoundInVersion,
                    EstimatedHours = (double?)createdBug.EstimatedHours,
                    TargetDate = createdBug.TargetDate,
                    Tags = createdBug.Tags,
                    ProjectId = createdBug.ProjectId,
                    FeatureId = createdBug.FeatureId,
                    UserStoryId = createdBug.UserStoryId,
                    CreatedAt = createdBug.CreatedAt,
                    UpdatedAt = createdBug.UpdatedAt
                };

                return CreatedWithLocation(nameof(GetBug), new { id = createdBug.Id }, bugDto, "Bug created successfully");
            }
            catch (Exception ex)
            {
                return HandleException(ex, "CreateBug");
            }
        }

        /// <summary>
        /// Update an existing bug
        /// </summary>
        /// <param name="id">Bug ID</param>
        /// <param name="request">Bug update data</param>
        /// <returns>Updated bug</returns>
        [HttpPut("{id:int}")]
        [ProducesResponseType(typeof(ApiResponse<BugDetailDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateBug([FromRoute] int id, [FromBody] UpdateBugRequest request)
        {
            try
            {
                if (id != request.Id)
                {
                    return BadRequest(ValidationError("Route ID does not match request ID"));
                }

                var validationResult = ValidateModelState();
                if (validationResult != null) return validationResult;

                var existingBug = await _agileService.GetBugByIdAsync(id);
                if (existingBug == null)
                {
                    return NotFound(NotFoundError("Bug", id));
                }

                // Check project access
                if (!await HasProjectAccessAsync(existingBug.ProjectId, _projectService))
                {
                    return NotFound(NotFoundError("Bug", id));
                }

                // Update properties
                existingBug.Title = request.Title;
                existingBug.Description = request.Description;
                existingBug.StepsToReproduce = request.StepsToReproduce;
                existingBug.ExpectedResult = request.ExpectedResult;
                existingBug.ActualResult = request.ActualResult;
                existingBug.Status = request.Status;
                existingBug.Severity = request.Severity;
                existingBug.Priority = request.Priority;
                existingBug.FoundInVersion = request.FoundInVersion;
                existingBug.FixedInVersion = request.FixedInVersion;
                existingBug.EstimatedHours = request.EstimatedHours.HasValue ? (decimal)request.EstimatedHours.Value : 0;
                existingBug.ActualHours = request.ActualHours.HasValue ? (decimal)request.ActualHours.Value : 0;
                existingBug.TargetDate = request.TargetDate;
                existingBug.Tags = request.Tags;
                existingBug.FeatureId = request.FeatureId;
                existingBug.UserStoryId = request.UserStoryId;
                existingBug.AssignedToUserId = request.AssignedToUserId;

                // Set resolution date if status changed to resolved
                if (request.Status == BugStatus.Resolved && existingBug.ResolvedDate == null)
                {
                    existingBug.ResolvedDate = DateTime.UtcNow;
                }

                var updatedBug = await _agileService.UpdateBugAsync(existingBug);
                await LogAuditAsync(AuditAction.Update, "Bug", id);

                var bugDto = new BugDetailDto
                {
                    Id = updatedBug.Id,
                    BugKey = updatedBug.BugKey,
                    Title = updatedBug.Title,
                    Description = updatedBug.Description,
                    StepsToReproduce = updatedBug.StepsToReproduce,
                    ExpectedResult = updatedBug.ExpectedResult,
                    ActualResult = updatedBug.ActualResult,
                    Status = updatedBug.Status,
                    Severity = updatedBug.Severity,
                    Priority = updatedBug.Priority,
                    FoundInVersion = updatedBug.FoundInVersion,
                    FixedInVersion = updatedBug.FixedInVersion,
                    EstimatedHours = (double?)updatedBug.EstimatedHours,
                    ActualHours = (double?)updatedBug.ActualHours,
                    TargetDate = updatedBug.TargetDate,
                    ResolvedDate = updatedBug.ResolvedDate,
                    VerifiedDate = updatedBug.VerifiedDate,
                    Tags = updatedBug.Tags,
                    ProjectId = updatedBug.ProjectId,
                    FeatureId = updatedBug.FeatureId,
                    UserStoryId = updatedBug.UserStoryId,
                    CreatedAt = updatedBug.CreatedAt,
                    UpdatedAt = updatedBug.UpdatedAt
                };

                return Ok(Success(bugDto, "Bug updated successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex, $"UpdateBug {id}");
            }
        }

        /// <summary>
        /// Delete a bug
        /// </summary>
        /// <param name="id">Bug ID</param>
        /// <returns>Success confirmation</returns>
        [HttpDelete("{id:int}")]
        [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteBug([FromRoute] int id)
        {
            try
            {
                var bug = await _agileService.GetBugByIdAsync(id);
                if (bug == null)
                {
                    return NotFound(NotFoundError("Bug", id));
                }

                // Check project access
                if (!await HasProjectAccessAsync(bug.ProjectId, _projectService))
                {
                    return NotFound(NotFoundError("Bug", id));
                }

                await _agileService.DeleteBugAsync(id);
                await LogAuditAsync(AuditAction.Delete, "Bug", id);

                return Ok(Success("Bug deleted successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex, $"DeleteBug {id}");
            }
        }

        /// <summary>
        /// Add a comment to a bug
        /// </summary>
        /// <param name="id">Bug ID</param>
        /// <param name="request">Comment data</param>
        /// <returns>Success confirmation</returns>
        [HttpPost("{id:int}/comments")]
        [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status201Created)]
        [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status404NotFound)]
        public async Task<IActionResult> AddBugComment([FromRoute] int id, [FromBody] AddBugCommentRequest request)
        {
            try
            {
                var validationResult = ValidateModelState();
                if (validationResult != null) return validationResult;

                var bug = await _agileService.GetBugByIdAsync(id);
                if (bug == null)
                {
                    return NotFound(NotFoundError("Bug", id));
                }

                // Check project access
                if (!await HasProjectAccessAsync(bug.ProjectId, _projectService))
                {
                    return NotFound(NotFoundError("Bug", id));
                }

                await _agileService.AddBugCommentAsync(id, CurrentUserId!, request.Content, request.Type);
                await LogAuditAsync(AuditAction.Create, "BugComment", id);

                return Ok(Success("Comment added successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex, $"AddBugComment {id}");
            }
        }

        /// <summary>
        /// Get comments for a bug
        /// </summary>
        /// <param name="id">Bug ID</param>
        /// <returns>List of comments</returns>
        [HttpGet("{id:int}/comments")]
        [ProducesResponseType(typeof(ApiResponse<IEnumerable<object>>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetBugComments([FromRoute] int id)
        {
            try
            {
                var bug = await _agileService.GetBugByIdAsync(id);
                if (bug == null)
                {
                    return NotFound(NotFoundError("Bug", id));
                }

                // Check project access
                if (!await HasProjectAccessAsync(bug.ProjectId, _projectService))
                {
                    return NotFound(NotFoundError("Bug", id));
                }

                var comments = await _agileService.GetBugCommentsAsync(id);
                var commentDtos = comments.Select(c => new
                {
                    c.Id,
                    c.Content,
                    c.Type,
                    c.CreatedAt,
                    UserName = c.User?.UserName
                });

                return Ok(Success(commentDtos));
            }
            catch (Exception ex)
            {
                return HandleException(ex, $"GetBugComments {id}");
            }
        }
    }

    /// <summary>
    /// Request model for querying bugs
    /// </summary>
    public class BugQueryRequest : BaseApiRequest
    {
        /// <summary>
        /// Filter by feature ID
        /// </summary>
        public int? FeatureId { get; set; }

        /// <summary>
        /// Filter by user story ID
        /// </summary>
        public int? UserStoryId { get; set; }

        /// <summary>
        /// Filter by bug status
        /// </summary>
        public BugStatus? Status { get; set; }

        /// <summary>
        /// Filter by bug severity
        /// </summary>
        public BugSeverity? Severity { get; set; }

        /// <summary>
        /// Filter by bug priority
        /// </summary>
        public BugPriority? Priority { get; set; }

        /// <summary>
        /// Filter by overdue status
        /// </summary>
        public bool? IsOverdue { get; set; }

        /// <summary>
        /// Filter by assigned user ID
        /// </summary>
        public string? AssignedToId { get; set; }
    }

    /// <summary>
    /// Request model for creating a bug
    /// </summary>
    public class CreateBugRequest : CreateApiRequest
    {
        /// <summary>
        /// Bug title
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Bug description
        /// </summary>
        [Required]
        [StringLength(2000)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Steps to reproduce the bug
        /// </summary>
        [StringLength(2000)]
        public string? StepsToReproduce { get; set; }

        /// <summary>
        /// Expected result
        /// </summary>
        [StringLength(1000)]
        public string? ExpectedResult { get; set; }

        /// <summary>
        /// Actual result
        /// </summary>
        [StringLength(1000)]
        public string? ActualResult { get; set; }

        /// <summary>
        /// Bug status
        /// </summary>
        [Required]
        public BugStatus Status { get; set; }

        /// <summary>
        /// Bug severity
        /// </summary>
        [Required]
        public BugSeverity Severity { get; set; }

        /// <summary>
        /// Bug priority
        /// </summary>
        [Required]
        public BugPriority Priority { get; set; }

        /// <summary>
        /// Version where bug was found
        /// </summary>
        [StringLength(50)]
        public string? FoundInVersion { get; set; }

        /// <summary>
        /// Estimated hours to fix
        /// </summary>
        [Range(0, double.MaxValue)]
        public double? EstimatedHours { get; set; }

        /// <summary>
        /// Target resolution date
        /// </summary>
        public DateTime? TargetDate { get; set; }

        /// <summary>
        /// Tags (comma-separated)
        /// </summary>
        [StringLength(500)]
        public string? Tags { get; set; }

        /// <summary>
        /// Project ID
        /// </summary>
        [Required]
        public int ProjectId { get; set; }

        /// <summary>
        /// Feature ID (optional)
        /// </summary>
        public int? FeatureId { get; set; }

        /// <summary>
        /// User story ID (optional)
        /// </summary>
        public int? UserStoryId { get; set; }

        /// <summary>
        /// Assigned user ID (optional)
        /// </summary>
        public string? AssignedToUserId { get; set; }
    }

    /// <summary>
    /// Request model for updating a bug
    /// </summary>
    public class UpdateBugRequest : UpdateApiRequest
    {
        /// <summary>
        /// Bug title
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Bug description
        /// </summary>
        [Required]
        [StringLength(2000)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Steps to reproduce the bug
        /// </summary>
        [StringLength(2000)]
        public string? StepsToReproduce { get; set; }

        /// <summary>
        /// Expected result
        /// </summary>
        [StringLength(1000)]
        public string? ExpectedResult { get; set; }

        /// <summary>
        /// Actual result
        /// </summary>
        [StringLength(1000)]
        public string? ActualResult { get; set; }

        /// <summary>
        /// Bug status
        /// </summary>
        [Required]
        public BugStatus Status { get; set; }

        /// <summary>
        /// Bug severity
        /// </summary>
        [Required]
        public BugSeverity Severity { get; set; }

        /// <summary>
        /// Bug priority
        /// </summary>
        [Required]
        public BugPriority Priority { get; set; }

        /// <summary>
        /// Version where bug was found
        /// </summary>
        [StringLength(50)]
        public string? FoundInVersion { get; set; }

        /// <summary>
        /// Version where bug was fixed
        /// </summary>
        [StringLength(50)]
        public string? FixedInVersion { get; set; }

        /// <summary>
        /// Estimated hours to fix
        /// </summary>
        [Range(0, double.MaxValue)]
        public double? EstimatedHours { get; set; }

        /// <summary>
        /// Actual hours spent fixing
        /// </summary>
        [Range(0, double.MaxValue)]
        public double? ActualHours { get; set; }

        /// <summary>
        /// Target resolution date
        /// </summary>
        public DateTime? TargetDate { get; set; }

        /// <summary>
        /// Tags (comma-separated)
        /// </summary>
        [StringLength(500)]
        public string? Tags { get; set; }

        /// <summary>
        /// Feature ID (optional)
        /// </summary>
        public int? FeatureId { get; set; }

        /// <summary>
        /// User story ID (optional)
        /// </summary>
        public int? UserStoryId { get; set; }

        /// <summary>
        /// Assigned user ID (optional)
        /// </summary>
        public string? AssignedToUserId { get; set; }
    }

    /// <summary>
    /// Request model for adding a bug comment
    /// </summary>
    public class AddBugCommentRequest
    {
        /// <summary>
        /// Comment content
        /// </summary>
        [Required]
        [StringLength(2000)]
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// Comment type
        /// </summary>
        [Required]
        public CommentType Type { get; set; }
    }
}
