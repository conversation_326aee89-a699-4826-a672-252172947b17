using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;
using MailKit.Net.Smtp;
using MimeKit;
using Microsoft.Extensions.Configuration;
using TaskStatus = PM.Tool.Core.Enums.TaskStatus;

namespace PM.Tool.Application.Services
{
    public class NotificationService : INotificationService
    {
        private readonly ApplicationDbContext _context;
        private readonly IConfiguration _configuration;
        private readonly ILogger<NotificationService> _logger;

        public NotificationService(ApplicationDbContext context, IConfiguration configuration, ILogger<NotificationService> logger)
        {
            _context = context;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<Notification> CreateNotificationAsync(string userId, string title, string message, NotificationType type, int? projectId = null, int? taskId = null)
        {
            var notification = new Notification
            {
                UserId = userId,
                Title = title,
                Message = message,
                Type = type,
                RelatedProjectId = projectId,
                RelatedTaskId = taskId,
                CreatedAt = DateTime.UtcNow
            };

            _context.Notifications.Add(notification);
            await _context.SaveChangesAsync();

            return notification;
        }

        public async Task<IEnumerable<Notification>> GetUserNotificationsAsync(string userId, bool unreadOnly = false)
        {
            var query = _context.Notifications
                .Include(n => n.RelatedProject)
                .Include(n => n.RelatedTask)
                .Where(n => n.UserId == userId);

            if (unreadOnly)
            {
                query = query.Where(n => !n.IsRead);
            }

            return await query
                .OrderByDescending(n => n.CreatedAt)
                .ToListAsync();
        }

        public async Task<bool> MarkAsReadAsync(int notificationId)
        {
            var notification = await _context.Notifications.FindAsync(notificationId);
            if (notification == null) return false;

            notification.IsRead = true;
            notification.ReadAt = DateTime.UtcNow;

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> MarkAllAsReadAsync(string userId)
        {
            var notifications = await _context.Notifications
                .Where(n => n.UserId == userId && !n.IsRead)
                .ToListAsync();

            foreach (var notification in notifications)
            {
                notification.IsRead = true;
                notification.ReadAt = DateTime.UtcNow;
            }

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<int> GetUnreadCountAsync(string userId)
        {
            return await _context.Notifications
                .CountAsync(n => n.UserId == userId && !n.IsRead);
        }

        public async Task SendEmailNotificationAsync(string userId, string subject, string message)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null || string.IsNullOrEmpty(user.Email)) return;

                var emailSettings = _configuration.GetSection("EmailSettings");
                var smtpServer = emailSettings["SmtpServer"];
                var smtpPort = int.Parse(emailSettings["SmtpPort"] ?? "587");
                var smtpUsername = emailSettings["SmtpUsername"];
                var smtpPassword = emailSettings["SmtpPassword"];
                var fromEmail = emailSettings["FromEmail"];
                var fromName = emailSettings["FromName"];

                if (string.IsNullOrEmpty(smtpServer) || string.IsNullOrEmpty(smtpUsername))
                {
                    _logger.LogWarning("Email settings not configured. Skipping email notification.");
                    return;
                }

                var email = new MimeMessage();
                email.From.Add(new MailboxAddress(fromName, fromEmail));
                email.To.Add(new MailboxAddress(user.FullName, user.Email));
                email.Subject = subject;
                email.Body = new TextPart("html") { Text = message };

                using var client = new SmtpClient();
                await client.ConnectAsync(smtpServer, smtpPort, MailKit.Security.SecureSocketOptions.StartTls);
                await client.AuthenticateAsync(smtpUsername, smtpPassword);
                await client.SendAsync(email);
                await client.DisconnectAsync(true);

                _logger.LogInformation("Email notification sent to {Email}", user.Email);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send email notification to user {UserId}", userId);
            }
        }

        public async Task NotifyTaskAssignedAsync(int taskId, string assignedToUserId)
        {
            var task = await _context.Tasks
                .Include(t => t.Project)
                .Include(t => t.CreatedBy)
                .FirstOrDefaultAsync(t => t.Id == taskId);

            if (task == null) return;

            var title = "Task Assigned";
            var message = $"You have been assigned to task '{task.Title}' in project '{task.Project.Name}'.";

            await CreateNotificationAsync(assignedToUserId, title, message, NotificationType.TaskAssigned, task.ProjectId, taskId);

            // Send email notification
            var emailMessage = $@"
                <h3>Task Assignment Notification</h3>
                <p>Hello,</p>
                <p>You have been assigned to a new task:</p>
                <ul>
                    <li><strong>Task:</strong> {task.Title}</li>
                    <li><strong>Project:</strong> {task.Project.Name}</li>
                    <li><strong>Assigned by:</strong> {task.CreatedBy.FullName}</li>
                    <li><strong>Due Date:</strong> {task.DueDate?.ToString("MMM dd, yyyy") ?? "Not set"}</li>
                </ul>
                <p>Please log in to the system to view the task details.</p>
            ";

            await SendEmailNotificationAsync(assignedToUserId, title, emailMessage);
        }

        public async Task NotifyTaskUpdatedAsync(int taskId)
        {
            var task = await _context.Tasks
                .Include(t => t.Project)
                .Include(t => t.AssignedTo)
                .FirstOrDefaultAsync(t => t.Id == taskId);

            if (task == null || task.AssignedTo == null) return;

            var title = "Task Updated";
            var message = $"Task '{task.Title}' in project '{task.Project.Name}' has been updated.";

            await CreateNotificationAsync(task.AssignedTo.Id, title, message, NotificationType.TaskUpdated, task.ProjectId, taskId);
        }

        public async Task NotifyTaskCompletedAsync(int taskId)
        {
            var task = await _context.Tasks
                .Include(t => t.Project)
                .Include(t => t.CreatedBy)
                .FirstOrDefaultAsync(t => t.Id == taskId);

            if (task == null) return;

            var title = "Task Completed";
            var message = $"Task '{task.Title}' in project '{task.Project.Name}' has been completed.";

            // Notify project members
            var projectMembers = await _context.ProjectMembers
                .Where(pm => pm.ProjectId == task.ProjectId && pm.IsActive)
                .Select(pm => pm.UserId)
                .ToListAsync();

            foreach (var memberId in projectMembers)
            {
                await CreateNotificationAsync(memberId, title, message, NotificationType.TaskCompleted, task.ProjectId, taskId);
            }
        }

        public async Task NotifyProjectUpdatedAsync(int projectId)
        {
            var project = await _context.Projects
                .Include(p => p.Members)
                .FirstOrDefaultAsync(p => p.Id == projectId);

            if (project == null) return;

            var title = "Project Updated";
            var message = $"Project '{project.Name}' has been updated.";

            foreach (var member in project.Members.Where(m => m.IsActive))
            {
                await CreateNotificationAsync(member.UserId, title, message, NotificationType.ProjectUpdated, projectId);
            }
        }

        public async Task NotifyCommentAddedAsync(int taskId, string commentUserId)
        {
            var task = await _context.Tasks
                .Include(t => t.Project)
                .Include(t => t.AssignedTo)
                .FirstOrDefaultAsync(t => t.Id == taskId);

            if (task == null) return;

            var commentUser = await _context.Users.FindAsync(commentUserId);
            if (commentUser == null) return;

            var title = "New Comment";
            var message = $"{commentUser.FullName} added a comment to task '{task.Title}'.";

            // Notify assigned user if different from commenter
            if (task.AssignedTo != null && task.AssignedTo.Id != commentUserId)
            {
                await CreateNotificationAsync(task.AssignedTo.Id, title, message, NotificationType.CommentAdded, task.ProjectId, taskId);
            }

            // Notify task creator if different from commenter and assignee
            if (task.CreatedByUserId != commentUserId && task.CreatedByUserId != task.AssignedToUserId)
            {
                await CreateNotificationAsync(task.CreatedByUserId, title, message, NotificationType.CommentAdded, task.ProjectId, taskId);
            }
        }

        public async Task NotifyMilestoneReachedAsync(int milestoneId)
        {
            var milestone = await _context.Milestones
                .Include(m => m.Project)
                .ThenInclude(p => p.Members)
                .FirstOrDefaultAsync(m => m.Id == milestoneId);

            if (milestone == null) return;

            var title = "Milestone Reached";
            var message = $"Milestone '{milestone.Name}' in project '{milestone.Project.Name}' has been reached.";

            foreach (var member in milestone.Project.Members.Where(m => m.IsActive))
            {
                await CreateNotificationAsync(member.UserId, title, message, NotificationType.MilestoneReached, milestone.ProjectId);
            }
        }

        public async Task NotifyDeadlineApproachingAsync()
        {
            var upcomingDeadlines = DateTime.UtcNow.AddDays(3);

            // Tasks approaching deadline
            var tasks = await _context.Tasks
                .Include(t => t.Project)
                .Include(t => t.AssignedTo)
                .Where(t => t.DueDate.HasValue && 
                           t.DueDate.Value <= upcomingDeadlines && 
                           t.DueDate.Value > DateTime.UtcNow &&
                           t.Status != TaskStatus.Done)
                .ToListAsync();

            foreach (var task in tasks)
            {
                if (task.AssignedTo != null)
                {
                    var title = "Deadline Approaching";
                    var message = $"Task '{task.Title}' is due on {task.DueDate?.ToString("MMM dd, yyyy")}.";

                    await CreateNotificationAsync(task.AssignedTo.Id, title, message, NotificationType.DeadlineApproaching, task.ProjectId, task.Id);
                }
            }

            // Projects approaching deadline
            var projects = await _context.Projects
                .Include(p => p.Members)
                .Where(p => p.EndDate.HasValue && 
                           p.EndDate.Value <= upcomingDeadlines && 
                           p.EndDate.Value > DateTime.UtcNow &&
                           p.Status != ProjectStatus.Completed)
                .ToListAsync();

            foreach (var project in projects)
            {
                var title = "Project Deadline Approaching";
                var message = $"Project '{project.Name}' is due on {project.EndDate?.ToString("MMM dd, yyyy")}.";

                foreach (var member in project.Members.Where(m => m.IsActive))
                {
                    await CreateNotificationAsync(member.UserId, title, message, NotificationType.DeadlineApproaching, project.Id);
                }
            }
        }
    }
}
