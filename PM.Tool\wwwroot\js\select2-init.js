/**
 * Select2 Initialization Script for PM Tool
 * Provides consistent Select2 setup across the application
 */

(function($) {
    'use strict';

    // Default Select2 configuration
    const defaultConfig = {
        theme: 'tailwind',
        width: '100%',
        minimumResultsForSearch: 0, // Always show search box
        allowClear: false,
        placeholder: 'Select an option...',
        language: {
            noResults: function() {
                return 'No results found';
            },
            searching: function() {
                return 'Searching...';
            },
            loadingMore: function() {
                return 'Loading more results...';
            }
        }
    };

    // Initialize Select2 with custom configurations
    function initializeSelect2() {
        if (typeof $.fn.select2 === 'undefined') {
            console.warn('Select2 is not loaded');
            return;
        }

        // Prevent multiple initializations
        if (window.Select2InitializationInProgress) {
            return;
        }
        window.Select2InitializationInProgress = true;

        // Initialize Select2 for dropdowns marked with data-select2
        $('select[data-select2="true"]:not(.select2-hidden-accessible)').each(function() {
            var $select = $(this);

            var config = $.extend({}, defaultConfig);

            // Merge with custom options if provided
            var customOptions = $select.data('select2-options');
            if (customOptions) {
                try {
                    // Handle both object and string formats
                    if (typeof customOptions === 'string') {
                        customOptions = JSON.parse(customOptions);
                    }
                    config = $.extend(config, customOptions);
                } catch (e) {
                    console.warn('Invalid Select2 options for element:', $select.attr('id'), customOptions);
                }
            }

            // Set placeholder from first option if available
            var firstOption = $select.find('option:first');
            if (firstOption.length && firstOption.val() === '') {
                config.placeholder = firstOption.text();
                config.allowClear = true;
            }

            // Initialize Select2
            try {
                $select.select2(config);

                // Handle theme changes for dark mode
                updateSelect2Theme($select);

                // Handle icon positioning
                handleIconPositioning($select);

            } catch (error) {
                console.error('Failed to initialize Select2 for element:', $select.attr('id'), error);
            }
        });

        // Initialize Select2 for elements with select2-enabled class
        $('.select2-enabled:not(.select2-hidden-accessible)').each(function() {
            var $select = $(this);

            var config = $.extend({}, defaultConfig);

            // Set placeholder from first option if available
            var firstOption = $select.find('option:first');
            if (firstOption.length && firstOption.val() === '') {
                config.placeholder = firstOption.text();
                config.allowClear = true;
            }

            // Always enable search - remove the condition that disabled it
            // All Select2 dropdowns should be searchable for better UX

            try {
                $select.select2(config);
                updateSelect2Theme($select);
                handleIconPositioning($select);
            } catch (error) {
                console.error('Failed to initialize Select2 for element:', $select.attr('id'), error);
            }
        });

        // Initialize basic Select2 for other selects
        $('select:not([data-select2="true"]):not(.select2-enabled):not(.select2-hidden-accessible):not([data-select2-exclude="true"])').each(function() {
            var $select = $(this);

            var basicConfig = $.extend({}, defaultConfig);

            // Set placeholder from first option if available
            var firstOption = $select.find('option:first');
            if (firstOption.length && firstOption.val() === '') {
                basicConfig.placeholder = firstOption.text();
                basicConfig.allowClear = true;
            }

            try {
                $select.select2(basicConfig);
                updateSelect2Theme($select);
            } catch (error) {
                console.error('Failed to initialize basic Select2 for element:', $select.attr('id'), error);
            }
        });

        // Mark initialization as complete
        window.Select2InitializationInProgress = false;
    }

    // Update Select2 theme based on current theme
    function updateSelect2Theme($select) {
        var isDark = document.documentElement.classList.contains('dark');
        var container = $select.next('.select2-container');

        if (isDark) {
            container.addClass('select2-container--dark');
        } else {
            container.removeClass('select2-container--dark');
        }
    }

    // Handle icon positioning for Select2
    function handleIconPositioning($select) {
        var iconPosition = $select.data('icon-position');
        var container = $select.next('.select2-container');

        if (iconPosition === 'left') {
            container.addClass('has-icon-left');
        } else if (iconPosition === 'right') {
            container.addClass('has-icon-right');
        }
    }

    // Reinitialize Select2 when new content is added
    function reinitializeSelect2(container) {
        container = container || document;

        // Find selects that need initialization but aren't already initialized
        $(container).find('select:not(.select2-hidden-accessible)').each(function() {
            var $select = $(this);

            // Check if this select should have Select2
            if ($select.data('select2') === 'true' ||
                $select.hasClass('select2-enabled') ||
                $select.data('select2-options')) {

                try {
                    // Initialize this specific select
                    initializeSpecificSelect($select);
                } catch (error) {
                    console.error('Failed to reinitialize Select2 for element:', $select.attr('id'), error);
                }
            }
        });
    }

    // Initialize a specific select element
    function initializeSpecificSelect($select) {
        if ($select.hasClass('select2-hidden-accessible')) {
            return; // Already initialized
        }

        var config = $.extend({}, defaultConfig);

        // Handle data-select2 elements
        if ($select.data('select2') === 'true') {
            var customOptions = $select.data('select2-options');
            if (customOptions) {
                try {
                    if (typeof customOptions === 'string') {
                        customOptions = JSON.parse(customOptions);
                    }
                    config = $.extend(config, customOptions);
                } catch (e) {
                    console.warn('Invalid Select2 options for element:', $select.attr('id'), customOptions);
                }
            }
        }

        // Set placeholder from first option if available
        var firstOption = $select.find('option:first');
        if (firstOption.length && firstOption.val() === '') {
            config.placeholder = firstOption.text();
            config.allowClear = true;
        }

        // Initialize Select2
        $select.select2(config);
        updateSelect2Theme($select);
        handleIconPositioning($select);
    }

    // Destroy and reinitialize Select2 (useful for dynamic content)
    function refreshSelect2(selector) {
        $(selector).each(function() {
            var $select = $(this);
            if ($select.hasClass('select2-hidden-accessible')) {
                $select.select2('destroy');
            }
        });
        initializeSelect2();
    }

    // Handle theme changes
    function handleThemeChange() {
        $('.select2-container').each(function() {
            var $container = $(this);
            var $select = $container.prev('select');
            updateSelect2Theme($select);
        });
    }

    // Public API
    window.Select2Manager = {
        init: initializeSelect2,
        reinitialize: reinitializeSelect2,
        refresh: refreshSelect2,
        updateTheme: handleThemeChange
    };

    // Auto-initialize when DOM is ready
    $(document).ready(function() {
        // Add a small delay to ensure all other scripts have loaded
        setTimeout(function() {
            initializeSelect2();
        }, 100);

        // Listen for theme changes
        if (window.TailwindThemeManager) {
            $(document).on('themeChanged', handleThemeChange);
        }

        // Reinitialize when new content is added via AJAX
        $(document).on('contentLoaded', function(e, container) {
            reinitializeSelect2(container);
        });
    });

    // Handle dynamic content loading
    $(document).on('DOMNodeInserted', function(e) {
        if (e.target.nodeType === 1) { // Element node
            var $target = $(e.target);
            if ($target.is('select') || $target.find('select').length) {
                setTimeout(function() {
                    reinitializeSelect2($target);
                }, 100);
            }
        }
    });

})(jQuery);

// Utility functions for manual initialization
function initSelect2(selector, options) {
    if (window.Select2Manager) {
        $(selector).attr('data-select2', 'true');
        if (options) {
            $(selector).attr('data-select2-options', JSON.stringify(options));
        }
        window.Select2Manager.init();
    }
}

function destroySelect2(selector) {
    $(selector).each(function() {
        if ($(this).hasClass('select2-hidden-accessible')) {
            $(this).select2('destroy');
        }
    });
}

function refreshSelect2(selector) {
    if (window.Select2Manager) {
        window.Select2Manager.refresh(selector);
    }
}
