﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <!-- Common UI Elements - Japanese -->
  <data name="Common.Save" xml:space="preserve">
    <value>ä¿å­˜</value>
  </data>
  <data name="Common.Cancel" xml:space="preserve">
    <value>ã‚­ãƒ£ãƒ³ã‚»ãƒ«</value>
  </data>
  <data name="Common.Delete" xml:space="preserve">
    <value>å‰Šé™¤</value>
  </data>
  <data name="Common.Edit" xml:space="preserve">
    <value>ç·¨é›†</value>
  </data>
  <data name="Common.Create" xml:space="preserve">
    <value>ä½œæˆ</value>
  </data>
  <data name="Common.Update" xml:space="preserve">
    <value>æ›´æ–°</value>
  </data>
  <data name="Common.Details" xml:space="preserve">
    <value>è©³ç´°</value>
  </data>
  <data name="Common.Back" xml:space="preserve">
    <value>æˆ»ã‚‹</value>
  </data>
  <data name="Common.Next" xml:space="preserve">
    <value>æ¬¡ã¸</value>
  </data>
  <data name="Common.Previous" xml:space="preserve">
    <value>å‰ã¸</value>
  </data>
  <data name="Common.Search" xml:space="preserve">
    <value>æ¤œç´¢</value>
  </data>
  <data name="Common.Filter" xml:space="preserve">
    <value>ãƒ•ã‚£ãƒ«ã‚¿ãƒ¼</value>
  </data>
  <data name="Common.Export" xml:space="preserve">
    <value>ã‚¨ã‚¯ã‚¹ãƒãƒ¼ãƒˆ</value>
  </data>
  <data name="Common.Import" xml:space="preserve">
    <value>ã‚¤ãƒ³ãƒãƒ¼ãƒˆ</value>
  </data>
  <data name="Common.Print" xml:space="preserve">
    <value>å°åˆ·</value>
  </data>
  <data name="Common.Download" xml:space="preserve">
    <value>ãƒ€ã‚¦ãƒ³ãƒ­ãƒ¼ãƒ‰</value>
  </data>
  <data name="Common.Upload" xml:space="preserve">
    <value>ã‚¢ãƒƒãƒ—ãƒ­ãƒ¼ãƒ‰</value>
  </data>
  <data name="Common.Yes" xml:space="preserve">
    <value>ã¯ã„</value>
  </data>
  <data name="Common.No" xml:space="preserve">
    <value>ã„ã„ãˆ</value>
  </data>
  <data name="Common.OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Common.Close" xml:space="preserve">
    <value>é–‰ã˜ã‚‹</value>
  </data>
  <data name="Common.Loading" xml:space="preserve">
    <value>èª­ã¿è¾¼ã¿ä¸­...</value>
  </data>
  <data name="Common.NoData" xml:space="preserve">
    <value>ãƒ‡ãƒ¼ã‚¿ãŒã‚ã‚Šã¾ã›ã‚“</value>
  </data>
  <data name="Common.Error" xml:space="preserve">
    <value>ã‚¨ãƒ©ãƒ¼</value>
  </data>
  <data name="Common.Success" xml:space="preserve">
    <value>æˆåŠŸ</value>
  </data>
  <data name="Common.Warning" xml:space="preserve">
    <value>è­¦å‘Š</value>
  </data>
  <data name="Common.Information" xml:space="preserve">
    <value>æƒ…å ±</value>
  </data>
  <!-- Navigation - Japanese -->
  <data name="Nav.Dashboard" xml:space="preserve">
    <value>ãƒ€ãƒƒã‚·ãƒ¥ãƒœãƒ¼ãƒ‰</value>
  </data>
  <data name="Nav.Projects" xml:space="preserve">
    <value>ãƒ—ãƒ­ã‚¸ã‚§ã‚¯ãƒˆ</value>
  </data>
  <data name="Nav.Tasks" xml:space="preserve">
    <value>ã‚¿ã‚¹ã‚¯</value>
  </data>
  <data name="Nav.Management" xml:space="preserve">
    <value>ç®¡ç†</value>
  </data>
  <data name="Nav.Analytics" xml:space="preserve">
    <value>åˆ†æž</value>
  </data>
  <data name="Nav.Resources" xml:space="preserve">
    <value>ãƒªã‚½ãƒ¼ã‚¹</value>
  </data>
  <data name="Nav.Risks" xml:space="preserve">
    <value>ãƒªã‚¹ã‚¯</value>
  </data>
  <data name="Nav.Meetings" xml:space="preserve">
    <value>ä¼šè­°</value>
  </data>
  <data name="Nav.Requirements" xml:space="preserve">
    <value>è¦ä»¶</value>
  </data>
  <data name="Nav.Backlog" xml:space="preserve">
    <value>ãƒãƒƒã‚¯ãƒ­ã‚°</value>
  </data>
  <data name="Nav.Kanban" xml:space="preserve">
    <value>ã‹ã‚“ã°ã‚“ãƒœãƒ¼ãƒ‰</value>
  </data>
  <data name="Nav.Documentation" xml:space="preserve">
    <value>ãƒ‰ã‚­ãƒ¥ãƒ¡ãƒ³ãƒˆ</value>
  </data>
  <data name="Nav.SkillsManagement" xml:space="preserve">
    <value>ã‚¹ã‚­ãƒ«ç®¡ç†</value>
  </data>
  <data name="Nav.ResourceUtilization" xml:space="preserve">
    <value>ãƒªã‚½ãƒ¼ã‚¹åˆ©ç”¨çŽ‡</value>
  </data>
  <data name="Nav.MeetingCalendar" xml:space="preserve">
    <value>ä¼šè­°ã‚«ãƒ¬ãƒ³ãƒ€ãƒ¼</value>
  </data>
  <data name="Nav.ActionItems" xml:space="preserve">
    <value>ã‚¢ã‚¯ã‚·ãƒ§ãƒ³é …ç›®</value>
  </data>
  <data name="Nav.AnalyticsDashboard" xml:space="preserve">
    <value>åˆ†æžãƒ€ãƒƒã‚·ãƒ¥ãƒœãƒ¼ãƒ‰</value>
  </data>
  <data name="Nav.AdvancedReports" xml:space="preserve">
    <value>é«˜åº¦ãªãƒ¬ãƒãƒ¼ãƒˆ</value>
  </data>
  <data name="Nav.TeamAnalytics" xml:space="preserve">
    <value>ãƒãƒ¼ãƒ åˆ†æž</value>
  </data>
  <data name="Nav.BurndownCharts" xml:space="preserve">
    <value>ãƒãƒ¼ãƒ³ãƒ€ã‚¦ãƒ³ãƒãƒ£ãƒ¼ãƒˆ</value>
  </data>
  <data name="Nav.VelocityCharts" xml:space="preserve">
    <value>ãƒ™ãƒ­ã‚·ãƒ†ã‚£ãƒãƒ£ãƒ¼ãƒˆ</value>
  </data>
  <data name="Nav.ExportData" xml:space="preserve">
    <value>ãƒ‡ãƒ¼ã‚¿ã‚¨ã‚¯ã‚¹ãƒãƒ¼ãƒˆ</value>
  </data>
  <!-- Project Management - Japanese -->
  <data name="Project.Title" xml:space="preserve">
    <value>ã‚¿ã‚¤ãƒˆãƒ«</value>
  </data>
  <data name="Project.Description" xml:space="preserve">
    <value>èª¬æ˜Ž</value>
  </data>
  <data name="Project.StartDate" xml:space="preserve">
    <value>é–‹å§‹æ—¥</value>
  </data>
  <data name="Project.EndDate" xml:space="preserve">
    <value>çµ‚äº†æ—¥</value>
  </data>
  <data name="Project.Status" xml:space="preserve">
    <value>ã‚¹ãƒ†ãƒ¼ã‚¿ã‚¹</value>
  </data>
  <data name="Project.Priority" xml:space="preserve">
    <value>å„ªå…ˆåº¦</value>
  </data>
  <data name="Project.Budget" xml:space="preserve">
    <value>äºˆç®—</value>
  </data>
  <data name="Project.Progress" xml:space="preserve">
    <value>é€²æ—</value>
  </data>
  <data name="Project.Manager" xml:space="preserve">
    <value>ãƒ—ãƒ­ã‚¸ã‚§ã‚¯ãƒˆãƒžãƒãƒ¼ã‚¸ãƒ£ãƒ¼</value>
  </data>
  <data name="Project.Team" xml:space="preserve">
    <value>ãƒãƒ¼ãƒ </value>
  </data>
  <!-- Task Management - Japanese -->
  <data name="Task.Title" xml:space="preserve">
    <value>ã‚¿ã‚¹ã‚¯ã‚¿ã‚¤ãƒˆãƒ«</value>
  </data>
  <data name="Task.Description" xml:space="preserve">
    <value>ã‚¿ã‚¹ã‚¯èª¬æ˜Ž</value>
  </data>
  <data name="Task.AssignedTo" xml:space="preserve">
    <value>æ‹…å½“è€…</value>
  </data>
  <data name="Task.DueDate" xml:space="preserve">
    <value>æœŸé™</value>
  </data>
  <data name="Task.EstimatedHours" xml:space="preserve">
    <value>è¦‹ç©æ™‚é–“</value>
  </data>
  <data name="Task.ActualHours" xml:space="preserve">
    <value>å®Ÿç¸¾æ™‚é–“</value>
  </data>
  <data name="Task.StoryPoints" xml:space="preserve">
    <value>ã‚¹ãƒˆãƒ¼ãƒªãƒ¼ãƒã‚¤ãƒ³ãƒˆ</value>
  </data>
  <!-- Agile Terms - Japanese -->
  <data name="Agile.Epic" xml:space="preserve">
    <value>ã‚¨ãƒ”ãƒƒã‚¯</value>
  </data>
  <data name="Agile.UserStory" xml:space="preserve">
    <value>ãƒ¦ãƒ¼ã‚¶ãƒ¼ã‚¹ãƒˆãƒ¼ãƒªãƒ¼</value>
  </data>
  <data name="Agile.Sprint" xml:space="preserve">
    <value>ã‚¹ãƒ—ãƒªãƒ³ãƒˆ</value>
  </data>
  <data name="Agile.Backlog" xml:space="preserve">
    <value>ãƒãƒƒã‚¯ãƒ­ã‚°</value>
  </data>
  <data name="Agile.Kanban" xml:space="preserve">
    <value>ã‹ã‚“ã°ã‚“</value>
  </data>
  <data name="Agile.Scrum" xml:space="preserve">
    <value>ã‚¹ã‚¯ãƒ©ãƒ </value>
  </data>
  <data name="Agile.Velocity" xml:space="preserve">
    <value>ãƒ™ãƒ­ã‚·ãƒ†ã‚£</value>
  </data>
  <data name="Agile.Burndown" xml:space="preserve">
    <value>ãƒãƒ¼ãƒ³ãƒ€ã‚¦ãƒ³</value>
  </data>
  <!-- Status Values - Japanese -->
  <data name="Status.Active" xml:space="preserve">
    <value>ã‚¢ã‚¯ãƒ†ã‚£ãƒ–</value>
  </data>
  <data name="Status.Inactive" xml:space="preserve">
    <value>éžã‚¢ã‚¯ãƒ†ã‚£ãƒ–</value>
  </data>
  <data name="Status.Completed" xml:space="preserve">
    <value>å®Œäº†</value>
  </data>
  <data name="Status.InProgress" xml:space="preserve">
    <value>é€²è¡Œä¸­</value>
  </data>
  <data name="Status.Pending" xml:space="preserve">
    <value>ä¿ç•™ä¸­</value>
  </data>
  <data name="Status.Cancelled" xml:space="preserve">
    <value>ã‚­ãƒ£ãƒ³ã‚»ãƒ«</value>
  </data>
  <!-- Priority Values - Japanese -->
  <data name="Priority.Critical" xml:space="preserve">
    <value>ç·Šæ€¥</value>
  </data>
  <data name="Priority.High" xml:space="preserve">
    <value>é«˜</value>
  </data>
  <data name="Priority.Medium" xml:space="preserve">
    <value>ä¸­</value>
  </data>
  <data name="Priority.Low" xml:space="preserve">
    <value>ä½Ž</value>
  </data>
  <!-- Messages - Japanese -->
  <data name="Message.SaveSuccess" xml:space="preserve">
    <value>ã‚¢ã‚¤ãƒ†ãƒ ãŒæ­£å¸¸ã«ä¿å­˜ã•ã‚Œã¾ã—ãŸ</value>
  </data>
  <data name="Message.DeleteSuccess" xml:space="preserve">
    <value>ã‚¢ã‚¤ãƒ†ãƒ ãŒæ­£å¸¸ã«å‰Šé™¤ã•ã‚Œã¾ã—ãŸ</value>
  </data>
  <data name="Message.UpdateSuccess" xml:space="preserve">
    <value>ã‚¢ã‚¤ãƒ†ãƒ ãŒæ­£å¸¸ã«æ›´æ–°ã•ã‚Œã¾ã—ãŸ</value>
  </data>
  <data name="Message.ErrorOccurred" xml:space="preserve">
    <value>ã‚¨ãƒ©ãƒ¼ãŒç™ºç”Ÿã—ã¾ã—ãŸã€‚ã‚‚ã†ä¸€åº¦ãŠè©¦ã—ãã ã•ã„ã€‚</value>
  </data>
  <data name="Message.ConfirmDelete" xml:space="preserve">
    <value>ã“ã®ã‚¢ã‚¤ãƒ†ãƒ ã‚’å‰Šé™¤ã—ã¦ã‚‚ã‚ˆã‚ã—ã„ã§ã™ã‹ï¼Ÿ</value>
  </data>
  <data name="Meeting.Title" xml:space="preserve">
    <value>[Meeting Title - ja-JP]</value>
  </data>
  <data name="Meeting.Description" xml:space="preserve">
    <value>[Description - ja-JP]</value>
  </data>
  <data name="Meeting.StartTime" xml:space="preserve">
    <value>[Start Time - ja-JP]</value>
  </data>
  <data name="Meeting.EndTime" xml:space="preserve">
    <value>[End Time - ja-JP]</value>
  </data>
  <data name="Meeting.Location" xml:space="preserve">
    <value>[Location - ja-JP]</value>
  </data>
  <data name="Meeting.Type" xml:space="preserve">
    <value>[Meeting Type - ja-JP]</value>
  </data>
  <data name="Meeting.Status" xml:space="preserve">
    <value>[Status - ja-JP]</value>
  </data>
  <data name="Meeting.Organizer" xml:space="preserve">
    <value>[Organizer - ja-JP]</value>
  </data>
  <data name="Meeting.Attendees" xml:space="preserve">
    <value>[Attendees - ja-JP]</value>
  </data>
  <data name="Meeting.ActionItems" xml:space="preserve">
    <value>[Action Items - ja-JP]</value>
  </data>
  <data name="Meeting.Documents" xml:space="preserve">
    <value>[Documents - ja-JP]</value>
  </data>
  <data name="Meeting.Minutes" xml:space="preserve">
    <value>[Meeting Minutes - ja-JP]</value>
  </data>
  <data name="Meeting.Agenda" xml:space="preserve">
    <value>[Agenda - ja-JP]</value>
  </data>
  <data name="Requirement.Title" xml:space="preserve">
    <value>[Requirement Title - ja-JP]</value>
  </data>
  <data name="Requirement.Description" xml:space="preserve">
    <value>[Description - ja-JP]</value>
  </data>
  <data name="Requirement.Type" xml:space="preserve">
    <value>[Type - ja-JP]</value>
  </data>
  <data name="Requirement.Priority" xml:space="preserve">
    <value>[Priority - ja-JP]</value>
  </data>
  <data name="Requirement.Status" xml:space="preserve">
    <value>[Status - ja-JP]</value>
  </data>
  <data name="Requirement.Source" xml:space="preserve">
    <value>[Source - ja-JP]</value>
  </data>
  <data name="Requirement.Stakeholder" xml:space="preserve">
    <value>[Stakeholder - ja-JP]</value>
  </data>
  <data name="Requirement.AcceptanceCriteria" xml:space="preserve">
    <value>[Acceptance Criteria - ja-JP]</value>
  </data>
  <data name="Requirement.BusinessValue" xml:space="preserve">
    <value>[Business Value - ja-JP]</value>
  </data>
  <data name="Requirement.Comments" xml:space="preserve">
    <value>[Comments - ja-JP]</value>
  </data>
  <data name="Requirement.Attachments" xml:space="preserve">
    <value>[Attachments - ja-JP]</value>
  </data>
  <data name="Requirement.Changes" xml:space="preserve">
    <value>[Change History - ja-JP]</value>
  </data>
  <data name="Requirement.Tasks" xml:space="preserve">
    <value>[Related Tasks - ja-JP]</value>
  </data>
  <data name="Risk.Title" xml:space="preserve">
    <value>[Risk Title - ja-JP]</value>
  </data>
  <data name="Risk.Description" xml:space="preserve">
    <value>[Description - ja-JP]</value>
  </data>
  <data name="Risk.Category" xml:space="preserve">
    <value>[Category - ja-JP]</value>
  </data>
  <data name="Risk.Probability" xml:space="preserve">
    <value>[Probability - ja-JP]</value>
  </data>
  <data name="Risk.Impact" xml:space="preserve">
    <value>[Impact - ja-JP]</value>
  </data>
  <data name="Risk.Score" xml:space="preserve">
    <value>[Risk Score - ja-JP]</value>
  </data>
  <data name="Risk.Status" xml:space="preserve">
    <value>[Status - ja-JP]</value>
  </data>
  <data name="Risk.Owner" xml:space="preserve">
    <value>[Risk Owner - ja-JP]</value>
  </data>
  <data name="Risk.MitigationPlan" xml:space="preserve">
    <value>[Mitigation Plan - ja-JP]</value>
  </data>
  <data name="Risk.MitigationActions" xml:space="preserve">
    <value>[Mitigation Actions - ja-JP]</value>
  </data>
  <data name="Risk.ContingencyPlan" xml:space="preserve">
    <value>[Contingency Plan - ja-JP]</value>
  </data>
  <data name="Resource.Name" xml:space="preserve">
    <value>[Resource Name - ja-JP]</value>
  </data>
  <data name="Resource.Type" xml:space="preserve">
    <value>[Type - ja-JP]</value>
  </data>
  <data name="Resource.Department" xml:space="preserve">
    <value>[Department - ja-JP]</value>
  </data>
  <data name="Resource.Location" xml:space="preserve">
    <value>[Location - ja-JP]</value>
  </data>
  <data name="Resource.HourlyRate" xml:space="preserve">
    <value>[Hourly Rate - ja-JP]</value>
  </data>
  <data name="Resource.Capacity" xml:space="preserve">
    <value>[Capacity - ja-JP]</value>
  </data>
  <data name="Resource.Skills" xml:space="preserve">
    <value>[Skills - ja-JP]</value>
  </data>
  <data name="Resource.Availability" xml:space="preserve">
    <value>[Availability - ja-JP]</value>
  </data>
  <data name="Resource.Utilization" xml:space="preserve">
    <value>[Utilization - ja-JP]</value>
  </data>
  <data name="Resource.Allocation" xml:space="preserve">
    <value>[Allocation - ja-JP]</value>
  </data>
  <data name="Agile.SprintPlanning" xml:space="preserve">
    <value>[Sprint Planning - ja-JP]</value>
  </data>
  <data name="Agile.SprintReview" xml:space="preserve">
    <value>[Sprint Review - ja-JP]</value>
  </data>
  <data name="Agile.SprintRetrospective" xml:space="preserve">
    <value>[Sprint Retrospective - ja-JP]</value>
  </data>
  <data name="Agile.DailyStandup" xml:space="preserve">
    <value>[Daily Standup - ja-JP]</value>
  </data>
  <data name="Agile.ProductBacklog" xml:space="preserve">
    <value>[Product Backlog - ja-JP]</value>
  </data>
  <data name="Agile.SprintBacklog" xml:space="preserve">
    <value>[Sprint Backlog - ja-JP]</value>
  </data>
  <data name="Agile.Definition" xml:space="preserve">
    <value>[Definition of Done - ja-JP]</value>
  </data>
  <data name="Agile.StoryPoints" xml:space="preserve">
    <value>[Story Points - ja-JP]</value>
  </data>
  <data name="Agile.SprintStarted" xml:space="preserve">
    <value>[Sprint started successfully - ja-JP]</value>
  </data>
  <data name="Agile.SprintCompleted" xml:space="preserve">
    <value>[Sprint completed successfully - ja-JP]</value>
  </data>
  <data name="Enum.MeetingType.General" xml:space="preserve">
    <value>[General - ja-JP]</value>
  </data>
  <data name="Enum.MeetingType.Standup" xml:space="preserve">
    <value>[Daily Standup - ja-JP]</value>
  </data>
  <data name="Enum.MeetingType.Planning" xml:space="preserve">
    <value>[Planning - ja-JP]</value>
  </data>
  <data name="Enum.MeetingType.Review" xml:space="preserve">
    <value>[Review - ja-JP]</value>
  </data>
  <data name="Enum.MeetingType.Retrospective" xml:space="preserve">
    <value>[Retrospective - ja-JP]</value>
  </data>
  <data name="Enum.MeetingType.Stakeholder" xml:space="preserve">
    <value>[Stakeholder - ja-JP]</value>
  </data>
  <data name="Enum.MeetingType.Requirements" xml:space="preserve">
    <value>[Requirements - ja-JP]</value>
  </data>
  <data name="Enum.MeetingType.Technical" xml:space="preserve">
    <value>[Technical - ja-JP]</value>
  </data>
  <data name="Enum.MeetingType.Status" xml:space="preserve">
    <value>[Status - ja-JP]</value>
  </data>
  <data name="Enum.MeetingStatus.Scheduled" xml:space="preserve">
    <value>[Scheduled - ja-JP]</value>
  </data>
  <data name="Enum.MeetingStatus.InProgress" xml:space="preserve">
    <value>[In Progress - ja-JP]</value>
  </data>
  <data name="Enum.MeetingStatus.Completed" xml:space="preserve">
    <value>[Completed - ja-JP]</value>
  </data>
  <data name="Enum.MeetingStatus.Cancelled" xml:space="preserve">
    <value>[Cancelled - ja-JP]</value>
  </data>
  <data name="Enum.MeetingStatus.Postponed" xml:space="preserve">
    <value>[Postponed - ja-JP]</value>
  </data>
  <data name="Enum.RequirementType.Functional" xml:space="preserve">
    <value>[Functional - ja-JP]</value>
  </data>
  <data name="Enum.RequirementType.NonFunctional" xml:space="preserve">
    <value>[Non-Functional - ja-JP]</value>
  </data>
  <data name="Enum.RequirementType.Business" xml:space="preserve">
    <value>[Business - ja-JP]</value>
  </data>
  <data name="Enum.RequirementType.Technical" xml:space="preserve">
    <value>[Technical - ja-JP]</value>
  </data>
  <data name="Enum.RequirementType.Performance" xml:space="preserve">
    <value>[Performance - ja-JP]</value>
  </data>
  <data name="Enum.RequirementType.Security" xml:space="preserve">
    <value>[Security - ja-JP]</value>
  </data>
  <data name="Enum.RequirementType.Usability" xml:space="preserve">
    <value>[Usability - ja-JP]</value>
  </data>
  <data name="Enum.RequirementType.Compliance" xml:space="preserve">
    <value>[Compliance - ja-JP]</value>
  </data>
  <data name="Enum.RequirementType.Integration" xml:space="preserve">
    <value>[Integration - ja-JP]</value>
  </data>
  <data name="Enum.RiskCategory.Technical" xml:space="preserve">
    <value>[Technical - ja-JP]</value>
  </data>
  <data name="Enum.RiskCategory.Schedule" xml:space="preserve">
    <value>[Schedule - ja-JP]</value>
  </data>
  <data name="Enum.RiskCategory.Budget" xml:space="preserve">
    <value>[Budget - ja-JP]</value>
  </data>
  <data name="Enum.RiskCategory.Resource" xml:space="preserve">
    <value>[Resource - ja-JP]</value>
  </data>
  <data name="Enum.RiskCategory.Quality" xml:space="preserve">
    <value>[Quality - ja-JP]</value>
  </data>
  <data name="Enum.RiskCategory.External" xml:space="preserve">
    <value>[External - ja-JP]</value>
  </data>
  <data name="Enum.RiskCategory.Organizational" xml:space="preserve">
    <value>[Organizational - ja-JP]</value>
  </data>
  <data name="Enum.ResourceType.Human" xml:space="preserve">
    <value>[Human Resource - ja-JP]</value>
  </data>
  <data name="Enum.ResourceType.Equipment" xml:space="preserve">
    <value>[Equipment - ja-JP]</value>
  </data>
  <data name="Enum.ResourceType.Material" xml:space="preserve">
    <value>[Material - ja-JP]</value>
  </data>
  <data name="Enum.ResourceType.Software" xml:space="preserve">
    <value>[Software - ja-JP]</value>
  </data>
  <data name="Enum.ResourceType.Facility" xml:space="preserve">
    <value>[Facility - ja-JP]</value>
  </data>
  <data name="WBS.ErrorLoadingStructure" xml:space="preserve">
    <value>[Error loading WBS structure - ja-JP]</value>
  </data>
  <data name="WBS.TaskCreatedSuccessfully" xml:space="preserve">
    <value>[Task created successfully - ja-JP]</value>
  </data>
  <data name="WBS.TaskDeletedSuccessfully" xml:space="preserve">
    <value>[Task deleted successfully - ja-JP]</value>
  </data>
  <data name="WBS.TaskDuplicatedSuccessfully" xml:space="preserve">
    <value>[Task duplicated successfully - ja-JP]</value>
  </data>
  <data name="WBS.TaskMovedSuccessfully" xml:space="preserve">
    <value>[Task moved {0} successfully - ja-JP]</value>
  </data>
  <data name="WBS.TaskStatusUpdated" xml:space="preserve">
    <value>[Task status updated to {0} - ja-JP]</value>
  </data>
  <data name="WBS.WbsCodesGenerated" xml:space="preserve">
    <value>[WBS codes generated successfully - ja-JP]</value>
  </data>
  <data name="WBS.ExportingTo" xml:space="preserve">
    <value>[Exporting WBS to {0}... - ja-JP]</value>
  </data>
  <data name="WBS.ExportingTask" xml:space="preserve">
    <value>[Exporting task... - ja-JP]</value>
  </data>
  <data name="WBS.PrintingWbs" xml:space="preserve">
    <value>[Opening print dialog... - ja-JP]</value>
  </data>
  <data name="WBS.CompactViewEnabled" xml:space="preserve">
    <value>[Compact view enabled - ja-JP]</value>
  </data>
  <data name="WBS.NormalViewEnabled" xml:space="preserve">
    <value>[Normal view enabled - ja-JP]</value>
  </data>
  <data name="WBS.DetailedViewEnabled" xml:space="preserve">
    <value>[Detailed view enabled - ja-JP]</value>
  </data>
  <data name="WBS.RefreshingView" xml:space="preserve">
    <value>[Refreshing WBS view... - ja-JP]</value>
  </data>
  <data name="WBS.FilteredToShow" xml:space="preserve">
    <value>[Filtered to show {0} tasks - ja-JP]</value>
  </data>
  <data name="WBS.ShowingAllTasks" xml:space="preserve">
    <value>[Showing all tasks - ja-JP]</value>
  </data>
  <data name="WBS.FilteredOverdue" xml:space="preserve">
    <value>[Filtered to show overdue tasks - ja-JP]</value>
  </data>
  <data name="WBS.TaskTitleRequired" xml:space="preserve">
    <value>[Task title is required - ja-JP]</value>
  </data>
  <data name="WBS.ProjectIdMissing" xml:space="preserve">
    <value>[Project ID is missing - ja-JP]</value>
  </data>
  <data name="WBS.ValidationError" xml:space="preserve">
    <value>[Validation error. Please check your input. - ja-JP]</value>
  </data>
  <data name="WBS.AccessDenied" xml:space="preserve">
    <value>[Access denied. Please refresh the page and try again. - ja-JP]</value>
  </data>
  <data name="WBS.RequestFormatError" xml:space="preserve">
    <value>[Request format error. Please try again. - ja-JP]</value>
  </data>
  <data name="WBS.ErrorCreatingTask" xml:space="preserve">
    <value>[Error creating task - ja-JP]</value>
  </data>
  <data name="WBS.ErrorLoadingTaskDetails" xml:space="preserve">
    <value>[Error loading task details - ja-JP]</value>
  </data>
  <data name="WBS.ErrorDeletingTask" xml:space="preserve">
    <value>[Error deleting task - ja-JP]</value>
  </data>
  <data name="WBS.ErrorDuplicatingTask" xml:space="preserve">
    <value>[Error duplicating task - ja-JP]</value>
  </data>
  <data name="WBS.ErrorMovingTask" xml:space="preserve">
    <value>[Error moving task {0} - ja-JP]</value>
  </data>
  <data name="WBS.ErrorUpdatingTaskStatus" xml:space="preserve">
    <value>[Error updating task status - ja-JP]</value>
  </data>
  <data name="WBS.ErrorGeneratingCodes" xml:space="preserve">
    <value>[Error generating WBS codes - ja-JP]</value>
  </data>
  <data name="WBS.ConfirmDeleteTask" xml:space="preserve">
    <value>[Are you sure you want to delete this task? This action cannot be undone. - ja-JP]</value>
  </data>
  <data name="WBS.ConfirmDuplicateTask" xml:space="preserve">
    <value>[Create a duplicate of this task? - ja-JP]</value>
  </data>
  <data name="WBS.ConfirmGenerateCodes" xml:space="preserve">
    <value>[This will regenerate all WBS codes. Continue? - ja-JP]</value>
  </data>
  <data name="WBS.CreateNewTask" xml:space="preserve">
    <value>[Create New Task - ja-JP]</value>
  </data>
  <data name="WBS.CreateChildTaskFor" xml:space="preserve">
    <value>[Create Child Task for: {0} - ja-JP]</value>
  </data>
  <data name="WBS.TaskNotFound" xml:space="preserve">
    <value>[Task not found - ja-JP]</value>
  </data>
  <data name="WBS.UnsupportedExportFormat" xml:space="preserve">
    <value>[Unsupported export format - ja-JP]</value>
  </data>
  <data name="WBS.GanttViewComingSoon" xml:space="preserve">
    <value>[Gantt view feature coming soon! - ja-JP]</value>
  </data>
  <data name="WBS.Action.ViewDetails" xml:space="preserve">
    <value>[View Details - ja-JP]</value>
  </data>
  <data name="WBS.Action.EditTask" xml:space="preserve">
    <value>[Edit Task - ja-JP]</value>
  </data>
  <data name="WBS.Action.Duplicate" xml:space="preserve">
    <value>[Duplicate - ja-JP]</value>
  </data>
  <data name="WBS.Action.AddChild" xml:space="preserve">
    <value>[Add Child - ja-JP]</value>
  </data>
  <data name="WBS.Action.MoreActions" xml:space="preserve">
    <value>[More Actions - ja-JP]</value>
  </data>
  <data name="WBS.Action.MoveUp" xml:space="preserve">
    <value>[Move Up - ja-JP]</value>
  </data>
  <data name="WBS.Action.MoveDown" xml:space="preserve">
    <value>[Move Down - ja-JP]</value>
  </data>
  <data name="WBS.Action.StartTask" xml:space="preserve">
    <value>[Start Task - ja-JP]</value>
  </data>
  <data name="WBS.Action.MarkInReview" xml:space="preserve">
    <value>[Mark In Review - ja-JP]</value>
  </data>
  <data name="WBS.Action.MarkComplete" xml:space="preserve">
    <value>[Mark Complete - ja-JP]</value>
  </data>
  <data name="WBS.Action.CancelTask" xml:space="preserve">
    <value>[Cancel Task - ja-JP]</value>
  </data>
  <data name="WBS.Action.ExportTask" xml:space="preserve">
    <value>[Export Task - ja-JP]</value>
  </data>
  <data name="WBS.Action.DeleteTask" xml:space="preserve">
    <value>[Delete Task - ja-JP]</value>
  </data>
  <data name="WBS.Label.Progress" xml:space="preserve">
    <value>[Progress - ja-JP]</value>
  </data>
  <data name="WBS.Label.Unassigned" xml:space="preserve">
    <value>[Unassigned - ja-JP]</value>
  </data>
  <data name="WBS.Label.Overdue" xml:space="preserve">
    <value>[Overdue - ja-JP]</value>
  </data>
  <data name="WBS.Label.Start" xml:space="preserve">
    <value>[Start - ja-JP]</value>
  </data>
  <data name="WBS.Label.Due" xml:space="preserve">
    <value>[Due - ja-JP]</value>
  </data>
  <data name="WBS.Label.GeneratedOn" xml:space="preserve">
    <value>[Generated on - ja-JP]</value>
  </data>
  <data name="WBS.Label.WorkBreakdownStructure" xml:space="preserve">
    <value>[Work Breakdown Structure - ja-JP]</value>
  </data>
</root>