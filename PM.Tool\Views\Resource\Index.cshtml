@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer Localizer
@model IEnumerable<PM.Tool.Core.Entities.Resource>
@{
    ViewData["Title"] = "Resource Management";
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Resource Management", Href = "", Icon = "fas fa-users-cog" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-users-cog mr-3 text-primary-600 dark:text-primary-400"></i>
                Resource Management
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Manage team resources, allocations, and capacity planning
            </p>
        </div>
        <div class="mt-4 sm:mt-0 flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
            @{
                ViewData["Text"] = "Add Resource";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-plus";
                ViewData["Href"] = Url.Action("Create");
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "Schedule View";
                ViewData["Variant"] = "outline";
                ViewData["Icon"] = "fas fa-calendar";
                ViewData["Href"] = Url.Action("Schedule");
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "Capacity Planning";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-chart-bar";
                ViewData["Href"] = Url.Action("Capacity");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
    @{
        ViewData["Title"] = "Total Resources";
        ViewData["Icon"] = "fas fa-users";
        ViewData["IconColor"] = "bg-gradient-to-br from-primary-500 to-primary-600";
        ViewData["Value"] = Model.Count().ToString();
        ViewData["Description"] = "All resources in system";
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />

    @{
        ViewData["Title"] = "Active Resources";
        ViewData["Icon"] = "fas fa-user-check";
        ViewData["IconColor"] = "bg-gradient-to-br from-success-500 to-success-600";
        ViewData["Value"] = Model.Count(r => r.IsActive).ToString();
        ViewData["Description"] = "Currently available";
        ViewData["Progress"] = Model.Any() ? (decimal)Model.Count(r => r.IsActive) / Model.Count() * 100 : 0;
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />

    @{
        ViewData["Title"] = "Human Resources";
        ViewData["Icon"] = "fas fa-user-tie";
        ViewData["IconColor"] = "bg-gradient-to-br from-info-500 to-info-600";
        ViewData["Value"] = Model.Count(r => r.Type == PM.Tool.Core.Entities.ResourceType.Human).ToString();
        ViewData["Description"] = "Team members";
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />

    @{
        ViewData["Title"] = "Equipment";
        ViewData["Icon"] = "fas fa-tools";
        ViewData["IconColor"] = "bg-gradient-to-br from-warning-500 to-warning-600";
        ViewData["Value"] = Model.Count(r => r.Type == PM.Tool.Core.Entities.ResourceType.Equipment).ToString();
        ViewData["Description"] = "Equipment & tools";
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />
</div>

<!-- Filters and Search -->
@{
    ViewData["Title"] = "Filters & Search";
    ViewData["Icon"] = "fas fa-filter";
    ViewData["BodyContent"] = @"
        <div class='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4'>
            <div>
                <label for='typeFilter' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Type</label>
                <select id='typeFilter' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                    <option value=''>All Types</option>
                    <option value='Human'>Human</option>
                    <option value='Equipment'>Equipment</option>
                    <option value='Material'>Material</option>
                    <option value='Facility'>Facility</option>
                </select>
            </div>
            <div>
                <label for='statusFilter' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Status</label>
                <select id='statusFilter' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                    <option value=''>All Statuses</option>
                    <option value='true'>Active</option>
                    <option value='false'>Inactive</option>
                </select>
            </div>
            <div>
                <label for='departmentFilter' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Department</label>
                <select id='departmentFilter' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                    <option value=''>All Departments</option>
                </select>
            </div>
            <div>
                <label for='locationFilter' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Location</label>
                <select id='locationFilter' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                    <option value=''>All Locations</option>
                </select>
            </div>
            <div class='lg:col-span-2'>
                <label for='searchInput' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Search</label>
                <div class='relative'>
                    <input type='text' id='searchInput' class='w-full pl-10 pr-4 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500' placeholder='Search resources...'>
                    <div class='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                        <i class='fas fa-search text-neutral-400 dark:text-dark-500'></i>
                    </div>
                </div>
            </div>
        </div>
        <div class='mt-4 flex justify-end'>
            <button id='clearFilters' class='inline-flex items-center px-4 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg text-sm font-medium text-neutral-700 dark:text-dark-300 bg-white dark:bg-dark-800 hover:bg-neutral-50 dark:hover:bg-dark-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'>
                <i class='fas fa-times mr-2'></i>
                Clear Filters
            </button>
        </div>
    ";
}
<div class="mb-6">
    <partial name="Components/_Card" view-data="ViewData" />
</div>

<!-- Resource List -->
<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6" id="resourceContainer">
    @if (Model != null && Model.Any())
    {
        @foreach (var resource in Model)
        {
            <div class="resource-card transition-all duration-200 hover:shadow-lg"
                 data-type="@resource.Type.ToString().ToLower()"
                 data-status="@resource.IsActive.ToString().ToLower()"
                 data-department="@(resource.Department ?? "")"
                 data-location="@(resource.Location ?? "")">
                <div class="card-custom">
                    <div class="card-header-custom">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 @GetResourceTypeIconBg(resource.Type) rounded-lg flex items-center justify-center">
                                    <i class="@GetResourceTypeIcon(resource.Type) text-white text-sm"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">@resource.Name</h3>
                                    <p class="text-sm text-neutral-500 dark:text-dark-400">@resource.Type</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                @if (resource.IsActive)
                                {
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200">
                                        Active
                                    </span>
                                }
                                else
                                {
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200">
                                        Inactive
                                    </span>
                                }
                            </div>
                        </div>
                    </div>
                    <div class="card-body-custom">
                        @if (!string.IsNullOrEmpty(resource.Description))
                        {
                            <p class="text-sm text-neutral-600 dark:text-dark-400 mb-4 line-clamp-2">@resource.Description</p>
                        }

                        <!-- Resource Details -->
                        <div class="space-y-3 mb-4">
                            @if (!string.IsNullOrEmpty(resource.Department))
                            {
                                <div class="flex items-center text-sm text-neutral-600 dark:text-dark-300">
                                    <i class="fas fa-building w-4 h-4 mr-2 text-neutral-400 dark:text-dark-500"></i>
                                    <span>@resource.Department</span>
                                </div>
                            }
                            @if (!string.IsNullOrEmpty(resource.Location))
                            {
                                <div class="flex items-center text-sm text-neutral-600 dark:text-dark-300">
                                    <i class="fas fa-map-marker-alt w-4 h-4 mr-2 text-neutral-400 dark:text-dark-500"></i>
                                    <span>@resource.Location</span>
                                </div>
                            }
                            @if (resource.Type == PM.Tool.Core.Entities.ResourceType.Human)
                            {
                                <div class="flex items-center text-sm text-neutral-600 dark:text-dark-300">
                                    <i class="fas fa-clock w-4 h-4 mr-2 text-neutral-400 dark:text-dark-500"></i>
                                    <span>@resource.Capacity hrs/day</span>
                                </div>
                                @if (resource.HourlyRate > 0)
                                {
                                    <div class="flex items-center text-sm text-neutral-600 dark:text-dark-300">
                                        <i class="fas fa-dollar-sign w-4 h-4 mr-2 text-neutral-400 dark:text-dark-500"></i>
                                        <span>$@resource.HourlyRate/hr</span>
                                    </div>
                                }
                            }
                        </div>

                        <!-- Skills (for Human resources) -->
                        @if (resource.Type == PM.Tool.Core.Entities.ResourceType.Human && !string.IsNullOrEmpty(resource.Skills))
                        {
                            <div class="mb-4">
                                <h4 class="text-xs font-medium text-neutral-500 dark:text-dark-400 mb-2">Skills</h4>
                                <div class="flex flex-wrap gap-1">
                                    @{
                                        var skills = resource.Skills.Split(',', StringSplitOptions.RemoveEmptyEntries);
                                        var displaySkills = skills.Take(3);
                                        var remainingCount = skills.Length - 3;
                                    }
                                    @foreach (var skill in displaySkills)
                                    {
                                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200">
                                            @skill.Trim()
                                        </span>
                                    }
                                    @if (remainingCount > 0)
                                    {
                                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200">
                                            +@remainingCount more
                                        </span>
                                    }
                                </div>
                            </div>
                        }

                        <!-- Actions -->
                        <div class="flex items-center justify-between">
                            <small class="text-neutral-500 dark:text-dark-400">
                                Updated @(resource.UpdatedAt?.ToString("MMM dd") ?? "N/A")
                            </small>
                            <div class="flex space-x-1">
                                <a asp-action="Details" asp-route-id="@resource.Id"
                                   class="inline-flex items-center justify-center w-8 h-8 text-xs bg-neutral-100 dark:bg-dark-700 text-neutral-600 dark:text-dark-300 hover:bg-primary-100 dark:hover:bg-primary-900 hover:text-primary-600 dark:hover:text-primary-400 rounded-md transition-colors"
                                   title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a asp-action="Edit" asp-route-id="@resource.Id"
                                   class="inline-flex items-center justify-center w-8 h-8 text-xs bg-neutral-100 dark:bg-dark-700 text-neutral-600 dark:text-dark-300 hover:bg-warning-100 dark:hover:bg-warning-900 hover:text-warning-600 dark:hover:text-warning-400 rounded-md transition-colors"
                                   title="Edit Resource">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a asp-action="Allocations" asp-route-id="@resource.Id"
                                   class="inline-flex items-center justify-center w-8 h-8 text-xs bg-neutral-100 dark:bg-dark-700 text-neutral-600 dark:text-dark-300 hover:bg-info-100 dark:hover:bg-info-900 hover:text-info-600 dark:hover:text-info-400 rounded-md transition-colors"
                                   title="View Allocations">
                                    <i class="fas fa-calendar-alt"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
    }
    else
    {
        <div class="col-span-full text-center py-12">
            <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-users-cog text-2xl text-neutral-400 dark:text-dark-500"></i>
            </div>
            <h3 class="text-lg font-medium text-neutral-900 dark:text-dark-100 mb-2">No Resources Found</h3>
            <p class="text-neutral-500 dark:text-dark-400 mb-6">Start by adding team members and resources to your project.</p>
            @{
                ViewData["Text"] = "Add Resource";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-plus";
                ViewData["Href"] = Url.Action("Create");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    }
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            populateFilterOptions();
            setupFilters();
        });

        function populateFilterOptions() {
            // Populate department filter
            const departments = [...new Set($('.resource-card').map(function() {
                return $(this).data('department');
            }).get().filter(d => d))];

            const departmentSelect = $('#departmentFilter');
            departments.forEach(dept => {
                departmentSelect.append(`<option value="${dept}">${dept}</option>`);
            });

            // Populate location filter
            const locations = [...new Set($('.resource-card').map(function() {
                return $(this).data('location');
            }).get().filter(l => l))];

            const locationSelect = $('#locationFilter');
            locations.forEach(loc => {
                locationSelect.append(`<option value="${loc}">${loc}</option>`);
            });
        }

        function setupFilters() {
            $('#typeFilter, #statusFilter, #departmentFilter, #locationFilter').on('change', filterResources);
            $('#searchInput').on('input', debounce(filterResources, 300));
            $('#clearFilters').on('click', clearFilters);
        }

        function filterResources() {
            const typeFilter = $('#typeFilter').val().toLowerCase();
            const statusFilter = $('#statusFilter').val();
            const departmentFilter = $('#departmentFilter').val();
            const locationFilter = $('#locationFilter').val();
            const searchTerm = $('#searchInput').val().toLowerCase();

            $('.resource-card').each(function() {
                const card = $(this);
                const type = card.data('type');
                const status = card.data('status').toString();
                const department = card.data('department');
                const location = card.data('location');
                const name = card.find('h3').text().toLowerCase();
                const description = card.find('p').text().toLowerCase();

                let show = true;

                if (typeFilter && type !== typeFilter) show = false;
                if (statusFilter && status !== statusFilter) show = false;
                if (departmentFilter && department !== departmentFilter) show = false;
                if (locationFilter && location !== locationFilter) show = false;
                if (searchTerm && !name.includes(searchTerm) && !description.includes(searchTerm)) show = false;

                if (show) {
                    card.removeClass('hidden').addClass('block');
                } else {
                    card.removeClass('block').addClass('hidden');
                }
            });
        }

        function clearFilters() {
            $('#typeFilter, #statusFilter, #departmentFilter, #locationFilter').val('');
            $('#searchInput').val('');
            $('.resource-card').removeClass('hidden').addClass('block');
        }

        // Utility function for debouncing
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>
}

<style>
    .resource-card {
        transition: all 0.2s ease-in-out;
    }

    .resource-card:hover {
        transform: translateY(-2px);
    }

    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>

@functions {
    string GetResourceTypeIcon(PM.Tool.Core.Entities.ResourceType type)
    {
        return type switch
        {
            PM.Tool.Core.Entities.ResourceType.Human => "fas fa-user",
            PM.Tool.Core.Entities.ResourceType.Equipment => "fas fa-tools",
            PM.Tool.Core.Entities.ResourceType.Material => "fas fa-boxes",
            PM.Tool.Core.Entities.ResourceType.Facility => "fas fa-building",
            _ => "fas fa-question"
        };
    }

    string GetResourceTypeIconBg(PM.Tool.Core.Entities.ResourceType type)
    {
        return type switch
        {
            PM.Tool.Core.Entities.ResourceType.Human => "bg-gradient-to-br from-primary-500 to-primary-600",
            PM.Tool.Core.Entities.ResourceType.Equipment => "bg-gradient-to-br from-warning-500 to-warning-600",
            PM.Tool.Core.Entities.ResourceType.Material => "bg-gradient-to-br from-info-500 to-info-600",
            PM.Tool.Core.Entities.ResourceType.Facility => "bg-gradient-to-br from-success-500 to-success-600",
            _ => "bg-gradient-to-br from-neutral-500 to-neutral-600"
        };
    }
}
