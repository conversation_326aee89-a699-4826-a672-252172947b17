<script>
    $(document).ready(function() {
        // Initialize meeting management functionality
        initializeMeetingFilters();
        initializeViewToggle();
        initializeQuickFilters();
        
        // Load initial data
        updateVisibleCount();
    });

    // Filter Management
    function initializeMeetingFilters() {
        // Search input with debounce
        let searchTimeout;
        $('#searchInput').on('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                applyFilters();
            }, 300);
        });

        // Filter dropdowns
        $('#statusFilter, #typeFilter, #dateFilter').on('change', function() {
            applyFilters();
        });

        // Apply filters button
        $('#applyFilters').on('click', function() {
            applyFilters();
        });

        // Clear filters button
        $('#clearFilters').on('click', function() {
            clearAllFilters();
        });
    }

    // Quick Filter Management
    function initializeQuickFilters() {
        $('.quick-filter-btn').on('click', function() {
            const filter = $(this).data('filter');
            
            // Remove active class from all quick filter buttons
            $('.quick-filter-btn').removeClass('active');
            
            // Add active class to clicked button
            $(this).addClass('active');
            
            // Apply quick filter
            applyQuickFilter(filter);
        });
    }

    // Apply Quick Filter
    function applyQuickFilter(filter) {
        // Clear existing filters first
        $('#statusFilter, #typeFilter, #dateFilter').val('');
        $('#searchInput').val('');
        
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        
        switch(filter) {
            case 'today':
                $('#dateFilter').val('today');
                break;
            case 'upcoming':
                $('#statusFilter').val('scheduled');
                break;
            case 'active':
                $('#statusFilter').val('inprogress');
                break;
            case 'completed':
                $('#statusFilter').val('completed');
                break;
            case 'mine':
                // This would filter by current user - implementation depends on backend
                console.log('Filter by my meetings');
                break;
        }
        
        applyFilters();
    }

    // Apply All Filters
    function applyFilters() {
        const searchTerm = $('#searchInput').val().toLowerCase();
        const statusFilter = $('#statusFilter').val().toLowerCase();
        const typeFilter = $('#typeFilter').val().toLowerCase();
        const dateFilter = $('#dateFilter').val();
        
        let visibleCount = 0;
        
        // Filter both grid and list items
        $('.meeting-card, .meeting-list-item').each(function() {
            const $item = $(this);
            const itemData = $item.data();
            
            let show = true;
            
            // Search filter
            if (searchTerm) {
                const searchData = itemData.search || '';
                if (!searchData.includes(searchTerm)) {
                    show = false;
                }
            }
            
            // Status filter
            if (statusFilter && itemData.status !== statusFilter) {
                show = false;
            }
            
            // Type filter
            if (typeFilter && itemData.type !== typeFilter) {
                show = false;
            }
            
            // Date filter
            if (dateFilter) {
                const itemDate = new Date(itemData.date);
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                
                switch(dateFilter) {
                    case 'today':
                        const todayEnd = new Date(today);
                        todayEnd.setHours(23, 59, 59, 999);
                        if (itemDate < today || itemDate > todayEnd) {
                            show = false;
                        }
                        break;
                    case 'tomorrow':
                        const tomorrow = new Date(today);
                        tomorrow.setDate(tomorrow.getDate() + 1);
                        const tomorrowEnd = new Date(tomorrow);
                        tomorrowEnd.setHours(23, 59, 59, 999);
                        if (itemDate < tomorrow || itemDate > tomorrowEnd) {
                            show = false;
                        }
                        break;
                    case 'thisweek':
                        const weekEnd = new Date(today);
                        weekEnd.setDate(weekEnd.getDate() + (7 - weekEnd.getDay()));
                        if (itemDate < today || itemDate > weekEnd) {
                            show = false;
                        }
                        break;
                    case 'nextweek':
                        const nextWeekStart = new Date(today);
                        nextWeekStart.setDate(nextWeekStart.getDate() + (7 - nextWeekStart.getDay()) + 1);
                        const nextWeekEnd = new Date(nextWeekStart);
                        nextWeekEnd.setDate(nextWeekEnd.getDate() + 6);
                        if (itemDate < nextWeekStart || itemDate > nextWeekEnd) {
                            show = false;
                        }
                        break;
                    case 'thismonth':
                        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                        const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                        if (itemDate < monthStart || itemDate > monthEnd) {
                            show = false;
                        }
                        break;
                }
            }
            
            if (show) {
                $item.show().addClass('fade-in');
                visibleCount++;
            } else {
                $item.hide().removeClass('fade-in');
            }
        });
        
        updateVisibleCount(visibleCount);
    }

    // Clear All Filters
    function clearAllFilters() {
        $('#searchInput').val('');
        $('#statusFilter, #typeFilter, #dateFilter').val('');
        $('.quick-filter-btn').removeClass('active');
        
        $('.meeting-card, .meeting-list-item').show().addClass('fade-in');
        updateVisibleCount();
    }

    // View Toggle Management
    function initializeViewToggle() {
        $('.view-toggle-btn').on('click', function() {
            const view = $(this).data('view');
            
            // Update button states
            $('.view-toggle-btn').removeClass('active');
            $(this).addClass('active');
            
            // Show/hide views
            switchView(view);
        });
    }

    // Switch View
    function switchView(view) {
        $('#gridView, #listView, #calendarView').hide();
        
        switch(view) {
            case 'grid':
                $('#gridView').show();
                break;
            case 'list':
                $('#listView').show();
                break;
            case 'calendar':
                $('#calendarView').show();
                break;
        }
        
        // Update visible count after view switch
        setTimeout(() => {
            updateVisibleCount();
        }, 100);
    }

    // Update Visible Count
    function updateVisibleCount(count = null) {
        if (count === null) {
            const currentView = $('.view-toggle-btn.active').data('view');
            const selector = currentView === 'list' ? '.meeting-list-item:visible' : '.meeting-card:visible';
            count = $(selector).length;
        }
        
        $('#visibleCount').text(count);
    }

    // Meeting Actions
    function startMeeting(meetingId) {
        if (confirm('Are you sure you want to start this meeting?')) {
            // Show loading state
            const $button = $(`button[onclick="startMeeting(${meetingId})"]`);
            const originalText = $button.html();
            $button.html('<i class="fas fa-spinner fa-spin mr-1.5"></i>Starting...').prop('disabled', true);
            
            $.post('@Url.Action("StartMeeting", "Meeting")', { id: meetingId })
                .done(function(response) {
                    if (response.success) {
                        // Show success message
                        showNotification('Meeting started successfully!', 'success');
                        
                        // Reload page after short delay
                        setTimeout(() => {
                            location.reload();
                        }, 1000);
                    } else {
                        showNotification(response.message || 'Failed to start meeting.', 'error');
                        $button.html(originalText).prop('disabled', false);
                    }
                })
                .fail(function() {
                    showNotification('Failed to start meeting. Please try again.', 'error');
                    $button.html(originalText).prop('disabled', false);
                });
        }
    }

    // Utility Functions
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = $(`
            <div class="fixed top-4 right-4 z-50 max-w-sm w-full bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300">
                <div class="p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas ${type === 'success' ? 'fa-check-circle text-green-500' : type === 'error' ? 'fa-exclamation-circle text-red-500' : 'fa-info-circle text-blue-500'}"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-neutral-900 dark:text-white">${message}</p>
                        </div>
                        <div class="ml-auto pl-3">
                            <button class="inline-flex text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-300" onclick="$(this).closest('.fixed').remove()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `);
        
        // Add to page
        $('body').append(notification);
        
        // Animate in
        setTimeout(() => {
            notification.removeClass('translate-x-full');
        }, 100);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.addClass('translate-x-full');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 5000);
    }

    // Make functions globally available
    window.startMeeting = startMeeting;
    window.clearAllFilters = clearAllFilters;
    window.applyQuickFilter = applyQuickFilter;
</script>
