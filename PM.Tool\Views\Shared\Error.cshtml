﻿@model ErrorViewModel
@{
    ViewData["Title"] = Model.StatusCode switch
    {
        404 => "Page Not Found",
        403 => "Access Denied",
        500 => "Server Error",
        _ => "Error"
    };
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="text-center mt-5 mb-4">
                @switch (Model.StatusCode)
                {
                    case 404:
                        <i class="bi bi-search display-1 text-primary mb-4"></i>
                        <h1 class="h3">Page Not Found</h1>
                        <p class="text-muted">The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.</p>
                        break;

                    case 403:
                        <i class="bi bi-shield-lock display-1 text-warning mb-4"></i>
                        <h1 class="h3">Access Denied</h1>
                        <p class="text-muted">You do not have permission to access this resource.</p>
                        break;

                    case 500:
                    default:
                        <i class="bi bi-exclamation-triangle display-1 text-danger mb-4"></i>
                        <h1 class="h3">Something Went Wrong</h1>
                        <p class="text-muted">We apologize for the inconvenience. Please try again later.</p>
                        break;
                }
            </div>

            @if (!string.IsNullOrEmpty(Model.Message))
            {
    <p>
        <strong>Request ID:</strong> <code>@Model.RequestId</code>
    </p>
}

<h3>Development Mode</h3>
<p>
    Swapping to <strong>Development</strong> environment will display more detailed information about the error that occurred.
</p>
<p>
    <strong>The Development environment shouldn't be enabled for deployed applications.</strong>
    It can result in displaying sensitive information from exceptions to end users.
    For local debugging, enable the <strong>Development</strong> environment by setting the <strong>ASPNETCORE_ENVIRONMENT</strong> environment variable to <strong>Development</strong>
    and restarting the app.
</p>
