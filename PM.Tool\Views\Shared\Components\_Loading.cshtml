@*
    Professional Loading Component - Usage Examples:

    1. Basic spinner:
    @{
        ViewData["Type"] = "spinner"; // spinner, skeleton, dots, pulse
        ViewData["Size"] = "md"; // sm, md, lg, xl
        ViewData["Message"] = "Loading...";
    }
    <partial name="Components/_Loading" view-data="ViewData" />

    2. Skeleton loader:
    @{
        ViewData["Type"] = "skeleton";
        ViewData["Lines"] = 3;
        ViewData["ShowAvatar"] = true;
    }
    <partial name="Components/_Loading" view-data="ViewData" />

    3. Full page loading:
    @{
        ViewData["Type"] = "fullpage";
        ViewData["Message"] = "Please wait while we load your data...";
        ViewData["ShowProgress"] = true;
    }
    <partial name="Components/_Loading" view-data="ViewData" />
*@

@{
    var type = ViewData["Type"]?.ToString() ?? "spinner";
    var size = ViewData["Size"]?.ToString() ?? "md";
    var message = ViewData["Message"]?.ToString() ?? "Loading...";
    var showMessage = ViewData["ShowMessage"] as bool? ?? true;
    var overlay = ViewData["Overlay"] as bool? ?? false;
    var fullPage = ViewData["FullPage"] as bool? ?? false;
    var color = ViewData["Color"]?.ToString() ?? "primary";

    // Skeleton specific
    var lines = ViewData["Lines"] as int? ?? 3;
    var showAvatar = ViewData["ShowAvatar"] as bool? ?? false;
    var showButton = ViewData["ShowButton"] as bool? ?? false;

    // Progress specific
    var showProgress = ViewData["ShowProgress"] as bool? ?? false;
    var progress = ViewData["Progress"] as int? ?? 0;

    var sizeClasses = size switch {
        "sm" => "h-4 w-4",
        "md" => "h-8 w-8",
        "lg" => "h-12 w-12",
        "xl" => "h-16 w-16",
        _ => "h-8 w-8"
    };

    var colorClasses = color switch {
        "primary" => "border-primary-600",
        "success" => "border-success-600",
        "warning" => "border-warning-600",
        "danger" => "border-danger-600",
        "info" => "border-info-600",
        _ => "border-primary-600"
    };

    var containerClasses = fullPage ? "fixed inset-0 z-50 bg-white dark:bg-neutral-900 bg-opacity-75 dark:bg-opacity-75" :
                          overlay ? "absolute inset-0 z-40 bg-white dark:bg-surface-dark bg-opacity-75" :
                          "flex items-center justify-center";
}

<div class="@containerClasses @(fullPage || overlay ? "flex items-center justify-center" : "")">

@switch (type)
{
    case "spinner":
        <div class="text-center">
            <div class="animate-spin rounded-full @sizeClasses border-b-2 @colorClasses mx-auto"></div>
            @if (showMessage && !string.IsNullOrEmpty(message))
            {
                <p class="mt-4 text-sm text-neutral-600 dark:text-dark-300">@message</p>
            }
            @if (showProgress)
            {
                <div class="mt-4 w-64 mx-auto">
                    <div class="w-full bg-neutral-200 dark:bg-dark-600 rounded-full h-2">
                        <div class="bg-primary-600 h-2 rounded-full transition-all duration-300" style="width: @progress%"></div>
                    </div>
                    <p class="text-xs text-neutral-500 dark:text-dark-400 mt-2">@progress% complete</p>
                </div>
            }
        </div>
        break;

    case "dots":
        <div class="text-center">
            <div class="flex space-x-2 justify-center">
                <div class="w-3 h-3 bg-primary-600 rounded-full animate-bounce"></div>
                <div class="w-3 h-3 bg-primary-600 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                <div class="w-3 h-3 bg-primary-600 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
            @if (showMessage && !string.IsNullOrEmpty(message))
            {
                <p class="mt-4 text-sm text-neutral-600 dark:text-dark-300">@message</p>
            }
        </div>
        break;

    case "pulse":
        <div class="text-center">
            <div class="@sizeClasses bg-primary-600 rounded-full mx-auto animate-pulse"></div>
            @if (showMessage && !string.IsNullOrEmpty(message))
            {
                <p class="mt-4 text-sm text-neutral-600 dark:text-dark-300">@message</p>
            }
        </div>
        break;

    case "skeleton":
        <div class="animate-pulse space-y-4 w-full max-w-md">
            @if (showAvatar)
            {
                <div class="flex items-center space-x-4">
                    <div class="rounded-full bg-neutral-300 dark:bg-dark-600 h-12 w-12"></div>
                    <div class="flex-1 space-y-2">
                        <div class="h-4 bg-neutral-300 dark:bg-dark-600 rounded w-3/4"></div>
                        <div class="h-3 bg-neutral-300 dark:bg-dark-600 rounded w-1/2"></div>
                    </div>
                </div>
            }

            @for (int i = 0; i < lines; i++)
            {
                <div class="space-y-2">
                    <div class="h-4 bg-neutral-300 dark:bg-dark-600 rounded"></div>
                    <div class="h-4 bg-neutral-300 dark:bg-dark-600 rounded w-5/6"></div>
                </div>
            }

            @if (showButton)
            {
                <div class="flex space-x-4">
                    <div class="h-10 bg-neutral-300 dark:bg-dark-600 rounded w-24"></div>
                    <div class="h-10 bg-neutral-300 dark:bg-dark-600 rounded w-20"></div>
                </div>
            }
        </div>
        break;

    case "card":
        <div class="animate-pulse">
            <div class="bg-white dark:bg-surface-dark border border-neutral-200 dark:border-dark-200 rounded-xl p-6 space-y-4">
                <div class="flex items-center space-x-4">
                    <div class="rounded-lg bg-neutral-300 dark:bg-dark-600 h-8 w-8"></div>
                    <div class="flex-1 space-y-2">
                        <div class="h-4 bg-neutral-300 dark:bg-dark-600 rounded w-1/3"></div>
                        <div class="h-3 bg-neutral-300 dark:bg-dark-600 rounded w-1/4"></div>
                    </div>
                </div>
                <div class="space-y-3">
                    <div class="h-4 bg-neutral-300 dark:bg-dark-600 rounded"></div>
                    <div class="h-4 bg-neutral-300 dark:bg-dark-600 rounded w-4/5"></div>
                    <div class="h-4 bg-neutral-300 dark:bg-dark-600 rounded w-3/5"></div>
                </div>
                <div class="flex justify-between pt-4">
                    <div class="h-8 bg-neutral-300 dark:bg-dark-600 rounded w-20"></div>
                    <div class="h-8 bg-neutral-300 dark:bg-dark-600 rounded w-16"></div>
                </div>
            </div>
        </div>
        break;

    case "table":
        <div class="animate-pulse">
            <div class="bg-white dark:bg-surface-dark border border-neutral-200 dark:border-dark-200 rounded-xl overflow-hidden">
                <!-- Table Header -->
                <div class="bg-neutral-50 dark:bg-surface-dark-secondary px-6 py-4 border-b border-neutral-200 dark:border-dark-200">
                    <div class="flex items-center space-x-4">
                        <div class="h-4 bg-neutral-300 dark:bg-dark-600 rounded w-1/4"></div>
                        <div class="h-4 bg-neutral-300 dark:bg-dark-600 rounded w-1/6"></div>
                        <div class="h-4 bg-neutral-300 dark:bg-dark-600 rounded w-1/6"></div>
                        <div class="h-4 bg-neutral-300 dark:bg-dark-600 rounded w-1/8"></div>
                    </div>
                </div>
                <!-- Table Rows -->
                @for (int i = 0; i < 5; i++)
                {
                    <div class="px-6 py-4 border-b border-neutral-200 dark:border-dark-200">
                        <div class="flex items-center space-x-4">
                            <div class="h-3 bg-neutral-300 dark:bg-dark-600 rounded w-1/4"></div>
                            <div class="h-3 bg-neutral-300 dark:bg-dark-600 rounded w-1/6"></div>
                            <div class="h-3 bg-neutral-300 dark:bg-dark-600 rounded w-1/6"></div>
                            <div class="h-6 bg-neutral-300 dark:bg-dark-600 rounded w-16"></div>
                        </div>
                    </div>
                }
            </div>
        </div>
        break;

    case "chart":
        <div class="animate-pulse">
            <div class="bg-white dark:bg-surface-dark border border-neutral-200 dark:border-dark-200 rounded-xl p-6">
                <div class="flex items-center space-x-3 mb-6">
                    <div class="w-8 h-8 bg-neutral-300 dark:bg-dark-600 rounded-lg"></div>
                    <div class="h-4 bg-neutral-300 dark:bg-dark-600 rounded w-1/3"></div>
                </div>
                <div class="h-64 bg-neutral-300 dark:bg-dark-600 rounded-lg"></div>
            </div>
        </div>
        break;

    default:
        <div class="text-center">
            <div class="animate-spin rounded-full @sizeClasses border-b-2 @colorClasses mx-auto"></div>
            @if (showMessage && !string.IsNullOrEmpty(message))
            {
                <p class="mt-4 text-sm text-neutral-600 dark:text-dark-300">@message</p>
            }
        </div>
        break;
}

</div>

@section Scripts {
    <script>
        // Loading component utilities
        window.LoadingComponent = {
            show: function(containerId, type = 'spinner', message = 'Loading...') {
                var container = document.getElementById(containerId);
                if (!container) return;

                var loadingHtml = this.generateLoadingHtml(type, message);
                container.innerHTML = loadingHtml;
                container.classList.add('loading-active');
            },

            hide: function(containerId) {
                var container = document.getElementById(containerId);
                if (!container) return;

                container.classList.remove('loading-active');
                // Don't clear content immediately, let the calling code handle it
            },

            generateLoadingHtml: function(type, message) {
                switch(type) {
                    case 'spinner':
                        return `
                            <div class="flex items-center justify-center py-8">
                                <div class="text-center">
                                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
                                    <p class="mt-4 text-sm text-neutral-600 dark:text-dark-300">${message}</p>
                                </div>
                            </div>
                        `;
                    case 'dots':
                        return `
                            <div class="flex items-center justify-center py-8">
                                <div class="text-center">
                                    <div class="flex space-x-2 justify-center">
                                        <div class="w-3 h-3 bg-primary-600 rounded-full animate-bounce"></div>
                                        <div class="w-3 h-3 bg-primary-600 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                                        <div class="w-3 h-3 bg-primary-600 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                                    </div>
                                    <p class="mt-4 text-sm text-neutral-600 dark:text-dark-300">${message}</p>
                                </div>
                            </div>
                        `;
                    default:
                        return this.generateLoadingHtml('spinner', message);
                }
            }
        };
    </script>
}
