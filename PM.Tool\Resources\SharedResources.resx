<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>

  <!-- Common UI Elements -->
  <data name="Common.Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Common.Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Common.Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Common.Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Common.Create" xml:space="preserve">
    <value>Create</value>
  </data>
  <data name="Common.Update" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="Common.Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="Common.Back" xml:space="preserve">
    <value>Back</value>
  </data>
  <data name="Common.Next" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="Common.Previous" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="Common.Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="Common.Filter" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="Common.Export" xml:space="preserve">
    <value>Export</value>
  </data>
  <data name="Common.Import" xml:space="preserve">
    <value>Import</value>
  </data>
  <data name="Common.Print" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="Common.Download" xml:space="preserve">
    <value>Download</value>
  </data>
  <data name="Common.Upload" xml:space="preserve">
    <value>Upload</value>
  </data>
  <data name="Common.Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="Common.No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="Common.OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Common.Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="Common.Loading" xml:space="preserve">
    <value>Loading...</value>
  </data>
  <data name="Common.NoData" xml:space="preserve">
    <value>No data available</value>
  </data>
  <data name="Common.Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="Common.Success" xml:space="preserve">
    <value>Success</value>
  </data>
  <data name="Common.Warning" xml:space="preserve">
    <value>Warning</value>
  </data>
  <data name="Common.Information" xml:space="preserve">
    <value>Information</value>
  </data>

  <!-- Navigation -->
  <data name="Nav.Dashboard" xml:space="preserve">
    <value>Dashboard</value>
  </data>
  <data name="Nav.Projects" xml:space="preserve">
    <value>Projects</value>
  </data>
  <data name="Nav.Tasks" xml:space="preserve">
    <value>Tasks</value>
  </data>
  <data name="Nav.Management" xml:space="preserve">
    <value>Management</value>
  </data>
  <data name="Nav.Analytics" xml:space="preserve">
    <value>Analytics</value>
  </data>
  <data name="Nav.Resources" xml:space="preserve">
    <value>Resources</value>
  </data>
  <data name="Nav.Risks" xml:space="preserve">
    <value>Risks</value>
  </data>
  <data name="Nav.Meetings" xml:space="preserve">
    <value>Meetings</value>
  </data>
  <data name="Nav.Requirements" xml:space="preserve">
    <value>Requirements</value>
  </data>
  <data name="Nav.Backlog" xml:space="preserve">
    <value>Backlog</value>
  </data>
  <data name="Nav.Kanban" xml:space="preserve">
    <value>Kanban Board</value>
  </data>
  <data name="Nav.Documentation" xml:space="preserve">
    <value>Documentation</value>
  </data>
  <data name="Nav.SkillsManagement" xml:space="preserve">
    <value>Skills Management</value>
  </data>
  <data name="Nav.ResourceUtilization" xml:space="preserve">
    <value>Resource Utilization</value>
  </data>
  <data name="Nav.MeetingCalendar" xml:space="preserve">
    <value>Meeting Calendar</value>
  </data>
  <data name="Nav.ActionItems" xml:space="preserve">
    <value>Action Items</value>
  </data>
  <data name="Nav.AnalyticsDashboard" xml:space="preserve">
    <value>Analytics Dashboard</value>
  </data>
  <data name="Nav.AdvancedReports" xml:space="preserve">
    <value>Advanced Reports</value>
  </data>
  <data name="Nav.TeamAnalytics" xml:space="preserve">
    <value>Team Analytics</value>
  </data>
  <data name="Nav.BurndownCharts" xml:space="preserve">
    <value>Burndown Charts</value>
  </data>
  <data name="Nav.VelocityCharts" xml:space="preserve">
    <value>Velocity Charts</value>
  </data>
  <data name="Nav.ExportData" xml:space="preserve">
    <value>Export Data</value>
  </data>

  <!-- Project Management -->
  <data name="Project.Title" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="Project.Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Project.StartDate" xml:space="preserve">
    <value>Start Date</value>
  </data>
  <data name="Project.EndDate" xml:space="preserve">
    <value>End Date</value>
  </data>
  <data name="Project.Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="Project.Priority" xml:space="preserve">
    <value>Priority</value>
  </data>
  <data name="Project.Budget" xml:space="preserve">
    <value>Budget</value>
  </data>
  <data name="Project.Progress" xml:space="preserve">
    <value>Progress</value>
  </data>
  <data name="Project.Manager" xml:space="preserve">
    <value>Project Manager</value>
  </data>
  <data name="Project.Team" xml:space="preserve">
    <value>Team</value>
  </data>

  <!-- Task Management -->
  <data name="Task.Title" xml:space="preserve">
    <value>Task Title</value>
  </data>
  <data name="Task.Description" xml:space="preserve">
    <value>Task Description</value>
  </data>
  <data name="Task.AssignedTo" xml:space="preserve">
    <value>Assigned To</value>
  </data>
  <data name="Task.DueDate" xml:space="preserve">
    <value>Due Date</value>
  </data>
  <data name="Task.EstimatedHours" xml:space="preserve">
    <value>Estimated Hours</value>
  </data>
  <data name="Task.ActualHours" xml:space="preserve">
    <value>Actual Hours</value>
  </data>
  <data name="Task.StoryPoints" xml:space="preserve">
    <value>Story Points</value>
  </data>

  <!-- Agile Terms -->
  <data name="Agile.Epic" xml:space="preserve">
    <value>Epic</value>
  </data>
  <data name="Agile.UserStory" xml:space="preserve">
    <value>User Story</value>
  </data>
  <data name="Agile.Sprint" xml:space="preserve">
    <value>Sprint</value>
  </data>
  <data name="Agile.Backlog" xml:space="preserve">
    <value>Backlog</value>
  </data>
  <data name="Agile.Kanban" xml:space="preserve">
    <value>Kanban</value>
  </data>
  <data name="Agile.Scrum" xml:space="preserve">
    <value>Scrum</value>
  </data>
  <data name="Agile.Velocity" xml:space="preserve">
    <value>Velocity</value>
  </data>
  <data name="Agile.Burndown" xml:space="preserve">
    <value>Burndown</value>
  </data>

  <!-- Status Values -->
  <data name="Status.Active" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="Status.Inactive" xml:space="preserve">
    <value>Inactive</value>
  </data>
  <data name="Status.Completed" xml:space="preserve">
    <value>Completed</value>
  </data>
  <data name="Status.InProgress" xml:space="preserve">
    <value>In Progress</value>
  </data>
  <data name="Status.Pending" xml:space="preserve">
    <value>Pending</value>
  </data>
  <data name="Status.Cancelled" xml:space="preserve">
    <value>Cancelled</value>
  </data>

  <!-- Priority Values -->
  <data name="Priority.Critical" xml:space="preserve">
    <value>Critical</value>
  </data>
  <data name="Priority.High" xml:space="preserve">
    <value>High</value>
  </data>
  <data name="Priority.Medium" xml:space="preserve">
    <value>Medium</value>
  </data>
  <data name="Priority.Low" xml:space="preserve">
    <value>Low</value>
  </data>

  <!-- Messages -->
  <data name="Message.SaveSuccess" xml:space="preserve">
    <value>Item saved successfully</value>
  </data>
  <data name="Message.DeleteSuccess" xml:space="preserve">
    <value>Item deleted successfully</value>
  </data>
  <data name="Message.UpdateSuccess" xml:space="preserve">
    <value>Item updated successfully</value>
  </data>
  <data name="Message.ErrorOccurred" xml:space="preserve">
    <value>An error occurred. Please try again.</value>
  </data>
  <data name="Message.ConfirmDelete" xml:space="preserve">
    <value>Are you sure you want to delete this item?</value>
  </data>

  <!-- Meeting Management -->
  <data name="Meeting.Title" xml:space="preserve">
    <value>Meeting Title</value>
  </data>
  <data name="Meeting.Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Meeting.StartTime" xml:space="preserve">
    <value>Start Time</value>
  </data>
  <data name="Meeting.EndTime" xml:space="preserve">
    <value>End Time</value>
  </data>
  <data name="Meeting.Location" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="Meeting.Type" xml:space="preserve">
    <value>Meeting Type</value>
  </data>
  <data name="Meeting.Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="Meeting.Organizer" xml:space="preserve">
    <value>Organizer</value>
  </data>
  <data name="Meeting.Attendees" xml:space="preserve">
    <value>Attendees</value>
  </data>
  <data name="Meeting.ActionItems" xml:space="preserve">
    <value>Action Items</value>
  </data>
  <data name="Meeting.Documents" xml:space="preserve">
    <value>Documents</value>
  </data>
  <data name="Meeting.Minutes" xml:space="preserve">
    <value>Meeting Minutes</value>
  </data>
  <data name="Meeting.Agenda" xml:space="preserve">
    <value>Agenda</value>
  </data>

  <!-- Requirements Management -->
  <data name="Requirement.Title" xml:space="preserve">
    <value>Requirement Title</value>
  </data>
  <data name="Requirement.Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Requirement.Type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="Requirement.Priority" xml:space="preserve">
    <value>Priority</value>
  </data>
  <data name="Requirement.Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="Requirement.Source" xml:space="preserve">
    <value>Source</value>
  </data>
  <data name="Requirement.Stakeholder" xml:space="preserve">
    <value>Stakeholder</value>
  </data>
  <data name="Requirement.AcceptanceCriteria" xml:space="preserve">
    <value>Acceptance Criteria</value>
  </data>
  <data name="Requirement.BusinessValue" xml:space="preserve">
    <value>Business Value</value>
  </data>
  <data name="Requirement.Comments" xml:space="preserve">
    <value>Comments</value>
  </data>
  <data name="Requirement.Attachments" xml:space="preserve">
    <value>Attachments</value>
  </data>
  <data name="Requirement.Changes" xml:space="preserve">
    <value>Change History</value>
  </data>
  <data name="Requirement.Tasks" xml:space="preserve">
    <value>Related Tasks</value>
  </data>

  <!-- Risk Management -->
  <data name="Risk.Title" xml:space="preserve">
    <value>Risk Title</value>
  </data>
  <data name="Risk.Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Risk.Category" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="Risk.Probability" xml:space="preserve">
    <value>Probability</value>
  </data>
  <data name="Risk.Impact" xml:space="preserve">
    <value>Impact</value>
  </data>
  <data name="Risk.Score" xml:space="preserve">
    <value>Risk Score</value>
  </data>
  <data name="Risk.Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="Risk.Owner" xml:space="preserve">
    <value>Risk Owner</value>
  </data>
  <data name="Risk.MitigationPlan" xml:space="preserve">
    <value>Mitigation Plan</value>
  </data>
  <data name="Risk.MitigationActions" xml:space="preserve">
    <value>Mitigation Actions</value>
  </data>
  <data name="Risk.ContingencyPlan" xml:space="preserve">
    <value>Contingency Plan</value>
  </data>

  <!-- Resource Management -->
  <data name="Resource.Name" xml:space="preserve">
    <value>Resource Name</value>
  </data>
  <data name="Resource.Type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="Resource.Department" xml:space="preserve">
    <value>Department</value>
  </data>
  <data name="Resource.Location" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="Resource.HourlyRate" xml:space="preserve">
    <value>Hourly Rate</value>
  </data>
  <data name="Resource.Capacity" xml:space="preserve">
    <value>Capacity</value>
  </data>
  <data name="Resource.Skills" xml:space="preserve">
    <value>Skills</value>
  </data>
  <data name="Resource.Availability" xml:space="preserve">
    <value>Availability</value>
  </data>
  <data name="Resource.Utilization" xml:space="preserve">
    <value>Utilization</value>
  </data>
  <data name="Resource.Allocation" xml:space="preserve">
    <value>Allocation</value>
  </data>

  <!-- Agile/Scrum Terms -->
  <data name="Agile.SprintPlanning" xml:space="preserve">
    <value>Sprint Planning</value>
  </data>
  <data name="Agile.SprintReview" xml:space="preserve">
    <value>Sprint Review</value>
  </data>
  <data name="Agile.SprintRetrospective" xml:space="preserve">
    <value>Sprint Retrospective</value>
  </data>
  <data name="Agile.DailyStandup" xml:space="preserve">
    <value>Daily Standup</value>
  </data>
  <data name="Agile.ProductBacklog" xml:space="preserve">
    <value>Product Backlog</value>
  </data>
  <data name="Agile.SprintBacklog" xml:space="preserve">
    <value>Sprint Backlog</value>
  </data>
  <data name="Agile.Definition" xml:space="preserve">
    <value>Definition of Done</value>
  </data>
  <data name="Agile.StoryPoints" xml:space="preserve">
    <value>Story Points</value>
  </data>
  <data name="Agile.SprintStarted" xml:space="preserve">
    <value>Sprint started successfully</value>
  </data>
  <data name="Agile.SprintCompleted" xml:space="preserve">
    <value>Sprint completed successfully</value>
  </data>

  <!-- Enum Values - Meeting Types -->
  <data name="Enum.MeetingType.General" xml:space="preserve">
    <value>General</value>
  </data>
  <data name="Enum.MeetingType.Standup" xml:space="preserve">
    <value>Daily Standup</value>
  </data>
  <data name="Enum.MeetingType.Planning" xml:space="preserve">
    <value>Planning</value>
  </data>
  <data name="Enum.MeetingType.Review" xml:space="preserve">
    <value>Review</value>
  </data>
  <data name="Enum.MeetingType.Retrospective" xml:space="preserve">
    <value>Retrospective</value>
  </data>
  <data name="Enum.MeetingType.Stakeholder" xml:space="preserve">
    <value>Stakeholder</value>
  </data>
  <data name="Enum.MeetingType.Requirements" xml:space="preserve">
    <value>Requirements</value>
  </data>
  <data name="Enum.MeetingType.Technical" xml:space="preserve">
    <value>Technical</value>
  </data>
  <data name="Enum.MeetingType.Status" xml:space="preserve">
    <value>Status</value>
  </data>

  <!-- Enum Values - Meeting Status -->
  <data name="Enum.MeetingStatus.Scheduled" xml:space="preserve">
    <value>Scheduled</value>
  </data>
  <data name="Enum.MeetingStatus.InProgress" xml:space="preserve">
    <value>In Progress</value>
  </data>
  <data name="Enum.MeetingStatus.Completed" xml:space="preserve">
    <value>Completed</value>
  </data>
  <data name="Enum.MeetingStatus.Cancelled" xml:space="preserve">
    <value>Cancelled</value>
  </data>
  <data name="Enum.MeetingStatus.Postponed" xml:space="preserve">
    <value>Postponed</value>
  </data>

  <!-- Enum Values - Requirement Types -->
  <data name="Enum.RequirementType.Functional" xml:space="preserve">
    <value>Functional</value>
  </data>
  <data name="Enum.RequirementType.NonFunctional" xml:space="preserve">
    <value>Non-Functional</value>
  </data>
  <data name="Enum.RequirementType.Business" xml:space="preserve">
    <value>Business</value>
  </data>
  <data name="Enum.RequirementType.Technical" xml:space="preserve">
    <value>Technical</value>
  </data>
  <data name="Enum.RequirementType.Performance" xml:space="preserve">
    <value>Performance</value>
  </data>
  <data name="Enum.RequirementType.Security" xml:space="preserve">
    <value>Security</value>
  </data>
  <data name="Enum.RequirementType.Usability" xml:space="preserve">
    <value>Usability</value>
  </data>
  <data name="Enum.RequirementType.Compliance" xml:space="preserve">
    <value>Compliance</value>
  </data>
  <data name="Enum.RequirementType.Integration" xml:space="preserve">
    <value>Integration</value>
  </data>

  <!-- Enum Values - Risk Categories -->
  <data name="Enum.RiskCategory.Technical" xml:space="preserve">
    <value>Technical</value>
  </data>
  <data name="Enum.RiskCategory.Schedule" xml:space="preserve">
    <value>Schedule</value>
  </data>
  <data name="Enum.RiskCategory.Budget" xml:space="preserve">
    <value>Budget</value>
  </data>
  <data name="Enum.RiskCategory.Resource" xml:space="preserve">
    <value>Resource</value>
  </data>
  <data name="Enum.RiskCategory.Quality" xml:space="preserve">
    <value>Quality</value>
  </data>
  <data name="Enum.RiskCategory.External" xml:space="preserve">
    <value>External</value>
  </data>
  <data name="Enum.RiskCategory.Organizational" xml:space="preserve">
    <value>Organizational</value>
  </data>

  <!-- Enum Values - Resource Types -->
  <data name="Enum.ResourceType.Human" xml:space="preserve">
    <value>Human Resource</value>
  </data>
  <data name="Enum.ResourceType.Equipment" xml:space="preserve">
    <value>Equipment</value>
  </data>
  <data name="Enum.ResourceType.Material" xml:space="preserve">
    <value>Material</value>
  </data>
  <data name="Enum.ResourceType.Software" xml:space="preserve">
    <value>Software</value>
  </data>
  <data name="Enum.ResourceType.Facility" xml:space="preserve">
    <value>Facility</value>
  </data>

  <!-- WBS Specific Messages -->
  <data name="WBS.ErrorLoadingStructure" xml:space="preserve">
    <value>Error loading WBS structure</value>
  </data>
  <data name="WBS.TaskCreatedSuccessfully" xml:space="preserve">
    <value>Task created successfully</value>
  </data>
  <data name="WBS.TaskDeletedSuccessfully" xml:space="preserve">
    <value>Task deleted successfully</value>
  </data>
  <data name="WBS.TaskDuplicatedSuccessfully" xml:space="preserve">
    <value>Task duplicated successfully</value>
  </data>
  <data name="WBS.TaskMovedSuccessfully" xml:space="preserve">
    <value>Task moved {0} successfully</value>
  </data>
  <data name="WBS.TaskStatusUpdated" xml:space="preserve">
    <value>Task status updated to {0}</value>
  </data>
  <data name="WBS.WbsCodesGenerated" xml:space="preserve">
    <value>WBS codes generated successfully</value>
  </data>
  <data name="WBS.ExportingTo" xml:space="preserve">
    <value>Exporting WBS to {0}...</value>
  </data>
  <data name="WBS.ExportingTask" xml:space="preserve">
    <value>Exporting task...</value>
  </data>
  <data name="WBS.PrintingWbs" xml:space="preserve">
    <value>Opening print dialog...</value>
  </data>
  <data name="WBS.CompactViewEnabled" xml:space="preserve">
    <value>Compact view enabled</value>
  </data>
  <data name="WBS.NormalViewEnabled" xml:space="preserve">
    <value>Normal view enabled</value>
  </data>
  <data name="WBS.DetailedViewEnabled" xml:space="preserve">
    <value>Detailed view enabled</value>
  </data>
  <data name="WBS.RefreshingView" xml:space="preserve">
    <value>Refreshing WBS view...</value>
  </data>
  <data name="WBS.FilteredToShow" xml:space="preserve">
    <value>Filtered to show {0} tasks</value>
  </data>
  <data name="WBS.ShowingAllTasks" xml:space="preserve">
    <value>Showing all tasks</value>
  </data>
  <data name="WBS.FilteredOverdue" xml:space="preserve">
    <value>Filtered to show overdue tasks</value>
  </data>
  <data name="WBS.TaskTitleRequired" xml:space="preserve">
    <value>Task title is required</value>
  </data>
  <data name="WBS.ProjectIdMissing" xml:space="preserve">
    <value>Project ID is missing</value>
  </data>
  <data name="WBS.ValidationError" xml:space="preserve">
    <value>Validation error. Please check your input.</value>
  </data>
  <data name="WBS.AccessDenied" xml:space="preserve">
    <value>Access denied. Please refresh the page and try again.</value>
  </data>
  <data name="WBS.RequestFormatError" xml:space="preserve">
    <value>Request format error. Please try again.</value>
  </data>
  <data name="WBS.ErrorCreatingTask" xml:space="preserve">
    <value>Error creating task</value>
  </data>
  <data name="WBS.ErrorLoadingTaskDetails" xml:space="preserve">
    <value>Error loading task details</value>
  </data>
  <data name="WBS.ErrorDeletingTask" xml:space="preserve">
    <value>Error deleting task</value>
  </data>
  <data name="WBS.ErrorDuplicatingTask" xml:space="preserve">
    <value>Error duplicating task</value>
  </data>
  <data name="WBS.ErrorMovingTask" xml:space="preserve">
    <value>Error moving task {0}</value>
  </data>
  <data name="WBS.ErrorUpdatingTaskStatus" xml:space="preserve">
    <value>Error updating task status</value>
  </data>
  <data name="WBS.ErrorGeneratingCodes" xml:space="preserve">
    <value>Error generating WBS codes</value>
  </data>
  <data name="WBS.ConfirmDeleteTask" xml:space="preserve">
    <value>Are you sure you want to delete this task? This action cannot be undone.</value>
  </data>
  <data name="WBS.ConfirmDuplicateTask" xml:space="preserve">
    <value>Create a duplicate of this task?</value>
  </data>
  <data name="WBS.ConfirmGenerateCodes" xml:space="preserve">
    <value>This will regenerate all WBS codes. Continue?</value>
  </data>
  <data name="WBS.CreateNewTask" xml:space="preserve">
    <value>Create New Task</value>
  </data>
  <data name="WBS.CreateChildTaskFor" xml:space="preserve">
    <value>Create Child Task for: {0}</value>
  </data>
  <data name="WBS.TaskNotFound" xml:space="preserve">
    <value>Task not found</value>
  </data>
  <data name="WBS.UnsupportedExportFormat" xml:space="preserve">
    <value>Unsupported export format</value>
  </data>
  <data name="WBS.GanttViewComingSoon" xml:space="preserve">
    <value>Gantt view feature coming soon!</value>
  </data>

  <!-- WBS Task Actions -->
  <data name="WBS.Action.ViewDetails" xml:space="preserve">
    <value>View Details</value>
  </data>
  <data name="WBS.Action.EditTask" xml:space="preserve">
    <value>Edit Task</value>
  </data>
  <data name="WBS.Action.Duplicate" xml:space="preserve">
    <value>Duplicate</value>
  </data>
  <data name="WBS.Action.AddChild" xml:space="preserve">
    <value>Add Child</value>
  </data>
  <data name="WBS.Action.MoreActions" xml:space="preserve">
    <value>More Actions</value>
  </data>
  <data name="WBS.Action.MoveUp" xml:space="preserve">
    <value>Move Up</value>
  </data>
  <data name="WBS.Action.MoveDown" xml:space="preserve">
    <value>Move Down</value>
  </data>
  <data name="WBS.Action.StartTask" xml:space="preserve">
    <value>Start Task</value>
  </data>
  <data name="WBS.Action.MarkInReview" xml:space="preserve">
    <value>Mark In Review</value>
  </data>
  <data name="WBS.Action.MarkComplete" xml:space="preserve">
    <value>Mark Complete</value>
  </data>
  <data name="WBS.Action.CancelTask" xml:space="preserve">
    <value>Cancel Task</value>
  </data>
  <data name="WBS.Action.ExportTask" xml:space="preserve">
    <value>Export Task</value>
  </data>
  <data name="WBS.Action.DeleteTask" xml:space="preserve">
    <value>Delete Task</value>
  </data>

  <!-- WBS Labels -->
  <data name="WBS.Label.Progress" xml:space="preserve">
    <value>Progress</value>
  </data>
  <data name="WBS.Label.Unassigned" xml:space="preserve">
    <value>Unassigned</value>
  </data>
  <data name="WBS.Label.Overdue" xml:space="preserve">
    <value>Overdue</value>
  </data>
  <data name="WBS.Label.Start" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="WBS.Label.Due" xml:space="preserve">
    <value>Due</value>
  </data>
  <data name="WBS.Label.GeneratedOn" xml:space="preserve">
    <value>Generated on</value>
  </data>
  <data name="WBS.Label.WorkBreakdownStructure" xml:space="preserve">
    <value>Work Breakdown Structure</value>
  </data>

</root>
