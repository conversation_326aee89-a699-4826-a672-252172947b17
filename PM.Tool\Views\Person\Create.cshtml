@model PM.Tool.Models.ViewModels.PersonCreateViewModel
@using PM.Tool.Core.Entities

@{
    ViewData["Title"] = "Create Person";
    ViewData["PageTitle"] = "Create New Person";
    ViewData["PageDescription"] = "Add a new person to the system with their role and information.";
}

<!-- Page Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8">
    <div>
        <h1 class="text-3xl font-bold text-neutral-900 dark:text-white mb-2">@ViewData["PageTitle"]</h1>
        <p class="text-neutral-600 dark:text-neutral-400">@ViewData["PageDescription"]</p>
    </div>
    <div class="flex space-x-3 mt-4 lg:mt-0">
        <a href="@Url.Action("Index")" class="btn-secondary-custom">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to People
        </a>
    </div>
</div>

<form asp-action="Create" method="post" class="space-y-8">
    <div asp-validation-summary="ModelOnly" class="alert-danger-custom mb-6"></div>

    <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
        <!-- Main Form Content -->
        <div class="xl:col-span-2 space-y-8">
            <!-- Basic Information -->
            <div class="card-custom">
                <div class="card-header-custom">
                    <h2 class="text-xl font-semibold text-neutral-900 dark:text-white">Basic Information</h2>
                    <p class="text-sm text-neutral-600 dark:text-neutral-400 mt-1">Enter the person's basic details</p>
                </div>
                <div class="card-body-custom">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- First Name -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.FirstName).ToString();
                            ViewData["Name"] = "FirstName";
                            ViewData["Type"] = "text";
                            ViewData["Required"] = true;
                            ViewData["Icon"] = "fas fa-user";
                            ViewData["Placeholder"] = "Enter first name";
                            ViewData["ErrorMessage"] = "";
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />
                        <span asp-validation-for="FirstName" class="text-sm text-danger-600 dark:text-danger-400"></span>

                        <!-- Last Name -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.LastName).ToString();
                            ViewData["Name"] = "LastName";
                            ViewData["Type"] = "text";
                            ViewData["Required"] = true;
                            ViewData["Icon"] = "fas fa-user";
                            ViewData["Placeholder"] = "Enter last name";
                            ViewData["ErrorMessage"] = "";
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />
                        <span asp-validation-for="LastName" class="text-sm text-danger-600 dark:text-danger-400"></span>

                        <!-- Email -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.Email).ToString();
                            ViewData["Name"] = "Email";
                            ViewData["Type"] = "email";
                            ViewData["Required"] = true;
                            ViewData["Icon"] = "fas fa-envelope";
                            ViewData["Placeholder"] = "Enter email address";
                            ViewData["ErrorMessage"] = "";
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />
                        <span asp-validation-for="Email" class="text-sm text-danger-600 dark:text-danger-400"></span>

                        <!-- Phone -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.Phone).ToString();
                            ViewData["Name"] = "Phone";
                            ViewData["Type"] = "tel";
                            ViewData["Required"] = false;
                            ViewData["Icon"] = "fas fa-phone";
                            ViewData["Placeholder"] = "+****************";
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Phone");
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />

                        <!-- Title -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.Title).ToString();
                            ViewData["Name"] = "Title";
                            ViewData["Type"] = "text";
                            ViewData["Required"] = false;
                            ViewData["Icon"] = "fas fa-briefcase";
                            ViewData["Placeholder"] = "Job title or position";
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Title");
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />

                        <!-- Department -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.Department).ToString();
                            ViewData["Name"] = "Department";
                            ViewData["Type"] = "text";
                            ViewData["Required"] = false;
                            ViewData["Icon"] = "fas fa-building";
                            ViewData["Placeholder"] = "Department or division";
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Department");
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />
                    </div>
                </div>
            </div>

            <!-- Professional Details -->
            <div class="card-custom">
                <div class="card-header-custom">
                    <h2 class="text-xl font-semibold text-neutral-900 dark:text-white">Professional Details</h2>
                    <p class="text-sm text-neutral-600 dark:text-neutral-400 mt-1">Work-related information and settings</p>
                </div>
                <div class="card-body-custom">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Organization -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.Organization).ToString();
                            ViewData["Name"] = "Organization";
                            ViewData["Type"] = "text";
                            ViewData["Required"] = false;
                            ViewData["Icon"] = "fas fa-building";
                            ViewData["Placeholder"] = "Company or organization";
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Organization");
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />

                        <!-- Location -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.Location).ToString();
                            ViewData["Name"] = "Location";
                            ViewData["Type"] = "text";
                            ViewData["Required"] = false;
                            ViewData["Icon"] = "fas fa-map-marker-alt";
                            ViewData["Placeholder"] = "City, State/Country";
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Location");
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />

                        <!-- Employee ID -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.EmployeeId).ToString();
                            ViewData["Name"] = "EmployeeId";
                            ViewData["Type"] = "text";
                            ViewData["Required"] = false;
                            ViewData["Icon"] = "fas fa-id-card";
                            ViewData["Placeholder"] = "Employee ID number";
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("EmployeeId");
                            ViewData["HelpText"] = "Required for internal employees";
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />

                        <!-- Hire Date -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.HireDate).ToString();
                            ViewData["Name"] = "HireDate";
                            ViewData["Type"] = "date";
                            ViewData["Required"] = false;
                            ViewData["Icon"] = "fas fa-calendar-alt";
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("HireDate");
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />

                        <!-- Working Hours -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.WorkingHours).ToString();
                            ViewData["Name"] = "WorkingHours";
                            ViewData["Type"] = "text";
                            ViewData["Required"] = false;
                            ViewData["Icon"] = "fas fa-clock";
                            ViewData["Placeholder"] = "e.g., 9:00 AM - 5:00 PM";
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("WorkingHours");
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />

                        <!-- Time Zone -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.TimeZone).ToString();
                            ViewData["Name"] = "TimeZone";
                            ViewData["Type"] = "text";
                            ViewData["Required"] = false;
                            ViewData["Icon"] = "fas fa-globe";
                            ViewData["Placeholder"] = "e.g., UTC-5, EST";
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("TimeZone");
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="card-custom">
                <div class="card-header-custom">
                    <h2 class="text-xl font-semibold text-neutral-900 dark:text-white">Additional Information</h2>
                    <p class="text-sm text-neutral-600 dark:text-neutral-400 mt-1">Optional details and notes</p>
                </div>
                <div class="card-body-custom">
                    <div class="space-y-6">
                        <!-- Bio -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.Bio).ToString();
                            ViewData["Name"] = "Bio";
                            ViewData["Type"] = "textarea";
                            ViewData["Required"] = false;
                            ViewData["Icon"] = "fas fa-user-circle";
                            ViewData["Placeholder"] = "Brief biography or description...";
                            ViewData["Rows"] = 4;
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Bio");
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />

                        <!-- Communication Preferences -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.CommunicationPreferences).ToString();
                            ViewData["Name"] = "CommunicationPreferences";
                            ViewData["Type"] = "textarea";
                            ViewData["Required"] = false;
                            ViewData["Icon"] = "fas fa-comments";
                            ViewData["Placeholder"] = "Preferred communication methods, times, frequency, etc.";
                            ViewData["Rows"] = 3;
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("CommunicationPreferences");
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />

                        <!-- Notes -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.Notes).ToString();
                            ViewData["Name"] = "Notes";
                            ViewData["Type"] = "textarea";
                            ViewData["Required"] = false;
                            ViewData["Icon"] = "fas fa-sticky-note";
                            ViewData["Placeholder"] = "Additional notes about this person...";
                            ViewData["Rows"] = 4;
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Notes");
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Person Type & Status -->
            <div class="card-custom">
                <div class="card-header-custom">
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-white">Type & Status</h3>
                </div>
                <div class="card-body-custom">
                    <div class="space-y-4">
                        <!-- Person Type -->
                        @{
                            var typeOptions = "";
                            foreach (PersonType type in Enum.GetValues<PersonType>())
                            {
                                typeOptions += $"<option value=\"{type}\">{type}</option>";
                            }

                            ViewData["Label"] = Html.DisplayNameFor(m => m.Type).ToString();
                            ViewData["Name"] = "Type";
                            ViewData["Type"] = "select";
                            ViewData["Required"] = true;
                            ViewData["Icon"] = "fas fa-tags";
                            ViewData["Options"] = "<option value=\"\">Select person type</option>" + typeOptions;
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Type");
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />

                        <!-- Status -->
                        @{
                            var statusOptions = "";
                            foreach (PersonStatus status in Enum.GetValues<PersonStatus>())
                            {
                                var selected = status == PersonStatus.Active ? "selected" : "";
                                statusOptions += $"<option value=\"{status}\" {selected}>{status}</option>";
                            }

                            ViewData["Label"] = Html.DisplayNameFor(m => m.Status).ToString();
                            ViewData["Name"] = "Status";
                            ViewData["Type"] = "select";
                            ViewData["Required"] = true;
                            ViewData["Icon"] = "fas fa-user-check";
                            ViewData["Options"] = statusOptions;
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Status");
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />
                    </div>
                </div>
            </div>

            <!-- System Access -->
            <div class="card-custom">
                <div class="card-header-custom">
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-white">System Access</h3>
                </div>
                <div class="card-body-custom">
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input asp-for="HasSystemAccess" type="checkbox" class="rounded border-neutral-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50" />
                            <label asp-for="HasSystemAccess" class="ml-2 text-sm text-neutral-700 dark:text-neutral-300">Grant system access</label>
                        </div>
                        <p class="text-sm text-neutral-500 dark:text-neutral-400">Allow this person to log into the system</p>

                        <!-- User ID -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.UserId).ToString();
                            ViewData["Name"] = "UserId";
                            ViewData["Type"] = "text";
                            ViewData["Required"] = false;
                            ViewData["Icon"] = "fas fa-user-cog";
                            ViewData["Placeholder"] = "User ID if linked to system account";
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("UserId");
                            ViewData["HelpText"] = "Link to existing user account";
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />
                    </div>
                </div>
            </div>

            <!-- Profile Picture -->
            <div class="card-custom">
                <div class="card-header-custom">
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-white">Profile Picture</h3>
                </div>
                <div class="card-body-custom">
                    <!-- Profile Picture URL -->
                    @{
                        ViewData["Label"] = Html.DisplayNameFor(m => m.ProfilePictureUrl).ToString();
                        ViewData["Name"] = "ProfilePictureUrl";
                        ViewData["Type"] = "url";
                        ViewData["Required"] = false;
                        ViewData["Icon"] = "fas fa-image";
                        ViewData["Placeholder"] = "https://example.com/photo.jpg";
                        ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("ProfilePictureUrl");
                        ViewData["HelpText"] = "URL to profile picture";
                    }
                    <partial name="Components/_FormInput" view-data="ViewData" />
                </div>
            </div>

            <!-- Form Actions -->
            <div class="card-custom">
                <div class="card-body-custom">
                    @{
                        ViewData["SubmitText"] = "Create Person";
                        ViewData["SubmitIcon"] = "fas fa-plus";
                        ViewData["CancelUrl"] = Url.Action("Index");
                        ViewData["ShowCancel"] = true;
                        ViewData["SubmitClass"] = "btn-primary-custom";
                        ViewData["CancelClass"] = "btn-secondary-custom";
                    }
                    <partial name="Components/_FormActions" view-data="ViewData" />
                </div>
            </div>
        </div>
    </div>
</form>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        $(document).ready(function() {
            // Show/hide employee ID based on person type
            $('select[name="Type"]').on('change', function() {
                const selectedType = $(this).val();
                const employeeIdField = $('input[name="EmployeeId"]').closest('.form-input-container');

                if (selectedType === 'Internal') {
                    employeeIdField.show();
                } else {
                    employeeIdField.hide();
                }
            });
        });
    </script>
}
