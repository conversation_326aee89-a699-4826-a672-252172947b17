@{
    ViewData["Title"] = "Dashboard";
    ViewBag.PageIcon = "fas fa-tachometer-alt";
    ViewBag.PageDescription = "Overview of your projects and tasks";
    ViewBag.BreadcrumbParent = null;
    Layout = "_LayoutSaaS";
}

@section PageActions {
    <button type="button" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        New Project
    </button>
    <div class="dropdown">
        <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
            <i class="fas fa-calendar me-2"></i>
            Last 30 days
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#">Last 7 days</a></li>
            <li><a class="dropdown-item" href="#">Last 30 days</a></li>
            <li><a class="dropdown-item" href="#">Last 90 days</a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item" href="#">Custom range</a></li>
        </ul>
    </div>
}

<!-- Key Metrics Row -->
<div class="row g-4 mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="card surface-elevated h-100">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <div class="text-muted small mb-1">Active Projects</div>
                        <div class="h3 mb-0 fw-bold text-primary">12</div>
                        <div class="small text-success">
                            <i class="fas fa-arrow-up me-1"></i>
                            +2 from last month
                        </div>
                    </div>
                    <div class="metric-icon bg-primary">
                        <i class="fas fa-folder-open"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card surface-elevated h-100">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <div class="text-muted small mb-1">Completed Tasks</div>
                        <div class="h3 mb-0 fw-bold text-success">248</div>
                        <div class="small text-success">
                            <i class="fas fa-arrow-up me-1"></i>
                            +15% this week
                        </div>
                    </div>
                    <div class="metric-icon bg-success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card surface-elevated h-100">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <div class="text-muted small mb-1">Team Members</div>
                        <div class="h3 mb-0 fw-bold text-info">24</div>
                        <div class="small text-muted">
                            <i class="fas fa-users me-1"></i>
                            Across 5 teams
                        </div>
                    </div>
                    <div class="metric-icon bg-info">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card surface-elevated h-100">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <div class="text-muted small mb-1">Overdue Tasks</div>
                        <div class="h3 mb-0 fw-bold text-danger">7</div>
                        <div class="small text-warning">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            Needs attention
                        </div>
                    </div>
                    <div class="metric-icon bg-danger">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Row -->
<div class="row g-4">
    <!-- Recent Projects -->
    <div class="col-lg-8">
        <div class="card surface-elevated h-100">
            <div class="card-header d-flex align-items-center justify-content-between">
                <h5 class="card-title mb-0">
                    <i class="fas fa-folder-open me-2 text-primary"></i>
                    Recent Projects
                </h5>
                <a href="@Url.Action("Index", "Projects")" class="btn btn-sm btn-outline-primary">
                    View All
                </a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Project</th>
                                <th>Progress</th>
                                <th>Team</th>
                                <th>Due Date</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="project-avatar bg-primary me-3">
                                            <i class="fas fa-rocket"></i>
                                        </div>
                                        <div>
                                            <div class="fw-medium">Mobile App Redesign</div>
                                            <div class="small text-muted">UI/UX improvements</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="progress" style="height: 6px;">
                                        <div class="progress-bar bg-primary" style="width: 75%"></div>
                                    </div>
                                    <small class="text-muted">75%</small>
                                </td>
                                <td>
                                    <div class="avatar-group">
                                        <div class="avatar">JD</div>
                                        <div class="avatar">SM</div>
                                        <div class="avatar">+2</div>
                                    </div>
                                </td>
                                <td>
                                    <span class="text-muted">Dec 15, 2024</span>
                                </td>
                                <td>
                                    <span class="badge bg-warning">In Progress</span>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="project-avatar bg-success me-3">
                                            <i class="fas fa-database"></i>
                                        </div>
                                        <div>
                                            <div class="fw-medium">API Integration</div>
                                            <div class="small text-muted">Backend services</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="progress" style="height: 6px;">
                                        <div class="progress-bar bg-success" style="width: 100%"></div>
                                    </div>
                                    <small class="text-muted">100%</small>
                                </td>
                                <td>
                                    <div class="avatar-group">
                                        <div class="avatar">AB</div>
                                        <div class="avatar">CD</div>
                                    </div>
                                </td>
                                <td>
                                    <span class="text-muted">Nov 30, 2024</span>
                                </td>
                                <td>
                                    <span class="badge bg-success">Completed</span>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="project-avatar bg-info me-3">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                        <div>
                                            <div class="fw-medium">Analytics Dashboard</div>
                                            <div class="small text-muted">Data visualization</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="progress" style="height: 6px;">
                                        <div class="progress-bar bg-info" style="width: 45%"></div>
                                    </div>
                                    <small class="text-muted">45%</small>
                                </td>
                                <td>
                                    <div class="avatar-group">
                                        <div class="avatar">EF</div>
                                        <div class="avatar">GH</div>
                                        <div class="avatar">IJ</div>
                                    </div>
                                </td>
                                <td>
                                    <span class="text-muted">Jan 20, 2025</span>
                                </td>
                                <td>
                                    <span class="badge bg-primary">Planning</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Feed -->
    <div class="col-lg-4">
        <div class="card surface-elevated h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2 text-info"></i>
                    Recent Activity
                </h5>
            </div>
            <div class="card-body">
                <div class="activity-feed">
                    <div class="activity-item">
                        <div class="activity-icon bg-success">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">Task completed</div>
                            <div class="activity-description">
                                "User authentication" marked as done
                            </div>
                            <div class="activity-time">2 minutes ago</div>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-icon bg-primary">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">New project created</div>
                            <div class="activity-description">
                                "E-commerce Platform" project started
                            </div>
                            <div class="activity-time">1 hour ago</div>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-icon bg-warning">
                            <i class="fas fa-comment"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">New comment</div>
                            <div class="activity-description">
                                John added a comment to "API Design"
                            </div>
                            <div class="activity-time">3 hours ago</div>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-icon bg-info">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">Team member added</div>
                            <div class="activity-description">
                                Sarah joined the development team
                            </div>
                            <div class="activity-time">1 day ago</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
<style>
/* Dashboard Specific Styles */
.metric-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-text-inverse);
    font-size: 1.25rem;
}

.project-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-text-inverse);
    font-size: 1rem;
}

.avatar-group {
    display: flex;
    gap: -0.5rem;
}

.avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--color-accent);
    color: var(--color-text-inverse);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
    border: 2px solid var(--color-surface);
    margin-left: -0.5rem;
}

.avatar:first-child {
    margin-left: 0;
}

.activity-feed {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.activity-item {
    display: flex;
    gap: 1rem;
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-text-inverse);
    font-size: 0.875rem;
    flex-shrink: 0;
}

.activity-content {
    flex-grow: 1;
}

.activity-title {
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: 0.25rem;
}

.activity-description {
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
    margin-bottom: 0.25rem;
}

.activity-time {
    color: var(--color-text-muted);
    font-size: var(--font-size-xs);
}

.progress {
    background-color: var(--color-bg-secondary);
    border-radius: 3px;
    margin-bottom: 0.25rem;
}

.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius-sm);
}

.table th {
    font-weight: 600;
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    border-bottom: 1px solid var(--color-border);
    padding: 1rem 1.5rem;
}

.table td {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--color-border-light);
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: var(--color-bg-secondary);
}
</style>
}

@section Scripts {
<script>
// Dashboard specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize any dashboard-specific functionality
    console.log('Dashboard loaded');
});
</script>
}
