@model PM.Tool.Core.Entities.Meeting
@using PM.Tool.Core.Enums
@{
    ViewData["Title"] = "Meeting Details";
    var attendees = ViewBag.Attendees as IEnumerable<PM.Tool.Core.Entities.MeetingAttendee> ?? new List<PM.Tool.Core.Entities.MeetingAttendee>();
    var actionItems = ViewBag.ActionItems as IEnumerable<PM.Tool.Core.Entities.MeetingActionItem> ?? new List<PM.Tool.Core.Entities.MeetingActionItem>();
    var documents = ViewBag.Documents as IEnumerable<PM.Tool.Core.Entities.MeetingDocument> ?? new List<PM.Tool.Core.Entities.MeetingDocument>();
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Meetings", Href = Url.Action("Index", "Meeting"), Icon = "fas fa-calendar" },
        new { Text = Model.Title, Href = "", Icon = "fas fa-info-circle" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-calendar mr-3 text-primary-600 dark:text-primary-400"></i>
                @Model.Title
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                @Model.Type meeting • @Model.ScheduledDate.ToString("MMM dd, yyyy 'at' HH:mm")
            </p>
        </div>
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
            @if (Model.Status == MeetingStatus.Scheduled)
            {
                ViewData["Text"] = "Start Meeting";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-play";
                ViewData["OnClick"] = $"startMeeting({Model.Id})";
                <partial name="Components/_Button" view-data="ViewData" />
            }

            @if (Model.Status != MeetingStatus.Completed && Model.Status != MeetingStatus.Cancelled)
            {
                ViewData["Text"] = "Edit";
                ViewData["Variant"] = "outline";
                ViewData["Icon"] = "fas fa-edit";
                ViewData["Href"] = Url.Action("Edit", new { id = Model.Id });
                <partial name="Components/_Button" view-data="ViewData" />
            }

            @{
                ViewData["Text"] = "Back to Meetings";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Index");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Meeting Status Alert -->
@if (Model.IsOverdue)
{
    <div class="alert-danger-custom mb-6">
        <i class="fas fa-exclamation-triangle text-danger-600 dark:text-danger-400"></i>
        <div>
            <p class="font-medium">Meeting Overdue</p>
            <p class="text-sm">This meeting was scheduled for @Model.ScheduledDate.ToString("MMM dd, yyyy 'at' HH:mm") and is now overdue.</p>
        </div>
    </div>
}

<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-8">
        <!-- Meeting Information -->
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-info-circle text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Meeting Information</h3>
                        <p class="text-sm text-neutral-500 dark:text-dark-400">Basic meeting details</p>
                    </div>
                </div>
            </div>
            <div class="card-body-custom">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Description</h4>
                        <p class="text-neutral-900 dark:text-dark-100">@(Model.Description ?? "No description provided")</p>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Status</h4>
                        @{
                            var statusClass = Model.Status.ToString().ToLower() switch {
                                "scheduled" => "bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200",
                                "inprogress" => "bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200",
                                "completed" => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
                                "cancelled" => "bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200",
                                _ => "bg-neutral-100 dark:bg-dark-600 text-neutral-800 dark:text-dark-200"
                            };
                        }
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium @statusClass">
                            @Model.Status
                        </span>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Date & Time</h4>
                        <p class="text-neutral-900 dark:text-dark-100">
                            <i class="fas fa-calendar mr-2 text-neutral-400 dark:text-dark-500"></i>
                            @Model.ScheduledDate.ToString("dddd, MMMM dd, yyyy")
                        </p>
                        <p class="text-neutral-900 dark:text-dark-100 mt-1">
                            <i class="fas fa-clock mr-2 text-neutral-400 dark:text-dark-500"></i>
                            @Model.ScheduledDate.ToString("HH:mm") - @Model.EndTime.ToString("HH:mm") (@Model.DurationMinutes min)
                        </p>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.Location))
                    {
                        <div>
                            <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Location</h4>
                            <p class="text-neutral-900 dark:text-dark-100">
                                <i class="fas fa-map-marker-alt mr-2 text-neutral-400 dark:text-dark-500"></i>
                                @Model.Location
                            </p>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.MeetingLink))
                    {
                        <div>
                            <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Meeting Link</h4>
                            <a href="@Model.MeetingLink" target="_blank" class="text-primary-600 dark:text-primary-400 hover:underline">
                                <i class="fas fa-external-link-alt mr-2"></i>
                                Join Meeting
                            </a>
                        </div>
                    }

                    <div>
                        <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Organizer</h4>
                        <p class="text-neutral-900 dark:text-dark-100">
                            <i class="fas fa-user mr-2 text-neutral-400 dark:text-dark-500"></i>
                            @Model.Organizer?.UserName
                        </p>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Project</h4>
                        <p class="text-neutral-900 dark:text-dark-100">
                            <i class="fas fa-project-diagram mr-2 text-neutral-400 dark:text-dark-500"></i>
                            @if (Model.Project != null)
                            {
                                <a href="@Url.Action("Details", "Projects", new { id = Model.ProjectId })" class="text-primary-600 dark:text-primary-400 hover:underline">
                                    @Model.Project.Name
                                </a>
                            }
                            else
                            {
                                <span>No project assigned</span>
                            }
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Agenda -->
        @if (!string.IsNullOrEmpty(Model.Agenda))
        {
            <div class="card-custom">
                <div class="card-header-custom">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-warning-100 dark:bg-warning-900 rounded-lg flex items-center justify-center">
                            <i class="fas fa-list text-warning-600 dark:text-warning-400 text-sm"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Agenda</h3>
                            <p class="text-sm text-neutral-500 dark:text-dark-400">Meeting agenda and topics</p>
                        </div>
                    </div>
                </div>
                <div class="card-body-custom">
                    <div class="prose prose-neutral dark:prose-invert max-w-none">
                        @Html.Raw(Model.Agenda.Replace("\n", "<br>"))
                    </div>
                </div>
            </div>
        }

        <!-- Meeting Minutes -->
        @if (!string.IsNullOrEmpty(Model.Minutes))
        {
            <div class="card-custom">
                <div class="card-header-custom">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-success-100 dark:bg-success-900 rounded-lg flex items-center justify-center">
                            <i class="fas fa-file-alt text-success-600 dark:text-success-400 text-sm"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Meeting Minutes</h3>
                            <p class="text-sm text-neutral-500 dark:text-dark-400">Notes and discussion points</p>
                        </div>
                    </div>
                </div>
                <div class="card-body-custom">
                    <div class="prose prose-neutral dark:prose-invert max-w-none">
                        @Html.Raw(Model.Minutes.Replace("\n", "<br>"))
                    </div>
                </div>
            </div>
        }

        <!-- Action Items -->
        @if (actionItems.Any())
        {
            <div class="card-custom">
                <div class="card-header-custom">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-danger-100 dark:bg-danger-900 rounded-lg flex items-center justify-center">
                                <i class="fas fa-tasks text-danger-600 dark:text-danger-400 text-sm"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Action Items</h3>
                                <p class="text-sm text-neutral-500 dark:text-dark-400">Follow-up tasks from this meeting</p>
                            </div>
                        </div>
                        @{
                            ViewData["Text"] = "Manage Action Items";
                            ViewData["Variant"] = "outline";
                            ViewData["Icon"] = "fas fa-cog";
                            ViewData["Size"] = "sm";
                            ViewData["Href"] = Url.Action("ActionItems", new { id = Model.Id });
                        }
                        <partial name="Components/_Button" view-data="ViewData" />
                    </div>
                </div>
                <div class="card-body-custom">
                    <div class="space-y-4">
                        @foreach (var item in actionItems)
                        {
                            <div class="flex items-start space-x-4 p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                                <div class="flex-shrink-0">
                                    @{
                                        var itemStatusClass = item.Status.ToString().ToLower() switch {
                                            "completed" => "text-success-600 dark:text-success-400",
                                            "inprogress" => "text-primary-600 dark:text-primary-400",
                                            "overdue" => "text-danger-600 dark:text-danger-400",
                                            _ => "text-neutral-400 dark:text-dark-500"
                                        };
                                    }
                                    <i class="fas fa-circle @itemStatusClass"></i>
                                </div>
                                <div class="flex-1">
                                    <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-100">@item.Description</h4>
                                    @if (!string.IsNullOrEmpty(item.Notes))
                                    {
                                        <p class="text-sm text-neutral-600 dark:text-dark-300 mt-1">@item.Notes</p>
                                    }
                                    <div class="flex items-center space-x-4 mt-2 text-xs text-neutral-500 dark:text-dark-400">
                                        @if (item.AssignedTo != null)
                                        {
                                            <span><i class="fas fa-user mr-1"></i>@item.AssignedTo.UserName</span>
                                        }
                                        @if (item.DueDate.HasValue)
                                        {
                                            <span><i class="fas fa-calendar mr-1"></i>Due @item.DueDate.Value.ToString("MMM dd")</span>
                                        }
                                        <span class="capitalize">@item.Status</span>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        }
    </div>

    <!-- Sidebar -->
    <div class="space-y-6">
        <!-- Attendees -->
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-info-100 dark:bg-info-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-info-600 dark:text-info-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Attendees</h3>
                        <p class="text-sm text-neutral-500 dark:text-dark-400">@attendees.Count() participants</p>
                    </div>
                </div>
            </div>
            <div class="card-body-custom">
                @if (attendees.Any())
                {
                    <div class="space-y-3">
                        @foreach (var attendee in attendees)
                        {
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-neutral-200 dark:bg-dark-600 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-neutral-600 dark:text-dark-300 text-xs"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-neutral-900 dark:text-dark-100">@attendee.User?.UserName</p>
                                        <p class="text-xs text-neutral-500 dark:text-dark-400 capitalize">@attendee.Role</p>
                                    </div>
                                </div>
                                @{
                                    var attendanceClass = attendee.Status.ToString().ToLower() switch {
                                        "accepted" => "text-success-600 dark:text-success-400",
                                        "declined" => "text-danger-600 dark:text-danger-400",
                                        "tentative" => "text-warning-600 dark:text-warning-400",
                                        _ => "text-neutral-400 dark:text-dark-500"
                                    };
                                }
                                <span class="text-xs @attendanceClass capitalize">@attendee.Status</span>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <p class="text-sm text-neutral-500 dark:text-dark-400 text-center py-4">No attendees added</p>
                }
            </div>
        </div>

        <!-- Documents -->
        @if (documents.Any())
        {
            <div class="card-custom">
                <div class="card-header-custom">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                            <i class="fas fa-file text-purple-600 dark:text-purple-400 text-sm"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Documents</h3>
                            <p class="text-sm text-neutral-500 dark:text-dark-400">@documents.Count() files</p>
                        </div>
                    </div>
                </div>
                <div class="card-body-custom">
                    <div class="space-y-3">
                        @foreach (var doc in documents)
                        {
                            <div class="flex items-center space-x-3 p-3 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                                <i class="fas fa-file-alt text-neutral-400 dark:text-dark-500"></i>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-neutral-900 dark:text-dark-100 truncate">@doc.FileName</p>
                                    <p class="text-xs text-neutral-500 dark:text-dark-400">@doc.Type • @((doc.FileSize / 1024).ToString("F0")) KB</p>
                                </div>
                                <a href="@Url.Action("DownloadDocument", new { id = doc.Id })" class="text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-200">
                                    <i class="fas fa-download"></i>
                                </a>
                            </div>
                        }
                    </div>
                </div>
            </div>
        }

        <!-- Quick Actions -->
        <div class="card-custom">
            <div class="card-header-custom">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Quick Actions</h3>
            </div>
            <div class="card-body-custom space-y-3">
                @{
                    ViewData["Text"] = "Add Action Item";
                    ViewData["Variant"] = "outline";
                    ViewData["Icon"] = "fas fa-plus";
                    ViewData["FullWidth"] = true;
                    ViewData["OnClick"] = $"addActionItem({Model.Id})";
                }
                <partial name="Components/_Button" view-data="ViewData" />

                @{
                    ViewData["Text"] = "Upload Document";
                    ViewData["Variant"] = "outline";
                    ViewData["Icon"] = "fas fa-upload";
                    ViewData["FullWidth"] = true;
                    ViewData["OnClick"] = $"uploadDocument({Model.Id})";
                }
                <partial name="Components/_Button" view-data="ViewData" />

                @{
                    ViewData["Text"] = "Send Summary";
                    ViewData["Variant"] = "outline";
                    ViewData["Icon"] = "fas fa-envelope";
                    ViewData["FullWidth"] = true;
                    ViewData["OnClick"] = $"sendSummary({Model.Id})";
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function startMeeting(meetingId) {
            if (confirm('Start this meeting now?')) {
                $.post('@Url.Action("StartMeeting", "Meeting")', { id: meetingId })
                    .done(function() {
                        location.reload();
                    })
                    .fail(function() {
                        alert('Failed to start meeting. Please try again.');
                    });
            }
        }

        function addActionItem(meetingId) {
            // Implementation for adding action item
            window.location.href = '@Url.Action("ActionItems", "Meeting")' + '/' + meetingId + '?action=add';
        }

        function uploadDocument(meetingId) {
            // Implementation for document upload
            alert('Document upload functionality to be implemented');
        }

        function sendSummary(meetingId) {
            if (confirm('Send meeting summary to all attendees?')) {
                $.post('@Url.Action("SendSummary", "Meeting")', { id: meetingId })
                    .done(function() {
                        alert('Meeting summary sent successfully!');
                    })
                    .fail(function() {
                        alert('Failed to send summary. Please try again.');
                    });
            }
        }
    </script>
}
