using PM.Tool.Core.Entities;

namespace PM.Tool.Core.Interfaces
{
    /// <summary>
    /// Service interface for unified person management
    /// Handles all person-related operations including employees, stakeholders, contractors, etc.
    /// </summary>
    public interface IPersonService
    {
        // Core Person Management
        Task<IEnumerable<Person>> GetAllPeopleAsync();
        Task<IEnumerable<Person>> GetActivePeopleAsync();
        Task<IEnumerable<Person>> GetPeopleByTypeAsync(PersonType type);
        Task<IEnumerable<Person>> GetPeopleByStatusAsync(PersonStatus status);
        Task<Person?> GetPersonByIdAsync(int id);
        Task<Person?> GetPersonByCodeAsync(string personCode);
        Task<Person?> GetPersonByEmailAsync(string email);
        Task<Person?> GetPersonByUserIdAsync(string userId);
        Task<Person> CreatePersonAsync(Person person);
        Task<Person> UpdatePersonAsync(Person person);
        Task<bool> DeletePersonAsync(int id);
        Task<bool> DeactivatePersonAsync(int id);
        Task<bool> ActivatePersonAsync(int id);

        // Person Search and Filtering
        Task<IEnumerable<Person>> SearchPeopleAsync(string searchTerm);
        Task<IEnumerable<Person>> GetPeopleByDepartmentAsync(string department);
        Task<IEnumerable<Person>> GetPeopleByOrganizationAsync(string organization);
        Task<IEnumerable<Person>> GetPeopleByLocationAsync(string location);
        Task<IEnumerable<Person>> GetPeopleWithSystemAccessAsync();
        Task<IEnumerable<Person>> GetPeopleWithoutSystemAccessAsync();

        // Role Management
        Task<IEnumerable<PersonRole>> GetPersonRolesAsync(int personId);
        Task<IEnumerable<PersonRole>> GetActivePersonRolesAsync(int personId);
        Task<IEnumerable<PersonRole>> GetPersonRolesByScopeAsync(int personId, RoleScope scope);
        Task<PersonRole> AssignRoleAsync(int personId, string roleCode, string roleName, RoleScope scope, string? scopeId = null, string? scopeName = null);
        Task<bool> RemoveRoleAsync(int personRoleId);
        Task<bool> DeactivateRoleAsync(int personRoleId);
        Task<IEnumerable<Person>> GetPeopleByRoleAsync(string roleCode, RoleScope? scope = null, string? scopeId = null);

        // Skills Management
        Task<IEnumerable<PersonSkill>> GetPersonSkillsAsync(int personId);
        Task<IEnumerable<PersonSkill>> GetVerifiedPersonSkillsAsync(int personId);
        Task<PersonSkill> AddPersonSkillAsync(int personId, int skillId, SkillLevel level, int yearsOfExperience);
        Task<PersonSkill> UpdatePersonSkillAsync(PersonSkill personSkill);
        Task<bool> RemovePersonSkillAsync(int personSkillId);
        Task<bool> VerifyPersonSkillAsync(int personSkillId, string verifiedByUserId);
        Task<IEnumerable<Person>> GetPeopleBySkillAsync(int skillId, SkillLevel? minimumLevel = null);
        Task<IEnumerable<Person>> GetPeopleBySkillsAsync(IEnumerable<int> skillIds, bool requireAll = false);

        // Project Assignment Management
        Task<IEnumerable<PersonProject>> GetPersonProjectsAsync(int personId);
        Task<IEnumerable<PersonProject>> GetActivePersonProjectsAsync(int personId);
        Task<IEnumerable<PersonProject>> GetProjectPeopleAsync(int projectId);
        Task<PersonProject> AssignPersonToProjectAsync(int personId, int projectId, string projectRole, decimal allocationPercentage = 100);
        Task<PersonProject> UpdateProjectAssignmentAsync(PersonProject personProject);
        Task<bool> RemovePersonFromProjectAsync(int personProjectId);
        Task<bool> DeactivateProjectAssignmentAsync(int personProjectId);
        Task<IEnumerable<Person>> GetAvailablePeopleForProjectAsync(int projectId, DateTime startDate, DateTime? endDate = null);

        // Stakeholder Management
        Task<PersonStakeholder?> GetPersonStakeholderInfoAsync(int personId);
        Task<PersonStakeholder> CreateOrUpdateStakeholderInfoAsync(int personId, StakeholderType type, InfluenceLevel influence, InterestLevel interest);
        Task<IEnumerable<Person>> GetStakeholdersAsync();
        Task<IEnumerable<Person>> GetStakeholdersByTypeAsync(StakeholderType type);
        Task<IEnumerable<Person>> GetStakeholdersByInfluenceAsync(InfluenceLevel influence);
        Task<IEnumerable<Person>> GetStakeholdersByInterestAsync(InterestLevel interest);
        Task<IEnumerable<Person>> GetKeyStakeholdersAsync(); // High influence + High interest

        // Resource Management
        Task<PersonResource?> GetPersonResourceInfoAsync(int personId);
        Task<PersonResource> CreateOrUpdateResourceInfoAsync(int personId, decimal hourlyRate, decimal dailyCapacity);
        Task<IEnumerable<Person>> GetResourcesAsync();
        Task<IEnumerable<Person>> GetAvailableResourcesAsync();
        Task<IEnumerable<Person>> GetResourcesByCapacityAsync(decimal minimumCapacity);
        Task<IEnumerable<Person>> GetResourcesByRateRangeAsync(decimal minRate, decimal maxRate);

        // Contact Management
        Task<IEnumerable<PersonContact>> GetPersonContactsAsync(int personId);
        Task<PersonContact> AddPersonContactAsync(int personId, ContactType type, string value, string? label = null, bool isPrimary = false);
        Task<PersonContact> UpdatePersonContactAsync(PersonContact contact);
        Task<bool> RemovePersonContactAsync(int contactId);

        // Availability Management
        Task<IEnumerable<PersonAvailability>> GetPersonAvailabilityAsync(int personId, DateTime? startDate = null, DateTime? endDate = null);
        Task<PersonAvailability> AddPersonAvailabilityAsync(int personId, DateTime startDate, DateTime endDate, AvailabilityType type, decimal availabilityPercentage = 100);
        Task<PersonAvailability> UpdatePersonAvailabilityAsync(PersonAvailability availability);
        Task<bool> RemovePersonAvailabilityAsync(int availabilityId);
        Task<IEnumerable<Person>> GetAvailablePeopleAsync(DateTime startDate, DateTime endDate, decimal minimumAvailability = 50);

        // Reporting and Analytics
        Task<Dictionary<PersonType, int>> GetPersonTypeDistributionAsync();
        Task<Dictionary<PersonStatus, int>> GetPersonStatusDistributionAsync();
        Task<Dictionary<string, int>> GetDepartmentDistributionAsync();
        Task<Dictionary<string, int>> GetSkillDistributionAsync();
        Task<Dictionary<string, int>> GetRoleDistributionAsync();
        Task<IEnumerable<Person>> GetPeopleWithExpiringCertificationsAsync(int daysAhead = 30);
        Task<IEnumerable<Person>> GetUnderutilizedResourcesAsync(decimal maxUtilization = 50);
        Task<IEnumerable<Person>> GetOverallocatedResourcesAsync(decimal maxAllocation = 100);

        // Bulk Operations
        Task<IEnumerable<Person>> CreatePeopleBulkAsync(IEnumerable<Person> people);
        Task<bool> UpdatePeopleStatusBulkAsync(IEnumerable<int> personIds, PersonStatus status);
        Task<bool> AssignRoleBulkAsync(IEnumerable<int> personIds, string roleCode, string roleName, RoleScope scope);
        Task<bool> DeactivatePeopleBulkAsync(IEnumerable<int> personIds);

        // Integration and Synchronization
        Task<Person?> SyncWithUserAsync(string userId); // Sync with ApplicationUser
        Task<bool> CreateSystemUserAsync(int personId, string email, string password); // Create ApplicationUser
        Task<bool> LinkToSystemUserAsync(int personId, string userId); // Link existing user
        Task<bool> UnlinkFromSystemUserAsync(int personId); // Remove system access

        // Validation and Business Rules
        Task<bool> IsPersonCodeUniqueAsync(string personCode, int? excludePersonId = null);
        Task<bool> IsEmailUniqueAsync(string email, int? excludePersonId = null);
        Task<bool> CanAssignToProjectAsync(int personId, int projectId, DateTime startDate, DateTime? endDate = null);
        Task<bool> HasRequiredSkillsAsync(int personId, IEnumerable<int> requiredSkillIds);
        Task<decimal> GetPersonUtilizationAsync(int personId, DateTime startDate, DateTime endDate);
    }
}
