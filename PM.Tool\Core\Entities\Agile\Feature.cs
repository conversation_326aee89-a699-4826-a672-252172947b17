using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities.Agile
{
    public class Feature : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [MaxLength(5000)]
        public string Description { get; set; } = string.Empty;

        public int EpicId { get; set; }
        public int ProjectId { get; set; }

        [MaxLength(50)]
        public string FeatureKey { get; set; } = string.Empty; // FT-001

        public FeatureStatus Status { get; set; } = FeatureStatus.Draft;
        public FeaturePriority Priority { get; set; } = FeaturePriority.Medium;

        [MaxLength(2000)]
        public string? BusinessValue { get; set; }

        [MaxLength(2000)]
        public string? AcceptanceCriteria { get; set; }

        public decimal EstimatedStoryPoints { get; set; }
        public decimal ActualStoryPoints { get; set; }

        public string? OwnerId { get; set; }

        private DateTime? _targetDate;
        public DateTime? TargetDate
        {
            get => _targetDate;
            set => _targetDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        private DateTime? _completedDate;
        public DateTime? CompletedDate
        {
            get => _completedDate;
            set => _completedDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        [MaxLength(500)]
        public string? Tags { get; set; } // JSON array

        public int SortOrder { get; set; }

        // Navigation properties
        public virtual Epic Epic { get; set; } = null!;
        public virtual Project Project { get; set; } = null!;
        public virtual ApplicationUser? Owner { get; set; }
        public virtual ICollection<UserStory> UserStories { get; set; } = new List<UserStory>();

        // Computed properties
        public bool IsCompleted => Status == FeatureStatus.Done;
        public bool IsOverdue => TargetDate.HasValue && DateTime.UtcNow > TargetDate.Value && !IsCompleted;
        public double ProgressPercentage => EstimatedStoryPoints > 0 ? (double)ActualStoryPoints / (double)EstimatedStoryPoints * 100 : 0;
        public int UserStoryCount => UserStories.Count;
        public int CompletedUserStoryCount => UserStories.Count(us => us.IsCompleted);
    }

    public enum FeatureStatus
    {
        Draft = 1,
        Ready = 2,
        InProgress = 3,
        Review = 4,
        Testing = 5,
        Done = 6,
        Completed = 7,
        OnHold = 8,
        Cancelled = 9
    }

    public enum FeaturePriority
    {
        Critical = 1,
        High = 2,
        Medium = 3,
        Low = 4
    }
}
