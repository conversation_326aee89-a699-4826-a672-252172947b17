using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities
{
    public class TaskAttachment
    {
        public int Id { get; set; }

        [Required]
        [MaxLength(255)]
        public string FileName { get; set; } = string.Empty;

        [Required]
        [MaxLength(500)]
        public string FilePath { get; set; } = string.Empty;

        [MaxLength(100)]
        public string? ContentType { get; set; }

        public long FileSize { get; set; }

        public int TaskId { get; set; }

        public int? CategoryId { get; set; }

        [MaxLength(500)]
        public string? Description { get; set; }

        [MaxLength(100)]
        public string? Tags { get; set; }

        public int CurrentVersion { get; set; }

        public string UploadedByUserId { get; set; } = string.Empty;

        public DateTime UploadedAt { get; set; } = DateTime.UtcNow;

        public DateTime? LastModifiedAt { get; set; }

        // Navigation properties
        public virtual TaskEntity Task { get; set; } = null!;
        public virtual DocumentCategory? Category { get; set; }
        public virtual ApplicationUser UploadedBy { get; set; } = null!;
        public virtual ICollection<DocumentVersion> Versions { get; set; } = new List<DocumentVersion>();
    }
}
