using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

using PM.Tool.Core.Constants;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Extensions;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;
using PM.Tool.Models.ViewModels;

namespace PM.Tool.Controllers
{
    [Authorize]
    public class CommentController : SecureBaseController
    {
        private readonly ApplicationDbContext _context;
        private readonly INotificationService _notificationService;

        public CommentController(
            ApplicationDbContext context,
            INotificationService notificationService,
            IAuditService auditService) : base(auditService)
        {
            _context = context;
            _notificationService = notificationService;
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CommentViewModel model)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);            var task = await _context.Tasks
                .Include(t => t.AssignedTo)
                .Include(t => t.Project)
                .FirstOrDefaultAsync(t => t.Id == model.TaskId);

            if (task == null)
                return NotFound();

            var comment = new Comment
            {
                Content = model.Content,
                TaskId = model.TaskId,
                AuthorId = User.GetUserId(),
                CreatedAt = DateTime.UtcNow
            };

            _context.Comments.Add(comment);
            await _context.SaveChangesAsync();

            await LogAuditAsync(AuditAction.Create, "Comment", comment.Id);            // Notify task assignee
            if (task.AssignedToUserId != User.GetUserId())
            {
                await _notificationService.NotifyCommentAddedAsync(task.Id, User.GetUserId());
            }

            return Json(new
            {
                id = comment.Id,
                content = comment.FormattedContent,
                authorName = User.Identity.Name,
                createdAt = comment.CreatedAt
            });
        }

        [HttpPut]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, CommentViewModel model)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            var comment = await _context.Comments.FindAsync(id);

            if (comment == null)
                return NotFound();

            if (comment.AuthorId != User.GetUserId() && !User.IsInRole(Roles.Admin))
                return Forbid();

            comment.Content = model.Content;
            comment.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            await LogAuditAsync(AuditAction.Update, "Comment", comment.Id);

            return Json(new
            {
                content = comment.FormattedContent,
                updatedAt = comment.UpdatedAt
            });
        }

        [HttpDelete]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            var comment = await _context.Comments.FindAsync(id);

            if (comment == null)
                return NotFound();

            if (comment.AuthorId != User.GetUserId() && !User.IsInRole(Roles.Admin))
                return Forbid();

            _context.Comments.Remove(comment);
            await _context.SaveChangesAsync();
            await LogAuditAsync(AuditAction.Delete, "Comment", comment.Id);

            return Ok();
        }
    }
}