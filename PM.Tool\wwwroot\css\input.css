@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Component Classes */
@layer components {
  /* Button Components */
  .btn-primary-custom {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-secondary-custom {
    @apply bg-white dark:bg-dark-900 hover:bg-neutral-50 dark:hover:bg-dark-800 text-neutral-700 dark:text-dark-100 font-medium py-2.5 px-4 rounded-lg border border-neutral-300 dark:border-dark-600 transition-all duration-200 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-outline-custom {
    @apply bg-transparent hover:bg-primary-50 dark:hover:bg-primary-900/20 text-primary-600 dark:text-primary-400 font-medium py-2.5 px-4 rounded-lg border border-primary-300 dark:border-primary-600 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-danger-custom {
    @apply bg-danger-600 hover:bg-danger-700 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-danger-500 focus:ring-offset-2;
  }

  .btn-success-custom {
    @apply bg-success-600 hover:bg-success-700 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-success-500 focus:ring-offset-2;
  }

  /* Card Components */
  .card-custom {
    @apply bg-white dark:bg-dark-900 border border-neutral-200 dark:border-dark-600 rounded-xl shadow-soft hover:shadow-medium transition-all duration-300;
  }

  .card-header-custom {
    @apply bg-neutral-50 dark:bg-dark-800 border-b border-neutral-200 dark:border-dark-600 px-6 py-4 rounded-t-xl;
  }

  .card-body-custom {
    @apply p-6;
  }

  .card-footer-custom {
    @apply bg-neutral-50 dark:bg-dark-800 border-t border-neutral-200 dark:border-dark-600 px-6 py-4 rounded-b-xl;
  }

  /* Form Components - Enhanced Dark Mode */
  .form-input-custom {
    @apply w-full px-3 py-2.5 border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-gray-800 text-neutral-900 dark:text-white placeholder-neutral-500 dark:placeholder-neutral-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }

  .form-label-custom {
    @apply block text-sm font-medium text-neutral-700 dark:text-white mb-2;
  }

  .form-select-custom {
    @apply w-full px-3 py-2.5 border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-gray-800 text-neutral-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
  }

  /* Dark mode dropdown arrow */
  .dark .form-select-custom {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23d1d5db' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  }

  /* Select with left icon - adjust padding to accommodate both icon and dropdown arrow */
  .form-select-custom.pl-10 {
    @apply pl-10;
    padding-right: 2.5rem;
  }

  /* Select with right icon - adjust padding to accommodate both icon and dropdown arrow */
  .form-select-custom.pr-10 {
    @apply pl-3;
    padding-right: 4rem;
  }

  /* Alert Components */
  .alert-success-custom {
    @apply bg-success-50 dark:bg-success-900/20 border border-success-200 dark:border-success-800 text-success-800 dark:text-success-200 px-4 py-3 rounded-lg flex items-start space-x-3;
  }

  .alert-danger-custom {
    @apply bg-danger-50 dark:bg-danger-900/20 border border-danger-200 dark:border-danger-800 text-danger-800 dark:text-danger-200 px-4 py-3 rounded-lg flex items-start space-x-3;
  }

  .alert-warning-custom {
    @apply bg-warning-50 dark:bg-warning-900/20 border border-warning-200 dark:border-warning-800 text-warning-800 dark:text-warning-200 px-4 py-3 rounded-lg flex items-start space-x-3;
  }

  .alert-info-custom {
    @apply bg-info-50 dark:bg-info-900/20 border border-info-200 dark:border-info-800 text-info-800 dark:text-info-200 px-4 py-3 rounded-lg flex items-start space-x-3;
  }

  /* Navigation Components - Enhanced Dark Mode */
  .nav-link-custom {
    @apply text-neutral-600 dark:text-white hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 rounded-lg font-medium transition-colors duration-200;
  }

  .nav-link-active {
    @apply text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20;
  }

  /* Sidebar Components - Enhanced Dark Mode */
  .sidebar-link {
    @apply flex items-center px-3 py-2.5 text-neutral-700 dark:text-white hover:bg-neutral-100 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400 rounded-lg transition-all duration-200;
  }

  .sidebar-link-active {
    @apply bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400 border-l-2 border-primary-600;
  }

  /* Custom Scrollbar for Sidebar */
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thumb-neutral-300::-webkit-scrollbar-thumb {
    background-color: #d1d5db;
    border-radius: 0.375rem;
  }

  .scrollbar-track-transparent::-webkit-scrollbar-track {
    background-color: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .dark .scrollbar-thumb-neutral-600::-webkit-scrollbar-thumb {
    background-color: #4b5563;
  }

  .dark .hover\:scrollbar-thumb-neutral-500:hover::-webkit-scrollbar-thumb {
    background-color: #6b7280;
  }

  /* Modal Components */
  .modal-custom {
    @apply fixed inset-0 z-50 overflow-y-auto;
  }

  .modal-backdrop {
    @apply fixed inset-0 bg-black bg-opacity-50 transition-opacity;
  }

  .modal-content-custom {
    @apply bg-white dark:bg-surface-dark rounded-xl shadow-strong max-w-lg mx-auto mt-20 border border-neutral-200 dark:border-dark-200;
  }

  .modal-header-custom {
    @apply px-6 py-4 border-b border-neutral-200 dark:border-dark-200;
  }

  .modal-body-custom {
    @apply px-6 py-4;
  }

  .modal-footer-custom {
    @apply px-6 py-4 border-t border-neutral-200 dark:border-dark-200 flex justify-end space-x-3;
  }

  /* Table Components */
  .table-custom {
    @apply w-full border-collapse bg-white dark:bg-surface-dark rounded-lg overflow-hidden shadow-soft;
  }

  .table-header-custom {
    @apply bg-neutral-50 dark:bg-surface-dark-secondary;
  }

  .table-cell-custom {
    @apply px-6 py-4 border-b border-neutral-200 dark:border-dark-200 text-neutral-900 dark:text-neutral-100;
  }

  .table-header-cell-custom {
    @apply px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider;
  }

  /* Stats Card Components */
  .stats-card-custom {
    @apply bg-white dark:bg-surface-dark border border-neutral-200 dark:border-dark-200 rounded-xl p-6 shadow-soft hover:shadow-medium transition-all duration-300;
  }

  /* Enhanced Text Visibility for Dark Mode */
  .text-primary-dark {
    @apply text-neutral-900 dark:text-neutral-100;
  }

  .text-secondary-dark {
    @apply text-neutral-600 dark:text-neutral-300;
  }

  .text-muted-dark {
    @apply text-neutral-500 dark:text-neutral-400;
  }

  /* Better contrast for headings in dark mode */
  .heading-dark {
    @apply text-neutral-900 dark:text-white font-semibold;
  }

  .subheading-dark {
    @apply text-neutral-700 dark:text-neutral-200;
  }

  /* Professional Component Enhancements */
  .suggestion-item.selected {
    @apply bg-primary-50 dark:bg-primary-900/20 border-l-2 border-primary-600;
  }

  .breadcrumb-item:hover {
    @apply text-primary-600 dark:text-primary-400;
  }

  /* Loading animations */
  .skeleton-pulse {
    @apply animate-pulse bg-neutral-300 dark:bg-dark-600 rounded;
  }

  /* Enhanced focus states */
  .focus-ring-enhanced {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-neutral-900;
  }

  /* Professional shadows */
  .shadow-professional {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .shadow-professional-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
}

/* Custom Utilities */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}

/* Dark mode root styles */
.dark {
  color-scheme: dark;
}

/* Enhanced dark mode text visibility */
.dark body {
  color: #ffffff !important; /* Pure white for body text */
}

.dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6 {
  color: #ffffff !important;
  font-weight: 600;
}

.dark p {
  color: #ffffff !important; /* Pure white for paragraphs */
}

.dark .text-muted {
  color: #d1d5db !important; /* Much lighter for muted text */
}

.dark .text-neutral-500 {
  color: #d1d5db !important; /* Much better contrast */
}

.dark .text-neutral-600 {
  color: #f3f4f6 !important; /* Very light for better visibility */
}

.dark .text-neutral-700 {
  color: #ffffff !important; /* Pure white for better visibility */
}

.dark .text-neutral-900 {
  color: #ffffff !important; /* Override dark text to white */
}

/* Custom dark theme text classes - Override existing */
.dark .dark\\:text-dark-100 {
  color: #ffffff !important;
}

.dark .dark\\:text-dark-200 {
  color: #f3f4f6 !important;
}

.dark .dark\\:text-dark-300 {
  color: #e5e7eb !important;
}

.dark .dark\\:text-dark-400 {
  color: #d1d5db !important;
}

.dark .dark\\:text-dark-500 {
  color: #9ca3af !important;
}

/* Improve link visibility in dark mode */
.dark a {
  color: #60a5fa; /* blue-400 */
}

.dark a:hover {
  color: #93c5fd; /* blue-300 */
}

/* Better form text visibility */
.dark input, .dark textarea, .dark select {
  color: #ffffff !important;
}

.dark input::placeholder, .dark textarea::placeholder {
  color: #9ca3af !important; /* neutral-400 */
}

/* Better button text visibility */
.dark .btn-secondary-custom {
  color: #f3f4f6 !important;
}

/* Enhanced Dropdown and Menu Visibility - MAXIMUM CONTRAST */
.dark .dropdown-menu {
  background-color: #111827 !important; /* Very dark background */
  border: 2px solid #6b7280 !important; /* Visible border */
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.5) !important;
}

.dark .dropdown-item {
  color: #ffffff !important;
  font-weight: 500 !important;
  padding: 12px 16px !important;
}

.dark .dropdown-item:hover {
  background-color: #1f2937 !important;
  color: #ffffff !important;
}

/* Menu Items - MAXIMUM VISIBILITY */
.dark [id$="-menu"] {
  background-color: #111827 !important;
  border: 2px solid #6b7280 !important;
  color: #ffffff !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.5) !important;
}

.dark [id$="-menu"] a,
.dark [id$="-menu"] button,
.dark [id$="-menu"] div {
  color: #ffffff !important;
  font-weight: 500 !important;
}

.dark [id$="-menu"] a:hover,
.dark [id$="-menu"] button:hover {
  background-color: #1f2937 !important;
  color: #ffffff !important;
}

/* Topbar Menu Items - ENHANCED */
.dark #user-menu,
.dark #language-menu,
.dark #quick-actions-menu {
  background-color: #111827 !important;
  border: 2px solid #6b7280 !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.5) !important;
}

.dark #user-menu a,
.dark #language-menu a,
.dark #quick-actions-menu a,
.dark #user-menu button,
.dark #language-menu button,
.dark #quick-actions-menu button,
.dark #user-menu div,
.dark #language-menu div,
.dark #quick-actions-menu div {
  color: #ffffff !important;
  font-weight: 500 !important;
}

.dark #user-menu a:hover,
.dark #language-menu a:hover,
.dark #quick-actions-menu a:hover,
.dark #user-menu button:hover,
.dark #language-menu button:hover,
.dark #quick-actions-menu button:hover {
  background-color: #1f2937 !important;
  color: #ffffff !important;
}

/* Language Menu Specific */
.dark #language-menu .flex.items-center {
  color: #ffffff !important;
}

.dark #language-menu span {
  color: #ffffff !important;
}

/* User Menu Text */
.dark #user-menu p {
  color: #ffffff !important;
}

/* Member Menu in Project Details */
.dark .member-menu {
  background-color: #111827 !important;
  border: 2px solid #6b7280 !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.5) !important;
}

.dark .member-menu button {
  color: #ffffff !important;
  font-weight: 500 !important;
}

.dark .member-menu button:hover {
  background-color: #1f2937 !important;
  color: #ffffff !important;
}

/* Icon and Text Alignment in Dropdowns */
.dark [id$="-menu"] .flex.items-center {
  align-items: center !important;
  gap: 0.75rem !important;
}

.dark [id$="-menu"] i {
  color: #ffffff !important;
  width: 1rem !important;
  text-align: center !important;
}

/* Ensure all text elements in menus are visible */
.dark [id$="-menu"] * {
  color: #ffffff !important;
}

/* Override any conflicting styles */
.dark [class*="text-neutral"],
.dark [class*="text-dark"] {
  color: #ffffff !important;
}
