using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MockQueryable.Moq;
using Moq;
using PM.Tool.Controllers;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;
using PM.Tool.Models.ViewModels;
using PM.Tool.Tests.Helpers;
using System.Security.Claims;
using Xunit;
using FluentAssertions;
using TaskStatus = PM.Tool.Core.Enums.TaskStatus;

namespace PM.Tool.Tests.Unit.Controllers
{
    public class DashboardControllerTests : IDisposable
    {
        private readonly ApplicationDbContext _context;
        private readonly Mock<IProjectService> _mockProjectService;
        private readonly Mock<ITaskService> _mockTaskService;
        private readonly Mock<INotificationService> _mockNotificationService;
        private readonly Mock<UserManager<ApplicationUser>> _mockUserManager;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IReportingService> _mockReportingService;
        private readonly Mock<ILogger<DashboardController>> _mockLogger;
        private readonly DashboardController _controller;
        private readonly ApplicationUser _testUser;

        public DashboardControllerTests()
        {
            _context = TestDbContextFactory.CreateInMemoryContext();
            _mockProjectService = new Mock<IProjectService>();
            _mockTaskService = new Mock<ITaskService>();
            _mockNotificationService = new Mock<INotificationService>();
            _mockUserManager = MockHelper.CreateMockUserManager();
            _mockMapper = new Mock<IMapper>();
            _mockReportingService = new Mock<IReportingService>();
            _mockLogger = MockHelper.CreateMockLogger<DashboardController>();

            _controller = new DashboardController(
                _context,
                _mockProjectService.Object,
                _mockTaskService.Object,
                _mockNotificationService.Object,
                _mockUserManager.Object,
                _mockMapper.Object,
                _mockReportingService.Object);

            _testUser = new ApplicationUser
            {
                Id = "test-user-1",
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Test",
                LastName = "User",
                IsActive = true
            };

            MockHelper.SetupControllerContext(_controller);

            // Setup TempData
            _controller.TempData = new TempDataDictionary(_controller.ControllerContext.HttpContext, Mock.Of<ITempDataProvider>());
        }

        public void Dispose()
        {
            _context?.Dispose();
        }

        [Fact]
        public async Task Index_WithValidUser_ReturnsViewWithDashboardData()
        {
            // Arrange
            var startDate = DateTime.UtcNow.AddDays(-30);
            var endDate = DateTime.UtcNow;

            var projects = new List<Project>
            {
                new Project
                {
                    Id = 1,
                    Name = "Test Project 1",
                    Status = ProjectStatus.Active,
                    UpdatedAt = DateTime.UtcNow.AddDays(-5),
                    Members = new List<ProjectMember>
                    {
                        new ProjectMember { UserId = _testUser.Id, IsActive = true }
                    },
                    Tasks = new List<TaskEntity>
                    {
                        new TaskEntity { Id = 1, Status = TaskStatus.Done },
                        new TaskEntity { Id = 2, Status = TaskStatus.Done },
                        new TaskEntity { Id = 3, Status = TaskStatus.InProgress },
                        new TaskEntity { Id = 4, Status = TaskStatus.ToDo }
                    }
                }
            };

            var tasks = new List<TaskEntity>
            {
                new TaskEntity
                {
                    Id = 1,
                    Title = "Test Task",
                    Status = TaskStatus.InProgress,
                    Priority = TaskPriority.High,
                    AssignedToUserId = _testUser.Id,
                    UpdatedAt = DateTime.UtcNow.AddDays(-2),
                    Project = projects[0]
                }
            };

            var notifications = new List<Notification>
            {
                new Notification
                {
                    Id = 1,
                    Title = "Test Notification",
                    Message = "Test Message",
                    UserId = _testUser.Id,
                    CreatedAt = DateTime.UtcNow
                }
            };

            // Setup test data in context
            _context.Users.Add(_testUser);
            _context.Projects.AddRange(projects);
            _context.Tasks.AddRange(tasks);
            await _context.SaveChangesAsync();

            var mockUsersDbSet = new List<ApplicationUser> { _testUser }.AsQueryable().BuildMockDbSet();
            _mockUserManager.Setup(x => x.Users).Returns(mockUsersDbSet.Object);

            _mockNotificationService.Setup(x => x.GetUserNotificationsAsync(_testUser.Id, false))
                .ReturnsAsync(notifications);

            _mockMapper.Setup(x => x.Map<List<NotificationViewModel>>(notifications))
                .Returns(new List<NotificationViewModel>
                {
                    new NotificationViewModel { Id = 1, Title = "Test Notification" }
                });

            _mockMapper.Setup(x => x.Map<List<ProjectSummaryViewModel>>(It.IsAny<List<Project>>()))
                .Returns(new List<ProjectSummaryViewModel>
                {
                    new ProjectSummaryViewModel { Id = 1, Name = "Test Project 1" }
                });

            _mockMapper.Setup(x => x.Map<List<TaskSummaryViewModel>>(It.IsAny<List<TaskEntity>>()))
                .Returns(new List<TaskSummaryViewModel>
                {
                    new TaskSummaryViewModel { Id = 1, Title = "Test Task" }
                });

            _mockMapper.Setup(x => x.Map<List<MilestoneViewModel>>(It.IsAny<List<Milestone>>()))
                .Returns(new List<MilestoneViewModel>());

            // Setup User.FindFirstValue to return the test user ID
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, _testUser.Id),
                new Claim(ClaimTypes.Role, "User")
            };
            var identity = new ClaimsIdentity(claims, "Test");
            var principal = new ClaimsPrincipal(identity);

            _controller.ControllerContext.HttpContext.User = principal;

            // Act
            var result = await _controller.Index(startDate, endDate);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<DashboardViewModel>();
            var model = viewResult.Model as DashboardViewModel;
            model!.StartDate.Should().Be(startDate);
            model.EndDate.Should().Be(endDate);
            model.Stats.Should().NotBeNull();
            model.Charts.Should().NotBeNull();
            model.RecentProjects.Should().NotBeNull();
            model.MyTasks.Should().NotBeNull();
            model.RecentNotifications.Should().NotBeNull();
        }

        [Fact]
        public async Task Index_WithDefaultDates_UsesLast30Days()
        {
            // Arrange
            var expectedStartDate = DateTime.UtcNow.AddDays(-30).Date;
            var expectedEndDate = DateTime.UtcNow.Date;

            // Setup minimal test data
            _context.Users.Add(_testUser);
            await _context.SaveChangesAsync();

            var mockUsersDbSet = new List<ApplicationUser> { _testUser }.AsQueryable().BuildMockDbSet();
            _mockUserManager.Setup(x => x.Users).Returns(mockUsersDbSet.Object);

            _mockNotificationService.Setup(x => x.GetUserNotificationsAsync(_testUser.Id, false))
                .ReturnsAsync(new List<Notification>());

            _mockMapper.Setup(x => x.Map<List<NotificationViewModel>>(It.IsAny<List<Notification>>()))
                .Returns(new List<NotificationViewModel>());
            _mockMapper.Setup(x => x.Map<List<ProjectSummaryViewModel>>(It.IsAny<List<Project>>()))
                .Returns(new List<ProjectSummaryViewModel>());
            _mockMapper.Setup(x => x.Map<List<TaskSummaryViewModel>>(It.IsAny<List<TaskEntity>>()))
                .Returns(new List<TaskSummaryViewModel>());
            _mockMapper.Setup(x => x.Map<List<MilestoneViewModel>>(It.IsAny<List<Milestone>>()))
                .Returns(new List<MilestoneViewModel>());

            var claims = new List<Claim> { new Claim(ClaimTypes.NameIdentifier, _testUser.Id) };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            // Act
            var result = await _controller.Index();

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            var model = viewResult!.Model as DashboardViewModel;
            model!.StartDate.Date.Should().Be(expectedStartDate);
            model.EndDate.Date.Should().Be(expectedEndDate);
        }

        [Fact]
        public async Task ExportTasks_ReturnsFileResult()
        {
            // Arrange
            var startDate = DateTime.UtcNow.AddDays(-30);
            var endDate = DateTime.UtcNow;
            var csvData = "Task,Status,Priority\nTest Task,InProgress,High"u8.ToArray();

            _mockReportingService.Setup(x => x.GenerateTasksReportAsync(startDate, endDate))
                .ReturnsAsync(csvData);

            // Act
            var result = await _controller.ExportTasks(startDate, endDate);

            // Assert
            result.Should().BeOfType<FileContentResult>();
            var fileResult = result as FileContentResult;
            fileResult!.ContentType.Should().Be("text/csv");
            fileResult.FileDownloadName.Should().StartWith("tasks_report_");
            fileResult.FileDownloadName.Should().EndWith(".csv");
            fileResult.FileContents.Should().Equal(csvData);
        }

        [Fact]
        public async Task ExportProjects_ReturnsFileResult()
        {
            // Arrange
            var startDate = DateTime.UtcNow.AddDays(-30);
            var endDate = DateTime.UtcNow;
            var csvData = "Project,Status,Progress\nTest Project,Active,75%"u8.ToArray();

            _mockReportingService.Setup(x => x.GenerateProjectReportAsync(startDate, endDate))
                .ReturnsAsync(csvData);

            // Act
            var result = await _controller.ExportProjects(startDate, endDate);

            // Assert
            result.Should().BeOfType<FileContentResult>();
            var fileResult = result as FileContentResult;
            fileResult!.ContentType.Should().Be("text/csv");
            fileResult.FileDownloadName.Should().StartWith("projects_report_");
            fileResult.FileDownloadName.Should().EndWith(".csv");
            fileResult.FileContents.Should().Equal(csvData);
        }

        [Fact]
        public async Task GetVelocityMetrics_ReturnsJsonResult()
        {
            // Arrange
            var startDate = DateTime.UtcNow.AddDays(-30);
            var endDate = DateTime.UtcNow;
            var metrics = new VelocityMetricsViewModel
            {
                AverageVelocity = 25.5,
                TotalCompletedTasks = 102,
                AverageCycleTime = 2.5
            };

            var claims = new List<Claim> { new Claim(ClaimTypes.NameIdentifier, _testUser.Id) };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            _mockReportingService.Setup(x => x.GetTeamVelocityMetricsAsync(_testUser.Id, startDate, endDate))
                .ReturnsAsync(metrics);

            // Act
            var result = await _controller.GetVelocityMetrics(startDate, endDate);

            // Assert
            result.Should().BeOfType<JsonResult>();
            var jsonResult = result as JsonResult;
            jsonResult!.Value.Should().Be(metrics);
        }

        [Fact]
        public async Task GetProductivityMetrics_ReturnsJsonResult()
        {
            // Arrange
            var startDate = DateTime.UtcNow.AddDays(-30);
            var endDate = DateTime.UtcNow;
            var metrics = new ProductivityMetricsViewModel
            {
                TotalTasksCompleted = 15,
                TotalTimeSpent = 120.5,
                AverageTasksPerMember = 3.75,
                AverageEfficiencyRatio = 85.2
            };

            var claims = new List<Claim> { new Claim(ClaimTypes.NameIdentifier, _testUser.Id) };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            _mockReportingService.Setup(x => x.GetTeamProductivityMetricsAsync(_testUser.Id, startDate, endDate))
                .ReturnsAsync(metrics);

            // Act
            var result = await _controller.GetProductivityMetrics(startDate, endDate);

            // Assert
            result.Should().BeOfType<JsonResult>();
            var jsonResult = result as JsonResult;
            jsonResult!.Value.Should().Be(metrics);
        }

        [Fact]
        public void TestSaaS_ReturnsView()
        {
            // Act
            var result = _controller.TestSaaS();

            // Assert
            result.Should().BeOfType<ViewResult>();
        }

        [Fact]
        public async Task Index_WithAdminUser_ShowsAllProjects()
        {
            // Arrange
            var projects = new List<Project>
            {
                new Project
                {
                    Id = 1,
                    Name = "Admin Project 1",
                    Status = ProjectStatus.Active,
                    UpdatedAt = DateTime.UtcNow.AddDays(-5),
                    Members = new List<ProjectMember>(),
                    Tasks = new List<TaskEntity>
                    {
                        new TaskEntity { Id = 1, Status = TaskStatus.Done },
                        new TaskEntity { Id = 2, Status = TaskStatus.InProgress }
                    }
                },
                new Project
                {
                    Id = 2,
                    Name = "Admin Project 2",
                    Status = ProjectStatus.Completed,
                    UpdatedAt = DateTime.UtcNow.AddDays(-10),
                    Members = new List<ProjectMember>(),
                    Tasks = new List<TaskEntity>
                    {
                        new TaskEntity { Id = 3, Status = TaskStatus.Done },
                        new TaskEntity { Id = 4, Status = TaskStatus.Done }
                    }
                }
            };

            var tasks = new List<TaskEntity>
            {
                new TaskEntity
                {
                    Id = 1,
                    Title = "Admin Task",
                    Status = TaskStatus.Done,
                    Priority = TaskPriority.Medium,
                    UpdatedAt = DateTime.UtcNow.AddDays(-1),
                    Project = projects[0]
                }
            };

            // Setup test data for admin user
            _context.Users.Add(_testUser);
            _context.Projects.AddRange(projects);
            _context.Tasks.AddRange(tasks);
            await _context.SaveChangesAsync();

            var mockUsersDbSet = new List<ApplicationUser> { _testUser }.AsQueryable().BuildMockDbSet();
            _mockUserManager.Setup(x => x.Users).Returns(mockUsersDbSet.Object);

            _mockNotificationService.Setup(x => x.GetUserNotificationsAsync(_testUser.Id, false))
                .ReturnsAsync(new List<Notification>());

            _mockMapper.Setup(x => x.Map<List<NotificationViewModel>>(It.IsAny<List<Notification>>()))
                .Returns(new List<NotificationViewModel>());
            _mockMapper.Setup(x => x.Map<List<ProjectSummaryViewModel>>(It.IsAny<List<Project>>()))
                .Returns(new List<ProjectSummaryViewModel>
                {
                    new ProjectSummaryViewModel { Id = 1, Name = "Admin Project 1" },
                    new ProjectSummaryViewModel { Id = 2, Name = "Admin Project 2" }
                });
            _mockMapper.Setup(x => x.Map<List<TaskSummaryViewModel>>(It.IsAny<List<TaskEntity>>()))
                .Returns(new List<TaskSummaryViewModel>());
            _mockMapper.Setup(x => x.Map<List<MilestoneViewModel>>(It.IsAny<List<Milestone>>()))
                .Returns(new List<MilestoneViewModel>());

            // Setup admin user claims
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, _testUser.Id),
                new Claim(ClaimTypes.Role, "Admin")
            };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            // Act
            var result = await _controller.Index();

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            var model = viewResult!.Model as DashboardViewModel;
            model!.RecentProjects.Should().HaveCount(2);
            model.Stats.Should().NotBeNull();
            model.Stats.TotalProjects.Should().Be(2);
            model.Stats.CompletedProjects.Should().Be(1);
        }

        [Fact]
        public async Task Index_WithNoProjects_ReturnsEmptyStats()
        {
            // Arrange
            // Setup test data - no projects or tasks for this test
            _context.Users.Add(_testUser);
            await _context.SaveChangesAsync();

            var mockUsersDbSet = new List<ApplicationUser> { _testUser }.AsQueryable().BuildMockDbSet();
            _mockUserManager.Setup(x => x.Users).Returns(mockUsersDbSet.Object);

            _mockNotificationService.Setup(x => x.GetUserNotificationsAsync(_testUser.Id, false))
                .ReturnsAsync(new List<Notification>());

            _mockMapper.Setup(x => x.Map<List<NotificationViewModel>>(It.IsAny<List<Notification>>()))
                .Returns(new List<NotificationViewModel>());
            _mockMapper.Setup(x => x.Map<List<ProjectSummaryViewModel>>(It.IsAny<List<Project>>()))
                .Returns(new List<ProjectSummaryViewModel>());
            _mockMapper.Setup(x => x.Map<List<TaskSummaryViewModel>>(It.IsAny<List<TaskEntity>>()))
                .Returns(new List<TaskSummaryViewModel>());
            _mockMapper.Setup(x => x.Map<List<MilestoneViewModel>>(It.IsAny<List<Milestone>>()))
                .Returns(new List<MilestoneViewModel>());

            var claims = new List<Claim> { new Claim(ClaimTypes.NameIdentifier, _testUser.Id) };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            // Act
            var result = await _controller.Index();

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            var model = viewResult!.Model as DashboardViewModel;
            model!.Stats.TotalProjects.Should().Be(0);
            model.Stats.ActiveProjects.Should().Be(0);
            model.Stats.CompletedProjects.Should().Be(0);
            model.Stats.TotalTasks.Should().Be(0);
            model.Stats.OverallProgress.Should().Be(0);
            model.RecentProjects.Should().BeEmpty();
        }

        [Fact]
        public async Task ExportTasks_WithNullDates_UsesDefaultDates()
        {
            // Arrange
            var csvData = "Task,Status\nDefault Task,InProgress"u8.ToArray();

            _mockReportingService.Setup(x => x.GenerateTasksReportAsync(null, null))
                .ReturnsAsync(csvData);

            // Act
            var result = await _controller.ExportTasks(null, null);

            // Assert
            result.Should().BeOfType<FileContentResult>();
            var fileResult = result as FileContentResult;
            fileResult!.FileContents.Should().Equal(csvData);
            _mockReportingService.Verify(x => x.GenerateTasksReportAsync(null, null), Times.Once);
        }

        [Fact]
        public async Task ExportProjects_WithNullDates_UsesDefaultDates()
        {
            // Arrange
            var csvData = "Project,Status\nDefault Project,Active"u8.ToArray();

            _mockReportingService.Setup(x => x.GenerateProjectReportAsync(null, null))
                .ReturnsAsync(csvData);

            // Act
            var result = await _controller.ExportProjects(null, null);

            // Assert
            result.Should().BeOfType<FileContentResult>();
            var fileResult = result as FileContentResult;
            fileResult!.FileContents.Should().Equal(csvData);
            _mockReportingService.Verify(x => x.GenerateProjectReportAsync(null, null), Times.Once);
        }

        [Fact]
        public async Task GetVelocityMetrics_WithNullDates_UsesDefaultDates()
        {
            // Arrange
            var expectedStartDate = DateTime.UtcNow.AddDays(-30).Date;
            var expectedEndDate = DateTime.UtcNow.Date;
            var metrics = new VelocityMetricsViewModel { AverageVelocity = 20.0 };

            var claims = new List<Claim> { new Claim(ClaimTypes.NameIdentifier, _testUser.Id) };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            _mockReportingService.Setup(x => x.GetTeamVelocityMetricsAsync(_testUser.Id, It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                .ReturnsAsync(metrics);

            // Act
            var result = await _controller.GetVelocityMetrics(null, null);

            // Assert
            result.Should().BeOfType<JsonResult>();
            _mockReportingService.Verify(x => x.GetTeamVelocityMetricsAsync(
                _testUser.Id,
                It.Is<DateTime>(d => d.Date == expectedStartDate),
                It.Is<DateTime>(d => d.Date == expectedEndDate)), Times.Once);
        }

        [Fact]
        public async Task GetProductivityMetrics_WithNullDates_UsesDefaultDates()
        {
            // Arrange
            var expectedStartDate = DateTime.UtcNow.AddDays(-30).Date;
            var expectedEndDate = DateTime.UtcNow.Date;
            var metrics = new ProductivityMetricsViewModel { TotalTasksCompleted = 10 };

            var claims = new List<Claim> { new Claim(ClaimTypes.NameIdentifier, _testUser.Id) };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            _mockReportingService.Setup(x => x.GetTeamProductivityMetricsAsync(_testUser.Id, It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                .ReturnsAsync(metrics);

            // Act
            var result = await _controller.GetProductivityMetrics(null, null);

            // Assert
            result.Should().BeOfType<JsonResult>();
            _mockReportingService.Verify(x => x.GetTeamProductivityMetricsAsync(
                _testUser.Id,
                It.Is<DateTime>(d => d.Date == expectedStartDate),
                It.Is<DateTime>(d => d.Date == expectedEndDate)), Times.Once);
        }
    }
}
