using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Moq;
using PM.Tool.Controllers;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Interfaces;
using PM.Tool.Models.ViewModels;
using PM.Tool.Services;
using PM.Tool.Tests.Helpers;
using System.Security.Claims;
using Xunit;
using FluentAssertions;

namespace PM.Tool.Tests.Unit.Controllers
{
    public class RequirementControllerTests
    {
        private readonly Mock<IRequirementService> _mockRequirementService;
        private readonly Mock<IProjectService> _mockProjectService;
        private readonly Mock<IStakeholderService> _mockStakeholderService;
        private readonly Mock<IFormHelperService> _mockFormHelper;
        private readonly Mock<IAuditService> _mockAuditService;
        private readonly RequirementController _controller;
        private readonly ApplicationUser _testUser;

        public RequirementControllerTests()
        {
            _mockRequirementService = new Mock<IRequirementService>();
            _mockProjectService = new Mock<IProjectService>();
            _mockStakeholderService = new Mock<IStakeholderService>();
            _mockFormHelper = new Mock<IFormHelperService>();
            _mockAuditService = MockHelper.CreateMockAuditService();

            _controller = new RequirementController(
                _mockRequirementService.Object,
                _mockProjectService.Object,
                _mockFormHelper.Object,
                _mockStakeholderService.Object,
                _mockAuditService.Object,
                MockHelper.CreateMockLogger<RequirementController>().Object);

            _testUser = new ApplicationUser
            {
                Id = "test-user-1",
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Test",
                LastName = "User",
                IsActive = true
            };

            MockHelper.SetupControllerContext(_controller);

            // Setup TempData
            _controller.TempData = new TempDataDictionary(_controller.ControllerContext.HttpContext, Mock.Of<ITempDataProvider>());
        }

        [Fact]
        public async Task Index_WithoutProjectId_ReturnsAllRequirements()
        {
            // Arrange
            var requirements = new List<Requirement>
            {
                new Requirement
                {
                    Id = 1,
                    Title = "Test Requirement 1",
                    Description = "Test Description 1",
                    Type = RequirementType.Functional,
                    Priority = RequirementPriority.High,
                    Status = RequirementStatus.Draft,
                    ProjectId = 1
                },
                new Requirement
                {
                    Id = 2,
                    Title = "Test Requirement 2",
                    Description = "Test Description 2",
                    Type = RequirementType.NonFunctional,
                    Priority = RequirementPriority.Medium,
                    Status = RequirementStatus.Approved,
                    ProjectId = 2
                }
            };

            _mockRequirementService.Setup(x => x.GetAllRequirementsAsync())
                .ReturnsAsync(requirements);

            // Act
            var result = await _controller.Index(null);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<List<Requirement>>();
            var model = viewResult.Model as List<Requirement>;
            model!.Should().HaveCount(2);
            model[0].Title.Should().Be("Test Requirement 1");
            model[1].Title.Should().Be("Test Requirement 2");
        }

        [Fact]
        public async Task Index_WithProjectId_ReturnsProjectRequirements()
        {
            // Arrange
            var projectId = 1;
            var project = new Project { Id = projectId, Name = "Test Project" };
            var requirements = new List<Requirement>
            {
                new Requirement
                {
                    Id = 1,
                    Title = "Project Requirement",
                    Description = "Project specific requirement",
                    ProjectId = projectId
                }
            };

            _mockRequirementService.Setup(x => x.GetProjectRequirementsAsync(projectId))
                .ReturnsAsync(requirements);
            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync(project);

            // Act
            var result = await _controller.Index(projectId);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<List<Requirement>>();
            var model = viewResult.Model as List<Requirement>;
            model!.Should().HaveCount(1);
            model[0].Title.Should().Be("Project Requirement");
            viewResult.ViewData["ProjectName"].Should().Be("Test Project");
            viewResult.ViewData["ProjectId"].Should().Be(projectId);
        }

        [Fact]
        public async Task Index_WithException_ReturnsEmptyViewWithError()
        {
            // Arrange
            _mockRequirementService.Setup(x => x.GetAllRequirementsAsync())
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.Index(null);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<List<Requirement>>();
            var model = viewResult.Model as List<Requirement>;
            model!.Should().BeEmpty();
            _controller.TempData["Error"].Should().Be("Error loading requirements.");
        }

        [Fact]
        public async Task Details_WithValidRequirement_ReturnsViewWithDetails()
        {
            // Arrange
            var requirementId = 1;
            var requirement = new Requirement
            {
                Id = requirementId,
                Title = "Test Requirement",
                Description = "Test Description",
                Type = RequirementType.Functional,
                Priority = RequirementPriority.High,
                Status = RequirementStatus.Draft
            };

            var comments = new List<RequirementComment>
            {
                new RequirementComment { Id = 1, Content = "Test comment", RequirementId = requirementId }
            };

            var attachments = new List<RequirementAttachment>
            {
                new RequirementAttachment { Id = 1, FileName = "test.pdf", RequirementId = requirementId }
            };

            var changes = new List<RequirementChange>
            {
                new RequirementChange { Id = 1, ChangeDescription = "Test change", RequirementId = requirementId }
            };

            var tasks = new List<TaskEntity>
            {
                new TaskEntity { Id = 1, Title = "Linked Task", ProjectId = 1 }
            };

            _mockRequirementService.Setup(x => x.GetRequirementByIdAsync(requirementId))
                .ReturnsAsync(requirement);
            _mockRequirementService.Setup(x => x.GetRequirementCommentsAsync(requirementId))
                .ReturnsAsync(comments);
            _mockRequirementService.Setup(x => x.GetRequirementAttachmentsAsync(requirementId))
                .ReturnsAsync(attachments);
            _mockRequirementService.Setup(x => x.GetRequirementChangesAsync(requirementId))
                .ReturnsAsync(changes);
            _mockRequirementService.Setup(x => x.GetRequirementTasksAsync(requirementId))
                .ReturnsAsync(tasks);

            // Act
            var result = await _controller.Details(requirementId);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<Requirement>();
            var model = viewResult.Model as Requirement;
            model!.Id.Should().Be(requirementId);
            model.Title.Should().Be("Test Requirement");
            viewResult.ViewData["Comments"].Should().Be(comments);
            viewResult.ViewData["Attachments"].Should().Be(attachments);
            viewResult.ViewData["Changes"].Should().Be(changes);
            viewResult.ViewData["Tasks"].Should().Be(tasks);
        }

        [Fact]
        public async Task Details_WithNonExistentRequirement_ReturnsNotFound()
        {
            // Arrange
            var requirementId = 999;
            _mockRequirementService.Setup(x => x.GetRequirementByIdAsync(requirementId))
                .ReturnsAsync((Requirement?)null);

            // Act
            var result = await _controller.Details(requirementId);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task Details_WithException_RedirectsToIndexWithError()
        {
            // Arrange
            var requirementId = 1;
            _mockRequirementService.Setup(x => x.GetRequirementByIdAsync(requirementId))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.Details(requirementId);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("Index");
            _controller.TempData["Error"].Should().Be("Error loading requirement details.");
        }

        [Fact]
        public async Task Create_Get_WithoutParameters_ReturnsViewWithNewRequirement()
        {
            // Act
            var result = await _controller.Create(null, null);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<Requirement>();
            var model = viewResult.Model as Requirement;
            model!.Id.Should().Be(0); // New requirement
            viewResult.ViewData.Should().ContainKeys("RequirementTypes", "RequirementPriorities", "RequirementStatuses", "RequirementSources");
        }

        [Fact]
        public async Task Create_Get_WithProjectId_SetsProjectAndReturnsView()
        {
            // Arrange
            var projectId = 1;
            var project = new Project { Id = projectId, Name = "Test Project" };

            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync(project);

            // Act
            var result = await _controller.Create(projectId, null);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<Requirement>();
            var model = viewResult.Model as Requirement;
            model!.ProjectId.Should().Be(projectId);
            viewResult.ViewData["ProjectName"].Should().Be("Test Project");
        }

        [Fact]
        public async Task Create_Get_WithParentId_SetsParentAndReturnsView()
        {
            // Arrange
            var parentId = 1;
            var parentRequirement = new Requirement { Id = parentId, Title = "Parent Requirement" };

            _mockRequirementService.Setup(x => x.GetRequirementByIdAsync(parentId))
                .ReturnsAsync(parentRequirement);

            // Act
            var result = await _controller.Create(null, parentId);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<Requirement>();
            var model = viewResult.Model as Requirement;
            model!.ParentRequirementId.Should().Be(parentId);
            viewResult.ViewData["ParentRequirement"].Should().Be(parentRequirement);
        }

        [Fact]
        public async Task Create_Get_WithException_RedirectsToIndexWithError()
        {
            // Arrange
            var projectId = 1;
            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.Create(projectId, null);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("Index");
            _controller.TempData["Error"].Should().Be("Error loading requirement creation form.");
        }

        [Fact]
        public async Task Create_Post_WithValidModel_CreatesRequirementAndRedirects()
        {
            // Arrange
            var requirement = new Requirement
            {
                Title = "New Requirement",
                Description = "New Description",
                Type = RequirementType.Functional,
                Priority = RequirementPriority.High,
                ProjectId = 1
            };

            var createdRequirement = new Requirement
            {
                Id = 1,
                Title = requirement.Title,
                Description = requirement.Description,
                Type = requirement.Type,
                Priority = requirement.Priority,
                ProjectId = requirement.ProjectId
            };

            // Setup User.FindFirstValue for audit logging
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, _testUser.Id)
            };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            _mockRequirementService.Setup(x => x.CreateRequirementAsync(It.IsAny<Requirement>()))
                .ReturnsAsync(createdRequirement);

            // Convert to ViewModel
            var viewModel = new RequirementCreateViewModel
            {
                Title = requirement.Title,
                Description = requirement.Description,
                Type = requirement.Type,
                Priority = requirement.Priority,
                ProjectId = requirement.ProjectId
            };

            // Act
            var result = await _controller.Create(viewModel);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("Details");
            redirectResult.RouteValues!["id"].Should().Be(1);
            _controller.TempData["Success"].Should().Be("Requirement created successfully.");

            // Verify audit logging
            _mockAuditService.Verify(x => x.LogAsync(
                AuditAction.Create,
                "Requirement",
                1,
                _testUser.Id,
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>()), Times.Once);
        }

        [Fact]
        public async Task Create_Post_WithInvalidModel_ReturnsViewWithModel()
        {
            // Arrange
            var viewModel = new RequirementCreateViewModel
            {
                // Missing Title - this will make ModelState invalid
                Description = "Test Description"
            };
            _controller.ModelState.AddModelError("Title", "Title is required");

            // Act
            var result = await _controller.Create(viewModel);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().Be(viewModel);
            viewResult.ViewData.Should().ContainKeys("RequirementTypes", "RequirementPriorities", "RequirementStatuses", "RequirementSources");
        }

        [Fact]
        public async Task Create_Post_WithException_ReturnsViewWithError()
        {
            // Arrange
            var viewModel = new RequirementCreateViewModel
            {
                Title = "Test Requirement",
                Description = "Test Description"
            };

            _mockRequirementService.Setup(x => x.CreateRequirementAsync(It.IsAny<Requirement>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.Create(viewModel);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().Be(viewModel);
            _controller.TempData["Error"].Should().Be("Error creating requirement.");
        }

        [Fact]
        public async Task Edit_Get_WithValidRequirement_ReturnsViewWithModel()
        {
            // Arrange
            var requirementId = 1;
            var requirement = new Requirement
            {
                Id = requirementId,
                Title = "Test Requirement",
                Description = "Test Description",
                Type = RequirementType.Functional,
                Priority = RequirementPriority.High,
                Status = RequirementStatus.Draft
            };

            _mockRequirementService.Setup(x => x.GetRequirementByIdAsync(requirementId))
                .ReturnsAsync(requirement);

            // Act
            var result = await _controller.Edit(requirementId);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<Requirement>();
            var model = viewResult.Model as Requirement;
            model!.Id.Should().Be(requirementId);
            model.Title.Should().Be("Test Requirement");
            viewResult.ViewData.Should().ContainKeys("RequirementTypes", "RequirementPriorities", "RequirementStatuses", "RequirementSources");
        }

        [Fact]
        public async Task Edit_Get_WithNonExistentRequirement_ReturnsNotFound()
        {
            // Arrange
            var requirementId = 999;
            _mockRequirementService.Setup(x => x.GetRequirementByIdAsync(requirementId))
                .ReturnsAsync((Requirement?)null);

            // Act
            var result = await _controller.Edit(requirementId);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task Edit_Get_WithException_RedirectsToIndexWithError()
        {
            // Arrange
            var requirementId = 1;
            _mockRequirementService.Setup(x => x.GetRequirementByIdAsync(requirementId))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.Edit(requirementId);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("Index");
            _controller.TempData["Error"].Should().Be("Error loading requirement for editing.");
        }

        [Fact]
        public async Task Edit_Post_WithValidModel_UpdatesRequirementAndRedirects()
        {
            // Arrange
            var requirementId = 1;
            var requirement = new Requirement
            {
                Id = requirementId,
                Title = "Updated Requirement",
                Description = "Updated Description",
                Type = RequirementType.NonFunctional,
                Priority = RequirementPriority.Medium
            };

            // Setup User.FindFirstValue for audit logging
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, _testUser.Id)
            };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            _mockRequirementService.Setup(x => x.UpdateRequirementAsync(It.IsAny<Requirement>()))
                .ReturnsAsync(requirement);

            // Act
            var result = await _controller.Edit(requirementId, requirement);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("Details");
            redirectResult.RouteValues!["id"].Should().Be(requirementId);
            _controller.TempData["Success"].Should().Be("Requirement updated successfully.");

            // Verify audit logging
            _mockAuditService.Verify(x => x.LogAsync(
                AuditAction.Update,
                "Requirement",
                requirementId,
                _testUser.Id,
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>()), Times.Once);
        }

        [Fact]
        public async Task Edit_Post_WithMismatchedId_ReturnsNotFound()
        {
            // Arrange
            var requirementId = 1;
            var requirement = new Requirement
            {
                Id = 2, // Different ID
                Title = "Test Requirement"
            };

            // Act
            var result = await _controller.Edit(requirementId, requirement);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task Edit_Post_WithInvalidModel_ReturnsViewWithModel()
        {
            // Arrange
            var requirementId = 1;
            var requirement = new Requirement
            {
                Id = requirementId,
                // Missing Title - this will make ModelState invalid
                Description = "Test Description"
            };
            _controller.ModelState.AddModelError("Title", "Title is required");

            // Act
            var result = await _controller.Edit(requirementId, requirement);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().Be(requirement);
            viewResult.ViewData.Should().ContainKeys("RequirementTypes", "RequirementPriorities", "RequirementStatuses", "RequirementSources");
        }

        [Fact]
        public async Task Edit_Post_WithException_ReturnsViewWithError()
        {
            // Arrange
            var requirementId = 1;
            var requirement = new Requirement
            {
                Id = requirementId,
                Title = "Test Requirement",
                Description = "Test Description"
            };

            _mockRequirementService.Setup(x => x.UpdateRequirementAsync(It.IsAny<Requirement>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.Edit(requirementId, requirement);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().Be(requirement);
            _controller.TempData["Error"].Should().Be("Error updating requirement.");
        }

        [Fact]
        public async Task Delete_WithValidRequirement_DeletesAndRedirectsToIndex()
        {
            // Arrange
            var requirementId = 1;

            // Setup User.FindFirstValue for audit logging
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, _testUser.Id)
            };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            _mockRequirementService.Setup(x => x.DeleteRequirementAsync(requirementId))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.Delete(requirementId);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("Index");
            _controller.TempData["Success"].Should().Be("Requirement deleted successfully.");

            // Verify audit logging
            _mockAuditService.Verify(x => x.LogAsync(
                AuditAction.Delete,
                "Requirement",
                requirementId,
                _testUser.Id,
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>()), Times.Once);
        }

        [Fact]
        public async Task Delete_WithFailedDeletion_RedirectsToIndexWithError()
        {
            // Arrange
            var requirementId = 1;
            _mockRequirementService.Setup(x => x.DeleteRequirementAsync(requirementId))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.Delete(requirementId);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("Index");
            _controller.TempData["Error"].Should().Be("Failed to delete requirement.");
        }

        [Fact]
        public async Task Delete_WithException_RedirectsToIndexWithError()
        {
            // Arrange
            var requirementId = 1;
            _mockRequirementService.Setup(x => x.DeleteRequirementAsync(requirementId))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.Delete(requirementId);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("Index");
            _controller.TempData["Error"].Should().Be("Error deleting requirement.");
        }

        [Fact]
        public async Task UpdateStatus_WithValidRequest_ReturnsSuccessJson()
        {
            // Arrange
            var requirementId = 1;
            var status = RequirementStatus.Approved;

            // Setup User.FindFirstValue for audit logging
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, _testUser.Id)
            };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            _mockRequirementService.Setup(x => x.UpdateRequirementStatusAsync(requirementId, status))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.UpdateStatus(requirementId, status);

            // Assert
            result.Should().BeOfType<JsonResult>();
            var jsonResult = result as JsonResult;
            var value = jsonResult!.Value;
            value.Should().NotBeNull();

            // Verify audit logging
            _mockAuditService.Verify(x => x.LogAsync(
                AuditAction.ChangeStatus,
                "Requirement",
                requirementId,
                _testUser.Id,
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>()), Times.Once);
        }

        [Fact]
        public async Task UpdateStatus_WithFailedUpdate_ReturnsFailureJson()
        {
            // Arrange
            var requirementId = 1;
            var status = RequirementStatus.Approved;
            _mockRequirementService.Setup(x => x.UpdateRequirementStatusAsync(requirementId, status))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.UpdateStatus(requirementId, status);

            // Assert
            result.Should().BeOfType<JsonResult>();
            var jsonResult = result as JsonResult;
            jsonResult!.Value.Should().NotBeNull();
        }

        [Fact]
        public async Task UpdateStatus_WithException_ReturnsFailureJson()
        {
            // Arrange
            var requirementId = 1;
            var status = RequirementStatus.Approved;
            _mockRequirementService.Setup(x => x.UpdateRequirementStatusAsync(requirementId, status))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.UpdateStatus(requirementId, status);

            // Assert
            result.Should().BeOfType<JsonResult>();
            var jsonResult = result as JsonResult;
            jsonResult!.Value.Should().NotBeNull();
        }

        [Fact]
        public async Task Approve_WithValidRequest_ApprovesAndRedirectsToDetails()
        {
            // Arrange
            var requirementId = 1;
            var comments = "Approved with minor changes";

            // Setup User.FindFirstValue for audit logging
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, _testUser.Id)
            };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            _mockRequirementService.Setup(x => x.ApproveRequirementAsync(requirementId, _testUser.Id, comments))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.Approve(requirementId, comments);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("Details");
            redirectResult.RouteValues!["id"].Should().Be(requirementId);
            _controller.TempData["Success"].Should().Be("Requirement approved successfully.");

            // Verify audit logging
            _mockAuditService.Verify(x => x.LogAsync(
                AuditAction.Approve,
                "Requirement",
                requirementId,
                _testUser.Id,
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>()), Times.Once);
        }

        [Fact]
        public async Task Approve_WithFailedApproval_RedirectsToDetailsWithError()
        {
            // Arrange
            var requirementId = 1;
            var comments = "Test comments";

            // Setup User.FindFirstValue
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, _testUser.Id)
            };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            _mockRequirementService.Setup(x => x.ApproveRequirementAsync(requirementId, _testUser.Id, comments))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.Approve(requirementId, comments);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("Details");
            redirectResult.RouteValues!["id"].Should().Be(requirementId);
            _controller.TempData["Error"].Should().Be("Failed to approve requirement.");
        }

        [Fact]
        public async Task Approve_WithException_RedirectsToDetailsWithError()
        {
            // Arrange
            var requirementId = 1;
            var comments = "Test comments";

            // Setup User.FindFirstValue
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, _testUser.Id)
            };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            _mockRequirementService.Setup(x => x.ApproveRequirementAsync(requirementId, _testUser.Id, comments))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.Approve(requirementId, comments);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("Details");
            redirectResult.RouteValues!["id"].Should().Be(requirementId);
            _controller.TempData["Error"].Should().Be("Error approving requirement.");
        }

        [Fact]
        public async Task Reject_WithValidRequest_RejectsAndRedirectsToDetails()
        {
            // Arrange
            var requirementId = 1;
            var reason = "Does not meet business requirements";

            // Setup User.FindFirstValue for audit logging
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, _testUser.Id)
            };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            _mockRequirementService.Setup(x => x.RejectRequirementAsync(requirementId, _testUser.Id, reason))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.Reject(requirementId, reason);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("Details");
            redirectResult.RouteValues!["id"].Should().Be(requirementId);
            _controller.TempData["Success"].Should().Be("Requirement rejected.");

            // Verify audit logging
            _mockAuditService.Verify(x => x.LogAsync(
                AuditAction.Reject,
                "Requirement",
                requirementId,
                _testUser.Id,
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>()), Times.Once);
        }

        [Fact]
        public async Task Reject_WithFailedRejection_RedirectsToDetailsWithError()
        {
            // Arrange
            var requirementId = 1;
            var reason = "Test reason";

            // Setup User.FindFirstValue
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, _testUser.Id)
            };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            _mockRequirementService.Setup(x => x.RejectRequirementAsync(requirementId, _testUser.Id, reason))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.Reject(requirementId, reason);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("Details");
            redirectResult.RouteValues!["id"].Should().Be(requirementId);
            _controller.TempData["Error"].Should().Be("Failed to reject requirement.");
        }

        [Fact]
        public async Task Hierarchy_WithValidProject_ReturnsViewWithHierarchy()
        {
            // Arrange
            var projectId = 1;
            var project = new Project { Id = projectId, Name = "Test Project" };
            var requirements = new List<Requirement>
            {
                new Requirement
                {
                    Id = 1,
                    Title = "Parent Requirement",
                    ProjectId = projectId,
                    ParentRequirementId = null
                },
                new Requirement
                {
                    Id = 2,
                    Title = "Child Requirement",
                    ProjectId = projectId,
                    ParentRequirementId = 1
                }
            };

            _mockRequirementService.Setup(x => x.GetRequirementHierarchyAsync(projectId))
                .ReturnsAsync(requirements);
            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync(project);

            // Act
            var result = await _controller.Hierarchy(projectId);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<List<Requirement>>();
            var model = viewResult.Model as List<Requirement>;
            model!.Should().HaveCount(2);
            viewResult.ViewData["ProjectName"].Should().Be("Test Project");
            viewResult.ViewData["ProjectId"].Should().Be(projectId);
        }

        [Fact]
        public async Task Hierarchy_WithException_RedirectsToIndexWithError()
        {
            // Arrange
            var projectId = 1;
            _mockRequirementService.Setup(x => x.GetRequirementHierarchyAsync(projectId))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.Hierarchy(projectId);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("Index");
            redirectResult.RouteValues!["projectId"].Should().Be(projectId);
            _controller.TempData["Error"].Should().Be("Error loading requirements hierarchy.");
        }

        [Fact]
        public async Task Analytics_WithValidProject_ReturnsViewWithAnalytics()
        {
            // Arrange
            var projectId = 1;
            var project = new Project { Id = projectId, Name = "Test Project" };
            var typeDistribution = new Dictionary<RequirementType, int>
            {
                { RequirementType.Functional, 5 },
                { RequirementType.NonFunctional, 3 }
            };
            var statusDistribution = new Dictionary<RequirementStatus, int>
            {
                { RequirementStatus.Draft, 2 },
                { RequirementStatus.Approved, 6 }
            };
            var priorityDistribution = new Dictionary<RequirementPriority, int>
            {
                { RequirementPriority.High, 3 },
                { RequirementPriority.Medium, 5 }
            };

            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync(project);
            _mockRequirementService.Setup(x => x.GetRequirementTypeDistributionAsync(projectId))
                .ReturnsAsync(typeDistribution);
            _mockRequirementService.Setup(x => x.GetRequirementStatusDistributionAsync(projectId))
                .ReturnsAsync(statusDistribution);
            _mockRequirementService.Setup(x => x.GetRequirementPriorityDistributionAsync(projectId))
                .ReturnsAsync(priorityDistribution);
            _mockRequirementService.Setup(x => x.GetRequirementCompletionPercentageAsync(projectId))
                .ReturnsAsync(75.5);
            _mockRequirementService.Setup(x => x.GetTotalEstimatedEffortAsync(projectId))
                .ReturnsAsync(120.5m);

            // Act
            var result = await _controller.Analytics(projectId);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.ViewData["ProjectName"].Should().Be("Test Project");
            viewResult.ViewData["ProjectId"].Should().Be(projectId);
            viewResult.ViewData["TypeDistribution"].Should().Be(typeDistribution);
            viewResult.ViewData["StatusDistribution"].Should().Be(statusDistribution);
            viewResult.ViewData["PriorityDistribution"].Should().Be(priorityDistribution);
            viewResult.ViewData["CompletionPercentage"].Should().Be(75.5);
            viewResult.ViewData["TotalEffort"].Should().Be(120.5m);
        }

        [Fact]
        public async Task Analytics_WithException_RedirectsToIndexWithError()
        {
            // Arrange
            var projectId = 1;
            _mockRequirementService.Setup(x => x.GetRequirementTypeDistributionAsync(projectId))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.Analytics(projectId);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult!.ActionName.Should().Be("Index");
            redirectResult.RouteValues!["projectId"].Should().Be(projectId);
            _controller.TempData["Error"].Should().Be("Error loading requirement analytics.");
        }

        [Fact]
        public async Task PendingApproval_WithValidUser_ReturnsViewWithPendingRequirements()
        {
            // Arrange
            var pendingRequirements = new List<Requirement>
            {
                new Requirement
                {
                    Id = 1,
                    Title = "Pending Requirement 1",
                    Status = RequirementStatus.UnderReview
                },
                new Requirement
                {
                    Id = 2,
                    Title = "Pending Requirement 2",
                    Status = RequirementStatus.UnderReview
                }
            };

            // Setup User.FindFirstValue
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, _testUser.Id)
            };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            _mockRequirementService.Setup(x => x.GetRequirementsPendingApprovalAsync(_testUser.Id))
                .ReturnsAsync(pendingRequirements);

            // Act
            var result = await _controller.PendingApproval();

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<List<Requirement>>();
            var model = viewResult.Model as List<Requirement>;
            model!.Should().HaveCount(2);
            model[0].Title.Should().Be("Pending Requirement 1");
            model[1].Title.Should().Be("Pending Requirement 2");
        }

        [Fact]
        public async Task PendingApproval_WithException_ReturnsEmptyViewWithError()
        {
            // Arrange
            // Setup User.FindFirstValue
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, _testUser.Id)
            };
            var identity = new ClaimsIdentity(claims, "Test");
            _controller.ControllerContext.HttpContext.User = new ClaimsPrincipal(identity);

            _mockRequirementService.Setup(x => x.GetRequirementsPendingApprovalAsync(_testUser.Id))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.PendingApproval();

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<List<Requirement>>();
            var model = viewResult.Model as List<Requirement>;
            model!.Should().BeEmpty();
            _controller.TempData["Error"].Should().Be("Error loading pending approvals.");
        }
    }
}
