﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <!-- Common UI Elements - Spanish -->
  <data name="Common.Save" xml:space="preserve">
    <value>Guardar</value>
  </data>
  <data name="Common.Cancel" xml:space="preserve">
    <value>Cancelar</value>
  </data>
  <data name="Common.Delete" xml:space="preserve">
    <value>Eliminar</value>
  </data>
  <data name="Common.Edit" xml:space="preserve">
    <value>Editar</value>
  </data>
  <data name="Common.Create" xml:space="preserve">
    <value>Crear</value>
  </data>
  <data name="Common.Update" xml:space="preserve">
    <value>Actualizar</value>
  </data>
  <data name="Common.Details" xml:space="preserve">
    <value>Detalles</value>
  </data>
  <data name="Common.Back" xml:space="preserve">
    <value>AtrÃ¡s</value>
  </data>
  <data name="Common.Next" xml:space="preserve">
    <value>Siguiente</value>
  </data>
  <data name="Common.Previous" xml:space="preserve">
    <value>Anterior</value>
  </data>
  <data name="Common.Search" xml:space="preserve">
    <value>Buscar</value>
  </data>
  <data name="Common.Filter" xml:space="preserve">
    <value>Filtrar</value>
  </data>
  <data name="Common.Export" xml:space="preserve">
    <value>Exportar</value>
  </data>
  <data name="Common.Import" xml:space="preserve">
    <value>Importar</value>
  </data>
  <data name="Common.Print" xml:space="preserve">
    <value>Imprimir</value>
  </data>
  <data name="Common.Download" xml:space="preserve">
    <value>Descargar</value>
  </data>
  <data name="Common.Upload" xml:space="preserve">
    <value>Subir</value>
  </data>
  <data name="Common.Yes" xml:space="preserve">
    <value>SÃ­</value>
  </data>
  <data name="Common.No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="Common.OK" xml:space="preserve">
    <value>Aceptar</value>
  </data>
  <data name="Common.Close" xml:space="preserve">
    <value>Cerrar</value>
  </data>
  <data name="Common.Loading" xml:space="preserve">
    <value>Cargando...</value>
  </data>
  <data name="Common.NoData" xml:space="preserve">
    <value>No hay datos disponibles</value>
  </data>
  <data name="Common.Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="Common.Success" xml:space="preserve">
    <value>Ã‰xito</value>
  </data>
  <data name="Common.Warning" xml:space="preserve">
    <value>Advertencia</value>
  </data>
  <data name="Common.Information" xml:space="preserve">
    <value>InformaciÃ³n</value>
  </data>
  <!-- Navigation - Spanish -->
  <data name="Nav.Dashboard" xml:space="preserve">
    <value>Panel de Control</value>
  </data>
  <data name="Nav.Projects" xml:space="preserve">
    <value>Proyectos</value>
  </data>
  <data name="Nav.Tasks" xml:space="preserve">
    <value>Tareas</value>
  </data>
  <data name="Nav.Management" xml:space="preserve">
    <value>GestiÃ³n</value>
  </data>
  <data name="Nav.Analytics" xml:space="preserve">
    <value>AnalÃ­ticas</value>
  </data>
  <data name="Nav.Resources" xml:space="preserve">
    <value>Recursos</value>
  </data>
  <data name="Nav.Risks" xml:space="preserve">
    <value>Riesgos</value>
  </data>
  <data name="Nav.Meetings" xml:space="preserve">
    <value>Reuniones</value>
  </data>
  <data name="Nav.Requirements" xml:space="preserve">
    <value>Requisitos</value>
  </data>
  <data name="Nav.Backlog" xml:space="preserve">
    <value>Backlog</value>
  </data>
  <data name="Nav.Kanban" xml:space="preserve">
    <value>Tablero Kanban</value>
  </data>
  <data name="Nav.Documentation" xml:space="preserve">
    <value>DocumentaciÃ³n</value>
  </data>
  <data name="Nav.SkillsManagement" xml:space="preserve">
    <value>GestiÃ³n de Habilidades</value>
  </data>
  <data name="Nav.ResourceUtilization" xml:space="preserve">
    <value>UtilizaciÃ³n de Recursos</value>
  </data>
  <data name="Nav.MeetingCalendar" xml:space="preserve">
    <value>Calendario de Reuniones</value>
  </data>
  <data name="Nav.ActionItems" xml:space="preserve">
    <value>Elementos de AcciÃ³n</value>
  </data>
  <data name="Nav.AnalyticsDashboard" xml:space="preserve">
    <value>Panel de AnalÃ­ticas</value>
  </data>
  <data name="Nav.AdvancedReports" xml:space="preserve">
    <value>Reportes Avanzados</value>
  </data>
  <data name="Nav.TeamAnalytics" xml:space="preserve">
    <value>AnalÃ­ticas de Equipo</value>
  </data>
  <data name="Nav.BurndownCharts" xml:space="preserve">
    <value>GrÃ¡ficos Burndown</value>
  </data>
  <data name="Nav.VelocityCharts" xml:space="preserve">
    <value>GrÃ¡ficos de Velocidad</value>
  </data>
  <data name="Nav.ExportData" xml:space="preserve">
    <value>Exportar Datos</value>
  </data>
  <!-- Project Management - Spanish -->
  <data name="Project.Title" xml:space="preserve">
    <value>TÃ­tulo</value>
  </data>
  <data name="Project.Description" xml:space="preserve">
    <value>DescripciÃ³n</value>
  </data>
  <data name="Project.StartDate" xml:space="preserve">
    <value>Fecha de Inicio</value>
  </data>
  <data name="Project.EndDate" xml:space="preserve">
    <value>Fecha de Fin</value>
  </data>
  <data name="Project.Status" xml:space="preserve">
    <value>Estado</value>
  </data>
  <data name="Project.Priority" xml:space="preserve">
    <value>Prioridad</value>
  </data>
  <data name="Project.Budget" xml:space="preserve">
    <value>Presupuesto</value>
  </data>
  <data name="Project.Progress" xml:space="preserve">
    <value>Progreso</value>
  </data>
  <data name="Project.Manager" xml:space="preserve">
    <value>Gerente de Proyecto</value>
  </data>
  <data name="Project.Team" xml:space="preserve">
    <value>Equipo</value>
  </data>
  <!-- Task Management - Spanish -->
  <data name="Task.Title" xml:space="preserve">
    <value>TÃ­tulo de Tarea</value>
  </data>
  <data name="Task.Description" xml:space="preserve">
    <value>DescripciÃ³n de Tarea</value>
  </data>
  <data name="Task.AssignedTo" xml:space="preserve">
    <value>Asignado a</value>
  </data>
  <data name="Task.DueDate" xml:space="preserve">
    <value>Fecha de Vencimiento</value>
  </data>
  <data name="Task.EstimatedHours" xml:space="preserve">
    <value>Horas Estimadas</value>
  </data>
  <data name="Task.ActualHours" xml:space="preserve">
    <value>Horas Reales</value>
  </data>
  <data name="Task.StoryPoints" xml:space="preserve">
    <value>Puntos de Historia</value>
  </data>
  <!-- Agile Terms - Spanish -->
  <data name="Agile.Epic" xml:space="preserve">
    <value>Ã‰pica</value>
  </data>
  <data name="Agile.UserStory" xml:space="preserve">
    <value>Historia de Usuario</value>
  </data>
  <data name="Agile.Sprint" xml:space="preserve">
    <value>Sprint</value>
  </data>
  <data name="Agile.Backlog" xml:space="preserve">
    <value>Backlog</value>
  </data>
  <data name="Agile.Kanban" xml:space="preserve">
    <value>Kanban</value>
  </data>
  <data name="Agile.Scrum" xml:space="preserve">
    <value>Scrum</value>
  </data>
  <data name="Agile.Velocity" xml:space="preserve">
    <value>Velocidad</value>
  </data>
  <data name="Agile.Burndown" xml:space="preserve">
    <value>Burndown</value>
  </data>
  <!-- Status Values - Spanish -->
  <data name="Status.Active" xml:space="preserve">
    <value>Activo</value>
  </data>
  <data name="Status.Inactive" xml:space="preserve">
    <value>Inactivo</value>
  </data>
  <data name="Status.Completed" xml:space="preserve">
    <value>Completado</value>
  </data>
  <data name="Status.InProgress" xml:space="preserve">
    <value>En Progreso</value>
  </data>
  <data name="Status.Pending" xml:space="preserve">
    <value>Pendiente</value>
  </data>
  <data name="Status.Cancelled" xml:space="preserve">
    <value>Cancelado</value>
  </data>
  <!-- Priority Values - Spanish -->
  <data name="Priority.Critical" xml:space="preserve">
    <value>CrÃ­tica</value>
  </data>
  <data name="Priority.High" xml:space="preserve">
    <value>Alta</value>
  </data>
  <data name="Priority.Medium" xml:space="preserve">
    <value>Media</value>
  </data>
  <data name="Priority.Low" xml:space="preserve">
    <value>Baja</value>
  </data>
  <!-- Messages - Spanish -->
  <data name="Message.SaveSuccess" xml:space="preserve">
    <value>Elemento guardado exitosamente</value>
  </data>
  <data name="Message.DeleteSuccess" xml:space="preserve">
    <value>Elemento eliminado exitosamente</value>
  </data>
  <data name="Message.UpdateSuccess" xml:space="preserve">
    <value>Elemento actualizado exitosamente</value>
  </data>
  <data name="Message.ErrorOccurred" xml:space="preserve">
    <value>OcurriÃ³ un error. Por favor, intÃ©ntelo de nuevo.</value>
  </data>
  <data name="Message.ConfirmDelete" xml:space="preserve">
    <value>Â¿EstÃ¡ seguro de que desea eliminar este elemento?</value>
  </data>
  <!-- Meeting Management - Spanish -->
  <data name="Meeting.Title" xml:space="preserve">
    <value>TÃ­tulo de la ReuniÃ³n</value>
  </data>
  <data name="Meeting.Description" xml:space="preserve">
    <value>DescripciÃ³n</value>
  </data>
  <data name="Meeting.StartTime" xml:space="preserve">
    <value>Hora de Inicio</value>
  </data>
  <data name="Meeting.EndTime" xml:space="preserve">
    <value>Hora de Fin</value>
  </data>
  <data name="Meeting.Location" xml:space="preserve">
    <value>UbicaciÃ³n</value>
  </data>
  <data name="Meeting.Type" xml:space="preserve">
    <value>Tipo de ReuniÃ³n</value>
  </data>
  <data name="Meeting.Status" xml:space="preserve">
    <value>Estado</value>
  </data>
  <data name="Meeting.Organizer" xml:space="preserve">
    <value>Organizador</value>
  </data>
  <data name="Meeting.Attendees" xml:space="preserve">
    <value>Asistentes</value>
  </data>
  <data name="Meeting.ActionItems" xml:space="preserve">
    <value>Elementos de AcciÃ³n</value>
  </data>
  <data name="Meeting.Documents" xml:space="preserve">
    <value>Documentos</value>
  </data>
  <data name="Meeting.Minutes" xml:space="preserve">
    <value>Actas de la ReuniÃ³n</value>
  </data>
  <data name="Meeting.Agenda" xml:space="preserve">
    <value>Agenda</value>
  </data>
  <!-- Requirements Management - Spanish -->
  <data name="Requirement.Title" xml:space="preserve">
    <value>TÃ­tulo del Requisito</value>
  </data>
  <data name="Requirement.Description" xml:space="preserve">
    <value>DescripciÃ³n</value>
  </data>
  <data name="Requirement.Type" xml:space="preserve">
    <value>Tipo</value>
  </data>
  <data name="Requirement.Priority" xml:space="preserve">
    <value>Prioridad</value>
  </data>
  <data name="Requirement.Status" xml:space="preserve">
    <value>Estado</value>
  </data>
  <data name="Requirement.Source" xml:space="preserve">
    <value>Fuente</value>
  </data>
  <data name="Requirement.Stakeholder" xml:space="preserve">
    <value>Interesado</value>
  </data>
  <data name="Requirement.AcceptanceCriteria" xml:space="preserve">
    <value>Criterios de AceptaciÃ³n</value>
  </data>
  <data name="Requirement.BusinessValue" xml:space="preserve">
    <value>Valor de Negocio</value>
  </data>
  <data name="Requirement.Comments" xml:space="preserve">
    <value>Comentarios</value>
  </data>
  <data name="Requirement.Attachments" xml:space="preserve">
    <value>Adjuntos</value>
  </data>
  <data name="Requirement.Changes" xml:space="preserve">
    <value>Historial de Cambios</value>
  </data>
  <data name="Requirement.Tasks" xml:space="preserve">
    <value>Tareas Relacionadas</value>
  </data>
  <!-- Risk Management - Spanish -->
  <data name="Risk.Title" xml:space="preserve">
    <value>TÃ­tulo del Riesgo</value>
  </data>
  <data name="Risk.Description" xml:space="preserve">
    <value>DescripciÃ³n</value>
  </data>
  <data name="Risk.Category" xml:space="preserve">
    <value>CategorÃ­a</value>
  </data>
  <data name="Risk.Probability" xml:space="preserve">
    <value>Probabilidad</value>
  </data>
  <data name="Risk.Impact" xml:space="preserve">
    <value>Impacto</value>
  </data>
  <data name="Risk.Score" xml:space="preserve">
    <value>PuntuaciÃ³n de Riesgo</value>
  </data>
  <data name="Risk.Status" xml:space="preserve">
    <value>Estado</value>
  </data>
  <data name="Risk.Owner" xml:space="preserve">
    <value>Propietario del Riesgo</value>
  </data>
  <data name="Risk.MitigationPlan" xml:space="preserve">
    <value>Plan de MitigaciÃ³n</value>
  </data>
  <data name="Risk.MitigationActions" xml:space="preserve">
    <value>Acciones de MitigaciÃ³n</value>
  </data>
  <data name="Risk.ContingencyPlan" xml:space="preserve">
    <value>Plan de Contingencia</value>
  </data>
  <!-- Resource Management - Spanish -->
  <data name="Resource.Name" xml:space="preserve">
    <value>Nombre del Recurso</value>
  </data>
  <data name="Resource.Type" xml:space="preserve">
    <value>Tipo</value>
  </data>
  <data name="Resource.Department" xml:space="preserve">
    <value>Departamento</value>
  </data>
  <data name="Resource.Location" xml:space="preserve">
    <value>UbicaciÃ³n</value>
  </data>
  <data name="Resource.HourlyRate" xml:space="preserve">
    <value>Tarifa por Hora</value>
  </data>
  <data name="Resource.Capacity" xml:space="preserve">
    <value>Capacidad</value>
  </data>
  <data name="Resource.Skills" xml:space="preserve">
    <value>Habilidades</value>
  </data>
  <data name="Resource.Availability" xml:space="preserve">
    <value>Disponibilidad</value>
  </data>
  <data name="Resource.Utilization" xml:space="preserve">
    <value>UtilizaciÃ³n</value>
  </data>
  <data name="Resource.Allocation" xml:space="preserve">
    <value>AsignaciÃ³n</value>
  </data>
  <!-- Agile/Scrum Terms - Spanish -->
  <data name="Agile.SprintPlanning" xml:space="preserve">
    <value>PlanificaciÃ³n del Sprint</value>
  </data>
  <data name="Agile.SprintReview" xml:space="preserve">
    <value>RevisiÃ³n del Sprint</value>
  </data>
  <data name="Agile.SprintRetrospective" xml:space="preserve">
    <value>Retrospectiva del Sprint</value>
  </data>
  <data name="Agile.DailyStandup" xml:space="preserve">
    <value>ReuniÃ³n Diaria</value>
  </data>
  <data name="Agile.ProductBacklog" xml:space="preserve">
    <value>Backlog del Producto</value>
  </data>
  <data name="Agile.SprintBacklog" xml:space="preserve">
    <value>Backlog del Sprint</value>
  </data>
  <data name="Agile.Definition" xml:space="preserve">
    <value>DefiniciÃ³n de Terminado</value>
  </data>
  <data name="Agile.StoryPoints" xml:space="preserve">
    <value>Puntos de Historia</value>
  </data>
  <data name="Agile.SprintStarted" xml:space="preserve">
    <value>Sprint iniciado exitosamente</value>
  </data>
  <data name="Agile.SprintCompleted" xml:space="preserve">
    <value>Sprint completado exitosamente</value>
  </data>
  <!-- Enum Values - Meeting Types - Spanish -->
  <data name="Enum.MeetingType.General" xml:space="preserve">
    <value>General</value>
  </data>
  <data name="Enum.MeetingType.Standup" xml:space="preserve">
    <value>ReuniÃ³n Diaria</value>
  </data>
  <data name="Enum.MeetingType.Planning" xml:space="preserve">
    <value>PlanificaciÃ³n</value>
  </data>
  <data name="Enum.MeetingType.Review" xml:space="preserve">
    <value>RevisiÃ³n</value>
  </data>
  <data name="Enum.MeetingType.Retrospective" xml:space="preserve">
    <value>Retrospectiva</value>
  </data>
  <data name="Enum.MeetingType.Stakeholder" xml:space="preserve">
    <value>Interesados</value>
  </data>
  <data name="Enum.MeetingType.Requirements" xml:space="preserve">
    <value>Requisitos</value>
  </data>
  <data name="Enum.MeetingType.Technical" xml:space="preserve">
    <value>TÃ©cnica</value>
  </data>
  <data name="Enum.MeetingType.Status" xml:space="preserve">
    <value>Estado</value>
  </data>
  <!-- Enum Values - Meeting Status - Spanish -->
  <data name="Enum.MeetingStatus.Scheduled" xml:space="preserve">
    <value>Programada</value>
  </data>
  <data name="Enum.MeetingStatus.InProgress" xml:space="preserve">
    <value>En Progreso</value>
  </data>
  <data name="Enum.MeetingStatus.Completed" xml:space="preserve">
    <value>Completada</value>
  </data>
  <data name="Enum.MeetingStatus.Cancelled" xml:space="preserve">
    <value>Cancelada</value>
  </data>
  <data name="Enum.MeetingStatus.Postponed" xml:space="preserve">
    <value>Pospuesta</value>
  </data>
  <!-- Enum Values - Requirement Types - Spanish -->
  <data name="Enum.RequirementType.Functional" xml:space="preserve">
    <value>Funcional</value>
  </data>
  <data name="Enum.RequirementType.NonFunctional" xml:space="preserve">
    <value>No Funcional</value>
  </data>
  <data name="Enum.RequirementType.Business" xml:space="preserve">
    <value>Negocio</value>
  </data>
  <data name="Enum.RequirementType.Technical" xml:space="preserve">
    <value>TÃ©cnico</value>
  </data>
  <data name="Enum.RequirementType.Performance" xml:space="preserve">
    <value>Rendimiento</value>
  </data>
  <data name="Enum.RequirementType.Security" xml:space="preserve">
    <value>Seguridad</value>
  </data>
  <data name="Enum.RequirementType.Usability" xml:space="preserve">
    <value>Usabilidad</value>
  </data>
  <data name="Enum.RequirementType.Compliance" xml:space="preserve">
    <value>Cumplimiento</value>
  </data>
  <data name="Enum.RequirementType.Integration" xml:space="preserve">
    <value>IntegraciÃ³n</value>
  </data>
  <!-- Enum Values - Risk Categories - Spanish -->
  <data name="Enum.RiskCategory.Technical" xml:space="preserve">
    <value>TÃ©cnico</value>
  </data>
  <data name="Enum.RiskCategory.Schedule" xml:space="preserve">
    <value>Cronograma</value>
  </data>
  <data name="Enum.RiskCategory.Budget" xml:space="preserve">
    <value>Presupuesto</value>
  </data>
  <data name="Enum.RiskCategory.Resource" xml:space="preserve">
    <value>Recurso</value>
  </data>
  <data name="Enum.RiskCategory.Quality" xml:space="preserve">
    <value>Calidad</value>
  </data>
  <data name="Enum.RiskCategory.External" xml:space="preserve">
    <value>Externo</value>
  </data>
  <data name="Enum.RiskCategory.Organizational" xml:space="preserve">
    <value>Organizacional</value>
  </data>
  <!-- Enum Values - Resource Types - Spanish -->
  <data name="Enum.ResourceType.Human" xml:space="preserve">
    <value>Recurso Humano</value>
  </data>
  <data name="Enum.ResourceType.Equipment" xml:space="preserve">
    <value>Equipo</value>
  </data>
  <data name="Enum.ResourceType.Material" xml:space="preserve">
    <value>Material</value>
  </data>
  <data name="Enum.ResourceType.Software" xml:space="preserve">
    <value>Software</value>
  </data>
  <data name="Enum.ResourceType.Facility" xml:space="preserve">
    <value>InstalaciÃ³n</value>
  </data>
  <data name="WBS.ErrorLoadingStructure" xml:space="preserve">
    <value>Error al cargar la estructura WBS</value>
  </data>
  <data name="WBS.TaskCreatedSuccessfully" xml:space="preserve">
    <value>Tarea creada exitosamente</value>
  </data>
  <data name="WBS.TaskDeletedSuccessfully" xml:space="preserve">
    <value>Tarea eliminada exitosamente</value>
  </data>
  <data name="WBS.TaskDuplicatedSuccessfully" xml:space="preserve">
    <value>Tarea duplicada exitosamente</value>
  </data>
  <data name="WBS.TaskMovedSuccessfully" xml:space="preserve">
    <value>Tarea movida {0} exitosamente</value>
  </data>
  <data name="WBS.TaskStatusUpdated" xml:space="preserve">
    <value>Estado de tarea actualizado a {0}</value>
  </data>
  <data name="WBS.WbsCodesGenerated" xml:space="preserve">
    <value>CÃ³digos WBS generados exitosamente</value>
  </data>
  <data name="WBS.ExportingTo" xml:space="preserve">
    <value>Exportando WBS a {0}...</value>
  </data>
  <data name="WBS.ExportingTask" xml:space="preserve">
    <value>Exportando tarea...</value>
  </data>
  <data name="WBS.PrintingWbs" xml:space="preserve">
    <value>Abriendo diÃ¡logo de impresiÃ³n...</value>
  </data>
  <data name="WBS.CompactViewEnabled" xml:space="preserve">
    <value>Vista compacta habilitada</value>
  </data>
  <data name="WBS.NormalViewEnabled" xml:space="preserve">
    <value>Vista normal habilitada</value>
  </data>
  <data name="WBS.DetailedViewEnabled" xml:space="preserve">
    <value>Vista detallada habilitada</value>
  </data>
  <data name="WBS.RefreshingView" xml:space="preserve">
    <value>Actualizando vista WBS...</value>
  </data>
  <data name="WBS.FilteredToShow" xml:space="preserve">
    <value>Filtrado para mostrar tareas {0}</value>
  </data>
  <data name="WBS.ShowingAllTasks" xml:space="preserve">
    <value>Mostrando todas las tareas</value>
  </data>
  <data name="WBS.FilteredOverdue" xml:space="preserve">
    <value>Filtrado para mostrar tareas vencidas</value>
  </data>
  <data name="WBS.TaskTitleRequired" xml:space="preserve">
    <value>El tÃ­tulo de la tarea es requerido</value>
  </data>
  <data name="WBS.ProjectIdMissing" xml:space="preserve">
    <value>Falta el ID del proyecto</value>
  </data>
  <data name="WBS.ValidationError" xml:space="preserve">
    <value>Error de validaciÃ³n. Por favor verifique su entrada.</value>
  </data>
  <data name="WBS.AccessDenied" xml:space="preserve">
    <value>Acceso denegado. Por favor actualice la pÃ¡gina e intente de nuevo.</value>
  </data>
  <data name="WBS.RequestFormatError" xml:space="preserve">
    <value>Error de formato de solicitud. Por favor intente de nuevo.</value>
  </data>
  <data name="WBS.ErrorCreatingTask" xml:space="preserve">
    <value>Error al crear la tarea</value>
  </data>
  <data name="WBS.ErrorLoadingTaskDetails" xml:space="preserve">
    <value>Error al cargar los detalles de la tarea</value>
  </data>
  <data name="WBS.ErrorDeletingTask" xml:space="preserve">
    <value>Error al eliminar la tarea</value>
  </data>
  <data name="WBS.ErrorDuplicatingTask" xml:space="preserve">
    <value>Error al duplicar la tarea</value>
  </data>
  <data name="WBS.ErrorMovingTask" xml:space="preserve">
    <value>Error al mover la tarea {0}</value>
  </data>
  <data name="WBS.ErrorUpdatingTaskStatus" xml:space="preserve">
    <value>Error al actualizar el estado de la tarea</value>
  </data>
  <data name="WBS.ErrorGeneratingCodes" xml:space="preserve">
    <value>Error al generar cÃ³digos WBS</value>
  </data>
  <data name="WBS.ConfirmDeleteTask" xml:space="preserve">
    <value>Â¿EstÃ¡ seguro de que desea eliminar esta tarea? Esta acciÃ³n no se puede deshacer.</value>
  </data>
  <data name="WBS.ConfirmDuplicateTask" xml:space="preserve">
    <value>Â¿Crear un duplicado de esta tarea?</value>
  </data>
  <data name="WBS.ConfirmGenerateCodes" xml:space="preserve">
    <value>Esto regenerarÃ¡ todos los cÃ³digos WBS. Â¿Continuar?</value>
  </data>
  <data name="WBS.CreateNewTask" xml:space="preserve">
    <value>Crear nueva tarea</value>
  </data>
  <data name="WBS.CreateChildTaskFor" xml:space="preserve">
    <value>Crear tarea hija para: {0}</value>
  </data>
  <data name="WBS.TaskNotFound" xml:space="preserve">
    <value>Tarea no encontrada</value>
  </data>
  <data name="WBS.UnsupportedExportFormat" xml:space="preserve">
    <value>Formato de exportaciÃ³n no soportado</value>
  </data>
  <data name="WBS.GanttViewComingSoon" xml:space="preserve">
    <value>Â¡La vista Gantt llegarÃ¡ pronto!</value>
  </data>
  <data name="WBS.Action.ViewDetails" xml:space="preserve">
    <value>Ver detalles</value>
  </data>
  <data name="WBS.Action.EditTask" xml:space="preserve">
    <value>Editar tarea</value>
  </data>
  <data name="WBS.Action.Duplicate" xml:space="preserve">
    <value>Duplicar</value>
  </data>
  <data name="WBS.Action.AddChild" xml:space="preserve">
    <value>Agregar hijo</value>
  </data>
  <data name="WBS.Action.MoreActions" xml:space="preserve">
    <value>MÃ¡s acciones</value>
  </data>
  <data name="WBS.Action.MoveUp" xml:space="preserve">
    <value>Mover arriba</value>
  </data>
  <data name="WBS.Action.MoveDown" xml:space="preserve">
    <value>Mover abajo</value>
  </data>
  <data name="WBS.Action.StartTask" xml:space="preserve">
    <value>Iniciar tarea</value>
  </data>
  <data name="WBS.Action.MarkInReview" xml:space="preserve">
    <value>Marcar en revisiÃ³n</value>
  </data>
  <data name="WBS.Action.MarkComplete" xml:space="preserve">
    <value>Marcar como completo</value>
  </data>
  <data name="WBS.Action.CancelTask" xml:space="preserve">
    <value>Cancelar tarea</value>
  </data>
  <data name="WBS.Action.ExportTask" xml:space="preserve">
    <value>Exportar tarea</value>
  </data>
  <data name="WBS.Action.DeleteTask" xml:space="preserve">
    <value>Eliminar tarea</value>
  </data>
  <data name="WBS.Label.Progress" xml:space="preserve">
    <value>Progreso</value>
  </data>
  <data name="WBS.Label.Unassigned" xml:space="preserve">
    <value>Sin asignar</value>
  </data>
  <data name="WBS.Label.Overdue" xml:space="preserve">
    <value>Vencido</value>
  </data>
  <data name="WBS.Label.Start" xml:space="preserve">
    <value>Inicio</value>
  </data>
  <data name="WBS.Label.Due" xml:space="preserve">
    <value>Vencimiento</value>
  </data>
  <data name="WBS.Label.GeneratedOn" xml:space="preserve">
    <value>Generado el</value>
  </data>
  <data name="WBS.Label.WorkBreakdownStructure" xml:space="preserve">
    <value>Estructura de Desglose del Trabajo</value>
  </data>
</root>