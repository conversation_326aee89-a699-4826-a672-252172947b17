<script>
    // WBS UI JavaScript - Modal, dropdown, and interface management

    // Modal functions
    function showModal(modalId) {
        // Prevent showing modal during page load or before WBS is initialized
        if (!document.readyState || document.readyState !== 'complete' || !window.wbsInitialized) {
            console.warn('Preventing modal display during page load or before WBS initialization');
            return;
        }

        const modal = document.getElementById(modalId);
        if (modal) {
            // Show modal using the show class and prevent body scroll
            modal.classList.remove('hidden');
            modal.classList.add('show');
            document.body.style.overflow = 'hidden';

            // Focus management
            setTimeout(() => {
                const firstInput = modal.querySelector('input, select, textarea, button');
                if (firstInput) {
                    firstInput.focus();
                }
            }, 100);

            // Initialize Select2 for modal selects
            if (modalId === 'createTaskModal') {
                setTimeout(() => {
                    if (typeof initializeModalSelect2 === 'function') {
                        initializeModalSelect2();
                    }
                }, 200);
            }
        }
    }

    function hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            // Hide modal and restore body scroll
            modal.classList.add('hidden');
            modal.classList.remove('show');
            document.body.style.overflow = '';

            // Clean up any Select2 dropdowns that might be open
            try {
                $('#' + modalId + ' .select2-container').each(function() {
                    const $select = $(this).prev('select');
                    if ($select.hasClass('select2-hidden-accessible')) {
                        $select.select2('close');
                    }
                });
            } catch (e) {
                console.warn('Error closing Select2 dropdowns:', e);
            }

            // Clear any form data if it's the create task modal
            if (modalId === 'createTaskModal') {
                try {
                    $('#createTaskForm')[0].reset();
                } catch (e) {
                    console.warn('Error resetting form:', e);
                }
            }
        }
    }

    function hideModalOnOverlayClick(event, modalId) {
        // Only hide if clicking directly on the overlay (modal-overlay), not on child elements
        if (event.target.classList.contains('modal-overlay')) {
            hideModal(modalId);
        }
    }

    // Dropdown management
    function toggleDropdown(dropdownId) {
        // Hide all other dropdowns first
        $('.dropdown-menu').not(`#${dropdownId}`).addClass('hidden');

        // Toggle the clicked dropdown
        const dropdown = $(`#${dropdownId}`);
        dropdown.toggleClass('hidden');

        // Close dropdown when clicking outside
        if (!dropdown.hasClass('hidden')) {
            $(document).one('click', function(e) {
                if (!$(e.target).closest(`#${dropdownId}, [onclick*="toggleDropdown('${dropdownId}')"]`).length) {
                    dropdown.addClass('hidden');
                }
            });
        }
    }

    function hideDropdown(dropdownId) {
        $(`#${dropdownId}`).addClass('hidden');
    }

    function hideAllDropdowns() {
        $('.dropdown-menu').addClass('hidden');
    }

    // View options
    function toggleCompactView() {
        $('body').toggleClass('compact-view');
        const isCompact = $('body').hasClass('compact-view');
        showAlert(isCompact ? 'Compact view enabled' : 'Normal view enabled', 'info');
    }

    function toggleDetailedView() {
        $('body').toggleClass('detailed-view');
        const isDetailed = $('body').hasClass('detailed-view');
        showAlert(isDetailed ? 'Detailed view enabled' : 'Normal view enabled', 'info');
    }

    function toggleGanttView() {
        showAlert('Gantt view feature coming soon!', 'info');
        // TODO: Implement Gantt chart view
    }

    function refreshWbsView() {
        showAlert('Refreshing WBS view...', 'info');
        loadWbsStructure();
        updateWbsStatistics();
    }

    // Filter functions
    function filterByStatus(status) {
        if (status === 'all') {
            $('.wbs-node').show();
            showAlert('Showing all tasks', 'info');
        } else {
            $('.wbs-node').each(function() {
                const taskId = $(this).data('task-id');
                const task = findTaskById(wbsData, taskId);
                if (task && task.status && task.status.toLowerCase() === status.toLowerCase()) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
            showAlert(`Filtered to show ${status} tasks`, 'info');
        }
    }

    function filterByPriority(priority) {
        $('.wbs-node').each(function() {
            const taskId = $(this).data('task-id');
            const task = findTaskById(wbsData, taskId);
            if (task && task.priority && task.priority.toLowerCase() === priority.toLowerCase()) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
        showAlert(`Filtered to show ${priority} priority tasks`, 'info');
    }

    function filterOverdueTasks() {
        $('.wbs-node').each(function() {
            const taskId = $(this).data('task-id');
            const task = findTaskById(wbsData, taskId);
            const isOverdue = task && task.dueDate && new Date(task.dueDate) < new Date() && task.status !== 'Done';
            if (isOverdue) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
        showAlert('Filtered to show overdue tasks', 'info');
    }

    // Event listeners
    $(document).ready(function() {
        // Close dropdowns when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.dropdown-menu, [onclick*="toggleDropdown"]').length) {
                hideAllDropdowns();
            }
        });

        // Close modals with ESC key
        $(document).on('keydown', function(event) {
            if (event.key === 'Escape') {
                // Close any open modals
                $('.modal-overlay.show').each(function() {
                    hideModal(this.id);
                });

                // Close any open dropdowns
                hideAllDropdowns();

                // Close any open Select2 dropdowns
                try {
                    $('.select2-container--open').each(function() {
                        $(this).prev('select').select2('close');
                    });
                } catch (e) {
                    // Ignore Select2 errors
                }
            }
        });

        // Keyboard shortcuts for power users
        $(document).on('keydown', function(event) {
            // Only trigger shortcuts when not in input fields and no modals are open
            if (event.target.tagName.toLowerCase() === 'input' ||
                event.target.tagName.toLowerCase() === 'textarea' ||
                event.target.isContentEditable ||
                $('.modal-overlay.show').length > 0) {
                return;
            }

            // Ctrl/Cmd + shortcuts
            if (event.ctrlKey || event.metaKey) {
                switch(event.key.toLowerCase()) {
                    case 'n': // Ctrl+N - New Task
                        event.preventDefault();
                        showCreateTaskModal();
                        break;
                    case 'r': // Ctrl+R - Refresh
                        event.preventDefault();
                        refreshWbsView();
                        break;
                    case 'e': // Ctrl+E - Expand All
                        event.preventDefault();
                        expandAll();
                        break;
                    case 'w': // Ctrl+W - Collapse All
                        event.preventDefault();
                        collapseAll();
                        break;
                }
            }
        });
    });
</script>
