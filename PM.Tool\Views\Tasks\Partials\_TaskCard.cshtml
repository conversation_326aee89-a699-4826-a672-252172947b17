@model TaskViewModel
@using PM.Tool.Core.Enums
@using TaskStatus = PM.Tool.Core.Enums.TaskStatus

<div class="task-card card-custom <EMAIL>().ToLower() @(Model.IsOverdue ? "border-red-300" : "")">
    <div class="card-body-custom">
        <div class="flex items-start justify-between">
            <!-- Task Selection and Content -->
            <div class="flex items-start space-x-3 flex-1">
                <input type="checkbox" class="task-checkbox mt-1" value="@Model.Id" />
                <div class="flex-1">
                    <!-- Task Title and Status -->
                    <div class="flex items-center space-x-2 mb-2">
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-white">
                            <a href="@Url.Action("Details", new { id = Model.Id })" class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                                @Model.Title
                            </a>
                        </h3>
                        <span class="status-pill <EMAIL>().ToLower()">
                            @Model.Status.ToString()
                        </span>
                        @if (Model.Priority == TaskPriority.Critical || Model.Priority == TaskPriority.High)
                        {
                            <span class="priority-indicator <EMAIL>().ToLower()">
                                <i class="fas fa-flag"></i>
                                @Model.Priority
                            </span>
                        }
                    </div>

                    <!-- Task Description -->
                    @if (!string.IsNullOrEmpty(Model.Description))
                    {
                        <p class="text-neutral-600 dark:text-neutral-400 text-sm mb-3 line-clamp-2">
                            @Model.Description
                        </p>
                    }

                    <!-- Task Meta Information -->
                    <div class="flex flex-wrap items-center gap-4 text-sm text-neutral-500 dark:text-neutral-400">
                        <!-- Project -->
                        <div class="flex items-center">
                            <i class="fas fa-folder mr-1"></i>
                            <span>@Model.ProjectName</span>
                        </div>

                        <!-- Due Date -->
                        @if (Model.DueDate.HasValue)
                        {
                            <div class="flex items-center @(Model.IsOverdue ? "text-red-600 dark:text-red-400" : "")">
                                <i class="fas fa-calendar mr-1"></i>
                                <span>Due @Model.DueDate.Value.ToString("MMM dd, yyyy")</span>
                                @if (Model.IsOverdue)
                                {
                                    <i class="fas fa-exclamation-triangle ml-1 text-red-500"></i>
                                }
                            </div>
                        }

                        <!-- Assigned To -->
                        @if (!string.IsNullOrEmpty(Model.AssignedToName))
                        {
                            <div class="flex items-center">
                                <i class="fas fa-user mr-1"></i>
                                <span>@Model.AssignedToName</span>
                            </div>
                        }

                        <!-- Progress -->
                        @if (Model.SubTaskCount > 0)
                        {
                            <div class="flex items-center">
                                <i class="fas fa-tasks mr-1"></i>
                                <span>@Model.CompletedSubTasks/@Model.SubTaskCount subtasks</span>
                                <div class="w-16 h-2 bg-neutral-200 dark:bg-dark-600 rounded-full ml-2">
                                    <div class="h-2 bg-primary-600 rounded-full" style="width: @Model.Progress%"></div>
                                </div>
                            </div>
                        }

                        <!-- Time Tracking -->
                        @if (Model.EstimatedHours > 0)
                        {
                            <div class="flex items-center">
                                <i class="fas fa-clock mr-1"></i>
                                <span>@Model.ActualHours/@Model.EstimatedHours hrs</span>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Task Actions -->
            <div class="flex items-center space-x-2">
                <!-- Quick Status Change -->
                <div class="dropdown">
                    <button type="button" class="btn-secondary-custom text-sm dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu">
                        @if (Model.Status != TaskStatus.InProgress)
                        {
                            <li><a class="dropdown-item" href="#" onclick="updateTaskStatus(@Model.Id, 'InProgress')">
                                <i class="fas fa-play mr-2"></i>Start Task
                            </a></li>
                        }
                        @if (Model.Status != TaskStatus.Done)
                        {
                            <li><a class="dropdown-item" href="#" onclick="updateTaskStatus(@Model.Id, 'Done')">
                                <i class="fas fa-check mr-2"></i>Mark Complete
                            </a></li>
                        }
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="@Url.Action("Edit", new { id = Model.Id })">
                            <i class="fas fa-edit mr-2"></i>Edit
                        </a></li>
                        <li><a class="dropdown-item" href="@Url.Action("Details", new { id = Model.Id })">
                            <i class="fas fa-eye mr-2"></i>View Details
                        </a></li>
                        @if (Model.ParentTaskId == null)
                        {
                            <li><a class="dropdown-item" href="@Url.Action("Create", new { parentTaskId = Model.Id })">
                                <i class="fas fa-plus mr-2"></i>Add Subtask
                            </a></li>
                        }
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
