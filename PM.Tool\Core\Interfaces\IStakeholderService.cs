using PM.Tool.Core.Entities;

namespace PM.Tool.Core.Interfaces
{
    public interface IStakeholderService
    {
        // Stakeholder Management
        Task<IEnumerable<Stakeholder>> GetAllStakeholdersAsync();
        Task<IEnumerable<Stakeholder>> GetActiveStakeholdersAsync();
        Task<Stakeholder?> GetStakeholderByIdAsync(int id);
        Task<Stakeholder> CreateStakeholderAsync(Stakeholder stakeholder);
        Task<Stakeholder> UpdateStakeholderAsync(Stakeholder stakeholder);
        Task<bool> DeleteStakeholderAsync(int id);
        Task<bool> DeactivateStakeholderAsync(int id);
        
        // Project Stakeholder Management
        Task<IEnumerable<ProjectStakeholder>> GetProjectStakeholdersAsync(int projectId);
        Task<IEnumerable<Stakeholder>> GetStakeholdersByProjectAsync(int projectId);
        Task<ProjectStakeholder> AssignStakeholderToProjectAsync(int projectId, int stakeholderId, ProjectRole role, bool isKeyStakeholder = false);
        Task<bool> RemoveStakeholderFromProjectAsync(int projectId, int stakeholderId);
        Task<bool> UpdateProjectStakeholderRoleAsync(int projectId, int stakeholderId, ProjectRole newRole);
        Task<IEnumerable<Stakeholder>> GetKeyStakeholdersAsync(int projectId);
        
        // Stakeholder Analysis
        Task<IEnumerable<Stakeholder>> GetStakeholdersByInfluenceAsync(InfluenceLevel influence);
        Task<IEnumerable<Stakeholder>> GetStakeholdersByInterestAsync(InterestLevel interest);
        Task<IEnumerable<Stakeholder>> GetStakeholdersByPriorityAsync(StakeholderPriority priority);
        Task<Dictionary<StakeholderPriority, int>> GetStakeholderPriorityDistributionAsync(int projectId);
        Task<IEnumerable<object>> GetStakeholderInfluenceInterestMatrixAsync(int projectId);
        
        // Communication Management
        Task<IEnumerable<StakeholderCommunication>> GetStakeholderCommunicationsAsync(int stakeholderId);
        Task<IEnumerable<StakeholderCommunication>> GetProjectCommunicationsAsync(int projectId);
        Task<StakeholderCommunication> LogCommunicationAsync(StakeholderCommunication communication);
        Task<bool> UpdateCommunicationAsync(StakeholderCommunication communication);
        Task<bool> DeleteCommunicationAsync(int communicationId);
        
        // Communication Planning
        Task<IEnumerable<StakeholderCommunication>> GetPendingFollowUpsAsync(string userId);
        Task<IEnumerable<StakeholderCommunication>> GetCommunicationsByDateRangeAsync(int projectId, DateTime startDate, DateTime endDate);
        Task<bool> ScheduleFollowUpAsync(int communicationId, DateTime followUpDate);
        Task<bool> CompleteFollowUpAsync(int communicationId);
        
        // Stakeholder Engagement
        Task<Dictionary<string, object>> GetStakeholderEngagementMetricsAsync(int projectId);
        Task<IEnumerable<object>> GetCommunicationFrequencyReportAsync(int projectId, DateTime startDate, DateTime endDate);
        Task<IEnumerable<Stakeholder>> GetInactiveStakeholdersAsync(int projectId, int daysSinceLastCommunication = 30);
        
        // Stakeholder Search and Filtering
        Task<IEnumerable<Stakeholder>> SearchStakeholdersAsync(string searchTerm);
        Task<IEnumerable<Stakeholder>> FilterStakeholdersAsync(StakeholderType? type = null, StakeholderRole? role = null, InfluenceLevel? influence = null, InterestLevel? interest = null, bool? isActive = null);
        Task<IEnumerable<Stakeholder>> GetStakeholdersByOrganizationAsync(string organization);
        
        // Stakeholder Notifications
        Task<bool> NotifyStakeholdersAsync(int projectId, string subject, string message, IEnumerable<int>? stakeholderIds = null);
        Task<bool> SendProjectUpdateAsync(int projectId, string updateContent, bool keyStakeholdersOnly = false);
        Task<IEnumerable<Stakeholder>> GetStakeholdersForNotificationAsync(int projectId, bool keyStakeholdersOnly = false);
        
        // Stakeholder Reporting
        Task<Dictionary<string, object>> GetStakeholderReportAsync(int projectId);
        Task<IEnumerable<object>> GetStakeholderEngagementReportAsync(int projectId, DateTime startDate, DateTime endDate);
        Task<IEnumerable<object>> GetCommunicationEffectivenessReportAsync(int projectId);
        
        // Stakeholder Import/Export
        Task<bool> ImportStakeholdersAsync(IEnumerable<Stakeholder> stakeholders);
        Task<IEnumerable<Stakeholder>> ExportProjectStakeholdersAsync(int projectId);
        
        // Stakeholder Validation
        Task<bool> ValidateStakeholderEmailAsync(string email);
        Task<bool> CheckDuplicateStakeholderAsync(string email, int? excludeId = null);
        Task<IEnumerable<string>> ValidateStakeholderDataAsync(Stakeholder stakeholder);
    }
}
