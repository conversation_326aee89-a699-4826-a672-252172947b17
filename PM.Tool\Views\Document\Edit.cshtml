@model UpdateAttachmentMetadataViewModel
@{
    ViewData["Title"] = "Edit Document";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Edit Document</h2>
        <div>
            <a asp-action="AttachmentDetails" asp-route-id="@Model.Id" asp-route-type="@Model.AttachmentType" 
               class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-2"></i>Back to Details
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <form asp-action="UpdateMetadata" method="post">
                        <input type="hidden" asp-for="Id" />
                        <input type="hidden" asp-for="AttachmentType" />

                        <div class="mb-3">
                            <label asp-for="Description" class="form-label">Description</label>
                            <textarea asp-for="Description" class="form-control" rows="4"></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                            <small class="form-text text-muted">
                                Add a description to help others understand the document's purpose and contents.
                            </small>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Tags" class="form-label">Tags</label>
                            <input asp-for="Tags" class="form-control" />
                            <span asp-validation-for="Tags" class="text-danger"></span>
                            <small class="form-text text-muted">
                                Add tags separated by commas (e.g., contract, legal, draft)
                            </small>
                        </div>

                        <div class="mb-3">
                            <label asp-for="CategoryId" class="form-label">Category</label>
                            <select asp-for="CategoryId" class="form-select"
                                    asp-items="@(new SelectList(ViewBag.Categories, "Id", "Name"))">
                                <option value="">-- Select Category --</option>
                            </select>
                            <span asp-validation-for="CategoryId" class="text-danger"></span>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a asp-action="AttachmentDetails" asp-route-id="@Model.Id" asp-route-type="@Model.AttachmentType" 
                               class="btn btn-outline-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title">Tips for Document Organization</h5>
                    <ul class="list-unstyled">
                        <li class="mb-3">
                            <i class="bi bi-tag me-2"></i>
                            Use descriptive tags to make documents easier to find
                        </li>
                        <li class="mb-3">
                            <i class="bi bi-folder me-2"></i>
                            Categorize documents to keep them organized
                        </li>
                        <li class="mb-3">
                            <i class="bi bi-card-text me-2"></i>
                            Add clear descriptions to help team members understand the content
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
