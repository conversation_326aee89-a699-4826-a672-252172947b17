using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Tests.Integration.Infrastructure;
using System.Net;
using System.Net.Http.Json;
using Xunit;
using Newtonsoft.Json;
using TaskStatus = PM.Tool.Core.Enums.TaskStatus;

namespace PM.Tool.Tests.Integration.Controllers
{
    [Collection("Integration")]
    public class TasksControllerIntegrationTests : IntegrationTestBase
    {
        public TasksControllerIntegrationTests(CustomWebApplicationFactory factory) : base(factory)
        {
        }

        [Fact]
        public async Task Index_WithExistingTasks_ReturnsTasksFromDatabase()
        {
            // Arrange
            await ClearDatabaseAsync();
            var project = await CreateTestProjectAsync("Test Project");
            var task1 = await CreateTestTaskAsync(project.Id, "Task 1");
            var task2 = await CreateTestTaskAsync(project.Id, "Task 2");

            // Act
            var response = await _client.GetAsync("/Tasks");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            var content = await response.Content.ReadAsStringAsync();
            content.Should().Contain("Task 1");
            content.Should().Contain("Task 2");
        }

        [Fact]
        public async Task Index_WithProjectFilter_ReturnsOnlyProjectTasks()
        {
            // Arrange
            await ClearDatabaseAsync();
            var project1 = await CreateTestProjectAsync("Project 1");
            var project2 = await CreateTestProjectAsync("Project 2");
            var task1 = await CreateTestTaskAsync(project1.Id, "Project 1 Task");
            var task2 = await CreateTestTaskAsync(project2.Id, "Project 2 Task");

            // Act
            var response = await _client.GetAsync($"/Tasks?projectId={project1.Id}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            var content = await response.Content.ReadAsStringAsync();
            content.Should().Contain("Project 1 Task");
            content.Should().NotContain("Project 2 Task");
        }

        [Fact]
        public async Task Details_WithValidTaskId_ReturnsTaskDetails()
        {
            // Arrange
            await ClearDatabaseAsync();
            var project = await CreateTestProjectAsync("Test Project");
            var task = await CreateTestTaskAsync(project.Id, "Detailed Task");

            // Act
            var response = await _client.GetAsync($"/Tasks/Details/{task.Id}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            var content = await response.Content.ReadAsStringAsync();
            content.Should().Contain("Detailed Task");
            content.Should().Contain(task.Description);
        }

        [Fact]
        public async Task Details_WithNonExistentTask_ReturnsNotFound()
        {
            // Arrange
            await ClearDatabaseAsync();
            var nonExistentId = 99999;

            // Act
            var response = await _client.GetAsync($"/Tasks/Details/{nonExistentId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task Create_Get_WithProjectId_ReturnsCreateForm()
        {
            // Arrange
            await ClearDatabaseAsync();
            var project = await CreateTestProjectAsync("Test Project");

            // Act
            var response = await _client.GetAsync($"/Tasks/Create?projectId={project.Id}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            var content = await response.Content.ReadAsStringAsync();
            content.Should().Contain("Create Task");
            content.Should().Contain(project.Name);
        }

        [Fact]
        public async Task Create_Post_WithValidTask_SavesTaskToDatabase()
        {
            // Arrange
            await ClearDatabaseAsync();
            var project = await CreateTestProjectAsync("Test Project");

            var formData = new Dictionary<string, string>
            {
                ["Title"] = "Integration Test Task",
                ["Description"] = "This is an integration test task",
                ["ProjectId"] = project.Id.ToString(),
                ["Status"] = ((int)TaskStatus.ToDo).ToString(),
                ["Priority"] = ((int)TaskPriority.High).ToString(),
                ["DueDate"] = DateTime.Today.AddDays(7).ToString("yyyy-MM-dd")
            };

            var formContent = new FormUrlEncodedContent(formData);

            // Act
            var response = await _client.PostAsync("/Tasks/Create", formContent);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Redirect);

            // Verify task was saved to database
            var savedTask = await _context.Tasks
                .FirstOrDefaultAsync(t => t.Title == "Integration Test Task");

            savedTask.Should().NotBeNull();
            savedTask!.Description.Should().Be("This is an integration test task");
            savedTask.ProjectId.Should().Be(project.Id);
            savedTask.AssignedToUserId.Should().Be(_testUser.Id);
            savedTask.Status.Should().Be(TaskStatus.ToDo);
            savedTask.Priority.Should().Be(TaskPriority.High);
        }

        [Fact]
        public async Task Create_Post_WithInvalidTask_ReturnsFormWithErrors()
        {
            // Arrange
            await ClearDatabaseAsync();
            var project = await CreateTestProjectAsync("Test Project");

            var formData = new Dictionary<string, string>
            {
                ["Title"] = "", // Invalid - empty title
                ["Description"] = "Test description",
                ["ProjectId"] = project.Id.ToString()
            };

            var formContent = new FormUrlEncodedContent(formData);

            // Act
            var response = await _client.PostAsync("/Tasks/Create", formContent);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK); // Returns form with validation errors
            var content = await response.Content.ReadAsStringAsync();
            content.Should().Contain("field is required"); // Validation error message
        }

        [Fact]
        public async Task Edit_Get_WithValidTask_ReturnsEditForm()
        {
            // Arrange
            await ClearDatabaseAsync();
            var project = await CreateTestProjectAsync("Test Project");
            var task = await CreateTestTaskAsync(project.Id, "Task to Edit");

            // Act
            var response = await _client.GetAsync($"/Tasks/Edit/{task.Id}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            var content = await response.Content.ReadAsStringAsync();
            content.Should().Contain("Task to Edit");
            content.Should().Contain("Edit Task");
        }

        [Fact]
        public async Task Edit_Post_WithValidChanges_UpdatesTaskInDatabase()
        {
            // Arrange
            await ClearDatabaseAsync();
            var project = await CreateTestProjectAsync("Test Project");
            var task = await CreateTestTaskAsync(project.Id, "Original Task Title");

            var formData = new Dictionary<string, string>
            {
                ["Id"] = task.Id.ToString(),
                ["Title"] = "Updated Task Title",
                ["Description"] = "Updated description",
                ["ProjectId"] = project.Id.ToString(),
                ["Status"] = ((int)TaskStatus.InProgress).ToString(),
                ["Priority"] = ((int)TaskPriority.Critical).ToString()
            };

            var formContent = new FormUrlEncodedContent(formData);

            // Act
            var response = await _client.PostAsync($"/Tasks/Edit/{task.Id}", formContent);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Redirect);

            // Verify task was updated in database
            var updatedTask = await _context.Tasks.FindAsync(task.Id);
            updatedTask.Should().NotBeNull();
            updatedTask!.Title.Should().Be("Updated Task Title");
            updatedTask.Description.Should().Be("Updated description");
            updatedTask.Status.Should().Be(TaskStatus.InProgress);
            updatedTask.Priority.Should().Be(TaskPriority.Critical);
        }

        [Fact]
        public async Task Delete_WithValidTask_RemovesTaskFromDatabase()
        {
            // Arrange
            await ClearDatabaseAsync();
            var project = await CreateTestProjectAsync("Test Project");
            var task = await CreateTestTaskAsync(project.Id, "Task to Delete");

            var formData = new Dictionary<string, string>();
            var formContent = new FormUrlEncodedContent(formData);

            // Act
            var response = await _client.PostAsync($"/Tasks/Delete/{task.Id}", formContent);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Redirect);

            // Verify task was deleted from database
            var deletedTask = await _context.Tasks.FindAsync(task.Id);
            deletedTask.Should().BeNull();
        }

        [Fact]
        public async Task UpdateStatus_WithValidRequest_UpdatesTaskStatusInDatabase()
        {
            // Arrange
            await ClearDatabaseAsync();
            var project = await CreateTestProjectAsync("Test Project");
            var task = await CreateTestTaskAsync(project.Id, "Task to Update Status");

            var requestData = new
            {
                taskId = task.Id,
                status = (int)TaskStatus.Done
            };

            // Act
            var response = await _client.PostAsJsonAsync("/Tasks/UpdateStatus", requestData);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            // Verify task status was updated in database
            var updatedTask = await _context.Tasks.FindAsync(task.Id);
            updatedTask.Should().NotBeNull();
            updatedTask!.Status.Should().Be(TaskStatus.Done);
            updatedTask.CompletedDate.Should().NotBeNull();
        }

        [Fact]
        public async Task AssignTask_WithValidRequest_UpdatesTaskAssignmentInDatabase()
        {
            // Arrange
            await ClearDatabaseAsync();
            var project = await CreateTestProjectAsync("Test Project");
            var task = await CreateTestTaskAsync(project.Id, "Task to Assign");

            var requestData = new
            {
                taskId = task.Id,
                userId = _testUser.Id
            };

            // Act
            var response = await _client.PostAsJsonAsync("/Tasks/AssignTask", requestData);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            // Verify task assignment was updated in database
            var updatedTask = await _context.Tasks.FindAsync(task.Id);
            updatedTask.Should().NotBeNull();
            updatedTask!.AssignedToUserId.Should().Be(_testUser.Id);
        }

        [Fact]
        public async Task GetTasksByStatus_WithValidStatus_ReturnsFilteredTasksAsJson()
        {
            // Arrange
            await ClearDatabaseAsync();
            var project = await CreateTestProjectAsync("Test Project");
            var todoTask = await CreateTestTaskAsync(project.Id, "Todo Task");
            var inProgressTask = await CreateTestTaskAsync(project.Id, "In Progress Task");

            // Update one task to InProgress
            inProgressTask.Status = TaskStatus.InProgress;
            _context.Tasks.Update(inProgressTask);
            await _context.SaveChangesAsync();

            // Act
            var response = await _client.GetAsync($"/Tasks/GetTasksByStatus?status={(int)TaskStatus.InProgress}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            response.Content.Headers.ContentType?.MediaType.Should().Be("application/json");

            var jsonContent = await response.Content.ReadAsStringAsync();
            var tasks = JsonConvert.DeserializeObject<List<dynamic>>(jsonContent);
            tasks.Should().HaveCount(1);
        }

        [Fact]
        public async Task CompleteTaskWorkflow_CreateEditUpdateDelete_WorksEndToEnd()
        {
            // Arrange
            await ClearDatabaseAsync();
            var project = await CreateTestProjectAsync("Workflow Test Project");

            // 1. Create Task
            var createFormData = new Dictionary<string, string>
            {
                ["Title"] = "Workflow Test Task",
                ["Description"] = "End-to-end workflow test task",
                ["ProjectId"] = project.Id.ToString(),
                ["Status"] = ((int)TaskStatus.ToDo).ToString(),
                ["Priority"] = ((int)TaskPriority.Medium).ToString(),
                ["DueDate"] = DateTime.Today.AddDays(5).ToString("yyyy-MM-dd")
            };

            var createResponse = await _client.PostAsync("/Tasks/Create",
                new FormUrlEncodedContent(createFormData));
            createResponse.StatusCode.Should().Be(HttpStatusCode.Redirect);

            // Verify task was created
            var createdTask = await _context.Tasks
                .FirstOrDefaultAsync(t => t.Title == "Workflow Test Task");
            createdTask.Should().NotBeNull();

            // 2. Update Task Status to In Progress
            var statusUpdateData = new
            {
                taskId = createdTask!.Id,
                status = (int)TaskStatus.InProgress
            };

            var statusResponse = await _client.PostAsJsonAsync("/Tasks/UpdateStatus", statusUpdateData);
            statusResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            // 3. Edit Task Details
            var editFormData = new Dictionary<string, string>
            {
                ["Id"] = createdTask.Id.ToString(),
                ["Title"] = "Updated Workflow Test Task",
                ["Description"] = "Updated description for workflow test",
                ["ProjectId"] = project.Id.ToString(),
                ["Status"] = ((int)TaskStatus.InProgress).ToString(),
                ["Priority"] = ((int)TaskPriority.High).ToString()
            };

            var editResponse = await _client.PostAsync($"/Tasks/Edit/{createdTask.Id}",
                new FormUrlEncodedContent(editFormData));
            editResponse.StatusCode.Should().Be(HttpStatusCode.Redirect);

            // 4. Complete Task
            var completeData = new
            {
                taskId = createdTask.Id,
                status = (int)TaskStatus.Done
            };

            var completeResponse = await _client.PostAsJsonAsync("/Tasks/UpdateStatus", completeData);
            completeResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            // 5. Verify Final State
            var finalTask = await _context.Tasks.FindAsync(createdTask.Id);
            finalTask.Should().NotBeNull();
            finalTask!.Title.Should().Be("Updated Workflow Test Task");
            finalTask.Description.Should().Be("Updated description for workflow test");
            finalTask.Status.Should().Be(TaskStatus.Done);
            finalTask.Priority.Should().Be(TaskPriority.High);
            finalTask.CompletedDate.Should().NotBeNull();

            // 6. Delete Task
            var deleteResponse = await _client.PostAsync($"/Tasks/Delete/{createdTask.Id}",
                new FormUrlEncodedContent(new Dictionary<string, string>()));
            deleteResponse.StatusCode.Should().Be(HttpStatusCode.Redirect);

            // Verify task was deleted
            var deletedTask = await _context.Tasks.FindAsync(createdTask.Id);
            deletedTask.Should().BeNull();
        }
    }
}
