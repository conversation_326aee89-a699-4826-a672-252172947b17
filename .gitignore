# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Node artifact files
node_modules/
dist/

# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

# Package files
*.jar

# Maven
target/
dist/

# JetBrains IDE
.idea/

# Visual Studio
.vs/
*.user
*.suo
*.userosscache
*.sln.docstates
*.userprefs

# Visual Studio Code
.vscode/

# .NET
bin/
obj/
*.dll
*.pdb
*.cache
*.tmp
project.lock.json
project.fragment.lock.json
artifacts/

# ASP.NET Scaffolding
ScaffoldingReadMe.txt

# NuGet
*.nupkg
**/packages/*
!**/packages/build/
*.nuget.props
*.nuget.targets

# Entity Framework
*.edmx.diagram
*.edmx.sql

# Rider
.idea/
*.sln.iml

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

