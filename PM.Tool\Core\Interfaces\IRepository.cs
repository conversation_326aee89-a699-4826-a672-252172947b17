using System.Linq.Expressions;
using PM.Tool.Core.Common;
using PM.Tool.Core.Specifications;

namespace PM.Tool.Core.Interfaces
{
    public interface IRepository<T> where T : class
    {
        Task<T?> GetByIdAsync(int id);
        Task<T?> GetByIdAsync(string id);
        Task<IEnumerable<T>> GetAllAsync();
        Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate);
        Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate);
        Task<bool> AnyAsync(Expression<Func<T, bool>> predicate);
        Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null);
        Task<T> AddAsync(T entity);
        Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities);
        Task UpdateAsync(T entity);
        Task UpdateRangeAsync(IEnumerable<T> entities);
        Task DeleteAsync(T entity);
        Task DeleteRangeAsync(IEnumerable<T> entities);
        Task<bool> SaveChangesAsync();

        // New methods for pagination and specification pattern
        Task<PaginatedList<T>> GetPagedAsync(PaginationParams pagination);
        Task<IEnumerable<T>> FindWithSpecificationAsync(ISpecification<T> spec);
        Task<PaginatedList<T>> GetPagedWithSpecificationAsync(ISpecification<T> spec, PaginationParams pagination);
        
        // New methods for soft delete
        Task<bool> SoftDeleteAsync(T entity);
        Task<bool> RestoreAsync(T entity);
        Task<IEnumerable<T>> GetAllIncludingDeletedAsync();
    }
}
