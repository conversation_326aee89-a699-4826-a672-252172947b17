using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;

namespace PM.Tool.Application.Services
{
    public class RiskService : IRiskService
    {
        private readonly ApplicationDbContext _context;

        public RiskService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Risk>> GetAllRisksAsync()
        {
            return await _context.Risks
                .Include(r => r.Project)
                .Include(r => r.Task)
                .Include(r => r.Owner)
                .Include(r => r.CreatedBy)
                .Include(r => r.MitigationActions)
                .Where(r => !r.IsDeleted)
                .OrderByDescending(r => (int)r.Probability * (int)r.Impact)
                .ToListAsync();
        }

        public async Task<IEnumerable<Risk>> GetProjectRisksAsync(int projectId)
        {
            return await _context.Risks
                .Include(r => r.Task)
                .Include(r => r.Owner)
                .Include(r => r.CreatedBy)
                .Include(r => r.MitigationActions)
                .Where(r => r.ProjectId == projectId && !r.IsDeleted)
                .OrderByDescending(r => (int)r.Probability * (int)r.Impact)
                .ToListAsync();
        }

        public async Task<IEnumerable<Risk>> GetTaskRisksAsync(int taskId)
        {
            return await _context.Risks
                .Include(r => r.Project)
                .Include(r => r.Owner)
                .Include(r => r.CreatedBy)
                .Include(r => r.MitigationActions)
                .Where(r => r.TaskId == taskId && !r.IsDeleted)
                .OrderByDescending(r => (int)r.Probability * (int)r.Impact)
                .ToListAsync();
        }

        public async Task<Risk?> GetRiskByIdAsync(int id)
        {
            return await _context.Risks
                .Include(r => r.Project)
                .Include(r => r.Task)
                .Include(r => r.Owner)
                .Include(r => r.CreatedBy)
                .Include(r => r.MitigationActions)
                    .ThenInclude(ma => ma.AssignedTo)
                .FirstOrDefaultAsync(r => r.Id == id && !r.IsDeleted);
        }

        public async Task<Risk> CreateRiskAsync(Risk risk)
        {
            risk.IdentifiedDate = DateTime.UtcNow;
            risk.CreatedAt = DateTime.UtcNow;
            risk.UpdatedAt = DateTime.UtcNow;

            _context.Risks.Add(risk);
            await _context.SaveChangesAsync();

            return risk;
        }

        public async Task<Risk> UpdateRiskAsync(Risk risk)
        {
            risk.UpdatedAt = DateTime.UtcNow;

            _context.Risks.Update(risk);
            await _context.SaveChangesAsync();

            return risk;
        }

        public async Task<bool> DeleteRiskAsync(int id)
        {
            var risk = await _context.Risks.FindAsync(id);
            if (risk == null) return false;

            risk.IsDeleted = true;
            risk.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<Risk>> GetRisksByLevelAsync(RiskLevel level)
        {
            // Since RiskLevel is also computed, we need to use AsEnumerable for this query
            var risks = await _context.Risks
                .Include(r => r.Project)
                .Include(r => r.Task)
                .Include(r => r.Owner)
                .Where(r => !r.IsDeleted)
                .ToListAsync();

            return risks
                .Where(r => r.RiskLevel == level)
                .OrderByDescending(r => r.RiskScore);
        }

        public async Task<IEnumerable<Risk>> GetOverdueRisksAsync()
        {
            // Since IsOverdue is computed, we need to use AsEnumerable for this query
            var risks = await _context.Risks
                .Include(r => r.Project)
                .Include(r => r.Task)
                .Include(r => r.Owner)
                .Where(r => !r.IsDeleted)
                .ToListAsync();

            return risks
                .Where(r => r.IsOverdue)
                .OrderByDescending(r => r.RiskScore);
        }

        public async Task<IEnumerable<Risk>> GetRisksByStatusAsync(RiskStatus status)
        {
            return await _context.Risks
                .Include(r => r.Project)
                .Include(r => r.Task)
                .Include(r => r.Owner)
                .Where(r => !r.IsDeleted && r.Status == status)
                .OrderByDescending(r => (int)r.Probability * (int)r.Impact)
                .ToListAsync();
        }

        public async Task<IEnumerable<Risk>> GetRisksByCategoryAsync(RiskCategory category)
        {
            return await _context.Risks
                .Include(r => r.Project)
                .Include(r => r.Task)
                .Include(r => r.Owner)
                .Where(r => !r.IsDeleted && r.Category == category)
                .OrderByDescending(r => (int)r.Probability * (int)r.Impact)
                .ToListAsync();
        }

        public async Task<IEnumerable<RiskMitigationAction>> GetRiskMitigationActionsAsync(int riskId)
        {
            return await _context.RiskMitigationActions
                .Include(ma => ma.AssignedTo)
                .Where(ma => ma.RiskId == riskId && !ma.IsDeleted)
                .OrderBy(ma => ma.DueDate)
                .ToListAsync();
        }

        public async Task<RiskMitigationAction> CreateMitigationActionAsync(RiskMitigationAction action)
        {
            action.CreatedAt = DateTime.UtcNow;
            action.UpdatedAt = DateTime.UtcNow;

            _context.RiskMitigationActions.Add(action);
            await _context.SaveChangesAsync();

            return action;
        }

        public async Task<RiskMitigationAction> UpdateMitigationActionAsync(RiskMitigationAction action)
        {
            action.UpdatedAt = DateTime.UtcNow;

            _context.RiskMitigationActions.Update(action);
            await _context.SaveChangesAsync();

            return action;
        }

        public async Task<bool> DeleteMitigationActionAsync(int id)
        {
            var action = await _context.RiskMitigationActions.FindAsync(id);
            if (action == null) return false;

            action.IsDeleted = true;
            action.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> CompleteMitigationActionAsync(int id)
        {
            var action = await _context.RiskMitigationActions.FindAsync(id);
            if (action == null) return false;

            action.Status = ActionStatus.Completed;
            action.CompletedDate = DateTime.UtcNow;
            action.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<Dictionary<RiskCategory, int>> GetRiskDistributionByCategoryAsync(int projectId)
        {
            var risks = await _context.Risks
                .Where(r => r.ProjectId == projectId && !r.IsDeleted)
                .GroupBy(r => r.Category)
                .Select(g => new { Category = g.Key, Count = g.Count() })
                .ToListAsync();

            return risks.ToDictionary(r => r.Category, r => r.Count);
        }

        public async Task<Dictionary<RiskLevel, int>> GetRiskDistributionByLevelAsync(int projectId)
        {
            var risks = await _context.Risks
                .Where(r => r.ProjectId == projectId && !r.IsDeleted)
                .ToListAsync();

            return risks
                .GroupBy(r => r.RiskLevel)
                .ToDictionary(g => g.Key, g => g.Count());
        }

        public async Task<decimal> GetProjectRiskScoreAsync(int projectId)
        {
            var risks = await _context.Risks
                .Where(r => r.ProjectId == projectId && !r.IsDeleted && r.Status != RiskStatus.Resolved)
                .ToListAsync();

            if (!risks.Any()) return 0;

            // Calculate weighted risk score
            var totalScore = risks.Sum(r => r.RiskScore * (r.EstimatedCost > 0 ? (decimal)r.RiskScore : 1));
            var totalWeight = risks.Sum(r => r.EstimatedCost > 0 ? (decimal)r.RiskScore : 1);

            return totalWeight > 0 ? totalScore / totalWeight : 0;
        }

        public async Task<IEnumerable<Risk>> GetTopRisksByScoreAsync(int projectId, int count = 10)
        {
            return await _context.Risks
                .Include(r => r.Owner)
                .Include(r => r.Task)
                .Where(r => r.ProjectId == projectId && !r.IsDeleted && r.Status != RiskStatus.Resolved)
                .OrderByDescending(r => (int)r.Probability * (int)r.Impact)
                .Take(count)
                .ToListAsync();
        }

        public async Task<IEnumerable<Risk>> GetRisksRequiringAttentionAsync(int projectId)
        {
            // Since this uses computed properties, we need to use AsEnumerable
            var risks = await _context.Risks
                .Include(r => r.Owner)
                .Include(r => r.Task)
                .Include(r => r.MitigationActions)
                .Where(r => r.ProjectId == projectId && !r.IsDeleted)
                .ToListAsync();

            return risks
                .Where(r => r.RiskLevel == RiskLevel.Critical ||
                           r.RiskLevel == RiskLevel.High ||
                           r.IsOverdue ||
                           r.MitigationActions.Any(ma => ma.IsOverdue))
                .OrderByDescending(r => r.RiskScore);
        }

        public async Task<bool> UpdateRiskStatusAsync(int riskId, RiskStatus status)
        {
            var risk = await _context.Risks.FindAsync(riskId);
            if (risk == null) return false;

            risk.Status = status;
            risk.UpdatedAt = DateTime.UtcNow;

            if (status == RiskStatus.Resolved)
            {
                risk.ActualResolutionDate = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<Risk>> GetRisksDueForReviewAsync(DateTime reviewDate)
        {
            return await _context.Risks
                .Include(r => r.Project)
                .Include(r => r.Owner)
                .Where(r => !r.IsDeleted &&
                           r.Status != RiskStatus.Resolved &&
                           r.TargetResolutionDate.HasValue &&
                           r.TargetResolutionDate.Value <= reviewDate)
                .OrderBy(r => r.TargetResolutionDate)
                .ToListAsync();
        }


    }
}
