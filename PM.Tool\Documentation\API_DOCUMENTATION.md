# PM Tool - API Documentation

## 🌐 API Overview

PM Tool provides a comprehensive RESTful API for programmatic access to all project management features. The API follows REST conventions, uses JSON for data exchange, and implements OAuth 2.0 for authentication.

## 🔐 Authentication

### OAuth 2.0 Authentication
```http
POST /api/auth/token
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "password",
  "grant_type": "password"
}
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "refresh_token": "def50200..."
}
```

### Using the Access Token
```http
GET /api/projects
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Refresh Token
```http
POST /api/auth/refresh
Content-Type: application/json

{
  "refresh_token": "def50200...",
  "grant_type": "refresh_token"
}
```

## 📊 Projects API

### Get All Projects
```http
GET /api/projects
Authorization: Bearer {token}
```

**Query Parameters:**
- `page` (int): Page number (default: 1)
- `pageSize` (int): Items per page (default: 20, max: 100)
- `status` (string): Filter by status (Planning, Active, OnHold, Completed, Cancelled)
- `search` (string): Search in project name and description
- `sortBy` (string): Sort field (name, startDate, endDate, status)
- `sortOrder` (string): Sort direction (asc, desc)

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "name": "Website Redesign",
      "description": "Complete redesign of company website",
      "status": "Active",
      "priority": "High",
      "startDate": "2024-01-15T00:00:00Z",
      "endDate": "2024-06-30T00:00:00Z",
      "budget": 50000.00,
      "actualCost": 25000.00,
      "progressPercentage": 45.5,
      "projectManagerId": "user123",
      "projectManager": {
        "id": "user123",
        "name": "John Smith",
        "email": "<EMAIL>"
      },
      "teamMembers": [
        {
          "id": "user456",
          "name": "Jane Doe",
          "role": "Developer"
        }
      ],
      "createdAt": "2024-01-10T10:30:00Z",
      "updatedAt": "2024-03-15T14:20:00Z"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "pageSize": 20,
    "totalItems": 45,
    "totalPages": 3,
    "hasNextPage": true,
    "hasPreviousPage": false
  }
}
```

### Get Project by ID
```http
GET /api/projects/{id}
Authorization: Bearer {token}
```

**Response:**
```json
{
  "id": 1,
  "name": "Website Redesign",
  "description": "Complete redesign of company website",
  "status": "Active",
  "priority": "High",
  "startDate": "2024-01-15T00:00:00Z",
  "endDate": "2024-06-30T00:00:00Z",
  "budget": 50000.00,
  "actualCost": 25000.00,
  "progressPercentage": 45.5,
  "projectManagerId": "user123",
  "tasks": [
    {
      "id": 101,
      "title": "Design Homepage",
      "status": "InProgress",
      "assignedToId": "user456",
      "dueDate": "2024-02-15T00:00:00Z"
    }
  ],
  "milestones": [
    {
      "id": 201,
      "name": "Design Phase Complete",
      "dueDate": "2024-03-01T00:00:00Z",
      "status": "Completed"
    }
  ]
}
```

### Create Project
```http
POST /api/projects
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "Mobile App Development",
  "description": "Develop iOS and Android mobile application",
  "startDate": "2024-04-01T00:00:00Z",
  "endDate": "2024-12-31T00:00:00Z",
  "budget": 100000.00,
  "priority": "High",
  "projectManagerId": "user123",
  "teamMemberIds": ["user456", "user789"]
}
```

**Response:** `201 Created`
```json
{
  "id": 2,
  "name": "Mobile App Development",
  "status": "Planning",
  "createdAt": "2024-03-20T10:00:00Z"
}
```

### Update Project
```http
PUT /api/projects/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "Mobile App Development - Updated",
  "description": "Updated description",
  "status": "Active",
  "budget": 120000.00
}
```

### Delete Project
```http
DELETE /api/projects/{id}
Authorization: Bearer {token}
```

**Response:** `204 No Content`

## ✅ Tasks API

### Get Project Tasks
```http
GET /api/projects/{projectId}/tasks
Authorization: Bearer {token}
```

**Query Parameters:**
- `status` (string): Filter by status
- `assignedTo` (string): Filter by assignee ID
- `priority` (string): Filter by priority
- `dueDate` (string): Filter by due date range

### Create Task
```http
POST /api/projects/{projectId}/tasks
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "Implement User Authentication",
  "description": "Implement OAuth 2.0 authentication system",
  "priority": "High",
  "estimatedHours": 16,
  "dueDate": "2024-04-15T00:00:00Z",
  "assignedToId": "user456",
  "parentTaskId": null,
  "tags": ["backend", "security"]
}
```

### Update Task Status
```http
PATCH /api/tasks/{id}/status
Authorization: Bearer {token}
Content-Type: application/json

{
  "status": "InProgress",
  "comment": "Started working on authentication module"
}
```

### Log Time
```http
POST /api/tasks/{id}/time-entries
Authorization: Bearer {token}
Content-Type: application/json

{
  "hours": 4.5,
  "description": "Implemented OAuth integration",
  "date": "2024-03-20T00:00:00Z"
}
```

## 🏃‍♂️ Agile API

### Get Kanban Board
```http
GET /api/projects/{projectId}/kanban
Authorization: Bearer {token}
```

**Response:**
```json
{
  "columns": [
    {
      "id": 1,
      "name": "Backlog",
      "userStories": [
        {
          "id": 301,
          "title": "User Registration",
          "storyPoints": 5,
          "priority": "High",
          "assignedTo": {
            "id": "user456",
            "name": "Jane Doe"
          },
          "tags": ["authentication", "frontend"]
        }
      ]
    }
  ]
}
```

### Move User Story
```http
POST /api/agile/user-stories/{id}/move
Authorization: Bearer {token}
Content-Type: application/json

{
  "columnId": 3,
  "position": 2
}
```

### Create User Story
```http
POST /api/projects/{projectId}/user-stories
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "User Profile Management",
  "asA": "registered user",
  "iWant": "to manage my profile information",
  "soThat": "I can keep my account details up to date",
  "acceptanceCriteria": "- User can edit profile\n- Changes are saved\n- Validation errors shown",
  "storyPoints": 8,
  "priority": "Medium",
  "epicId": 401
}
```

### Sprint Management
```http
# Create Sprint
POST /api/projects/{projectId}/sprints
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "Sprint 1",
  "goal": "Implement user authentication and basic profile management",
  "startDate": "2024-04-01T00:00:00Z",
  "endDate": "2024-04-14T00:00:00Z",
  "plannedStoryPoints": 40
}

# Start Sprint
POST /api/sprints/{id}/start
Authorization: Bearer {token}

# Complete Sprint
POST /api/sprints/{id}/complete
Authorization: Bearer {token}
Content-Type: application/json

{
  "retrospectiveNotes": "Good velocity, need better estimation"
}
```

## 👥 Team & Resources API

### Get Team Members
```http
GET /api/projects/{projectId}/team
Authorization: Bearer {token}
```

### Assign User to Project
```http
POST /api/projects/{projectId}/team
Authorization: Bearer {token}
Content-Type: application/json

{
  "userId": "user789",
  "role": "Developer",
  "startDate": "2024-04-01T00:00:00Z"
}
```

### Get Resource Utilization
```http
GET /api/resources/utilization
Authorization: Bearer {token}
```

**Query Parameters:**
- `startDate` (string): Start date for utilization report
- `endDate` (string): End date for utilization report
- `userId` (string): Specific user ID (optional)

## 📊 Analytics & Reporting API

### Project Analytics
```http
GET /api/projects/{projectId}/analytics
Authorization: Bearer {token}
```

**Response:**
```json
{
  "overview": {
    "totalTasks": 45,
    "completedTasks": 23,
    "overdueTasks": 3,
    "progressPercentage": 51.1
  },
  "burndown": [
    {
      "date": "2024-03-01T00:00:00Z",
      "remainingWork": 100,
      "idealRemaining": 95
    }
  ],
  "velocity": [
    {
      "sprint": "Sprint 1",
      "plannedPoints": 40,
      "completedPoints": 38
    }
  ]
}
```

### Generate Report
```http
POST /api/reports/generate
Authorization: Bearer {token}
Content-Type: application/json

{
  "type": "ProjectStatus",
  "projectId": 1,
  "format": "PDF",
  "parameters": {
    "includeCharts": true,
    "dateRange": {
      "start": "2024-01-01T00:00:00Z",
      "end": "2024-03-31T00:00:00Z"
    }
  }
}
```

**Response:**
```json
{
  "reportId": "rpt_123456",
  "status": "Generating",
  "estimatedCompletionTime": "2024-03-20T10:05:00Z"
}
```

### Download Report
```http
GET /api/reports/{reportId}/download
Authorization: Bearer {token}
```

## 🔔 Notifications API

### Get Notifications
```http
GET /api/notifications
Authorization: Bearer {token}
```

**Query Parameters:**
- `unreadOnly` (bool): Show only unread notifications
- `type` (string): Filter by notification type

### Mark as Read
```http
POST /api/notifications/{id}/read
Authorization: Bearer {token}
```

### Notification Preferences
```http
GET /api/users/me/notification-preferences
Authorization: Bearer {token}

PUT /api/users/me/notification-preferences
Authorization: Bearer {token}
Content-Type: application/json

{
  "emailNotifications": true,
  "taskAssignments": true,
  "projectUpdates": false,
  "meetingReminders": true
}
```

## 🌍 Localization API

### Get Supported Languages
```http
GET /api/localization/languages
```

**Response:**
```json
{
  "languages": [
    {
      "code": "en-US",
      "name": "English (United States)",
      "nativeName": "English",
      "isRtl": false
    },
    {
      "code": "ar-SA",
      "name": "Arabic (Saudi Arabia)",
      "nativeName": "العربية",
      "isRtl": true
    }
  ]
}
```

### Get Translations
```http
GET /api/localization/translations/{languageCode}
Authorization: Bearer {token}
```

## 📁 File Management API

### Upload File
```http
POST /api/files/upload
Authorization: Bearer {token}
Content-Type: multipart/form-data

{
  "file": [binary data],
  "projectId": 1,
  "taskId": 101,
  "description": "Design mockups"
}
```

**Response:**
```json
{
  "id": "file_123",
  "fileName": "mockup.pdf",
  "fileSize": 2048576,
  "contentType": "application/pdf",
  "uploadedAt": "2024-03-20T10:00:00Z",
  "downloadUrl": "/api/files/file_123/download"
}
```

### Download File
```http
GET /api/files/{fileId}/download
Authorization: Bearer {token}
```

## 🚨 Error Handling

### Error Response Format
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "One or more validation errors occurred",
    "details": [
      {
        "field": "name",
        "message": "Project name is required"
      }
    ],
    "traceId": "trace_123456"
  }
}
```

### HTTP Status Codes
- `200 OK`: Successful GET, PUT, PATCH requests
- `201 Created`: Successful POST requests
- `204 No Content`: Successful DELETE requests
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `409 Conflict`: Resource conflict
- `422 Unprocessable Entity`: Validation errors
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

## 🔄 Rate Limiting

### Rate Limits
- **Authenticated requests**: 1000 requests per hour
- **File uploads**: 100 requests per hour
- **Report generation**: 10 requests per hour

### Rate Limit Headers
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1616161616
```

## 📡 Webhooks

### Configure Webhook
```http
POST /api/webhooks
Authorization: Bearer {token}
Content-Type: application/json

{
  "url": "https://your-app.com/webhook",
  "events": ["task.created", "task.updated", "project.completed"],
  "secret": "your-webhook-secret"
}
```

### Webhook Events
- `project.created`
- `project.updated`
- `project.completed`
- `task.created`
- `task.updated`
- `task.completed`
- `user_story.moved`
- `sprint.started`
- `sprint.completed`

### Webhook Payload Example
```json
{
  "event": "task.created",
  "timestamp": "2024-03-20T10:00:00Z",
  "data": {
    "id": 101,
    "title": "New Task",
    "projectId": 1,
    "assignedToId": "user456"
  }
}
```

## 🧪 Testing the API

### Using cURL
```bash
# Get access token
curl -X POST "https://api.pmtool.com/api/auth/token" \
  -H "Content-Type: application/json" \
  -d '{"username":"<EMAIL>","password":"password","grant_type":"password"}'

# Use the API
curl -X GET "https://api.pmtool.com/api/projects" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### Using Postman
1. Import the provided Postman collection
2. Set up environment variables for base URL and tokens
3. Use the authentication request to get access token
4. Test various endpoints with different scenarios

### SDK Examples

#### JavaScript/Node.js
```javascript
const PMToolAPI = require('pmtool-api-client');

const client = new PMToolAPI({
  baseURL: 'https://api.pmtool.com',
  clientId: 'your-client-id',
  clientSecret: 'your-client-secret'
});

// Get projects
const projects = await client.projects.getAll();

// Create task
const task = await client.tasks.create({
  projectId: 1,
  title: 'New Task',
  assignedToId: 'user456'
});
```

#### Python
```python
from pmtool_client import PMToolClient

client = PMToolClient(
    base_url='https://api.pmtool.com',
    client_id='your-client-id',
    client_secret='your-client-secret'
)

# Get projects
projects = client.projects.get_all()

# Create task
task = client.tasks.create(
    project_id=1,
    title='New Task',
    assigned_to_id='user456'
)
```

---

*This API documentation provides comprehensive information for integrating with PM Tool. For additional examples and advanced usage, please refer to the SDK documentation and code samples.*
