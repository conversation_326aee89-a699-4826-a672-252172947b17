using PM.Tool.Core.Enums;
using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities
{
    public class ProjectMember
    {
        public int Id { get; set; }

        public int ProjectId { get; set; }

        public string UserId { get; set; } = string.Empty;

        public UserRole Role { get; set; } = UserRole.TeamMember;

        public DateTime JoinedAt { get; set; } = DateTime.UtcNow;

        public bool IsActive { get; set; } = true;

        public bool IsProjectManager => Role == UserRole.ProjectManager;

        // Navigation properties
        public virtual Project Project { get; set; } = null!;
        public virtual ApplicationUser User { get; set; } = null!;
    }
}
