@using Microsoft.AspNetCore.Identity
@using PM.Tool.Core.Entities
@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager

<!-- Sidebar -->
<aside id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-surface-dark border-r border-neutral-200 dark:border-dark-200 transform -translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out flex flex-col">
    <!-- Sidebar Header -->
    <div class="flex items-center justify-between h-16 px-6 border-b border-neutral-200 dark:border-dark-200 flex-shrink-0">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <i class="fas fa-project-diagram text-white text-sm"></i>
            </div>
            <span class="text-xl font-bold text-neutral-900 dark:text-white">PM Tool</span>
        </div>
        <button id="sidebar-close" class="lg:hidden p-2 rounded-lg hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors">
            <i class="fas fa-times text-neutral-500 dark:text-neutral-400"></i>
        </button>
    </div>

    <!-- Navigation - Scrollable -->
    <nav class="flex-1 px-4 py-4 overflow-y-auto scrollbar-thin scrollbar-thumb-neutral-300 dark:scrollbar-thumb-neutral-600 scrollbar-track-transparent hover:scrollbar-thumb-neutral-400 dark:hover:scrollbar-thumb-neutral-500">
        <div class="space-y-1">
            <!-- Dashboard -->
            <a href="@Url.Action("Index", "Home")" class="sidebar-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Home" ? "sidebar-link-active" : "")">
                <i class="fas fa-tachometer-alt w-5 h-5 mr-3 text-current"></i>
                <span class="font-medium text-neutral-700 dark:text-white">@Localizer["Nav.Dashboard"]</span>
            </a>
        </div>

        <!-- Core Features -->
        <div class="mt-6">
            <h3 class="px-3 text-xs font-semibold text-neutral-500 dark:text-neutral-400 uppercase tracking-wider mb-3">@Localizer["Nav.Management"]</h3>
            <div class="space-y-1">
                <a href="@Url.Action("Index", "Projects")" class="sidebar-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Projects" ? "sidebar-link-active" : "")">
                    <i class="fas fa-folder-open w-5 h-5 mr-3 text-current"></i>
                    <span class="font-medium text-neutral-700 dark:text-white">@Localizer["Nav.Projects"]</span>
                </a>

                <a href="@Url.Action("Index", "Tasks")" class="sidebar-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Tasks" && ViewContext.RouteData.Values["Action"]?.ToString() == "Index" ? "sidebar-link-active" : "")">
                    <i class="fas fa-tasks w-5 h-5 mr-3 text-current"></i>
                    <span class="font-medium text-neutral-700 dark:text-white">@Localizer["Nav.Tasks"]</span>
                </a>

                <a href="@Url.Action("MyTasks", "Tasks")" class="sidebar-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Tasks" && ViewContext.RouteData.Values["Action"]?.ToString() == "MyTasks" ? "sidebar-link-active" : "")">
                    <i class="fas fa-user-check w-5 h-5 mr-3 text-current"></i>
                    <span class="font-medium text-neutral-700 dark:text-white">My Tasks</span>
                </a>

                <a href="@Url.Action("Index", "Agile")" class="sidebar-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Agile" ? "sidebar-link-active" : "")">
                    <i class="fas fa-columns w-5 h-5 mr-3 text-current"></i>
                    <span class="font-medium text-neutral-700 dark:text-white">@Localizer["Nav.Kanban"]</span>
                </a>
            </div>
        </div>

        <!-- Analytics & Reports -->
        @if (SignInManager.IsSignedIn(User) && (User.IsInRole("Admin") || User.IsInRole("Manager")))
        {
            <div class="mt-6">
                <h3 class="px-3 text-xs font-semibold text-neutral-500 dark:text-neutral-400 uppercase tracking-wider mb-3">@Localizer["Nav.Analytics"]</h3>
                <div class="space-y-1">
                    <a href="@Url.Action("Index", "Analytics")" class="sidebar-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Analytics" ? "sidebar-link-active" : "")">
                        <i class="fas fa-chart-line w-5 h-5 mr-3 text-current"></i>
                        <span class="font-medium text-neutral-700 dark:text-white">@Localizer["Nav.AdvancedReports"]</span>
                    </a>

                    <a href="@Url.Action("Create", "Projects")" class="sidebar-link">
                        <i class="fas fa-plus w-5 h-5 mr-3 text-current"></i>
                        <span class="font-medium text-neutral-700 dark:text-white">@Localizer["Common.Create"] @Localizer["Nav.Projects"]</span>
                    </a>
                </div>
            </div>
        }

        <!-- Collaboration -->
        <div class="mt-6">
            <h3 class="px-3 text-xs font-semibold text-neutral-500 dark:text-neutral-400 uppercase tracking-wider mb-3">@Localizer["Nav.Meetings"]</h3>
            <div class="space-y-1">
                <a href="@Url.Action("Index", "Meeting")" class="sidebar-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Meeting" ? "sidebar-link-active" : "")">
                    <i class="fas fa-video w-5 h-5 mr-3 text-current"></i>
                    <span class="font-medium text-neutral-700 dark:text-white">@Localizer["Nav.Meetings"]</span>
                </a>

                <a href="@Url.Action("Index", "Documentation")" class="sidebar-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Documentation" ? "sidebar-link-active" : "")">
                    <i class="fas fa-book w-5 h-5 mr-3 text-current"></i>
                    <span class="font-medium text-neutral-700 dark:text-white">@Localizer["Nav.Documentation"]</span>
                </a>
            </div>
        </div>

        <!-- Advanced Features -->
        @if (SignInManager.IsSignedIn(User) && (User.IsInRole("Admin") || User.IsInRole("Manager")))
        {
            <div class="mt-6">
                <h3 class="px-3 text-xs font-semibold text-neutral-500 dark:text-neutral-400 uppercase tracking-wider mb-3">@Localizer["Nav.Management"]</h3>
                <div class="space-y-1">
                    <a href="@Url.Action("Index", "Resource")" class="sidebar-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Resource" ? "sidebar-link-active" : "")">
                        <i class="fas fa-users w-5 h-5 mr-3 text-current"></i>
                        <span class="font-medium text-neutral-700 dark:text-white">@Localizer["Nav.Resources"]</span>
                    </a>

                    <a href="@Url.Action("Index", "TimeTracking")" class="sidebar-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "TimeTracking" ? "sidebar-link-active" : "")">
                        <i class="fas fa-clock w-5 h-5 mr-3 text-current"></i>
                        <span class="font-medium text-neutral-700 dark:text-white">Time Tracking</span>
                    </a>

                    <a href="@Url.Action("Index", "Wbs")" class="sidebar-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Wbs" ? "sidebar-link-active" : "")">
                        <i class="fas fa-sitemap w-5 h-5 mr-3 text-current"></i>
                        <span class="font-medium text-neutral-700 dark:text-white">Work Breakdown</span>
                    </a>

                    <a href="@Url.Action("Index", "Risk")" class="sidebar-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Risk" ? "sidebar-link-active" : "")">
                        <i class="fas fa-exclamation-triangle w-5 h-5 mr-3 text-current"></i>
                        <span class="font-medium text-neutral-700 dark:text-white">@Localizer["Nav.Risks"]</span>
                    </a>

                    <a href="@Url.Action("Index", "Requirement")" class="sidebar-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Requirement" ? "sidebar-link-active" : "")">
                        <i class="fas fa-list-alt w-5 h-5 mr-3 text-current"></i>
                        <span class="font-medium text-neutral-700 dark:text-white">@Localizer["Nav.Requirements"]</span>
                    </a>

                    <a href="@Url.Action("Index", "Stakeholder")" class="sidebar-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Stakeholder" ? "sidebar-link-active" : "")">
                        <i class="fas fa-users-cog w-5 h-5 mr-3 text-current"></i>
                        <span class="font-medium text-neutral-700 dark:text-white">Stakeholders</span>
                    </a>

                    <a href="@Url.Action("Index", "Person")" class="sidebar-link @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Person" ? "sidebar-link-active" : "")">
                        <i class="fas fa-users w-5 h-5 mr-3 text-current"></i>
                        <span class="font-medium text-neutral-700 dark:text-white">People Management</span>
                    </a>
                </div>
            </div>
        }
    </nav>

    <!-- Sidebar Footer -->
    <div class="flex-shrink-0 p-4 border-t border-neutral-200 dark:border-neutral-600">
        @if (SignInManager.IsSignedIn(User))
        {
            <div class="flex items-center space-x-3 p-3 rounded-lg bg-neutral-50 dark:bg-dark-700 hover:bg-neutral-100 dark:hover:bg-dark-600 transition-colors cursor-pointer">
                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-user text-primary-600 dark:text-primary-400 text-sm"></i>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-neutral-900 dark:text-white truncate">
                        @(User.Identity?.Name ?? "Guest")
                    </p>
                    <p class="text-xs text-neutral-500 dark:text-neutral-400">
                        @if (User.IsInRole("Admin"))
                        {
                            <span>Administrator</span>
                        }
                        else if (User.IsInRole("Manager"))
                        {
                            <span>Project Manager</span>
                        }
                        else
                        {
                            <span>Team Member</span>
                        }
                    </p>
                </div>
                <div class="flex-shrink-0">
                    <i class="fas fa-chevron-up text-neutral-400 dark:text-neutral-500 text-xs"></i>
                </div>
            </div>
        }
        else
        {
            <div class="text-center">
                <a href="@Url.Action("Login", "Account", new { area = "Identity" })"
                   class="block w-full px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-lg hover:bg-primary-700 transition-colors">
                    Sign In
                </a>
            </div>
        }
    </div>
</aside>

<!-- Sidebar Overlay (Mobile) -->
<div id="sidebar-overlay" class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden hidden"></div>
