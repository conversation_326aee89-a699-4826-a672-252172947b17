@model PM.Tool.Models.ViewModels.FeatureDetailsViewModel
@using PM.Tool.Core.Entities.Agile

@{
    ViewData["Title"] = $"Feature: {Model.Feature.Title}";
    ViewData["PageTitle"] = $"{Model.Feature.FeatureKey} - {Model.Feature.Title}";
    ViewData["PageDescription"] = "Feature details, progress tracking, and related work items.";
}

@section Styles {
    <style>
        .detail-card {
            transition: all 0.2s ease-in-out;
        }
        .detail-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .progress-ring {
            transform: rotate(-90deg);
        }
        .progress-ring-circle {
            transition: stroke-dasharray 0.35s;
            transform: rotate(-90deg);
            transform-origin: 50% 50%;
        }
    </style>
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <div class="flex items-center space-x-3 mb-2">
                <span class="text-sm font-mono text-neutral-500 dark:text-dark-400 bg-neutral-100 dark:bg-dark-300 px-3 py-1 rounded">
                    @Model.Feature.FeatureKey
                </span>
                <span class="px-3 py-1 text-sm font-medium rounded-full @GetStatusBadgeClass(Model.Feature.Status)">
                    @Model.Feature.Status
                </span>
                <span class="px-3 py-1 text-sm font-medium rounded-full @GetPriorityBadgeClass(Model.Feature.Priority)">
                    @Model.Feature.Priority
                </span>
            </div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-puzzle-piece mr-3 text-primary-600 dark:text-primary-400"></i>
                @Model.Feature.Title
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Epic: @Model.Feature.Epic?.Title | Project: @Model.Feature.Project?.Name
            </p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
            @{
                ViewData["Text"] = "Edit";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-edit";
                ViewData["Href"] = Url.Action("Edit", new { id = Model.Feature.Id });
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "Back to List";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Index", new { projectId = Model.Feature.ProjectId });
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Feature Overview -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
    <!-- Main Details -->
    <div class="lg:col-span-2">
        @{
            ViewData["Title"] = "Feature Details";
            ViewData["Icon"] = "fas fa-info-circle";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="space-y-6">
                <!-- Description -->
                <div>
                    <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-100 mb-2">Description</h4>
                    <p class="text-neutral-600 dark:text-dark-300">@Model.Feature.Description</p>
                </div>

                <!-- Business Value -->
                @if (!string.IsNullOrEmpty(Model.Feature.BusinessValue))
                {
                    <div>
                        <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-100 mb-2">Business Value</h4>
                        <p class="text-neutral-600 dark:text-dark-300">@Model.Feature.BusinessValue</p>
                    </div>
                }

                <!-- Acceptance Criteria -->
                @if (!string.IsNullOrEmpty(Model.Feature.AcceptanceCriteria))
                {
                    <div>
                        <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-100 mb-2">Acceptance Criteria</h4>
                        <div class="bg-neutral-50 dark:bg-dark-300 rounded-lg p-4">
                            <pre class="text-sm text-neutral-600 dark:text-dark-300 whitespace-pre-wrap">@Model.Feature.AcceptanceCriteria</pre>
                        </div>
                    </div>
                }

                <!-- Tags -->
                @if (!string.IsNullOrEmpty(Model.Feature.Tags))
                {
                    <div>
                        <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-100 mb-2">Tags</h4>
                        <div class="flex flex-wrap gap-2">
                            @foreach (var tag in Model.Feature.Tags.Split(',', StringSplitOptions.RemoveEmptyEntries))
                            {
                                <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full">
                                    @tag.Trim()
                                </span>
                            }
                        </div>
                    </div>
                }
            </div>
        </partial>
    </div>

    <!-- Progress & Metrics -->
    <div class="space-y-6">
        <!-- Progress Card -->
        @{
            ViewData["Title"] = "Progress";
            ViewData["Icon"] = "fas fa-chart-pie";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="text-center">
                <!-- Progress Circle -->
                <div class="relative inline-flex items-center justify-center w-32 h-32 mb-4">
                    <svg class="w-32 h-32 progress-ring">
                        <circle cx="64" cy="64" r="56" stroke="currentColor" stroke-width="8" fill="transparent"
                                class="text-neutral-200 dark:text-dark-300"/>
                        <circle cx="64" cy="64" r="56" stroke="currentColor" stroke-width="8" fill="transparent"
                                stroke-dasharray="@(2 * Math.PI * 56)"
                                stroke-dashoffset="@(2 * Math.PI * 56 * (1 - (double)Model.Feature.ProgressPercentage / 100))"
                                class="text-primary-600 dark:text-primary-400 progress-ring-circle"/>
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center">
                        <span class="text-2xl font-bold text-neutral-900 dark:text-dark-100">
                            @Model.Feature.ProgressPercentage.ToString("F0")%
                        </span>
                    </div>
                </div>

                <!-- Story Points -->
                <div class="space-y-2">
                    <div class="flex justify-between text-sm">
                        <span class="text-neutral-600 dark:text-dark-300">Story Points</span>
                        <span class="font-medium">@Model.Feature.ActualStoryPoints / @Model.Feature.EstimatedStoryPoints</span>
                    </div>
                    <div class="w-full bg-neutral-200 dark:bg-dark-300 rounded-full h-2">
                        <div class="bg-green-500 h-2 rounded-full"
                             style="width: @(Model.Feature.EstimatedStoryPoints > 0 ? (double)Model.Feature.ActualStoryPoints / (double)Model.Feature.EstimatedStoryPoints * 100 : 0)%"></div>
                    </div>
                </div>
            </div>
        </partial>

        <!-- Key Metrics -->
        @{
            ViewData["Title"] = "Metrics";
            ViewData["Icon"] = "fas fa-chart-bar";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-neutral-600 dark:text-dark-300">User Stories</span>
                    <span class="font-medium">@Model.Metrics.CompletedUserStories / @Model.Metrics.TotalUserStories</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-neutral-600 dark:text-dark-300">Open Bugs</span>
                    <span class="font-medium text-red-600 dark:text-red-400">@Model.Metrics.OpenBugs</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-neutral-600 dark:text-dark-300">Test Cases</span>
                    <span class="font-medium">@Model.Metrics.TotalTestCases</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-neutral-600 dark:text-dark-300">Test Pass Rate</span>
                    <span class="font-medium text-green-600 dark:text-green-400">@Model.Metrics.TestPassRate.ToString("F1")%</span>
                </div>
            </div>
        </partial>

        <!-- Feature Info -->
        @{
            ViewData["Title"] = "Information";
            ViewData["Icon"] = "fas fa-info";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="space-y-3">
                @if (!string.IsNullOrEmpty(Model.Feature.Owner?.UserName))
                {
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-neutral-600 dark:text-dark-300">Owner</span>
                        <span class="font-medium">@Model.Feature.Owner.UserName</span>
                    </div>
                }
                @if (Model.Feature.TargetDate.HasValue)
                {
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-neutral-600 dark:text-dark-300">Target Date</span>
                        <span class="font-medium @(Model.Feature.TargetDate < DateTime.Now ? "text-red-600 dark:text-red-400" : "")">
                            @Model.Feature.TargetDate.Value.ToString("MMM dd, yyyy")
                        </span>
                    </div>
                }
                <div class="flex justify-between items-center">
                    <span class="text-sm text-neutral-600 dark:text-dark-300">Created</span>
                    <span class="font-medium">@Model.Feature.CreatedAt.ToString("MMM dd, yyyy")</span>
                </div>
                @if (Model.Feature.UpdatedAt.HasValue)
                {
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-neutral-600 dark:text-dark-300">Last Updated</span>
                        <span class="font-medium">@Model.Feature.UpdatedAt.Value.ToString("MMM dd, yyyy")</span>
                    </div>
                }
            </div>
        </partial>
    </div>
</div>

<!-- Related Work Items -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- User Stories -->
    @{
        ViewData["Title"] = $"User Stories ({Model.UserStories.Count()})";
        ViewData["Icon"] = "fas fa-book";
        ViewData["HeaderActions"] = $"<a href=\"{Url.Action("Create", "UserStory", new { projectId = Model.Feature.ProjectId, featureId = Model.Feature.Id })}\" class=\"btn-primary-custom btn-sm\"><i class=\"fas fa-plus mr-2\"></i>Add Story</a>";
    }
    <partial name="Components/_Card" view-data="ViewData">
        @if (Model.UserStories.Any())
        {
            <div class="space-y-3">
                @foreach (var story in Model.UserStories.Take(5))
                {
                    <div class="flex items-center justify-between p-3 bg-neutral-50 dark:bg-dark-300 rounded-lg">
                        <div class="flex-1">
                            <h5 class="font-medium text-sm text-neutral-900 dark:text-dark-100">@story.Title</h5>
                            <p class="text-xs text-neutral-500 dark:text-dark-400">@story.StoryKey</p>
                        </div>
                        <span class="px-2 py-1 text-xs font-medium rounded-full @GetUserStoryStatusBadgeClass(story.Status)">
                            @story.Status
                        </span>
                    </div>
                }
                @if (Model.UserStories.Count() > 5)
                {
                    <div class="text-center pt-2">
                        <a href="@Url.Action("Index", "UserStory", new { projectId = Model.Feature.ProjectId, featureId = Model.Feature.Id })"
                           class="text-sm text-primary-600 dark:text-primary-400 hover:underline">
                            View all @Model.UserStories.Count() user stories
                        </a>
                    </div>
                }
            </div>
        }
        else
        {
            <div class="text-center py-6">
                <i class="fas fa-book text-2xl text-neutral-300 dark:text-dark-400 mb-2"></i>
                <p class="text-sm text-neutral-500 dark:text-dark-400">No user stories yet</p>
            </div>
        }
    </partial>

    <!-- Bugs -->
    @{
        ViewData["Title"] = $"Bugs ({Model.Bugs.Count()})";
        ViewData["Icon"] = "fas fa-bug";
        ViewData["HeaderActions"] = $"<a href=\"{Url.Action("Create", "Bug", new { projectId = Model.Feature.ProjectId, featureId = Model.Feature.Id })}\" class=\"btn-primary-custom btn-sm\"><i class=\"fas fa-plus mr-2\"></i>Report Bug</a>";
    }
    <partial name="Components/_Card" view-data="ViewData">
        @if (Model.Bugs.Any())
        {
            <div class="space-y-3">
                @foreach (var bug in Model.Bugs.Take(5))
                {
                    <div class="flex items-center justify-between p-3 bg-neutral-50 dark:bg-dark-300 rounded-lg">
                        <div class="flex-1">
                            <h5 class="font-medium text-sm text-neutral-900 dark:text-dark-100">@bug.Title</h5>
                            <p class="text-xs text-neutral-500 dark:text-dark-400">@bug.BugKey - @bug.Severity</p>
                        </div>
                        <span class="px-2 py-1 text-xs font-medium rounded-full @GetBugStatusBadgeClass(bug.Status)">
                            @bug.Status
                        </span>
                    </div>
                }
                @if (Model.Bugs.Count() > 5)
                {
                    <div class="text-center pt-2">
                        <a href="@Url.Action("Index", "Bug", new { projectId = Model.Feature.ProjectId, featureId = Model.Feature.Id })"
                           class="text-sm text-primary-600 dark:text-primary-400 hover:underline">
                            View all @Model.Bugs.Count() bugs
                        </a>
                    </div>
                }
            </div>
        }
        else
        {
            <div class="text-center py-6">
                <i class="fas fa-bug text-2xl text-neutral-300 dark:text-dark-400 mb-2"></i>
                <p class="text-sm text-neutral-500 dark:text-dark-400">No bugs reported</p>
            </div>
        }
    </partial>

    <!-- Test Cases -->
    @{
        ViewData["Title"] = $"Test Cases ({Model.TestCases.Count()})";
        ViewData["Icon"] = "fas fa-vial";
        ViewData["HeaderActions"] = $"<a href=\"{Url.Action("Create", "TestCase", new { projectId = Model.Feature.ProjectId, featureId = Model.Feature.Id })}\" class=\"btn-primary-custom btn-sm\"><i class=\"fas fa-plus mr-2\"></i>Add Test</a>";
    }
    <partial name="Components/_Card" view-data="ViewData">
        @if (Model.TestCases.Any())
        {
            <div class="space-y-3">
                @foreach (var testCase in Model.TestCases.Take(5))
                {
                    <div class="flex items-center justify-between p-3 bg-neutral-50 dark:bg-dark-300 rounded-lg">
                        <div class="flex-1">
                            <h5 class="font-medium text-sm text-neutral-900 dark:text-dark-100">@testCase.Title</h5>
                            <p class="text-xs text-neutral-500 dark:text-dark-400">@testCase.TestCaseKey - @testCase.Type</p>
                        </div>
                        @if (testCase.LastExecutionResult.HasValue)
                        {
                            <span class="px-2 py-1 text-xs font-medium rounded-full @GetTestResultBadgeClass(testCase.LastExecutionResult.Value)">
                                @testCase.LastExecutionResult
                            </span>
                        }
                        else
                        {
                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100">
                                Not Run
                            </span>
                        }
                    </div>
                }
                @if (Model.TestCases.Count() > 5)
                {
                    <div class="text-center pt-2">
                        <a href="@Url.Action("Index", "TestCase", new { projectId = Model.Feature.ProjectId, featureId = Model.Feature.Id })"
                           class="text-sm text-primary-600 dark:text-primary-400 hover:underline">
                            View all @Model.TestCases.Count() test cases
                        </a>
                    </div>
                }
            </div>
        }
        else
        {
            <div class="text-center py-6">
                <i class="fas fa-vial text-2xl text-neutral-300 dark:text-dark-400 mb-2"></i>
                <p class="text-sm text-neutral-500 dark:text-dark-400">No test cases yet</p>
            </div>
        }
    </partial>
</div>

@functions {
    private string GetStatusBadgeClass(FeatureStatus status)
    {
        return status switch
        {
            FeatureStatus.Draft => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100",
            FeatureStatus.InProgress => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
            FeatureStatus.Testing => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            FeatureStatus.Completed => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            FeatureStatus.OnHold => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
            FeatureStatus.Cancelled => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100"
        };
    }

    private string GetPriorityBadgeClass(FeaturePriority priority)
    {
        return priority switch
        {
            FeaturePriority.Low => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            FeaturePriority.Medium => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            FeaturePriority.High => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
            FeaturePriority.Critical => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100"
        };
    }

    private string GetUserStoryStatusBadgeClass(UserStoryStatus status)
    {
        return status switch
        {
            UserStoryStatus.New => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100",
            UserStoryStatus.Ready => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
            UserStoryStatus.InProgress => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            UserStoryStatus.Done => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100"
        };
    }

    private string GetBugStatusBadgeClass(BugStatus status)
    {
        return status switch
        {
            BugStatus.New => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            BugStatus.Assigned => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            BugStatus.InProgress => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
            BugStatus.Resolved => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            BugStatus.Closed => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100"
        };
    }

    private string GetTestResultBadgeClass(TestExecutionResult result)
    {
        return result switch
        {
            TestExecutionResult.Passed => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            TestExecutionResult.Failed => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            TestExecutionResult.Blocked => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
            TestExecutionResult.NotApplicable => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100"
        };
    }
}
