using FluentAssertions;
using PM.Tool.Tests.E2E.Infrastructure;
using System.Net;
using Xunit;

namespace PM.Tool.Tests.E2E.Workflows
{
    [Collection("E2E")]
    public class RequirementsWorkflowE2ETests : E2ETestBase
    {
        public RequirementsWorkflowE2ETests(E2EWebApplicationFactory factory) : base(factory)
        {
        }

        [Fact]
        public async Task CompleteRequirementsWorkflow_FromCreationToTraceability_WorksEndToEnd()
        {
            // This test validates the complete requirements management workflow:
            // 1. User views requirements dashboard
            // 2. User creates functional requirements
            // 3. User creates non-functional requirements
            // 4. User links requirements to projects/tasks
            // 5. User views traceability matrix
            // 6. User generates requirements analytics
            // 7. User exports requirements documentation

            // Step 1: Navigate to Requirements Dashboard
            var requirementsResponse = await _client.GetAsync("/Requirements");
            AssertSuccessResponse(requirementsResponse);
            
            var requirementsContent = await requirementsResponse.Content.ReadAsStringAsync();
            AssertPageContains(requirementsContent, "Requirements", "Create New Requirement");

            // Step 2: Create Functional Requirement
            var (createPageContent, createToken) = await GetPageWithTokenAsync("/Requirements/Create");
            AssertPageContains(createPageContent, 
                "Create Requirement", 
                "Title", 
                "Description", 
                "Type", 
                "Priority", 
                "Status");

            var functionalReqData = new Dictionary<string, string>
            {
                ["Title"] = "User Authentication System",
                ["Description"] = "The system shall provide secure user authentication with username and password",
                ["Type"] = "1", // Functional
                ["Priority"] = "3", // High
                ["Status"] = "1", // Draft
                ["Source"] = "1", // Stakeholder
                ["Category"] = "Security",
                ["AcceptanceCriteria"] = "User can login with valid credentials and receive appropriate error messages for invalid attempts"
            };

            var createFunctionalResponse = await PostFormAsync("/Requirements/Create", functionalReqData, createToken);
            createFunctionalResponse.StatusCode.Should().BeOneOf(HttpStatusCode.Redirect, HttpStatusCode.Found);

            // Step 3: Create Non-Functional Requirement
            var (createNFPageContent, createNFToken) = await GetPageWithTokenAsync("/Requirements/Create");
            
            var nonFunctionalReqData = new Dictionary<string, string>
            {
                ["Title"] = "System Performance Requirements",
                ["Description"] = "The system shall respond to user requests within 2 seconds under normal load",
                ["Type"] = "2", // Non-Functional
                ["Priority"] = "2", // Medium
                ["Status"] = "1", // Draft
                ["Source"] = "2", // Business Analyst
                ["Category"] = "Performance",
                ["AcceptanceCriteria"] = "Response time measured under load testing scenarios"
            };

            var createNonFunctionalResponse = await PostFormAsync("/Requirements/Create", nonFunctionalReqData, createNFToken);
            createNonFunctionalResponse.StatusCode.Should().BeOneOf(HttpStatusCode.Redirect, HttpStatusCode.Found);

            // Step 4: Verify Requirements in List
            var reqListResponse = await _client.GetAsync("/Requirements");
            var reqListContent = await reqListResponse.Content.ReadAsStringAsync();
            AssertPageContains(reqListContent, 
                "User Authentication System", 
                "System Performance Requirements");

            // Step 5: Test Requirements Filtering
            var filterResponse = await _client.GetAsync("/Requirements?type=1"); // Functional only
            if (filterResponse.StatusCode == HttpStatusCode.OK)
            {
                var filterContent = await filterResponse.Content.ReadAsStringAsync();
                AssertPageContains(filterContent, "User Authentication System");
            }

            // Step 6: Test Requirement Details View
            var reqDetailsResponse = await _client.GetAsync("/Requirements/Details/1");
            
            if (reqDetailsResponse.StatusCode == HttpStatusCode.OK)
            {
                var detailsContent = await reqDetailsResponse.Content.ReadAsStringAsync();
                AssertPageContains(detailsContent, 
                    "Requirement Details", 
                    "Acceptance Criteria", 
                    "Traceability",
                    "Related Tasks");
            }
            else
            {
                // Expected if requirement doesn't exist yet
                AssertNotFoundResponse(reqDetailsResponse);
            }
        }

        [Fact]
        public async Task RequirementTraceabilityWorkflow_LinkRequirementsToProjectsAndTasks_WorksEndToEnd()
        {
            // This test validates requirement traceability functionality

            // Step 1: Access Traceability Matrix
            var traceabilityResponse = await _client.GetAsync("/Requirements/TraceabilityMatrix");
            AssertSuccessResponse(traceabilityResponse);
            
            var traceabilityContent = await traceabilityResponse.Content.ReadAsStringAsync();
            AssertPageContains(traceabilityContent, "Traceability Matrix", "Requirements", "Projects", "Tasks");

            // Step 2: Test Traceability Matrix with Project Filter
            var projectTraceResponse = await _client.GetAsync("/Requirements/TraceabilityMatrix?projectId=1");
            AssertSuccessResponse(projectTraceResponse);

            // Step 3: Test Requirement Linking (if requirement exists)
            var linkRequirementResponse = await _client.GetAsync("/Requirements/LinkToProject/1");
            
            if (linkRequirementResponse.StatusCode == HttpStatusCode.OK)
            {
                var linkContent = await linkRequirementResponse.Content.ReadAsStringAsync();
                AssertPageContains(linkContent, "Link Requirement", "Select Project");
            }
            else
            {
                // Expected if requirement doesn't exist
                linkRequirementResponse.StatusCode.Should().BeOneOf(HttpStatusCode.NotFound, HttpStatusCode.Redirect);
            }
        }

        [Fact]
        public async Task RequirementAnalyticsWorkflow_ViewRequirementMetrics_WorksEndToEnd()
        {
            // This test validates requirements analytics functionality

            // Step 1: Access Requirements Analytics
            var analyticsResponse = await _client.GetAsync("/Requirements/Analytics");
            AssertSuccessResponse(analyticsResponse);
            
            var analyticsContent = await analyticsResponse.Content.ReadAsStringAsync();
            AssertPageContains(analyticsContent, 
                "Requirements Analytics", 
                "Status Distribution", 
                "Priority Breakdown",
                "Type Analysis");

            // Step 2: Test Analytics API Endpoints
            var statusDataResponse = await _client.GetAsync("/Requirements/GetStatusDistribution");
            
            if (statusDataResponse.StatusCode == HttpStatusCode.OK)
            {
                statusDataResponse.Content.Headers.ContentType?.MediaType.Should().Be("application/json");
            }
            else
            {
                // Expected if no requirements exist
                statusDataResponse.StatusCode.Should().BeOneOf(HttpStatusCode.NoContent, HttpStatusCode.NotFound);
            }

            var priorityDataResponse = await _client.GetAsync("/Requirements/GetPriorityBreakdown");
            
            if (priorityDataResponse.StatusCode == HttpStatusCode.OK)
            {
                priorityDataResponse.Content.Headers.ContentType?.MediaType.Should().Be("application/json");
            }
            else
            {
                // Expected if no requirements exist
                priorityDataResponse.StatusCode.Should().BeOneOf(HttpStatusCode.NoContent, HttpStatusCode.NotFound);
            }
        }

        [Fact]
        public async Task RequirementEditingWorkflow_UpdateRequirementDetails_WorksEndToEnd()
        {
            // This test validates requirement editing functionality

            // Step 1: Test Edit Form Access (if requirement exists)
            var editResponse = await _client.GetAsync("/Requirements/Edit/1");
            
            if (editResponse.StatusCode == HttpStatusCode.OK)
            {
                var (editPageContent, editToken) = await GetPageWithTokenAsync("/Requirements/Edit/1");
                AssertPageContains(editPageContent, 
                    "Edit Requirement", 
                    "Title", 
                    "Description", 
                    "Status");

                // Step 2: Submit Edit Form
                var editData = new Dictionary<string, string>
                {
                    ["Title"] = "Updated User Authentication System",
                    ["Description"] = "Updated description for user authentication requirements",
                    ["Type"] = "1", // Functional
                    ["Priority"] = "3", // High
                    ["Status"] = "2", // Under Review
                    ["Source"] = "1", // Stakeholder
                    ["Category"] = "Security"
                };

                var updateResponse = await PostFormAsync("/Requirements/Edit/1", editData, editToken);
                updateResponse.StatusCode.Should().BeOneOf(HttpStatusCode.Redirect, HttpStatusCode.Found);
            }
            else
            {
                // Expected if requirement doesn't exist
                AssertNotFoundResponse(editResponse);
            }
        }

        [Fact]
        public async Task RequirementValidationWorkflow_TestFormValidation_WorksEndToEnd()
        {
            // This test validates form validation for requirements

            // Step 1: Submit Invalid Requirement (missing required fields)
            var (createPageContent, createToken) = await GetPageWithTokenAsync("/Requirements/Create");
            
            var invalidReqData = new Dictionary<string, string>
            {
                ["Title"] = "", // Empty title should cause validation error
                ["Description"] = "",
                ["Type"] = "1",
                ["Priority"] = "1",
                ["Status"] = "1",
                ["Source"] = "1"
            };

            var invalidResponse = await PostFormAsync("/Requirements/Create", invalidReqData, createToken);
            
            if (invalidResponse.StatusCode == HttpStatusCode.OK)
            {
                // Form should return with validation errors
                var errorContent = await invalidResponse.Content.ReadAsStringAsync();
                AssertPageContains(errorContent, "Create Requirement");
                // Should contain validation error messages
            }
            else
            {
                // May redirect or handle differently based on validation setup
                invalidResponse.StatusCode.Should().BeOneOf(HttpStatusCode.Redirect, HttpStatusCode.BadRequest);
            }
        }

        [Fact]
        public async Task RequirementSearchWorkflow_SearchAndFilterRequirements_WorksEndToEnd()
        {
            // This test validates requirement search and filtering

            // Step 1: Test Search Functionality
            var searchResponse = await _client.GetAsync("/Requirements?search=authentication");
            AssertSuccessResponse(searchResponse);
            
            var searchContent = await searchResponse.Content.ReadAsStringAsync();
            AssertPageContains(searchContent, "Requirements");

            // Step 2: Test Type Filtering
            var typeFilterResponse = await _client.GetAsync("/Requirements?type=1"); // Functional
            AssertSuccessResponse(typeFilterResponse);

            // Step 3: Test Priority Filtering
            var priorityFilterResponse = await _client.GetAsync("/Requirements?priority=3"); // High
            AssertSuccessResponse(priorityFilterResponse);

            // Step 4: Test Status Filtering
            var statusFilterResponse = await _client.GetAsync("/Requirements?status=1"); // Draft
            AssertSuccessResponse(statusFilterResponse);

            // Step 5: Test Combined Filters
            var combinedFilterResponse = await _client.GetAsync("/Requirements?type=1&priority=3&status=1");
            AssertSuccessResponse(combinedFilterResponse);
        }

        [Fact]
        public async Task RequirementExportWorkflow_ExportRequirementsDocumentation_WorksEndToEnd()
        {
            // This test validates requirement export functionality

            // Step 1: Test Export to PDF (if implemented)
            var pdfExportResponse = await _client.GetAsync("/Requirements/ExportToPdf");
            
            if (pdfExportResponse.StatusCode == HttpStatusCode.OK)
            {
                pdfExportResponse.Content.Headers.ContentType?.MediaType.Should().Be("application/pdf");
            }
            else
            {
                // Expected if export feature not implemented or no data
                pdfExportResponse.StatusCode.Should().BeOneOf(
                    HttpStatusCode.NotFound, 
                    HttpStatusCode.NoContent,
                    HttpStatusCode.NotImplemented);
            }

            // Step 2: Test Export to Excel (if implemented)
            var excelExportResponse = await _client.GetAsync("/Requirements/ExportToExcel");
            
            if (excelExportResponse.StatusCode == HttpStatusCode.OK)
            {
                var contentType = excelExportResponse.Content.Headers.ContentType?.MediaType;
                contentType.Should().BeOneOf(
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "application/vnd.ms-excel");
            }
            else
            {
                // Expected if export feature not implemented or no data
                excelExportResponse.StatusCode.Should().BeOneOf(
                    HttpStatusCode.NotFound, 
                    HttpStatusCode.NoContent,
                    HttpStatusCode.NotImplemented);
            }
        }

        [Fact]
        public async Task RequirementDeletionWorkflow_DeleteRequirement_WorksEndToEnd()
        {
            // This test validates requirement deletion functionality

            // Step 1: Test Delete Confirmation Page (if requirement exists)
            var deleteResponse = await _client.GetAsync("/Requirements/Delete/1");
            
            if (deleteResponse.StatusCode == HttpStatusCode.OK)
            {
                var deleteContent = await deleteResponse.Content.ReadAsStringAsync();
                AssertPageContains(deleteContent, "Delete Requirement", "Are you sure");

                // Step 2: Confirm Deletion
                var (deletePageContent, deleteToken) = await GetPageWithTokenAsync("/Requirements/Delete/1");
                
                var confirmDeleteResponse = await PostFormAsync("/Requirements/Delete/1", 
                    new Dictionary<string, string>(), deleteToken);
                confirmDeleteResponse.StatusCode.Should().BeOneOf(HttpStatusCode.Redirect, HttpStatusCode.Found);
            }
            else
            {
                // Expected if requirement doesn't exist
                AssertNotFoundResponse(deleteResponse);
            }
        }

        [Fact]
        public async Task RequirementAPIEndpoints_TestAjaxEndpoints_WorkCorrectly()
        {
            // This test validates AJAX endpoints used by the Requirements module

            var apiEndpoints = new[]
            {
                "/Requirements/GetStatusDistribution",
                "/Requirements/GetPriorityBreakdown", 
                "/Requirements/GetTypeAnalysis",
                "/Requirements/GetTraceabilityData/1"
            };

            foreach (var endpoint in apiEndpoints)
            {
                var response = await _client.GetAsync(endpoint);
                
                if (response.StatusCode == HttpStatusCode.OK)
                {
                    // Should return JSON
                    response.Content.Headers.ContentType?.MediaType.Should().Be("application/json");
                }
                else
                {
                    // Expected if no data exists
                    response.StatusCode.Should().BeOneOf(
                        HttpStatusCode.NotFound, 
                        HttpStatusCode.NoContent,
                        HttpStatusCode.BadRequest);
                }
            }
        }
    }
}
