# PM Tool - Developer Guide

## 🛠️ Development Overview

This guide provides comprehensive information for developers working on the PM Tool project, including architecture, coding standards, development workflows, and contribution guidelines.

## 🏗️ Architecture Overview

### Solution Structure
```
PM.Tool/
├── PM.Tool.Core/                 # Domain layer
│   ├── Entities/                 # Domain entities
│   ├── Enums/                    # Enumerations
│   ├── Interfaces/               # Service contracts
│   ├── Authorization/            # Authorization policies
│   └── Localization/             # Localization interfaces
├── PM.Tool.Application/          # Application layer
│   ├── Services/                 # Application services
│   ├── DTOs/                     # Data transfer objects
│   ├── Validators/               # FluentValidation validators
│   └── Mappings/                 # AutoMapper profiles
├── PM.Tool.Infrastructure/       # Infrastructure layer
│   ├── Services/                 # Infrastructure services
│   ├── Localization/             # Localization implementation
│   ├── Filters/                  # Action filters
│   └── Middleware/               # Custom middleware
├── PM.Tool.Data/                 # Data access layer
│   ├── ApplicationDbContext.cs   # EF Core context
│   ├── Migrations/               # Database migrations
│   └── Configurations/           # Entity configurations
├── PM.Tool/                      # Presentation layer
│   ├── Controllers/              # MVC controllers
│   ├── Views/                    # Razor views
│   ├── wwwroot/                  # Static files
│   └── Resources/                # Localization resources
└── PM.Tool.Tests/                # Test projects
    ├── Unit/                     # Unit tests
    ├── Integration/              # Integration tests
    └── E2E/                      # End-to-end tests
```

### Design Patterns
- **Clean Architecture**: Separation of concerns with dependency inversion
- **Repository Pattern**: Data access abstraction
- **Specification Pattern**: Complex query logic encapsulation
- **CQRS**: Command Query Responsibility Segregation
- **Mediator Pattern**: Decoupled request/response handling
- **Factory Pattern**: Object creation abstraction
- **Observer Pattern**: Event-driven notifications

## 🚀 Development Environment Setup

### Prerequisites
```bash
# Install .NET 9.0 SDK
winget install Microsoft.DotNet.SDK.9

# Install Node.js (for frontend tooling)
winget install OpenJS.NodeJS

# Install PostgreSQL
winget install PostgreSQL.PostgreSQL

# Install Git
winget install Git.Git

# Install Visual Studio or VS Code
winget install Microsoft.VisualStudio.2022.Community
# OR
winget install Microsoft.VisualStudioCode
```

### IDE Extensions (VS Code)
```json
{
  "recommendations": [
    "ms-dotnettools.csharp",
    "ms-dotnettools.vscode-dotnet-runtime",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-json",
    "humao.rest-client",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense"
  ]
}
```

### Local Development Setup
```bash
# Clone repository
git clone <repository-url>
cd PM.Tool

# Restore packages
dotnet restore

# Set up database
dotnet ef database update

# Install frontend dependencies
npm install

# Build solution
dotnet build

# Run application
dotnet run --project PM.Tool
```

### Environment Configuration
```json
// appsettings.Development.json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore.Database.Command": "Information"
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=pmtool_dev;Username=dev_user;Password=dev_password"
  },
  "JwtSettings": {
    "SecretKey": "development-secret-key-256-bits-long",
    "Issuer": "PMTool-Dev",
    "Audience": "PMToolUsers-Dev",
    "ExpirationMinutes": 480
  }
}
```

## 📝 Coding Standards

### C# Coding Conventions
```csharp
// Use PascalCase for public members
public class ProjectService : IProjectService
{
    // Use camelCase for private fields with underscore prefix
    private readonly IProjectRepository _projectRepository;
    private readonly ILogger<ProjectService> _logger;

    // Use PascalCase for properties
    public string ProjectName { get; set; }

    // Use async/await for asynchronous operations
    public async Task<Project> GetProjectByIdAsync(int id)
    {
        // Use guard clauses for validation
        if (id <= 0)
            throw new ArgumentException("Project ID must be positive", nameof(id));

        // Use meaningful variable names
        var project = await _projectRepository.GetByIdAsync(id);
        
        // Use null-conditional operators where appropriate
        return project ?? throw new NotFoundException($"Project with ID {id} not found");
    }

    // Use expression-bodied members for simple operations
    public bool IsProjectActive(Project project) => 
        project.Status == ProjectStatus.Active;
}
```

### Entity Design Patterns
```csharp
// Base entity with common properties
public abstract class BaseEntity
{
    public int Id { get; set; }
    
    private DateTime _createdAt;
    public DateTime CreatedAt
    {
        get => _createdAt;
        set => _createdAt = DateTime.SpecifyKind(value, DateTimeKind.Utc);
    }
    
    private DateTime _updatedAt;
    public DateTime UpdatedAt
    {
        get => _updatedAt;
        set => _updatedAt = DateTime.SpecifyKind(value, DateTimeKind.Utc);
    }
    
    public string CreatedByUserId { get; set; } = string.Empty;
    public string UpdatedByUserId { get; set; } = string.Empty;
}

// Domain entity with business logic
public class Project : BaseEntity
{
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;
    
    // Use computed properties for business logic
    public bool IsOverdue => EndDate.HasValue && 
                           DateTime.UtcNow > EndDate.Value && 
                           Status != ProjectStatus.Completed;
    
    // Use collections for navigation properties
    public virtual ICollection<TaskEntity> Tasks { get; set; } = new List<TaskEntity>();
}
```

### Service Layer Patterns
```csharp
// Service interface
public interface IProjectService
{
    Task<IEnumerable<Project>> GetAllProjectsAsync();
    Task<Project?> GetProjectByIdAsync(int id);
    Task<Project> CreateProjectAsync(Project project);
    Task<Project> UpdateProjectAsync(Project project);
    Task<bool> DeleteProjectAsync(int id);
}

// Service implementation
public class ProjectService : IProjectService
{
    private readonly IProjectRepository _repository;
    private readonly IMapper _mapper;
    private readonly ILogger<ProjectService> _logger;

    public ProjectService(
        IProjectRepository repository,
        IMapper mapper,
        ILogger<ProjectService> logger)
    {
        _repository = repository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<Project> CreateProjectAsync(Project project)
    {
        try
        {
            // Validation
            if (project == null)
                throw new ArgumentNullException(nameof(project));

            // Business logic
            project.CreatedAt = DateTime.UtcNow;
            project.Status = ProjectStatus.Planning;

            // Repository operation
            var createdProject = await _repository.AddAsync(project);
            
            // Logging
            _logger.LogInformation("Project created: {ProjectId}", createdProject.Id);
            
            return createdProject;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating project: {ProjectName}", project?.Name);
            throw;
        }
    }
}
```

### Controller Patterns
```csharp
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ProjectsController : ControllerBase
{
    private readonly IProjectService _projectService;
    private readonly ILogger<ProjectsController> _logger;

    public ProjectsController(
        IProjectService projectService,
        ILogger<ProjectsController> logger)
    {
        _projectService = projectService;
        _logger = logger;
    }

    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<ProjectDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<ProjectDto>>> GetProjects()
    {
        try
        {
            var projects = await _projectService.GetAllProjectsAsync();
            return Ok(projects);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving projects");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost]
    [ProducesResponseType(typeof(ProjectDto), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ProjectDto>> CreateProject([FromBody] CreateProjectDto dto)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        try
        {
            var project = _mapper.Map<Project>(dto);
            var createdProject = await _projectService.CreateProjectAsync(project);
            var result = _mapper.Map<ProjectDto>(createdProject);
            
            return CreatedAtAction(
                nameof(GetProject), 
                new { id = result.Id }, 
                result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating project");
            return StatusCode(500, "Internal server error");
        }
    }
}
```

## 🗄️ Database Development

### Entity Framework Conventions
```csharp
// Entity configuration
public class ProjectConfiguration : IEntityTypeConfiguration<Project>
{
    public void Configure(EntityTypeBuilder<Project> builder)
    {
        // Table configuration
        builder.ToTable("Projects");
        
        // Primary key
        builder.HasKey(p => p.Id);
        
        // Properties
        builder.Property(p => p.Name)
            .IsRequired()
            .HasMaxLength(200);
            
        builder.Property(p => p.Description)
            .HasMaxLength(2000);
            
        // Indexes
        builder.HasIndex(p => p.Name)
            .IsUnique();
            
        builder.HasIndex(p => p.Status);
        
        // Relationships
        builder.HasMany(p => p.Tasks)
            .WithOne(t => t.Project)
            .HasForeignKey(t => t.ProjectId)
            .OnDelete(DeleteBehavior.Cascade);
            
        // Value conversions
        builder.Property(p => p.Status)
            .HasConversion<int>();
    }
}
```

### Migration Best Practices
```bash
# Create migration
dotnet ef migrations add "DescriptiveMigrationName" --project PM.Tool.Data

# Review migration before applying
# Check generated SQL and ensure it's correct

# Apply migration
dotnet ef database update --project PM.Tool.Data

# Rollback if needed
dotnet ef database update PreviousMigrationName --project PM.Tool.Data
```

### Database Seeding
```csharp
public static class DatabaseSeeder
{
    public static async Task SeedAsync(ApplicationDbContext context)
    {
        // Seed roles
        if (!context.Roles.Any())
        {
            var roles = new[]
            {
                new IdentityRole { Name = "Administrator", NormalizedName = "ADMINISTRATOR" },
                new IdentityRole { Name = "ProjectManager", NormalizedName = "PROJECTMANAGER" },
                new IdentityRole { Name = "TeamMember", NormalizedName = "TEAMMEMBER" }
            };
            
            context.Roles.AddRange(roles);
            await context.SaveChangesAsync();
        }

        // Seed default data
        if (!context.Projects.Any())
        {
            var defaultProject = new Project
            {
                Name = "Sample Project",
                Description = "This is a sample project for demonstration",
                Status = ProjectStatus.Planning,
                CreatedAt = DateTime.UtcNow
            };
            
            context.Projects.Add(defaultProject);
            await context.SaveChangesAsync();
        }
    }
}
```

## 🧪 Testing Guidelines

### Unit Testing
```csharp
[TestClass]
public class ProjectServiceTests
{
    private Mock<IProjectRepository> _mockRepository;
    private Mock<IMapper> _mockMapper;
    private Mock<ILogger<ProjectService>> _mockLogger;
    private ProjectService _service;

    [TestInitialize]
    public void Setup()
    {
        _mockRepository = new Mock<IProjectRepository>();
        _mockMapper = new Mock<IMapper>();
        _mockLogger = new Mock<ILogger<ProjectService>>();
        _service = new ProjectService(_mockRepository.Object, _mockMapper.Object, _mockLogger.Object);
    }

    [TestMethod]
    public async Task CreateProjectAsync_ValidProject_ReturnsCreatedProject()
    {
        // Arrange
        var project = new Project { Name = "Test Project" };
        var expectedProject = new Project { Id = 1, Name = "Test Project" };
        
        _mockRepository.Setup(r => r.AddAsync(It.IsAny<Project>()))
                      .ReturnsAsync(expectedProject);

        // Act
        var result = await _service.CreateProjectAsync(project);

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(expectedProject.Id, result.Id);
        Assert.AreEqual(expectedProject.Name, result.Name);
        _mockRepository.Verify(r => r.AddAsync(It.IsAny<Project>()), Times.Once);
    }

    [TestMethod]
    [ExpectedException(typeof(ArgumentNullException))]
    public async Task CreateProjectAsync_NullProject_ThrowsArgumentNullException()
    {
        // Act & Assert
        await _service.CreateProjectAsync(null);
    }
}
```

### Integration Testing
```csharp
[TestClass]
public class ProjectsControllerIntegrationTests
{
    private WebApplicationFactory<Program> _factory;
    private HttpClient _client;

    [TestInitialize]
    public void Setup()
    {
        _factory = new WebApplicationFactory<Program>()
            .WithWebHostBuilder(builder =>
            {
                builder.ConfigureServices(services =>
                {
                    // Replace database with in-memory database
                    services.RemoveAll(typeof(DbContextOptions<ApplicationDbContext>));
                    services.AddDbContext<ApplicationDbContext>(options =>
                        options.UseInMemoryDatabase("TestDb"));
                });
            });
        
        _client = _factory.CreateClient();
    }

    [TestMethod]
    public async Task GetProjects_ReturnsSuccessStatusCode()
    {
        // Act
        var response = await _client.GetAsync("/api/projects");

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        Assert.IsNotNull(content);
    }

    [TestCleanup]
    public void Cleanup()
    {
        _client?.Dispose();
        _factory?.Dispose();
    }
}
```

## 🌐 Frontend Development

### JavaScript/TypeScript Standards
```javascript
// Use modern ES6+ features
class KanbanBoard {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = { ...this.defaultOptions, ...options };
        this.init();
    }

    // Use async/await for asynchronous operations
    async loadData() {
        try {
            const response = await fetch(this.options.apiEndpoint);
            const data = await response.json();
            this.updateBoard(data);
        } catch (error) {
            console.error('Error loading data:', error);
            this.showError('Failed to load board data');
        }
    }

    // Use arrow functions for event handlers
    handleCardDrop = (event) => {
        event.preventDefault();
        const cardId = event.dataTransfer.getData('text/plain');
        const targetColumn = event.target.closest('.kanban-column');
        this.moveCard(cardId, targetColumn.dataset.columnId);
    }

    // Use template literals for HTML generation
    renderCard(card) {
        return `
            <div class="kanban-card" data-card-id="${card.id}">
                <h4>${this.escapeHtml(card.title)}</h4>
                <p>${this.escapeHtml(card.description)}</p>
                <div class="card-meta">
                    <span class="priority ${card.priority.toLowerCase()}">${card.priority}</span>
                    <span class="assignee">${card.assignee}</span>
                </div>
            </div>
        `;
    }
}
```

### CSS/SCSS Organization
```scss
// Variables
:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    --font-family-sans: 'Inter', sans-serif;
    --font-size-base: 1rem;
    --line-height-base: 1.5;
    
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
}

// Mixins
@mixin button-variant($bg-color, $text-color: white) {
    background-color: $bg-color;
    color: $text-color;
    border: 1px solid $bg-color;
    
    &:hover {
        background-color: darken($bg-color, 10%);
        border-color: darken($bg-color, 10%);
    }
    
    &:focus {
        box-shadow: 0 0 0 3px rgba($bg-color, 0.25);
    }
}

// Component styles
.btn-modern {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    font-family: var(--font-family-sans);
    font-size: var(--font-size-base);
    border-radius: 0.375rem;
    transition: all 0.15s ease-in-out;
    
    &.btn-primary {
        @include button-variant(var(--primary-color));
    }
    
    &.btn-secondary {
        @include button-variant(var(--secondary-color));
    }
}
```

## 🔧 Build & Deployment

### Build Configuration
```xml
<!-- Directory.Build.props -->
<Project>
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>CS1591</WarningsNotAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.CodeAnalysis.Analyzers" Version="3.3.4" PrivateAssets="all" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.1.118" PrivateAssets="all" />
  </ItemGroup>
</Project>
```

### Docker Configuration
```dockerfile
# Dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 8080
EXPOSE 8443

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
COPY ["PM.Tool/PM.Tool.csproj", "PM.Tool/"]
COPY ["PM.Tool.Core/PM.Tool.Core.csproj", "PM.Tool.Core/"]
COPY ["PM.Tool.Application/PM.Tool.Application.csproj", "PM.Tool.Application/"]
COPY ["PM.Tool.Infrastructure/PM.Tool.Infrastructure.csproj", "PM.Tool.Infrastructure/"]
COPY ["PM.Tool.Data/PM.Tool.Data.csproj", "PM.Tool.Data/"]

RUN dotnet restore "PM.Tool/PM.Tool.csproj"
COPY . .
WORKDIR "/src/PM.Tool"
RUN dotnet build "PM.Tool.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "PM.Tool.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "PM.Tool.dll"]
```

### CI/CD Pipeline (GitHub Actions)
```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: pmtool_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: 9.0.x
        
    - name: Restore dependencies
      run: dotnet restore
      
    - name: Build
      run: dotnet build --no-restore
      
    - name: Test
      run: dotnet test --no-build --verbosity normal --collect:"XPlat Code Coverage"
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Build and push Docker image
      run: |
        docker build -t pmtool:latest .
        docker tag pmtool:latest ${{ secrets.REGISTRY_URL }}/pmtool:latest
        docker push ${{ secrets.REGISTRY_URL }}/pmtool:latest
```

## 🔍 Debugging & Troubleshooting

### Logging Configuration
```csharp
// Program.cs
builder.Host.UseSerilog((context, configuration) =>
{
    configuration
        .ReadFrom.Configuration(context.Configuration)
        .Enrich.FromLogContext()
        .Enrich.WithMachineName()
        .Enrich.WithEnvironmentUserName()
        .WriteTo.Console(outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}")
        .WriteTo.File("logs/log-.txt", rollingInterval: RollingInterval.Day);
});
```

### Performance Monitoring
```csharp
// Custom middleware for performance monitoring
public class PerformanceMonitoringMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<PerformanceMonitoringMiddleware> _logger;

    public PerformanceMonitoringMiddleware(RequestDelegate next, ILogger<PerformanceMonitoringMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        
        await _next(context);
        
        stopwatch.Stop();
        
        if (stopwatch.ElapsedMilliseconds > 1000) // Log slow requests
        {
            _logger.LogWarning("Slow request: {Method} {Path} took {ElapsedMilliseconds}ms",
                context.Request.Method,
                context.Request.Path,
                stopwatch.ElapsedMilliseconds);
        }
    }
}
```

## 📚 Documentation Standards

### XML Documentation
```csharp
/// <summary>
/// Represents a project management service that handles project operations.
/// </summary>
public interface IProjectService
{
    /// <summary>
    /// Retrieves a project by its unique identifier.
    /// </summary>
    /// <param name="id">The unique identifier of the project.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. 
    /// The task result contains the project if found; otherwise, null.
    /// </returns>
    /// <exception cref="ArgumentException">Thrown when the id is less than or equal to zero.</exception>
    Task<Project?> GetProjectByIdAsync(int id);
}
```

### API Documentation
```csharp
/// <summary>
/// Creates a new project.
/// </summary>
/// <param name="dto">The project creation data.</param>
/// <returns>The created project.</returns>
/// <response code="201">Returns the newly created project</response>
/// <response code="400">If the project data is invalid</response>
/// <response code="401">If the user is not authenticated</response>
/// <response code="403">If the user doesn't have permission to create projects</response>
[HttpPost]
[ProducesResponseType(typeof(ProjectDto), StatusCodes.Status201Created)]
[ProducesResponseType(StatusCodes.Status400BadRequest)]
[ProducesResponseType(StatusCodes.Status401Unauthorized)]
[ProducesResponseType(StatusCodes.Status403Forbidden)]
public async Task<ActionResult<ProjectDto>> CreateProject([FromBody] CreateProjectDto dto)
{
    // Implementation
}
```

## 🤝 Contributing Guidelines

### Git Workflow
```bash
# Create feature branch
git checkout -b feature/new-feature-name

# Make changes and commit
git add .
git commit -m "feat: add new feature description"

# Push branch
git push origin feature/new-feature-name

# Create pull request
# Follow PR template and ensure all checks pass
```

### Commit Message Convention
```
type(scope): description

[optional body]

[optional footer]
```

Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

### Code Review Checklist
- [ ] Code follows established conventions
- [ ] All tests pass
- [ ] Code coverage meets requirements
- [ ] Documentation is updated
- [ ] Security considerations addressed
- [ ] Performance impact assessed
- [ ] Accessibility requirements met

---

*This developer guide provides comprehensive information for contributing to PM Tool. For additional technical details, please refer to the inline code documentation and architectural decision records.*
