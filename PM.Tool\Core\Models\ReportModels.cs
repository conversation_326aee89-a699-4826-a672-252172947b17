namespace PM.Tool.Core.Models
{
    public class ProjectReportModel
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime CreatedAt { get; set; }
        public decimal Budget { get; set; }
        public string ClientName { get; set; } = string.Empty;
        public double ProgressPercentage { get; set; }
        public int TotalTasks { get; set; }
        public int CompletedTasks { get; set; }
    }

    public class TaskReportModel
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public string AssignedTo { get; set; } = string.Empty;
        public DateTime? StartDate { get; set; }
        public DateTime? DueDate { get; set; }
        public DateTime? CompletedDate { get; set; }
        public int EstimatedHours { get; set; }
        public int ActualHours { get; set; }
        public double Progress { get; set; }
    }

    public class TeamMemberMetrics
    {
        public string MemberName { get; set; } = string.Empty;
        public int CompletedTasks { get; set; }
        public int TotalHours { get; set; }
        public double EfficiencyRatio { get; set; }
    }

    public class VelocityDataPoint
    {
        public DateTime WeekStartDate { get; set; }
        public int CompletedTasks { get; set; }
        public double AverageCycleTime { get; set; }
    }

    public class UserProductivityData
    {
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public int CompletedTasks { get; set; }
        public double TotalTimeSpent { get; set; }
        public double AverageTimePerTask { get; set; }
    }
}
