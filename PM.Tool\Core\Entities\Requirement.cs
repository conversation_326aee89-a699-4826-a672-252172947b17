using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities
{
    public class Requirement : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [MaxLength(5000)]
        public string Description { get; set; } = string.Empty;

        public int ProjectId { get; set; }

        [MaxLength(50)]
        public string RequirementId { get; set; } = string.Empty; // REQ-001, etc.

        public RequirementType Type { get; set; } = RequirementType.Functional;

        public RequirementPriority Priority { get; set; } = RequirementPriority.Medium;

        public RequirementStatus Status { get; set; } = RequirementStatus.Draft;

        public RequirementSource Source { get; set; } = RequirementSource.Stakeholder;

        [MaxLength(2000)]
        public string? AcceptanceCriteria { get; set; }

        [MaxLength(1000)]
        public string? BusinessJustification { get; set; }

        public decimal EstimatedEffort { get; set; } // Story points or hours

        public string? StakeholderUserId { get; set; }

        public string? AnalystUserId { get; set; }

        public string? DeveloperUserId { get; set; }

        public string? TesterUserId { get; set; }

        private DateTime? _targetDate;
        public DateTime? TargetDate
        {
            get => _targetDate;
            set => _targetDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        private DateTime? _approvedDate;
        public DateTime? ApprovedDate
        {
            get => _approvedDate;
            set => _approvedDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        public string? ApprovedByUserId { get; set; }

        [MaxLength(2000)]
        public string? Notes { get; set; }

        [MaxLength(1000)]
        public string? TestCases { get; set; }

        public int? ParentRequirementId { get; set; }

        [MaxLength(500)]
        public string? Tags { get; set; } // JSON array of tags

        public bool IsBlocked { get; set; } = false;

        [MaxLength(1000)]
        public string? BlockedReason { get; set; }

        // Navigation properties
        public virtual Project Project { get; set; } = null!;
        public virtual ApplicationUser? Stakeholder { get; set; }
        public virtual ApplicationUser? Analyst { get; set; }
        public virtual ApplicationUser? Developer { get; set; }
        public virtual ApplicationUser? Tester { get; set; }
        public virtual ApplicationUser? ApprovedBy { get; set; }
        public virtual Requirement? ParentRequirement { get; set; }
        public virtual ICollection<Requirement> ChildRequirements { get; set; } = new List<Requirement>();
        public virtual ICollection<RequirementComment> Comments { get; set; } = new List<RequirementComment>();
        public virtual ICollection<RequirementAttachment> Attachments { get; set; } = new List<RequirementAttachment>();
        public virtual ICollection<RequirementChange> Changes { get; set; } = new List<RequirementChange>();
        public virtual ICollection<RequirementTask> Tasks { get; set; } = new List<RequirementTask>();

        // Computed properties
        public bool IsApproved => Status == RequirementStatus.Approved;
        public bool IsOverdue => TargetDate.HasValue && DateTime.UtcNow > TargetDate.Value && Status != RequirementStatus.Completed;
        public bool HasChildren => ChildRequirements.Any();
        public int ChildCount => ChildRequirements.Count;
        public double CompletionPercentage => CalculateCompletionPercentage();

        private double CalculateCompletionPercentage()
        {
            if (!HasChildren)
            {
                return Status switch
                {
                    RequirementStatus.Completed => 100,
                    RequirementStatus.InProgress => 50,
                    RequirementStatus.Testing => 80,
                    RequirementStatus.Approved => 25,
                    _ => 0
                };
            }

            var completedChildren = ChildRequirements.Count(c => c.Status == RequirementStatus.Completed);
            return ChildRequirements.Count > 0 ? (double)completedChildren / ChildRequirements.Count * 100 : 0;
        }
    }

    public class RequirementComment : BaseEntity
    {
        public int RequirementId { get; set; }

        [Required]
        [MaxLength(2000)]
        public string Content { get; set; } = string.Empty;

        public string UserId { get; set; } = string.Empty;

        public CommentType Type { get; set; } = CommentType.General;

        // Navigation properties
        public virtual Requirement Requirement { get; set; } = null!;
        public virtual ApplicationUser User { get; set; } = null!;
    }

    public class RequirementAttachment : BaseEntity
    {
        public int RequirementId { get; set; }

        [Required]
        [MaxLength(255)]
        public string FileName { get; set; } = string.Empty;

        [Required]
        [MaxLength(500)]
        public string FilePath { get; set; } = string.Empty;

        [MaxLength(100)]
        public string? ContentType { get; set; }

        public long FileSize { get; set; }

        [MaxLength(500)]
        public string? Description { get; set; }

        public string UploadedByUserId { get; set; } = string.Empty;

        // Navigation properties
        public virtual Requirement Requirement { get; set; } = null!;
        public virtual ApplicationUser UploadedBy { get; set; } = null!;
    }

    public class RequirementChange : BaseEntity
    {
        public int RequirementId { get; set; }

        [Required]
        [MaxLength(500)]
        public string ChangeDescription { get; set; } = string.Empty;

        public ChangeType Type { get; set; } = ChangeType.Modification;

        [MaxLength(2000)]
        public string? Justification { get; set; }

        public ChangeStatus Status { get; set; } = ChangeStatus.Proposed;

        public string RequestedByUserId { get; set; } = string.Empty;

        public string? ApprovedByUserId { get; set; }

        private DateTime? _approvedDate;
        public DateTime? ApprovedDate
        {
            get => _approvedDate;
            set => _approvedDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        public decimal ImpactEffort { get; set; } // Additional effort required

        [MaxLength(1000)]
        public string? ImpactAnalysis { get; set; }

        // Navigation properties
        public virtual Requirement Requirement { get; set; } = null!;
        public virtual ApplicationUser RequestedBy { get; set; } = null!;
        public virtual ApplicationUser? ApprovedBy { get; set; }
    }

    public class RequirementTask : BaseEntity
    {
        public int RequirementId { get; set; }

        public int TaskId { get; set; }

        [MaxLength(500)]
        public string? Notes { get; set; }

        // Navigation properties
        public virtual Requirement Requirement { get; set; } = null!;
        public virtual TaskEntity Task { get; set; } = null!;
    }

    public enum RequirementType
    {
        Functional = 1,
        NonFunctional = 2,
        Business = 3,
        Technical = 4,
        Performance = 5,
        Security = 6,
        Usability = 7,
        Compliance = 8,
        Integration = 9
    }

    public enum RequirementPriority
    {
        Critical = 1,
        High = 2,
        Medium = 3,
        Low = 4,
        Nice2Have = 5
    }

    public enum RequirementStatus
    {
        Draft = 1,
        UnderReview = 2,
        Approved = 3,
        InProgress = 4,
        Testing = 5,
        Completed = 6,
        Rejected = 7,
        OnHold = 8,
        Cancelled = 9
    }

    public enum RequirementSource
    {
        Stakeholder = 1,
        BusinessAnalyst = 2,
        Customer = 3,
        Regulatory = 4,
        Technical = 5,
        Market = 6,
        Internal = 7
    }

    public enum CommentType
    {
        General = 1,
        Question = 2,
        Clarification = 3,
        Concern = 4,
        Approval = 5,
        Rejection = 6
    }

    public enum ChangeType
    {
        Addition = 1,
        Modification = 2,
        Deletion = 3,
        Clarification = 4
    }

    public enum ChangeStatus
    {
        Proposed = 1,
        UnderReview = 2,
        Approved = 3,
        Rejected = 4,
        Implemented = 5
    }
}
