@*
    Form Button Component - Usage Examples:

    1. Submit button:
    @{
        ViewData["Text"] = "Save";
        ViewData["Type"] = "submit";
        ViewData["Variant"] = "primary";
        ViewData["Icon"] = "fas fa-save";
    }
    <partial name="Components/_FormButton" view-data="ViewData" />

    2. Reset button:
    @{
        ViewData["Text"] = "Reset";
        ViewData["Type"] = "reset";
        ViewData["Variant"] = "secondary";
    }
    <partial name="Components/_FormButton" view-data="ViewData" />

    3. Regular button with onclick:
    @{
        ViewData["Text"] = "Cancel";
        ViewData["Type"] = "button";
        ViewData["Variant"] = "secondary";
        ViewData["OnClick"] = "window.location.href='/Risk'";
    }
    <partial name="Components/_FormButton" view-data="ViewData" />
*@

@{
    var text = ViewData["Text"]?.ToString() ?? "Button";
    var variant = ViewData["Variant"]?.ToString() ?? "primary"; // primary, secondary, outline, danger, success
    var size = ViewData["Size"]?.ToString() ?? "md"; // sm, md, lg
    var icon = ViewData["Icon"]?.ToString();
    var iconPosition = ViewData["IconPosition"]?.ToString() ?? "left"; // left, right
    var onclick = ViewData["OnClick"]?.ToString();
    var type = ViewData["Type"]?.ToString() ?? "button"; // button, submit, reset
    var disabled = ViewData["Disabled"] as bool? ?? false;
    var fullWidth = ViewData["FullWidth"] as bool? ?? false;
    var loading = ViewData["Loading"] as bool? ?? false;
    var additionalClasses = ViewData["AdditionalClasses"]?.ToString() ?? "";
    var id = ViewData["Id"]?.ToString();
    var ariaLabel = ViewData["AriaLabel"]?.ToString();
    var title = ViewData["Title"]?.ToString();
    var form = ViewData["Form"]?.ToString(); // form attribute for buttons outside the form

    var baseClasses = "inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed";

    var variantClasses = variant switch {
        "primary" => "btn-primary-custom",
        "secondary" => "btn-secondary-custom",
        "outline" => "btn-outline-custom",
        "danger" => "btn-danger-custom",
        "success" => "btn-success-custom",
        _ => "btn-primary-custom"
    };

    var sizeClasses = size switch {
        "sm" => "px-3 py-1.5 text-sm rounded-md",
        "md" => "px-4 py-2.5 text-sm rounded-lg",
        "lg" => "px-6 py-3 text-base rounded-lg",
        _ => "px-4 py-2.5 text-sm rounded-lg"
    };

    var widthClass = fullWidth ? "w-full" : "";
    var allClasses = $"{baseClasses} {variantClasses} {sizeClasses} {widthClass} {additionalClasses}".Trim();
}

<button type="@type" class="@allClasses"
        @if (!string.IsNullOrEmpty(id)) { <text>id="@id"</text> }
        @if (!string.IsNullOrEmpty(ariaLabel)) { <text>aria-label="@ariaLabel"</text> }
        @if (!string.IsNullOrEmpty(onclick)) { <text>onclick="@onclick"</text> }
        @if (!string.IsNullOrEmpty(title)) { <text>title="@title"</text> }
        @if (!string.IsNullOrEmpty(form)) { <text>form="@form"</text> }
        @if (disabled) { <text>disabled</text> }>
    
    @if (loading)
    {
        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
    }
    else if (!string.IsNullOrEmpty(icon) && iconPosition == "left")
    {
        <i class="@icon mr-2"></i>
    }

    <span>@text</span>

    @if (!string.IsNullOrEmpty(icon) && iconPosition == "right")
    {
        <i class="@icon ml-2"></i>
    }
</button>
