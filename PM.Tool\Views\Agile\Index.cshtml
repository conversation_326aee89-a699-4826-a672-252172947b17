@model IEnumerable<PM.Tool.Core.Entities.Agile.Epic>
@{
    ViewData["Title"] = "Agile Management";
    var hasProjects = ViewBag.HasProjects as bool? ?? false;
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-rocket mr-3 text-primary-600 dark:text-primary-400"></i>
                Agile Management
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Manage epics, sprints, and user stories across your projects
            </p>
        </div>
        @if (hasProjects)
        {
            <div class="mt-4 sm:mt-0">
                @{
                    ViewData["Text"] = "Select Project";
                    ViewData["Variant"] = "outline";
                    ViewData["Icon"] = "fas fa-project-diagram";
                    ViewData["Href"] = Url.Action("Index", "Projects");
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
        }
    </div>
</div>

@if (!hasProjects)
{
    <!-- Empty State -->
    <div class="text-center py-16">
        <div class="w-24 h-24 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-project-diagram text-4xl text-neutral-400 dark:text-dark-500"></i>
        </div>
        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">No Projects Found</h3>
        <p class="text-neutral-500 dark:text-dark-400 mb-8 max-w-md mx-auto">
            You need to create or be assigned to a project before you can use agile management features.
        </p>
        @{
            ViewData["Text"] = "Create Your First Project";
            ViewData["Variant"] = "primary";
            ViewData["Icon"] = "fas fa-plus";
            ViewData["Href"] = Url.Action("Create", "Projects");
        }
        <partial name="Components/_Button" view-data="ViewData" />
    </div>
}
else
{
    <!-- Info Banner -->
    <div class="bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-lg p-4 mb-8">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <i class="fas fa-info-circle text-primary-600 dark:text-primary-400 mt-0.5"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm text-primary-700 dark:text-primary-300">
                    Showing agile management overview across <span class="font-semibold">@ViewBag.ProjectCount project(s)</span>.
                    <a href="@Url.Action("Index", "Projects")" class="font-medium underline hover:no-underline">
                        Select a specific project
                    </a> for detailed agile management.
                </p>
            </div>
        </div>
    </div>

    <!-- Navigation Tabs -->
    <div class="border-b border-neutral-200 dark:border-dark-200 mb-8">
        <nav class="-mb-px flex space-x-8" aria-label="Tabs">
            <button class="tab-button active" id="epics-tab" data-target="#epics" type="button" role="tab" aria-selected="true">
                <i class="fas fa-mountain mr-2"></i>
                Epics
            </button>
            <button class="tab-button" id="sprints-tab" data-target="#sprints" type="button" role="tab" aria-selected="false">
                <i class="fas fa-clock mr-2"></i>
                Sprints
            </button>
            <button class="tab-button" id="backlog-tab" data-target="#backlog" type="button" role="tab" aria-selected="false">
                <i class="fas fa-list mr-2"></i>
                Product Backlog
            </button>
            <button class="tab-button" id="burndown-tab" data-target="#burndown" type="button" role="tab" aria-selected="false">
                <i class="fas fa-chart-line mr-2"></i>
                Analytics
            </button>
        </nav>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
        <!-- Epics Tab -->
        <div class="tab-panel active" id="epics" role="tabpanel">
            @{
                ViewData["Title"] = "Epics Overview";
                ViewData["Icon"] = "fas fa-mountain";
            }
            <partial name="Components/_Card" view-data="ViewData">
                @if (Model != null && Model.Any())
                {
                    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                        @foreach (var epic in Model)
                        {
                            <div class="epic-card bg-white dark:bg-surface-dark border border-neutral-200 dark:border-dark-200 rounded-xl p-6 hover:shadow-medium transition-all duration-300">
                                <!-- Epic Header -->
                                <div class="flex justify-between items-start mb-4">
                                    <div class="flex space-x-2">
                                        @{
                                            var priorityColor = epic.Priority.ToString().ToLower() switch {
                                                "critical" => "bg-danger-100 text-danger-700 dark:bg-danger-900 dark:text-danger-300",
                                                "high" => "bg-warning-100 text-warning-700 dark:bg-warning-900 dark:text-warning-300",
                                                "medium" => "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300",
                                                "low" => "bg-success-100 text-success-700 dark:bg-success-900 dark:text-success-300",
                                                _ => "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300"
                                            };
                                        }
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @priorityColor">
                                            @epic.Priority
                                        </span>
                                    </div>
                                    <div>
                                        @{
                                            var statusColor = epic.Status.ToString().ToLower() switch {
                                                "draft" => "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300",
                                                "ready" => "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300",
                                                "inprogress" => "bg-warning-100 text-warning-700 dark:bg-warning-900 dark:text-warning-300",
                                                "done" => "bg-success-100 text-success-700 dark:bg-success-900 dark:text-success-300",
                                                _ => "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300"
                                            };
                                        }
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @statusColor">
                                            @epic.Status
                                        </span>
                                    </div>
                                </div>

                                <!-- Epic Content -->
                                <div class="mb-4">
                                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">@epic.Title</h3>
                                    <p class="text-sm text-neutral-600 dark:text-dark-400 line-clamp-3">
                                        @(epic.Description?.Length > 100 ? epic.Description.Substring(0, 100) + "..." : epic.Description)
                                    </p>
                                </div>

                                <!-- Progress Bar -->
                                <div class="mb-4">
                                    <div class="flex justify-between items-center mb-2">
                                        <span class="text-sm font-medium text-neutral-700 dark:text-dark-300">Progress</span>
                                        <span class="text-sm text-neutral-600 dark:text-dark-400">@epic.ProgressPercentage.ToString("F1")%</span>
                                    </div>
                                    <div class="w-full bg-neutral-200 dark:bg-dark-600 rounded-full h-2">
                                        <div class="bg-primary-600 h-2 rounded-full transition-all duration-300" style="width: @epic.ProgressPercentage%"></div>
                                    </div>
                                </div>

                                <!-- Epic Footer -->
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-neutral-500 dark:text-dark-400">
                                        @(epic.UserStories?.Count() ?? 0) Stories
                                    </span>
                                    <div class="flex space-x-2">
                                        @{
                                            ViewData["Text"] = "";
                                            ViewData["Variant"] = "outline";
                                            ViewData["Size"] = "sm";
                                            ViewData["Icon"] = "fas fa-eye";
                                            ViewData["Href"] = Url.Action("Index", "Projects");
                                            ViewData["AriaLabel"] = "View Epic Details";
                                            ViewData["Title"] = "View Details";
                                        }
                                        <partial name="Components/_Button" view-data="ViewData" />
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center py-12">
                        <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-mountain text-2xl text-neutral-400 dark:text-dark-500"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">No Epics Found</h3>
                        <p class="text-neutral-500 dark:text-dark-400 mb-6 max-w-sm mx-auto">
                            Create your first epic to get started with agile project management.
                        </p>
                        @{
                            ViewData["Text"] = "Select Project to Create Epic";
                            ViewData["Variant"] = "primary";
                            ViewData["Icon"] = "fas fa-plus";
                            ViewData["Href"] = Url.Action("Index", "Projects");
                        }
                        <partial name="Components/_Button" view-data="ViewData" />
                    </div>
                }
            </partial>
        </div>

        <!-- Sprints Tab -->
        <div class="tab-panel" id="sprints" role="tabpanel">
            @{
                ViewData["Title"] = "Sprint Management";
                ViewData["Icon"] = "fas fa-clock";
            }
            <partial name="Components/_Card" view-data="ViewData">
                <div id="sprintsContainer">
                    <div class="flex items-center justify-center py-12">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                        <span class="ml-3 text-neutral-600 dark:text-dark-400">Loading sprints...</span>
                    </div>
                </div>
            </partial>
        </div>

        <!-- Backlog Tab -->
        <div class="tab-panel" id="backlog" role="tabpanel">
            @{
                ViewData["Title"] = "Product Backlog";
                ViewData["Icon"] = "fas fa-list";
            }
            <partial name="Components/_Card" view-data="ViewData">
                <div id="backlogContainer">
                    <div class="flex items-center justify-center py-12">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                        <span class="ml-3 text-neutral-600 dark:text-dark-400">Loading backlog...</span>
                    </div>
                </div>
            </partial>
        </div>

        <!-- Analytics Tab -->
        <div class="tab-panel" id="burndown" role="tabpanel">
            @{
                ViewData["Title"] = "Analytics & Burndown";
                ViewData["Icon"] = "fas fa-chart-line";
            }
            <partial name="Components/_Card" view-data="ViewData">
                <div class="bg-neutral-50 dark:bg-dark-700 rounded-lg p-8">
                    <canvas id="burndownChart" class="w-full h-96"></canvas>
                </div>
            </partial>
        </div>
    </div>
} <!-- End of hasProjects else block -->

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Tab functionality
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabPanels = document.querySelectorAll('.tab-panel');

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const targetId = this.getAttribute('data-target');

                    // Remove active class from all tabs and panels
                    tabButtons.forEach(btn => {
                        btn.classList.remove('active');
                        btn.setAttribute('aria-selected', 'false');
                    });
                    tabPanels.forEach(panel => panel.classList.remove('active'));

                    // Add active class to clicked tab and corresponding panel
                    this.classList.add('active');
                    this.setAttribute('aria-selected', 'true');
                    document.querySelector(targetId).classList.add('active');

                    // Load content based on tab
                    if (targetId === '#sprints') {
                        loadSprints();
                    } else if (targetId === '#backlog') {
                        loadBacklog();
                    } else if (targetId === '#burndown') {
                        loadBurndownChart();
                    }
                });
            });
        });

        function loadSprints() {
            const container = document.getElementById('sprintsContainer');
            container.innerHTML = `
                <div class="flex items-center justify-center py-12">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                    <span class="ml-3 text-neutral-600 dark:text-dark-400">Loading sprints...</span>
                </div>
            `;

            fetch('@Url.Action("GetSprints", "Agile")')
                .then(response => response.json())
                .then(data => {
                    if (data.html) {
                        container.innerHTML = data.html;
                    } else {
                        container.innerHTML = `
                            <div class="text-center py-12">
                                <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-clock text-2xl text-neutral-400 dark:text-dark-500"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">No Sprints Found</h3>
                                <p class="text-neutral-500 dark:text-dark-400">Select a project to view and manage sprints.</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    container.innerHTML = `
                        <div class="bg-danger-50 dark:bg-danger-900/20 border border-danger-200 dark:border-danger-800 rounded-lg p-4">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-triangle text-danger-600 dark:text-danger-400 mr-3"></i>
                                <span class="text-danger-700 dark:text-danger-300">Failed to load sprints. Please try again.</span>
                            </div>
                        </div>
                    `;
                });
        }

        function loadBacklog() {
            const container = document.getElementById('backlogContainer');
            container.innerHTML = `
                <div class="flex items-center justify-center py-12">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                    <span class="ml-3 text-neutral-600 dark:text-dark-400">Loading backlog...</span>
                </div>
            `;

            fetch('@Url.Action("GetBacklog", "Agile")')
                .then(response => response.json())
                .then(data => {
                    if (data.html) {
                        container.innerHTML = data.html;
                    } else {
                        container.innerHTML = `
                            <div class="text-center py-12">
                                <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-list text-2xl text-neutral-400 dark:text-dark-500"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">No User Stories Found</h3>
                                <p class="text-neutral-500 dark:text-dark-400">Select a project to view and manage the product backlog.</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    container.innerHTML = `
                        <div class="bg-danger-50 dark:bg-danger-900/20 border border-danger-200 dark:border-danger-800 rounded-lg p-4">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-triangle text-danger-600 dark:text-danger-400 mr-3"></i>
                                <span class="text-danger-700 dark:text-danger-300">Failed to load backlog. Please try again.</span>
                            </div>
                        </div>
                    `;
                });
        }

        function loadBurndownChart() {
            fetch('@Url.Action("GetBurndownData", "Agile")')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Failed to load burndown data:', data.error);
                        return;
                    }
                    renderBurndownChart(data);
                })
                .catch(error => {
                    console.error('Failed to load burndown data:', error);
                });
        }

        function renderBurndownChart(data) {
            const ctx = document.getElementById('burndownChart').getContext('2d');

            // Destroy existing chart if it exists
            if (window.burndownChart) {
                window.burndownChart.destroy();
            }

            window.burndownChart = new Chart(ctx, {
                type: 'line',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Sprint Burndown Chart',
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Story Points'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Sprint Days'
                            }
                        }
                    }
                }
            });
        }
    </script>
}

@section Styles {
    <style>
        /* Custom Tab Styling */
        .tab-button {
            white-space: nowrap;
            padding: 0.5rem 0.25rem;
            border-bottom: 2px solid transparent;
            font-weight: 500;
            font-size: 0.875rem;
            color: #6b7280;
            transition: all 0.2s;
            cursor: pointer;
            background: transparent;
            border-left: none;
            border-right: none;
            border-top: none;
        }

        .dark .tab-button {
            color: #9ca3af;
        }

        .tab-button:hover {
            color: #374151;
            border-bottom-color: #d1d5db;
        }

        .dark .tab-button:hover {
            color: #e5e7eb;
            border-bottom-color: #4b5563;
        }

        .tab-button.active {
            border-bottom-color: #2563eb;
            color: #2563eb;
        }

        .dark .tab-button.active {
            color: #60a5fa;
        }

        .tab-button:focus {
            outline: none;
            box-shadow: 0 0 0 2px #2563eb;
        }

        /* Tab Panel Styling */
        .tab-panel {
            display: none;
        }

        .tab-panel.active {
            display: block;
        }

        /* Epic Card Hover Effects */
        .epic-card {
            transition: all 0.3s;
        }

        .epic-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* Line Clamp Utility */
        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Chart Container */
        #burndownChart {
            width: 100%;
            height: 400px !important;
        }
    </style>
}
