version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: pmtool-postgres
    environment:
      POSTGRES_DB: PMToolDB
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - pmtool-network

  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: pmtool-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "8080:80"
    depends_on:
      - postgres
    networks:
      - pmtool-network

  pmtool-app:
    build:
      context: .
      dockerfile: PM.Tool/Dockerfile
    container_name: pmtool-app
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=PMToolDB;Username=postgres;Password=postgres;Port=5432
    ports:
      - "5000:8080"
      - "5001:8081"
    depends_on:
      - postgres
    networks:
      - pmtool-network
    volumes:
      - ./uploads:/app/uploads

volumes:
  postgres_data:

networks:
  pmtool-network:
    driver: bridge
