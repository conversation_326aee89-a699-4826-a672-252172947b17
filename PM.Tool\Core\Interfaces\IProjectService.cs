using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;

namespace PM.Tool.Core.Interfaces
{
    public interface IProjectService
    {
        Task<IEnumerable<Project>> GetAllProjectsAsync();
        Task<IEnumerable<Project>> GetUserProjectsAsync(string userId);
        Task<Project?> GetProjectByIdAsync(int id);
        Task<Project?> GetProjectWithDetailsAsync(int id);
        Task<Project> CreateProjectAsync(Project project);
        Task<Project> UpdateProjectAsync(Project project);
        Task<bool> DeleteProjectAsync(int id);
        Task<bool> AddMemberToProjectAsync(int projectId, string userId, UserRole role);
        Task<bool> RemoveMemberFromProjectAsync(int projectId, string userId);
        Task<bool> UpdateMemberRoleAsync(int projectId, string userId, UserRole role);
        Task<IEnumerable<ProjectMember>> GetProjectMembersAsync(int projectId);
        Task<bool> IsUserProjectMemberAsync(int projectId, string userId);
        Task<UserRole?> GetUserRoleInProjectAsync(int projectId, string userId);
        Task<IEnumerable<Project>> GetOverdueProjectsAsync();
        Task<IEnumerable<Project>> GetProjectsEndingSoonAsync(int days = 7);
    }
}
