using Microsoft.EntityFrameworkCore;
using Moq;
using PM.Tool.Application.Services;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;
using PM.Tool.Tests.Helpers;
using Xunit;
using FluentAssertions;

using TaskStatus = PM.Tool.Core.Enums.TaskStatus;

namespace PM.Tool.Tests.Unit.Services
{
    public class TaskServiceTests : IDisposable
    {
        private readonly ApplicationDbContext _context;
        private readonly Mock<ITaskRepository> _mockTaskRepository;
        private readonly TaskService _taskService;
        private readonly ApplicationUser _testUser;

        public TaskServiceTests()
        {
            _context = TestDbContextFactory.CreateInMemoryContext();
            _mockTaskRepository = MockHelper.CreateMockTaskRepository();
            _taskService = new TaskService(_mockTaskRepository.Object, _context);

            _testUser = new ApplicationUser
            {
                Id = "test-user-1",
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Test",
                LastName = "User",
                IsActive = true
            };
        }

        [Fact]
        public async Task GetAllTasksAsync_ReturnsAllTasks()
        {
            // Arrange
            var tasks = new List<TaskEntity>
            {
                new TaskEntity { Id = 1, Title = "Task 1", Status = TaskStatus.ToDo },
                new TaskEntity { Id = 2, Title = "Task 2", Status = TaskStatus.InProgress }
            };

            _mockTaskRepository.Setup(x => x.GetAllAsync())
                .ReturnsAsync(tasks);

            // Act
            var result = await _taskService.GetAllTasksAsync();

            // Assert
            result.Should().HaveCount(2);
            result.Should().Contain(t => t.Title == "Task 1");
            result.Should().Contain(t => t.Title == "Task 2");
        }

        [Fact]
        public async Task GetProjectTasksAsync_ReturnsProjectTasks()
        {
            // Arrange
            var projectId = 1;
            var tasks = new List<TaskEntity>
            {
                new TaskEntity { Id = 1, Title = "Project Task 1", ProjectId = projectId },
                new TaskEntity { Id = 2, Title = "Project Task 2", ProjectId = projectId }
            };

            _mockTaskRepository.Setup(x => x.GetTasksByProjectIdAsync(projectId))
                .ReturnsAsync(tasks);

            // Act
            var result = await _taskService.GetProjectTasksAsync(projectId);

            // Assert
            result.Should().HaveCount(2);
            result.Should().Contain(t => t.Title == "Project Task 1");
            result.Should().Contain(t => t.Title == "Project Task 2");
        }

        [Fact]
        public async Task GetUserTasksAsync_ReturnsUserTasks()
        {
            // Arrange
            var userId = "test-user-1";
            var tasks = new List<TaskEntity>
            {
                new TaskEntity { Id = 1, Title = "User Task 1", AssignedToUserId = userId },
                new TaskEntity { Id = 2, Title = "User Task 2", AssignedToUserId = userId }
            };

            _mockTaskRepository.Setup(x => x.GetTasksByUserIdAsync(userId))
                .ReturnsAsync(tasks);

            // Act
            var result = await _taskService.GetUserTasksAsync(userId);

            // Assert
            result.Should().HaveCount(2);
            result.Should().Contain(t => t.Title == "User Task 1");
            result.Should().Contain(t => t.Title == "User Task 2");
        }

        [Fact]
        public async Task GetTaskByIdAsync_WithValidId_ReturnsTask()
        {
            // Arrange
            var taskId = 1;
            var task = new TaskEntity { Id = taskId, Title = "Test Task" };

            _mockTaskRepository.Setup(x => x.GetByIdAsync(taskId))
                .ReturnsAsync(task);

            // Act
            var result = await _taskService.GetTaskByIdAsync(taskId);

            // Assert
            result.Should().NotBeNull();
            result.Id.Should().Be(taskId);
            result.Title.Should().Be("Test Task");
        }

        [Fact]
        public async Task GetTaskByIdAsync_WithInvalidId_ReturnsNull()
        {
            // Arrange
            var taskId = 999;
            _mockTaskRepository.Setup(x => x.GetByIdAsync(taskId))
                .ReturnsAsync((TaskEntity)null);

            // Act
            var result = await _taskService.GetTaskByIdAsync(taskId);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task GetTaskWithDetailsAsync_WithValidId_ReturnsTaskWithDetails()
        {
            // Arrange
            var taskId = 1;
            var task = new TaskEntity
            {
                Id = taskId,
                Title = "Test Task",
                SubTasks = new List<TaskEntity>()
            };

            _mockTaskRepository.Setup(x => x.GetTaskWithSubTasksAsync(taskId))
                .ReturnsAsync(task);

            // Act
            var result = await _taskService.GetTaskWithDetailsAsync(taskId);

            // Assert
            result.Should().NotBeNull();
            result.Id.Should().Be(taskId);
            result.SubTasks.Should().NotBeNull();
        }

        [Fact]
        public async Task CreateTaskAsync_WithValidTask_CreatesTask()
        {
            // Arrange
            var task = new TaskEntity
            {
                Title = "New Task",
                Description = "New Description",
                Status = TaskStatus.ToDo,
                Priority = TaskPriority.Medium,
                ProjectId = 1,
                CreatedByUserId = _testUser.Id
            };

            var createdTask = new TaskEntity
            {
                Id = 1,
                Title = task.Title,
                Description = task.Description,
                Status = task.Status,
                Priority = task.Priority,
                ProjectId = task.ProjectId,
                CreatedByUserId = task.CreatedByUserId
            };

            _mockTaskRepository.Setup(x => x.AddAsync(It.IsAny<TaskEntity>()))
                .ReturnsAsync((TaskEntity t) => {
                    t.Id = 1; // Simulate database setting the ID
                    return t;
                });
            _mockTaskRepository.Setup(x => x.SaveChangesAsync())
                .ReturnsAsync(true);

            // Act
            var result = await _taskService.CreateTaskAsync(task);

            // Assert
            result.Should().NotBeNull();
            result.Id.Should().Be(1);
            result.Title.Should().Be("New Task");
            result.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
            result.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
        }

        [Fact]
        public async Task UpdateTaskAsync_WithValidTask_UpdatesTask()
        {
            // Arrange
            var task = new TaskEntity
            {
                Id = 1,
                Title = "Updated Task",
                Description = "Updated Description",
                Status = TaskStatus.InProgress,
                Priority = TaskPriority.High
            };

            _mockTaskRepository.Setup(x => x.UpdateAsync(It.IsAny<TaskEntity>()))
                .Returns(Task.CompletedTask);
            _mockTaskRepository.Setup(x => x.SaveChangesAsync())
                .ReturnsAsync(true);

            // Act
            var result = await _taskService.UpdateTaskAsync(task);

            // Assert
            result.Should().NotBeNull();
            result.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
            _mockTaskRepository.Verify(x => x.UpdateAsync(task), Times.Once);
            _mockTaskRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        }

        [Fact]
        public async Task UpdateTaskAsync_WithCompletedStatus_SetsCompletedDate()
        {
            // Arrange
            var task = new TaskEntity
            {
                Id = 1,
                Title = "Task to Complete",
                Status = TaskStatus.Done,
                CompletedDate = null
            };

            _mockTaskRepository.Setup(x => x.UpdateAsync(It.IsAny<TaskEntity>()))
                .Returns(Task.CompletedTask);
            _mockTaskRepository.Setup(x => x.SaveChangesAsync())
                .ReturnsAsync(true);

            // Act
            var result = await _taskService.UpdateTaskAsync(task);

            // Assert
            result.CompletedDate.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
        }

        [Fact]
        public async Task DeleteTaskAsync_WithValidId_DeletesTask()
        {
            // Arrange
            var taskId = 1;
            var task = new TaskEntity { Id = taskId, Title = "Test Task" };

            _mockTaskRepository.Setup(x => x.GetByIdAsync(taskId))
                .ReturnsAsync(task);
            _mockTaskRepository.Setup(x => x.DeleteAsync(task))
                .Returns(Task.CompletedTask);
            _mockTaskRepository.Setup(x => x.SaveChangesAsync())
                .ReturnsAsync(true);

            // Act
            var result = await _taskService.DeleteTaskAsync(taskId);

            // Assert
            result.Should().BeTrue();
            _mockTaskRepository.Verify(x => x.DeleteAsync(task), Times.Once);
            _mockTaskRepository.Verify(x => x.SaveChangesAsync(), Times.Once);
        }

        [Fact]
        public async Task DeleteTaskAsync_WithInvalidId_ReturnsFalse()
        {
            // Arrange
            var taskId = 999;
            _mockTaskRepository.Setup(x => x.GetByIdAsync(taskId))
                .ReturnsAsync((TaskEntity)null);

            // Act
            var result = await _taskService.DeleteTaskAsync(taskId);

            // Assert
            result.Should().BeFalse();
            _mockTaskRepository.Verify(x => x.DeleteAsync(It.IsAny<TaskEntity>()), Times.Never);
        }

        [Fact]
        public async Task AssignTaskAsync_WithValidTaskAndUser_AssignsTask()
        {
            // Arrange
            var taskId = 1;
            var userId = "new-user-id";
            var task = new TaskEntity { Id = taskId, Title = "Test Task", AssignedToUserId = null };

            _mockTaskRepository.Setup(x => x.GetByIdAsync(taskId))
                .ReturnsAsync(task);
            _mockTaskRepository.Setup(x => x.UpdateAsync(It.IsAny<TaskEntity>()))
                .Returns(Task.CompletedTask);
            _mockTaskRepository.Setup(x => x.SaveChangesAsync())
                .ReturnsAsync(true);

            // Act
            var result = await _taskService.AssignTaskAsync(taskId, userId);

            // Assert
            result.Should().BeTrue();
            task.AssignedToUserId.Should().Be(userId);
            task.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
        }

        [Fact]
        public async Task UpdateTaskStatusAsync_WithValidTaskAndStatus_UpdatesStatus()
        {
            // Arrange
            var taskId = 1;
            var newStatus = TaskStatus.Done;
            var task = new TaskEntity { Id = taskId, Title = "Test Task", Status = TaskStatus.InProgress, CompletedDate = null };

            _mockTaskRepository.Setup(x => x.GetByIdAsync(taskId))
                .ReturnsAsync(task);
            _mockTaskRepository.Setup(x => x.UpdateAsync(It.IsAny<TaskEntity>()))
                .Returns(Task.CompletedTask);
            _mockTaskRepository.Setup(x => x.SaveChangesAsync())
                .ReturnsAsync(true);

            // Act
            var result = await _taskService.UpdateTaskStatusAsync(taskId, newStatus);

            // Assert
            result.Should().BeTrue();
            task.Status.Should().Be(newStatus);
            task.CompletedDate.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
            task.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
        }

        [Fact]
        public async Task UpdateTaskPriorityAsync_WithValidTaskAndPriority_UpdatesPriority()
        {
            // Arrange
            var taskId = 1;
            var newPriority = TaskPriority.Critical;
            var task = new TaskEntity { Id = taskId, Title = "Test Task", Priority = TaskPriority.Medium };

            _mockTaskRepository.Setup(x => x.GetByIdAsync(taskId))
                .ReturnsAsync(task);
            _mockTaskRepository.Setup(x => x.UpdateAsync(It.IsAny<TaskEntity>()))
                .Returns(Task.CompletedTask);
            _mockTaskRepository.Setup(x => x.SaveChangesAsync())
                .ReturnsAsync(true);

            // Act
            var result = await _taskService.UpdateTaskPriorityAsync(taskId, newPriority);

            // Assert
            result.Should().BeTrue();
            task.Priority.Should().Be(newPriority);
            task.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
        }

        [Fact]
        public async Task CreateSubTaskAsync_WithValidParentAndSubTask_CreatesSubTask()
        {
            // Arrange
            var parentTaskId = 1;
            var subTask = new TaskEntity
            {
                Title = "Sub Task",
                Description = "Sub Task Description",
                ProjectId = 1,
                CreatedByUserId = _testUser.Id
            };

            var createdSubTask = new TaskEntity
            {
                Id = 2,
                Title = subTask.Title,
                Description = subTask.Description,
                ProjectId = subTask.ProjectId,
                ParentTaskId = parentTaskId,
                CreatedByUserId = subTask.CreatedByUserId
            };

            _mockTaskRepository.Setup(x => x.AddAsync(It.IsAny<TaskEntity>()))
                .ReturnsAsync(createdSubTask);
            _mockTaskRepository.Setup(x => x.SaveChangesAsync())
                .ReturnsAsync(true);

            // Act
            var result = await _taskService.CreateSubTaskAsync(parentTaskId, subTask);

            // Assert
            result.Should().NotBeNull();
            result.ParentTaskId.Should().Be(parentTaskId);
            result.Title.Should().Be("Sub Task");
        }

        [Fact]
        public async Task AddCommentToTaskAsync_WithValidData_AddsComment()
        {
            // Arrange
            var taskId = 1;
            var userId = _testUser.Id;
            var content = "This is a test comment";

            // Act
            var result = await _taskService.AddCommentToTaskAsync(taskId, userId, content);

            // Assert
            result.Should().BeTrue();
            var comment = await _context.TaskComments
                .FirstOrDefaultAsync(c => c.TaskId == taskId && c.UserId == userId);
            comment.Should().NotBeNull();
            comment.Content.Should().Be(content);
            comment.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
        }

        [Fact]
        public async Task GetTaskCommentsAsync_ReturnsCommentsOrderedByDate()
        {
            // Arrange
            var taskId = 1;
            var user = new ApplicationUser { Id = "user-1", FirstName = "Test", LastName = "User" };
            _context.Users.Add(user);

            var comments = new List<TaskComment>
            {
                new TaskComment { TaskId = taskId, UserId = user.Id, Content = "First comment", CreatedAt = DateTime.UtcNow.AddDays(-2) },
                new TaskComment { TaskId = taskId, UserId = user.Id, Content = "Second comment", CreatedAt = DateTime.UtcNow.AddDays(-1) }
            };

            _context.TaskComments.AddRange(comments);
            await _context.SaveChangesAsync();

            // Act
            var result = await _taskService.GetTaskCommentsAsync(taskId);

            // Assert
            result.Should().HaveCount(2);
            result.First().Content.Should().Be("Second comment"); // Most recent first
            result.Last().Content.Should().Be("First comment");
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}
