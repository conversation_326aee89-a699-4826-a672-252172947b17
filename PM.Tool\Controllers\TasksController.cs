using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Models;
using PM.Tool.Core.Specifications;
using PM.Tool.Models.ViewModels;
using PM.Tool.Services;

using TaskStatus = PM.Tool.Core.Enums.TaskStatus;

namespace PM.Tool.Controllers
{
    [Authorize]
    public class TasksController : SecureBaseController
    {
        private readonly ITaskService _taskService;
        private readonly IProjectService _projectService;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IFormHelperService _formHelper;
        private readonly ILogger<TasksController> _logger;

        public TasksController(
            ITaskService taskService,
            IProjectService projectService,
            UserManager<ApplicationUser> userManager,
            IFormHelperService formHelper,
            IAuditService auditService,
            ILogger<TasksController> logger) : base(auditService)
        {
            _taskService = taskService;
            _projectService = projectService;
            _userManager = userManager;
            _formHelper = formHelper;
            _logger = logger;
        }

        // GET: Tasks
        public async Task<IActionResult> Index(int? projectId, TaskStatus? status, string? assignedTo)
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null) return RedirectToAction("Login", "Account");

                IEnumerable<TaskEntity> tasks;

                if (projectId.HasValue)
                {
                    // Check if user has access to this project
                    var userRole = await _projectService.GetUserRoleInProjectAsync(projectId.Value, user.Id);
                    var project = await _projectService.GetProjectByIdAsync(projectId.Value);
                    if (userRole == null && project?.CreatedByUserId != user.Id)
                    {
                        return Forbid();
                    }
                    tasks = await _taskService.GetProjectTasksAsync(projectId.Value);
                }
                else
                {
                    tasks = await _taskService.GetUserTasksAsync(user.Id);
                }

                // Apply filters
                if (status.HasValue)
                {
                    tasks = tasks.Where(t => t.Status == status.Value);
                }

                if (!string.IsNullOrEmpty(assignedTo))
                {
                    tasks = tasks.Where(t => t.AssignedToUserId == assignedTo);
                }

                var taskViewModels = tasks.Select(t => new TaskViewModel
                {
                    Id = t.Id,
                    Title = t.Title,
                    Description = t.Description,
                    Status = t.Status,
                    Priority = t.Priority,
                    ProjectId = t.ProjectId,
                    ProjectName = t.Project.Name,
                    AssignedToUserId = t.AssignedToUserId,
                    AssignedToName = t.AssignedTo?.FullName,
                    ParentTaskId = t.ParentTaskId,
                    ParentTaskTitle = t.ParentTask?.Title,
                    StartDate = t.StartDate,
                    DueDate = t.DueDate,
                    CompletedDate = t.CompletedDate,
                    EstimatedHours = t.EstimatedHours,
                    ActualHours = t.ActualHours,
                    CreatedByUserId = t.CreatedByUserId,
                    CreatedByName = t.CreatedBy.FullName,
                    CreatedAt = t.CreatedAt,
                    UpdatedAt = (DateTime)t.UpdatedAt,
                    IsOverdue = t.IsOverdue,
                    IsSubTask = t.IsSubTask,
                    SubTaskCount = t.SubTaskCount,
                    CompletedSubTasks = t.CompletedSubTasks,
                    SubTaskProgress = t.SubTaskProgress
                }).ToList();

                ViewBag.ProjectId = projectId;
                ViewBag.Status = status;
                ViewBag.AssignedTo = assignedTo;

                // Populate dropdown data for filters
                await PopulateDropdowns();

                return View(taskViewModels);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading tasks");
                return View(new List<TaskViewModel>());
            }
        }

        // GET: Tasks/Details/5
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null) return RedirectToAction("Login", "Account");

                var task = await _taskService.GetTaskWithDetailsAsync(id);
                if (task == null) return NotFound();

                // Check if user has access to this task's project
                var userRole = await _projectService.GetUserRoleInProjectAsync(task.ProjectId, user.Id);
                var project = await _projectService.GetProjectByIdAsync(task.ProjectId);
                if (userRole == null && project?.CreatedByUserId != user.Id)
                {
                    return Forbid();
                }

                var comments = await _taskService.GetTaskCommentsAsync(id);

                var viewModel = new TaskDetailsViewModel
                {
                    Id = task.Id,
                    Title = task.Title,
                    Description = task.Description,
                    Status = task.Status,
                    Priority = task.Priority,
                    ProjectId = task.ProjectId,
                    ProjectName = task.Project.Name,
                    AssignedToUserId = task.AssignedToUserId,
                    AssignedToName = task.AssignedTo?.FullName,
                    ParentTaskId = task.ParentTaskId,
                    ParentTaskTitle = task.ParentTask?.Title,
                    StartDate = task.StartDate,
                    DueDate = task.DueDate,
                    CompletedDate = task.CompletedDate,
                    EstimatedHours = task.EstimatedHours,
                    ActualHours = task.ActualHours,
                    CreatedByUserId = task.CreatedByUserId,
                    CreatedByName = task.CreatedBy.FullName,
                    CreatedAt = task.CreatedAt,
                    UpdatedAt = (DateTime)task.UpdatedAt,
                    IsOverdue = task.IsOverdue,
                    IsSubTask = task.IsSubTask,
                    SubTaskCount = task.SubTaskCount,
                    CompletedSubTasks = task.CompletedSubTasks,
                    SubTaskProgress = task.SubTaskProgress,
                    CurrentUserRole = userRole,
                    CanEdit = userRole == UserRole.ProjectManager || userRole == UserRole.Admin ||
                             task.CreatedByUserId == user.Id || task.AssignedToUserId == user.Id,
                    CanDelete = userRole == UserRole.ProjectManager || userRole == UserRole.Admin ||
                               task.CreatedByUserId == user.Id,
                    CanComment = userRole != null || project?.CreatedByUserId == user.Id,
                    SubTasks = task.SubTasks.Select(st => new TaskViewModel
                    {
                        Id = st.Id,
                        Title = st.Title,
                        Status = st.Status,
                        Priority = st.Priority,
                        AssignedToName = st.AssignedTo?.FullName,
                        DueDate = st.DueDate,
                        IsOverdue = st.IsOverdue,
                        CompletedDate = st.CompletedDate
                    }).ToList(),
                    Comments = comments.Select(c => new TaskCommentViewModel
                    {
                        Id = c.Id,
                        Content = c.Content,
                        UserName = c.User.FullName,
                        CreatedAt = c.CreatedAt,
                        UpdatedAt = c.UpdatedAt
                    }).ToList()
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading task details for task {TaskId}", id);
                return NotFound();
            }
        }

        // GET: Tasks/Create
        public async Task<IActionResult> Create(int? projectId, int? parentTaskId)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null) return RedirectToAction("Login", "Account");

            var viewModel = new TaskCreateViewModelEnhanced();

            if (projectId.HasValue)
            {
                var project = await _projectService.GetProjectByIdAsync(projectId.Value);
                if (project == null) return NotFound();

                var userRole = await _projectService.GetUserRoleInProjectAsync(projectId.Value, user.Id);
                if (userRole == null && project.CreatedByUserId != user.Id)
                {
                    return Forbid();
                }

                viewModel.ProjectId = projectId.Value;
            }

            if (parentTaskId.HasValue)
            {
                var parentTask = await _taskService.GetTaskByIdAsync(parentTaskId.Value);
                if (parentTask == null) return NotFound();

                viewModel.ParentTaskId = parentTaskId.Value;
                viewModel.ProjectId = parentTask.ProjectId;
            }

            await PopulateDropdowns();
            return View(viewModel);
        }

        // POST: Tasks/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(TaskCreateViewModelEnhanced viewModel)
        {
            return await this.HandleCreateAsync<TaskCreateViewModelEnhanced, TaskEntity>(
                viewModel,
                async (task) => {
                    var user = await _userManager.GetUserAsync(User);
                    if (user == null) throw new UnauthorizedAccessException("User not found");

                    // Check if user has access to the project
                    var userRole = await _projectService.GetUserRoleInProjectAsync(viewModel.ProjectId, user.Id);
                    var project = await _projectService.GetProjectByIdAsync(viewModel.ProjectId);
                    if (userRole == null && project?.CreatedByUserId != user.Id)
                    {
                        throw new UnauthorizedAccessException("User does not have access to this project");
                    }

                    task.CreatedByUserId = user.Id;
                    var createdTask = await _taskService.CreateTaskAsync(task);
                    await LogAuditAsync(AuditAction.Create, "Task", createdTask.Id);
                    return createdTask;
                },
                task => task.Id,
                PopulateDropdowns,
                _formHelper,
                _logger,
                "Task"
            );
        }

        // GET: Tasks/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null) return RedirectToAction("Login", "Account");

                var task = await _taskService.GetTaskByIdAsync(id);
                if (task == null) return NotFound();

                // Check permissions
                var userRole = await _projectService.GetUserRoleInProjectAsync(task.ProjectId, user.Id);
                var project = await _projectService.GetProjectByIdAsync(task.ProjectId);
                if (userRole == null && project?.CreatedByUserId != user.Id &&
                    task.CreatedByUserId != user.Id && task.AssignedToUserId != user.Id)
                {
                    return Forbid();
                }

                var viewModel = TaskEditViewModelEnhanced.FromEntity(task);
                await PopulateDropdowns();
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading task for edit {TaskId}", id);
                return NotFound();
            }
        }

        // POST: Tasks/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, TaskEditViewModelEnhanced viewModel)
        {
            if (id != viewModel.Id) return NotFound();

            return await this.HandleUpdateAsync<TaskEditViewModelEnhanced, TaskEntity>(
                id,
                viewModel,
                async (taskId) => {
                    var user = await _userManager.GetUserAsync(User);
                    if (user == null) throw new UnauthorizedAccessException("User not found");

                    var task = await _taskService.GetTaskByIdAsync(taskId);
                    if (task == null) return null;

                    // Check permissions
                    var userRole = await _projectService.GetUserRoleInProjectAsync(task.ProjectId, user.Id);
                    var project = await _projectService.GetProjectByIdAsync(task.ProjectId);
                    if (userRole == null && project?.CreatedByUserId != user.Id &&
                        task.CreatedByUserId != user.Id && task.AssignedToUserId != user.Id)
                    {
                        throw new UnauthorizedAccessException("User does not have permission to edit this task");
                    }

                    return task;
                },
                async (task) => {
                    var updatedTask = await _taskService.UpdateTaskAsync(task);
                    await LogAuditAsync(AuditAction.Update, "Task", id);
                    return updatedTask;
                },
                PopulateDropdowns,
                _formHelper,
                _logger,
                "Task"
            );
        }

        // GET: Tasks/MyTasks
        public async Task<IActionResult> MyTasks(
            TaskStatus? status = null,
            TaskPriority? priority = null,
            int? projectId = null,
            string? search = null,
            string? sortBy = "dueDate",
            string? sortOrder = "asc",
            int page = 1,
            int pageSize = 25,
            bool? isOverdue = null,
            DateTime? dueDateFrom = null,
            DateTime? dueDateTo = null)
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null) return RedirectToAction("Login", "Account");

                // Get user's tasks with comprehensive filtering
                var tasksQuery = _taskService.GetUserTasksQuery(user.Id);

                // Apply filters
                if (status.HasValue)
                    tasksQuery = tasksQuery.Where(t => t.Status == status.Value);

                if (priority.HasValue)
                    tasksQuery = tasksQuery.Where(t => t.Priority == priority.Value);

                if (projectId.HasValue)
                    tasksQuery = tasksQuery.Where(t => t.ProjectId == projectId.Value);

                if (!string.IsNullOrEmpty(search))
                    tasksQuery = tasksQuery.Where(t => t.Title.Contains(search) ||
                                                      (t.Description != null && t.Description.Contains(search)));

                if (isOverdue.HasValue && isOverdue.Value)
                    tasksQuery = tasksQuery.Where(t => t.DueDate.HasValue && t.DueDate < DateTime.UtcNow && t.Status != TaskStatus.Done);

                if (dueDateFrom.HasValue)
                    tasksQuery = tasksQuery.Where(t => t.DueDate >= dueDateFrom.Value);

                if (dueDateTo.HasValue)
                    tasksQuery = tasksQuery.Where(t => t.DueDate <= dueDateTo.Value);

                // Apply sorting
                tasksQuery = sortBy?.ToLower() switch
                {
                    "title" => sortOrder == "desc" ? tasksQuery.OrderByDescending(t => t.Title) : tasksQuery.OrderBy(t => t.Title),
                    "status" => sortOrder == "desc" ? tasksQuery.OrderByDescending(t => t.Status) : tasksQuery.OrderBy(t => t.Status),
                    "priority" => sortOrder == "desc" ? tasksQuery.OrderByDescending(t => t.Priority) : tasksQuery.OrderBy(t => t.Priority),
                    "project" => sortOrder == "desc" ? tasksQuery.OrderByDescending(t => t.Project.Name) : tasksQuery.OrderBy(t => t.Project.Name),
                    "created" => sortOrder == "desc" ? tasksQuery.OrderByDescending(t => t.CreatedAt) : tasksQuery.OrderBy(t => t.CreatedAt),
                    _ => sortOrder == "desc" ? tasksQuery.OrderByDescending(t => t.DueDate) : tasksQuery.OrderBy(t => t.DueDate)
                };

                // Get total count for pagination
                var totalTasks = await tasksQuery.CountAsync();

                // Apply pagination
                var tasks = await tasksQuery
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                // Get task statistics
                var allUserTasks = await _taskService.GetUserTasksAsync(user.Id);
                var taskStats = new MyTasksStatsViewModel
                {
                    TotalTasks = allUserTasks.Count(),
                    TodoTasks = allUserTasks.Count(t => t.Status == TaskStatus.ToDo),
                    InProgressTasks = allUserTasks.Count(t => t.Status == TaskStatus.InProgress),
                    CompletedTasks = allUserTasks.Count(t => t.Status == TaskStatus.Done),
                    OverdueTasks = allUserTasks.Count(t => t.IsOverdue),
                    HighPriorityTasks = allUserTasks.Count(t => t.Priority == TaskPriority.High || t.Priority == TaskPriority.Critical),
                    DueTodayTasks = allUserTasks.Count(t => t.DueDate.HasValue && t.DueDate.Value.Date == DateTime.Today),
                    DueThisWeekTasks = allUserTasks.Count(t => t.DueDate.HasValue &&
                                                              t.DueDate.Value.Date >= DateTime.Today &&
                                                              t.DueDate.Value.Date <= DateTime.Today.AddDays(7))
                };

                // Get user's projects for filter dropdown
                var userProjects = await _projectService.GetUserProjectsAsync(user.Id);

                var viewModel = new MyTasksViewModel
                {
                    Tasks = tasks.Select(t => new TaskViewModel
                    {
                        Id = t.Id,
                        Title = t.Title,
                        Description = t.Description,
                        Status = t.Status,
                        Priority = t.Priority,
                        ProjectId = t.ProjectId,
                        ProjectName = t.Project?.Name ?? "Unknown",
                        AssignedToUserId = t.AssignedToUserId,
                        AssignedToName = t.AssignedTo?.FullName,
                        CreatedByUserId = t.CreatedByUserId,
                        CreatedByName = t.CreatedBy?.FullName,
                        DueDate = t.DueDate,
                        StartDate = t.StartDate,
                        CompletedDate = t.CompletedDate,
                        CreatedAt = t.CreatedAt,
                        UpdatedAt = t.UpdatedAt ?? t.CreatedAt,
                        IsOverdue = t.IsOverdue,
                        EstimatedHours = t.EstimatedHours,
                        ActualHours = t.ActualHours,
                        Progress = t.Progress,
                        SubTaskCount = t.SubTaskCount,
                        CompletedSubTasks = t.CompletedSubTasks,
                        WbsCode = t.WbsCode,
                        ParentTaskId = t.ParentTaskId
                    }).ToList(),
                    Stats = taskStats,
                    CurrentFilters = new MyTasksFiltersViewModel
                    {
                        Status = status,
                        Priority = priority,
                        ProjectId = projectId,
                        Search = search,
                        SortBy = sortBy,
                        SortOrder = sortOrder,
                        IsOverdue = isOverdue,
                        DueDateFrom = dueDateFrom,
                        DueDateTo = dueDateTo
                    },
                    Pagination = new PaginationViewModel
                    {
                        CurrentPage = page,
                        PageSize = pageSize,
                        TotalItems = totalTasks,
                        TotalPages = (int)Math.Ceiling((double)totalTasks / pageSize)
                    },
                    Projects = userProjects.Select(p => new SelectListItem
                    {
                        Value = p.Id.ToString(),
                        Text = p.Name,
                        Selected = p.Id == projectId
                    }).ToList()
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading MyTasks view for user");
                TempData["Error"] = "An error occurred while loading your tasks.";
                return RedirectToAction("Index", "Home");
            }
        }

        // GET: Tasks/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null) return RedirectToAction("Login", "Account");

                var task = await _taskService.GetTaskWithDetailsAsync(id);
                if (task == null) return NotFound();

                // Check permissions - only project manager, admin, or task creator can delete
                var userRole = await _projectService.GetUserRoleInProjectAsync(task.ProjectId, user.Id);
                var project = await _projectService.GetProjectByIdAsync(task.ProjectId);
                if (userRole != UserRole.ProjectManager && userRole != UserRole.Admin &&
                    project?.CreatedByUserId != user.Id && task.CreatedByUserId != user.Id)
                {
                    return Forbid();
                }

                var viewModel = new TaskViewModel
                {
                    Id = task.Id,
                    Title = task.Title,
                    Description = task.Description,
                    Status = task.Status,
                    Priority = task.Priority,
                    ProjectId = task.ProjectId,
                    ProjectName = task.Project?.Name ?? "",
                    AssignedToUserId = task.AssignedToUserId,
                    AssignedToName = task.AssignedTo?.FullName,
                    ParentTaskId = task.ParentTaskId,
                    ParentTaskTitle = task.ParentTask?.Title,
                    StartDate = task.StartDate,
                    DueDate = task.DueDate,
                    CompletedDate = task.CompletedDate,
                    EstimatedHours = task.EstimatedHours,
                    ActualHours = task.ActualHours,
                    CreatedByUserId = task.CreatedByUserId,
                    CreatedByName = task.CreatedBy?.FullName,
                    CreatedAt = task.CreatedAt,
                    UpdatedAt = task.UpdatedAt ?? DateTime.UtcNow,
                    IsOverdue = task.IsOverdue,
                    IsSubTask = task.IsSubTask,
                    SubTaskCount = task.SubTaskCount,
                    CompletedSubTasks = task.CompletedSubTasks,
                    SubTaskProgress = task.SubTaskProgress
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading task for delete {TaskId}", id);
                return NotFound();
            }
        }

        // POST: Tasks/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null) return RedirectToAction("Login", "Account");

                var task = await _taskService.GetTaskByIdAsync(id);
                if (task == null) return NotFound();

                // Check permissions - only project manager, admin, or task creator can delete
                var userRole = await _projectService.GetUserRoleInProjectAsync(task.ProjectId, user.Id);
                var project = await _projectService.GetProjectByIdAsync(task.ProjectId);
                if (userRole != UserRole.ProjectManager && userRole != UserRole.Admin &&
                    project?.CreatedByUserId != user.Id && task.CreatedByUserId != user.Id)
                {
                    return Forbid();
                }

                // Store projectId before deletion
                var projectId = task.ProjectId;
                await _taskService.DeleteTaskAsync(id);

                TempData["SuccessMessage"] = "Task deleted successfully!";
                return RedirectToAction(nameof(Index), new { projectId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting task {TaskId}", id);
                TempData["ErrorMessage"] = "An error occurred while deleting the task. Please try again.";
                return RedirectToAction(nameof(Delete), new { id });
            }
        }

        private async Task PopulateDropdowns()
        {
            // Get all active projects for dropdown
            var projects = await _projectService.GetAllProjectsAsync();
            ViewBag.Projects = projects.Select(p => new SelectListItem
            {
                Value = p.Id.ToString(),
                Text = p.Name
            }).ToList();

            // Get all users for assignment dropdown
            var users = await _userManager.Users.Where(u => u.IsActive).ToListAsync();
            ViewBag.Users = users.Select(u => new SelectListItem
            {
                Value = u.Id,
                Text = $"{u.FirstName} {u.LastName}".Trim()
            }).ToList();

            // Get all task statuses for dropdown
            ViewBag.Statuses = Enum.GetValues<TaskStatus>().Select(s => new SelectListItem
            {
                Value = ((int)s).ToString(),
                Text = s.ToString()
            }).ToList();
        }
    }
}
