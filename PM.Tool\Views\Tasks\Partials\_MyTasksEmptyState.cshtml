@model MyTasksViewModel

<div class="card-custom">
    <div class="card-body-custom text-center py-12">
        <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-tasks text-2xl text-neutral-400 dark:text-neutral-500"></i>
        </div>
        <h3 class="text-lg font-medium text-neutral-900 dark:text-white mb-2">No tasks found</h3>
        <p class="text-neutral-500 dark:text-neutral-400 mb-6">
            @if (Model.CurrentFilters.Search != null || Model.CurrentFilters.Status != null || Model.CurrentFilters.Priority != null)
            {
                <span>No tasks match your current filters. Try adjusting your search criteria.</span>
            }
            else
            {
                <span>You don't have any assigned tasks yet. Create a new task to get started.</span>
            }
        </p>
        <div class="flex justify-center space-x-3">
            @{
                ViewData["Text"] = "Create New Task";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-plus";
                ViewData["Href"] = Url.Action("Create");
            }
            <partial name="Components/_Button" view-data="ViewData" />
            
            @if (Model.CurrentFilters.Search != null || Model.CurrentFilters.Status != null || Model.CurrentFilters.Priority != null)
            {
                ViewData["Text"] = "Clear Filters";
                ViewData["Variant"] = "outline";
                ViewData["Icon"] = "fas fa-times";
                ViewData["Href"] = Url.Action("MyTasks");
                <partial name="Components/_Button" view-data="ViewData" />
            }
        </div>
    </div>
</div>
