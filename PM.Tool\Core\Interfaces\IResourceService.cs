using PM.Tool.Core.Entities;

namespace PM.Tool.Core.Interfaces
{
    public interface IResourceService
    {
        // Resource Management
        Task<IEnumerable<Resource>> GetAllResourcesAsync();
        Task<IEnumerable<Resource>> GetActiveResourcesAsync();
        Task<Resource?> GetResourceByIdAsync(int id);
        Task<Resource> CreateResourceAsync(Resource resource);
        Task<Resource> UpdateResourceAsync(Resource resource);
        Task<bool> DeleteResourceAsync(int id);
        Task<bool> DeactivateResourceAsync(int id);
        
        // Resource Allocation
        Task<IEnumerable<ResourceAllocation>> GetResourceAllocationsAsync(int resourceId);
        Task<IEnumerable<ResourceAllocation>> GetProjectResourceAllocationsAsync(int projectId);
        Task<ResourceAllocation> CreateAllocationAsync(ResourceAllocation allocation);
        Task<ResourceAllocation> UpdateAllocationAsync(ResourceAllocation allocation);
        Task<bool> DeleteAllocationAsync(int id);
        
        // Resource Availability
        Task<bool> IsResourceAvailableAsync(int resourceId, DateTime startDate, DateTime endDate, decimal requiredHours);
        Task<IEnumerable<Resource>> GetAvailableResourcesAsync(DateTime startDate, DateTime endDate, decimal requiredHours);
        Task<decimal> GetResourceUtilizationAsync(int resourceId, DateTime startDate, DateTime endDate);
        Task<Dictionary<int, decimal>> GetTeamUtilizationAsync(IEnumerable<int> resourceIds, DateTime startDate, DateTime endDate);
        
        // Skills Management
        Task<IEnumerable<Skill>> GetAllSkillsAsync();
        Task<Skill> CreateSkillAsync(Skill skill);
        Task<IEnumerable<Resource>> GetResourcesBySkillAsync(int skillId, SkillLevel? minimumLevel = null);
        Task<bool> AssignSkillToResourceAsync(int resourceId, int skillId, SkillLevel level, int yearsOfExperience);
        
        // Resource Recommendations
        Task<IEnumerable<Resource>> RecommendResourcesForTaskAsync(int taskId);
        Task<IEnumerable<Resource>> GetResourcesByCapacityAsync(decimal minCapacity, decimal maxCapacity);
    }
}
