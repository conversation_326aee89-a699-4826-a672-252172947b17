@model PM.Tool.Models.ViewModels.PersonEditViewModel
@using PM.Tool.Core.Entities

@{
    ViewData["Title"] = "Edit Person";
    ViewData["PageTitle"] = $"Edit {Model.FirstName} {Model.LastName}";
    ViewData["PageDescription"] = "Update person information and settings.";
}

@section Styles {
    <style>
        .form-section {
            transition: all 0.3s ease-in-out;
        }
        .form-group {
            margin-bottom: 1.5rem;
        }
        .required-field::after {
            content: " *";
            color: #ef4444;
        }
    </style>
}

<!-- Page Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8">
    <div>
        <h1 class="text-3xl font-bold text-neutral-900 dark:text-white mb-2">@ViewData["PageTitle"]</h1>
        <p class="text-neutral-600 dark:text-dark-400">@ViewData["PageDescription"]</p>
        <div class="flex items-center space-x-2 mt-2">
            <span class="text-sm text-neutral-500 dark:text-dark-500">Person Code:</span>
            <span class="text-sm font-medium text-neutral-900 dark:text-white">@Model.PersonCode</span>
        </div>
    </div>
    <div class="flex space-x-3 mt-4 lg:mt-0">
        <a href="@Url.Action("Details", new { id = Model.Id })" class="btn-secondary">
            <i class="fas fa-eye mr-2"></i>
            View Details
        </a>
        <a href="@Url.Action("Index")" class="btn-secondary">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to People
        </a>
    </div>
</div>

<form asp-action="Edit" method="post" id="editPersonForm">
    <div asp-validation-summary="ModelOnly" class="text-danger mb-4"></div>
    
    <input type="hidden" asp-for="Id" />
    <input type="hidden" asp-for="PersonCode" />

    <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
        <!-- Main Form Content -->
        <div class="xl:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
                <h2 class="text-xl font-semibold text-neutral-900 dark:text-white mb-6">Basic Information</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="form-group">
                        <label asp-for="FirstName" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1 required-field">First Name</label>
                        <input asp-for="FirstName" class="form-input" required />
                        <span asp-validation-for="FirstName" class="text-danger text-sm"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="LastName" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1 required-field">Last Name</label>
                        <input asp-for="LastName" class="form-input" required />
                        <span asp-validation-for="LastName" class="text-danger text-sm"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Email" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1 required-field">Email Address</label>
                        <input asp-for="Email" type="email" class="form-input" required />
                        <span asp-validation-for="Email" class="text-danger text-sm"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Phone" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Phone Number</label>
                        <input asp-for="Phone" type="tel" class="form-input" />
                        <span asp-validation-for="Phone" class="text-danger text-sm"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Title" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Job Title</label>
                        <input asp-for="Title" class="form-input" />
                        <span asp-validation-for="Title" class="text-danger text-sm"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Department" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Department</label>
                        <input asp-for="Department" class="form-input" />
                        <span asp-validation-for="Department" class="text-danger text-sm"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Organization" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Organization</label>
                        <input asp-for="Organization" class="form-input" />
                        <span asp-validation-for="Organization" class="text-danger text-sm"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Location" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Location</label>
                        <input asp-for="Location" class="form-input" />
                        <span asp-validation-for="Location" class="text-danger text-sm"></span>
                    </div>
                </div>

                <!-- Employee ID (for Internal type only) -->
                <div id="employeeIdSection" class="form-group @(Model.Type != PersonType.Internal ? "hidden" : "")">
                    <label asp-for="EmployeeId" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Employee ID</label>
                    <input asp-for="EmployeeId" class="form-input" />
                    <span asp-validation-for="EmployeeId" class="text-danger text-sm"></span>
                </div>
            </div>

            <!-- Professional Details -->
            <div class="bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
                <h2 class="text-xl font-semibold text-neutral-900 dark:text-white mb-6">Professional Details</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="form-group">
                        <label asp-for="WorkingHours" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Working Hours</label>
                        <input asp-for="WorkingHours" class="form-input" placeholder="e.g., 9:00 AM - 5:00 PM" />
                        <span asp-validation-for="WorkingHours" class="text-danger text-sm"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="TimeZone" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Time Zone</label>
                        <input asp-for="TimeZone" class="form-input" placeholder="e.g., UTC-5, EST" />
                        <span asp-validation-for="TimeZone" class="text-danger text-sm"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="HireDate" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Hire Date</label>
                        <input asp-for="HireDate" type="date" class="form-input" />
                        <span asp-validation-for="HireDate" class="text-danger text-sm"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="TerminationDate" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Termination Date</label>
                        <input asp-for="TerminationDate" type="date" class="form-input" />
                        <span asp-validation-for="TerminationDate" class="text-danger text-sm"></span>
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
                <h2 class="text-xl font-semibold text-neutral-900 dark:text-white mb-6">Additional Information</h2>
                
                <div class="space-y-6">
                    <div class="form-group">
                        <label asp-for="Bio" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Bio</label>
                        <textarea asp-for="Bio" rows="4" class="form-textarea" placeholder="Brief biography or description..."></textarea>
                        <span asp-validation-for="Bio" class="text-danger text-sm"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="CommunicationPreferences" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Communication Preferences</label>
                        <textarea asp-for="CommunicationPreferences" rows="3" class="form-textarea" placeholder="Preferred communication methods, times, frequency, etc."></textarea>
                        <span asp-validation-for="CommunicationPreferences" class="text-danger text-sm"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Notes" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Notes</label>
                        <textarea asp-for="Notes" rows="4" class="form-textarea" placeholder="Additional notes about this person..."></textarea>
                        <span asp-validation-for="Notes" class="text-danger text-sm"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Type & Status -->
            <div class="bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-white mb-4">Type & Status</h3>
                
                <div class="space-y-4">
                    <div class="form-group">
                        <label asp-for="Type" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Person Type</label>
                        <select asp-for="Type" class="form-select" id="personType">
                            @foreach (PersonType type in Enum.GetValues<PersonType>())
                            {
                                <option value="@type">@type</option>
                            }
                        </select>
                        <span asp-validation-for="Type" class="text-danger text-sm"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Status" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Status</label>
                        <select asp-for="Status" class="form-select">
                            @foreach (PersonStatus status in Enum.GetValues<PersonStatus>())
                            {
                                <option value="@status">@status</option>
                            }
                        </select>
                        <span asp-validation-for="Status" class="text-danger text-sm"></span>
                    </div>
                </div>
            </div>

            <!-- System Access -->
            <div class="bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-white mb-4">System Access</h3>
                
                <div class="space-y-4">
                    <div class="flex items-center">
                        <input asp-for="HasSystemAccess" type="checkbox" class="rounded border-neutral-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50" />
                        <label asp-for="HasSystemAccess" class="ml-2 text-sm text-neutral-700 dark:text-dark-300">Grant system access</label>
                    </div>
                    <p class="text-sm text-neutral-500 dark:text-dark-500">Allow this person to log into the system</p>
                    
                    <div class="form-group">
                        <label asp-for="UserId" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Linked User Account</label>
                        <input asp-for="UserId" class="form-input" placeholder="User ID if linked to system account" />
                        <span asp-validation-for="UserId" class="text-danger text-sm"></span>
                    </div>
                </div>
            </div>

            <!-- Profile Picture -->
            <div class="bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-white mb-4">Profile Picture</h3>
                
                <div class="form-group">
                    <label asp-for="ProfilePictureUrl" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-1">Picture URL</label>
                    <input asp-for="ProfilePictureUrl" type="url" class="form-input" placeholder="https://..." />
                    <span asp-validation-for="ProfilePictureUrl" class="text-danger text-sm"></span>
                </div>
                
                @if (!string.IsNullOrEmpty(Model.ProfilePictureUrl))
                {
                    <div class="mt-4">
                        <img src="@Model.ProfilePictureUrl" alt="Profile Picture" class="w-20 h-20 rounded-lg object-cover" />
                    </div>
                }
            </div>

            <!-- Form Actions -->
            <div class="bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6">
                @{
                    ViewData["SubmitText"] = "Update Person";
                    ViewData["SubmitIcon"] = "fas fa-save";
                    ViewData["CancelUrl"] = Url.Action("Details", new { id = Model.Id });
                    ViewData["ShowCancel"] = true;
                    ViewData["SubmitClass"] = "btn-primary";
                    ViewData["CancelClass"] = "btn-secondary";
                }
                <partial name="Components/_FormActions" view-data="ViewData" />
            </div>
        </div>
    </div>
</form>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        $(document).ready(function() {
            // Show/hide employee ID section based on person type
            $('#personType').on('change', function() {
                const selectedType = $(this).val();
                if (selectedType === 'Internal') {
                    $('#employeeIdSection').removeClass('hidden');
                } else {
                    $('#employeeIdSection').addClass('hidden');
                }
            });

            // Form validation
            $('#editPersonForm').on('submit', function(e) {
                const firstName = $('#FirstName').val().trim();
                const lastName = $('#LastName').val().trim();
                const email = $('#Email').val().trim();

                if (!firstName || !lastName || !email) {
                    e.preventDefault();
                    alert('Please fill in all required fields (First Name, Last Name, Email)');
                    return false;
                }

                // Basic email validation
                const emailRegex = /^[^\s@@]+@@[^\s@@]+\.[^\s@@]+$/;
                if (!emailRegex.test(email)) {
                    e.preventDefault();
                    alert('Please enter a valid email address');
                    return false;
                }

                return true;
            });
        });
    </script>
}
