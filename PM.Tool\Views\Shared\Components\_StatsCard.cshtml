@{
    var title = ViewData["Title"]?.ToString() ?? "";
    var icon = ViewData["Icon"]?.ToString() ?? "fas fa-chart-line";
    var iconColor = ViewData["IconColor"]?.ToString() ?? "bg-gradient-to-br from-primary-500 to-primary-600";
    var value = ViewData["Value"]?.ToString() ?? "0";
    var description = ViewData["Description"]?.ToString() ?? "";
    var trend = ViewData["Trend"]?.ToString() ?? "";
    var trendIcon = ViewData["TrendIcon"]?.ToString() ?? "";
    var trendColor = ViewData["TrendColor"]?.ToString() ?? "text-neutral-500 dark:text-dark-400";
    var progress = ViewData["Progress"] as decimal?;
    var href = ViewData["Href"]?.ToString();
    var clickable = !string.IsNullOrEmpty(href);
}

@if (clickable)
{
    <a href="@href" class="block bg-white dark:bg-surface-dark border border-neutral-200 dark:border-dark-200 rounded-xl p-6 hover:shadow-lg hover:border-primary-300 dark:hover:border-primary-600 transition-all duration-200 group">
        <div class="flex items-center justify-between">
            <div class="flex-1">
                <div class="flex items-center space-x-4">
                    <!-- Icon -->
                    <div class="w-12 h-12 @iconColor rounded-xl flex items-center justify-center flex-shrink-0">
                        <i class="@icon text-white text-lg"></i>
                    </div>

                    <!-- Content -->
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-1">@title</p>
                        <p class="text-2xl font-bold text-neutral-900 dark:text-dark-100 mb-1">@value</p>
                        @if (!string.IsNullOrEmpty(description))
                        {
                            <p class="text-xs text-neutral-500 dark:text-dark-400">@description</p>
                        }
                    </div>
                </div>
            </div>

            <!-- Trend Indicator -->
            @if (!string.IsNullOrEmpty(trend))
            {
                <div class="text-right">
                    <div class="flex items-center space-x-1 @trendColor">
                        @if (!string.IsNullOrEmpty(trendIcon))
                        {
                            <i class="@trendIcon text-xs"></i>
                        }
                        <span class="text-xs font-medium">@trend</span>
                    </div>
                </div>
            }
        </div>

        <!-- Progress Bar (if provided) -->
        @if (progress.HasValue)
        {
            <div class="mt-4">
                @{
                    var progressPercentage = Math.Max(0, Math.Min(100, progress.Value));
                    var progressColor = progressPercentage >= 75 ? "bg-success-500" :
                                      progressPercentage >= 50 ? "bg-primary-500" :
                                      progressPercentage >= 25 ? "bg-warning-500" : "bg-danger-500";
                }
                <div class="flex items-center justify-between text-xs text-neutral-500 dark:text-dark-400 mb-2">
                    <span>Progress</span>
                    <span>@progressPercentage.ToString("F0")%</span>
                </div>
                <div class="w-full bg-neutral-200 dark:bg-dark-600 rounded-full h-2">
                    <div class="@progressColor h-2 rounded-full transition-all duration-500 ease-out"
                         style="width: @progressPercentage%"></div>
                </div>
            </div>
        }
    </a>
}
else
{
    <div class="bg-white dark:bg-surface-dark border border-neutral-200 dark:border-dark-200 rounded-xl p-6 hover:shadow-lg transition-shadow duration-200">
        <div class="flex items-center justify-between">
            <div class="flex-1">
                <div class="flex items-center space-x-4">
                    <!-- Icon -->
                    <div class="w-12 h-12 @iconColor rounded-xl flex items-center justify-center flex-shrink-0">
                        <i class="@icon text-white text-lg"></i>
                    </div>

                    <!-- Content -->
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-1">@title</p>
                        <p class="text-2xl font-bold text-neutral-900 dark:text-dark-100 mb-1">@value</p>
                        @if (!string.IsNullOrEmpty(description))
                        {
                            <p class="text-xs text-neutral-500 dark:text-dark-400">@description</p>
                        }
                    </div>
                </div>
            </div>

            <!-- Trend Indicator -->
            @if (!string.IsNullOrEmpty(trend))
            {
                <div class="text-right">
                    <div class="flex items-center space-x-1 @trendColor">
                        @if (!string.IsNullOrEmpty(trendIcon))
                        {
                            <i class="@trendIcon text-xs"></i>
                        }
                        <span class="text-xs font-medium">@trend</span>
                    </div>
                </div>
            }
        </div>

        <!-- Progress Bar (if provided) -->
        @if (progress.HasValue)
        {
            <div class="mt-4">
                @{
                    var progressPercentage = Math.Max(0, Math.Min(100, progress.Value));
                    var progressColor = progressPercentage >= 75 ? "bg-success-500" :
                                      progressPercentage >= 50 ? "bg-primary-500" :
                                      progressPercentage >= 25 ? "bg-warning-500" : "bg-danger-500";
                }
                <div class="flex items-center justify-between text-xs text-neutral-500 dark:text-dark-400 mb-2">
                    <span>Progress</span>
                    <span>@progressPercentage.ToString("F0")%</span>
                </div>
                <div class="w-full bg-neutral-200 dark:bg-dark-600 rounded-full h-2">
                    <div class="@progressColor h-2 rounded-full transition-all duration-500 ease-out"
                         style="width: @progressPercentage%"></div>
                </div>
            </div>
        }
    </div>
}
