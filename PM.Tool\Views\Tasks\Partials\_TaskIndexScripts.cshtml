@* Task Index Scripts *@
<script>
    $(document).ready(function () {
        // Initialize task index functionality
        initializeTaskIndex();
    });

    function initializeTaskIndex() {
        // Initialize DataTables
        initializeDataTables();
        
        // Initialize filter handling
        initializeFilterHandling();
        
        // Initialize UI animations
        initializeUIAnimations();
    }

    function initializeDataTables() {
        // Initialize DataTables for enhanced table functionality
        if (typeof $.fn.DataTable !== 'undefined') {
            $('#tasksTable').DataTable({
                responsive: true,
                pageLength: 25,
                order: [[5, 'asc']], // Sort by due date ascending
                columnDefs: [
                    { orderable: false, targets: [6] } // Disable sorting for actions column
                ],
                language: {
                    search: "Search tasks:",
                    lengthMenu: "Show _MENU_ tasks per page",
                    info: "Showing _START_ to _END_ of _TOTAL_ tasks",
                    paginate: {
                        first: "First",
                        last: "Last",
                        next: "Next",
                        previous: "Previous"
                    }
                },
                // Custom styling for dark mode compatibility
                initComplete: function() {
                    // Apply Tailwind classes to DataTables elements
                    $('.dataTables_wrapper .dataTables_length select').addClass('form-select-custom');
                    $('.dataTables_wrapper .dataTables_filter input').addClass('form-input-custom');
                    $('.dataTables_wrapper .dataTables_paginate .paginate_button').addClass('px-3 py-2 text-sm');
                }
            });
        }
    }

    function initializeFilterHandling() {
        // Select2 is handled by the global select2-init.js file
        // No need for additional initialization here

        // Handle filter changes with debouncing
        let filterTimeout;
        $('select').change(function () {
            clearTimeout(filterTimeout);
            filterTimeout = setTimeout(() => {
                $(this).closest('form').submit();
            }, 300);
        });
    }

    function initializeUIAnimations() {
        // Add smooth animations to table rows
        setTimeout(function() {
            document.querySelectorAll('tbody tr').forEach(function(row, index) {
                row.style.opacity = '0';
                row.style.transform = 'translateY(10px)';
                setTimeout(function() {
                    row.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                    row.style.opacity = '1';
                    row.style.transform = 'translateY(0)';
                }, index * 50);
            });
        }, 100);
    }
</script>
