using System.ComponentModel.DataAnnotations;

using PM.Tool.Core.Entities;

namespace PM.Tool.Models.ViewModels
{
    public class ResourceCreateViewModel : BaseCreateViewModel, IEntityViewModel<Resource>
    {
        [Required]
        [MaxLength(200)]
        [Display(Name = "Resource Name")]
        public string Name { get; set; } = string.Empty;

        [MaxLength(1000)]
        [Display(Name = "Description")]
        public string? Description { get; set; }

        [Required]
        [Display(Name = "Resource Type")]
        public ResourceType Type { get; set; } = ResourceType.Human;

        [MaxLength(100)]
        [Display(Name = "Department")]
        public string? Department { get; set; }

        [MaxLength(100)]
        [Display(Name = "Location")]
        public string? Location { get; set; }

        [Required]
        [Display(Name = "Hourly Rate")]
        [DataType(DataType.Currency)]
        [Range(0, double.MaxValue, ErrorMessage = "Hourly rate must be a positive number")]
        public decimal HourlyRate { get; set; }

        [Required]
        [Display(Name = "Daily Capacity (Hours)")]
        [Range(0.5, 24, ErrorMessage = "Capacity must be between 0.5 and 24 hours")]
        public decimal Capacity { get; set; } = 8;

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        [MaxLength(500)]
        [Display(Name = "Skills")]
        public string? Skills { get; set; }

        [MaxLength(100)]
        [Display(Name = "Email")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        public string? Email { get; set; }

        [MaxLength(20)]
        [Display(Name = "Phone")]
        [Phone(ErrorMessage = "Please enter a valid phone number")]
        public string? Phone { get; set; }

        [Display(Name = "Linked User")]
        public string? UserId { get; set; }

        public Resource ToEntity()
        {
            return new Resource
            {
                Name = Name,
                Description = Description,
                Type = Type,
                Department = Department,
                Location = Location,
                HourlyRate = HourlyRate,
                Capacity = Capacity,
                IsActive = IsActive,
                Skills = Skills,
                Email = Email,
                Phone = Phone,
                UserId = UserId
            };
        }

        public void UpdateEntity(Resource entity)
        {
            entity.Name = Name;
            entity.Description = Description;
            entity.Type = Type;
            entity.Department = Department;
            entity.Location = Location;
            entity.HourlyRate = HourlyRate;
            entity.Capacity = Capacity;
            entity.IsActive = IsActive;
            entity.Skills = Skills;
            entity.Email = Email;
            entity.Phone = Phone;
            entity.UserId = UserId;
        }
    }

    public class ResourceEditViewModel : ResourceCreateViewModel
    {
        public int Id { get; set; }

        public static ResourceEditViewModel FromEntity(Resource resource)
        {
            return new ResourceEditViewModel
            {
                Id = resource.Id,
                Name = resource.Name,
                Description = resource.Description,
                Type = resource.Type,
                Department = resource.Department,
                Location = resource.Location,
                HourlyRate = resource.HourlyRate,
                Capacity = resource.Capacity,
                IsActive = resource.IsActive,
                Skills = resource.Skills,
                Email = resource.Email,
                Phone = resource.Phone,
                UserId = resource.UserId
            };
        }
    }
}
