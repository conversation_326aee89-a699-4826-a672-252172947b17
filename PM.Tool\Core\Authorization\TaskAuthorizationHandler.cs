using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Constants;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Data;
using System.Security.Claims;

namespace PM.Tool.Core.Authorization
{
    public class TaskOperationRequirement : IAuthorizationRequirement
    {
        public TaskOperation Operation { get; }

        public TaskOperationRequirement(TaskOperation operation)
        {
            Operation = operation;
        }
    }

    public enum TaskOperation
    {
        View,
        Create,
        Edit,
        Update,
        Delete,
        AssignUser,
        ChangeStatus,
        UpdateStatus
    }

    public class TaskAuthorizationHandler : AuthorizationHandler<TaskOperationRequirement, TaskEntity>
    {
        private readonly ApplicationDbContext _context;

        public TaskAuthorizationHandler(ApplicationDbContext context)
        {
            _context = context;
        }

        protected override async Task HandleRequirementAsync(
            AuthorizationHandlerContext context,
            TaskOperationRequirement requirement,
            TaskEntity resource)
        {
            var user = context.User;
            if (!user.Identity?.IsAuthenticated ?? true)
            {
                return;
            }

            var userId = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (userId == null)
            {
                return;
            }

            // Admin can do anything
            if (user.IsInRole(Roles.Admin))
            {
                context.Succeed(requirement);
                return;
            }

            // Task Creator has full access
            if (resource.CreatedByUserId == userId)
            {
                context.Succeed(requirement);
                return;
            }

            // Check project membership
            var membership = await _context.ProjectMembers
                .FirstOrDefaultAsync(m => m.ProjectId == resource.ProjectId && m.UserId == userId && m.IsActive);

            if (membership != null)
            {
                switch (requirement.Operation)
                {
                    case TaskOperation.View:
                        context.Succeed(requirement);
                        break;

                    case TaskOperation.Create:
                        if (membership.IsActive)
                        {
                            context.Succeed(requirement);
                        }
                        break;

                    case TaskOperation.Update:
                    case TaskOperation.Edit:
                    case TaskOperation.Delete:
                    case TaskOperation.AssignUser:
                        if (membership.IsProjectManager || resource.CreatedByUserId == userId)
                        {
                            context.Succeed(requirement);
                        }
                        break;

                    case TaskOperation.UpdateStatus:
                    case TaskOperation.ChangeStatus:
                        if (membership.IsProjectManager || 
                            resource.CreatedByUserId == userId || 
                            resource.AssignedToUserId == userId)
                        {
                            context.Succeed(requirement);
                        }
                        break;
                }
            }
        }
    }
}
