@model PM.Tool.Models.ViewModels.StakeholderCreateViewModel
@using PM.Tool.Core.Entities

@{
    ViewData["Title"] = "Create Stakeholder";
    ViewData["PageTitle"] = "Create New Stakeholder";
    ViewData["PageDescription"] = "Add a new stakeholder to the system and define their role, influence, and interest levels.";
}

<!-- Page Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8">
    <div>
        <h1 class="text-3xl font-bold text-neutral-900 dark:text-white mb-2">@ViewData["PageTitle"]</h1>
        <p class="text-neutral-600 dark:text-neutral-400">@ViewData["PageDescription"]</p>
    </div>
    <div class="flex space-x-3 mt-4 lg:mt-0">
        <a href="@Url.Action("Index")" class="btn-secondary-custom">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Stakeholders
        </a>
    </div>
</div>

<form asp-action="Create" method="post" class="space-y-8">
    <div asp-validation-summary="ModelOnly" class="alert-danger-custom mb-6"></div>

    <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
        <!-- Main Form Content -->
        <div class="xl:col-span-2 space-y-8">
            <!-- Basic Information -->
            <div class="card-custom">
                <div class="card-header-custom">
                    <h2 class="text-xl font-semibold text-neutral-900 dark:text-white">Basic Information</h2>
                    <p class="text-sm text-neutral-600 dark:text-neutral-400 mt-1">Enter the stakeholder's contact details</p>
                </div>
                <div class="card-body-custom">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Name -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.Name).ToString();
                            ViewData["Name"] = "Name";
                            ViewData["Type"] = "text";
                            ViewData["Required"] = true;
                            ViewData["Icon"] = "fas fa-user";
                            ViewData["Placeholder"] = "Enter stakeholder name";
                            ViewData["ErrorMessage"] = "";
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />
                        <span asp-validation-for="Name" class="text-sm text-danger-600 dark:text-danger-400"></span>

                        <!-- Email -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.Email).ToString();
                            ViewData["Name"] = "Email";
                            ViewData["Type"] = "email";
                            ViewData["Required"] = true;
                            ViewData["Icon"] = "fas fa-envelope";
                            ViewData["Placeholder"] = "<EMAIL>";
                            ViewData["ErrorMessage"] = "";
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />
                        <span asp-validation-for="Email" class="text-sm text-danger-600 dark:text-danger-400"></span>

                        <!-- Phone -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.Phone).ToString();
                            ViewData["Name"] = "Phone";
                            ViewData["Type"] = "tel";
                            ViewData["Required"] = false;
                            ViewData["Icon"] = "fas fa-phone";
                            ViewData["Placeholder"] = "+****************";
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Phone");
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />

                        <!-- Title -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.Title).ToString();
                            ViewData["Name"] = "Title";
                            ViewData["Type"] = "text";
                            ViewData["Required"] = false;
                            ViewData["Icon"] = "fas fa-briefcase";
                            ViewData["Placeholder"] = "Job title or position";
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Title");
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />

                        <!-- Organization -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.Organization).ToString();
                            ViewData["Name"] = "Organization";
                            ViewData["Type"] = "text";
                            ViewData["Required"] = false;
                            ViewData["Icon"] = "fas fa-building";
                            ViewData["Placeholder"] = "Company or organization";
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Organization");
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />

                        <!-- Department -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.Department).ToString();
                            ViewData["Name"] = "Department";
                            ViewData["Type"] = "text";
                            ViewData["Required"] = false;
                            ViewData["Icon"] = "fas fa-sitemap";
                            ViewData["Placeholder"] = "Department or division";
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Department");
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />
                    </div>
                </div>
            </div>

            <!-- Stakeholder Analysis -->
            <div class="card-custom">
                <div class="card-header-custom">
                    <h2 class="text-xl font-semibold text-neutral-900 dark:text-white">Stakeholder Analysis</h2>
                    <p class="text-sm text-neutral-600 dark:text-neutral-400 mt-1">Define influence and interest levels for prioritization</p>
                </div>
                <div class="card-body-custom">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Influence Level -->
                        @{
                            var influenceOptions = "";
                            foreach (InfluenceLevel level in Enum.GetValues<InfluenceLevel>())
                            {
                                influenceOptions += $"<option value=\"{level}\">{level}</option>";
                            }

                            ViewData["Label"] = Html.DisplayNameFor(m => m.Influence).ToString();
                            ViewData["Name"] = "Influence";
                            ViewData["Type"] = "select";
                            ViewData["Required"] = true;
                            ViewData["Icon"] = "fas fa-chart-line";
                            ViewData["Options"] = "<option value=\"\">Select influence level</option>" + influenceOptions;
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Influence");
                            ViewData["HelpText"] = "How much power does this stakeholder have to influence the project?";
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />

                        <!-- Interest Level -->
                        @{
                            var interestOptions = "";
                            foreach (InterestLevel level in Enum.GetValues<InterestLevel>())
                            {
                                interestOptions += $"<option value=\"{level}\">{level}</option>";
                            }

                            ViewData["Label"] = Html.DisplayNameFor(m => m.Interest).ToString();
                            ViewData["Name"] = "Interest";
                            ViewData["Type"] = "select";
                            ViewData["Required"] = true;
                            ViewData["Icon"] = "fas fa-eye";
                            ViewData["Options"] = "<option value=\"\">Select interest level</option>" + interestOptions;
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Interest");
                            ViewData["HelpText"] = "How interested is this stakeholder in the project outcome?";
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />
                    </div>

                    <!-- Priority Matrix Guide -->
                    <div class="mt-6 p-4 bg-neutral-50 dark:bg-neutral-800 rounded-lg">
                        <h4 class="font-medium text-neutral-900 dark:text-white mb-3">Stakeholder Priority Matrix</h4>
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div class="text-center p-3 bg-red-100 dark:bg-red-900 rounded">
                                <div class="font-medium text-red-800 dark:text-red-200">High Influence + High Interest</div>
                                <div class="text-red-600 dark:text-red-400">Manage Closely</div>
                            </div>
                            <div class="text-center p-3 bg-yellow-100 dark:bg-yellow-900 rounded">
                                <div class="font-medium text-yellow-800 dark:text-yellow-200">High Influence + Low Interest</div>
                                <div class="text-yellow-600 dark:text-yellow-400">Keep Satisfied</div>
                            </div>
                            <div class="text-center p-3 bg-blue-100 dark:bg-blue-900 rounded">
                                <div class="font-medium text-blue-800 dark:text-blue-200">Low Influence + High Interest</div>
                                <div class="text-blue-600 dark:text-blue-400">Keep Informed</div>
                            </div>
                            <div class="text-center p-3 bg-neutral-100 dark:bg-neutral-800 rounded">
                                <div class="font-medium text-neutral-800 dark:text-neutral-200">Low Influence + Low Interest</div>
                                <div class="text-neutral-600 dark:text-neutral-400">Monitor</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Communication & Notes -->
            <div class="card-custom">
                <div class="card-header-custom">
                    <h2 class="text-xl font-semibold text-neutral-900 dark:text-white">Communication & Notes</h2>
                    <p class="text-sm text-neutral-600 dark:text-neutral-400 mt-1">Additional details and preferences</p>
                </div>
                <div class="card-body-custom">
                    <div class="space-y-6">
                        <!-- Communication Preferences -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.CommunicationPreferences).ToString();
                            ViewData["Name"] = "CommunicationPreferences";
                            ViewData["Type"] = "textarea";
                            ViewData["Required"] = false;
                            ViewData["Icon"] = "fas fa-comments";
                            ViewData["Placeholder"] = "Preferred communication methods, frequency, best times to contact, etc.";
                            ViewData["Rows"] = 3;
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("CommunicationPreferences");
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />

                        <!-- Notes -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.Notes).ToString();
                            ViewData["Name"] = "Notes";
                            ViewData["Type"] = "textarea";
                            ViewData["Required"] = false;
                            ViewData["Icon"] = "fas fa-sticky-note";
                            ViewData["Placeholder"] = "Additional notes about this stakeholder, their concerns, requirements, etc.";
                            ViewData["Rows"] = 4;
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Notes");
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Type & Role -->
            <div class="card-custom">
                <div class="card-header-custom">
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-white">Type & Role</h3>
                </div>
                <div class="card-body-custom">
                    <div class="space-y-4">
                        <!-- Stakeholder Type -->
                        @{
                            var typeOptions = "";
                            foreach (StakeholderType type in Enum.GetValues<StakeholderType>())
                            {
                                typeOptions += $"<option value=\"{type}\">{type}</option>";
                            }

                            ViewData["Label"] = Html.DisplayNameFor(m => m.Type).ToString();
                            ViewData["Name"] = "Type";
                            ViewData["Type"] = "select";
                            ViewData["Required"] = true;
                            ViewData["Icon"] = "fas fa-tags";
                            ViewData["Options"] = "<option value=\"\">Select type</option>" + typeOptions;
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Type");
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />

                        <!-- Stakeholder Role -->
                        @{
                            var roleOptions = "";
                            foreach (StakeholderRole role in Enum.GetValues<StakeholderRole>())
                            {
                                roleOptions += $"<option value=\"{role}\">{role}</option>";
                            }

                            ViewData["Label"] = Html.DisplayNameFor(m => m.Role).ToString();
                            ViewData["Name"] = "Role";
                            ViewData["Type"] = "select";
                            ViewData["Required"] = true;
                            ViewData["Icon"] = "fas fa-user-tag";
                            ViewData["Options"] = "<option value=\"\">Select role</option>" + roleOptions;
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Role");
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />
                    </div>
                </div>
            </div>

            <!-- Status & Access -->
            <div class="card-custom">
                <div class="card-header-custom">
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-white">Status & Access</h3>
                </div>
                <div class="card-body-custom">
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input asp-for="IsActive" type="checkbox" class="rounded border-neutral-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50" checked />
                            <label asp-for="IsActive" class="ml-2 text-sm text-neutral-700 dark:text-neutral-300">Active stakeholder</label>
                        </div>

                        <!-- User ID -->
                        @{
                            ViewData["Label"] = Html.DisplayNameFor(m => m.UserId).ToString();
                            ViewData["Name"] = "UserId";
                            ViewData["Type"] = "text";
                            ViewData["Required"] = false;
                            ViewData["Icon"] = "fas fa-user-cog";
                            ViewData["Placeholder"] = "User ID if they have system access";
                            ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("UserId");
                            ViewData["HelpText"] = "Link to system user account if applicable";
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="card-custom">
                <div class="card-body-custom">
                    @{
                        ViewData["SubmitText"] = "Create Stakeholder";
                        ViewData["SubmitIcon"] = "fas fa-plus";
                        ViewData["CancelUrl"] = Url.Action("Index");
                        ViewData["ShowCancel"] = true;
                        ViewData["SubmitClass"] = "btn-primary-custom";
                        ViewData["CancelClass"] = "btn-secondary-custom";
                    }
                    <partial name="Components/_FormActions" view-data="ViewData" />
                </div>
            </div>
        </div>
    </div>
</form>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
