using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;

namespace PM.Tool.Core.Interfaces
{
    public interface ITaskRepository : IRepository<TaskEntity>
    {
        Task<IEnumerable<TaskEntity>> GetTasksByProjectIdAsync(int projectId);
        Task<IEnumerable<TaskEntity>> GetTasksByUserIdAsync(string userId);
        Task<IEnumerable<TaskEntity>> GetTasksByStatusAsync(Enums.TaskStatus status);
        Task<IEnumerable<TaskEntity>> GetOverdueTasksAsync();
        Task<IEnumerable<TaskEntity>> GetTasksDueInDaysAsync(int days);
        Task<TaskEntity?> GetTaskWithSubTasksAsync(int taskId);
        Task<TaskEntity?> GetTaskWithCommentsAsync(int taskId);
        Task<IEnumerable<TaskEntity>> GetSubTasksAsync(int parentTaskId);
        Task<IEnumerable<TaskEntity>> GetTasksByPriorityAsync(TaskPriority priority);
    }
}
