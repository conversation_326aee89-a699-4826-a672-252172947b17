using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Constants;
using PM.Tool.Core.Entities;
using PM.Tool.Data;
using System.Security.Claims;

namespace PM.Tool.Core.Authorization
{
    public class ProjectOperationRequirement : IAuthorizationRequirement
    {
        public ProjectOperation Operation { get; }

        public ProjectOperationRequirement(ProjectOperation operation)
        {
            Operation = operation;
        }
    }

    public enum ProjectOperation
    {
        View,
        Create,
        Edit,
        Update,
        Delete,
        ManageTasks,
        ManageTeam
    }

    public class ProjectAuthorizationHandler 
        : AuthorizationHandler<ProjectOperationRequirement, Project>
    {
        private readonly ApplicationDbContext _context;

        public ProjectAuthorizationHandler(ApplicationDbContext context)
        {
            _context = context;
        }

        protected override async Task HandleRequirementAsync(
            AuthorizationHandlerContext context,
            ProjectOperationRequirement requirement,
            Project resource)
        {
            var user = context.User;
            if (!user.Identity?.IsAuthenticated ?? true)
            {
                return;
            }

            var userId = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (userId == null)
            {
                return;
            }

            // Admin can do anything
            if (user.IsInRole(Roles.Admin))
            {
                context.Succeed(requirement);
                return;
            }

            // Project Creator has full access
            if (resource.CreatedByUserId == userId)
            {
                context.Succeed(requirement);
                return;
            }

            // Check project membership
            var membership = await _context.ProjectMembers
                .FirstOrDefaultAsync(m => m.ProjectId == resource.Id && m.UserId == userId && m.IsActive);

            if (membership != null)
            {
                switch (requirement.Operation)
                {
                    case ProjectOperation.View:
                        context.Succeed(requirement);
                        break;

                    case ProjectOperation.Update:
                    case ProjectOperation.Edit:
                    case ProjectOperation.Delete:
                    case ProjectOperation.ManageTasks:
                    case ProjectOperation.ManageTeam:
                        if (membership.IsProjectManager)
                        {
                            context.Succeed(requirement);
                        }
                        break;
                }
            }
        }
    }
}
