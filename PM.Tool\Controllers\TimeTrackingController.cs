using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Models.ViewModels;

namespace PM.Tool.Controllers
{
    [Authorize]
    public class TimeTrackingController : Controller
    {
        private readonly ITimeTrackingService _timeTrackingService;
        private readonly ITaskService _taskService;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<TimeTrackingController> _logger;

        public TimeTrackingController(
            ITimeTrackingService timeTrackingService,
            ITaskService taskService,
            UserManager<ApplicationUser> userManager,
            ILogger<TimeTrackingController> logger)
        {
            _timeTrackingService = timeTrackingService;
            _taskService = taskService;
            _userManager = userManager;
            _logger = logger;
        }

        // GET: TimeTracking
        public async Task<IActionResult> Index()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null) return RedirectToAction("Login", "Account");

            var startDate = DateTime.UtcNow.Date.AddDays(-30);
            var endDate = DateTime.UtcNow;

            var timeEntries = await _timeTrackingService.GetUserTimeEntriesAsync(user.Id, startDate, endDate);
            var activeEntry = await _timeTrackingService.GetActiveTimeEntryAsync(user.Id);

            var viewModel = new TimeTrackingViewModel
            {
                TimeEntries = timeEntries.Select(te => new TimeEntryViewModel
                {
                    Id = te.Id,
                    TaskId = te.TaskId,
                    TaskTitle = te.Task.Title,
                    StartTime = te.StartTime,
                    EndTime = te.EndTime,
                    Duration = te.Duration,
                    Description = te.Description
                }).ToList(),
                ActiveTimeEntry = activeEntry != null ? new TimeEntryViewModel
                {
                    Id = activeEntry.Id,
                    TaskId = activeEntry.TaskId,
                    TaskTitle = activeEntry.Task.Title,
                    StartTime = activeEntry.StartTime,
                    Description = activeEntry.Description
                } : null
            };

            return View(viewModel);
        }

        // POST: TimeTracking/Start
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Start(int taskId, string? description)
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null) return RedirectToAction("Login", "Account");

                var task = await _taskService.GetTaskByIdAsync(taskId);
                if (task == null) return NotFound();

                await _timeTrackingService.StartTimeTrackingAsync(taskId, user.Id, description);
                
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting time tracking");
                TempData["ErrorMessage"] = "Failed to start time tracking. " + ex.Message;
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: TimeTracking/Stop
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Stop(int timeEntryId)
        {
            try
            {
                await _timeTrackingService.StopTimeTrackingAsync(timeEntryId);
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error stopping time tracking");
                TempData["ErrorMessage"] = "Failed to stop time tracking. " + ex.Message;
                return RedirectToAction(nameof(Index));
            }
        }

        // GET: TimeTracking/Report
        public async Task<IActionResult> Report(DateTime? startDate, DateTime? endDate)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null) return RedirectToAction("Login", "Account");

            startDate ??= DateTime.UtcNow.Date.AddDays(-30);
            endDate ??= DateTime.UtcNow;

            var report = await _timeTrackingService.GetUserTimeReportAsync(user.Id, startDate.Value, endDate.Value);
            
            var viewModel = new TimeTrackingReportViewModel
            {
                StartDate = startDate.Value,
                EndDate = endDate.Value,
                TimeByTask = report.Select(kvp => new TimeByTaskViewModel
                {
                    TaskName = kvp.Key,
                    Hours = kvp.Value
                }).ToList()
            };

            return View(viewModel);
        }
    }
}
