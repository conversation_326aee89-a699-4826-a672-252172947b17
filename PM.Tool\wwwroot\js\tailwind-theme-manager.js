/**
 * Tailwind Theme Manager for PM Tool
 * Handles dark/light theme switching with Tailwind CSS
 */

class TailwindThemeManager {
    constructor() {
        this.storageKey = 'pm-tool-theme';
        this.defaultTheme = 'light';
        this.currentTheme = this.getStoredTheme();

        this.init();
    }

    init() {
        // Apply the current theme immediately
        this.applyTheme(this.currentTheme);

        // Set up theme toggle button
        this.setupThemeToggle();

        // Listen for system theme changes
        this.setupSystemThemeListener();

        console.log('Tailwind Theme Manager initialized with theme:', this.currentTheme);
    }

    getStoredTheme() {
        try {
            return localStorage.getItem(this.storageKey) || this.defaultTheme;
        } catch (error) {
            console.warn('Failed to get stored theme:', error);
            return this.defaultTheme;
        }
    }

    setStoredTheme(theme) {
        try {
            localStorage.setItem(this.storageKey, theme);
        } catch (error) {
            console.warn('Failed to store theme:', error);
        }
    }

    applyTheme(theme) {
        try {
            const html = document.documentElement;
            const body = document.body;
            const themeIcon = document.getElementById('theme-icon');

            // Validate theme parameter
            if (!theme || (theme !== 'dark' && theme !== 'light')) {
                console.warn('Invalid theme provided:', theme);
                theme = 'light'; // fallback to light theme
            }

            // Remove existing theme classes
            html.classList.remove('dark', 'light');

            // Apply new theme
            if (theme === 'dark') {
                html.classList.add('dark');
                if (themeIcon) {
                    themeIcon.className = 'fas fa-sun text-neutral-600 dark:text-dark-300';
                }
            } else {
                html.classList.remove('dark');
                if (themeIcon) {
                    themeIcon.className = 'fas fa-moon text-neutral-600 dark:text-dark-300';
                }
            }

            // Update meta theme-color for mobile browsers
            this.updateMetaThemeColor(theme);

            this.currentTheme = theme;
            this.setStoredTheme(theme);

            // Dispatch custom event for other components to listen to (with fallback)
            this.dispatchThemeChangeEvent(theme);

        } catch (error) {
            console.error('Error applying theme:', error);
            // Fallback to basic theme application
            try {
                document.documentElement.classList.remove('dark', 'light');
                if (theme === 'dark') {
                    document.documentElement.classList.add('dark');
                }
                this.currentTheme = theme || 'light';
            } catch (fallbackError) {
                console.error('Fallback theme application failed:', fallbackError);
            }
        }
    }

    dispatchThemeChangeEvent(theme) {
        try {
            // Try modern CustomEvent first
            if (typeof CustomEvent === 'function') {
                window.dispatchEvent(new CustomEvent('themeChanged', {
                    detail: { theme: theme }
                }));
            } else {
                // Fallback for older browsers
                const event = document.createEvent('CustomEvent');
                event.initCustomEvent('themeChanged', false, false, { theme: theme });
                window.dispatchEvent(event);
            }
        } catch (error) {
            console.warn('Failed to dispatch theme change event:', error);
            // Silent fallback - theme will still work without events
        }
    }

    updateMetaThemeColor(theme) {
        try {
            let metaThemeColor = document.querySelector('meta[name="theme-color"]');

            if (!metaThemeColor) {
                metaThemeColor = document.createElement('meta');
                metaThemeColor.name = 'theme-color';
                document.head.appendChild(metaThemeColor);
            }

            // Set theme color based on current theme
            if (theme === 'dark') {
                metaThemeColor.content = '#262626'; // surface-dark
            } else {
                metaThemeColor.content = '#ffffff'; // white
            }
        } catch (error) {
            console.warn('Failed to update meta theme color:', error);
        }
    }

    toggleTheme() {
        const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
        this.applyTheme(newTheme);

        // Add a subtle animation effect
        this.addToggleAnimation();
    }

    addToggleAnimation() {
        const body = document.body;
        body.style.transition = 'background-color 0.3s ease, color 0.3s ease';

        setTimeout(() => {
            body.style.transition = '';
        }, 300);
    }

    setupThemeToggle() {
        const themeToggle = document.getElementById('theme-toggle');

        if (themeToggle) {
            themeToggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleTheme();
            });

            // Add keyboard support
            themeToggle.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.toggleTheme();
                }
            });
        }
    }

    setupSystemThemeListener() {
        // Listen for system theme changes
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

            mediaQuery.addEventListener('change', (e) => {
                // Only auto-switch if user hasn't manually set a preference
                const storedTheme = this.getStoredTheme();
                if (!storedTheme || storedTheme === 'system') {
                    const systemTheme = e.matches ? 'dark' : 'light';
                    this.applyTheme(systemTheme);
                }
            });
        }
    }

    // Public methods for external use
    setTheme(theme) {
        if (theme === 'dark' || theme === 'light') {
            this.applyTheme(theme);
        } else {
            console.warn('Invalid theme:', theme);
        }
    }

    getTheme() {
        return this.currentTheme;
    }

    // Method to sync theme with system preference
    syncWithSystem() {
        if (window.matchMedia) {
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            const systemTheme = prefersDark ? 'dark' : 'light';
            this.applyTheme(systemTheme);
        }
    }
}

// Initialize theme manager (prevent double initialization)
function initializeThemeManager() {
    if (!window.TailwindThemeManager) {
        try {
            window.TailwindThemeManager = new TailwindThemeManager();
        } catch (error) {
            console.error('Failed to initialize TailwindThemeManager:', error);
        }
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeThemeManager);
} else {
    // DOM is already loaded
    initializeThemeManager();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TailwindThemeManager;
}
