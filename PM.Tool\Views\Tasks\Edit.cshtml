@model PM.Tool.Models.ViewModels.TaskEditViewModelEnhanced
@using Microsoft.AspNetCore.Mvc.Rendering

@{
    ViewData["Title"] = "Edit Task";
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-edit mr-3 text-primary-600 dark:text-primary-400"></i>
                Edit Task
            </h1>
            <div class="mt-1 space-y-1">
                <p class="text-sm text-neutral-500 dark:text-dark-400">
                    <i class="fas fa-project-diagram mr-1"></i>
                    Project ID: <span class="font-medium">@Model.ProjectId</span>
                </p>
                @if (Model.ParentTaskId.HasValue)
                {
                    <p class="text-sm text-neutral-500 dark:text-dark-400">
                        <i class="fas fa-tasks mr-1"></i>
                        Parent Task ID: <span class="font-medium">@Model.ParentTaskId</span>
                    </p>
                }
            </div>
        </div>
        <div class="flex space-x-3">
            @{
                ViewData["Text"] = "View Details";
                ViewData["Variant"] = "outline";
                ViewData["Icon"] = "fas fa-eye";
                ViewData["Href"] = Url.Action("Details", new { id = Model.Id });
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "Back to Tasks";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Index");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Edit Task Form -->
<div class="card-custom">
    <div class="card-header-custom">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                <i class="fas fa-tasks text-primary-600 dark:text-primary-400 text-sm"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Task Information</h3>
            </div>
        </div>
    </div>
    <div class="card-body-custom">
        <form asp-action="Edit" class="space-y-6">
            <div asp-validation-summary="ModelOnly" class="alert-danger-custom"></div>
            <input type="hidden" asp-for="Id" />
            <input type="hidden" asp-for="ProjectId" />
            <input type="hidden" asp-for="ParentTaskId" />

            <!-- Task Title -->
            @{
                ViewData["Label"] = Html.DisplayNameFor(m => m.Title).ToString();
                ViewData["Name"] = "Title";
                ViewData["Type"] = "text";
                ViewData["Required"] = true;
                ViewData["Icon"] = "fas fa-tag";
                ViewData["Value"] = Model.Title;
                ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Title");
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

            <!-- Task Description -->
            @{
                ViewData["Label"] = Html.DisplayNameFor(m => m.Description).ToString();
                ViewData["Name"] = "Description";
                ViewData["Type"] = "textarea";
                ViewData["Required"] = false;
                ViewData["Icon"] = "fas fa-align-left";
                ViewData["Value"] = Model.Description;
                ViewData["Rows"] = 5;
                ViewData["HelpText"] = "Supports Markdown formatting";
                ViewData["AdditionalClasses"] = "markdown-editor";
                ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Description");
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

            <!-- Status, Priority, and Assignment -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Status -->
                @{
                    var statusOptions = "<option value=\"\">-- Select Status --</option>";
                    foreach (var item in Html.GetEnumSelectList<PM.Tool.Core.Enums.TaskStatus>())
                    {
                        var selected = item.Value == Model.Status.ToString() ? "selected" : "";
                        statusOptions += $"<option value=\"{item.Value}\" {selected}>{item.Text}</option>";
                    }

                    ViewData["Label"] = Html.DisplayNameFor(m => m.Status).ToString();
                    ViewData["Name"] = "Status";
                    ViewData["Type"] = "select";
                    ViewData["Required"] = true;
                    ViewData["Icon"] = "fas fa-info-circle";
                    ViewData["Options"] = statusOptions;
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Status");
                    ViewData["ContainerClasses"] = "";
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Priority -->
                @{
                    var priorityOptions = "<option value=\"\">-- Select Priority --</option>";
                    foreach (var item in Html.GetEnumSelectList<PM.Tool.Core.Enums.TaskPriority>())
                    {
                        var selected = item.Value == Model.Priority.ToString() ? "selected" : "";
                        priorityOptions += $"<option value=\"{item.Value}\" {selected}>{item.Text}</option>";
                    }

                    ViewData["Label"] = Html.DisplayNameFor(m => m.Priority).ToString();
                    ViewData["Name"] = "Priority";
                    ViewData["Type"] = "select";
                    ViewData["Required"] = true;
                    ViewData["Icon"] = "fas fa-exclamation-triangle";
                    ViewData["Options"] = priorityOptions;
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Priority");
                    ViewData["ContainerClasses"] = "";
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Assigned To -->
                @{
                    var userOptions = "<option value=\"\">-- Select User --</option>";
                    if (ViewBag.Users != null)
                    {
                        foreach (var user in (IEnumerable<SelectListItem>)ViewBag.Users)
                        {
                            var selected = user.Value == Model.AssignedToUserId ? "selected" : "";
                            userOptions += $"<option value=\"{user.Value}\" {selected}>{user.Text}</option>";
                        }
                    }

                    ViewData["Label"] = "Assign To";
                    ViewData["Name"] = "AssignedToUserId";
                    ViewData["Type"] = "select";
                    ViewData["Required"] = false;
                    ViewData["Icon"] = "fas fa-user";
                    ViewData["Options"] = userOptions;
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("AssignedToUserId");
                    ViewData["ContainerClasses"] = "";
                    ViewData["UseSelect2"] = true;
                    ViewData["Select2Options"] = "{\"placeholder\": \"Select User\", \"allowClear\": true, \"theme\": \"tailwind\"}";
                }
                <partial name="Components/_FormInput" view-data="ViewData" />
            </div>

            <!-- Dates and Hours -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <!-- Start Date -->
                @{
                    ViewData["Label"] = Html.DisplayNameFor(m => m.StartDate).ToString();
                    ViewData["Name"] = "StartDate";
                    ViewData["Type"] = "date";
                    ViewData["Required"] = false;
                    ViewData["Icon"] = "fas fa-calendar-alt";
                    ViewData["Value"] = Model.StartDate?.ToString("yyyy-MM-dd");
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("StartDate");
                    ViewData["ContainerClasses"] = "";
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Due Date -->
                @{
                    ViewData["Label"] = Html.DisplayNameFor(m => m.DueDate).ToString();
                    ViewData["Name"] = "DueDate";
                    ViewData["Type"] = "date";
                    ViewData["Required"] = false;
                    ViewData["Icon"] = "fas fa-calendar-check";
                    ViewData["Value"] = Model.DueDate?.ToString("yyyy-MM-dd");
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("DueDate");
                    ViewData["ContainerClasses"] = "";
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Estimated Hours -->
                @{
                    ViewData["Label"] = Html.DisplayNameFor(m => m.EstimatedHours).ToString();
                    ViewData["Name"] = "EstimatedHours";
                    ViewData["Type"] = "number";
                    ViewData["Required"] = false;
                    ViewData["Icon"] = "fas fa-clock";
                    ViewData["Value"] = Model.EstimatedHours.ToString();
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("EstimatedHours");
                    ViewData["ContainerClasses"] = "";
                    ViewData["AdditionalAttributes"] = "min=\"0\" step=\"1\"";
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Actual Hours -->
                @{
                    ViewData["Label"] = Html.DisplayNameFor(m => m.ActualHours).ToString();
                    ViewData["Name"] = "ActualHours";
                    ViewData["Type"] = "number";
                    ViewData["Required"] = false;
                    ViewData["Icon"] = "fas fa-stopwatch";
                    ViewData["Value"] = Model.ActualHours.ToString();
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("ActualHours");
                    ViewData["ContainerClasses"] = "";
                    ViewData["AdditionalAttributes"] = "min=\"0\" step=\"1\"";
                }
                <partial name="Components/_FormInput" view-data="ViewData" />
            </div>

            <!-- Form Actions -->
            @{
                ViewData["SubmitText"] = "Save Changes";
                ViewData["SubmitIcon"] = "fas fa-save";
                ViewData["CancelUrl"] = Url.Action("Details", new { id = Model.Id });
            }
            <partial name="Components/_FormActions" view-data="ViewData" />
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        $(document).ready(function() {
            // Form validation styling
            $('form').on('submit', function(e) {
                var isValid = true;

                // Check required fields
                $(this).find('[required]').each(function() {
                    if (!$(this).val()) {
                        isValid = false;
                        $(this).addClass('border-danger-300 dark:border-danger-600');
                    } else {
                        $(this).removeClass('border-danger-300 dark:border-danger-600');
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    // Show error message
                    if (!$('.alert-danger-custom').length) {
                        $('form').prepend(`
                            <div class="alert-danger-custom mb-6">
                                <i class="fas fa-exclamation-circle text-danger-600 dark:text-danger-400"></i>
                                <div>
                                    <p class="font-medium">Validation Error</p>
                                    <p class="text-sm">Please fill in all required fields.</p>
                                </div>
                            </div>
                        `);
                    }
                }
            });

            // Date validation
            $('input[name="DueDate"]').on('change', function() {
                var startDate = $('input[name="StartDate"]').val();
                var dueDate = $(this).val();

                if (startDate && dueDate && new Date(dueDate) < new Date(startDate)) {
                    $(this).addClass('border-danger-300 dark:border-danger-600');
                    if (!$(this).siblings('.text-danger-600').length) {
                        $(this).after('<p class="text-sm text-danger-600 dark:text-danger-400 mt-1">Due date must be after start date</p>');
                    }
                } else {
                    $(this).removeClass('border-danger-300 dark:border-danger-600');
                    $(this).siblings('.text-danger-600').remove();
                }
            });

            // Hours validation
            $('input[name="ActualHours"]').on('change', function() {
                var estimatedHours = parseFloat($('input[name="EstimatedHours"]').val()) || 0;
                var actualHours = parseFloat($(this).val()) || 0;

                if (actualHours > estimatedHours * 1.5) {
                    $(this).addClass('border-warning-300 dark:border-warning-600');
                    if (!$(this).siblings('.text-warning-600').length) {
                        $(this).after('<p class="text-sm text-warning-600 dark:text-warning-400 mt-1">Actual hours significantly exceed estimate</p>');
                    }
                } else {
                    $(this).removeClass('border-warning-300 dark:border-warning-600');
                    $(this).siblings('.text-warning-600').remove();
                }
            });

            // Initialize markdown editor if element exists
            var markdownElement = document.querySelector('.markdown-editor');
            if (markdownElement && typeof EasyMDE !== 'undefined') {
                var easyMDE = new EasyMDE({
                    element: markdownElement,
                    spellChecker: false,
                    status: false,
                    placeholder: 'Enter task description...',
                    toolbar: ["bold", "italic", "heading", "|", "quote", "unordered-list", "ordered-list", "|", "link", "preview"]
                });
            }
        });
    </script>
}
