@using PM.Tool.Models.ViewModels
@model ResourceCreateViewModel

@{
    ViewData["Title"] = "Create Resource";
    ViewData["Subtitle"] = "Add a new resource to your project management system";
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-plus-circle mr-3 text-primary-600 dark:text-primary-400"></i>
                Create New Resource
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Add a new resource to your project management system
            </p>
        </div>
        <div>
            @{
                ViewData["Text"] = "Back to Resources";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Index");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Create Resource Form -->
<div class="card-custom">
    <div class="card-header-custom">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                <i class="fas fa-user-plus text-primary-600 dark:text-primary-400 text-sm"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Resource Information</h3>
            </div>
        </div>
    </div>
    <div class="card-body-custom">
        <form asp-action="Create" class="space-y-6">
            <div asp-validation-summary="ModelOnly" class="alert-danger-custom"></div>

            <!-- Resource Name -->
            @{
                ViewData["Label"] = Html.DisplayNameFor(m => m.Name).ToString();
                ViewData["Name"] = "Name";
                ViewData["Type"] = "text";
                ViewData["Required"] = true;
                ViewData["Icon"] = "fas fa-tag";
                ViewData["Placeholder"] = "Enter resource name";
                ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Name");
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

            <!-- Resource Type and Description -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Resource Type -->
                @{
                    var typeOptions = "<option value=\"\">-- Select Resource Type --</option>";
                    foreach (var item in Html.GetEnumSelectList<PM.Tool.Core.Entities.ResourceType>())
                    {
                        typeOptions += $"<option value=\"{item.Value}\">{item.Text}</option>";
                    }

                    ViewData["Label"] = Html.DisplayNameFor(m => m.Type).ToString();
                    ViewData["Name"] = "Type";
                    ViewData["Type"] = "select";
                    ViewData["Required"] = true;
                    ViewData["Icon"] = "fas fa-cube";
                    ViewData["Options"] = typeOptions;
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Type");
                    ViewData["ContainerClasses"] = "";
                    ViewData["UseSelect2"] = true;
                    ViewData["Select2Options"] = "{\"placeholder\": \"Select Resource Type\", \"theme\": \"tailwind\"}";
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Department -->
                @{
                    ViewData["Label"] = Html.DisplayNameFor(m => m.Department).ToString();
                    ViewData["Name"] = "Department";
                    ViewData["Type"] = "text";
                    ViewData["Required"] = false;
                    ViewData["Icon"] = "fas fa-building";
                    ViewData["Placeholder"] = "Enter department";
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Department");
                    ViewData["ContainerClasses"] = "";
                }
                <partial name="Components/_FormInput" view-data="ViewData" />
            </div>

            <!-- Description -->
            @{
                ViewData["Label"] = Html.DisplayNameFor(m => m.Description).ToString();
                ViewData["Name"] = "Description";
                ViewData["Type"] = "textarea";
                ViewData["Required"] = false;
                ViewData["Icon"] = "fas fa-align-left";
                ViewData["Placeholder"] = "Enter resource description";
                ViewData["Rows"] = 3;
                ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Description");
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

            <!-- Location and Capacity -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Location -->
                @{
                    ViewData["Label"] = Html.DisplayNameFor(m => m.Location).ToString();
                    ViewData["Name"] = "Location";
                    ViewData["Type"] = "text";
                    ViewData["Required"] = false;
                    ViewData["Icon"] = "fas fa-map-marker-alt";
                    ViewData["Placeholder"] = "Enter location";
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Location");
                    ViewData["ContainerClasses"] = "";
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Capacity -->
                @{
                    ViewData["Label"] = Html.DisplayNameFor(m => m.Capacity).ToString();
                    ViewData["Name"] = "Capacity";
                    ViewData["Type"] = "number";
                    ViewData["Required"] = true;
                    ViewData["Icon"] = "fas fa-clock";
                    ViewData["Placeholder"] = "8.0";
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Capacity");
                    ViewData["ContainerClasses"] = "";
                    ViewData["HelpText"] = "Daily capacity in hours";
                }
                <partial name="Components/_FormInput" view-data="ViewData" />
            </div>

            <!-- Hourly Rate and Contact -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Hourly Rate -->
                @{
                    ViewData["Label"] = Html.DisplayNameFor(m => m.HourlyRate).ToString();
                    ViewData["Name"] = "HourlyRate";
                    ViewData["Type"] = "number";
                    ViewData["Required"] = true;
                    ViewData["Icon"] = "fas fa-dollar-sign";
                    ViewData["Placeholder"] = "0.00";
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("HourlyRate");
                    ViewData["ContainerClasses"] = "";
                    ViewData["HelpText"] = "Cost per hour";
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Email -->
                @{
                    ViewData["Label"] = Html.DisplayNameFor(m => m.Email).ToString();
                    ViewData["Name"] = "Email";
                    ViewData["Type"] = "email";
                    ViewData["Required"] = false;
                    ViewData["Icon"] = "fas fa-envelope";
                    ViewData["Placeholder"] = "Enter email address";
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Email");
                    ViewData["ContainerClasses"] = "";
                }
                <partial name="Components/_FormInput" view-data="ViewData" />
            </div>

            <!-- Phone and Skills -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Phone -->
                @{
                    ViewData["Label"] = Html.DisplayNameFor(m => m.Phone).ToString();
                    ViewData["Name"] = "Phone";
                    ViewData["Type"] = "tel";
                    ViewData["Required"] = false;
                    ViewData["Icon"] = "fas fa-phone";
                    ViewData["Placeholder"] = "Enter phone number";
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Phone");
                    ViewData["ContainerClasses"] = "";
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Active Status -->
                <div class="flex items-center space-x-3 pt-8">
                    <input asp-for="IsActive" type="checkbox" class="form-checkbox-custom" />
                    <label asp-for="IsActive" class="form-label-custom">
                        Resource is active and available for allocation
                    </label>
                </div>
            </div>

            <!-- Skills -->
            @{
                ViewData["Label"] = Html.DisplayNameFor(m => m.Skills).ToString();
                ViewData["Name"] = "Skills";
                ViewData["Type"] = "textarea";
                ViewData["Required"] = false;
                ViewData["Icon"] = "fas fa-star";
                ViewData["Placeholder"] = "Enter skills (comma-separated)";
                ViewData["Rows"] = 3;
                ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Skills");
                ViewData["HelpText"] = "List skills separated by commas (e.g., \"JavaScript, React, Node.js\")";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

            <!-- Form Actions -->
            @{
                ViewData["SubmitText"] = "Create Resource";
                ViewData["SubmitIcon"] = "fas fa-plus";
                ViewData["CancelUrl"] = Url.Action("Index");
            }
            <partial name="Components/_FormActions" view-data="ViewData" />
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        $(document).ready(function() {
            // Add form validation styling
            $('form').on('submit', function(e) {
                var isValid = true;

                // Check required fields
                $(this).find('[required]').each(function() {
                    if (!$(this).val()) {
                        isValid = false;
                        $(this).addClass('border-danger-300 dark:border-danger-600');
                    } else {
                        $(this).removeClass('border-danger-300 dark:border-danger-600');
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    // Show error message
                    if (!$('.alert-danger-custom').length) {
                        $('form').prepend(`
                            <div class="alert-danger-custom mb-6">
                                <i class="fas fa-exclamation-circle text-danger-600 dark:text-danger-400"></i>
                                <div>
                                    <p class="font-medium">Validation Error</p>
                                    <p class="text-sm">Please fill in all required fields.</p>
                                </div>
                            </div>
                        `);
                    }
                }
            });

            // Auto-format hourly rate input
            $('input[name="HourlyRate"]').on('input', function() {
                let value = $(this).val().replace(/[^\d.]/g, '');
                if (value.includes('.')) {
                    let parts = value.split('.');
                    if (parts[1] && parts[1].length > 2) {
                        parts[1] = parts[1].substring(0, 2);
                        value = parts.join('.');
                    }
                }
                $(this).val(value);
            });

            // Auto-format capacity input
            $('input[name="Capacity"]').on('input', function() {
                let value = parseFloat($(this).val());
                if (value > 24) {
                    $(this).val('24');
                } else if (value < 0) {
                    $(this).val('0');
                }
            });
        });
    </script>
}
