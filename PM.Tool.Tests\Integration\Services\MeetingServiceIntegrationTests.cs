using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using PM.Tool.Application.Services;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Data;
using PM.Tool.Tests.Helpers;
using Xunit;

namespace PM.Tool.Tests.Integration.Services
{
    public class MeetingServiceIntegrationTests : IDisposable
    {
        private readonly ApplicationDbContext _context;
        private readonly MeetingService _service;

        public MeetingServiceIntegrationTests()
        {
            var options = new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            _context = new ApplicationDbContext(options);
            _service = new MeetingService(_context);
        }

        [Fact]
        public async Task GetAllMeetingsAsync_ReturnsAllMeetingsOrderedByDate()
        {
            // Arrange
            var meetings = MockHelper.CreateMockMeetings(3).ToList();
            // Reset IDs to let database assign them
            foreach (var meeting in meetings)
            {
                meeting.Id = 0;
            }
            await _context.Meetings.AddRangeAsync(meetings);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.GetAllMeetingsAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(3);
            result.Should().BeInDescendingOrder(m => m.ScheduledDate);
        }

        [Fact]
        public async Task GetProjectMeetingsAsync_ReturnsProjectMeetingsOnly()
        {
            // Arrange
            var projectId = 1;
            var meetings = MockHelper.CreateMockMeetings(5).ToList();

            // Reset IDs and set some meetings to different projects
            foreach (var meeting in meetings)
            {
                meeting.Id = 0;
            }
            meetings[0].ProjectId = projectId;
            meetings[1].ProjectId = projectId;
            meetings[2].ProjectId = 2; // Different project

            await _context.Meetings.AddRangeAsync(meetings);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.GetProjectMeetingsAsync(projectId);

            // Assert
            result.Should().NotBeNull();
            result.Should().OnlyContain(m => m.ProjectId == projectId);
            result.Should().HaveCount(2);
        }

        [Fact]
        public async Task GetMeetingByIdAsync_WithValidId_ReturnsMeeting()
        {
            // Arrange
            var meeting = MockHelper.CreateMockMeeting();
            meeting.Id = 0; // Reset ID to let database assign it
            await _context.Meetings.AddAsync(meeting);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.GetMeetingByIdAsync(meeting.Id);

            // Assert
            result.Should().NotBeNull();
            result!.Id.Should().Be(meeting.Id);
            result.Title.Should().Be(meeting.Title);
        }

        [Fact]
        public async Task GetMeetingByIdAsync_WithInvalidId_ReturnsNull()
        {
            // Arrange
            var invalidId = 999;

            // Act
            var result = await _service.GetMeetingByIdAsync(invalidId);

            // Assert
            result.Should().BeNull();
        }

        [Fact]
        public async Task CreateMeetingAsync_WithValidMeeting_CreatesMeeting()
        {
            // Arrange
            var meeting = MockHelper.CreateMockMeeting();
            meeting.Id = 0; // Reset ID for creation

            // Act
            var result = await _service.CreateMeetingAsync(meeting);

            // Assert
            result.Should().NotBeNull();
            result.Id.Should().BeGreaterThan(0);
            result.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));

            // Verify it was saved to database
            var savedMeeting = await _context.Meetings.FindAsync(result.Id);
            savedMeeting.Should().NotBeNull();
            savedMeeting!.Title.Should().Be(meeting.Title);
        }

        [Fact]
        public async Task UpdateMeetingAsync_WithValidMeeting_UpdatesMeeting()
        {
            // Arrange
            var meeting = MockHelper.CreateMockMeeting();
            await _context.Meetings.AddAsync(meeting);
            await _context.SaveChangesAsync();

            // Modify the meeting
            meeting.Title = "Updated Meeting Title";
            meeting.Description = "Updated Description";

            // Act
            var result = await _service.UpdateMeetingAsync(meeting);

            // Assert
            result.Should().BeTrue();
            meeting.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));

            // Verify changes were saved
            var updatedMeeting = await _context.Meetings.FindAsync(meeting.Id);
            updatedMeeting.Should().NotBeNull();
            updatedMeeting!.Title.Should().Be("Updated Meeting Title");
            updatedMeeting.Description.Should().Be("Updated Description");
        }

        [Fact]
        public async Task DeleteMeetingAsync_WithValidId_DeletesMeeting()
        {
            // Arrange
            var meeting = MockHelper.CreateMockMeeting();
            await _context.Meetings.AddAsync(meeting);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.DeleteMeetingAsync(meeting.Id);

            // Assert
            result.Should().BeTrue();

            // Verify meeting was deleted
            var deletedMeeting = await _context.Meetings.FindAsync(meeting.Id);
            deletedMeeting.Should().BeNull();
        }

        [Fact]
        public async Task StartMeetingAsync_WithValidId_StartsMeeting()
        {
            // Arrange
            var meeting = MockHelper.CreateMockMeeting();
            meeting.Status = MeetingStatus.Scheduled;
            await _context.Meetings.AddAsync(meeting);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.StartMeetingAsync(meeting.Id);

            // Assert
            result.Should().BeTrue();

            // Verify status was updated
            var updatedMeeting = await _context.Meetings.FindAsync(meeting.Id);
            updatedMeeting.Should().NotBeNull();
            updatedMeeting!.Status.Should().Be(MeetingStatus.InProgress);
            updatedMeeting.ActualStartTime.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            updatedMeeting.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        }

        [Fact]
        public async Task EndMeetingAsync_WithValidId_EndsMeeting()
        {
            // Arrange
            var meeting = MockHelper.CreateMockMeeting();
            meeting.Status = MeetingStatus.InProgress;
            meeting.ActualStartTime = DateTime.UtcNow.AddHours(-1);
            await _context.Meetings.AddAsync(meeting);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.EndMeetingAsync(meeting.Id);

            // Assert
            result.Should().BeTrue();

            // Verify status was updated
            var updatedMeeting = await _context.Meetings.FindAsync(meeting.Id);
            updatedMeeting.Should().NotBeNull();
            updatedMeeting!.Status.Should().Be(MeetingStatus.Completed);
            updatedMeeting.ActualEndTime.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
            updatedMeeting.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        }

        [Fact]
        public async Task AddAttendeeAsync_WithValidData_AddsAttendee()
        {
            // Arrange
            var meeting = MockHelper.CreateMockMeeting();
            await _context.Meetings.AddAsync(meeting);
            await _context.SaveChangesAsync();

            var userId = "user123";
            var role = AttendeeRole.Attendee;
            var isRequired = true;

            // Act
            var result = await _service.AddAttendeeAsync(meeting.Id, userId, role, isRequired);

            // Assert
            result.Should().BeTrue();

            // Verify attendee was added
            var attendee = await _context.MeetingAttendees
                .FirstOrDefaultAsync(a => a.MeetingId == meeting.Id && a.UserId == userId);
            attendee.Should().NotBeNull();
            attendee!.Role.Should().Be(role);
            attendee.IsRequired.Should().Be(isRequired);
        }

        [Fact]
        public async Task RemoveAttendeeAsync_WithValidData_RemovesAttendee()
        {
            // Arrange
            var meeting = MockHelper.CreateMockMeeting();
            await _context.Meetings.AddAsync(meeting);

            var attendee = new MeetingAttendee
            {
                MeetingId = meeting.Id,
                UserId = "user123",
                Role = AttendeeRole.Attendee,
                Status = AttendanceStatus.Invited
            };
            await _context.MeetingAttendees.AddAsync(attendee);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.RemoveAttendeeAsync(meeting.Id, "user123");

            // Assert
            result.Should().BeTrue();

            // Verify attendee was removed
            var removedAttendee = await _context.MeetingAttendees
                .FirstOrDefaultAsync(a => a.MeetingId == meeting.Id && a.UserId == "user123");
            removedAttendee.Should().BeNull();
        }

        [Fact]
        public async Task UpdateAttendeeStatusAsync_WithValidData_UpdatesStatus()
        {
            // Arrange
            var meeting = MockHelper.CreateMockMeeting();
            await _context.Meetings.AddAsync(meeting);

            var attendee = new MeetingAttendee
            {
                MeetingId = meeting.Id,
                UserId = "user123",
                Role = AttendeeRole.Attendee,
                Status = AttendanceStatus.Invited
            };
            await _context.MeetingAttendees.AddAsync(attendee);
            await _context.SaveChangesAsync();

            var newStatus = AttendanceStatus.Accepted;

            // Act
            var result = await _service.UpdateAttendeeStatusAsync(meeting.Id, "user123", newStatus);

            // Assert
            result.Should().BeTrue();

            // Verify status was updated
            var updatedAttendee = await _context.MeetingAttendees
                .FirstOrDefaultAsync(a => a.MeetingId == meeting.Id && a.UserId == "user123");
            updatedAttendee.Should().NotBeNull();
            updatedAttendee!.Status.Should().Be(newStatus);
            updatedAttendee.ResponseDate.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        }

        [Fact]
        public async Task GetUpcomingMeetingsAsync_ReturnsUpcomingMeetingsForUser()
        {
            // Arrange
            var userId = "user123";
            var days = 7;
            var meetings = MockHelper.CreateMockMeetings(5).ToList();

            // Reset IDs and set up some meetings as upcoming for the user
            foreach (var meeting in meetings)
            {
                meeting.Id = 0;
            }
            meetings[0].OrganizerUserId = userId;
            meetings[0].ScheduledDate = DateTime.UtcNow.AddDays(1);
            meetings[1].ScheduledDate = DateTime.UtcNow.AddDays(2);
            meetings[2].ScheduledDate = DateTime.UtcNow.AddDays(-1); // Past meeting
            meetings[3].ScheduledDate = DateTime.UtcNow.AddDays(10); // Too far in future
            meetings[4].ScheduledDate = DateTime.UtcNow.AddDays(3);

            await _context.Meetings.AddRangeAsync(meetings);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.GetUpcomingMeetingsAsync(userId, days);

            // Assert
            result.Should().NotBeNull();
            result.Should().OnlyContain(m => m.ScheduledDate >= DateTime.UtcNow && m.ScheduledDate <= DateTime.UtcNow.AddDays(days));
            result.Should().BeInAscendingOrder(m => m.ScheduledDate);
        }

        [Fact]
        public async Task CheckMeetingConflictAsync_WithConflict_ReturnsTrue()
        {
            // Arrange
            var proposedTime = DateTime.UtcNow.AddDays(1);
            var durationMinutes = 60;
            var attendeeIds = new[] { "user1", "user2" };

            // Create an existing meeting that overlaps
            var existingMeeting = MockHelper.CreateMockMeeting();
            existingMeeting.ScheduledDate = proposedTime.AddMinutes(30); // Overlapping
            existingMeeting.DurationMinutes = 60;
            await _context.Meetings.AddAsync(existingMeeting);

            // Add attendees to the existing meeting
            var attendee1 = new MeetingAttendee
            {
                MeetingId = existingMeeting.Id,
                UserId = "user1",
                Role = AttendeeRole.Attendee,
                Status = AttendanceStatus.Accepted
            };
            await _context.MeetingAttendees.AddAsync(attendee1);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.CheckMeetingConflictAsync(proposedTime, durationMinutes, attendeeIds);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task CheckMeetingConflictAsync_WithoutConflict_ReturnsFalse()
        {
            // Arrange
            var proposedTime = DateTime.UtcNow.AddDays(1);
            var durationMinutes = 60;
            var attendeeIds = new[] { "user1", "user2" };

            // Create an existing meeting that doesn't overlap
            var existingMeeting = MockHelper.CreateMockMeeting();
            existingMeeting.ScheduledDate = proposedTime.AddHours(2); // No overlap
            existingMeeting.DurationMinutes = 60;
            await _context.Meetings.AddAsync(existingMeeting);

            // Add attendees to the existing meeting
            var attendee1 = new MeetingAttendee
            {
                MeetingId = existingMeeting.Id,
                UserId = "user1",
                Role = AttendeeRole.Attendee,
                Status = AttendanceStatus.Accepted
            };
            await _context.MeetingAttendees.AddAsync(attendee1);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.CheckMeetingConflictAsync(proposedTime, durationMinutes, attendeeIds);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task GetUserMeetingsAsync_ReturnsUserMeetings()
        {
            // Arrange
            var userId = "user123";
            var meetings = MockHelper.CreateMockMeetings(3).ToList();

            // Reset IDs and set one meeting as organized by user
            foreach (var meeting in meetings)
            {
                meeting.Id = 0;
            }
            meetings[0].OrganizerUserId = userId;

            await _context.Meetings.AddRangeAsync(meetings);
            await _context.SaveChangesAsync();

            // Add user as attendee to another meeting
            var attendee = new MeetingAttendee
            {
                MeetingId = meetings[1].Id,
                UserId = userId,
                Role = AttendeeRole.Attendee,
                Status = AttendanceStatus.Accepted
            };
            await _context.MeetingAttendees.AddAsync(attendee);
            await _context.SaveChangesAsync();

            // Act
            var result = await _service.GetUserMeetingsAsync(userId);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2); // One organized, one attending
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}
