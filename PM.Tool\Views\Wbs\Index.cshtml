@model IEnumerable<PM.Tool.Core.Entities.TaskEntity>
@{
    ViewData["Title"] = "Work Breakdown Structure";
    ViewData["Breadcrumbs"] = new List<object>
    {
        new { Text = "Dashboard", Href = Url.Action("Index", "Dashboard"), Icon = "fas fa-home" },
        new { Text = "Projects", Href = Url.Action("Index", "Projects"), Icon = "fas fa-folder-open" },
        new { Text = ViewBag.ProjectName ?? "Unknown Project", Href = Url.Action("Details", "Projects", new { id = ViewBag.ProjectId }), Icon = "fas fa-project-diagram" },
        new { Text = "WBS", Href = "#", Icon = "fas fa-sitemap" }
    };
    var projectId = ViewBag.ProjectId;
    var projectName = ViewBag.ProjectName ?? "Unknown Project";

    // Debug information
    var hasValidProjectId = projectId != null && (int)projectId > 0;
    var hasValidProjectName = !string.IsNullOrEmpty(projectName);
    var hasModel = Model != null;
    var modelCount = Model?.Count() ?? 0;
}

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        // Set global project ID for JavaScript
        window.projectId = @projectId;

        // Debug information
        console.log('WBS Index View Loaded');
        console.log('Project ID:', window.projectId);
        console.log('Project Name:', '@ViewBag.ProjectName');

        // Check if required libraries are loaded
        $(document).ready(function() {
            console.log('jQuery loaded:', typeof $ !== 'undefined');
            console.log('Select2 loaded:', typeof $.fn.select2 !== 'undefined');
            console.log('Sortable loaded:', typeof Sortable !== 'undefined');
        });
    </script>

    <!-- WBS Scripts - Split into multiple files for better organization -->
    @await Html.PartialAsync("_WbsCore")
    @await Html.PartialAsync("_WbsHelpers")
    @await Html.PartialAsync("_WbsRendering")
    @await Html.PartialAsync("_WbsActions")
    @await Html.PartialAsync("_WbsUI")
    @*@await Html.PartialAsync("_WbsExport")*@
}

@section Styles {
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    @await Html.PartialAsync("_WbsStyles")
}

@Html.AntiForgeryToken()

<!-- Enterprise WBS Header -->
<div class="wbs-header-container">
    <div class="wbs-header-content">
        <!-- Main Header Section -->
        <div class="wbs-header-main">
            <div class="wbs-header-title-section">
                <div class="wbs-header-icon">
                    <i class="fas fa-sitemap"></i>
                </div>
                <div class="wbs-header-text">
                    <h1 class="wbs-header-title">Work Breakdown Structure</h1>
                    <p class="wbs-header-subtitle">@ViewBag.ProjectName - Organize and manage your project tasks</p>
                </div>
            </div>

            <!-- Enterprise Action Toolbar -->
            <div class="wbs-toolbar">
                <!-- Add Task Button -->
                <button type="button"
                        onclick="showCreateTaskModal()"
                        class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200 shadow-sm">
                    <i class="fas fa-plus mr-2"></i>
                    Add Task
                </button>

                <!-- Generate Codes Button -->
                <button type="button"
                        onclick="generateWbsCodes()"
                        class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors duration-200 shadow-sm">
                    <i class="fas fa-code mr-2"></i>
                    Generate Codes
                </button>

                <!-- View Options Dropdown -->
                <div class="relative">
                    <button type="button"
                            id="viewDropdownBtn"
                            onclick="toggleDropdown('viewDropdown')"
                            class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg transition-colors duration-200 shadow-sm">
                        <i class="fas fa-eye mr-2"></i>
                        View Options
                        <i class="fas fa-chevron-down ml-2 text-xs"></i>
                    </button>

                    <div id="viewDropdown" class="dropdown-menu hidden">
                        <button onclick="expandAll(); hideDropdown('viewDropdown')" class="dropdown-item">
                            <i class="fas fa-expand-arrows-alt mr-3 text-green-500"></i>Expand All
                        </button>
                        <button onclick="collapseAll(); hideDropdown('viewDropdown')" class="dropdown-item">
                            <i class="fas fa-compress-arrows-alt mr-3 text-orange-500"></i>Collapse All
                        </button>
                        <div class="dropdown-divider"></div>
                        <button onclick="toggleCompactView(); hideDropdown('viewDropdown')" class="dropdown-item">
                            <i class="fas fa-compress mr-3 text-blue-500"></i>Toggle Compact View
                        </button>
                        <button onclick="toggleDetailedView(); hideDropdown('viewDropdown')" class="dropdown-item">
                            <i class="fas fa-list-ul mr-3 text-purple-500"></i>Toggle Detailed View
                        </button>
                        <button onclick="toggleGanttView(); hideDropdown('viewDropdown')" class="dropdown-item">
                            <i class="fas fa-chart-gantt mr-3 text-indigo-500"></i>Gantt View
                        </button>
                        <div class="dropdown-divider"></div>
                        <button onclick="refreshWbsView(); hideDropdown('viewDropdown')" class="dropdown-item">
                            <i class="fas fa-sync-alt mr-3 text-gray-500"></i>Refresh View
                        </button>
                    </div>
                </div>

                <!-- Filter Options Dropdown -->
                <div class="relative">
                    <button type="button"
                            id="filterDropdownBtn"
                            onclick="toggleDropdown('filterDropdown')"
                            class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg transition-colors duration-200 shadow-sm">
                        <i class="fas fa-filter mr-2"></i>
                        Filters
                        <i class="fas fa-chevron-down ml-2 text-xs"></i>
                    </button>

                    <div id="filterDropdown" class="dropdown-menu hidden">
                        <button onclick="filterByStatus('all'); hideDropdown('filterDropdown')" class="dropdown-item">
                            <i class="fas fa-list mr-3 text-gray-500"></i>Show All Tasks
                        </button>
                        <div class="dropdown-divider"></div>
                        <button onclick="filterByStatus('ToDo'); hideDropdown('filterDropdown')" class="dropdown-item">
                            <i class="fas fa-circle mr-3 text-gray-500"></i>To Do Tasks
                        </button>
                        <button onclick="filterByStatus('InProgress'); hideDropdown('filterDropdown')" class="dropdown-item">
                            <i class="fas fa-play mr-3 text-blue-500"></i>In Progress
                        </button>
                        <button onclick="filterByStatus('InReview'); hideDropdown('filterDropdown')" class="dropdown-item">
                            <i class="fas fa-eye mr-3 text-purple-500"></i>In Review
                        </button>
                        <button onclick="filterByStatus('Done'); hideDropdown('filterDropdown')" class="dropdown-item">
                            <i class="fas fa-check mr-3 text-green-500"></i>Completed
                        </button>
                        <div class="dropdown-divider"></div>
                        <button onclick="filterByPriority('Critical'); hideDropdown('filterDropdown')" class="dropdown-item">
                            <i class="fas fa-exclamation-triangle mr-3 text-red-500"></i>Critical Priority
                        </button>
                        <button onclick="filterByPriority('High'); hideDropdown('filterDropdown')" class="dropdown-item">
                            <i class="fas fa-arrow-up mr-3 text-orange-500"></i>High Priority
                        </button>
                        <button onclick="filterOverdueTasks(); hideDropdown('filterDropdown')" class="dropdown-item">
                            <i class="fas fa-clock mr-3 text-red-500"></i>Overdue Tasks
                        </button>
                    </div>
                </div>

                <!-- Export Options Dropdown -->
                <div class="relative">
                    <button type="button"
                            id="exportDropdownBtn"
                            onclick="toggleDropdown('exportDropdown')"
                            class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors duration-200 shadow-sm">
                        <i class="fas fa-download mr-2"></i>
                        Export
                        <i class="fas fa-chevron-down ml-2 text-xs"></i>
                    </button>

                    <div id="exportDropdown" class="dropdown-menu hidden">
                        <button onclick="exportWbs('excel'); hideDropdown('exportDropdown')" class="dropdown-item">
                            <i class="fas fa-file-excel mr-3 text-green-600"></i>Export to Excel
                        </button>
                        <button onclick="exportWbs('pdf'); hideDropdown('exportDropdown')" class="dropdown-item">
                            <i class="fas fa-file-pdf mr-3 text-red-600"></i>Export to PDF
                        </button>
                        <button onclick="exportWbs('csv'); hideDropdown('exportDropdown')" class="dropdown-item">
                            <i class="fas fa-file-csv mr-3 text-blue-600"></i>Export to CSV
                        </button>
                        <button onclick="exportWbs('json'); hideDropdown('exportDropdown')" class="dropdown-item">
                            <i class="fas fa-file-code mr-3 text-purple-600"></i>Export to JSON
                        </button>
                        <div class="dropdown-divider"></div>
                        <button onclick="printWbs(); hideDropdown('exportDropdown')" class="dropdown-item">
                            <i class="fas fa-print mr-3 text-gray-600"></i>Print WBS
                        </button>
                    </div>
                </div>

                <!-- Back to Project Button -->
                <a href="@Url.Action("Details", "Projects", new { id = ViewBag.ProjectId })"
                   class="inline-flex items-center px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white font-medium rounded-lg transition-colors duration-200 shadow-sm">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Project
                </a>
            </div>
        </div>

        <!-- WBS Statistics Bar -->
        <div class="wbs-stats-container">
            <div class="wbs-stat-card stat-card-blue">
                <div class="stat-label">Total Tasks</div>
                <div id="totalTasks" class="stat-value">0</div>
            </div>
            <div class="wbs-stat-card stat-card-green">
                <div class="stat-label">Completed</div>
                <div id="completedTasks" class="stat-value">0</div>
            </div>
            <div class="wbs-stat-card stat-card-yellow">
                <div class="stat-label">In Progress</div>
                <div id="inProgressTasks" class="stat-value">0</div>
            </div>
            <div class="wbs-stat-card stat-card-purple">
                <div class="stat-label">In Review</div>
                <div id="inReviewTasks" class="stat-value">0</div>
            </div>
            <div class="wbs-stat-card stat-card-red">
                <div class="stat-label">Overdue</div>
                <div id="overdueTasks" class="stat-value">0</div>
            </div>
            <div class="wbs-stat-card stat-card-gray">
                <div class="stat-label">Progress</div>
                <div id="overallProgress" class="stat-value">0%</div>
            </div>
        </div>
    </div>
</div>

<!-- WBS Container -->
<div class="wbs-container">
    <div class="wbs-content">

        <!-- WBS Tasks Container -->
        <div class="wbs-tasks-container">
            <div id="wbs-container" class="min-h-96">
                <div id="wbs-tree"></div>

                <!-- Loading State -->
                <div id="wbs-loading" class="hidden flex items-center justify-center py-12">
                    <div class="text-center">
                        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                        <p class="text-gray-600 dark:text-gray-400">Loading WBS structure...</p>
                    </div>
                </div>

                <!-- Empty State -->
                <div id="wbs-empty" class="hidden text-center py-12">
                    <div class="mx-auto h-24 w-24 text-gray-400 mb-4">
                        <i class="fas fa-sitemap text-6xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No tasks found</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">Get started by creating your first task in this project.</p>
                    <button type="button"
                            onclick="showCreateTaskModal()"
                            class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        Create First Task
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@await Html.PartialAsync("_TaskDetailsModal")
@await Html.PartialAsync("_CreateTaskModal")
@await Html.PartialAsync("_ContextMenu")



