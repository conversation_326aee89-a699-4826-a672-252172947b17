using AutoMapper;
using PM.Tool.Core.Entities;
using PM.Tool.Models.ViewModels;

namespace PM.Tool.Application.Mappings
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            // Project mappings
            CreateMap<Project, ProjectSummaryViewModel>()
                .ForMember(dest => dest.MemberCount, opt => opt.MapFrom(src => src.Members.Count))
                .ForMember(dest => dest.TaskCount, opt => opt.MapFrom(src => src.Tasks.Count));

            // Task mappings
            CreateMap<TaskEntity, TaskSummaryViewModel>()
                .ForMember(dest => dest.ProjectName, opt => opt.MapFrom(src => src.Project.Name));

            // Milestone mappings
            CreateMap<Milestone, MilestoneViewModel>()
                .ForMember(dest => dest.ProjectName, opt => opt.MapFrom(src => src.Project.Name));

            // Notification mappings
            CreateMap<Notification, NotificationViewModel>()
                .ForMember(dest => dest.RelatedProjectName, opt => opt.MapFrom(src => src.RelatedProject.Name))
                .ForMember(dest => dest.RelatedTaskTitle, opt => opt.MapFrom(src => src.RelatedTask.Title));
        }
    }
}
