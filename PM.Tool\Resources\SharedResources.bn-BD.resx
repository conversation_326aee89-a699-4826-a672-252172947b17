<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>

  <!-- Common UI Elements - Bengali (Bangladesh) -->
  <data name="Common.Save" xml:space="preserve">
    <value>সংরক্ষণ করুন</value>
  </data>
  <data name="Common.Cancel" xml:space="preserve">
    <value>বাতিল</value>
  </data>
  <data name="Common.Delete" xml:space="preserve">
    <value>মুছে ফেলুন</value>
  </data>
  <data name="Common.Edit" xml:space="preserve">
    <value>সম্পাদনা</value>
  </data>
  <data name="Common.Create" xml:space="preserve">
    <value>তৈরি করুন</value>
  </data>
  <data name="Common.Update" xml:space="preserve">
    <value>আপডেট</value>
  </data>
  <data name="Common.Details" xml:space="preserve">
    <value>বিস্তারিত</value>
  </data>
  <data name="Common.Back" xml:space="preserve">
    <value>পিছনে</value>
  </data>
  <data name="Common.Next" xml:space="preserve">
    <value>পরবর্তী</value>
  </data>
  <data name="Common.Previous" xml:space="preserve">
    <value>পূর্ববর্তী</value>
  </data>
  <data name="Common.Search" xml:space="preserve">
    <value>অনুসন্ধান</value>
  </data>
  <data name="Common.Filter" xml:space="preserve">
    <value>ফিল্টার</value>
  </data>
  <data name="Common.Export" xml:space="preserve">
    <value>রপ্তানি</value>
  </data>
  <data name="Common.Import" xml:space="preserve">
    <value>আমদানি</value>
  </data>
  <data name="Common.Print" xml:space="preserve">
    <value>মুদ্রণ</value>
  </data>
  <data name="Common.Download" xml:space="preserve">
    <value>ডাউনলোড</value>
  </data>
  <data name="Common.Upload" xml:space="preserve">
    <value>আপলোড</value>
  </data>
  <data name="Common.Yes" xml:space="preserve">
    <value>হ্যাঁ</value>
  </data>
  <data name="Common.No" xml:space="preserve">
    <value>না</value>
  </data>
  <data name="Common.OK" xml:space="preserve">
    <value>ঠিক আছে</value>
  </data>
  <data name="Common.Close" xml:space="preserve">
    <value>বন্ধ</value>
  </data>
  <data name="Common.Loading" xml:space="preserve">
    <value>লোড হচ্ছে...</value>
  </data>
  <data name="Common.NoData" xml:space="preserve">
    <value>কোন তথ্য উপলব্ধ নেই</value>
  </data>
  <data name="Common.Error" xml:space="preserve">
    <value>ত্রুটি</value>
  </data>
  <data name="Common.Success" xml:space="preserve">
    <value>সফল</value>
  </data>
  <data name="Common.Warning" xml:space="preserve">
    <value>সতর্কতা</value>
  </data>
  <data name="Common.Information" xml:space="preserve">
    <value>তথ্য</value>
  </data>

  <!-- Navigation - Bengali (Bangladesh) -->
  <data name="Nav.Dashboard" xml:space="preserve">
    <value>ড্যাশবোর্ড</value>
  </data>
  <data name="Nav.Projects" xml:space="preserve">
    <value>প্রকল্পসমূহ</value>
  </data>
  <data name="Nav.Tasks" xml:space="preserve">
    <value>কাজসমূহ</value>
  </data>
  <data name="Nav.Management" xml:space="preserve">
    <value>ব্যবস্থাপনা</value>
  </data>
  <data name="Nav.Analytics" xml:space="preserve">
    <value>বিশ্লেষণ</value>
  </data>
  <data name="Nav.Resources" xml:space="preserve">
    <value>সম্পদ</value>
  </data>
  <data name="Nav.Risks" xml:space="preserve">
    <value>ঝুঁকি</value>
  </data>
  <data name="Nav.Meetings" xml:space="preserve">
    <value>সভা</value>
  </data>
  <data name="Nav.Requirements" xml:space="preserve">
    <value>প্রয়োজনীয়তা</value>
  </data>
  <data name="Nav.Backlog" xml:space="preserve">
    <value>ব্যাকলগ</value>
  </data>
  <data name="Nav.Kanban" xml:space="preserve">
    <value>কানবান বোর্ড</value>
  </data>
  <data name="Nav.Documentation" xml:space="preserve">
    <value>ডকুমেন্টেশন</value>
  </data>
  <data name="Nav.SkillsManagement" xml:space="preserve">
    <value>দক্ষতা ব্যবস্থাপনা</value>
  </data>
  <data name="Nav.ResourceUtilization" xml:space="preserve">
    <value>সম্পদ ব্যবহার</value>
  </data>
  <data name="Nav.MeetingCalendar" xml:space="preserve">
    <value>সভার ক্যালেন্ডার</value>
  </data>
  <data name="Nav.ActionItems" xml:space="preserve">
    <value>কর্ম আইটেম</value>
  </data>
  <data name="Nav.AnalyticsDashboard" xml:space="preserve">
    <value>বিশ্লেষণ ড্যাশবোর্ড</value>
  </data>
  <data name="Nav.AdvancedReports" xml:space="preserve">
    <value>উন্নত রিপোর্ট</value>
  </data>
  <data name="Nav.TeamAnalytics" xml:space="preserve">
    <value>দল বিশ্লেষণ</value>
  </data>
  <data name="Nav.BurndownCharts" xml:space="preserve">
    <value>বার্নডাউন চার্ট</value>
  </data>
  <data name="Nav.VelocityCharts" xml:space="preserve">
    <value>বেগ চার্ট</value>
  </data>
  <data name="Nav.ExportData" xml:space="preserve">
    <value>তথ্য রপ্তানি</value>
  </data>

  <!-- Project Management - Bengali (Bangladesh) -->
  <data name="Project.Title" xml:space="preserve">
    <value>শিরোনাম</value>
  </data>
  <data name="Project.Description" xml:space="preserve">
    <value>বিবরণ</value>
  </data>
  <data name="Project.StartDate" xml:space="preserve">
    <value>শুরুর তারিখ</value>
  </data>
  <data name="Project.EndDate" xml:space="preserve">
    <value>শেষের তারিখ</value>
  </data>
  <data name="Project.Status" xml:space="preserve">
    <value>অবস্থা</value>
  </data>
  <data name="Project.Priority" xml:space="preserve">
    <value>অগ্রাধিকার</value>
  </data>
  <data name="Project.Budget" xml:space="preserve">
    <value>বাজেট</value>
  </data>
  <data name="Project.Progress" xml:space="preserve">
    <value>অগ্রগতি</value>
  </data>
  <data name="Project.Manager" xml:space="preserve">
    <value>প্রকল্প ব্যবস্থাপক</value>
  </data>
  <data name="Project.Team" xml:space="preserve">
    <value>দল</value>
  </data>

  <!-- Task Management - Bengali (Bangladesh) -->
  <data name="Task.Title" xml:space="preserve">
    <value>কাজের শিরোনাম</value>
  </data>
  <data name="Task.Description" xml:space="preserve">
    <value>কাজের বিবরণ</value>
  </data>
  <data name="Task.AssignedTo" xml:space="preserve">
    <value>বরাদ্দকৃত</value>
  </data>
  <data name="Task.DueDate" xml:space="preserve">
    <value>নির্ধারিত তারিখ</value>
  </data>
  <data name="Task.EstimatedHours" xml:space="preserve">
    <value>আনুমানিক ঘন্টা</value>
  </data>
  <data name="Task.ActualHours" xml:space="preserve">
    <value>প্রকৃত ঘন্টা</value>
  </data>
  <data name="Task.StoryPoints" xml:space="preserve">
    <value>গল্প পয়েন্ট</value>
  </data>

  <!-- Agile Terms - Bengali (Bangladesh) -->
  <data name="Agile.Epic" xml:space="preserve">
    <value>মহাকাব্য</value>
  </data>
  <data name="Agile.UserStory" xml:space="preserve">
    <value>ব্যবহারকারীর গল্প</value>
  </data>
  <data name="Agile.Sprint" xml:space="preserve">
    <value>স্প্রিন্ট</value>
  </data>
  <data name="Agile.Backlog" xml:space="preserve">
    <value>ব্যাকলগ</value>
  </data>
  <data name="Agile.Kanban" xml:space="preserve">
    <value>কানবান</value>
  </data>
  <data name="Agile.Scrum" xml:space="preserve">
    <value>স্ক্রাম</value>
  </data>
  <data name="Agile.Velocity" xml:space="preserve">
    <value>বেগ</value>
  </data>
  <data name="Agile.Burndown" xml:space="preserve">
    <value>বার্নডাউন</value>
  </data>

  <!-- Status Values - Bengali (Bangladesh) -->
  <data name="Status.Active" xml:space="preserve">
    <value>সক্রিয়</value>
  </data>
  <data name="Status.Inactive" xml:space="preserve">
    <value>নিষ্ক্রিয়</value>
  </data>
  <data name="Status.Completed" xml:space="preserve">
    <value>সম্পন্ন</value>
  </data>
  <data name="Status.InProgress" xml:space="preserve">
    <value>চলমান</value>
  </data>
  <data name="Status.Pending" xml:space="preserve">
    <value>অপেক্ষমান</value>
  </data>
  <data name="Status.Cancelled" xml:space="preserve">
    <value>বাতিল</value>
  </data>

  <!-- Priority Values - Bengali (Bangladesh) -->
  <data name="Priority.Critical" xml:space="preserve">
    <value>গুরুত্বপূর্ণ</value>
  </data>
  <data name="Priority.High" xml:space="preserve">
    <value>উচ্চ</value>
  </data>
  <data name="Priority.Medium" xml:space="preserve">
    <value>মধ্যম</value>
  </data>
  <data name="Priority.Low" xml:space="preserve">
    <value>নিম্ন</value>
  </data>

  <!-- Messages - Bengali (Bangladesh) -->
  <data name="Message.SaveSuccess" xml:space="preserve">
    <value>আইটেম সফলভাবে সংরক্ষিত হয়েছে</value>
  </data>
  <data name="Message.DeleteSuccess" xml:space="preserve">
    <value>আইটেম সফলভাবে মুছে ফেলা হয়েছে</value>
  </data>
  <data name="Message.UpdateSuccess" xml:space="preserve">
    <value>আইটেম সফলভাবে আপডেট হয়েছে</value>
  </data>
  <data name="Message.ErrorOccurred" xml:space="preserve">
    <value>একটি ত্রুটি ঘটেছে। অনুগ্রহ করে আবার চেষ্টা করুন।</value>
  </data>
  <data name="Message.ConfirmDelete" xml:space="preserve">
    <value>আপনি কি নিশ্চিত যে আপনি এই আইটেমটি মুছে ফেলতে চান?</value>
  </data>

  <!-- Meeting Management - Bengali (Bangladesh) -->
  <data name="Meeting.Title" xml:space="preserve">
    <value>সভার শিরোনাম</value>
  </data>
  <data name="Meeting.Description" xml:space="preserve">
    <value>বিবরণ</value>
  </data>
  <data name="Meeting.StartTime" xml:space="preserve">
    <value>শুরুর সময়</value>
  </data>
  <data name="Meeting.EndTime" xml:space="preserve">
    <value>শেষের সময়</value>
  </data>
  <data name="Meeting.Location" xml:space="preserve">
    <value>অবস্থান</value>
  </data>
  <data name="Meeting.Type" xml:space="preserve">
    <value>সভার ধরন</value>
  </data>
  <data name="Meeting.Status" xml:space="preserve">
    <value>অবস্থা</value>
  </data>
  <data name="Meeting.Organizer" xml:space="preserve">
    <value>আয়োজক</value>
  </data>
  <data name="Meeting.Attendees" xml:space="preserve">
    <value>অংশগ্রহণকারী</value>
  </data>
  <data name="Meeting.ActionItems" xml:space="preserve">
    <value>কর্ম আইটেম</value>
  </data>
  <data name="Meeting.Documents" xml:space="preserve">
    <value>নথিপত্র</value>
  </data>
  <data name="Meeting.Minutes" xml:space="preserve">
    <value>সভার কার্যবিবরণী</value>
  </data>
  <data name="Meeting.Agenda" xml:space="preserve">
    <value>কর্মসূচি</value>
  </data>

  <!-- Requirements Management - Bengali (Bangladesh) -->
  <data name="Requirement.Title" xml:space="preserve">
    <value>প্রয়োজনীয়তার শিরোনাম</value>
  </data>
  <data name="Requirement.Description" xml:space="preserve">
    <value>বিবরণ</value>
  </data>
  <data name="Requirement.Type" xml:space="preserve">
    <value>ধরন</value>
  </data>
  <data name="Requirement.Priority" xml:space="preserve">
    <value>অগ্রাধিকার</value>
  </data>
  <data name="Requirement.Status" xml:space="preserve">
    <value>অবস্থা</value>
  </data>
  <data name="Requirement.Source" xml:space="preserve">
    <value>উৎস</value>
  </data>
  <data name="Requirement.Stakeholder" xml:space="preserve">
    <value>স্টেকহোল্ডার</value>
  </data>
  <data name="Requirement.AcceptanceCriteria" xml:space="preserve">
    <value>গ্রহণযোগ্যতার মানদণ্ড</value>
  </data>
  <data name="Requirement.BusinessValue" xml:space="preserve">
    <value>ব্যবসায়িক মূল্য</value>
  </data>
  <data name="Requirement.Comments" xml:space="preserve">
    <value>মন্তব্য</value>
  </data>
  <data name="Requirement.Attachments" xml:space="preserve">
    <value>সংযুক্তি</value>
  </data>
  <data name="Requirement.Changes" xml:space="preserve">
    <value>পরিবর্তনের ইতিহাস</value>
  </data>
  <data name="Requirement.Tasks" xml:space="preserve">
    <value>সম্পর্কিত কাজ</value>
  </data>

  <!-- Risk Management - Bengali (Bangladesh) -->
  <data name="Risk.Title" xml:space="preserve">
    <value>ঝুঁকির শিরোনাম</value>
  </data>
  <data name="Risk.Description" xml:space="preserve">
    <value>বিবরণ</value>
  </data>
  <data name="Risk.Category" xml:space="preserve">
    <value>বিভাগ</value>
  </data>
  <data name="Risk.Probability" xml:space="preserve">
    <value>সম্ভাবনা</value>
  </data>
  <data name="Risk.Impact" xml:space="preserve">
    <value>প্রভাব</value>
  </data>
  <data name="Risk.Score" xml:space="preserve">
    <value>ঝুঁকির স্কোর</value>
  </data>
  <data name="Risk.Status" xml:space="preserve">
    <value>অবস্থা</value>
  </data>
  <data name="Risk.Owner" xml:space="preserve">
    <value>ঝুঁকির মালিক</value>
  </data>
  <data name="Risk.MitigationPlan" xml:space="preserve">
    <value>প্রশমন পরিকল্পনা</value>
  </data>
  <data name="Risk.MitigationActions" xml:space="preserve">
    <value>প্রশমন কার্যক্রম</value>
  </data>
  <data name="Risk.ContingencyPlan" xml:space="preserve">
    <value>জরুরি পরিকল্পনা</value>
  </data>

  <!-- Resource Management - Bengali (Bangladesh) -->
  <data name="Resource.Name" xml:space="preserve">
    <value>সম্পদের নাম</value>
  </data>
  <data name="Resource.Type" xml:space="preserve">
    <value>ধরন</value>
  </data>
  <data name="Resource.Department" xml:space="preserve">
    <value>বিভাগ</value>
  </data>
  <data name="Resource.Location" xml:space="preserve">
    <value>অবস্থান</value>
  </data>
  <data name="Resource.HourlyRate" xml:space="preserve">
    <value>ঘণ্টার হার</value>
  </data>
  <data name="Resource.Capacity" xml:space="preserve">
    <value>ক্ষমতা</value>
  </data>
  <data name="Resource.Skills" xml:space="preserve">
    <value>দক্ষতা</value>
  </data>
  <data name="Resource.Availability" xml:space="preserve">
    <value>উপলব্ধতা</value>
  </data>
  <data name="Resource.Utilization" xml:space="preserve">
    <value>ব্যবহার</value>
  </data>
  <data name="Resource.Allocation" xml:space="preserve">
    <value>বরাদ্দ</value>
  </data>

  <!-- Agile/Scrum Terms - Bengali (Bangladesh) -->
  <data name="Agile.SprintPlanning" xml:space="preserve">
    <value>স্প্রিন্ট পরিকল্পনা</value>
  </data>
  <data name="Agile.SprintReview" xml:space="preserve">
    <value>স্প্রিন্ট পর্যালোচনা</value>
  </data>
  <data name="Agile.SprintRetrospective" xml:space="preserve">
    <value>স্প্রিন্ট পূর্ণাঙ্গ পর্যালোচনা</value>
  </data>
  <data name="Agile.DailyStandup" xml:space="preserve">
    <value>দৈনিক স্ট্যান্ডআপ</value>
  </data>
  <data name="Agile.ProductBacklog" xml:space="preserve">
    <value>পণ্য ব্যাকলগ</value>
  </data>
  <data name="Agile.SprintBacklog" xml:space="preserve">
    <value>স্প্রিন্ট ব্যাকলগ</value>
  </data>
  <data name="Agile.Definition" xml:space="preserve">
    <value>সম্পন্নের সংজ্ঞা</value>
  </data>
  <data name="Agile.StoryPoints" xml:space="preserve">
    <value>গল্প পয়েন্ট</value>
  </data>
  <data name="Agile.SprintStarted" xml:space="preserve">
    <value>স্প্রিন্ট সফলভাবে শুরু হয়েছে</value>
  </data>
  <data name="Agile.SprintCompleted" xml:space="preserve">
    <value>স্প্রিন্ট সফলভাবে সম্পন্ন হয়েছে</value>
  </data>

  <!-- Enum Values - Meeting Types - Bengali (Bangladesh) -->
  <data name="Enum.MeetingType.General" xml:space="preserve">
    <value>সাধারণ</value>
  </data>
  <data name="Enum.MeetingType.Standup" xml:space="preserve">
    <value>দৈনিক স্ট্যান্ডআপ</value>
  </data>
  <data name="Enum.MeetingType.Planning" xml:space="preserve">
    <value>পরিকল্পনা</value>
  </data>
  <data name="Enum.MeetingType.Review" xml:space="preserve">
    <value>পর্যালোচনা</value>
  </data>
  <data name="Enum.MeetingType.Retrospective" xml:space="preserve">
    <value>পূর্ণাঙ্গ পর্যালোচনা</value>
  </data>
  <data name="Enum.MeetingType.Stakeholder" xml:space="preserve">
    <value>স্টেকহোল্ডার</value>
  </data>
  <data name="Enum.MeetingType.Requirements" xml:space="preserve">
    <value>প্রয়োজনীয়তা</value>
  </data>
  <data name="Enum.MeetingType.Technical" xml:space="preserve">
    <value>প্রযুক্তিগত</value>
  </data>
  <data name="Enum.MeetingType.Status" xml:space="preserve">
    <value>অবস্থা</value>
  </data>

  <!-- Enum Values - Meeting Status - Bengali (Bangladesh) -->
  <data name="Enum.MeetingStatus.Scheduled" xml:space="preserve">
    <value>নির্ধারিত</value>
  </data>
  <data name="Enum.MeetingStatus.InProgress" xml:space="preserve">
    <value>চলমান</value>
  </data>
  <data name="Enum.MeetingStatus.Completed" xml:space="preserve">
    <value>সম্পন্ন</value>
  </data>
  <data name="Enum.MeetingStatus.Cancelled" xml:space="preserve">
    <value>বাতিল</value>
  </data>
  <data name="Enum.MeetingStatus.Postponed" xml:space="preserve">
    <value>স্থগিত</value>
  </data>

  <!-- Enum Values - Requirement Types - Bengali (Bangladesh) -->
  <data name="Enum.RequirementType.Functional" xml:space="preserve">
    <value>কার্যকরী</value>
  </data>
  <data name="Enum.RequirementType.NonFunctional" xml:space="preserve">
    <value>অ-কার্যকরী</value>
  </data>
  <data name="Enum.RequirementType.Business" xml:space="preserve">
    <value>ব্যবসায়িক</value>
  </data>
  <data name="Enum.RequirementType.Technical" xml:space="preserve">
    <value>প্রযুক্তিগত</value>
  </data>
  <data name="Enum.RequirementType.Performance" xml:space="preserve">
    <value>কর্মক্ষমতা</value>
  </data>
  <data name="Enum.RequirementType.Security" xml:space="preserve">
    <value>নিরাপত্তা</value>
  </data>
  <data name="Enum.RequirementType.Usability" xml:space="preserve">
    <value>ব্যবহারযোগ্যতা</value>
  </data>
  <data name="Enum.RequirementType.Compliance" xml:space="preserve">
    <value>সম্মতি</value>
  </data>
  <data name="Enum.RequirementType.Integration" xml:space="preserve">
    <value>একীকরণ</value>
  </data>

  <!-- Enum Values - Risk Categories - Bengali (Bangladesh) -->
  <data name="Enum.RiskCategory.Technical" xml:space="preserve">
    <value>প্রযুক্তিগত</value>
  </data>
  <data name="Enum.RiskCategory.Schedule" xml:space="preserve">
    <value>সময়সূচি</value>
  </data>
  <data name="Enum.RiskCategory.Budget" xml:space="preserve">
    <value>বাজেট</value>
  </data>
  <data name="Enum.RiskCategory.Resource" xml:space="preserve">
    <value>সম্পদ</value>
  </data>
  <data name="Enum.RiskCategory.Quality" xml:space="preserve">
    <value>গুণমান</value>
  </data>
  <data name="Enum.RiskCategory.External" xml:space="preserve">
    <value>বাহ্যিক</value>
  </data>
  <data name="Enum.RiskCategory.Organizational" xml:space="preserve">
    <value>সাংগঠনিক</value>
  </data>

  <!-- Enum Values - Resource Types - Bengali (Bangladesh) -->
  <data name="Enum.ResourceType.Human" xml:space="preserve">
    <value>মানব সম্পদ</value>
  </data>
  <data name="Enum.ResourceType.Equipment" xml:space="preserve">
    <value>সরঞ্জাম</value>
  </data>
  <data name="Enum.ResourceType.Material" xml:space="preserve">
    <value>উপাদান</value>
  </data>
  <data name="Enum.ResourceType.Software" xml:space="preserve">
    <value>সফটওয়্যার</value>
  </data>
  <data name="Enum.ResourceType.Facility" xml:space="preserve">
    <value>সুবিধা</value>
  </data>

  <!-- WBS Specific Messages - Bengali (Bangladesh) -->
  <data name="WBS.ErrorLoadingStructure" xml:space="preserve">
    <value>WBS কাঠামো লোড করতে ত্রুটি</value>
  </data>
  <data name="WBS.TaskCreatedSuccessfully" xml:space="preserve">
    <value>কাজ সফলভাবে তৈরি হয়েছে</value>
  </data>
  <data name="WBS.TaskDeletedSuccessfully" xml:space="preserve">
    <value>কাজ সফলভাবে মুছে ফেলা হয়েছে</value>
  </data>
  <data name="WBS.TaskDuplicatedSuccessfully" xml:space="preserve">
    <value>কাজ সফলভাবে নকল করা হয়েছে</value>
  </data>
  <data name="WBS.TaskMovedSuccessfully" xml:space="preserve">
    <value>কাজ সফলভাবে {0} সরানো হয়েছে</value>
  </data>
  <data name="WBS.TaskStatusUpdated" xml:space="preserve">
    <value>কাজের অবস্থা {0} এ আপডেট হয়েছে</value>
  </data>
  <data name="WBS.WbsCodesGenerated" xml:space="preserve">
    <value>WBS কোড সফলভাবে তৈরি হয়েছে</value>
  </data>
  <data name="WBS.ExportingTo" xml:space="preserve">
    <value>WBS {0} এ রপ্তানি করা হচ্ছে...</value>
  </data>
  <data name="WBS.ExportingTask" xml:space="preserve">
    <value>কাজ রপ্তানি করা হচ্ছে...</value>
  </data>
  <data name="WBS.PrintingWbs" xml:space="preserve">
    <value>প্রিন্ট ডায়ালগ খোলা হচ্ছে...</value>
  </data>
  <data name="WBS.CompactViewEnabled" xml:space="preserve">
    <value>সংক্ষিপ্ত দৃশ্য সক্রিয় করা হয়েছে</value>
  </data>
  <data name="WBS.NormalViewEnabled" xml:space="preserve">
    <value>সাধারণ দৃশ্য সক্রিয় করা হয়েছে</value>
  </data>
  <data name="WBS.DetailedViewEnabled" xml:space="preserve">
    <value>বিস্তারিত দৃশ্য সক্রিয় করা হয়েছে</value>
  </data>
  <data name="WBS.RefreshingView" xml:space="preserve">
    <value>WBS দৃশ্য রিফ্রেশ করা হচ্ছে...</value>
  </data>
  <data name="WBS.FilteredToShow" xml:space="preserve">
    <value>{0} কাজ দেখানোর জন্য ফিল্টার করা হয়েছে</value>
  </data>
  <data name="WBS.ShowingAllTasks" xml:space="preserve">
    <value>সব কাজ দেখানো হচ্ছে</value>
  </data>
  <data name="WBS.FilteredOverdue" xml:space="preserve">
    <value>মেয়াদোত্তীর্ণ কাজ দেখানোর জন্য ফিল্টার করা হয়েছে</value>
  </data>
  <data name="WBS.TaskTitleRequired" xml:space="preserve">
    <value>কাজের শিরোনাম প্রয়োজন</value>
  </data>
  <data name="WBS.ProjectIdMissing" xml:space="preserve">
    <value>প্রকল্প আইডি অনুপস্থিত</value>
  </data>
  <data name="WBS.ValidationError" xml:space="preserve">
    <value>যাচাইকরণ ত্রুটি। অনুগ্রহ করে আপনার ইনপুট পরীক্ষা করুন।</value>
  </data>
  <data name="WBS.AccessDenied" xml:space="preserve">
    <value>প্রবেশাধিকার অস্বীকৃত। অনুগ্রহ করে পৃষ্ঠা রিফ্রেশ করে আবার চেষ্টা করুন।</value>
  </data>
  <data name="WBS.RequestFormatError" xml:space="preserve">
    <value>অনুরোধ ফরম্যাট ত্রুটি। অনুগ্রহ করে আবার চেষ্টা করুন।</value>
  </data>
  <data name="WBS.ErrorCreatingTask" xml:space="preserve">
    <value>কাজ তৈরি করতে ত্রুটি</value>
  </data>
  <data name="WBS.ErrorLoadingTaskDetails" xml:space="preserve">
    <value>কাজের বিস্তারিত লোড করতে ত্রুটি</value>
  </data>
  <data name="WBS.ErrorDeletingTask" xml:space="preserve">
    <value>কাজ মুছে ফেলতে ত্রুটি</value>
  </data>
  <data name="WBS.ErrorDuplicatingTask" xml:space="preserve">
    <value>কাজ নকল করতে ত্রুটি</value>
  </data>
  <data name="WBS.ErrorMovingTask" xml:space="preserve">
    <value>কাজ {0} সরাতে ত্রুটি</value>
  </data>
  <data name="WBS.ErrorUpdatingTaskStatus" xml:space="preserve">
    <value>কাজের অবস্থা আপডেট করতে ত্রুটি</value>
  </data>
  <data name="WBS.ErrorGeneratingCodes" xml:space="preserve">
    <value>WBS কোড তৈরি করতে ত্রুটি</value>
  </data>
  <data name="WBS.ConfirmDeleteTask" xml:space="preserve">
    <value>আপনি কি নিশ্চিত যে আপনি এই কাজটি মুছে ফেলতে চান? এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না।</value>
  </data>
  <data name="WBS.ConfirmDuplicateTask" xml:space="preserve">
    <value>এই কাজের একটি নকল তৈরি করবেন?</value>
  </data>
  <data name="WBS.ConfirmGenerateCodes" xml:space="preserve">
    <value>এটি সব WBS কোড পুনরায় তৈরি করবে। চালিয়ে যাবেন?</value>
  </data>
  <data name="WBS.CreateNewTask" xml:space="preserve">
    <value>নতুন কাজ তৈরি করুন</value>
  </data>
  <data name="WBS.CreateChildTaskFor" xml:space="preserve">
    <value>{0} এর জন্য শিশু কাজ তৈরি করুন</value>
  </data>
  <data name="WBS.TaskNotFound" xml:space="preserve">
    <value>কাজ পাওয়া যায়নি</value>
  </data>
  <data name="WBS.UnsupportedExportFormat" xml:space="preserve">
    <value>অসমর্থিত রপ্তানি ফরম্যাট</value>
  </data>
  <data name="WBS.GanttViewComingSoon" xml:space="preserve">
    <value>গ্যান্ট ভিউ ফিচার শীঘ্রই আসছে!</value>
  </data>

  <!-- WBS Task Actions - Bengali (Bangladesh) -->
  <data name="WBS.Action.ViewDetails" xml:space="preserve">
    <value>বিস্তারিত দেখুন</value>
  </data>
  <data name="WBS.Action.EditTask" xml:space="preserve">
    <value>কাজ সম্পাদনা করুন</value>
  </data>
  <data name="WBS.Action.Duplicate" xml:space="preserve">
    <value>নকল করুন</value>
  </data>
  <data name="WBS.Action.AddChild" xml:space="preserve">
    <value>শিশু যোগ করুন</value>
  </data>
  <data name="WBS.Action.MoreActions" xml:space="preserve">
    <value>আরও কাজ</value>
  </data>
  <data name="WBS.Action.MoveUp" xml:space="preserve">
    <value>উপরে সরান</value>
  </data>
  <data name="WBS.Action.MoveDown" xml:space="preserve">
    <value>নিচে সরান</value>
  </data>
  <data name="WBS.Action.StartTask" xml:space="preserve">
    <value>কাজ শুরু করুন</value>
  </data>
  <data name="WBS.Action.MarkInReview" xml:space="preserve">
    <value>পর্যালোচনায় চিহ্নিত করুন</value>
  </data>
  <data name="WBS.Action.MarkComplete" xml:space="preserve">
    <value>সম্পূর্ণ হিসেবে চিহ্নিত করুন</value>
  </data>
  <data name="WBS.Action.CancelTask" xml:space="preserve">
    <value>কাজ বাতিল করুন</value>
  </data>
  <data name="WBS.Action.ExportTask" xml:space="preserve">
    <value>কাজ রপ্তানি করুন</value>
  </data>
  <data name="WBS.Action.DeleteTask" xml:space="preserve">
    <value>কাজ মুছে ফেলুন</value>
  </data>

  <!-- WBS Labels - Bengali (Bangladesh) -->
  <data name="WBS.Label.Progress" xml:space="preserve">
    <value>অগ্রগতি</value>
  </data>
  <data name="WBS.Label.Unassigned" xml:space="preserve">
    <value>অনির্ধারিত</value>
  </data>
  <data name="WBS.Label.Overdue" xml:space="preserve">
    <value>মেয়াদোত্তীর্ণ</value>
  </data>
  <data name="WBS.Label.Start" xml:space="preserve">
    <value>শুরু</value>
  </data>
  <data name="WBS.Label.Due" xml:space="preserve">
    <value>নির্ধারিত</value>
  </data>
  <data name="WBS.Label.GeneratedOn" xml:space="preserve">
    <value>তৈরি হয়েছে</value>
  </data>
  <data name="WBS.Label.WorkBreakdownStructure" xml:space="preserve">
    <value>কাজের ভাঙ্গন কাঠামো</value>
  </data>

</root>
