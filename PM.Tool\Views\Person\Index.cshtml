@model IEnumerable<PM.Tool.Core.Entities.Person>
@using PM.Tool.Core.Entities

@{
    ViewData["Title"] = "People Management";
    ViewData["PageTitle"] = "People Management";
    ViewData["PageDescription"] = "Unified management of all people in the organization - employees, contractors, stakeholders, vendors, and partners.";
}

<!-- <PERSON>er with Enhanced Stats -->
<div class="mb-8">
    <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold color: var(--color-text-primary) mb-2">People Management</h1>
            <p class="color: var(--color-text-secondary)">Manage all people in your organization from one unified platform</p>
        </div>
        <div class="flex space-x-3 mt-4 lg:mt-0">
            <a href="@Url.Action("Create")" class="btn-enterprise btn-primary-enterprise">
                <i class="fas fa-plus mr-2"></i>
                Add Person
            </a>
            <a href="@Url.Action("Analytics")" class="btn-enterprise btn-secondary-enterprise">
                <i class="fas fa-chart-bar mr-2"></i>
                Analytics
            </a>
        </div>
    </div>

    <!-- Grid-Based Stats -->
    <div class="stats-grid mb-4">
        <div class="stat-card">
            <div class="stat-content">
                <div class="stat-icon stat-primary">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-info">
                    <h3 class="stat-number">@Model.Count()</h3>
                    <p class="stat-label">Total People</p>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-content">
                <div class="stat-icon stat-success">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="stat-info">
                    <h3 class="stat-number">@Model.Count(p => p.Status == PersonStatus.Active)</h3>
                    <p class="stat-label">Active</p>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-content">
                <div class="stat-icon stat-warning">
                    <i class="fas fa-id-badge"></i>
                </div>
                <div class="stat-info">
                    <h3 class="stat-number">@Model.Count(p => p.Type == PersonType.Internal)</h3>
                    <p class="stat-label">Employees</p>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-content">
                <div class="stat-icon stat-info">
                    <i class="fas fa-user-cog"></i>
                </div>
                <div class="stat-info">
                    <h3 class="stat-number">@Model.Count(p => p.HasSystemAccess)</h3>
                    <p class="stat-label">System Users</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Filters and Search -->
<div class="card-enterprise mb-6">
    <!-- Search Row -->
    <div class="row mb-4">
        <div class="col-md-8 mb-3 mb-md-0">
            <label class="form-label-enterprise sr-only" for="searchInput">Search People</label>
            <div style="position: relative;">
                <input type="text" id="searchInput" placeholder="Search people by name, email, or code..."
                       class="form-input-enterprise" style="padding-left: 2.5rem; width: 100%;">
                <div style="position: absolute; top: 50%; left: 0.75rem; transform: translateY(-50%); pointer-events: none;">
                    <i class="fas fa-search" style="color: var(--color-text-muted);"></i>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <label class="form-label-enterprise sr-only" for="statusFilter">Filter by Status</label>
            <select id="statusFilter" class="form-input-enterprise select2-enabled" style="width: 100%;"
                    data-select2="true"
                    data-select2-options='{"placeholder": "All Status", "allowClear": false, "minimumResultsForSearch": -1}'>
                <option value="">All Status</option>
                <option value="Active">Active</option>
                <option value="Inactive">Inactive</option>
                <option value="OnLeave">On Leave</option>
                <option value="Terminated">Terminated</option>
            </select>
        </div>
    </div>

    <!-- Filter Chips Row -->
    <div class="mb-0">
        <div style="margin-bottom: 0.75rem;">
            <span class="text-sm font-medium" style="color: var(--color-text-secondary);">Filter by Type:</span>
        </div>
        <div style="display: block;">
            <button class="filter-chip me-2 mb-2" data-filter="all">All Types</button>
            <button class="filter-chip me-2 mb-2" data-filter="Internal">Employees</button>
            <button class="filter-chip me-2 mb-2" data-filter="Contractor">Contractors</button>
            <button class="filter-chip me-2 mb-2" data-filter="External">Stakeholders</button>
            <button class="filter-chip me-2 mb-2" data-filter="Vendor">Vendors</button>
        </div>
    </div>
</div>

<style>
/* CSS Grid Stats Layout */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--color-border-strong);
}

[data-theme="dark"] .stat-card {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .stat-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

.stat-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    flex-shrink: 0;
}

.stat-primary {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.stat-success {
    background: linear-gradient(135deg, #10b981, #059669);
}

.stat-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-info {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.stat-info {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--color-text-primary);
    margin: 0 0 0.25rem 0;
    line-height: 1.2;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--color-text-secondary);
    font-weight: 500;
    margin: 0;
    line-height: 1.4;
}

/* Responsive Grid Adjustments */
@@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .stat-card {
        padding: 1.25rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .stat-number {
        font-size: 1.75rem;
    }
}

@@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-content {
        gap: 0.75rem;
    }
}

.filter-chip {
    display: inline-block;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.4;
    border-radius: 0.5rem;
    border: 1px solid var(--color-border);
    background-color: var(--color-surface);
    color: var(--color-text-secondary);
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    text-decoration: none;
    vertical-align: top;
}

.filter-chip:hover {
    background-color: var(--color-bg-secondary);
    border-color: var(--color-border-strong);
    color: var(--color-text-primary);
    transform: translateY(-1px);
    text-decoration: none;
}

.filter-chip.active {
    background-color: var(--color-accent);
    border-color: var(--color-accent);
    color: white;
    box-shadow: var(--shadow-sm);
}

.filter-chip:focus {
    outline: 2px solid var(--color-accent);
    outline-offset: 2px;
}

/* Dark mode adjustments */
[data-theme="dark"] .filter-chip {
    background-color: var(--color-surface);
    border-color: var(--color-border);
    color: var(--color-text-secondary);
}

[data-theme="dark"] .filter-chip:hover {
    background-color: var(--color-surface-elevated);
    border-color: var(--color-border-strong);
    color: var(--color-text-primary);
}

[data-theme="dark"] .filter-chip.active {
    background-color: var(--color-accent);
    border-color: var(--color-accent);
    color: white;
}

/* Mobile responsive adjustments */
@@media (max-width: 640px) {
    .filter-chip {
        font-size: 0.8rem;
        padding: 0.3rem 0.6rem;
        margin-right: 0.5rem !important;
        margin-bottom: 0.5rem !important;
    }

    .card-enterprise {
        padding: 1rem;
    }
}

/* Ensure proper spacing on all screen sizes */
@@media (max-width: 768px) {
    .filter-chip {
        margin-bottom: 0.5rem;
    }
}

/* Fix for search input positioning */
.search-input-container {
    position: relative;
    width: 100%;
}

.search-icon {
    position: absolute;
    top: 50%;
    left: 0.75rem;
    transform: translateY(-50%);
    pointer-events: none;
    z-index: 1;
}
</style>

<!-- Enhanced People Grid -->
<div class="row" id="peopleGrid">
    @foreach (var person in Model)
    {
        <div class="col-xl-3 col-lg-4 col-md-6 col-sm-12 mb-4">
            <div class="card-enterprise person-card"
                 data-type="@person.Type" data-status="@person.Status" data-search="@person.FullName.ToLower() @person.Email.ToLower() @person.PersonCode.ToLower()"
                 style="height: 100%;">

            <!-- Person Header -->
            <div class="card-header-enterprise">
                <div class="flex items-center space-x-3">
                    <div class="user-avatar avatar-lg" style="background-color: @(GetPersonTypeColor(person.Type));">
                        <i class="@GetPersonTypeIcon(person.Type)"></i>
                    </div>
                    <div class="flex-1">
                        <h3 class="card-title-enterprise">@person.FullName</h3>
                        <p class="card-subtitle-enterprise">@person.PersonCode</p>
                    </div>
                </div>
                <div class="flex flex-col items-end space-y-2">
                    <span class="@(GetPersonTypeStatusPill(person.Type))">
                        @person.Type
                    </span>
                    @if (person.Status == PersonStatus.Active)
                    {
                        <span class="status-pill status-completed">
                            Active
                        </span>
                    }
                    else
                    {
                        <span class="status-pill status-blocked">
                            @person.Status
                        </span>
                    }
                </div>
            </div>

            <!-- Person Details -->
            <div class="card-body-enterprise">
                <div class="space-y-3">
                    @if (!string.IsNullOrEmpty(person.Title))
                    {
                        <div class="flex items-center text-sm" style="color: var(--color-text-secondary);">
                            <i class="fas fa-briefcase w-4 h-4 mr-3" style="color: var(--color-text-muted);"></i>
                            <span>@person.Title</span>
                        </div>
                    }
                    @if (!string.IsNullOrEmpty(person.Department))
                    {
                        <div class="flex items-center text-sm" style="color: var(--color-text-secondary);">
                            <i class="fas fa-sitemap w-4 h-4 mr-3" style="color: var(--color-text-muted);"></i>
                            <span>@person.Department</span>
                        </div>
                    }
                    @if (!string.IsNullOrEmpty(person.Organization))
                    {
                        <div class="flex items-center text-sm" style="color: var(--color-text-secondary);">
                            <i class="fas fa-building w-4 h-4 mr-3" style="color: var(--color-text-muted);"></i>
                            <span>@person.Organization</span>
                        </div>
                    }
                    <div class="flex items-center text-sm" style="color: var(--color-text-secondary);">
                        <i class="fas fa-envelope w-4 h-4 mr-3" style="color: var(--color-text-muted);"></i>
                        <span class="truncate">@person.Email</span>
                    </div>
                </div>

                <!-- Person Roles -->
                @if (person.Roles.Any())
                {
                    <div class="mt-4">
                        <div class="flex flex-wrap gap-2">
                            @foreach (var role in person.Roles.Take(3))
                            {
                                <span class="priority-indicator priority-medium">
                                    @role.RoleCode
                                </span>
                            }
                            @if (person.Roles.Count() > 3)
                            {
                                <span class="priority-indicator priority-low">
                                    +@(person.Roles.Count() - 3) more
                                </span>
                            }
                        </div>
                    </div>
                }
            </div>

            <!-- Action Buttons -->
            <div class="card-footer-enterprise">
                <div class="flex space-x-2 w-full">
                    <a href="@Url.Action("Details", new { id = person.Id })"
                       class="btn-enterprise btn-secondary-enterprise flex-1 text-center">
                        <i class="fas fa-eye mr-1"></i>
                        View
                    </a>
                    <a href="@Url.Action("Edit", new { id = person.Id })"
                       class="btn-enterprise btn-primary-enterprise flex-1 text-center">
                        <i class="fas fa-edit mr-1"></i>
                        Edit
                    </a>
                    @if (person.HasSystemAccess)
                    {
                        <button class="btn-enterprise btn-secondary-enterprise" title="Has System Access">
                            <i class="fas fa-user-cog" style="color: var(--color-success);"></i>
                        </button>
                    }
                </div>
            </div>
            </div>
        </div>
    }
</div>

<!-- Enhanced Empty State -->
@if (!Model.Any())
{
    <div class="empty-state">
        <div class="empty-state-icon">
            <i class="fas fa-users"></i>
        </div>
        <h3 class="empty-state-title">No People Found</h3>
        <p class="empty-state-description">Get started by adding your first person to the system. You can manage employees, contractors, stakeholders, vendors, and partners all in one place.</p>
        <a href="@Url.Action("Create")" class="btn-enterprise btn-primary-enterprise">
            <i class="fas fa-plus mr-2"></i>
            Add First Person
        </a>
    </div>
}

@section Scripts {
    <script>
        $(document).ready(function() {
            // Search functionality
            $('#searchInput').on('input', function() {
                filterPeople();
            });

            // Type filter functionality
            $('.filter-chip').on('click', function() {
                $('.filter-chip').removeClass('active');
                $(this).addClass('active');
                filterPeople();
            });

            // Status filter functionality
            $('#statusFilter').on('change', function() {
                filterPeople();
            });

            function filterPeople() {
                const searchTerm = $('#searchInput').val().toLowerCase();
                const selectedType = $('.filter-chip.active').data('filter');
                const selectedStatus = $('#statusFilter').val();

                $('.person-card').each(function() {
                    const $card = $(this);
                    const $column = $card.closest('.col-xl-3, .col-lg-4, .col-md-6, .col-sm-12');
                    const cardType = $card.data('type');
                    const cardStatus = $card.data('status');
                    const cardSearch = $card.data('search');

                    let showCard = true;

                    // Search filter
                    if (searchTerm && !cardSearch.includes(searchTerm)) {
                        showCard = false;
                    }

                    // Type filter
                    if (selectedType && selectedType !== 'all' && cardType !== selectedType) {
                        showCard = false;
                    }

                    // Status filter
                    if (selectedStatus && cardStatus !== selectedStatus) {
                        showCard = false;
                    }

                    if (showCard) {
                        $column.fadeIn(300);
                    } else {
                        $column.fadeOut(300);
                    }
                });
            }

            // Initialize first filter as active
            $('.filter-chip[data-filter="all"]').addClass('active');

            // Add smooth animations for card filtering
            $('.person-card').closest('.col-xl-3, .col-lg-4, .col-md-6, .col-sm-12').hide().fadeIn(300);

            // Add keyboard support for filter chips
            $('.filter-chip').on('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    $(this).click();
                }
            });
        });
    </script>
}

@functions {
    private string GetPersonTypeIcon(PersonType type)
    {
        return type switch
        {
            PersonType.Internal => "fas fa-user-tie",
            PersonType.External => "fas fa-user-friends",
            PersonType.Contractor => "fas fa-user-hard-hat",
            PersonType.Vendor => "fas fa-user-tag",
            PersonType.Customer => "fas fa-handshake",
            PersonType.Partner => "fas fa-users",
            PersonType.Regulatory => "fas fa-gavel",
            _ => "fas fa-user"
        };
    }

    private string GetPersonTypeColor(PersonType type)
    {
        return type switch
        {
            PersonType.Internal => "#2563eb",
            PersonType.External => "#22c55e",
            PersonType.Contractor => "#f59e0b",
            PersonType.Vendor => "#8b5cf6",
            PersonType.Customer => "#ec4899",
            PersonType.Partner => "#6366f1",
            PersonType.Regulatory => "#ef4444",
            _ => "#6b7280"
        };
    }

    private string GetPersonTypeStatusPill(PersonType type)
    {
        return type switch
        {
            PersonType.Internal => "status-pill status-in-progress",
            PersonType.External => "status-pill status-completed",
            PersonType.Contractor => "status-pill status-on-hold",
            PersonType.Vendor => "status-pill status-blocked",
            PersonType.Customer => "status-pill status-todo",
            PersonType.Partner => "status-pill status-in-progress",
            PersonType.Regulatory => "status-pill status-blocked",
            _ => "status-pill status-todo"
        };
    }

    private string GetPersonTypeIconBg(PersonType type)
    {
        return type switch
        {
            PersonType.Internal => "bg-blue-500",
            PersonType.External => "bg-green-500",
            PersonType.Contractor => "bg-orange-500",
            PersonType.Vendor => "bg-purple-500",
            PersonType.Customer => "bg-pink-500",
            PersonType.Partner => "bg-indigo-500",
            PersonType.Regulatory => "bg-red-500",
            _ => "bg-neutral-500"
        };
    }

    private string GetPersonTypeBadgeClass(PersonType type)
    {
        return type switch
        {
            PersonType.Internal => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
            PersonType.External => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            PersonType.Contractor => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
            PersonType.Vendor => "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
            PersonType.Customer => "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200",
            PersonType.Partner => "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200",
            PersonType.Regulatory => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200"
        };
    }
}
