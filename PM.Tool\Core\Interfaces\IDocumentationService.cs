using PM.Tool.Core.Models;

namespace PM.Tool.Core.Interfaces
{
    public interface IDocumentationService
    {
        /// <summary>
        /// Get all documentation sections with their pages
        /// </summary>
        Task<List<DocumentationSection>> GetAllSectionsAsync();

        /// <summary>
        /// Get a specific documentation page by ID
        /// </summary>
        Task<DocumentationPage?> GetPageAsync(string pageId);

        /// <summary>
        /// Get a documentation page by section and page name
        /// </summary>
        Task<DocumentationPage?> GetPageAsync(string sectionId, string pageName);

        /// <summary>
        /// Get all pages in a specific section
        /// </summary>
        Task<List<DocumentationPage>> GetSectionPagesAsync(string sectionId);

        /// <summary>
        /// Search documentation content
        /// </summary>
        Task<List<DocumentationSearchResult>> SearchAsync(string query, int maxResults = 10);

        /// <summary>
        /// Get navigation information for a page
        /// </summary>
        Task<DocumentationNavigation> GetNavigationAsync(string pageId);

        /// <summary>
        /// Get table of contents for a page
        /// </summary>
        Task<DocumentationTableOfContents> GetTableOfContentsAsync(string pageId);

        /// <summary>
        /// Get documentation metadata and statistics
        /// </summary>
        Task<DocumentationMetadata> GetMetadataAsync();

        /// <summary>
        /// Get documentation configuration
        /// </summary>
        DocumentationConfiguration GetConfiguration();

        /// <summary>
        /// Convert markdown to HTML
        /// </summary>
        string ConvertMarkdownToHtml(string markdown);

        /// <summary>
        /// Extract headings from markdown content
        /// </summary>
        List<DocumentationHeading> ExtractHeadings(string markdown);

        /// <summary>
        /// Calculate estimated reading time
        /// </summary>
        TimeSpan CalculateReadingTime(string content);

        /// <summary>
        /// Get breadcrumb navigation for a page
        /// </summary>
        Task<List<DocumentationPage>> GetBreadcrumbAsync(string pageId);

        /// <summary>
        /// Get related pages based on content similarity
        /// </summary>
        Task<List<DocumentationPage>> GetRelatedPagesAsync(string pageId, int maxResults = 5);

        /// <summary>
        /// Export page content in specified format
        /// </summary>
        Task<byte[]> ExportPageAsync(string pageId, string format);

        /// <summary>
        /// Export entire documentation in specified format
        /// </summary>
        Task<byte[]> ExportAllAsync(string format);

        /// <summary>
        /// Refresh documentation cache
        /// </summary>
        Task RefreshCacheAsync();

        /// <summary>
        /// Get popular pages based on view statistics
        /// </summary>
        Task<List<DocumentationPage>> GetPopularPagesAsync(int maxResults = 10);

        /// <summary>
        /// Track page view for analytics
        /// </summary>
        Task TrackPageViewAsync(string pageId, string? userId = null);
    }
}
