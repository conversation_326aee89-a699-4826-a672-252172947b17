using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;

namespace PM.Tool.Application.Services
{
    public class TimeTrackingService : ITimeTrackingService
    {
        private readonly ApplicationDbContext _context;

        public TimeTrackingService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<TimeEntry> StartTimeTrackingAsync(int taskId, string userId, string? description)
        {
            // Check if user has any active time entries
            var activeEntry = await GetActiveTimeEntryAsync(userId);
            if (activeEntry != null)
            {
                throw new InvalidOperationException("User already has an active time entry");
            }

            var timeEntry = new TimeEntry
            {
                TaskId = taskId,
                UserId = userId,
                StartTime = DateTime.UtcNow,
                Description = description
            };

            _context.TimeEntries.Add(timeEntry);
            await _context.SaveChangesAsync();

            return timeEntry;
        }

        public async Task<TimeEntry> StopTimeTrackingAsync(int timeEntryId)
        {
            var timeEntry = await _context.TimeEntries.FindAsync(timeEntryId);
            if (timeEntry == null)
                throw new KeyNotFoundException("Time entry not found");

            if (timeEntry.EndTime.HasValue)
                throw new InvalidOperationException("Time entry already stopped");

            timeEntry.EndTime = DateTime.UtcNow;
            timeEntry.Duration = (timeEntry.EndTime.Value - timeEntry.StartTime).TotalHours;
            timeEntry.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return timeEntry;
        }

        public async Task<TimeEntry?> GetActiveTimeEntryAsync(string userId)
        {
            return await _context.TimeEntries
                .Include(te => te.Task)
                .FirstOrDefaultAsync(te => te.UserId == userId && !te.EndTime.HasValue);
        }

        public async Task<IEnumerable<TimeEntry>> GetUserTimeEntriesAsync(string userId, DateTime startDate, DateTime endDate)
        {
            return await _context.TimeEntries
                .Include(te => te.Task)
                .Where(te => te.UserId == userId && te.StartTime >= startDate && te.StartTime <= endDate)
                .OrderByDescending(te => te.StartTime)
                .ToListAsync();
        }

        public async Task<IEnumerable<TimeEntry>> GetTaskTimeEntriesAsync(int taskId)
        {
            return await _context.TimeEntries
                .Include(te => te.User)
                .Where(te => te.TaskId == taskId)
                .OrderByDescending(te => te.StartTime)
                .ToListAsync();
        }

        public async Task<TimeEntry> UpdateTimeEntryAsync(int timeEntryId, DateTime? startTime, DateTime? endTime, string? description)
        {
            var timeEntry = await _context.TimeEntries.FindAsync(timeEntryId);
            if (timeEntry == null)
                throw new KeyNotFoundException("Time entry not found");

            if (startTime.HasValue)
                timeEntry.StartTime = startTime.Value;
            
            if (endTime.HasValue)
            {
                timeEntry.EndTime = endTime.Value;
                timeEntry.Duration = (timeEntry.EndTime.Value - timeEntry.StartTime).TotalHours;
            }

            if (description != null)
                timeEntry.Description = description;

            timeEntry.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            return timeEntry;
        }

        public async Task<bool> DeleteTimeEntryAsync(int timeEntryId)
        {
            var timeEntry = await _context.TimeEntries.FindAsync(timeEntryId);
            if (timeEntry == null)
                return false;

            _context.TimeEntries.Remove(timeEntry);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<double> GetTotalTimeSpentAsync(int taskId)
        {
            return await _context.TimeEntries
                .Where(te => te.TaskId == taskId && te.EndTime.HasValue)
                .SumAsync(te => te.Duration);
        }

        public async Task<IDictionary<string, double>> GetUserTimeReportAsync(string userId, DateTime startDate, DateTime endDate)
        {
            var timeEntries = await _context.TimeEntries
                .Include(te => te.Task)
                .Where(te => te.UserId == userId && 
                           te.StartTime >= startDate && 
                           te.StartTime <= endDate &&
                           te.EndTime.HasValue)
                .ToListAsync();

            return timeEntries
                .GroupBy(te => te.Task.Title)
                .ToDictionary(g => g.Key, g => g.Sum(te => te.Duration));
        }
    }
}
