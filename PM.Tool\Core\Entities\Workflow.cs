using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities
{
    public class Workflow : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(1000)]
        public string? Description { get; set; }

        public int ProjectId { get; set; }

        public WorkflowType Type { get; set; } = WorkflowType.Task;

        public bool IsActive { get; set; } = true;

        public bool IsDefault { get; set; } = false;

        public string CreatedByUserId { get; set; } = string.Empty;

        // Navigation properties
        public virtual Project Project { get; set; } = null!;
        public virtual ApplicationUser CreatedBy { get; set; } = null!;
        public virtual ICollection<WorkflowState> States { get; set; } = new List<WorkflowState>();
        public virtual ICollection<WorkflowTransition> Transitions { get; set; } = new List<WorkflowTransition>();
        public virtual ICollection<WorkflowRule> Rules { get; set; } = new List<WorkflowRule>();
    }

    public class WorkflowState : BaseEntity
    {
        public int WorkflowId { get; set; }

        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? Description { get; set; }

        [MaxLength(20)]
        public string Color { get; set; } = "#6c757d";

        public int SortOrder { get; set; }

        public bool IsInitial { get; set; } = false;

        public bool IsFinal { get; set; } = false;

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual Workflow Workflow { get; set; } = null!;
        public virtual ICollection<WorkflowTransition> FromTransitions { get; set; } = new List<WorkflowTransition>();
        public virtual ICollection<WorkflowTransition> ToTransitions { get; set; } = new List<WorkflowTransition>();
    }

    public class WorkflowTransition : BaseEntity
    {
        public int WorkflowId { get; set; }

        public int FromStateId { get; set; }

        public int ToStateId { get; set; }

        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? Description { get; set; }

        public bool RequiresApproval { get; set; } = false;

        public string? ApproverRoles { get; set; } // JSON array of roles

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual Workflow Workflow { get; set; } = null!;
        public virtual WorkflowState FromState { get; set; } = null!;
        public virtual WorkflowState ToState { get; set; } = null!;
        public virtual ICollection<WorkflowRule> Rules { get; set; } = new List<WorkflowRule>();
    }

    public class WorkflowRule : BaseEntity
    {
        public int WorkflowId { get; set; }

        public int? TransitionId { get; set; }

        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(1000)]
        public string? Description { get; set; }

        public RuleType Type { get; set; } = RuleType.Condition;

        [Required]
        public string Condition { get; set; } = string.Empty; // JSON condition

        [Required]
        public string Action { get; set; } = string.Empty; // JSON action

        public bool IsActive { get; set; } = true;

        public int Priority { get; set; } = 0;

        // Navigation properties
        public virtual Workflow Workflow { get; set; } = null!;
        public virtual WorkflowTransition? Transition { get; set; }
    }

    public enum WorkflowType
    {
        Task = 1,
        Project = 2,
        Risk = 3,
        Document = 4
    }

    public enum RuleType
    {
        Condition = 1,
        Validation = 2,
        Automation = 3,
        Notification = 4
    }
}
