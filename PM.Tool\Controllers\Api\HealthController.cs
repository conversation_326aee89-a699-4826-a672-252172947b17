using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;
using PM.Tool.Models.Api;
using System.Diagnostics;
using System.Reflection;
using Asp.Versioning;

namespace PM.Tool.Controllers.Api
{
    /// <summary>
    /// API controller for health checks and system status
    /// </summary>
    [ApiVersion("1.0")]
    [Tags("Health")]
    [AllowAnonymous]
    public class HealthController : BaseApiController
    {
        private readonly ApplicationDbContext _context;
        private readonly IConfiguration _configuration;

        public HealthController(
            ApplicationDbContext context,
            IConfiguration configuration,
            IAuditService auditService,
            ILogger<HealthController> logger)
            : base(auditService, logger)
        {
            _context = context;
            _configuration = configuration;
        }

        /// <summary>
        /// Get overall system health status
        /// </summary>
        /// <returns>Health check results</returns>
        [HttpGet]
        [ProducesResponseType(typeof(HealthCheckResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(HealthCheckResponse), StatusCodes.Status503ServiceUnavailable)]
        public async Task<IActionResult> GetHealth()
        {
            try
            {
                var healthChecks = new Dictionary<string, ComponentHealth>();
                var overallHealthy = true;

                // Database health check
                var dbHealth = await CheckDatabaseHealthAsync();
                healthChecks["database"] = dbHealth;
                if (dbHealth.Status != "Healthy") overallHealthy = false;

                // Memory health check
                var memoryHealth = CheckMemoryHealth();
                healthChecks["memory"] = memoryHealth;
                if (memoryHealth.Status != "Healthy") overallHealthy = false;

                // Disk space health check
                var diskHealth = CheckDiskHealth();
                healthChecks["disk"] = diskHealth;
                if (diskHealth.Status != "Healthy") overallHealthy = false;

                // Application health check
                var appHealth = CheckApplicationHealth();
                healthChecks["application"] = appHealth;
                if (appHealth.Status != "Healthy") overallHealthy = false;

                var response = new HealthCheckResponse
                {
                    Success = overallHealthy,
                    Message = overallHealthy ? "All systems operational" : "Some systems are experiencing issues",
                    Status = overallHealthy ? "Healthy" : "Unhealthy",
                    Components = healthChecks,
                    Version = GetVersionInfo(),
                    Timestamp = DateTime.UtcNow
                };

                return overallHealthy ? Ok(response) : StatusCode(503, response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during health check");

                var errorResponse = new HealthCheckResponse
                {
                    Success = false,
                    Message = "Health check failed",
                    Status = "Unhealthy",
                    Components = new Dictionary<string, ComponentHealth>
                    {
                        ["system"] = new ComponentHealth
                        {
                            Status = "Unhealthy",
                            Details = new { Error = ex.Message }
                        }
                    },
                    Version = GetVersionInfo(),
                    Timestamp = DateTime.UtcNow
                };

                return StatusCode(503, errorResponse);
            }
        }

        /// <summary>
        /// Get detailed system information (requires authentication)
        /// </summary>
        /// <returns>Detailed system metrics</returns>
        [HttpGet("detailed")]
        [Authorize]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetDetailedHealth()
        {
            try
            {
                var systemInfo = new
                {
                    Server = new
                    {
                        MachineName = Environment.MachineName,
                        OSVersion = Environment.OSVersion.ToString(),
                        ProcessorCount = Environment.ProcessorCount,
                        Is64BitOperatingSystem = Environment.Is64BitOperatingSystem,
                        Is64BitProcess = Environment.Is64BitProcess,
                        WorkingSet = Environment.WorkingSet,
                        SystemPageSize = Environment.SystemPageSize
                    },
                    Application = new
                    {
                        Version = GetVersionInfo(),
                        StartTime = Process.GetCurrentProcess().StartTime,
                        Uptime = DateTime.Now - Process.GetCurrentProcess().StartTime,
                        ThreadCount = Process.GetCurrentProcess().Threads.Count,
                        HandleCount = Process.GetCurrentProcess().HandleCount
                    },
                    Memory = new
                    {
                        TotalPhysicalMemory = GC.GetTotalMemory(false),
                        WorkingSet = Process.GetCurrentProcess().WorkingSet64,
                        PrivateMemorySize = Process.GetCurrentProcess().PrivateMemorySize64,
                        VirtualMemorySize = Process.GetCurrentProcess().VirtualMemorySize64,
                        GCCollections = new
                        {
                            Gen0 = GC.CollectionCount(0),
                            Gen1 = GC.CollectionCount(1),
                            Gen2 = GC.CollectionCount(2)
                        }
                    },
                    Database = await GetDatabaseInfoAsync(),
                    Configuration = new
                    {
                        Environment = _configuration["ASPNETCORE_ENVIRONMENT"],
                        ConnectionStringConfigured = !string.IsNullOrEmpty(_configuration.GetConnectionString("DefaultConnection")),
                        LogLevel = _configuration["Logging:LogLevel:Default"]
                    }
                };

                return Ok(Success(systemInfo));
            }
            catch (Exception ex)
            {
                return HandleException(ex, "GetDetailedHealth");
            }
        }

        /// <summary>
        /// Simple ping endpoint for basic connectivity testing
        /// </summary>
        /// <returns>Pong response</returns>
        [HttpGet("ping")]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
        public IActionResult Ping()
        {
            return Ok(Success(new {
                Message = "Pong",
                Timestamp = DateTime.UtcNow,
                Server = Environment.MachineName
            }));
        }

        #region Private Health Check Methods

        private async Task<ComponentHealth> CheckDatabaseHealthAsync()
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                // Simple database connectivity test
                await _context.Database.ExecuteSqlRawAsync("SELECT 1");
                stopwatch.Stop();

                return new ComponentHealth
                {
                    Status = "Healthy",
                    ResponseTime = stopwatch.ElapsedMilliseconds,
                    Details = new { ConnectionState = "Connected" }
                };
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                return new ComponentHealth
                {
                    Status = "Unhealthy",
                    ResponseTime = stopwatch.ElapsedMilliseconds,
                    Details = new { Error = ex.Message }
                };
            }
        }

        private ComponentHealth CheckMemoryHealth()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                var workingSetMB = process.WorkingSet64 / 1024 / 1024;
                var privateMemoryMB = process.PrivateMemorySize64 / 1024 / 1024;

                // Consider unhealthy if using more than 1GB of working set
                var status = workingSetMB > 1024 ? "Degraded" : "Healthy";

                return new ComponentHealth
                {
                    Status = status,
                    Details = new
                    {
                        WorkingSetMB = workingSetMB,
                        PrivateMemoryMB = privateMemoryMB,
                        GCTotalMemoryMB = GC.GetTotalMemory(false) / 1024 / 1024
                    }
                };
            }
            catch (Exception ex)
            {
                return new ComponentHealth
                {
                    Status = "Unhealthy",
                    Details = new { Error = ex.Message }
                };
            }
        }

        private ComponentHealth CheckDiskHealth()
        {
            try
            {
                var drives = DriveInfo.GetDrives()
                    .Where(d => d.IsReady && d.DriveType == DriveType.Fixed)
                    .Select(d => new
                    {
                        Name = d.Name,
                        TotalSizeGB = d.TotalSize / 1024 / 1024 / 1024,
                        AvailableFreeSpaceGB = d.AvailableFreeSpace / 1024 / 1024 / 1024,
                        FreeSpacePercentage = (double)d.AvailableFreeSpace / d.TotalSize * 100
                    })
                    .ToList();

                // Consider degraded if any drive has less than 10% free space
                var minFreeSpacePercentage = drives.Any() ? drives.Min(d => d.FreeSpacePercentage) : 100;
                var status = minFreeSpacePercentage < 10 ? "Degraded" : "Healthy";

                return new ComponentHealth
                {
                    Status = status,
                    Details = new { Drives = drives }
                };
            }
            catch (Exception ex)
            {
                return new ComponentHealth
                {
                    Status = "Unhealthy",
                    Details = new { Error = ex.Message }
                };
            }
        }

        private ComponentHealth CheckApplicationHealth()
        {
            try
            {
                var process = Process.GetCurrentProcess();
                var uptime = DateTime.Now - process.StartTime;

                return new ComponentHealth
                {
                    Status = "Healthy",
                    Details = new
                    {
                        UptimeMinutes = uptime.TotalMinutes,
                        ThreadCount = process.Threads.Count,
                        HandleCount = process.HandleCount
                    }
                };
            }
            catch (Exception ex)
            {
                return new ComponentHealth
                {
                    Status = "Unhealthy",
                    Details = new { Error = ex.Message }
                };
            }
        }

        private VersionInfo GetVersionInfo()
        {
            var assembly = Assembly.GetExecutingAssembly();
            var version = assembly.GetName().Version?.ToString() ?? "Unknown";
            var buildDate = GetBuildDate(assembly);

            return new VersionInfo
            {
                ApiVersion = "1.0",
                AppVersion = version,
                BuildDate = buildDate,
                Environment = _configuration["ASPNETCORE_ENVIRONMENT"] ?? "Unknown"
            };
        }

        private DateTime? GetBuildDate(Assembly assembly)
        {
            try
            {
                var attribute = assembly.GetCustomAttribute<AssemblyInformationalVersionAttribute>();
                if (attribute != null && DateTime.TryParse(attribute.InformationalVersion, out var buildDate))
                {
                    return buildDate;
                }

                // Fallback to file creation time
                var location = assembly.Location;
                if (!string.IsNullOrEmpty(location) && System.IO.File.Exists(location))
                {
                    return System.IO.File.GetCreationTime(location);
                }
            }
            catch
            {
                // Ignore errors
            }

            return null;
        }

        private async Task<object> GetDatabaseInfoAsync()
        {
            try
            {
                var connectionString = _context.Database.GetConnectionString();
                var databaseName = _context.Database.GetDbConnection().Database;

                // Get some basic statistics
                var userCount = await _context.Users.CountAsync();
                var projectCount = await _context.Set<PM.Tool.Core.Entities.Project>().CountAsync();

                return new
                {
                    DatabaseName = databaseName,
                    Provider = _context.Database.ProviderName,
                    ConnectionConfigured = !string.IsNullOrEmpty(connectionString),
                    Statistics = new
                    {
                        UserCount = userCount,
                        ProjectCount = projectCount
                    }
                };
            }
            catch (Exception ex)
            {
                return new { Error = ex.Message };
            }
        }

        #endregion
    }
}
