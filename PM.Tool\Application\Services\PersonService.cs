using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;

namespace PM.Tool.Application.Services
{
    /// <summary>
    /// Service implementation for unified person management
    /// Provides comprehensive person management capabilities for all types of people in the system
    /// </summary>
    public class PersonService : IPersonService
    {
        private readonly ApplicationDbContext _context;

        public PersonService(ApplicationDbContext context)
        {
            _context = context;
        }

        #region Core Person Management

        public async Task<IEnumerable<Person>> GetAllPeopleAsync()
        {
            return await _context.People
                .Include(p => p.User)
                .Include(p => p.Roles.Where(r => r.IsActive))
                .Include(p => p.StakeholderInfo)
                .Include(p => p.ResourceInfo)
                .OrderBy(p => p.FirstName)
                .ThenBy(p => p.LastName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Person>> GetActivePeopleAsync()
        {
            return await _context.People
                .Include(p => p.User)
                .Include(p => p.Roles.Where(r => r.IsActive))
                .Include(p => p.StakeholderInfo)
                .Include(p => p.ResourceInfo)
                .Where(p => p.Status == PersonStatus.Active)
                .OrderBy(p => p.FirstName)
                .ThenBy(p => p.LastName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Person>> GetPeopleByTypeAsync(PersonType type)
        {
            return await _context.People
                .Include(p => p.User)
                .Include(p => p.Roles.Where(r => r.IsActive))
                .Where(p => p.Type == type)
                .OrderBy(p => p.FirstName)
                .ThenBy(p => p.LastName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Person>> GetPeopleByStatusAsync(PersonStatus status)
        {
            return await _context.People
                .Include(p => p.User)
                .Include(p => p.Roles.Where(r => r.IsActive))
                .Where(p => p.Status == status)
                .OrderBy(p => p.FirstName)
                .ThenBy(p => p.LastName)
                .ToListAsync();
        }

        public async Task<Person?> GetPersonByIdAsync(int id)
        {
            return await _context.People
                .Include(p => p.User)
                .Include(p => p.Roles.Where(r => r.IsActive))
                .Include(p => p.Skills)
                    .ThenInclude(ps => ps.Skill)
                .Include(p => p.ProjectAssignments.Where(pa => pa.IsActive))
                    .ThenInclude(pa => pa.Project)
                .Include(p => p.StakeholderInfo)
                .Include(p => p.ResourceInfo)
                .Include(p => p.Contacts)
                .Include(p => p.Availability)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<Person?> GetPersonByCodeAsync(string personCode)
        {
            return await _context.People
                .Include(p => p.User)
                .Include(p => p.Roles.Where(r => r.IsActive))
                .FirstOrDefaultAsync(p => p.PersonCode == personCode);
        }

        public async Task<Person?> GetPersonByEmailAsync(string email)
        {
            return await _context.People
                .Include(p => p.User)
                .Include(p => p.Roles.Where(r => r.IsActive))
                .FirstOrDefaultAsync(p => p.Email == email);
        }

        public async Task<Person?> GetPersonByUserIdAsync(string userId)
        {
            return await _context.People
                .Include(p => p.User)
                .Include(p => p.Roles.Where(r => r.IsActive))
                .FirstOrDefaultAsync(p => p.UserId == userId);
        }

        public async Task<Person> CreatePersonAsync(Person person)
        {
            // Generate unique person code if not provided
            if (string.IsNullOrEmpty(person.PersonCode))
            {
                person.PersonCode = await GeneratePersonCodeAsync(person.Type);
            }

            // Validate uniqueness
            if (await IsPersonCodeUniqueAsync(person.PersonCode) == false)
            {
                throw new InvalidOperationException($"Person code '{person.PersonCode}' already exists.");
            }

            if (await IsEmailUniqueAsync(person.Email) == false)
            {
                throw new InvalidOperationException($"Email '{person.Email}' already exists.");
            }

            person.CreatedAt = DateTime.UtcNow;
            person.UpdatedAt = DateTime.UtcNow;

            _context.People.Add(person);
            await _context.SaveChangesAsync();
            return person;
        }

        public async Task<Person> UpdatePersonAsync(Person person)
        {
            // Validate email uniqueness (excluding current person)
            if (await IsEmailUniqueAsync(person.Email, person.Id) == false)
            {
                throw new InvalidOperationException($"Email '{person.Email}' already exists.");
            }

            person.UpdatedAt = DateTime.UtcNow;

            _context.People.Update(person);
            await _context.SaveChangesAsync();
            return person;
        }

        public async Task<bool> DeletePersonAsync(int id)
        {
            var person = await _context.People.FindAsync(id);
            if (person == null) return false;

            // Check for dependencies
            var hasActiveProjects = await _context.PersonProjects
                .AnyAsync(pp => pp.PersonId == id && pp.IsActive);

            if (hasActiveProjects)
            {
                throw new InvalidOperationException("Cannot delete person with active project assignments.");
            }

            _context.People.Remove(person);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeactivatePersonAsync(int id)
        {
            var person = await _context.People.FindAsync(id);
            if (person == null) return false;

            person.Status = PersonStatus.Inactive;
            person.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ActivatePersonAsync(int id)
        {
            var person = await _context.People.FindAsync(id);
            if (person == null) return false;

            person.Status = PersonStatus.Active;
            person.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }

        #endregion

        #region Person Search and Filtering

        public async Task<IEnumerable<Person>> SearchPeopleAsync(string searchTerm)
        {
            var term = searchTerm.ToLower();
            return await _context.People
                .Include(p => p.User)
                .Include(p => p.Roles.Where(r => r.IsActive))
                .Where(p => 
                    p.FirstName.ToLower().Contains(term) ||
                    p.LastName.ToLower().Contains(term) ||
                    p.Email.ToLower().Contains(term) ||
                    p.PersonCode.ToLower().Contains(term) ||
                    (p.Organization != null && p.Organization.ToLower().Contains(term)) ||
                    (p.Department != null && p.Department.ToLower().Contains(term)) ||
                    (p.Title != null && p.Title.ToLower().Contains(term)))
                .OrderBy(p => p.FirstName)
                .ThenBy(p => p.LastName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Person>> GetPeopleByDepartmentAsync(string department)
        {
            return await _context.People
                .Include(p => p.User)
                .Include(p => p.Roles.Where(r => r.IsActive))
                .Where(p => p.Department == department)
                .OrderBy(p => p.FirstName)
                .ThenBy(p => p.LastName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Person>> GetPeopleByOrganizationAsync(string organization)
        {
            return await _context.People
                .Include(p => p.User)
                .Include(p => p.Roles.Where(r => r.IsActive))
                .Where(p => p.Organization == organization)
                .OrderBy(p => p.FirstName)
                .ThenBy(p => p.LastName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Person>> GetPeopleByLocationAsync(string location)
        {
            return await _context.People
                .Include(p => p.User)
                .Include(p => p.Roles.Where(r => r.IsActive))
                .Where(p => p.Location == location)
                .OrderBy(p => p.FirstName)
                .ThenBy(p => p.LastName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Person>> GetPeopleWithSystemAccessAsync()
        {
            return await _context.People
                .Include(p => p.User)
                .Include(p => p.Roles.Where(r => r.IsActive))
                .Where(p => p.HasSystemAccess && p.UserId != null)
                .OrderBy(p => p.FirstName)
                .ThenBy(p => p.LastName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Person>> GetPeopleWithoutSystemAccessAsync()
        {
            return await _context.People
                .Include(p => p.Roles.Where(r => r.IsActive))
                .Where(p => !p.HasSystemAccess || p.UserId == null)
                .OrderBy(p => p.FirstName)
                .ThenBy(p => p.LastName)
                .ToListAsync();
        }

        #endregion

        #region Private Helper Methods

        private async Task<string> GeneratePersonCodeAsync(PersonType type)
        {
            var prefix = type switch
            {
                PersonType.Internal => "EMP",
                PersonType.External => "EXT",
                PersonType.Contractor => "CTR",
                PersonType.Vendor => "VND",
                PersonType.Customer => "CUS",
                PersonType.Partner => "PTR",
                PersonType.Regulatory => "REG",
                _ => "PER"
            };

            var lastCode = await _context.People
                .Where(p => p.PersonCode.StartsWith(prefix))
                .OrderByDescending(p => p.PersonCode)
                .Select(p => p.PersonCode)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (!string.IsNullOrEmpty(lastCode) && lastCode.Length > prefix.Length)
            {
                var numberPart = lastCode.Substring(prefix.Length);
                if (int.TryParse(numberPart, out int currentNumber))
                {
                    nextNumber = currentNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D4}"; // e.g., EMP0001, CTR0001
        }

        #endregion

        // Placeholder implementations for interface compliance
        // These will be implemented in the next part
        public Task<IEnumerable<PersonRole>> GetPersonRolesAsync(int personId) => throw new NotImplementedException();
        public Task<IEnumerable<PersonRole>> GetActivePersonRolesAsync(int personId) => throw new NotImplementedException();
        public Task<IEnumerable<PersonRole>> GetPersonRolesByScopeAsync(int personId, RoleScope scope) => throw new NotImplementedException();
        public Task<PersonRole> AssignRoleAsync(int personId, string roleCode, string roleName, RoleScope scope, string? scopeId = null, string? scopeName = null) => throw new NotImplementedException();
        public Task<bool> RemoveRoleAsync(int personRoleId) => throw new NotImplementedException();
        public Task<bool> DeactivateRoleAsync(int personRoleId) => throw new NotImplementedException();
        public Task<IEnumerable<Person>> GetPeopleByRoleAsync(string roleCode, RoleScope? scope = null, string? scopeId = null) => throw new NotImplementedException();
        public Task<IEnumerable<PersonSkill>> GetPersonSkillsAsync(int personId) => throw new NotImplementedException();
        public Task<IEnumerable<PersonSkill>> GetVerifiedPersonSkillsAsync(int personId) => throw new NotImplementedException();
        public Task<PersonSkill> AddPersonSkillAsync(int personId, int skillId, SkillLevel level, int yearsOfExperience) => throw new NotImplementedException();
        public Task<PersonSkill> UpdatePersonSkillAsync(PersonSkill personSkill) => throw new NotImplementedException();
        public Task<bool> RemovePersonSkillAsync(int personSkillId) => throw new NotImplementedException();
        public Task<bool> VerifyPersonSkillAsync(int personSkillId, string verifiedByUserId) => throw new NotImplementedException();
        public Task<IEnumerable<Person>> GetPeopleBySkillAsync(int skillId, SkillLevel? minimumLevel = null) => throw new NotImplementedException();
        public Task<IEnumerable<Person>> GetPeopleBySkillsAsync(IEnumerable<int> skillIds, bool requireAll = false) => throw new NotImplementedException();
        public Task<IEnumerable<PersonProject>> GetPersonProjectsAsync(int personId) => throw new NotImplementedException();
        public Task<IEnumerable<PersonProject>> GetActivePersonProjectsAsync(int personId) => throw new NotImplementedException();
        public Task<IEnumerable<PersonProject>> GetProjectPeopleAsync(int projectId) => throw new NotImplementedException();
        public Task<PersonProject> AssignPersonToProjectAsync(int personId, int projectId, string projectRole, decimal allocationPercentage = 100) => throw new NotImplementedException();
        public Task<PersonProject> UpdateProjectAssignmentAsync(PersonProject personProject) => throw new NotImplementedException();
        public Task<bool> RemovePersonFromProjectAsync(int personProjectId) => throw new NotImplementedException();
        public Task<bool> DeactivateProjectAssignmentAsync(int personProjectId) => throw new NotImplementedException();
        public Task<IEnumerable<Person>> GetAvailablePeopleForProjectAsync(int projectId, DateTime startDate, DateTime? endDate = null) => throw new NotImplementedException();
        public Task<PersonStakeholder?> GetPersonStakeholderInfoAsync(int personId) => throw new NotImplementedException();
        public Task<PersonStakeholder> CreateOrUpdateStakeholderInfoAsync(int personId, StakeholderType type, InfluenceLevel influence, InterestLevel interest) => throw new NotImplementedException();
        public Task<IEnumerable<Person>> GetStakeholdersAsync() => throw new NotImplementedException();
        public Task<IEnumerable<Person>> GetStakeholdersByTypeAsync(StakeholderType type) => throw new NotImplementedException();
        public Task<IEnumerable<Person>> GetStakeholdersByInfluenceAsync(InfluenceLevel influence) => throw new NotImplementedException();
        public Task<IEnumerable<Person>> GetStakeholdersByInterestAsync(InterestLevel interest) => throw new NotImplementedException();
        public Task<IEnumerable<Person>> GetKeyStakeholdersAsync() => throw new NotImplementedException();
        public Task<PersonResource?> GetPersonResourceInfoAsync(int personId) => throw new NotImplementedException();
        public Task<PersonResource> CreateOrUpdateResourceInfoAsync(int personId, decimal hourlyRate, decimal dailyCapacity) => throw new NotImplementedException();
        public Task<IEnumerable<Person>> GetResourcesAsync() => throw new NotImplementedException();
        public Task<IEnumerable<Person>> GetAvailableResourcesAsync() => throw new NotImplementedException();
        public Task<IEnumerable<Person>> GetResourcesByCapacityAsync(decimal minimumCapacity) => throw new NotImplementedException();
        public Task<IEnumerable<Person>> GetResourcesByRateRangeAsync(decimal minRate, decimal maxRate) => throw new NotImplementedException();
        public Task<IEnumerable<PersonContact>> GetPersonContactsAsync(int personId) => throw new NotImplementedException();
        public Task<PersonContact> AddPersonContactAsync(int personId, ContactType type, string value, string? label = null, bool isPrimary = false) => throw new NotImplementedException();
        public Task<PersonContact> UpdatePersonContactAsync(PersonContact contact) => throw new NotImplementedException();
        public Task<bool> RemovePersonContactAsync(int contactId) => throw new NotImplementedException();
        public Task<IEnumerable<PersonAvailability>> GetPersonAvailabilityAsync(int personId, DateTime? startDate = null, DateTime? endDate = null) => throw new NotImplementedException();
        public Task<PersonAvailability> AddPersonAvailabilityAsync(int personId, DateTime startDate, DateTime endDate, AvailabilityType type, decimal availabilityPercentage = 100) => throw new NotImplementedException();
        public Task<PersonAvailability> UpdatePersonAvailabilityAsync(PersonAvailability availability) => throw new NotImplementedException();
        public Task<bool> RemovePersonAvailabilityAsync(int availabilityId) => throw new NotImplementedException();
        public Task<IEnumerable<Person>> GetAvailablePeopleAsync(DateTime startDate, DateTime endDate, decimal minimumAvailability = 50) => throw new NotImplementedException();
        public Task<Dictionary<PersonType, int>> GetPersonTypeDistributionAsync() => throw new NotImplementedException();
        public Task<Dictionary<PersonStatus, int>> GetPersonStatusDistributionAsync() => throw new NotImplementedException();
        public Task<Dictionary<string, int>> GetDepartmentDistributionAsync() => throw new NotImplementedException();
        public Task<Dictionary<string, int>> GetSkillDistributionAsync() => throw new NotImplementedException();
        public Task<Dictionary<string, int>> GetRoleDistributionAsync() => throw new NotImplementedException();
        public Task<IEnumerable<Person>> GetPeopleWithExpiringCertificationsAsync(int daysAhead = 30) => throw new NotImplementedException();
        public Task<IEnumerable<Person>> GetUnderutilizedResourcesAsync(decimal maxUtilization = 50) => throw new NotImplementedException();
        public Task<IEnumerable<Person>> GetOverallocatedResourcesAsync(decimal maxAllocation = 100) => throw new NotImplementedException();
        public Task<IEnumerable<Person>> CreatePeopleBulkAsync(IEnumerable<Person> people) => throw new NotImplementedException();
        public Task<bool> UpdatePeopleStatusBulkAsync(IEnumerable<int> personIds, PersonStatus status) => throw new NotImplementedException();
        public Task<bool> AssignRoleBulkAsync(IEnumerable<int> personIds, string roleCode, string roleName, RoleScope scope) => throw new NotImplementedException();
        public Task<bool> DeactivatePeopleBulkAsync(IEnumerable<int> personIds) => throw new NotImplementedException();
        public Task<Person?> SyncWithUserAsync(string userId) => throw new NotImplementedException();
        public Task<bool> CreateSystemUserAsync(int personId, string email, string password) => throw new NotImplementedException();
        public Task<bool> LinkToSystemUserAsync(int personId, string userId) => throw new NotImplementedException();
        public Task<bool> UnlinkFromSystemUserAsync(int personId) => throw new NotImplementedException();
        public Task<bool> IsPersonCodeUniqueAsync(string personCode, int? excludePersonId = null) => throw new NotImplementedException();
        public Task<bool> IsEmailUniqueAsync(string email, int? excludePersonId = null) => throw new NotImplementedException();
        public Task<bool> CanAssignToProjectAsync(int personId, int projectId, DateTime startDate, DateTime? endDate = null) => throw new NotImplementedException();
        public Task<bool> HasRequiredSkillsAsync(int personId, IEnumerable<int> requiredSkillIds) => throw new NotImplementedException();
        public Task<decimal> GetPersonUtilizationAsync(int personId, DateTime startDate, DateTime endDate) => throw new NotImplementedException();
    }
}
