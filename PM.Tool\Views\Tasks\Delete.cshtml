@using PM.Tool.Core.Enums
@using TaskStatus = PM.Tool.Core.Enums.TaskStatus
@model TaskViewModel

@{
    ViewData["Title"] = "Delete Task";
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-exclamation-triangle mr-3 text-danger-600 dark:text-danger-400"></i>
                Delete Task
            </h1>
            <p class="mt-1 text-sm text-danger-600 dark:text-danger-400">
                Are you sure you want to delete this task? This action cannot be undone.
            </p>
        </div>
        <div class="flex space-x-3">
            @{
                ViewData["Text"] = "Back to Task";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Details", new { id = Model.Id });
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Delete Confirmation Card -->
<div class="max-w-4xl mx-auto">
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-danger-100 dark:bg-danger-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-tasks text-danger-600 dark:text-danger-400 text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Task Details</h3>
                    <p class="text-sm text-neutral-500 dark:text-dark-400">Review the task information before deletion</p>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <!-- Warning Alert -->
            <div class="alert-danger-custom mb-6">
                <i class="fas fa-exclamation-triangle text-danger-600 dark:text-danger-400"></i>
                <div>
                    <p class="font-medium">Permanent Deletion Warning</p>
                    <p class="text-sm">This task and all associated data (comments, attachments, time logs) will be permanently deleted. This action cannot be undone.</p>
                </div>
            </div>

            <!-- Task Information -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Basic Information -->
                <div class="space-y-6">
                    <div>
                        <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Task Title</h4>
                        <p class="text-lg font-semibold text-neutral-900 dark:text-dark-100">@Model.Title</p>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Project</h4>
                        <a href="@Url.Action("Details", "Projects", new { id = Model.ProjectId })"
                           class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors font-medium">
                            <i class="fas fa-project-diagram mr-1"></i>
                            @Model.ProjectName
                        </a>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.Description))
                    {
                        <div>
                            <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Description</h4>
                            <p class="text-neutral-700 dark:text-dark-200 bg-neutral-50 dark:bg-dark-700 p-3 rounded-lg">@Model.Description</p>
                        </div>
                    }

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Status</h4>
                            @{
                                var statusClass = Model.Status.ToString().ToLower() switch {
                                    "done" => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
                                    "inprogress" => "bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200",
                                    "inreview" => "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
                                    _ => "bg-neutral-100 dark:bg-dark-600 text-neutral-800 dark:text-dark-200"
                                };
                            }
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium @statusClass">
                                @Model.Status
                            </span>
                        </div>

                        <div>
                            <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Priority</h4>
                            @{
                                var priorityClass = Model.Priority.ToString().ToLower() switch {
                                    "critical" => "bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200",
                                    "high" => "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
                                    "medium" => "bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200",
                                    "low" => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
                                    _ => "bg-neutral-100 dark:bg-dark-600 text-neutral-800 dark:text-dark-200"
                                };
                            }
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium @priorityClass">
                                @Model.Priority Priority
                            </span>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Assigned To</h4>
                        <p class="text-neutral-900 dark:text-dark-100 font-medium">
                            <i class="fas fa-user mr-2 text-neutral-400 dark:text-dark-500"></i>
                            @(Model.AssignedToName ?? "Unassigned")
                        </p>
                    </div>
                </div>

                <!-- Dates and Metadata -->
                <div class="space-y-6">
                    @if (Model.StartDate.HasValue || Model.DueDate.HasValue)
                    {
                        <div class="space-y-4">
                            @if (Model.StartDate.HasValue)
                            {
                                <div>
                                    <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Start Date</h4>
                                    <p class="text-neutral-900 dark:text-dark-100 font-medium">
                                        <i class="fas fa-calendar-alt mr-2 text-neutral-400 dark:text-dark-500"></i>
                                        @Model.StartDate.Value.ToString("MMM dd, yyyy")
                                    </p>
                                </div>
                            }

                            @if (Model.DueDate.HasValue)
                            {
                                <div>
                                    <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Due Date</h4>
                                    <p class="font-medium @(Model.IsOverdue ? "text-danger-600 dark:text-danger-400" : "text-neutral-900 dark:text-dark-100")">
                                        <i class="fas fa-calendar-check mr-2 @(Model.IsOverdue ? "text-danger-600 dark:text-danger-400" : "text-neutral-400 dark:text-dark-500")"></i>
                                        @Model.DueDate.Value.ToString("MMM dd, yyyy")
                                        @if (Model.IsOverdue)
                                        {
                                            <span class="ml-2 text-xs">(Overdue)</span>
                                        }
                                    </p>
                                </div>
                            }
                        </div>
                    }

                    @if (Model.EstimatedHours > 0 || Model.ActualHours > 0)
                    {
                        <div>
                            <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-3">Time Tracking</h4>
                            <div class="bg-neutral-50 dark:bg-dark-700 p-4 rounded-lg space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-neutral-600 dark:text-dark-300">Estimated Hours:</span>
                                    <span class="text-sm font-medium text-neutral-900 dark:text-dark-100">@Model.EstimatedHours hrs</span>
                                </div>
                                @if (Model.ActualHours > 0)
                                {
                                    <div class="flex justify-between">
                                        <span class="text-sm text-neutral-600 dark:text-dark-300">Actual Hours:</span>
                                        <span class="text-sm font-medium text-neutral-900 dark:text-dark-100">@Model.ActualHours hrs</span>
                                    </div>
                                }
                            </div>
                        </div>
                    }

                    <div class="space-y-4">
                        <div>
                            <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Created By</h4>
                            <p class="text-neutral-900 dark:text-dark-100 font-medium">
                                <i class="fas fa-user mr-2 text-neutral-400 dark:text-dark-500"></i>
                                @Model.CreatedByName
                            </p>
                        </div>

                        <div>
                            <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Created On</h4>
                            <p class="text-neutral-900 dark:text-dark-100 font-medium">
                                <i class="fas fa-calendar-plus mr-2 text-neutral-400 dark:text-dark-500"></i>
                                @Model.CreatedAt.ToString("MMM dd, yyyy HH:mm")
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0 pt-8 border-t border-neutral-200 dark:border-dark-200">
                <div>
                    @{
                        ViewData["Text"] = "Cancel";
                        ViewData["Variant"] = "secondary";
                        ViewData["Icon"] = "fas fa-times";
                        ViewData["Href"] = Url.Action("Details", new { id = Model.Id });
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </div>
                <div>
                    <form asp-action="Delete" method="post" class="inline">
                        <input type="hidden" asp-for="Id" />
                        @Html.AntiForgeryToken()
                        @{
                            ViewData["Text"] = "Confirm Delete";
                            ViewData["Variant"] = "danger";
                            ViewData["Icon"] = "fas fa-trash";
                            ViewData["Type"] = "submit";
                            ViewData["Href"] = null;
                            ViewData["OnClick"] = "return confirm('Are you absolutely sure you want to delete this task? This action cannot be undone.')";
                        }
                        <partial name="Components/_Button" view-data="ViewData" />
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
