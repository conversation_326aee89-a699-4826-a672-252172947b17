using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities
{
    public abstract class BaseEntity
    {
        public int Id { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? UpdatedAt { get; set; }
        
        private DateTime? _deletedAt;
        public DateTime? DeletedAt 
        { 
            get => _deletedAt;
            set => _deletedAt = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }
        
        public bool IsDeleted { get; set; }
        
        // Method to update the UpdatedAt timestamp
        public void Touch()
        {
            UpdatedAt = DateTime.UtcNow;
        }
    }
}
