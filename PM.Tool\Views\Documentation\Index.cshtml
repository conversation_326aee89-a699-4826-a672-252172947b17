@using PM.Tool.Core.Models
@model List<DocumentationSection>
@{
    ViewData["Title"] = "Documentation";
    ViewData["Description"] = "Comprehensive documentation for PM Tool - project management platform";
    Layout = "_DocumentationLayout";

    var metadata = ViewBag.Metadata as DocumentationMetadata;
    var popularPages = ViewBag.PopularPages as List<DocumentationPage>;
    var config = ViewBag.Configuration as DocumentationConfiguration;
}

<!-- Documentation Home -->
<div class="max-w-6xl mx-auto">
    <!-- Hero Section -->
    <div class="text-center mb-12">
        <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-book-open text-3xl text-primary-600 dark:text-primary-400"></i>
        </div>
        <h1 class="text-4xl font-bold text-neutral-900 dark:text-white mb-4">Documentation</h1>
        <p class="text-xl text-neutral-600 dark:text-neutral-300 max-w-2xl mx-auto">
            Comprehensive documentation for PM Tool - project management platform
        </p>
    </div>

    <!-- Statistics -->
    @if (metadata != null)
    {
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            <div class="text-center p-6 bg-white dark:bg-gray-800 rounded-xl border border-neutral-200 dark:border-gray-700">
                <div class="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2">@metadata.TotalPages</div>
                <div class="text-neutral-600 dark:text-neutral-300">Pages</div>
            </div>
            <div class="text-center p-6 bg-white dark:bg-gray-800 rounded-xl border border-neutral-200 dark:border-gray-700">
                <div class="text-3xl font-bold text-success-600 dark:text-success-400 mb-2">@metadata.TotalSections</div>
                <div class="text-neutral-600 dark:text-neutral-300">Sections</div>
            </div>
            <div class="text-center p-6 bg-white dark:bg-gray-800 rounded-xl border border-neutral-200 dark:border-gray-700">
                <div class="text-3xl font-bold text-info-600 dark:text-info-400 mb-2">
                    @(metadata.Statistics.ContainsKey("estimated_read_time_minutes") ? metadata.Statistics["estimated_read_time_minutes"] : 0)
                </div>
                <div class="text-neutral-600 dark:text-neutral-300">Minutes</div>
            </div>
        </div>
    }

    <!-- Documentation Sections -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @foreach (var docSection in Model.OrderBy(s => s.Order))
        {
            <div class="bg-white dark:bg-gray-800 rounded-xl border border-neutral-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300 group">
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center mr-3">
                            <i class="@(docSection.Icon) text-primary-600 dark:text-primary-400"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-white">@docSection.Title</h3>
                    </div>
                    <p class="text-neutral-600 dark:text-neutral-300 mb-4 line-clamp-3">@docSection.Description</p>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-neutral-500 dark:text-neutral-400">@docSection.Pages.Count pages</span>
                        <a href="@Url.Action("Section", new { sectionId = docSection.Id })"
                           class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-lg hover:bg-primary-700 transition-colors group-hover:bg-primary-700">
                            View Section
                            <i class="fas fa-arrow-right ml-2 text-xs"></i>
                        </a>
                    </div>
                </div>
            </div>
        }
    </div>

    <!-- Empty State -->
    @if (!Model.Any())
    {
        <div class="text-center py-16">
            <div class="w-24 h-24 bg-neutral-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-book text-3xl text-neutral-400 dark:text-neutral-500"></i>
            </div>
            <h3 class="text-xl font-semibold text-neutral-900 dark:text-white mb-2">No Documentation Available</h3>
            <p class="text-neutral-600 dark:text-neutral-300 mb-6">Documentation is being prepared. Please check back later.</p>
            <a href="@Url.Action("Index", "Home")"
               class="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-primary-600 rounded-lg hover:bg-primary-700 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Application
            </a>
        </div>
    }
</div>


