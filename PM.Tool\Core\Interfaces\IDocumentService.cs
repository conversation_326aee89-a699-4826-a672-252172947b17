using PM.Tool.Core.Entities;

namespace PM.Tool.Core.Interfaces
{
    public interface IDocumentService
    {
        // Category management
        Task<DocumentCategory> CreateCategoryAsync(DocumentCategory category);
        Task<DocumentCategory?> GetCategoryByIdAsync(int categoryId);
        Task<IEnumerable<DocumentCategory>> GetAllCategoriesAsync();
        Task<DocumentCategory> UpdateCategoryAsync(DocumentCategory category);
        Task<bool> DeleteCategoryAsync(int categoryId);

        // Version management
        Task<DocumentVersion> AddVersionAsync(DocumentVersion version);
        Task<DocumentVersion?> GetVersionByIdAsync(int versionId);
        Task<IEnumerable<DocumentVersion>> GetVersionsForAttachmentAsync(int attachmentId, string attachmentType);
        Task<bool> DeleteVersionAsync(int versionId);

        // Attachment updates
        Task<(ProjectAttachment?, TaskAttachment?)> UpdateAttachmentCategoryAsync(int attachmentId, string attachmentType, int? categoryId);
        Task<(ProjectAttachment?, TaskAttachment?)> UpdateAttachmentMetadataAsync(
            int attachmentId, 
            string attachmentType, 
            string? description = null, 
            string? tags = null);
    }
}
