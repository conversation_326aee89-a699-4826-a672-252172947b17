using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities.Agile
{
    public class Epic : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [MaxLength(5000)]
        public string Description { get; set; } = string.Empty;

        public int ProjectId { get; set; }

        [MaxLength(50)]
        public string EpicKey { get; set; } = string.Empty; // EP-001, etc.

        public EpicStatus Status { get; set; } = EpicStatus.Draft;

        public EpicPriority Priority { get; set; } = EpicPriority.Medium;

        [MaxLength(2000)]
        public string? AcceptanceCriteria { get; set; }

        [MaxLength(1000)]
        public string? BusinessValue { get; set; }

        public decimal EstimatedStoryPoints { get; set; }

        public decimal ActualStoryPoints { get; set; }

        public string? OwnerId { get; set; }

        private DateTime? _targetDate;
        public DateTime? TargetDate
        {
            get => _targetDate;
            set => _targetDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        private DateTime? _completedDate;
        public DateTime? CompletedDate
        {
            get => _completedDate;
            set => _completedDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        [MaxLength(500)]
        public string? Tags { get; set; } // JSON array

        public int SortOrder { get; set; }

        // Navigation properties
        public virtual Project Project { get; set; } = null!;
        public virtual ApplicationUser? Owner { get; set; }
        public virtual ICollection<Feature> Features { get; set; } = new List<Feature>();
        public virtual ICollection<UserStory> UserStories { get; set; } = new List<UserStory>();

        // Computed properties
        public bool IsCompleted => Status == EpicStatus.Done;
        public bool IsOverdue => TargetDate.HasValue && DateTime.UtcNow > TargetDate.Value && !IsCompleted;
        public double ProgressPercentage => EstimatedStoryPoints > 0 ? (double)ActualStoryPoints / (double)EstimatedStoryPoints * 100 : 0;
        public int FeatureCount => Features.Count;
        public int CompletedFeatureCount => Features.Count(f => f.IsCompleted);
        public int UserStoryCount => UserStories.Count;
        public int CompletedUserStoryCount => UserStories.Count(us => us.IsCompleted);
    }

    public class UserStory : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [MaxLength(5000)]
        public string Description { get; set; } = string.Empty;

        public int ProjectId { get; set; }

        public int? EpicId { get; set; }
        public int? FeatureId { get; set; }

        public int? SprintId { get; set; }

        [MaxLength(50)]
        public string StoryKey { get; set; } = string.Empty; // US-001, etc.

        [Required]
        [MaxLength(1000)]
        public string AsA { get; set; } = string.Empty; // "As a [user type]"

        [Required]
        [MaxLength(1000)]
        public string IWant { get; set; } = string.Empty; // "I want [functionality]"

        [Required]
        [MaxLength(1000)]
        public string SoThat { get; set; } = string.Empty; // "So that [benefit]"

        [MaxLength(2000)]
        public string? AcceptanceCriteria { get; set; }

        public UserStoryStatus Status { get; set; } = UserStoryStatus.Backlog;

        public UserStoryPriority Priority { get; set; } = UserStoryPriority.Medium;

        public decimal StoryPoints { get; set; }

        public string? AssignedToUserId { get; set; }

        private DateTime? _targetDate;
        public DateTime? TargetDate
        {
            get => _targetDate;
            set => _targetDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        private DateTime? _completedDate;
        public DateTime? CompletedDate
        {
            get => _completedDate;
            set => _completedDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        [MaxLength(500)]
        public string? Tags { get; set; } // JSON array

        public int BacklogOrder { get; set; }

        public int KanbanColumn { get; set; } = 1; // 1=Backlog, 2=To Do, 3=In Progress, 4=Review, 5=Done

        // Navigation properties
        public virtual Project Project { get; set; } = null!;
        public virtual Epic? Epic { get; set; }
        public virtual Feature? Feature { get; set; }
        public virtual Sprint? Sprint { get; set; }
        public virtual ApplicationUser? AssignedTo { get; set; }
        public virtual ICollection<TaskEntity> Tasks { get; set; } = new List<TaskEntity>();
        public virtual ICollection<UserStoryComment> Comments { get; set; } = new List<UserStoryComment>();

        // Computed properties
        public bool IsCompleted => Status == UserStoryStatus.Done;
        public bool IsOverdue => TargetDate.HasValue && DateTime.UtcNow > TargetDate.Value && !IsCompleted;
        public string UserStoryFormat => $"As a {AsA}, I want {IWant}, so that {SoThat}";
        public int TaskCount => Tasks.Count;
        public int CompletedTaskCount => Tasks.Count(t => t.Status == Core.Enums.TaskStatus.Done);
        public double TaskProgressPercentage => TaskCount > 0 ? (double)CompletedTaskCount / TaskCount * 100 : 0;
    }

    public class Sprint : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(1000)]
        public string? Description { get; set; }

        public int ProjectId { get; set; }

        [MaxLength(50)]
        public string SprintKey { get; set; } = string.Empty; // SP-001, etc.

        private DateTime _startDate;
        public DateTime StartDate
        {
            get => _startDate;
            set => _startDate = DateTime.SpecifyKind(value, DateTimeKind.Utc);
        }

        private DateTime _endDate;
        public DateTime EndDate
        {
            get => _endDate;
            set => _endDate = DateTime.SpecifyKind(value, DateTimeKind.Utc);
        }

        public SprintStatus Status { get; set; } = SprintStatus.Planning;

        [MaxLength(2000)]
        public string? Goal { get; set; }

        public decimal PlannedStoryPoints { get; set; }

        public decimal CompletedStoryPoints { get; set; }

        public string? ScrumMasterId { get; set; }

        // Navigation properties
        public virtual Project Project { get; set; } = null!;
        public virtual ApplicationUser? ScrumMaster { get; set; }
        public virtual ICollection<UserStory> UserStories { get; set; } = new List<UserStory>();

        // Computed properties
        public bool IsActive => Status == SprintStatus.Active;
        public bool IsCompleted => Status == SprintStatus.Completed;
        public int DurationDays => (EndDate - StartDate).Days + 1;
        public int DaysRemaining => IsActive ? Math.Max(0, (EndDate - DateTime.UtcNow).Days) : 0;
        public double Velocity => (double)CompletedStoryPoints;
        public double ProgressPercentage => PlannedStoryPoints > 0 ? (double)CompletedStoryPoints / (double)PlannedStoryPoints * 100 : 0;
        public int UserStoryCount => UserStories.Count;
        public int CompletedUserStoryCount => UserStories.Count(us => us.IsCompleted);
    }

    public class UserStoryComment : BaseEntity
    {
        public int UserStoryId { get; set; }

        [Required]
        [MaxLength(2000)]
        public string Content { get; set; } = string.Empty;

        public string UserId { get; set; } = string.Empty;

        public CommentType Type { get; set; } = CommentType.General;

        // Navigation properties
        public virtual UserStory UserStory { get; set; } = null!;
        public virtual ApplicationUser User { get; set; } = null!;
    }

    public enum EpicStatus
    {
        Draft = 1,
        Ready = 2,
        InProgress = 3,
        Done = 4,
        Cancelled = 5
    }

    public enum EpicPriority
    {
        Critical = 1,
        High = 2,
        Medium = 3,
        Low = 4
    }

    public enum UserStoryStatus
    {
        New = 0,
        Backlog = 1,
        Ready = 2,
        InProgress = 3,
        Review = 4,
        Done = 5,
        Cancelled = 6
    }

    public enum UserStoryPriority
    {
        Critical = 1,
        High = 2,
        Medium = 3,
        Low = 4
    }

    public enum SprintStatus
    {
        Planning = 1,
        Active = 2,
        Completed = 3,
        Cancelled = 4
    }

    public enum CommentType
    {
        General = 1,
        Question = 2,
        Clarification = 3,
        Concern = 4,
        Approval = 5,
        Rejection = 6,
        Resolution = 7,
        StatusUpdate = 8
    }
}
