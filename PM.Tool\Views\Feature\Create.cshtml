@model PM.Tool.Models.ViewModels.FeatureCreateViewModel
@using PM.Tool.Core.Entities.Agile

@{
    ViewData["Title"] = "Create Feature";
    ViewData["PageTitle"] = "Create New Feature";
    ViewData["PageDescription"] = "Create a new feature for the project.";
    var project = ViewBag.Project as PM.Tool.Core.Entities.Project;
}

@section Styles {
    <style>
        .form-section {
            transition: all 0.2s ease-in-out;
        }
        .form-section:hover {
            transform: translateY(-1px);
        }
    </style>
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-plus mr-3 text-primary-600 dark:text-primary-400"></i>
                Create Feature
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                @(project?.Name ?? "Project") - Add a new feature to track development progress
            </p>
        </div>
        <div class="mt-4 sm:mt-0">
            @{
                ViewData["Text"] = "Back to Features";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Index", new { projectId = Model.ProjectId });
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Create Form -->
@{
    ViewData["Title"] = "Feature Information";
    ViewData["Icon"] = "fas fa-puzzle-piece";
}
<partial name="Components/_Card" view-data="ViewData">
    <form asp-action="Create" method="post" class="space-y-8">
        <input type="hidden" asp-for="ProjectId" />

        <!-- Basic Information -->
        <div class="form-section">
            <h3 class="text-lg font-medium text-neutral-900 dark:text-dark-100 mb-4">Basic Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Title -->
                @{
                    ViewData["Label"] = "Feature Title";
                    ViewData["Name"] = "Title";
                    ViewData["Type"] = "text";
                    ViewData["Required"] = true;
                    ViewData["Icon"] = "fas fa-heading";
                    ViewData["Placeholder"] = "Enter feature title...";
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Title");
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Epic -->
                @{
                    var epicOptions = "<option value=\"\">-- Select Epic (Optional) --</option>";
                    if (ViewBag.EpicId != null)
                    {
                        foreach (var epic in (IEnumerable<SelectListItem>)ViewBag.EpicId)
                        {
                            var selected = epic.Value == Model.EpicId.ToString() ? "selected" : "";
                            epicOptions += $"<option value=\"{epic.Value}\" {selected}>{epic.Text}</option>";
                        }
                    }

                    ViewData["Label"] = "Epic";
                    ViewData["Name"] = "EpicId";
                    ViewData["Type"] = "select";
                    ViewData["Required"] = false;
                    ViewData["Icon"] = "fas fa-layer-group";
                    ViewData["Options"] = epicOptions;
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("EpicId");
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Priority -->
                @{
                    var priorityOptions = "";
                    foreach (FeaturePriority priority in Enum.GetValues<FeaturePriority>())
                    {
                        var selected = priority == Model.Priority ? "selected" : "";
                        priorityOptions += $"<option value=\"{(int)priority}\" {selected}>{priority}</option>";
                    }

                    ViewData["Label"] = "Priority";
                    ViewData["Name"] = "Priority";
                    ViewData["Type"] = "select";
                    ViewData["Required"] = true;
                    ViewData["Icon"] = "fas fa-exclamation";
                    ViewData["Options"] = priorityOptions;
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Priority");
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Status -->
                @{
                    var statusOptions = "";
                    foreach (FeatureStatus status in Enum.GetValues<FeatureStatus>())
                    {
                        var selected = status == Model.Status ? "selected" : "";
                        statusOptions += $"<option value=\"{(int)status}\" {selected}>{status}</option>";
                    }

                    ViewData["Label"] = "Status";
                    ViewData["Name"] = "Status";
                    ViewData["Type"] = "select";
                    ViewData["Required"] = true;
                    ViewData["Icon"] = "fas fa-flag";
                    ViewData["Options"] = statusOptions;
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Status");
                }
                <partial name="Components/_FormInput" view-data="ViewData" />
            </div>
        </div>

        <!-- Description & Details -->
        <div class="form-section">
            <h3 class="text-lg font-medium text-neutral-900 dark:text-dark-100 mb-4">Description & Details</h3>
            <div class="space-y-6">
                <!-- Description -->
                @{
                    ViewData["Label"] = "Description";
                    ViewData["Name"] = "Description";
                    ViewData["Type"] = "textarea";
                    ViewData["Required"] = true;
                    ViewData["Icon"] = "fas fa-align-left";
                    ViewData["Placeholder"] = "Describe the feature and its purpose...";
                    ViewData["Rows"] = "4";
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Description");
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Business Value -->
                @{
                    ViewData["Label"] = "Business Value";
                    ViewData["Name"] = "BusinessValue";
                    ViewData["Type"] = "textarea";
                    ViewData["Required"] = false;
                    ViewData["Icon"] = "fas fa-chart-line";
                    ViewData["Placeholder"] = "Explain the business value and impact...";
                    ViewData["Rows"] = "3";
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("BusinessValue");
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Acceptance Criteria -->
                @{
                    ViewData["Label"] = "Acceptance Criteria";
                    ViewData["Name"] = "AcceptanceCriteria";
                    ViewData["Type"] = "textarea";
                    ViewData["Required"] = false;
                    ViewData["Icon"] = "fas fa-check-square";
                    ViewData["Placeholder"] = "Define the acceptance criteria for this feature...";
                    ViewData["Rows"] = "4";
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("AcceptanceCriteria");
                }
                <partial name="Components/_FormInput" view-data="ViewData" />
            </div>
        </div>

        <!-- Planning & Estimation -->
        <div class="form-section">
            <h3 class="text-lg font-medium text-neutral-900 dark:text-dark-100 mb-4">Planning & Estimation</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Estimated Story Points -->
                @{
                    ViewData["Label"] = "Estimated Story Points";
                    ViewData["Name"] = "EstimatedStoryPoints";
                    ViewData["Type"] = "number";
                    ViewData["Required"] = false;
                    ViewData["Icon"] = "fas fa-calculator";
                    ViewData["Placeholder"] = "0";
                    ViewData["Min"] = "0";
                    ViewData["Step"] = "1";
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("EstimatedStoryPoints");
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Target Date -->
                @{
                    ViewData["Label"] = "Target Date";
                    ViewData["Name"] = "TargetDate";
                    ViewData["Type"] = "date";
                    ViewData["Required"] = false;
                    ViewData["Icon"] = "fas fa-calendar";
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("TargetDate");
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Sort Order -->
                @{
                    ViewData["Label"] = "Sort Order";
                    ViewData["Name"] = "SortOrder";
                    ViewData["Type"] = "number";
                    ViewData["Required"] = false;
                    ViewData["Icon"] = "fas fa-sort-numeric-up";
                    ViewData["Placeholder"] = "0";
                    ViewData["Min"] = "0";
                    ViewData["Step"] = "1";
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("SortOrder");
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Tags -->
                @{
                    ViewData["Label"] = "Tags";
                    ViewData["Name"] = "Tags";
                    ViewData["Type"] = "text";
                    ViewData["Required"] = false;
                    ViewData["Icon"] = "fas fa-tags";
                    ViewData["Placeholder"] = "Enter tags separated by commas...";
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Tags");
                }
                <partial name="Components/_FormInput" view-data="ViewData" />
            </div>
        </div>

        <!-- Form Actions -->
        @{
            ViewData["SubmitText"] = "Create Feature";
            ViewData["SubmitIcon"] = "fas fa-plus";
            ViewData["CancelUrl"] = Url.Action("Index", new { projectId = Model.ProjectId });
            ViewData["ShowReset"] = true;
        }
        <partial name="Components/_FormActions" view-data="ViewData" />
    </form>
</partial>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        $(document).ready(function() {
            setupFormValidation();
            setupFormEnhancements();
        });

        function setupFormValidation() {
            $('form').on('submit', function(e) {
                var isValid = true;
                var errors = [];

                // Validate required fields
                $(this).find('[required]').each(function() {
                    var $field = $(this);
                    var value = $field.val().trim();

                    if (!value) {
                        isValid = false;
                        $field.addClass('border-red-300 dark:border-red-600');
                        errors.push($field.closest('.form-group').find('label').text() + ' is required');
                    } else {
                        $field.removeClass('border-red-300 dark:border-red-600');
                    }
                });

                // Validate title length
                var title = $('input[name="Title"]').val().trim();
                if (title && title.length < 3) {
                    isValid = false;
                    errors.push('Feature title must be at least 3 characters long');
                }

                // Validate description length
                var description = $('textarea[name="Description"]').val().trim();
                if (description && description.length < 10) {
                    isValid = false;
                    errors.push('Description must be at least 10 characters long');
                }

                // Validate target date
                var targetDate = $('input[name="TargetDate"]').val();
                if (targetDate) {
                    var target = new Date(targetDate);
                    var today = new Date();
                    today.setHours(0, 0, 0, 0);

                    if (target < today) {
                        isValid = false;
                        errors.push('Target date cannot be in the past');
                    }
                }

                if (!isValid) {
                    e.preventDefault();
                    showValidationErrors(errors);
                } else {
                    hideValidationErrors();
                }
            });
        }

        function setupFormEnhancements() {
            // Auto-resize textareas
            $('textarea').on('input', function() {
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
            });

            // Character counter for description
            $('textarea[name="Description"]').on('input', function() {
                var length = $(this).val().length;
                var counter = $(this).siblings('.char-counter');

                if (counter.length === 0) {
                    counter = $('<div class="char-counter text-xs text-neutral-500 dark:text-dark-400 mt-1"></div>');
                    $(this).after(counter);
                }

                counter.text(length + ' characters');

                if (length < 10) {
                    counter.addClass('text-red-500').removeClass('text-neutral-500');
                } else {
                    counter.removeClass('text-red-500').addClass('text-neutral-500');
                }
            });

            // Tag input enhancement
            $('input[name="Tags"]').on('blur', function() {
                var tags = $(this).val().split(',').map(tag => tag.trim()).filter(tag => tag);
                $(this).val(tags.join(', '));
            });
        }

        function showValidationErrors(errors) {
            hideValidationErrors();

            var errorHtml = `
                <div class="validation-errors bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <i class="fas fa-exclamation-circle text-red-600 dark:text-red-400 mt-0.5 mr-3"></i>
                        <div>
                            <h4 class="text-sm font-medium text-red-800 dark:text-red-200 mb-2">Please correct the following errors:</h4>
                            <ul class="text-sm text-red-700 dark:text-red-300 list-disc list-inside space-y-1">
                                ${errors.map(error => `<li>${error}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                </div>
            `;

            $('form').prepend(errorHtml);
            $('html, body').animate({ scrollTop: 0 }, 300);
        }

        function hideValidationErrors() {
            $('.validation-errors').remove();
        }
    </script>
}
