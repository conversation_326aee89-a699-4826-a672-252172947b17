@* Task Details Scripts *@
<script>
    $(document).ready(function() {
        // Initialize task details functionality
        initializeTaskDetails();
    });

    function initializeTaskDetails() {
        // Initialize modal functions
        initializeModals();

        // Initialize comment functionality
        initializeCommentForm();

        // Initialize file upload functionality
        initializeFileUpload();

        // Initialize UI enhancements
        initializeUIEnhancements();
    }

    function initializeModals() {
        // Modal functions
        window.showDeleteModal = function() {
            const modal = document.getElementById('deleteTaskModal');
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        };

        window.showUploadModal = function() {
            const modal = document.getElementById('uploadAttachmentModal');
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        };

        window.closeModal = function(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = '';
            }
        };

        // Close modals on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const openModals = document.querySelectorAll('.modal-custom:not(.hidden)');
                openModals.forEach(modal => {
                    closeModal(modal.id);
                });
            }
        });
    }

    function initializeCommentForm() {
        // Add Comment Form
        $('#addCommentForm').on('submit', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const form = $(this);
            const submitBtn = form.find('button[type="submit"]');

            // Prevent double submission
            if (submitBtn.prop('disabled')) {
                return false;
            }

            const content = form.find('textarea[name="content"]').val().trim();
            const taskId = form.find('input[name="taskId"]').val();

            if (!content) {
                alert('Please enter a comment.');
                return false;
            }

            // Show loading state
            const originalText = submitBtn.html();
            submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Adding...');

            // Ensure body scroll is not locked before starting
            document.body.style.overflow = '';

            $.ajax({
                url: '@Url.Action("Create", "Comment")',
                type: 'POST',
                data: {
                    content: content,
                    taskId: taskId,
                    __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                },
                success: function(response) {
                    handleCommentSuccess(response, form);
                },
                error: function(xhr, status, error) {
                    handleCommentError(xhr, status, error);
                },
                complete: function() {
                    resetCommentForm(submitBtn, originalText);
                }
            });
        });
    }

    function handleCommentSuccess(response, form) {
        try {
            const newComment = $(`
                <div class="comment comment-card p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg fade-in-up" data-comment-id="${response.id}">
                    <div class="flex space-x-3">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                                <span class="text-sm font-semibold text-primary-600 dark:text-primary-400">
                                    ${response.authorName.charAt(0).toUpperCase()}
                                </span>
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center space-x-2 mb-2">
                                <h4 class="text-sm font-semibold text-neutral-900 dark:text-dark-100">${response.authorName}</h4>
                                <span class="text-xs text-neutral-500 dark:text-dark-400">
                                    ${new Date(response.createdAt).toLocaleString()}
                                </span>
                            </div>
                            <div class="prose prose-sm prose-neutral dark:prose-invert max-w-none">
                            </div>
                        </div>
                    </div>
                </div>
            `);

            // Set the content separately to avoid template literal issues
            newComment.find('.prose').html(response.content);

            // Check if there's an empty state and remove it
            const emptyState = $('#commentsList .text-center');
            if (emptyState.length) {
                emptyState.remove();
            }

            $('#commentsList').prepend(newComment);
            form.find('textarea[name="content"]').val('');

            // Update comment count
            const currentCount = $('#commentsList .comment').length;
            $('.card-header-custom p').text(`${currentCount} comment(s)`);

            // Ensure body scroll is not locked
            document.body.style.overflow = '';

            // Show success feedback
            const textarea = form.find('textarea[name="content"]');
            textarea.focus();

        } catch (error) {
            console.error('Error processing comment response:', error);
            alert('Comment added but there was an error updating the display. Please refresh the page.');
        }
    }

    function handleCommentError(xhr, status, error) {
        console.error('Error adding comment:', error);

        // Ensure body scroll is not locked
        document.body.style.overflow = '';

        // Show user-friendly error message
        let errorMessage = 'Error adding comment. Please try again.';
        if (xhr.responseJSON && xhr.responseJSON.message) {
            errorMessage = xhr.responseJSON.message;
        } else if (xhr.status === 403) {
            errorMessage = 'You do not have permission to add comments to this task.';
        } else if (xhr.status === 404) {
            errorMessage = 'Task not found. Please refresh the page and try again.';
        }

        alert(errorMessage);
    }

    function resetCommentForm(submitBtn, originalText) {
        // Always reset button state and ensure UI is not stuck
        try {
            submitBtn.prop('disabled', false).html(originalText);

            // Ensure body scroll is restored
            document.body.style.overflow = '';

            // Remove any stuck modal backdrops
            $('.modal-backdrop').remove();

            // Ensure no modals are stuck open
            $('.modal-custom:not(.hidden)').addClass('hidden');

        } catch (error) {
            console.error('Error in complete handler:', error);
            // Fallback: force reset button
            if (submitBtn.length) {
                submitBtn[0].disabled = false;
                submitBtn[0].innerHTML = originalText;
            }
        }
    }

    function initializeFileUpload() {
        // File upload progress
        $('form[enctype="multipart/form-data"]').on('submit', function() {
            const submitBtn = $(this).find('button[type="submit"]');
            const originalText = submitBtn.text();
            submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>Uploading...');
        });
    }

    function initializeUIEnhancements() {
        // Add hover effects to cards
        $('.comment-card, .subtask-card, .attachment-card').hover(
            function() {
                $(this).addClass('shadow-md');
            },
            function() {
                $(this).removeClass('shadow-md');
            }
        );

        // Initialize progress bar animations
        $('.progress-bar').each(function() {
            const $bar = $(this);
            const width = $bar.css('width');
            $bar.css('--progress-width', width);
        });
    }

    // Global cleanup function to fix stuck UI states
    window.fixStuckUI = function() {
        try {
            // Restore body scroll
            document.body.style.overflow = '';

            // Remove any stuck modal backdrops
            $('.modal-backdrop').remove();

            // Close any stuck modals
            $('.modal-custom:not(.hidden)').addClass('hidden');

            // Re-enable any disabled buttons
            $('button:disabled').each(function() {
                const $btn = $(this);
                if ($btn.html().includes('fa-spinner')) {
                    $btn.prop('disabled', false);
                    // Try to restore original text
                    if ($btn.data('original-text')) {
                        $btn.html($btn.data('original-text'));
                    } else {
                        $btn.html($btn.html().replace(/<i[^>]*fa-spinner[^>]*><\/i>\s*/g, ''));
                    }
                }
            });

            console.log('UI cleanup completed');
        } catch (error) {
            console.error('Error during UI cleanup:', error);
        }
    };

    // Auto-cleanup on page visibility change (when user switches tabs and comes back)
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            setTimeout(window.fixStuckUI, 1000);
        }
    });

    // Task History Functions
    window.showAllHistory = function() {
        // This would typically load more history via AJAX
        // For now, we'll just show a message
        alert('Loading full history... (This would be implemented with AJAX to load more records)');
    };

    // Tab Switching Functions
    window.switchTab = function(tabName) {
        // Remove active class from all tab buttons
        document.querySelectorAll('.tab-button').forEach(button => {
            button.classList.remove('active');
        });

        // Hide all tab contents
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
            content.classList.add('hidden');
        });

        // Activate the selected tab button
        const activeButton = document.querySelector(`[data-tab="${tabName}"]`);
        if (activeButton) {
            activeButton.classList.add('active');
        }

        // Show the selected tab content
        const activeContent = document.getElementById(`${tabName}-tab`);
        if (activeContent) {
            activeContent.classList.remove('hidden');
            activeContent.classList.add('active');
        }

        // Store the active tab in localStorage for persistence
        localStorage.setItem('activeTaskTab', tabName);
    };

    // Initialize tabs on page load
    document.addEventListener('DOMContentLoaded', function() {
        // Restore the last active tab or default to assignment
        const savedTab = localStorage.getItem('activeTaskTab') || 'assignment';
        switchTab(savedTab);

        // Add click event listeners to tab buttons
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', function() {
                const tabName = this.getAttribute('data-tab');
                switchTab(tabName);
            });
        });
    });
</script>
