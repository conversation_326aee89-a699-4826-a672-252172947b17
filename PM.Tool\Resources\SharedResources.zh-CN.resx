﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <!-- Common UI Elements - Chinese Simplified -->
  <data name="Common.Save" xml:space="preserve">
    <value>ä¿å­˜</value>
  </data>
  <data name="Common.Cancel" xml:space="preserve">
    <value>å–æ¶ˆ</value>
  </data>
  <data name="Common.Delete" xml:space="preserve">
    <value>åˆ é™¤</value>
  </data>
  <data name="Common.Edit" xml:space="preserve">
    <value>ç¼–è¾‘</value>
  </data>
  <data name="Common.Create" xml:space="preserve">
    <value>åˆ›å»º</value>
  </data>
  <data name="Common.Update" xml:space="preserve">
    <value>æ›´æ–°</value>
  </data>
  <data name="Common.Details" xml:space="preserve">
    <value>è¯¦æƒ…</value>
  </data>
  <data name="Common.Back" xml:space="preserve">
    <value>è¿”å›ž</value>
  </data>
  <data name="Common.Next" xml:space="preserve">
    <value>ä¸‹ä¸€æ­¥</value>
  </data>
  <data name="Common.Previous" xml:space="preserve">
    <value>ä¸Šä¸€æ­¥</value>
  </data>
  <data name="Common.Search" xml:space="preserve">
    <value>æœç´¢</value>
  </data>
  <data name="Common.Filter" xml:space="preserve">
    <value>ç­›é€‰</value>
  </data>
  <data name="Common.Export" xml:space="preserve">
    <value>å¯¼å‡º</value>
  </data>
  <data name="Common.Import" xml:space="preserve">
    <value>å¯¼å…¥</value>
  </data>
  <data name="Common.Print" xml:space="preserve">
    <value>æ‰“å°</value>
  </data>
  <data name="Common.Download" xml:space="preserve">
    <value>ä¸‹è½½</value>
  </data>
  <data name="Common.Upload" xml:space="preserve">
    <value>ä¸Šä¼ </value>
  </data>
  <data name="Common.Yes" xml:space="preserve">
    <value>æ˜¯</value>
  </data>
  <data name="Common.No" xml:space="preserve">
    <value>å¦</value>
  </data>
  <data name="Common.OK" xml:space="preserve">
    <value>ç¡®å®š</value>
  </data>
  <data name="Common.Close" xml:space="preserve">
    <value>å…³é—­</value>
  </data>
  <data name="Common.Loading" xml:space="preserve">
    <value>åŠ è½½ä¸­...</value>
  </data>
  <data name="Common.NoData" xml:space="preserve">
    <value>æš‚æ— æ•°æ®</value>
  </data>
  <data name="Common.Error" xml:space="preserve">
    <value>é”™è¯¯</value>
  </data>
  <data name="Common.Success" xml:space="preserve">
    <value>æˆåŠŸ</value>
  </data>
  <data name="Common.Warning" xml:space="preserve">
    <value>è­¦å‘Š</value>
  </data>
  <data name="Common.Information" xml:space="preserve">
    <value>ä¿¡æ¯</value>
  </data>
  <!-- Navigation - Chinese Simplified -->
  <data name="Nav.Dashboard" xml:space="preserve">
    <value>ä»ªè¡¨æ¿</value>
  </data>
  <data name="Nav.Projects" xml:space="preserve">
    <value>é¡¹ç›®</value>
  </data>
  <data name="Nav.Tasks" xml:space="preserve">
    <value>ä»»åŠ¡</value>
  </data>
  <data name="Nav.Management" xml:space="preserve">
    <value>ç®¡ç†</value>
  </data>
  <data name="Nav.Analytics" xml:space="preserve">
    <value>åˆ†æž</value>
  </data>
  <data name="Nav.Resources" xml:space="preserve">
    <value>èµ„æº</value>
  </data>
  <data name="Nav.Risks" xml:space="preserve">
    <value>é£Žé™©</value>
  </data>
  <data name="Nav.Meetings" xml:space="preserve">
    <value>ä¼šè®®</value>
  </data>
  <data name="Nav.Requirements" xml:space="preserve">
    <value>éœ€æ±‚</value>
  </data>
  <data name="Nav.Backlog" xml:space="preserve">
    <value>å¾…åŠžäº‹é¡¹</value>
  </data>
  <data name="Nav.Kanban" xml:space="preserve">
    <value>çœ‹æ¿</value>
  </data>
  <data name="Nav.Documentation" xml:space="preserve">
    <value>æ–‡æ¡£</value>
  </data>
  <data name="Nav.SkillsManagement" xml:space="preserve">
    <value>æŠ€èƒ½ç®¡ç†</value>
  </data>
  <data name="Nav.ResourceUtilization" xml:space="preserve">
    <value>èµ„æºåˆ©ç”¨çŽ‡</value>
  </data>
  <data name="Nav.MeetingCalendar" xml:space="preserve">
    <value>ä¼šè®®æ—¥åŽ†</value>
  </data>
  <data name="Nav.ActionItems" xml:space="preserve">
    <value>è¡ŒåŠ¨é¡¹</value>
  </data>
  <data name="Nav.AnalyticsDashboard" xml:space="preserve">
    <value>åˆ†æžä»ªè¡¨æ¿</value>
  </data>
  <data name="Nav.AdvancedReports" xml:space="preserve">
    <value>é«˜çº§æŠ¥å‘Š</value>
  </data>
  <data name="Nav.TeamAnalytics" xml:space="preserve">
    <value>å›¢é˜Ÿåˆ†æž</value>
  </data>
  <data name="Nav.BurndownCharts" xml:space="preserve">
    <value>ç‡ƒå°½å›¾</value>
  </data>
  <data name="Nav.VelocityCharts" xml:space="preserve">
    <value>é€Ÿåº¦å›¾</value>
  </data>
  <data name="Nav.ExportData" xml:space="preserve">
    <value>å¯¼å‡ºæ•°æ®</value>
  </data>
  <!-- Project Management - Chinese Simplified -->
  <data name="Project.Title" xml:space="preserve">
    <value>æ ‡é¢˜</value>
  </data>
  <data name="Project.Description" xml:space="preserve">
    <value>æè¿°</value>
  </data>
  <data name="Project.StartDate" xml:space="preserve">
    <value>å¼€å§‹æ—¥æœŸ</value>
  </data>
  <data name="Project.EndDate" xml:space="preserve">
    <value>ç»“æŸæ—¥æœŸ</value>
  </data>
  <data name="Project.Status" xml:space="preserve">
    <value>çŠ¶æ€</value>
  </data>
  <data name="Project.Priority" xml:space="preserve">
    <value>ä¼˜å…ˆçº§</value>
  </data>
  <data name="Project.Budget" xml:space="preserve">
    <value>é¢„ç®—</value>
  </data>
  <data name="Project.Progress" xml:space="preserve">
    <value>è¿›åº¦</value>
  </data>
  <data name="Project.Manager" xml:space="preserve">
    <value>é¡¹ç›®ç»ç†</value>
  </data>
  <data name="Project.Team" xml:space="preserve">
    <value>å›¢é˜Ÿ</value>
  </data>
  <!-- Task Management - Chinese Simplified -->
  <data name="Task.Title" xml:space="preserve">
    <value>ä»»åŠ¡æ ‡é¢˜</value>
  </data>
  <data name="Task.Description" xml:space="preserve">
    <value>ä»»åŠ¡æè¿°</value>
  </data>
  <data name="Task.AssignedTo" xml:space="preserve">
    <value>åˆ†é…ç»™</value>
  </data>
  <data name="Task.DueDate" xml:space="preserve">
    <value>æˆªæ­¢æ—¥æœŸ</value>
  </data>
  <data name="Task.EstimatedHours" xml:space="preserve">
    <value>é¢„ä¼°å°æ—¶</value>
  </data>
  <data name="Task.ActualHours" xml:space="preserve">
    <value>å®žé™…å°æ—¶</value>
  </data>
  <data name="Task.StoryPoints" xml:space="preserve">
    <value>æ•…äº‹ç‚¹</value>
  </data>
  <!-- Agile Terms - Chinese Simplified -->
  <data name="Agile.Epic" xml:space="preserve">
    <value>å²è¯—</value>
  </data>
  <data name="Agile.UserStory" xml:space="preserve">
    <value>ç”¨æˆ·æ•…äº‹</value>
  </data>
  <data name="Agile.Sprint" xml:space="preserve">
    <value>å†²åˆº</value>
  </data>
  <data name="Agile.Backlog" xml:space="preserve">
    <value>å¾…åŠžäº‹é¡¹</value>
  </data>
  <data name="Agile.Kanban" xml:space="preserve">
    <value>çœ‹æ¿</value>
  </data>
  <data name="Agile.Scrum" xml:space="preserve">
    <value>Scrum</value>
  </data>
  <data name="Agile.Velocity" xml:space="preserve">
    <value>é€Ÿåº¦</value>
  </data>
  <data name="Agile.Burndown" xml:space="preserve">
    <value>ç‡ƒå°½</value>
  </data>
  <!-- Status Values - Chinese Simplified -->
  <data name="Status.Active" xml:space="preserve">
    <value>æ´»è·ƒ</value>
  </data>
  <data name="Status.Inactive" xml:space="preserve">
    <value>éžæ´»è·ƒ</value>
  </data>
  <data name="Status.Completed" xml:space="preserve">
    <value>å·²å®Œæˆ</value>
  </data>
  <data name="Status.InProgress" xml:space="preserve">
    <value>è¿›è¡Œä¸­</value>
  </data>
  <data name="Status.Pending" xml:space="preserve">
    <value>å¾…å¤„ç†</value>
  </data>
  <data name="Status.Cancelled" xml:space="preserve">
    <value>å·²å–æ¶ˆ</value>
  </data>
  <!-- Priority Values - Chinese Simplified -->
  <data name="Priority.Critical" xml:space="preserve">
    <value>ç´§æ€¥</value>
  </data>
  <data name="Priority.High" xml:space="preserve">
    <value>é«˜</value>
  </data>
  <data name="Priority.Medium" xml:space="preserve">
    <value>ä¸­</value>
  </data>
  <data name="Priority.Low" xml:space="preserve">
    <value>ä½Ž</value>
  </data>
  <!-- Messages - Chinese Simplified -->
  <data name="Message.SaveSuccess" xml:space="preserve">
    <value>é¡¹ç›®ä¿å­˜æˆåŠŸ</value>
  </data>
  <data name="Message.DeleteSuccess" xml:space="preserve">
    <value>é¡¹ç›®åˆ é™¤æˆåŠŸ</value>
  </data>
  <data name="Message.UpdateSuccess" xml:space="preserve">
    <value>é¡¹ç›®æ›´æ–°æˆåŠŸ</value>
  </data>
  <data name="Message.ErrorOccurred" xml:space="preserve">
    <value>å‘ç”Ÿé”™è¯¯ï¼Œè¯·é‡è¯•ã€‚</value>
  </data>
  <data name="Message.ConfirmDelete" xml:space="preserve">
    <value>æ‚¨ç¡®å®šè¦åˆ é™¤æ­¤é¡¹ç›®å—ï¼Ÿ</value>
  </data>
  <data name="Meeting.Title" xml:space="preserve">
    <value>[Meeting Title - zh-CN]</value>
  </data>
  <data name="Meeting.Description" xml:space="preserve">
    <value>[Description - zh-CN]</value>
  </data>
  <data name="Meeting.StartTime" xml:space="preserve">
    <value>[Start Time - zh-CN]</value>
  </data>
  <data name="Meeting.EndTime" xml:space="preserve">
    <value>[End Time - zh-CN]</value>
  </data>
  <data name="Meeting.Location" xml:space="preserve">
    <value>[Location - zh-CN]</value>
  </data>
  <data name="Meeting.Type" xml:space="preserve">
    <value>[Meeting Type - zh-CN]</value>
  </data>
  <data name="Meeting.Status" xml:space="preserve">
    <value>[Status - zh-CN]</value>
  </data>
  <data name="Meeting.Organizer" xml:space="preserve">
    <value>[Organizer - zh-CN]</value>
  </data>
  <data name="Meeting.Attendees" xml:space="preserve">
    <value>[Attendees - zh-CN]</value>
  </data>
  <data name="Meeting.ActionItems" xml:space="preserve">
    <value>[Action Items - zh-CN]</value>
  </data>
  <data name="Meeting.Documents" xml:space="preserve">
    <value>[Documents - zh-CN]</value>
  </data>
  <data name="Meeting.Minutes" xml:space="preserve">
    <value>[Meeting Minutes - zh-CN]</value>
  </data>
  <data name="Meeting.Agenda" xml:space="preserve">
    <value>[Agenda - zh-CN]</value>
  </data>
  <data name="Requirement.Title" xml:space="preserve">
    <value>[Requirement Title - zh-CN]</value>
  </data>
  <data name="Requirement.Description" xml:space="preserve">
    <value>[Description - zh-CN]</value>
  </data>
  <data name="Requirement.Type" xml:space="preserve">
    <value>[Type - zh-CN]</value>
  </data>
  <data name="Requirement.Priority" xml:space="preserve">
    <value>[Priority - zh-CN]</value>
  </data>
  <data name="Requirement.Status" xml:space="preserve">
    <value>[Status - zh-CN]</value>
  </data>
  <data name="Requirement.Source" xml:space="preserve">
    <value>[Source - zh-CN]</value>
  </data>
  <data name="Requirement.Stakeholder" xml:space="preserve">
    <value>[Stakeholder - zh-CN]</value>
  </data>
  <data name="Requirement.AcceptanceCriteria" xml:space="preserve">
    <value>[Acceptance Criteria - zh-CN]</value>
  </data>
  <data name="Requirement.BusinessValue" xml:space="preserve">
    <value>[Business Value - zh-CN]</value>
  </data>
  <data name="Requirement.Comments" xml:space="preserve">
    <value>[Comments - zh-CN]</value>
  </data>
  <data name="Requirement.Attachments" xml:space="preserve">
    <value>[Attachments - zh-CN]</value>
  </data>
  <data name="Requirement.Changes" xml:space="preserve">
    <value>[Change History - zh-CN]</value>
  </data>
  <data name="Requirement.Tasks" xml:space="preserve">
    <value>[Related Tasks - zh-CN]</value>
  </data>
  <data name="Risk.Title" xml:space="preserve">
    <value>[Risk Title - zh-CN]</value>
  </data>
  <data name="Risk.Description" xml:space="preserve">
    <value>[Description - zh-CN]</value>
  </data>
  <data name="Risk.Category" xml:space="preserve">
    <value>[Category - zh-CN]</value>
  </data>
  <data name="Risk.Probability" xml:space="preserve">
    <value>[Probability - zh-CN]</value>
  </data>
  <data name="Risk.Impact" xml:space="preserve">
    <value>[Impact - zh-CN]</value>
  </data>
  <data name="Risk.Score" xml:space="preserve">
    <value>[Risk Score - zh-CN]</value>
  </data>
  <data name="Risk.Status" xml:space="preserve">
    <value>[Status - zh-CN]</value>
  </data>
  <data name="Risk.Owner" xml:space="preserve">
    <value>[Risk Owner - zh-CN]</value>
  </data>
  <data name="Risk.MitigationPlan" xml:space="preserve">
    <value>[Mitigation Plan - zh-CN]</value>
  </data>
  <data name="Risk.MitigationActions" xml:space="preserve">
    <value>[Mitigation Actions - zh-CN]</value>
  </data>
  <data name="Risk.ContingencyPlan" xml:space="preserve">
    <value>[Contingency Plan - zh-CN]</value>
  </data>
  <data name="Resource.Name" xml:space="preserve">
    <value>[Resource Name - zh-CN]</value>
  </data>
  <data name="Resource.Type" xml:space="preserve">
    <value>[Type - zh-CN]</value>
  </data>
  <data name="Resource.Department" xml:space="preserve">
    <value>[Department - zh-CN]</value>
  </data>
  <data name="Resource.Location" xml:space="preserve">
    <value>[Location - zh-CN]</value>
  </data>
  <data name="Resource.HourlyRate" xml:space="preserve">
    <value>[Hourly Rate - zh-CN]</value>
  </data>
  <data name="Resource.Capacity" xml:space="preserve">
    <value>[Capacity - zh-CN]</value>
  </data>
  <data name="Resource.Skills" xml:space="preserve">
    <value>[Skills - zh-CN]</value>
  </data>
  <data name="Resource.Availability" xml:space="preserve">
    <value>[Availability - zh-CN]</value>
  </data>
  <data name="Resource.Utilization" xml:space="preserve">
    <value>[Utilization - zh-CN]</value>
  </data>
  <data name="Resource.Allocation" xml:space="preserve">
    <value>[Allocation - zh-CN]</value>
  </data>
  <data name="Agile.SprintPlanning" xml:space="preserve">
    <value>[Sprint Planning - zh-CN]</value>
  </data>
  <data name="Agile.SprintReview" xml:space="preserve">
    <value>[Sprint Review - zh-CN]</value>
  </data>
  <data name="Agile.SprintRetrospective" xml:space="preserve">
    <value>[Sprint Retrospective - zh-CN]</value>
  </data>
  <data name="Agile.DailyStandup" xml:space="preserve">
    <value>[Daily Standup - zh-CN]</value>
  </data>
  <data name="Agile.ProductBacklog" xml:space="preserve">
    <value>[Product Backlog - zh-CN]</value>
  </data>
  <data name="Agile.SprintBacklog" xml:space="preserve">
    <value>[Sprint Backlog - zh-CN]</value>
  </data>
  <data name="Agile.Definition" xml:space="preserve">
    <value>[Definition of Done - zh-CN]</value>
  </data>
  <data name="Agile.StoryPoints" xml:space="preserve">
    <value>[Story Points - zh-CN]</value>
  </data>
  <data name="Agile.SprintStarted" xml:space="preserve">
    <value>[Sprint started successfully - zh-CN]</value>
  </data>
  <data name="Agile.SprintCompleted" xml:space="preserve">
    <value>[Sprint completed successfully - zh-CN]</value>
  </data>
  <data name="Enum.MeetingType.General" xml:space="preserve">
    <value>[General - zh-CN]</value>
  </data>
  <data name="Enum.MeetingType.Standup" xml:space="preserve">
    <value>[Daily Standup - zh-CN]</value>
  </data>
  <data name="Enum.MeetingType.Planning" xml:space="preserve">
    <value>[Planning - zh-CN]</value>
  </data>
  <data name="Enum.MeetingType.Review" xml:space="preserve">
    <value>[Review - zh-CN]</value>
  </data>
  <data name="Enum.MeetingType.Retrospective" xml:space="preserve">
    <value>[Retrospective - zh-CN]</value>
  </data>
  <data name="Enum.MeetingType.Stakeholder" xml:space="preserve">
    <value>[Stakeholder - zh-CN]</value>
  </data>
  <data name="Enum.MeetingType.Requirements" xml:space="preserve">
    <value>[Requirements - zh-CN]</value>
  </data>
  <data name="Enum.MeetingType.Technical" xml:space="preserve">
    <value>[Technical - zh-CN]</value>
  </data>
  <data name="Enum.MeetingType.Status" xml:space="preserve">
    <value>[Status - zh-CN]</value>
  </data>
  <data name="Enum.MeetingStatus.Scheduled" xml:space="preserve">
    <value>[Scheduled - zh-CN]</value>
  </data>
  <data name="Enum.MeetingStatus.InProgress" xml:space="preserve">
    <value>[In Progress - zh-CN]</value>
  </data>
  <data name="Enum.MeetingStatus.Completed" xml:space="preserve">
    <value>[Completed - zh-CN]</value>
  </data>
  <data name="Enum.MeetingStatus.Cancelled" xml:space="preserve">
    <value>[Cancelled - zh-CN]</value>
  </data>
  <data name="Enum.MeetingStatus.Postponed" xml:space="preserve">
    <value>[Postponed - zh-CN]</value>
  </data>
  <data name="Enum.RequirementType.Functional" xml:space="preserve">
    <value>[Functional - zh-CN]</value>
  </data>
  <data name="Enum.RequirementType.NonFunctional" xml:space="preserve">
    <value>[Non-Functional - zh-CN]</value>
  </data>
  <data name="Enum.RequirementType.Business" xml:space="preserve">
    <value>[Business - zh-CN]</value>
  </data>
  <data name="Enum.RequirementType.Technical" xml:space="preserve">
    <value>[Technical - zh-CN]</value>
  </data>
  <data name="Enum.RequirementType.Performance" xml:space="preserve">
    <value>[Performance - zh-CN]</value>
  </data>
  <data name="Enum.RequirementType.Security" xml:space="preserve">
    <value>[Security - zh-CN]</value>
  </data>
  <data name="Enum.RequirementType.Usability" xml:space="preserve">
    <value>[Usability - zh-CN]</value>
  </data>
  <data name="Enum.RequirementType.Compliance" xml:space="preserve">
    <value>[Compliance - zh-CN]</value>
  </data>
  <data name="Enum.RequirementType.Integration" xml:space="preserve">
    <value>[Integration - zh-CN]</value>
  </data>
  <data name="Enum.RiskCategory.Technical" xml:space="preserve">
    <value>[Technical - zh-CN]</value>
  </data>
  <data name="Enum.RiskCategory.Schedule" xml:space="preserve">
    <value>[Schedule - zh-CN]</value>
  </data>
  <data name="Enum.RiskCategory.Budget" xml:space="preserve">
    <value>[Budget - zh-CN]</value>
  </data>
  <data name="Enum.RiskCategory.Resource" xml:space="preserve">
    <value>[Resource - zh-CN]</value>
  </data>
  <data name="Enum.RiskCategory.Quality" xml:space="preserve">
    <value>[Quality - zh-CN]</value>
  </data>
  <data name="Enum.RiskCategory.External" xml:space="preserve">
    <value>[External - zh-CN]</value>
  </data>
  <data name="Enum.RiskCategory.Organizational" xml:space="preserve">
    <value>[Organizational - zh-CN]</value>
  </data>
  <data name="Enum.ResourceType.Human" xml:space="preserve">
    <value>[Human Resource - zh-CN]</value>
  </data>
  <data name="Enum.ResourceType.Equipment" xml:space="preserve">
    <value>[Equipment - zh-CN]</value>
  </data>
  <data name="Enum.ResourceType.Material" xml:space="preserve">
    <value>[Material - zh-CN]</value>
  </data>
  <data name="Enum.ResourceType.Software" xml:space="preserve">
    <value>[Software - zh-CN]</value>
  </data>
  <data name="Enum.ResourceType.Facility" xml:space="preserve">
    <value>[Facility - zh-CN]</value>
  </data>
  <data name="WBS.ErrorLoadingStructure" xml:space="preserve">
    <value>[Error loading WBS structure - zh-CN]</value>
  </data>
  <data name="WBS.TaskCreatedSuccessfully" xml:space="preserve">
    <value>[Task created successfully - zh-CN]</value>
  </data>
  <data name="WBS.TaskDeletedSuccessfully" xml:space="preserve">
    <value>[Task deleted successfully - zh-CN]</value>
  </data>
  <data name="WBS.TaskDuplicatedSuccessfully" xml:space="preserve">
    <value>[Task duplicated successfully - zh-CN]</value>
  </data>
  <data name="WBS.TaskMovedSuccessfully" xml:space="preserve">
    <value>[Task moved {0} successfully - zh-CN]</value>
  </data>
  <data name="WBS.TaskStatusUpdated" xml:space="preserve">
    <value>[Task status updated to {0} - zh-CN]</value>
  </data>
  <data name="WBS.WbsCodesGenerated" xml:space="preserve">
    <value>[WBS codes generated successfully - zh-CN]</value>
  </data>
  <data name="WBS.ExportingTo" xml:space="preserve">
    <value>[Exporting WBS to {0}... - zh-CN]</value>
  </data>
  <data name="WBS.ExportingTask" xml:space="preserve">
    <value>[Exporting task... - zh-CN]</value>
  </data>
  <data name="WBS.PrintingWbs" xml:space="preserve">
    <value>[Opening print dialog... - zh-CN]</value>
  </data>
  <data name="WBS.CompactViewEnabled" xml:space="preserve">
    <value>[Compact view enabled - zh-CN]</value>
  </data>
  <data name="WBS.NormalViewEnabled" xml:space="preserve">
    <value>[Normal view enabled - zh-CN]</value>
  </data>
  <data name="WBS.DetailedViewEnabled" xml:space="preserve">
    <value>[Detailed view enabled - zh-CN]</value>
  </data>
  <data name="WBS.RefreshingView" xml:space="preserve">
    <value>[Refreshing WBS view... - zh-CN]</value>
  </data>
  <data name="WBS.FilteredToShow" xml:space="preserve">
    <value>[Filtered to show {0} tasks - zh-CN]</value>
  </data>
  <data name="WBS.ShowingAllTasks" xml:space="preserve">
    <value>[Showing all tasks - zh-CN]</value>
  </data>
  <data name="WBS.FilteredOverdue" xml:space="preserve">
    <value>[Filtered to show overdue tasks - zh-CN]</value>
  </data>
  <data name="WBS.TaskTitleRequired" xml:space="preserve">
    <value>[Task title is required - zh-CN]</value>
  </data>
  <data name="WBS.ProjectIdMissing" xml:space="preserve">
    <value>[Project ID is missing - zh-CN]</value>
  </data>
  <data name="WBS.ValidationError" xml:space="preserve">
    <value>[Validation error. Please check your input. - zh-CN]</value>
  </data>
  <data name="WBS.AccessDenied" xml:space="preserve">
    <value>[Access denied. Please refresh the page and try again. - zh-CN]</value>
  </data>
  <data name="WBS.RequestFormatError" xml:space="preserve">
    <value>[Request format error. Please try again. - zh-CN]</value>
  </data>
  <data name="WBS.ErrorCreatingTask" xml:space="preserve">
    <value>[Error creating task - zh-CN]</value>
  </data>
  <data name="WBS.ErrorLoadingTaskDetails" xml:space="preserve">
    <value>[Error loading task details - zh-CN]</value>
  </data>
  <data name="WBS.ErrorDeletingTask" xml:space="preserve">
    <value>[Error deleting task - zh-CN]</value>
  </data>
  <data name="WBS.ErrorDuplicatingTask" xml:space="preserve">
    <value>[Error duplicating task - zh-CN]</value>
  </data>
  <data name="WBS.ErrorMovingTask" xml:space="preserve">
    <value>[Error moving task {0} - zh-CN]</value>
  </data>
  <data name="WBS.ErrorUpdatingTaskStatus" xml:space="preserve">
    <value>[Error updating task status - zh-CN]</value>
  </data>
  <data name="WBS.ErrorGeneratingCodes" xml:space="preserve">
    <value>[Error generating WBS codes - zh-CN]</value>
  </data>
  <data name="WBS.ConfirmDeleteTask" xml:space="preserve">
    <value>[Are you sure you want to delete this task? This action cannot be undone. - zh-CN]</value>
  </data>
  <data name="WBS.ConfirmDuplicateTask" xml:space="preserve">
    <value>[Create a duplicate of this task? - zh-CN]</value>
  </data>
  <data name="WBS.ConfirmGenerateCodes" xml:space="preserve">
    <value>[This will regenerate all WBS codes. Continue? - zh-CN]</value>
  </data>
  <data name="WBS.CreateNewTask" xml:space="preserve">
    <value>[Create New Task - zh-CN]</value>
  </data>
  <data name="WBS.CreateChildTaskFor" xml:space="preserve">
    <value>[Create Child Task for: {0} - zh-CN]</value>
  </data>
  <data name="WBS.TaskNotFound" xml:space="preserve">
    <value>[Task not found - zh-CN]</value>
  </data>
  <data name="WBS.UnsupportedExportFormat" xml:space="preserve">
    <value>[Unsupported export format - zh-CN]</value>
  </data>
  <data name="WBS.GanttViewComingSoon" xml:space="preserve">
    <value>[Gantt view feature coming soon! - zh-CN]</value>
  </data>
  <data name="WBS.Action.ViewDetails" xml:space="preserve">
    <value>[View Details - zh-CN]</value>
  </data>
  <data name="WBS.Action.EditTask" xml:space="preserve">
    <value>[Edit Task - zh-CN]</value>
  </data>
  <data name="WBS.Action.Duplicate" xml:space="preserve">
    <value>[Duplicate - zh-CN]</value>
  </data>
  <data name="WBS.Action.AddChild" xml:space="preserve">
    <value>[Add Child - zh-CN]</value>
  </data>
  <data name="WBS.Action.MoreActions" xml:space="preserve">
    <value>[More Actions - zh-CN]</value>
  </data>
  <data name="WBS.Action.MoveUp" xml:space="preserve">
    <value>[Move Up - zh-CN]</value>
  </data>
  <data name="WBS.Action.MoveDown" xml:space="preserve">
    <value>[Move Down - zh-CN]</value>
  </data>
  <data name="WBS.Action.StartTask" xml:space="preserve">
    <value>[Start Task - zh-CN]</value>
  </data>
  <data name="WBS.Action.MarkInReview" xml:space="preserve">
    <value>[Mark In Review - zh-CN]</value>
  </data>
  <data name="WBS.Action.MarkComplete" xml:space="preserve">
    <value>[Mark Complete - zh-CN]</value>
  </data>
  <data name="WBS.Action.CancelTask" xml:space="preserve">
    <value>[Cancel Task - zh-CN]</value>
  </data>
  <data name="WBS.Action.ExportTask" xml:space="preserve">
    <value>[Export Task - zh-CN]</value>
  </data>
  <data name="WBS.Action.DeleteTask" xml:space="preserve">
    <value>[Delete Task - zh-CN]</value>
  </data>
  <data name="WBS.Label.Progress" xml:space="preserve">
    <value>[Progress - zh-CN]</value>
  </data>
  <data name="WBS.Label.Unassigned" xml:space="preserve">
    <value>[Unassigned - zh-CN]</value>
  </data>
  <data name="WBS.Label.Overdue" xml:space="preserve">
    <value>[Overdue - zh-CN]</value>
  </data>
  <data name="WBS.Label.Start" xml:space="preserve">
    <value>[Start - zh-CN]</value>
  </data>
  <data name="WBS.Label.Due" xml:space="preserve">
    <value>[Due - zh-CN]</value>
  </data>
  <data name="WBS.Label.GeneratedOn" xml:space="preserve">
    <value>[Generated on - zh-CN]</value>
  </data>
  <data name="WBS.Label.WorkBreakdownStructure" xml:space="preserve">
    <value>[Work Breakdown Structure - zh-CN]</value>
  </data>
</root>