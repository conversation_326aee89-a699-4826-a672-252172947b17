using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Models.Api
{
    /// <summary>
    /// Base API response structure
    /// </summary>
    public class ApiResponse
    {
        /// <summary>
        /// Indicates if the operation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Optional message describing the result
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// Timestamp when the response was generated
        /// </summary>
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// Generic API response with data
    /// </summary>
    /// <typeparam name="T">Type of the response data</typeparam>
    public class ApiResponse<T> : ApiResponse
    {
        /// <summary>
        /// The response data
        /// </summary>
        public T? Data { get; set; }
    }

    /// <summary>
    /// API response for paginated data
    /// </summary>
    /// <typeparam name="T">Type of the items in the collection</typeparam>
    public class ApiPagedResponse<T> : ApiResponse<IEnumerable<T>>
    {
        /// <summary>
        /// Pagination information
        /// </summary>
        public PaginationInfo? Pagination { get; set; }
    }

    /// <summary>
    /// Pagination metadata
    /// </summary>
    public class PaginationInfo
    {
        /// <summary>
        /// Current page number (1-based)
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// Number of items per page
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// Total number of items across all pages
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Total number of pages
        /// </summary>
        public int TotalPages { get; set; }

        /// <summary>
        /// Indicates if there is a next page
        /// </summary>
        public bool HasNextPage { get; set; }

        /// <summary>
        /// Indicates if there is a previous page
        /// </summary>
        public bool HasPreviousPage { get; set; }
    }

    /// <summary>
    /// API error response
    /// </summary>
    public class ApiErrorResponse : ApiResponse
    {
        /// <summary>
        /// Error details
        /// </summary>
        public ApiError? Error { get; set; }
    }

    /// <summary>
    /// Detailed error information
    /// </summary>
    public class ApiError
    {
        /// <summary>
        /// Error code for programmatic handling
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Human-readable error message
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Additional error details (validation errors, etc.)
        /// </summary>
        public object? Details { get; set; }

        /// <summary>
        /// Trace ID for debugging
        /// </summary>
        public string? TraceId { get; set; }
    }

    /// <summary>
    /// Base class for API request models with common query parameters
    /// </summary>
    public class BaseApiRequest
    {
        /// <summary>
        /// Page number for pagination (1-based)
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "Page must be greater than 0")]
        public int Page { get; set; } = 1;

        /// <summary>
        /// Number of items per page
        /// </summary>
        [Range(1, 100, ErrorMessage = "Page size must be between 1 and 100")]
        public int PageSize { get; set; } = 20;

        /// <summary>
        /// Search term for filtering results
        /// </summary>
        [StringLength(100, ErrorMessage = "Search term cannot exceed 100 characters")]
        public string? Search { get; set; }

        /// <summary>
        /// Field to sort by
        /// </summary>
        [StringLength(50, ErrorMessage = "Sort field cannot exceed 50 characters")]
        public string? SortBy { get; set; }

        /// <summary>
        /// Sort order (asc or desc)
        /// </summary>
        [RegularExpression("^(asc|desc)$", ErrorMessage = "Sort order must be 'asc' or 'desc'")]
        public string? SortOrder { get; set; } = "asc";
    }

    /// <summary>
    /// API request for creating resources
    /// </summary>
    public abstract class CreateApiRequest
    {
        // Base properties for create requests can be added here
    }

    /// <summary>
    /// API request for updating resources
    /// </summary>
    public abstract class UpdateApiRequest
    {
        /// <summary>
        /// ID of the resource to update
        /// </summary>
        [Required]
        public int Id { get; set; }
    }

    /// <summary>
    /// API request for bulk operations
    /// </summary>
    /// <typeparam name="T">Type of the operation data</typeparam>
    public class BulkApiRequest<T>
    {
        /// <summary>
        /// Collection of items to process
        /// </summary>
        [Required]
        [MinLength(1, ErrorMessage = "At least one item is required")]
        [MaxLength(100, ErrorMessage = "Cannot process more than 100 items at once")]
        public IEnumerable<T> Items { get; set; } = new List<T>();
    }

    /// <summary>
    /// Response for bulk operations
    /// </summary>
    /// <typeparam name="T">Type of the result data</typeparam>
    public class BulkApiResponse<T> : ApiResponse
    {
        /// <summary>
        /// Successfully processed items
        /// </summary>
        public IEnumerable<T> SuccessfulItems { get; set; } = new List<T>();

        /// <summary>
        /// Failed items with error details
        /// </summary>
        public IEnumerable<BulkOperationError> FailedItems { get; set; } = new List<BulkOperationError>();

        /// <summary>
        /// Total number of items processed
        /// </summary>
        public int TotalProcessed { get; set; }

        /// <summary>
        /// Number of successful operations
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// Number of failed operations
        /// </summary>
        public int FailureCount { get; set; }
    }

    /// <summary>
    /// Error information for bulk operations
    /// </summary>
    public class BulkOperationError
    {
        /// <summary>
        /// Index of the failed item in the original request
        /// </summary>
        public int Index { get; set; }

        /// <summary>
        /// The item that failed to process
        /// </summary>
        public object? Item { get; set; }

        /// <summary>
        /// Error code
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Error message
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Additional error details
        /// </summary>
        public object? Details { get; set; }
    }

    /// <summary>
    /// Response for file upload operations
    /// </summary>
    public class FileUploadResponse : ApiResponse<FileInfo>
    {
        /// <summary>
        /// Information about the uploaded file
        /// </summary>
        public class FileInfo
        {
            /// <summary>
            /// Unique identifier for the uploaded file
            /// </summary>
            public string FileId { get; set; } = string.Empty;

            /// <summary>
            /// Original filename
            /// </summary>
            public string FileName { get; set; } = string.Empty;

            /// <summary>
            /// File size in bytes
            /// </summary>
            public long FileSize { get; set; }

            /// <summary>
            /// MIME content type
            /// </summary>
            public string ContentType { get; set; } = string.Empty;

            /// <summary>
            /// URL for downloading the file
            /// </summary>
            public string DownloadUrl { get; set; } = string.Empty;

            /// <summary>
            /// When the file was uploaded
            /// </summary>
            public DateTime UploadedAt { get; set; }
        }
    }

    /// <summary>
    /// Response for long-running operations
    /// </summary>
    public class OperationStatusResponse : ApiResponse
    {
        /// <summary>
        /// Unique identifier for the operation
        /// </summary>
        public string OperationId { get; set; } = string.Empty;

        /// <summary>
        /// Current status of the operation
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Progress percentage (0-100)
        /// </summary>
        public int? ProgressPercentage { get; set; }

        /// <summary>
        /// Estimated completion time
        /// </summary>
        public DateTime? EstimatedCompletion { get; set; }

        /// <summary>
        /// Result data (available when operation is complete)
        /// </summary>
        public object? Result { get; set; }

        /// <summary>
        /// Error information (available when operation fails)
        /// </summary>
        public ApiError? Error { get; set; }
    }

    /// <summary>
    /// Health check response
    /// </summary>
    public class HealthCheckResponse : ApiResponse
    {
        /// <summary>
        /// Overall health status
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Individual component health checks
        /// </summary>
        public Dictionary<string, ComponentHealth> Components { get; set; } = new();

        /// <summary>
        /// API version information
        /// </summary>
        public VersionInfo Version { get; set; } = new();
    }

    /// <summary>
    /// Health status of individual components
    /// </summary>
    public class ComponentHealth
    {
        /// <summary>
        /// Component status (Healthy, Degraded, Unhealthy)
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Response time in milliseconds
        /// </summary>
        public long? ResponseTime { get; set; }

        /// <summary>
        /// Additional component details
        /// </summary>
        public object? Details { get; set; }
    }

    /// <summary>
    /// API version information
    /// </summary>
    public class VersionInfo
    {
        /// <summary>
        /// API version
        /// </summary>
        public string ApiVersion { get; set; } = string.Empty;

        /// <summary>
        /// Application version
        /// </summary>
        public string AppVersion { get; set; } = string.Empty;

        /// <summary>
        /// Build timestamp
        /// </summary>
        public DateTime? BuildDate { get; set; }

        /// <summary>
        /// Environment name
        /// </summary>
        public string Environment { get; set; } = string.Empty;
    }
}
