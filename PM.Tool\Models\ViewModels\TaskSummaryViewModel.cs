using PM.Tool.Core.Enums;

using TaskStatus = PM.Tool.Core.Enums.TaskStatus;

namespace PM.Tool.Models.ViewModels
{
    public class TaskSummaryViewModel
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public int ProjectId { get; set; }
        public string ProjectName { get; set; } = string.Empty;
        public TaskStatus Status { get; set; }
        public TaskPriority Priority { get; set; }
        public DateTime? DueDate { get; set; }        public string? AssignedToUserId { get; set; }
        public string? AssignedToName { get; set; }
        public int? ParentTaskId { get; set; }
        public double ProgressPercentage { get; set; }
        public bool IsOverdue { get; set; }
    }
}
