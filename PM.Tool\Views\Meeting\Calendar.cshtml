@model IEnumerable<PM.Tool.Core.Entities.Meeting>
@using PM.Tool.Core.Enums
@{
    ViewData["Title"] = "Meeting Calendar";
    var selectedDate = ViewBag.SelectedDate as DateTime? ?? DateTime.Today;
    var projectId = ViewBag.ProjectId as int?;
    var projectName = ViewBag.ProjectName as string;
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Meetings", Href = Url.Action("Index", "Meeting"), Icon = "fas fa-calendar" },
        new { Text = "Calendar", Href = "", Icon = "fas fa-calendar-alt" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-calendar-alt mr-3 text-primary-600 dark:text-primary-400"></i>
                Meeting Calendar
                @if (!string.IsNullOrEmpty(projectName))
                {
                    <span class="text-lg text-neutral-500 dark:text-dark-400 ml-2">- @projectName</span>
                }
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                View and manage your meetings in calendar format
            </p>
        </div>
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
            @{
                ViewData["Text"] = "Schedule Meeting";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-plus";
                ViewData["Href"] = Url.Action("Create", new { projectId = projectId });
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "List View";
                ViewData["Variant"] = "outline";
                ViewData["Icon"] = "fas fa-list";
                ViewData["Href"] = Url.Action("Index", new { projectId = projectId });
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Calendar Controls -->
<div class="card-custom mb-8">
    <div class="card-body-custom">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <!-- Date Navigation -->
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                    <button onclick="navigateMonth(-1)" class="btn-secondary-custom p-2">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 min-w-[200px] text-center" id="currentMonth">
                        @selectedDate.ToString("MMMM yyyy")
                    </h3>
                    <button onclick="navigateMonth(1)" class="btn-secondary-custom p-2">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
                <button onclick="goToToday()" class="btn-outline-custom">
                    Today
                </button>
            </div>

            <!-- View Controls -->
            <div class="flex items-center space-x-4">
                <!-- Project Filter -->
                @if (projectId == null)
                {
                    <div class="relative">
                        <select id="projectFilter" class="form-select-custom">
                            <option value="">All Projects</option>
                            <!-- Options will be populated via AJAX -->
                        </select>
                    </div>
                }

                <!-- Calendar View Toggle -->
                <div class="flex bg-neutral-100 dark:bg-dark-700 rounded-lg p-1">
                    <button onclick="setCalendarView('month')" class="calendar-view-btn active px-3 py-1 text-sm rounded-md" data-view="month">
                        Month
                    </button>
                    <button onclick="setCalendarView('week')" class="calendar-view-btn px-3 py-1 text-sm rounded-md" data-view="week">
                        Week
                    </button>
                    <button onclick="setCalendarView('day')" class="calendar-view-btn px-3 py-1 text-sm rounded-md" data-view="day">
                        Day
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Calendar Container -->
@{
    ViewData["Title"] = "Meeting Calendar";
    ViewData["Icon"] = "fas fa-calendar-alt";
    ViewData["BodyContent"] = @"
        <div id='calendar' class='bg-white dark:bg-dark-800 rounded-lg' style='min-height: 600px;'></div>
    ";
}
<partial name="Components/_Card" view-data="ViewData" />

<!-- Meeting Details Modal -->
<div id="meetingModal" class="fixed inset-0 z-50 hidden">
    <div class="fixed inset-0 bg-black bg-opacity-50" onclick="closeMeetingModal()"></div>
    <div class="fixed inset-0 flex items-center justify-center p-4">
        <div class="bg-white dark:bg-surface-dark rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100" id="modalTitle">Meeting Details</h3>
                    <button onclick="closeMeetingModal()" class="text-neutral-400 dark:text-dark-500 hover:text-neutral-600 dark:hover:text-dark-300">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div id="modalContent">
                    <!-- Meeting details will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Today's Meetings Sidebar -->
<div class="fixed right-4 top-1/2 transform -translate-y-1/2 w-80 hidden xl:block" id="todaysSidebar">
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-clock text-primary-600 dark:text-primary-400 text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Today's Meetings</h3>
                    <p class="text-sm text-neutral-500 dark:text-dark-400" id="todaysDate">@DateTime.Today.ToString("MMM dd, yyyy")</p>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <div id="todaysMeetings">
                <!-- Today's meetings will be populated here -->
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <!-- FullCalendar CSS and JS -->
    <link href='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css' rel='stylesheet' />
    <script src='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js'></script>

    <script>
        let calendar;
        let meetings = @Html.Raw(Json.Serialize(Model.Select(m => new {
            id = m.Id,
            title = m.Title,
            description = m.Description,
            start = m.ScheduledDate.ToString("yyyy-MM-ddTHH:mm:ss"),
            end = m.EndTime.ToString("yyyy-MM-ddTHH:mm:ss"),
            status = m.Status.ToString(),
            type = m.Type.ToString(),
            location = m.Location,
            projectId = m.ProjectId,
            projectName = m.Project?.Name,
            backgroundColor = GetMeetingColor(m.Status),
            borderColor = GetMeetingColor(m.Status),
            textColor = "#ffffff"
        })));

        $(document).ready(function() {
            initializeCalendar();
            loadProjects();
            loadTodaysMeetings();

            // Setup project filter
            $('#projectFilter').on('change', function() {
                const projectId = $(this).val();
                filterMeetingsByProject(projectId);
            });
        });

        function initializeCalendar() {
            const calendarEl = document.getElementById('calendar');

            calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,timeGridDay,listWeek'
                },
                height: 'auto',
                events: meetings,
                eventClick: function(info) {
                    showMeetingDetails(info.event);
                },
                dateClick: function(info) {
                    // Offer to create a meeting on clicked date
                    if (confirm(`Would you like to schedule a meeting on ${info.dateStr}?`)) {
                        window.location.href = '@Url.Action("Create")' + `?date=${info.dateStr}`;
                    }
                },
                eventDidMount: function(info) {
                    // Add tooltip
                    info.el.setAttribute('title',
                        `${info.event.title}\n${info.event.extendedProps.description || ''}\n${info.event.extendedProps.location || ''}`
                    );
                },
                dayMaxEvents: 3,
                moreLinkClick: 'popover',
                businessHours: {
                    daysOfWeek: [1, 2, 3, 4, 5],
                    startTime: '09:00',
                    endTime: '17:00'
                },
                slotMinTime: '08:00:00',
                slotMaxTime: '18:00:00',
                weekends: true,
                nowIndicator: true,
                selectable: true,
                selectMirror: true,
                select: function(selectionInfo) {
                    const startDate = selectionInfo.startStr;
                    if (confirm(`Would you like to schedule a meeting on ${startDate}?`)) {
                        window.location.href = '@Url.Action("Create")' + `?date=${startDate}`;
                    }
                    calendar.unselect();
                }
            });

            calendar.render();
        }

        function filterMeetingsByProject(projectId) {
            let filteredEvents = meetings;

            if (projectId) {
                filteredEvents = meetings.filter(meeting => meeting.projectId == projectId);
            }

            calendar.removeAllEvents();
            calendar.addEventSource(filteredEvents);
        }

        function setCalendarView(view) {
            const viewMap = {
                'month': 'dayGridMonth',
                'week': 'timeGridWeek',
                'day': 'timeGridDay'
            };

            if (calendar && viewMap[view]) {
                calendar.changeView(viewMap[view]);
            }

            // Update button states
            document.querySelectorAll('.calendar-view-btn').forEach(btn => {
                btn.classList.remove('active', 'bg-primary-600', 'text-white');
                btn.classList.add('text-neutral-600', 'dark:text-dark-300');
            });

            const activeBtn = document.querySelector(`[data-view="${view}"]`);
            if (activeBtn) {
                activeBtn.classList.add('active', 'bg-primary-600', 'text-white');
                activeBtn.classList.remove('text-neutral-600', 'dark:text-dark-300');
            }
        }

        function goToToday() {
            if (calendar) {
                calendar.today();
            }
        }

        function navigateMonth(direction) {
            if (calendar) {
                if (direction > 0) {
                    calendar.next();
                } else {
                    calendar.prev();
                }
            }
        }

        function formatTime(dateString) {
            const date = new Date(dateString);
            return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false });
        }

        function getMeetingsForDate(date) {
            return meetings.filter(meeting => {
                const meetingDate = new Date(meeting.start);
                return meetingDate.toDateString() === date.toDateString();
            });
        }

        function showMeetingDetails(event) {
            // Handle both FullCalendar event object and plain meeting object
            const meeting = event.extendedProps ? {
                id: event.id,
                title: event.title,
                description: event.extendedProps.description,
                start: event.startStr || event.start,
                end: event.endStr || event.end,
                status: event.extendedProps.status,
                location: event.extendedProps.location,
                projectName: event.extendedProps.projectName
            } : event;

            document.getElementById('modalTitle').textContent = meeting.title;
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-4">
                    <div>
                        <h4 class="font-medium text-neutral-900 dark:text-dark-100">Description</h4>
                        <p class="text-neutral-600 dark:text-dark-300">${meeting.description || 'No description'}</p>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <h4 class="font-medium text-neutral-900 dark:text-dark-100">Date & Time</h4>
                            <p class="text-neutral-600 dark:text-dark-300">${new Date(meeting.start).toLocaleDateString()}</p>
                            <p class="text-neutral-600 dark:text-dark-300">${formatTime(meeting.start)} - ${formatTime(meeting.end)}</p>
                        </div>
                        <div>
                            <h4 class="font-medium text-neutral-900 dark:text-dark-100">Status</h4>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusClass(meeting.status)}">${meeting.status}</span>
                        </div>
                    </div>
                    ${meeting.location ? `
                        <div>
                            <h4 class="font-medium text-neutral-900 dark:text-dark-100">Location</h4>
                            <p class="text-neutral-600 dark:text-dark-300">${meeting.location}</p>
                        </div>
                    ` : ''}
                    ${meeting.projectName ? `
                        <div>
                            <h4 class="font-medium text-neutral-900 dark:text-dark-100">Project</h4>
                            <p class="text-neutral-600 dark:text-dark-300">${meeting.projectName}</p>
                        </div>
                    ` : ''}
                    <div class="flex space-x-3 pt-4">
                        <a href="@Url.Action("Details")/${meeting.id}" class="btn-primary-custom">View Details</a>
                        <a href="@Url.Action("Edit")/${meeting.id}" class="btn-outline-custom">Edit</a>
                    </div>
                </div>
            `;
            document.getElementById('meetingModal').classList.remove('hidden');
        }

        function showDayMeetings(date, dayMeetings) {
            document.getElementById('modalTitle').textContent = `Meetings on ${date.toLocaleDateString()}`;
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-3">
                    ${dayMeetings.map(meeting => `
                        <div class="p-4 border border-neutral-200 dark:border-dark-200 rounded-lg">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h4 class="font-medium text-neutral-900 dark:text-dark-100">${meeting.title}</h4>
                                    <p class="text-sm text-neutral-600 dark:text-dark-300">${formatTime(meeting.scheduledDate)} - ${formatTime(meeting.endTime)}</p>
                                    ${meeting.location ? `<p class="text-sm text-neutral-500 dark:text-dark-400"><i class="fas fa-map-marker-alt mr-1"></i>${meeting.location}</p>` : ''}
                                </div>
                                <div class="flex space-x-2">
                                    <a href="@Url.Action("Details")/${meeting.id}" class="text-primary-600 dark:text-primary-400 hover:underline text-sm">View</a>
                                    <a href="@Url.Action("Edit")/${meeting.id}" class="text-neutral-600 dark:text-dark-300 hover:underline text-sm">Edit</a>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
            document.getElementById('meetingModal').classList.remove('hidden');
        }

        function closeMeetingModal() {
            document.getElementById('meetingModal').classList.add('hidden');
        }

        function getStatusClass(status) {
            const classes = {
                'Scheduled': 'bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200',
                'InProgress': 'bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200',
                'Completed': 'bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200',
                'Cancelled': 'bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200'
            };
            return classes[status] || 'bg-neutral-100 dark:bg-dark-600 text-neutral-800 dark:text-dark-200';
        }

        function loadProjects() {
            if (document.getElementById('projectFilter')) {
                $.get('@Url.Action("GetProjects", "Projects")')
                    .done(function(data) {
                        const select = $('#projectFilter');
                        data.forEach(function(project) {
                            select.append(`<option value="${project.id}" ${project.id == @(projectId ?? 0) ? 'selected' : ''}>${project.name}</option>`);
                        });
                    });
            }
        }

        function loadTodaysMeetings() {
            const today = new Date();
            const todayMeetings = getMeetingsForDate(today);
            const container = document.getElementById('todaysMeetings');

            if (todayMeetings.length === 0) {
                container.innerHTML = '<p class="text-sm text-neutral-500 dark:text-dark-400 text-center py-4">No meetings today</p>';
            } else {
                container.innerHTML = todayMeetings.map(meeting => `
                    <div class="p-3 border border-neutral-200 dark:border-dark-200 rounded-lg mb-3 cursor-pointer hover:bg-neutral-50 dark:hover:bg-dark-700" onclick="showMeetingDetails(${JSON.stringify(meeting).replace(/"/g, '&quot;')})">
                        <h4 class="font-medium text-neutral-900 dark:text-dark-100 text-sm">${meeting.title}</h4>
                        <p class="text-xs text-neutral-600 dark:text-dark-300">${formatTime(meeting.scheduledDate)} - ${formatTime(meeting.endTime)}</p>
                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getStatusClass(meeting.status)} mt-1">${meeting.status}</span>
                    </div>
                `).join('');
            }
        }

        // Initialize calendar view buttons
        document.addEventListener('DOMContentLoaded', function() {
            setCalendarView('month');
        });
    </script>
}

<style>
    .calendar-view-btn.active {
        background-color: rgb(37 99 235);
        color: white;
    }

    .calendar-view-btn:not(.active) {
        color: rgb(75 85 99);
    }

    .dark .calendar-view-btn:not(.active) {
        color: rgb(156 163 175);
    }

    .calendar-view-btn:not(.active):hover {
        background-color: rgb(229 231 235);
    }

    .dark .calendar-view-btn:not(.active):hover {
        background-color: rgb(55 65 81);
    }
</style>

@functions {
    string GetMeetingColor(MeetingStatus status)
    {
        return status switch
        {
            MeetingStatus.Scheduled => "#3b82f6", // Blue
            MeetingStatus.InProgress => "#f59e0b", // Amber
            MeetingStatus.Completed => "#10b981", // Green
            MeetingStatus.Cancelled => "#ef4444", // Red
            MeetingStatus.Postponed => "#6b7280", // Gray
            _ => "#6b7280"
        };
    }
}
