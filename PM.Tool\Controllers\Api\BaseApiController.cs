using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Interfaces;
using PM.Tool.Models.Api;
using System.Security.Claims;

namespace PM.Tool.Controllers.Api
{
    /// <summary>
    /// Base controller for all API endpoints providing common functionality
    /// </summary>
    [ApiController]
    [Route("api/v{version:apiVersion}/[controller]")]
    [Authorize]
    [Produces("application/json")]
    [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status500InternalServerError)]
    public abstract class BaseApiController : ControllerBase
    {
        protected readonly IAuditService _auditService;
        protected readonly ILogger _logger;

        protected BaseApiController(IAuditService auditService, ILogger logger)
        {
            _auditService = auditService;
            _logger = logger;
        }

        /// <summary>
        /// Gets the current user ID from the JWT token
        /// </summary>
        protected string? CurrentUserId => User.FindFirstValue(ClaimTypes.NameIdentifier);

        /// <summary>
        /// Gets the current user name from the JWT token
        /// </summary>
        protected string? CurrentUserName => User.FindFirstValue(ClaimTypes.Name);

        /// <summary>
        /// Logs audit information for API actions
        /// </summary>
        protected async Task LogAuditAsync(AuditAction action, string entityName, int? entityId = null, string? details = null)
        {
            try
            {
                await _auditService.LogAsync(action, entityName, entityId, CurrentUserId, details);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to log audit for {Action} on {EntityName} {EntityId}", action, entityName, entityId);
            }
        }

        /// <summary>
        /// Creates a standardized success response
        /// </summary>
        protected ApiResponse<T> Success<T>(T data, string? message = null)
        {
            return new ApiResponse<T>
            {
                Success = true,
                Data = data,
                Message = message,
                Timestamp = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Creates a standardized success response without data
        /// </summary>
        protected ApiResponse Success(string? message = null)
        {
            return new ApiResponse
            {
                Success = true,
                Message = message,
                Timestamp = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Creates a standardized paginated response
        /// </summary>
        protected ApiPagedResponse<T> PagedSuccess<T>(IEnumerable<T> data, int totalCount, int page, int pageSize, string? message = null)
        {
            return new ApiPagedResponse<T>
            {
                Success = true,
                Data = data,
                Message = message,
                Timestamp = DateTime.UtcNow,
                Pagination = new PaginationInfo
                {
                    Page = page,
                    PageSize = pageSize,
                    TotalCount = totalCount,
                    TotalPages = (int)Math.Ceiling((double)totalCount / pageSize),
                    HasNextPage = page * pageSize < totalCount,
                    HasPreviousPage = page > 1
                }
            };
        }

        /// <summary>
        /// Creates a standardized error response
        /// </summary>
        protected ApiErrorResponse Error(string message, string? code = null, object? details = null)
        {
            return new ApiErrorResponse
            {
                Success = false,
                Message = message,
                Error = new ApiError
                {
                    Code = code ?? "GENERAL_ERROR",
                    Message = message,
                    Details = details,
                    TraceId = HttpContext.TraceIdentifier
                },
                Timestamp = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Creates a validation error response
        /// </summary>
        protected ApiErrorResponse ValidationError(string message, Dictionary<string, string[]>? validationErrors = null)
        {
            return new ApiErrorResponse
            {
                Success = false,
                Message = message,
                Error = new ApiError
                {
                    Code = "VALIDATION_ERROR",
                    Message = message,
                    Details = validationErrors,
                    TraceId = HttpContext.TraceIdentifier
                },
                Timestamp = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Creates a not found error response
        /// </summary>
        protected ApiErrorResponse NotFoundError(string entityName, int id)
        {
            var message = $"{entityName} with ID {id} was not found.";
            return new ApiErrorResponse
            {
                Success = false,
                Message = message,
                Error = new ApiError
                {
                    Code = "NOT_FOUND",
                    Message = message,
                    Details = new { EntityName = entityName, Id = id },
                    TraceId = HttpContext.TraceIdentifier
                },
                Timestamp = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Handles exceptions and returns appropriate error responses
        /// </summary>
        protected IActionResult HandleException(Exception ex, string operation)
        {
            _logger.LogError(ex, "Error during {Operation}", operation);

            var errorResponse = Error(
                "An internal server error occurred. Please try again later.",
                "INTERNAL_ERROR",
                new { Operation = operation, ExceptionType = ex.GetType().Name }
            );

            return StatusCode(StatusCodes.Status500InternalServerError, errorResponse);
        }

        /// <summary>
        /// Validates model state and returns validation errors if any
        /// </summary>
        protected IActionResult? ValidateModelState()
        {
            if (!ModelState.IsValid)
            {
                var validationErrors = ModelState
                    .Where(x => x.Value?.Errors.Count > 0)
                    .ToDictionary(
                        kvp => kvp.Key,
                        kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToArray() ?? Array.Empty<string>()
                    );

                var errorResponse = ValidationError("One or more validation errors occurred.", validationErrors);
                return BadRequest(errorResponse);
            }

            return null;
        }

        /// <summary>
        /// Checks if the current user has access to the specified project
        /// </summary>
        protected async Task<bool> HasProjectAccessAsync(int projectId, IProjectService projectService)
        {
            try
            {
                var project = await projectService.GetProjectByIdAsync(projectId);
                if (project == null) return false;

                // Add your project access logic here
                // For now, we'll assume all authenticated users have access
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Applies common query parameters for filtering and pagination
        /// </summary>
        protected (int page, int pageSize, string? search, string? sortBy, bool sortDescending) ParseQueryParameters(
            int page = 1, 
            int pageSize = 20, 
            string? search = null, 
            string? sortBy = null, 
            string? sortOrder = null)
        {
            // Validate and constrain parameters
            page = Math.Max(1, page);
            pageSize = Math.Min(Math.Max(1, pageSize), 100); // Max 100 items per page
            
            var sortDescending = string.Equals(sortOrder, "desc", StringComparison.OrdinalIgnoreCase);

            return (page, pageSize, search, sortBy, sortDescending);
        }

        /// <summary>
        /// Creates a standardized created response with location header
        /// </summary>
        protected CreatedAtActionResult CreatedWithLocation<T>(string actionName, object routeValues, T data, string? message = null)
        {
            var response = Success(data, message);
            return CreatedAtAction(actionName, routeValues, response);
        }

        /// <summary>
        /// Creates a standardized no content response for successful operations without return data
        /// </summary>
        protected NoContentResult NoContentSuccess()
        {
            return NoContent();
        }

        /// <summary>
        /// Creates an accepted response for long-running operations
        /// </summary>
        protected AcceptedResult AcceptedOperation(string? location = null, object? value = null)
        {
            return Accepted(location, value);
        }
    }
}
