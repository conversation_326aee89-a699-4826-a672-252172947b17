2025-06-14 13:02:44.951 +06:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 13:02:47.309 +06:00 [INF] Executed DbCommand (50ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 13:02:47.388 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 13:02:47.395 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 13:02:47.424 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__normalizedEmail_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
2025-06-14 13:02:47.890 +06:00 [INF] Executed DbCommand (14ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE t."DueDate" IS NOT NULL AND t."DueDate" <= @__upcomingDeadlines_0 AND t."DueDate" > now() AND t."Status" <> 4
2025-06-14 13:02:47.932 +06:00 [INF] Now listening on: http://localhost:5045
2025-06-14 13:02:47.935 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", p0."Id", p0."IsActive", p0."JoinedAt", p0."ProjectId", p0."Role", p0."UserId"
FROM "Projects" AS p
LEFT JOIN "ProjectMembers" AS p0 ON p."Id" = p0."ProjectId"
WHERE p."EndDate" IS NOT NULL AND p."EndDate" <= @__upcomingDeadlines_0 AND p."EndDate" > now() AND p."Status" <> 5
ORDER BY p."Id"
2025-06-14 13:02:47.935 +06:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 13:02:47.938 +06:00 [INF] Deadline check completed at: "2025-06-14T13:02:47.9379318+06:00"
2025-06-14 13:02:47.938 +06:00 [INF] Hosting environment: Development
2025-06-14 13:02:47.941 +06:00 [INF] Content root path: D:\TGI\PM.Tool\PM.Tool
2025-06-14 13:03:04.125 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Risk - null null
2025-06-14 13:03:04.153 +06:00 [WRN] Failed to determine the https port for redirect.
2025-06-14 13:03:04.259 +06:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-14 13:03:04.270 +06:00 [INF] AuthenticationScheme: Identity.Application was challenged.
2025-06-14 13:03:04.279 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Risk - 302 0 null 156.2642ms
2025-06-14 13:03:04.304 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Identity/Account/Login?ReturnUrl=%2FRisk - null null
2025-06-14 13:03:04.315 +06:00 [INF] Executing endpoint '/Account/Login'
2025-06-14 13:03:04.349 +06:00 [INF] Route matched with {page = "/Account/Login", area = "Identity", action = "", controller = ""}. Executing page /Account/Login
2025-06-14 13:03:04.368 +06:00 [INF] Executing handler method Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginModel.OnGetAsync - ModelState is "Valid"
2025-06-14 13:03:04.372 +06:00 [INF] AuthenticationScheme: Identity.External signed out.
2025-06-14 13:03:04.374 +06:00 [INF] Executed handler method OnGetAsync, returned result .
2025-06-14 13:03:04.379 +06:00 [INF] Executing an implicit handler method - ModelState is "Valid"
2025-06-14 13:03:04.380 +06:00 [INF] Executed an implicit handler method, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-14 13:03:04.486 +06:00 [INF] Executed page /Account/Login in 133.9277ms
2025-06-14 13:03:04.489 +06:00 [INF] Executed endpoint '/Account/Login'
2025-06-14 13:03:04.492 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Identity/Account/Login?ReturnUrl=%2FRisk - 200 null text/html; charset=utf-8 187.4178ms
2025-06-14 13:03:04.537 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/css/tailwind.css?v=Vk1BkLOsT7-Yh4o1pDln8tZyZ3_rFi9yXuRHMX2g50E - null null
2025-06-14 13:03:04.539 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/js/tailwind-theme-manager.js?v=QS9OsoodixBTZAmxGj2gDzPtp60VU7kbda0nOueg4T0 - null null
2025-06-14 13:03:04.548 +06:00 [INF] The file /css/tailwind.css was not modified
2025-06-14 13:03:04.548 +06:00 [INF] The file /js/tailwind-theme-manager.js was not modified
2025-06-14 13:03:04.550 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/css/tailwind.css?v=Vk1BkLOsT7-Yh4o1pDln8tZyZ3_rFi9yXuRHMX2g50E - 304 null text/css 13.4055ms
2025-06-14 13:03:04.550 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/js/tailwind-theme-manager.js?v=QS9OsoodixBTZAmxGj2gDzPtp60VU7kbda0nOueg4T0 - 304 null text/javascript 10.9622ms
2025-06-14 13:03:14.175 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Identity/Account/Login - null null
2025-06-14 13:03:14.185 +06:00 [INF] Executing endpoint '/Account/Login'
2025-06-14 13:03:14.188 +06:00 [INF] Route matched with {page = "/Account/Login", area = "Identity", action = "", controller = ""}. Executing page /Account/Login
2025-06-14 13:03:14.208 +06:00 [INF] Executing handler method Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginModel.OnGetAsync - ModelState is "Valid"
2025-06-14 13:03:14.212 +06:00 [INF] AuthenticationScheme: Identity.External signed out.
2025-06-14 13:03:14.213 +06:00 [INF] Executed handler method OnGetAsync, returned result .
2025-06-14 13:03:14.214 +06:00 [INF] Executing an implicit handler method - ModelState is "Valid"
2025-06-14 13:03:14.215 +06:00 [INF] Executed an implicit handler method, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-14 13:03:14.224 +06:00 [INF] Executed page /Account/Login in 34.0326ms
2025-06-14 13:03:14.227 +06:00 [INF] Executed endpoint '/Account/Login'
2025-06-14 13:03:14.230 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Identity/Account/Login - 200 null text/html; charset=utf-8 55.1042ms
2025-06-14 13:04:19.853 +06:00 [INF] Request starting HTTP/1.1 POST http://localhost:5045/Identity/Account/Login?ReturnUrl=%2FRisk - application/x-www-form-urlencoded 285
2025-06-14 13:04:19.858 +06:00 [INF] Executing endpoint '/Account/Login'
2025-06-14 13:04:19.864 +06:00 [INF] Route matched with {page = "/Account/Login", area = "Identity", action = "", controller = ""}. Executing page /Account/Login
2025-06-14 13:04:19.882 +06:00 [INF] Executing handler method Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginModel.OnPostAsync - ModelState is "Valid"
2025-06-14 13:04:19.937 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__normalizedUserName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
2025-06-14 13:04:20.095 +06:00 [INF] Executed handler method OnPostAsync, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-14 13:04:20.108 +06:00 [INF] Executed page /Account/Login in 241.2631ms
2025-06-14 13:04:20.111 +06:00 [INF] Executed endpoint '/Account/Login'
2025-06-14 13:04:20.118 +06:00 [INF] Request finished HTTP/1.1 POST http://localhost:5045/Identity/Account/Login?ReturnUrl=%2FRisk - 200 null text/html; charset=utf-8 265.0271ms
2025-06-14 13:05:51.427 +06:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 13:05:53.418 +06:00 [INF] Executed DbCommand (38ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 13:05:53.490 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 13:05:53.498 +06:00 [INF] Executed DbCommand (3ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 13:05:53.516 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__normalizedEmail_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
2025-06-14 13:05:53.864 +06:00 [INF] Executed DbCommand (18ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE t."DueDate" IS NOT NULL AND t."DueDate" <= @__upcomingDeadlines_0 AND t."DueDate" > now() AND t."Status" <> 4
2025-06-14 13:05:53.881 +06:00 [INF] Now listening on: http://localhost:5045
2025-06-14 13:05:53.884 +06:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 13:05:53.885 +06:00 [INF] Hosting environment: Development
2025-06-14 13:05:53.887 +06:00 [INF] Content root path: D:\TGI\PM.Tool\PM.Tool
2025-06-14 13:05:53.902 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", p0."Id", p0."IsActive", p0."JoinedAt", p0."ProjectId", p0."Role", p0."UserId"
FROM "Projects" AS p
LEFT JOIN "ProjectMembers" AS p0 ON p."Id" = p0."ProjectId"
WHERE p."EndDate" IS NOT NULL AND p."EndDate" <= @__upcomingDeadlines_0 AND p."EndDate" > now() AND p."Status" <> 5
ORDER BY p."Id"
2025-06-14 13:05:53.907 +06:00 [INF] Deadline check completed at: "2025-06-14T13:05:53.9067749+06:00"
2025-06-14 13:06:12.050 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Risk/Test - null null
2025-06-14 13:06:12.068 +06:00 [WRN] Failed to determine the https port for redirect.
2025-06-14 13:06:12.117 +06:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-14 13:06:12.121 +06:00 [INF] AuthenticationScheme: Identity.Application was challenged.
2025-06-14 13:06:12.128 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Risk/Test - 302 0 null 83.1401ms
2025-06-14 13:06:12.137 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Identity/Account/Login?ReturnUrl=%2FRisk%2FTest - null null
2025-06-14 13:06:12.148 +06:00 [INF] Executing endpoint '/Account/Login'
2025-06-14 13:06:12.230 +06:00 [INF] Route matched with {page = "/Account/Login", area = "Identity", action = "", controller = ""}. Executing page /Account/Login
2025-06-14 13:06:12.275 +06:00 [INF] Executing handler method Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginModel.OnGetAsync - ModelState is "Valid"
2025-06-14 13:06:12.281 +06:00 [INF] AuthenticationScheme: Identity.External signed out.
2025-06-14 13:06:12.282 +06:00 [INF] Executed handler method OnGetAsync, returned result .
2025-06-14 13:06:12.285 +06:00 [INF] Executing an implicit handler method - ModelState is "Valid"
2025-06-14 13:06:12.286 +06:00 [INF] Executed an implicit handler method, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-14 13:06:12.396 +06:00 [INF] Executed page /Account/Login in 162.6057ms
2025-06-14 13:06:12.398 +06:00 [INF] Executed endpoint '/Account/Login'
2025-06-14 13:06:12.400 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Identity/Account/Login?ReturnUrl=%2FRisk%2FTest - 200 null text/html; charset=utf-8 263.2676ms
2025-06-14 13:06:29.657 +06:00 [INF] Request starting HTTP/1.1 POST http://localhost:5045/Identity/Account/Login?ReturnUrl=%2FRisk%2FTest - application/x-www-form-urlencoded 285
2025-06-14 13:06:29.673 +06:00 [INF] Executing endpoint '/Account/Login'
2025-06-14 13:06:29.711 +06:00 [INF] Route matched with {page = "/Account/Login", area = "Identity", action = "", controller = ""}. Executing page /Account/Login
2025-06-14 13:06:29.751 +06:00 [INF] Executing handler method Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginModel.OnPostAsync - ModelState is "Valid"
2025-06-14 13:06:29.806 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__normalizedUserName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
2025-06-14 13:06:29.933 +06:00 [INF] Executed handler method OnPostAsync, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-14 13:06:29.952 +06:00 [INF] Executed page /Account/Login in 238.7959ms
2025-06-14 13:06:29.954 +06:00 [INF] Executed endpoint '/Account/Login'
2025-06-14 13:06:29.961 +06:00 [INF] Request finished HTTP/1.1 POST http://localhost:5045/Identity/Account/Login?ReturnUrl=%2FRisk%2FTest - 200 null text/html; charset=utf-8 303.9583ms
2025-06-14 13:06:32.965 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Projects - null null
2025-06-14 13:06:32.978 +06:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-14 13:06:32.981 +06:00 [INF] AuthenticationScheme: Identity.Application was challenged.
2025-06-14 13:06:32.985 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Projects - 302 0 null 19.0699ms
2025-06-14 13:06:33.000 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Identity/Account/Login?ReturnUrl=%2FProjects - null null
2025-06-14 13:06:33.004 +06:00 [INF] Executing endpoint '/Account/Login'
2025-06-14 13:06:33.009 +06:00 [INF] Route matched with {page = "/Account/Login", area = "Identity", action = "", controller = ""}. Executing page /Account/Login
2025-06-14 13:06:33.013 +06:00 [INF] Executing handler method Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginModel.OnGetAsync - ModelState is "Valid"
2025-06-14 13:06:33.015 +06:00 [INF] AuthenticationScheme: Identity.External signed out.
2025-06-14 13:06:33.017 +06:00 [INF] Executed handler method OnGetAsync, returned result .
2025-06-14 13:06:33.021 +06:00 [INF] Executing an implicit handler method - ModelState is "Valid"
2025-06-14 13:06:33.024 +06:00 [INF] Executed an implicit handler method, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-14 13:06:33.034 +06:00 [INF] Executed page /Account/Login in 21.3467ms
2025-06-14 13:06:33.037 +06:00 [INF] Executed endpoint '/Account/Login'
2025-06-14 13:06:33.041 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Identity/Account/Login?ReturnUrl=%2FProjects - 200 null text/html; charset=utf-8 40.8763ms
2025-06-14 13:06:54.315 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Identity/Account/Login - null null
2025-06-14 13:06:54.327 +06:00 [INF] Executing endpoint '/Account/Login'
2025-06-14 13:06:54.341 +06:00 [INF] Route matched with {page = "/Account/Login", area = "Identity", action = "", controller = ""}. Executing page /Account/Login
2025-06-14 13:06:54.343 +06:00 [INF] Executing handler method Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginModel.OnGetAsync - ModelState is "Valid"
2025-06-14 13:06:54.345 +06:00 [INF] AuthenticationScheme: Identity.External signed out.
2025-06-14 13:06:54.346 +06:00 [INF] Executed handler method OnGetAsync, returned result .
2025-06-14 13:06:54.347 +06:00 [INF] Executing an implicit handler method - ModelState is "Valid"
2025-06-14 13:06:54.348 +06:00 [INF] Executed an implicit handler method, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-14 13:06:54.356 +06:00 [INF] Executed page /Account/Login in 13.44ms
2025-06-14 13:06:54.358 +06:00 [INF] Executed endpoint '/Account/Login'
2025-06-14 13:06:54.362 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Identity/Account/Login - 200 null text/html; charset=utf-8 47.1777ms
2025-06-14 13:07:05.134 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Identity/Account/Login?ReturnUrl=%2FProjects - null null
2025-06-14 13:07:05.145 +06:00 [INF] Executing endpoint '/Account/Login'
2025-06-14 13:07:05.147 +06:00 [INF] Route matched with {page = "/Account/Login", area = "Identity", action = "", controller = ""}. Executing page /Account/Login
2025-06-14 13:07:05.150 +06:00 [INF] Executing handler method Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginModel.OnGetAsync - ModelState is "Valid"
2025-06-14 13:07:05.151 +06:00 [INF] AuthenticationScheme: Identity.External signed out.
2025-06-14 13:07:05.159 +06:00 [INF] Executed handler method OnGetAsync, returned result .
2025-06-14 13:07:05.161 +06:00 [INF] Executing an implicit handler method - ModelState is "Valid"
2025-06-14 13:07:05.165 +06:00 [INF] Executed an implicit handler method, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-14 13:07:05.207 +06:00 [INF] Executed page /Account/Login in 57.9686ms
2025-06-14 13:07:05.225 +06:00 [INF] Executed endpoint '/Account/Login'
2025-06-14 13:07:05.227 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Identity/Account/Login?ReturnUrl=%2FProjects - 200 null text/html; charset=utf-8 93.2968ms
2025-06-14 13:07:43.284 +06:00 [INF] Request starting HTTP/1.1 POST http://localhost:5045/Identity/Account/Login?ReturnUrl=%2FRisk - application/x-www-form-urlencoded 285
2025-06-14 13:07:43.287 +06:00 [INF] Executing endpoint '/Account/Login'
2025-06-14 13:07:43.289 +06:00 [INF] Route matched with {page = "/Account/Login", area = "Identity", action = "", controller = ""}. Executing page /Account/Login
2025-06-14 13:07:43.293 +06:00 [INF] Executing handler method Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginModel.OnPostAsync - ModelState is "Valid"
2025-06-14 13:07:43.306 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__normalizedUserName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
2025-06-14 13:07:43.448 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__user_Id_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ClaimType", a."ClaimValue", a."UserId"
FROM "AspNetUserClaims" AS a
WHERE a."UserId" = @__user_Id_0
2025-06-14 13:07:43.487 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a0."Name"
FROM "AspNetUserRoles" AS a
INNER JOIN "AspNetRoles" AS a0 ON a."RoleId" = a0."Id"
WHERE a."UserId" = @__userId_0
2025-06-14 13:07:43.500 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 13:07:43.524 +06:00 [INF] Executed DbCommand (10ms) [Parameters=[@__role_Id_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."ClaimType", a."ClaimValue"
FROM "AspNetRoleClaims" AS a
WHERE a."RoleId" = @__role_Id_0
2025-06-14 13:07:43.534 +06:00 [INF] AuthenticationScheme: Identity.Application signed in.
2025-06-14 13:07:43.539 +06:00 [INF] User logged in.
2025-06-14 13:07:43.541 +06:00 [INF] Executed handler method OnPostAsync, returned result Microsoft.AspNetCore.Mvc.LocalRedirectResult.
2025-06-14 13:07:43.546 +06:00 [INF] Executing LocalRedirectResult, redirecting to /Risk.
2025-06-14 13:07:43.548 +06:00 [INF] Executed page /Account/Login in 256.8477ms
2025-06-14 13:07:43.549 +06:00 [INF] Executed endpoint '/Account/Login'
2025-06-14 13:07:43.552 +06:00 [INF] Request finished HTTP/1.1 POST http://localhost:5045/Identity/Account/Login?ReturnUrl=%2FRisk - 302 0 null 267.1455ms
2025-06-14 13:07:43.558 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Risk - null null
2025-06-14 13:07:43.567 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-14 13:07:43.577 +06:00 [INF] Route matched with {action = "Index", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-14 13:07:43.614 +06:00 [ERR] Error loading risks
System.InvalidOperationException: The LINQ expression 'DbSet<Risk>()
    .Where(r => !(r.IsDeleted))
    .OrderByDescending(r => r.RiskScore)' could not be translated. Additional information: Translation of member 'RiskScore' on entity type 'Risk' failed. This commonly occurs when the specified member is unmapped. Either rewrite the query in a form that can be translated, or switch to client evaluation explicitly by inserting a call to 'AsEnumerable', 'AsAsyncEnumerable', 'ToList', or 'ToListAsync'. See https://go.microsoft.com/fwlink/?linkid=2101038 for more information.
   at Microsoft.EntityFrameworkCore.Query.QueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at PM.Tool.Application.Services.RiskService.GetAllRisksAsync() in D:\TGI\PM.Tool\PM.Tool\Application\Services\RiskService.cs:line 19
   at PM.Tool.Controllers.RiskController.Index(Nullable`1 projectId) in D:\TGI\PM.Tool\PM.Tool\Controllers\RiskController.cs:line 42
2025-06-14 13:07:43.689 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-14 13:07:43.745 +06:00 [INF] Executed ViewResult - view Index executed in 58.8858ms.
2025-06-14 13:07:43.747 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Index (PM.Tool) in 167.7992ms
2025-06-14 13:07:43.750 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-14 13:07:43.752 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Risk - 200 null text/html; charset=utf-8 193.3711ms
2025-06-14 13:07:43.954 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Projects/GetProjects - null null
2025-06-14 13:07:43.958 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-14 13:07:43.964 +06:00 [INF] Route matched with {action = "GetProjects", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProjects() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-14 13:07:43.987 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-14 13:07:44.044 +06:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-14 13:07:44.071 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-14 13:07:44.259 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType43`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-14 13:07:44.308 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool) in 342.3168ms
2025-06-14 13:07:44.310 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-14 13:07:44.311 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Projects/GetProjects - 200 null application/json; charset=utf-8 356.936ms
2025-06-14 13:07:51.819 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Risk - null null
2025-06-14 13:07:51.829 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-14 13:07:51.833 +06:00 [INF] Route matched with {action = "Index", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-14 13:07:51.842 +06:00 [ERR] Error loading risks
System.InvalidOperationException: The LINQ expression 'DbSet<Risk>()
    .Where(r => !(r.IsDeleted))
    .OrderByDescending(r => r.RiskScore)' could not be translated. Additional information: Translation of member 'RiskScore' on entity type 'Risk' failed. This commonly occurs when the specified member is unmapped. Either rewrite the query in a form that can be translated, or switch to client evaluation explicitly by inserting a call to 'AsEnumerable', 'AsAsyncEnumerable', 'ToList', or 'ToListAsync'. See https://go.microsoft.com/fwlink/?linkid=2101038 for more information.
   at Microsoft.EntityFrameworkCore.Query.QueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at PM.Tool.Application.Services.RiskService.GetAllRisksAsync() in D:\TGI\PM.Tool\PM.Tool\Application\Services\RiskService.cs:line 19
   at PM.Tool.Controllers.RiskController.Index(Nullable`1 projectId) in D:\TGI\PM.Tool\PM.Tool\Controllers\RiskController.cs:line 42
2025-06-14 13:07:51.853 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-14 13:07:51.859 +06:00 [INF] Executed ViewResult - view Index executed in 5.9181ms.
2025-06-14 13:07:51.862 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Index (PM.Tool) in 26.023ms
2025-06-14 13:07:51.864 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-14 13:07:51.866 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Risk - 200 null text/html; charset=utf-8 46.8992ms
2025-06-14 13:07:52.040 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Projects/GetProjects - null null
2025-06-14 13:07:52.043 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-14 13:07:52.045 +06:00 [INF] Route matched with {action = "GetProjects", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProjects() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-14 13:07:52.059 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-14 13:07:52.065 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/favicon.ico - null null
2025-06-14 13:07:52.074 +06:00 [INF] Executed DbCommand (11ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-14 13:07:52.074 +06:00 [INF] The file /favicon.ico was not modified
2025-06-14 13:07:52.078 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/favicon.ico - 304 null image/x-icon 12.6882ms
2025-06-14 13:07:52.078 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType43`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-14 13:07:52.082 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool) in 34.5327ms
2025-06-14 13:07:52.085 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-14 13:07:52.086 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Projects/GetProjects - 200 null application/json; charset=utf-8 46.3015ms
2025-06-14 13:08:02.896 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Risk - null null
2025-06-14 13:08:02.903 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-14 13:08:02.905 +06:00 [INF] Route matched with {action = "Index", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-14 13:08:02.913 +06:00 [ERR] Error loading risks
System.InvalidOperationException: The LINQ expression 'DbSet<Risk>()
    .Where(r => !(r.IsDeleted))
    .OrderByDescending(r => r.RiskScore)' could not be translated. Additional information: Translation of member 'RiskScore' on entity type 'Risk' failed. This commonly occurs when the specified member is unmapped. Either rewrite the query in a form that can be translated, or switch to client evaluation explicitly by inserting a call to 'AsEnumerable', 'AsAsyncEnumerable', 'ToList', or 'ToListAsync'. See https://go.microsoft.com/fwlink/?linkid=2101038 for more information.
   at Microsoft.EntityFrameworkCore.Query.QueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at PM.Tool.Application.Services.RiskService.GetAllRisksAsync() in D:\TGI\PM.Tool\PM.Tool\Application\Services\RiskService.cs:line 19
   at PM.Tool.Controllers.RiskController.Index(Nullable`1 projectId) in D:\TGI\PM.Tool\PM.Tool\Controllers\RiskController.cs:line 42
2025-06-14 13:08:02.922 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-14 13:08:02.929 +06:00 [INF] Executed ViewResult - view Index executed in 6.9256ms.
2025-06-14 13:08:02.931 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Index (PM.Tool) in 23.5785ms
2025-06-14 13:08:02.932 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-14 13:08:02.934 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Risk - 200 null text/html; charset=utf-8 38.0117ms
2025-06-14 13:08:03.033 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Projects/GetProjects - null null
2025-06-14 13:08:03.045 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-14 13:08:03.047 +06:00 [INF] Route matched with {action = "GetProjects", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProjects() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-14 13:08:03.060 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-14 13:08:03.076 +06:00 [INF] Executed DbCommand (12ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-14 13:08:03.080 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType43`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-14 13:08:03.081 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool) in 32.3835ms
2025-06-14 13:08:03.082 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-14 13:08:03.084 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Projects/GetProjects - 200 null application/json; charset=utf-8 50.3152ms
2025-06-14 13:08:10.550 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Risk - null null
2025-06-14 13:08:10.554 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-14 13:08:10.555 +06:00 [INF] Route matched with {action = "Index", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-14 13:08:10.563 +06:00 [ERR] Error loading risks
System.InvalidOperationException: The LINQ expression 'DbSet<Risk>()
    .Where(r => !(r.IsDeleted))
    .OrderByDescending(r => r.RiskScore)' could not be translated. Additional information: Translation of member 'RiskScore' on entity type 'Risk' failed. This commonly occurs when the specified member is unmapped. Either rewrite the query in a form that can be translated, or switch to client evaluation explicitly by inserting a call to 'AsEnumerable', 'AsAsyncEnumerable', 'ToList', or 'ToListAsync'. See https://go.microsoft.com/fwlink/?linkid=2101038 for more information.
   at Microsoft.EntityFrameworkCore.Query.QueryableMethodTranslatingExpressionVisitor.Translate(Expression expression)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutorExpression[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass11_0`1.<ExecuteCore>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteCore[TResult](Expression query, Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryable`1.GetAsyncEnumerator(CancellationToken cancellationToken)
   at System.Runtime.CompilerServices.ConfiguredCancelableAsyncEnumerable`1.GetAsyncEnumerator()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at PM.Tool.Application.Services.RiskService.GetAllRisksAsync() in D:\TGI\PM.Tool\PM.Tool\Application\Services\RiskService.cs:line 19
   at PM.Tool.Controllers.RiskController.Index(Nullable`1 projectId) in D:\TGI\PM.Tool\PM.Tool\Controllers\RiskController.cs:line 42
2025-06-14 13:08:10.572 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-14 13:08:10.576 +06:00 [INF] Executed ViewResult - view Index executed in 4.1763ms.
2025-06-14 13:08:10.578 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Index (PM.Tool) in 18.6736ms
2025-06-14 13:08:10.580 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-14 13:08:10.581 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Risk - 200 null text/html; charset=utf-8 30.8018ms
2025-06-14 13:08:10.671 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Projects/GetProjects - null null
2025-06-14 13:08:10.675 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-14 13:08:10.677 +06:00 [INF] Route matched with {action = "GetProjects", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProjects() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-14 13:08:10.688 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-14 13:08:10.699 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-14 13:08:10.706 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType43`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-14 13:08:10.708 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool) in 28.8925ms
2025-06-14 13:08:10.709 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-14 13:08:10.711 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Projects/GetProjects - 200 null application/json; charset=utf-8 39.3109ms
2025-06-14 13:13:58.594 +06:00 [INF] Application is shutting down...
2025-06-14 13:13:58.602 +06:00 [ERR] Error occurred while checking deadlines
System.Threading.Tasks.TaskCanceledException: A task was canceled.
   at PM.Tool.Application.Services.DeadlineNotificationBackgroundService.ExecuteAsync(CancellationToken stoppingToken) in D:\TGI\PM.Tool\PM.Tool\Application\Services\DeadlineNotificationBackgroundService.cs:line 29
2025-06-14 13:16:24.666 +06:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 13:16:27.811 +06:00 [INF] Executed DbCommand (45ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 13:16:27.870 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 13:16:27.881 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 13:16:27.898 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__normalizedEmail_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
2025-06-14 13:16:28.297 +06:00 [INF] Executed DbCommand (15ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE t."DueDate" IS NOT NULL AND t."DueDate" <= @__upcomingDeadlines_0 AND t."DueDate" > now() AND t."Status" <> 4
2025-06-14 13:16:28.353 +06:00 [INF] Executed DbCommand (11ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", p0."Id", p0."IsActive", p0."JoinedAt", p0."ProjectId", p0."Role", p0."UserId"
FROM "Projects" AS p
LEFT JOIN "ProjectMembers" AS p0 ON p."Id" = p0."ProjectId"
WHERE p."EndDate" IS NOT NULL AND p."EndDate" <= @__upcomingDeadlines_0 AND p."EndDate" > now() AND p."Status" <> 5
ORDER BY p."Id"
2025-06-14 13:16:28.362 +06:00 [INF] Deadline check completed at: "2025-06-14T13:16:28.3612642+06:00"
2025-06-14 13:16:28.408 +06:00 [INF] Now listening on: https://localhost:7029
2025-06-14 13:16:28.411 +06:00 [INF] Now listening on: http://localhost:5045
2025-06-14 13:16:28.454 +06:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 13:16:28.456 +06:00 [INF] Hosting environment: Development
2025-06-14 13:16:28.458 +06:00 [INF] Content root path: D:\TGI\PM.Tool\PM.Tool
2025-06-14 13:16:29.507 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/ - null null
2025-06-14 13:16:29.742 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-14 13:16:29.762 +06:00 [INF] Route matched with {action = "Index", controller = "Home", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-14 13:16:29.817 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-14 13:16:29.859 +06:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-14 13:16:29.888 +06:00 [INF] Executed DbCommand (13ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-14 13:16:30.006 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-14 13:16:30.030 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" < now()
ORDER BY t."DueDate"
2025-06-14 13:16:30.047 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__dueDate_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
FROM "Tasks" AS t
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" <= @__dueDate_0
2025-06-14 13:16:30.079 +06:00 [INF] Executed DbCommand (10ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT n."Id", n."Title", n."Message", n."Type", n."IsRead", n."CreatedAt", n."RelatedProjectId", n."RelatedTaskId"
FROM "Notifications" AS n
WHERE n."UserId" = @__user_Id_0
ORDER BY n."CreatedAt" DESC
LIMIT @__p_1
2025-06-14 13:16:30.108 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."Name", m."Description", m."ProjectId", p."Name", m."DueDate", m."CompletedDate", m."IsCompleted", m."CreatedAt", m."IsDeleted", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."StartDate", p."Status", p."UpdatedAt"
FROM "Milestones" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
WHERE EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__user_Id_0 AND p0."IsActive") AND NOT (m."IsCompleted") AND m."DueDate" >= now()
ORDER BY m."DueDate"
LIMIT @__p_1
2025-06-14 13:16:30.125 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-14 13:16:30.260 +06:00 [INF] Executed ViewResult - view Index executed in 134.0516ms.
2025-06-14 13:16:30.281 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.Index (PM.Tool) in 515.9311ms
2025-06-14 13:16:30.283 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-14 13:16:30.294 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/ - 200 null text/html; charset=utf-8 796.9821ms
2025-06-14 13:16:30.501 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-14 13:16:30.501 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-14 13:16:30.549 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 48.1813ms
2025-06-14 13:16:30.580 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 78.7668ms
2025-06-14 13:16:36.972 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Risk - null null
2025-06-14 13:16:36.988 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Risk - 307 0 null 16.8782ms
2025-06-14 13:16:37.001 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Risk - null null
2025-06-14 13:16:37.016 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-14 13:16:37.030 +06:00 [INF] Route matched with {action = "Index", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-14 13:16:37.116 +06:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."ActualResolutionDate", r."Category", r."ContingencyPlan", r."CreatedAt", r."CreatedByUserId", r."DeletedAt", r."Description", r."EstimatedCost", r."EstimatedDelayDays", r."IdentifiedDate", r."Impact", r."IsDeleted", r."MitigationPlan", r."OwnerId", r."Probability", r."ProjectId", r."Status", r."TargetResolutionDate", r."TaskId", r."Title", r."UpdatedAt", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", r0."Id", r0."Action", r0."AssignedToUserId", r0."CompletedDate", r0."CreatedAt", r0."DeletedAt", r0."Description", r0."DueDate", r0."IsDeleted", r0."Notes", r0."RiskId", r0."Status", r0."UpdatedAt"
FROM "Risks" AS r
INNER JOIN "Projects" AS p ON r."ProjectId" = p."Id"
LEFT JOIN "Tasks" AS t ON r."TaskId" = t."Id"
LEFT JOIN "AspNetUsers" AS a ON r."OwnerId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON r."CreatedByUserId" = a0."Id"
LEFT JOIN "RiskMitigationActions" AS r0 ON r."Id" = r0."RiskId"
WHERE NOT (r."IsDeleted")
ORDER BY r."Probability" * r."Impact" DESC, r."Id", p."Id", t."Id", a."Id", a0."Id"
2025-06-14 13:16:37.122 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-14 13:16:37.157 +06:00 [INF] Executed ViewResult - view Index executed in 35.8827ms.
2025-06-14 13:16:37.161 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Index (PM.Tool) in 126.2321ms
2025-06-14 13:16:37.162 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-14 13:16:37.164 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Risk - 200 null text/html; charset=utf-8 162.4929ms
2025-06-14 13:16:37.243 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/css/tailwind.css?v=Vk1BkLOsT7-Yh4o1pDln8tZyZ3_rFi9yXuRHMX2g50E - null null
2025-06-14 13:16:37.251 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/lib/jquery/dist/jquery.min.js - null null
2025-06-14 13:16:37.251 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/js/tailwind-theme-manager.js?v=QS9OsoodixBTZAmxGj2gDzPtp60VU7kbda0nOueg4T0 - null null
2025-06-14 13:16:37.251 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/js/site.js?v=gfa0xw_jEM_w5r3HMCk1LwRfuj7T6ktfvUiYt5ZeuS4 - null null
2025-06-14 13:16:37.251 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-14 13:16:37.265 +06:00 [INF] The file /js/tailwind-theme-manager.js was not modified
2025-06-14 13:16:37.265 +06:00 [INF] The file /css/tailwind.css was not modified
2025-06-14 13:16:37.265 +06:00 [INF] The file /js/site.js was not modified
2025-06-14 13:16:37.316 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 65.3418ms
2025-06-14 13:16:37.265 +06:00 [INF] The file /lib/jquery/dist/jquery.min.js was not modified
2025-06-14 13:16:37.358 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/js/tailwind-theme-manager.js?v=QS9OsoodixBTZAmxGj2gDzPtp60VU7kbda0nOueg4T0 - 304 null text/javascript 109.524ms
2025-06-14 13:16:37.358 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/css/tailwind.css?v=Vk1BkLOsT7-Yh4o1pDln8tZyZ3_rFi9yXuRHMX2g50E - 304 null text/css 115.0484ms
2025-06-14 13:16:37.359 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/js/site.js?v=gfa0xw_jEM_w5r3HMCk1LwRfuj7T6ktfvUiYt5ZeuS4 - 304 null text/javascript 108.5922ms
2025-06-14 13:16:37.364 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/lib/jquery/dist/jquery.min.js - 304 null text/javascript 115.0098ms
2025-06-14 13:16:37.366 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-14 13:16:37.400 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 34.0829ms
2025-06-14 13:16:37.602 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Projects/GetProjects - null null
2025-06-14 13:16:37.634 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-14 13:16:37.642 +06:00 [INF] Route matched with {action = "GetProjects", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProjects() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-14 13:16:37.662 +06:00 [INF] Executed DbCommand (11ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-14 13:16:37.695 +06:00 [INF] Executed DbCommand (28ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-14 13:16:37.702 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType43`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-14 13:16:37.714 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool) in 69.3615ms
2025-06-14 13:16:37.716 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-14 13:16:37.717 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Projects/GetProjects - 200 null application/json; charset=utf-8 115.4559ms
2025-06-14 13:16:44.812 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Risk - null null
2025-06-14 13:16:44.819 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-14 13:16:44.822 +06:00 [INF] Route matched with {action = "Index", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-14 13:16:44.839 +06:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."ActualResolutionDate", r."Category", r."ContingencyPlan", r."CreatedAt", r."CreatedByUserId", r."DeletedAt", r."Description", r."EstimatedCost", r."EstimatedDelayDays", r."IdentifiedDate", r."Impact", r."IsDeleted", r."MitigationPlan", r."OwnerId", r."Probability", r."ProjectId", r."Status", r."TargetResolutionDate", r."TaskId", r."Title", r."UpdatedAt", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", r0."Id", r0."Action", r0."AssignedToUserId", r0."CompletedDate", r0."CreatedAt", r0."DeletedAt", r0."Description", r0."DueDate", r0."IsDeleted", r0."Notes", r0."RiskId", r0."Status", r0."UpdatedAt"
FROM "Risks" AS r
INNER JOIN "Projects" AS p ON r."ProjectId" = p."Id"
LEFT JOIN "Tasks" AS t ON r."TaskId" = t."Id"
LEFT JOIN "AspNetUsers" AS a ON r."OwnerId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON r."CreatedByUserId" = a0."Id"
LEFT JOIN "RiskMitigationActions" AS r0 ON r."Id" = r0."RiskId"
WHERE NOT (r."IsDeleted")
ORDER BY r."Probability" * r."Impact" DESC, r."Id", p."Id", t."Id", a."Id", a0."Id"
2025-06-14 13:16:44.843 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-14 13:16:44.846 +06:00 [INF] Executed ViewResult - view Index executed in 3.7799ms.
2025-06-14 13:16:44.848 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Index (PM.Tool) in 23.0341ms
2025-06-14 13:16:44.850 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-14 13:16:44.851 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Risk - 200 null text/html; charset=utf-8 39.1602ms
2025-06-14 13:16:44.904 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-14 13:16:44.907 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 3.4231ms
2025-06-14 13:16:44.934 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-14 13:16:44.941 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 6.594ms
2025-06-14 13:16:45.032 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Projects/GetProjects - null null
2025-06-14 13:16:45.039 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-14 13:16:45.040 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/favicon.ico - null null
2025-06-14 13:16:45.043 +06:00 [INF] Route matched with {action = "GetProjects", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProjects() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-14 13:16:45.045 +06:00 [INF] The file /favicon.ico was not modified
2025-06-14 13:16:45.048 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/favicon.ico - 304 null image/x-icon 7.5208ms
2025-06-14 13:16:45.055 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-14 13:16:45.072 +06:00 [INF] Executed DbCommand (12ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-14 13:16:45.078 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType43`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-14 13:16:45.080 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool) in 32.982ms
2025-06-14 13:16:45.081 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-14 13:16:45.082 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Projects/GetProjects - 200 null application/json; charset=utf-8 50.5155ms
2025-06-14 13:16:48.550 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Risk/Create - null null
2025-06-14 13:16:48.556 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Create (PM.Tool)'
2025-06-14 13:16:48.562 +06:00 [INF] Route matched with {action = "Create", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Create(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-14 13:16:48.599 +06:00 [INF] Executing ViewResult, running view Create.
2025-06-14 13:16:48.634 +06:00 [INF] Executed ViewResult - view Create executed in 35.7777ms.
2025-06-14 13:16:48.639 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Create (PM.Tool) in 73.4413ms
2025-06-14 13:16:48.640 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Create (PM.Tool)'
2025-06-14 13:16:48.642 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Risk/Create - 200 null text/html; charset=utf-8 91.7441ms
2025-06-14 13:16:48.676 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/lib/jquery-validation/dist/jquery.validate.min.js - null null
2025-06-14 13:16:48.681 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - null null
2025-06-14 13:16:48.683 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-14 13:16:48.684 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-14 13:16:48.687 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 3.6161ms
2025-06-14 13:16:48.703 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 18.5195ms
2025-06-14 13:16:48.712 +06:00 [INF] Sending file. Request path: '/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js'. Physical path: 'D:\TGI\PM.Tool\PM.Tool\wwwroot\lib\jquery-validation-unobtrusive\dist\jquery.validate.unobtrusive.min.js'
2025-06-14 13:16:48.712 +06:00 [INF] Sending file. Request path: '/lib/jquery-validation/dist/jquery.validate.min.js'. Physical path: 'D:\TGI\PM.Tool\PM.Tool\wwwroot\lib\jquery-validation\dist\jquery.validate.min.js'
2025-06-14 13:16:48.714 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - 200 5824 text/javascript 32.9818ms
2025-06-14 13:16:48.715 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/lib/jquery-validation/dist/jquery.validate.min.js - 200 25308 text/javascript 39.483ms
2025-06-14 13:16:48.779 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Projects/GetProjects - null null
2025-06-14 13:16:48.783 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-14 13:16:48.786 +06:00 [INF] Route matched with {action = "GetProjects", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProjects() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-14 13:16:48.797 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-14 13:16:48.809 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-14 13:16:48.813 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType43`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-14 13:16:48.814 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool) in 25.2892ms
2025-06-14 13:16:48.815 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-14 13:16:48.817 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Projects/GetProjects - 200 null application/json; charset=utf-8 38.0813ms
2025-06-14 13:19:12.278 +06:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 13:19:14.315 +06:00 [INF] Executed DbCommand (37ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 13:19:14.383 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 13:19:14.390 +06:00 [INF] Executed DbCommand (3ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 13:19:14.408 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__normalizedEmail_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
2025-06-14 13:19:14.770 +06:00 [INF] Executed DbCommand (12ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE t."DueDate" IS NOT NULL AND t."DueDate" <= @__upcomingDeadlines_0 AND t."DueDate" > now() AND t."Status" <> 4
2025-06-14 13:19:14.789 +06:00 [INF] Now listening on: http://localhost:5045
2025-06-14 13:19:14.792 +06:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 13:19:14.793 +06:00 [INF] Hosting environment: Development
2025-06-14 13:19:14.794 +06:00 [INF] Content root path: D:\TGI\PM.Tool\PM.Tool
2025-06-14 13:19:14.804 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", p0."Id", p0."IsActive", p0."JoinedAt", p0."ProjectId", p0."Role", p0."UserId"
FROM "Projects" AS p
LEFT JOIN "ProjectMembers" AS p0 ON p."Id" = p0."ProjectId"
WHERE p."EndDate" IS NOT NULL AND p."EndDate" <= @__upcomingDeadlines_0 AND p."EndDate" > now() AND p."Status" <> 5
ORDER BY p."Id"
2025-06-14 13:19:14.807 +06:00 [INF] Deadline check completed at: "2025-06-14T13:19:14.8073068+06:00"
2025-06-14 13:19:42.624 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/ - null null
2025-06-14 13:19:42.646 +06:00 [WRN] Failed to determine the https port for redirect.
2025-06-14 13:19:42.710 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-14 13:19:42.732 +06:00 [INF] Route matched with {action = "Index", controller = "Home", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-14 13:19:42.783 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-14 13:19:42.871 +06:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-14 13:19:42.923 +06:00 [INF] Executed DbCommand (14ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-14 13:19:43.156 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-14 13:19:43.185 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" < now()
ORDER BY t."DueDate"
2025-06-14 13:19:43.204 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__dueDate_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
FROM "Tasks" AS t
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" <= @__dueDate_0
2025-06-14 13:19:43.235 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT n."Id", n."Title", n."Message", n."Type", n."IsRead", n."CreatedAt", n."RelatedProjectId", n."RelatedTaskId"
FROM "Notifications" AS n
WHERE n."UserId" = @__user_Id_0
ORDER BY n."CreatedAt" DESC
LIMIT @__p_1
2025-06-14 13:19:43.262 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."Name", m."Description", m."ProjectId", p."Name", m."DueDate", m."CompletedDate", m."IsCompleted", m."CreatedAt", m."IsDeleted", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."StartDate", p."Status", p."UpdatedAt"
FROM "Milestones" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
WHERE EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__user_Id_0 AND p0."IsActive") AND NOT (m."IsCompleted") AND m."DueDate" >= now()
ORDER BY m."DueDate"
LIMIT @__p_1
2025-06-14 13:19:43.278 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-14 13:19:43.436 +06:00 [INF] Executed ViewResult - view Index executed in 160.25ms.
2025-06-14 13:19:43.440 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.Index (PM.Tool) in 705.2352ms
2025-06-14 13:19:43.442 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-14 13:19:43.452 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/ - 200 null text/html; charset=utf-8 829.937ms
2025-06-14 13:19:53.820 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Home/SetLanguage?culture=en-US&returnUrl=%2F - null null
2025-06-14 13:19:53.835 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.SetLanguage (PM.Tool)'
2025-06-14 13:19:53.844 +06:00 [INF] Route matched with {action = "SetLanguage", controller = "Home", page = "", area = ""}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult SetLanguage(System.String, System.String) on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-14 13:19:53.874 +06:00 [INF] Language set to en-US, Cookie: c=en-US|uic=en-US
2025-06-14 13:19:53.877 +06:00 [INF] Executing RedirectResult, redirecting to /.
2025-06-14 13:19:53.879 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.SetLanguage (PM.Tool) in 28.9799ms
2025-06-14 13:19:53.881 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.SetLanguage (PM.Tool)'
2025-06-14 13:19:53.884 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Home/SetLanguage?culture=en-US&returnUrl=%2F - 302 0 null 63.3933ms
2025-06-14 13:19:53.892 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/ - null null
2025-06-14 13:19:53.934 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-14 13:19:53.961 +06:00 [INF] Route matched with {action = "Index", controller = "Home", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-14 13:19:53.991 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-14 13:19:54.006 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-14 13:19:54.026 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-14 13:19:54.039 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" < now()
ORDER BY t."DueDate"
2025-06-14 13:19:54.049 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__dueDate_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
FROM "Tasks" AS t
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" <= @__dueDate_0
2025-06-14 13:19:54.060 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT n."Id", n."Title", n."Message", n."Type", n."IsRead", n."CreatedAt", n."RelatedProjectId", n."RelatedTaskId"
FROM "Notifications" AS n
WHERE n."UserId" = @__user_Id_0
ORDER BY n."CreatedAt" DESC
LIMIT @__p_1
2025-06-14 13:19:54.070 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."Name", m."Description", m."ProjectId", p."Name", m."DueDate", m."CompletedDate", m."IsCompleted", m."CreatedAt", m."IsDeleted", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."StartDate", p."Status", p."UpdatedAt"
FROM "Milestones" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
WHERE EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__user_Id_0 AND p0."IsActive") AND NOT (m."IsCompleted") AND m."DueDate" >= now()
ORDER BY m."DueDate"
LIMIT @__p_1
2025-06-14 13:19:54.074 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-14 13:19:54.087 +06:00 [INF] Executed ViewResult - view Index executed in 14.1138ms.
2025-06-14 13:19:54.090 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.Index (PM.Tool) in 126.8678ms
2025-06-14 13:19:54.092 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-14 13:19:54.094 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/ - 200 null text/html; charset=utf-8 202.3761ms
2025-06-14 13:19:55.490 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Risk - null null
2025-06-14 13:19:55.497 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-14 13:19:55.506 +06:00 [INF] Route matched with {action = "Index", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-14 13:19:55.591 +06:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."ActualResolutionDate", r."Category", r."ContingencyPlan", r."CreatedAt", r."CreatedByUserId", r."DeletedAt", r."Description", r."EstimatedCost", r."EstimatedDelayDays", r."IdentifiedDate", r."Impact", r."IsDeleted", r."MitigationPlan", r."OwnerId", r."Probability", r."ProjectId", r."Status", r."TargetResolutionDate", r."TaskId", r."Title", r."UpdatedAt", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", r0."Id", r0."Action", r0."AssignedToUserId", r0."CompletedDate", r0."CreatedAt", r0."DeletedAt", r0."Description", r0."DueDate", r0."IsDeleted", r0."Notes", r0."RiskId", r0."Status", r0."UpdatedAt"
FROM "Risks" AS r
INNER JOIN "Projects" AS p ON r."ProjectId" = p."Id"
LEFT JOIN "Tasks" AS t ON r."TaskId" = t."Id"
LEFT JOIN "AspNetUsers" AS a ON r."OwnerId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON r."CreatedByUserId" = a0."Id"
LEFT JOIN "RiskMitigationActions" AS r0 ON r."Id" = r0."RiskId"
WHERE NOT (r."IsDeleted")
ORDER BY r."Probability" * r."Impact" DESC, r."Id", p."Id", t."Id", a."Id", a0."Id"
2025-06-14 13:19:55.597 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-14 13:19:55.645 +06:00 [INF] Executed ViewResult - view Index executed in 49.3771ms.
2025-06-14 13:19:55.648 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Index (PM.Tool) in 138.6856ms
2025-06-14 13:19:55.649 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-14 13:19:55.651 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Risk - 200 null text/html; charset=utf-8 161.0955ms
2025-06-14 13:19:55.775 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Projects/GetProjects - null null
2025-06-14 13:19:55.779 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-14 13:19:55.788 +06:00 [INF] Route matched with {action = "GetProjects", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProjects() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-14 13:19:55.800 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-14 13:19:55.811 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-14 13:19:55.819 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType43`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-14 13:19:55.844 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool) in 54.1622ms
2025-06-14 13:19:55.848 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-14 13:19:55.849 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Projects/GetProjects - 200 null application/json; charset=utf-8 73.546ms
2025-06-14 13:19:58.093 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Risk/Create - null null
2025-06-14 13:19:58.098 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Create (PM.Tool)'
2025-06-14 13:19:58.103 +06:00 [INF] Route matched with {action = "Create", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Create(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-14 13:19:58.144 +06:00 [INF] Executing ViewResult, running view Create.
2025-06-14 13:19:58.185 +06:00 [INF] Executed ViewResult - view Create executed in 41.8889ms.
2025-06-14 13:19:58.187 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Create (PM.Tool) in 81.8494ms
2025-06-14 13:19:58.189 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Create (PM.Tool)'
2025-06-14 13:19:58.191 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Risk/Create - 200 null text/html; charset=utf-8 97.2128ms
2025-06-14 13:19:58.236 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/lib/jquery-validation/dist/jquery.validate.min.js - null null
2025-06-14 13:19:58.240 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - null null
2025-06-14 13:19:58.262 +06:00 [INF] Sending file. Request path: '/lib/jquery-validation/dist/jquery.validate.min.js'. Physical path: 'D:\TGI\PM.Tool\PM.Tool\wwwroot\lib\jquery-validation\dist\jquery.validate.min.js'
2025-06-14 13:19:58.262 +06:00 [INF] Sending file. Request path: '/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js'. Physical path: 'D:\TGI\PM.Tool\PM.Tool\wwwroot\lib\jquery-validation-unobtrusive\dist\jquery.validate.unobtrusive.min.js'
2025-06-14 13:19:58.263 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/lib/jquery-validation/dist/jquery.validate.min.js - 200 25308 text/javascript 27.4985ms
2025-06-14 13:19:58.267 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - 200 5824 text/javascript 26.7642ms
2025-06-14 13:19:58.324 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Projects/GetProjects - null null
2025-06-14 13:19:58.324 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Projects/GetUsers - null null
2025-06-14 13:19:58.329 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-14 13:19:58.331 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetUsers (PM.Tool)'
2025-06-14 13:19:58.332 +06:00 [INF] Route matched with {action = "GetProjects", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProjects() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-14 13:19:58.337 +06:00 [INF] Route matched with {action = "GetUsers", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetUsers() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-14 13:19:58.344 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-14 13:19:58.363 +06:00 [INF] Executed DbCommand (17ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-14 13:19:58.369 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType43`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-14 13:19:58.371 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool) in 34.7828ms
2025-06-14 13:19:58.372 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-14 13:19:58.374 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Projects/GetProjects - 200 null application/json; charset=utf-8 49.5383ms
2025-06-14 13:19:58.391 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id" AS id, a."UserName" AS "userName", a."Email" AS email, a."FirstName" || ' ' || a."LastName" AS "fullName", a."FirstName" AS "firstName", a."LastName" AS "lastName"
FROM "AspNetUsers" AS a
WHERE a."IsActive"
ORDER BY a."FirstName", a."LastName"
2025-06-14 13:19:58.393 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType44`6[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-14 13:19:58.400 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetUsers (PM.Tool) in 61.1501ms
2025-06-14 13:19:58.403 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetUsers (PM.Tool)'
2025-06-14 13:19:58.405 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Projects/GetUsers - 200 null application/json; charset=utf-8 80.4776ms
2025-06-14 13:20:49.304 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Risk - null null
2025-06-14 13:20:49.308 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-14 13:20:49.310 +06:00 [INF] Route matched with {action = "Index", controller = "Risk", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RiskController (PM.Tool).
2025-06-14 13:20:49.329 +06:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."ActualResolutionDate", r."Category", r."ContingencyPlan", r."CreatedAt", r."CreatedByUserId", r."DeletedAt", r."Description", r."EstimatedCost", r."EstimatedDelayDays", r."IdentifiedDate", r."Impact", r."IsDeleted", r."MitigationPlan", r."OwnerId", r."Probability", r."ProjectId", r."Status", r."TargetResolutionDate", r."TaskId", r."Title", r."UpdatedAt", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", r0."Id", r0."Action", r0."AssignedToUserId", r0."CompletedDate", r0."CreatedAt", r0."DeletedAt", r0."Description", r0."DueDate", r0."IsDeleted", r0."Notes", r0."RiskId", r0."Status", r0."UpdatedAt"
FROM "Risks" AS r
INNER JOIN "Projects" AS p ON r."ProjectId" = p."Id"
LEFT JOIN "Tasks" AS t ON r."TaskId" = t."Id"
LEFT JOIN "AspNetUsers" AS a ON r."OwnerId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON r."CreatedByUserId" = a0."Id"
LEFT JOIN "RiskMitigationActions" AS r0 ON r."Id" = r0."RiskId"
WHERE NOT (r."IsDeleted")
ORDER BY r."Probability" * r."Impact" DESC, r."Id", p."Id", t."Id", a."Id", a0."Id"
2025-06-14 13:20:49.333 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-14 13:20:49.338 +06:00 [INF] Executed ViewResult - view Index executed in 5.2435ms.
2025-06-14 13:20:49.341 +06:00 [INF] Executed action PM.Tool.Controllers.RiskController.Index (PM.Tool) in 27.6336ms
2025-06-14 13:20:49.343 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RiskController.Index (PM.Tool)'
2025-06-14 13:20:49.345 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Risk - 200 null text/html; charset=utf-8 40.9411ms
2025-06-14 13:20:49.586 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Projects/GetProjects - null null
2025-06-14 13:20:49.593 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-14 13:20:49.595 +06:00 [INF] Route matched with {action = "GetProjects", controller = "Projects", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetProjects() on controller PM.Tool.Controllers.ProjectsController (PM.Tool).
2025-06-14 13:20:49.608 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-14 13:20:49.621 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-14 13:20:49.624 +06:00 [INF] Executing JsonResult, writing value of type 'System.Collections.Generic.List`1[[<>f__AnonymousType43`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], PM.Tool, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-14 13:20:49.625 +06:00 [INF] Executed action PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool) in 26.5536ms
2025-06-14 13:20:49.626 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.ProjectsController.GetProjects (PM.Tool)'
2025-06-14 13:20:49.627 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Projects/GetProjects - 200 null application/json; charset=utf-8 41.485ms
