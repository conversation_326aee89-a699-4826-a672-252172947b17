2025-06-14 09:49:42.044 +06:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-14 09:51:17.963 +06:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-14 09:51:18.697 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE IF NOT EXISTS "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);
2025-06-14 09:51:18.708 +06:00 [INF] Acquiring an exclusive lock for migration application. See https://aka.ms/efcore-docs-migrations-lock for more information if this takes too long.
2025-06-14 09:51:18.716 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
LOCK TABLE "__EFMigrationsHistory" IN ACCESS EXCLUSIVE MODE
2025-06-14 09:51:18.723 +06:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-14 09:51:18.734 +06:00 [INF] Applying migration '20250614034955_SyncDatabaseModel'.
2025-06-14 09:51:18.826 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
ALTER TABLE "Meetings" ADD "CancellationReason" character varying(1000);
2025-06-14 09:51:18.836 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
ALTER TABLE "Meetings" ADD "ParentMeetingId" integer;
2025-06-14 09:51:18.847 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
ALTER TABLE "MeetingActionItems" ADD "Title" character varying(200) NOT NULL DEFAULT '';
2025-06-14 09:51:18.853 +06:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250614034955_SyncDatabaseModel', '9.0.0');
2025-06-14 09:51:29.101 +06:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 09:51:31.117 +06:00 [INF] Executed DbCommand (41ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 09:51:31.189 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 09:51:31.205 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 09:51:31.230 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__normalizedEmail_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
2025-06-14 09:51:31.670 +06:00 [INF] Executed DbCommand (14ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE t."DueDate" IS NOT NULL AND t."DueDate" <= @__upcomingDeadlines_0 AND t."DueDate" > now() AND t."Status" <> 4
2025-06-14 09:51:31.700 +06:00 [INF] Now listening on: http://localhost:5045
2025-06-14 09:51:31.701 +06:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 09:51:31.702 +06:00 [INF] Hosting environment: Development
2025-06-14 09:51:31.703 +06:00 [INF] Content root path: D:\TGI\PM.Tool\PM.Tool
2025-06-14 09:51:31.706 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", p0."Id", p0."IsActive", p0."JoinedAt", p0."ProjectId", p0."Role", p0."UserId"
FROM "Projects" AS p
LEFT JOIN "ProjectMembers" AS p0 ON p."Id" = p0."ProjectId"
WHERE p."EndDate" IS NOT NULL AND p."EndDate" <= @__upcomingDeadlines_0 AND p."EndDate" > now() AND p."Status" <> 5
ORDER BY p."Id"
2025-06-14 09:51:31.710 +06:00 [INF] Deadline check completed at: "2025-06-14T09:51:31.7103042+06:00"
2025-06-14 09:54:43.647 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/ - null null
2025-06-14 09:54:43.675 +06:00 [WRN] Failed to determine the https port for redirect.
2025-06-14 09:54:43.732 +06:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-14 09:54:43.740 +06:00 [INF] AuthenticationScheme: Identity.Application was challenged.
2025-06-14 09:54:43.745 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/ - 302 0 null 99.8878ms
2025-06-14 09:54:43.759 +06:00 [INF] Request starting HTTP/1.1 GET http://localhost:5045/Identity/Account/Login?ReturnUrl=%2F - null null
2025-06-14 09:54:43.779 +06:00 [INF] Executing endpoint '/Account/Login'
2025-06-14 09:54:43.809 +06:00 [INF] Route matched with {page = "/Account/Login", area = "Identity", action = "", controller = ""}. Executing page /Account/Login
2025-06-14 09:54:43.833 +06:00 [INF] Executing handler method Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginModel.OnGetAsync - ModelState is "Valid"
2025-06-14 09:54:43.840 +06:00 [INF] AuthenticationScheme: Identity.External signed out.
2025-06-14 09:54:43.841 +06:00 [INF] Executed handler method OnGetAsync, returned result .
2025-06-14 09:54:43.844 +06:00 [INF] Executing an implicit handler method - ModelState is "Valid"
2025-06-14 09:54:43.845 +06:00 [INF] Executed an implicit handler method, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-14 09:54:43.977 +06:00 [INF] Executed page /Account/Login in 164.1916ms
2025-06-14 09:54:43.980 +06:00 [INF] Executed endpoint '/Account/Login'
2025-06-14 09:54:43.983 +06:00 [INF] Request finished HTTP/1.1 GET http://localhost:5045/Identity/Account/Login?ReturnUrl=%2F - 200 null text/html; charset=utf-8 224.0976ms
2025-06-14 09:56:02.590 +06:00 [INF] Request starting HTTP/1.1 POST http://localhost:5045/Identity/Account/Login?ReturnUrl=%2F - application/x-www-form-urlencoded 285
2025-06-14 09:56:02.603 +06:00 [INF] Executing endpoint '/Account/Login'
2025-06-14 09:56:02.611 +06:00 [INF] Route matched with {page = "/Account/Login", area = "Identity", action = "", controller = ""}. Executing page /Account/Login
2025-06-14 09:56:02.700 +06:00 [INF] Executing handler method Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginModel.OnPostAsync - ModelState is "Valid"
2025-06-14 09:56:02.799 +06:00 [INF] Executed DbCommand (13ms) [Parameters=[@__normalizedUserName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."NormalizedUserName" = @__normalizedUserName_0
LIMIT 1
2025-06-14 09:56:03.011 +06:00 [INF] Executed handler method OnPostAsync, returned result Microsoft.AspNetCore.Mvc.RazorPages.PageResult.
2025-06-14 09:56:03.037 +06:00 [INF] Executed page /Account/Login in 423.5817ms
2025-06-14 09:56:03.041 +06:00 [INF] Executed endpoint '/Account/Login'
2025-06-14 09:56:03.053 +06:00 [INF] Request finished HTTP/1.1 POST http://localhost:5045/Identity/Account/Login?ReturnUrl=%2F - 200 null text/html; charset=utf-8 462.9486ms
2025-06-14 09:57:55.921 +06:00 [INF] Application is shutting down...
2025-06-14 09:57:55.936 +06:00 [ERR] Error occurred while checking deadlines
System.Threading.Tasks.TaskCanceledException: A task was canceled.
   at PM.Tool.Application.Services.DeadlineNotificationBackgroundService.ExecuteAsync(CancellationToken stoppingToken) in D:\TGI\PM.Tool\PM.Tool\Application\Services\DeadlineNotificationBackgroundService.cs:line 29
