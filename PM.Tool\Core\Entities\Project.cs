using PM.Tool.Core.Enums;
using System.ComponentModel.DataAnnotations;
using TaskStatus = PM.Tool.Core.Enums.TaskStatus;

namespace PM.Tool.Core.Entities
{
    public class Project : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(1000)]
        public string? Description { get; set; }

        public ProjectStatus Status { get; set; } = ProjectStatus.Planning;

        private DateTime _startDate;
        public DateTime StartDate
        {
            get => _startDate;
            set => _startDate = DateTime.SpecifyKind(value, DateTimeKind.Utc);
        }

        private DateTime? _endDate;
        public DateTime? EndDate
        {
            get => _endDate;
            set => _endDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        private DateTime? _actualEndDate;
        public DateTime? ActualEndDate
        {
            get => _actualEndDate;
            set => _actualEndDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        public decimal Budget { get; set; }

        public string? ClientName { get; set; }

        public string CreatedByUserId { get; set; } = string.Empty;

        [Required]
        public string ManagerId { get; set; } = string.Empty;

        // Navigation properties
        public virtual ApplicationUser CreatedBy { get; set; } = null!;
        public virtual ApplicationUser Manager { get; set; } = null!;
        public virtual ICollection<ProjectMember> Members { get; set; } = new List<ProjectMember>();
        public virtual ICollection<TaskEntity> Tasks { get; set; } = new List<TaskEntity>();
        public virtual ICollection<Milestone> Milestones { get; set; } = new List<Milestone>();
        public virtual ICollection<ProjectAttachment> Attachments { get; set; } = new List<ProjectAttachment>();

        // Computed properties
        public int TotalTasks => Tasks.Count(t => !t.IsDeleted);
        public int CompletedTasks => Tasks.Count(t => t.Status == TaskStatus.Done && !t.IsDeleted);
        public double ProgressPercentage => Tasks?.Any(t => !t.IsDeleted) == true
            ? Tasks.Count(t => t.Status == Core.Enums.TaskStatus.Done && !t.IsDeleted) * 100.0 / Tasks.Count(t => !t.IsDeleted)
            : 0;
        public bool IsOverdue => EndDate.HasValue && DateTime.UtcNow > EndDate.Value && Status != ProjectStatus.Completed;
        public bool IsCompleted { get; set; }
    }
}
