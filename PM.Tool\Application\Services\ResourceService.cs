using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;

namespace PM.Tool.Application.Services
{
    public class ResourceService : IResourceService
    {
        private readonly ApplicationDbContext _context;

        public ResourceService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Resource>> GetAllResourcesAsync()
        {
            return await _context.Resources
                .Include(r => r.User)
                .Include(r => r.ResourceSkills)
                    .ThenInclude(rs => rs.Skill)
                .Where(r => !r.IsDeleted)
                .OrderBy(r => r.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Resource>> GetActiveResourcesAsync()
        {
            return await _context.Resources
                .Include(r => r.User)
                .Include(r => r.ResourceSkills)
                    .ThenInclude(rs => rs.Skill)
                .Where(r => !r.IsDeleted && r.IsActive)
                .OrderBy(r => r.Name)
                .ToListAsync();
        }

        public async Task<Resource?> GetResourceByIdAsync(int id)
        {
            return await _context.Resources
                .Include(r => r.User)
                .Include(r => r.ResourceSkills)
                    .ThenInclude(rs => rs.Skill)
                .Include(r => r.Allocations)
                .FirstOrDefaultAsync(r => r.Id == id && !r.IsDeleted);
        }

        public async Task<Resource> CreateResourceAsync(Resource resource)
        {
            resource.CreatedAt = DateTime.UtcNow;
            resource.UpdatedAt = DateTime.UtcNow;
            
            _context.Resources.Add(resource);
            await _context.SaveChangesAsync();
            
            return resource;
        }

        public async Task<Resource> UpdateResourceAsync(Resource resource)
        {
            resource.UpdatedAt = DateTime.UtcNow;
            
            _context.Resources.Update(resource);
            await _context.SaveChangesAsync();
            
            return resource;
        }

        public async Task<bool> DeleteResourceAsync(int id)
        {
            var resource = await _context.Resources.FindAsync(id);
            if (resource == null) return false;

            resource.IsDeleted = true;
            resource.UpdatedAt = DateTime.UtcNow;
            
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeactivateResourceAsync(int id)
        {
            var resource = await _context.Resources.FindAsync(id);
            if (resource == null) return false;

            resource.IsActive = false;
            resource.UpdatedAt = DateTime.UtcNow;
            
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<ResourceAllocation>> GetResourceAllocationsAsync(int resourceId)
        {
            return await _context.ResourceAllocations
                .Include(ra => ra.Project)
                .Include(ra => ra.Task)
                .Include(ra => ra.CreatedBy)
                .Where(ra => ra.ResourceId == resourceId && !ra.IsDeleted)
                .OrderBy(ra => ra.StartDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<ResourceAllocation>> GetProjectResourceAllocationsAsync(int projectId)
        {
            return await _context.ResourceAllocations
                .Include(ra => ra.Resource)
                .Include(ra => ra.Task)
                .Include(ra => ra.CreatedBy)
                .Where(ra => ra.ProjectId == projectId && !ra.IsDeleted)
                .OrderBy(ra => ra.StartDate)
                .ToListAsync();
        }

        public async Task<ResourceAllocation> CreateAllocationAsync(ResourceAllocation allocation)
        {
            allocation.CreatedAt = DateTime.UtcNow;
            allocation.UpdatedAt = DateTime.UtcNow;
            
            _context.ResourceAllocations.Add(allocation);
            await _context.SaveChangesAsync();
            
            return allocation;
        }

        public async Task<ResourceAllocation> UpdateAllocationAsync(ResourceAllocation allocation)
        {
            allocation.UpdatedAt = DateTime.UtcNow;
            
            _context.ResourceAllocations.Update(allocation);
            await _context.SaveChangesAsync();
            
            return allocation;
        }

        public async Task<bool> DeleteAllocationAsync(int id)
        {
            var allocation = await _context.ResourceAllocations.FindAsync(id);
            if (allocation == null) return false;

            allocation.IsDeleted = true;
            allocation.UpdatedAt = DateTime.UtcNow;
            
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> IsResourceAvailableAsync(int resourceId, DateTime startDate, DateTime endDate, decimal requiredHours)
        {
            var resource = await _context.Resources.FindAsync(resourceId);
            if (resource == null || !resource.IsActive) return false;

            var overlappingAllocations = await _context.ResourceAllocations
                .Where(ra => ra.ResourceId == resourceId && 
                           !ra.IsDeleted &&
                           ra.Status == AllocationStatus.Active &&
                           ra.StartDate < endDate && 
                           ra.EndDate > startDate)
                .ToListAsync();

            var totalDays = (decimal)(endDate - startDate).TotalDays + 1;
            var dailyCapacity = resource.Capacity;
            var totalCapacity = dailyCapacity * totalDays;

            var allocatedHours = overlappingAllocations.Sum(ra => ra.AllocatedHours);
            var availableHours = totalCapacity - allocatedHours;

            return availableHours >= requiredHours;
        }

        public async Task<IEnumerable<Resource>> GetAvailableResourcesAsync(DateTime startDate, DateTime endDate, decimal requiredHours)
        {
            var activeResources = await GetActiveResourcesAsync();
            var availableResources = new List<Resource>();

            foreach (var resource in activeResources)
            {
                if (await IsResourceAvailableAsync(resource.Id, startDate, endDate, requiredHours))
                {
                    availableResources.Add(resource);
                }
            }

            return availableResources;
        }

        public async Task<decimal> GetResourceUtilizationAsync(int resourceId, DateTime startDate, DateTime endDate)
        {
            var resource = await _context.Resources.FindAsync(resourceId);
            if (resource == null) return 0;

            var allocations = await _context.ResourceAllocations
                .Where(ra => ra.ResourceId == resourceId && 
                           !ra.IsDeleted &&
                           ra.StartDate < endDate && 
                           ra.EndDate > startDate)
                .ToListAsync();

            var totalDays = (decimal)(endDate - startDate).TotalDays + 1;
            var totalCapacity = resource.Capacity * totalDays;
            var allocatedHours = allocations.Sum(ra => ra.AllocatedHours);

            return totalCapacity > 0 ? (allocatedHours / totalCapacity) * 100 : 0;
        }

        public async Task<Dictionary<int, decimal>> GetTeamUtilizationAsync(IEnumerable<int> resourceIds, DateTime startDate, DateTime endDate)
        {
            var utilization = new Dictionary<int, decimal>();

            foreach (var resourceId in resourceIds)
            {
                utilization[resourceId] = await GetResourceUtilizationAsync(resourceId, startDate, endDate);
            }

            return utilization;
        }

        public async Task<IEnumerable<Skill>> GetAllSkillsAsync()
        {
            return await _context.Skills
                .Where(s => !s.IsDeleted && s.IsActive)
                .OrderBy(s => s.Category)
                .ThenBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<Skill> CreateSkillAsync(Skill skill)
        {
            skill.CreatedAt = DateTime.UtcNow;
            skill.UpdatedAt = DateTime.UtcNow;
            
            _context.Skills.Add(skill);
            await _context.SaveChangesAsync();
            
            return skill;
        }

        public async Task<IEnumerable<Resource>> GetResourcesBySkillAsync(int skillId, SkillLevel? minimumLevel = null)
        {
            var query = _context.Resources
                .Include(r => r.User)
                .Include(r => r.ResourceSkills)
                    .ThenInclude(rs => rs.Skill)
                .Where(r => !r.IsDeleted && 
                           r.IsActive && 
                           r.ResourceSkills.Any(rs => rs.SkillId == skillId));

            if (minimumLevel.HasValue)
            {
                query = query.Where(r => r.ResourceSkills
                    .Any(rs => rs.SkillId == skillId && rs.Level >= minimumLevel.Value));
            }

            return await query.OrderBy(r => r.Name).ToListAsync();
        }

        public async Task<bool> AssignSkillToResourceAsync(int resourceId, int skillId, SkillLevel level, int yearsOfExperience)
        {
            var existingSkill = await _context.ResourceSkills
                .FirstOrDefaultAsync(rs => rs.ResourceId == resourceId && rs.SkillId == skillId);

            if (existingSkill != null)
            {
                existingSkill.Level = level;
                existingSkill.YearsOfExperience = yearsOfExperience;
                existingSkill.UpdatedAt = DateTime.UtcNow;
            }
            else
            {
                var resourceSkill = new ResourceSkill
                {
                    ResourceId = resourceId,
                    SkillId = skillId,
                    Level = level,
                    YearsOfExperience = yearsOfExperience,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };
                
                _context.ResourceSkills.Add(resourceSkill);
            }

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<Resource>> RecommendResourcesForTaskAsync(int taskId)
        {
            var task = await _context.Tasks
                .Include(t => t.Project)
                .FirstOrDefaultAsync(t => t.Id == taskId);

            if (task == null) return new List<Resource>();

            // Get task skill requirements
            var skillRequirements = await _context.TaskSkillRequirements
                .Include(tsr => tsr.Skill)
                .Where(tsr => tsr.TaskId == taskId)
                .ToListAsync();

            if (!skillRequirements.Any())
            {
                // If no specific skills required, return available resources
                return await GetAvailableResourcesAsync(
                    task.StartDate ?? DateTime.UtcNow,
                    task.DueDate ?? DateTime.UtcNow.AddDays(7),
                    task.EstimatedHours);
            }

            // Find resources with matching skills
            var matchingResources = new List<Resource>();
            
            foreach (var requirement in skillRequirements)
            {
                var resourcesWithSkill = await GetResourcesBySkillAsync(
                    requirement.SkillId, 
                    requirement.RequiredLevel);
                
                matchingResources.AddRange(resourcesWithSkill);
            }

            // Remove duplicates and filter by availability
            var uniqueResources = matchingResources.Distinct().ToList();
            var availableResources = new List<Resource>();

            foreach (var resource in uniqueResources)
            {
                if (await IsResourceAvailableAsync(
                    resource.Id,
                    task.StartDate ?? DateTime.UtcNow,
                    task.DueDate ?? DateTime.UtcNow.AddDays(7),
                    task.EstimatedHours))
                {
                    availableResources.Add(resource);
                }
            }

            return availableResources.OrderBy(r => r.Name);
        }

        public async Task<IEnumerable<Resource>> GetResourcesByCapacityAsync(decimal minCapacity, decimal maxCapacity)
        {
            return await _context.Resources
                .Include(r => r.User)
                .Where(r => !r.IsDeleted && 
                           r.IsActive && 
                           r.Capacity >= minCapacity && 
                           r.Capacity <= maxCapacity)
                .OrderBy(r => r.Capacity)
                .ToListAsync();
        }
    }
}
