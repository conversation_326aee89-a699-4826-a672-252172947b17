/* Professional SaaS Theme System for PM Tool */
/* ============================================= */

/* Light Theme Variables (Default) */
:root {
    /* Background Colors */
    --color-bg: #ffffff;
    --color-bg-secondary: #f9fafb;
    --color-surface: #ffffff;
    --color-surface-elevated: #ffffff;

    /* Text Colors */
    --color-text-primary: #111827;
    --color-text-secondary: #6b7280;
    --color-text-muted: #9ca3af;
    --color-text-inverse: #ffffff;

    /* Border & Divider Colors */
    --color-border: #e5e7eb;
    --color-border-light: #f3f4f6;
    --color-border-strong: #d1d5db;

    /* Brand & Accent Colors */
    --color-accent: #2563eb;
    --color-accent-hover: #1d4ed8;
    --color-accent-light: #3b82f6;
    --color-accent-lighter: #93c5fd;
    --color-accent-lightest: #dbeafe;

    /* Semantic Colors */
    --color-success: #16a34a;
    --color-success-light: #22c55e;
    --color-success-bg: #dcfce7;
    --color-warning: #f59e0b;
    --color-warning-light: #fbbf24;
    --color-warning-bg: #fef3c7;
    --color-danger: #dc2626;
    --color-danger-light: #ef4444;
    --color-danger-bg: #fee2e2;
    --color-info: #0ea5e9;
    --color-info-light: #38bdf8;
    --color-info-bg: #e0f2fe;

    /* Shadows & Elevation */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;

    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;

    /* Font Weights */
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Line Heights */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;

    /* Spacing Scale */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
}

/* Professional Dark Theme Variables */
[data-theme="dark"] {
    /* Background Colors - Deep neutrals for professional appearance */
    --color-bg: #111827;
    --color-bg-secondary: #1f2937;
    --color-surface: #1e1e1e;
    --color-surface-elevated: #171717;

    /* Text Colors - High contrast for readability */
    --color-text-primary: #f9fafb;
    --color-text-secondary: #d1d5db;
    --color-text-muted: #9ca3af;
    --color-text-inverse: #111827;

    /* Border & Divider Colors - Subtle but visible */
    --color-border: #374151;
    --color-border-light: #2d3748;
    --color-border-strong: #4b5563;

    /* Brand & Accent Colors - Refined blue palette */
    --color-accent: #2563eb;
    --color-accent-hover: #1d4ed8;
    --color-accent-light: #3b82f6;
    --color-accent-lighter: #60a5fa;
    --color-accent-lightest: #1e3a8a;

    /* Semantic Colors - Enterprise-grade palette */
    --color-success: #22c55e;
    --color-success-light: #4ade80;
    --color-success-bg: #14532d;
    --color-warning: #f59e0b;
    --color-warning-light: #fbbf24;
    --color-warning-bg: #78350f;
    --color-danger: #ef4444;
    --color-danger-light: #f87171;
    --color-danger-bg: #7f1d1d;
    --color-info: #0ea5e9;
    --color-info-light: #38bdf8;
    --color-info-bg: #075985;

    /* Enhanced Shadows & Elevation for dark mode */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.6);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.7), 0 1px 2px 0 rgba(0, 0, 0, 0.6);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.7), 0 2px 4px -1px rgba(0, 0, 0, 0.6);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.8), 0 4px 6px -2px rgba(0, 0, 0, 0.7);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.8), 0 10px 10px -5px rgba(0, 0, 0, 0.6);
}

/* Global Base Styles */
* {
    box-sizing: border-box;
}

html {
    font-family: var(--font-family);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    background-color: var(--color-bg);
    color: var(--color-text-primary);
    margin: 0;
    padding: 0;
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Surface Components */
.surface {
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    transition: all var(--transition-normal);
}

.surface-elevated {
    background-color: var(--color-surface-elevated);
    box-shadow: var(--shadow-md);
}

/* Text Utilities */
.text-primary {
    color: var(--color-text-primary) !important;
}

.text-secondary {
    color: var(--color-text-secondary) !important;
}

.text-muted {
    color: var(--color-text-muted) !important;
}

/* Button Overrides */
.btn {
    border-radius: var(--radius);
    font-weight: 500;
    transition: all var(--transition-fast);
    border-width: 1px;
}

.btn-primary {
    background-color: var(--color-accent);
    border-color: var(--color-accent);
    color: var(--color-text-inverse);
}

.btn-primary:hover {
    background-color: var(--color-accent-hover);
    border-color: var(--color-accent-hover);
    color: var(--color-text-inverse);
}

.btn-outline-primary {
    border-color: var(--color-accent);
    color: var(--color-accent);
    background-color: transparent;
}

.btn-outline-primary:hover {
    background-color: var(--color-accent);
    border-color: var(--color-accent);
    color: var(--color-text-inverse);
}

.btn-secondary {
    background-color: var(--color-surface);
    border-color: var(--color-border);
    color: var(--color-text-primary);
}

.btn-secondary:hover {
    background-color: var(--color-bg-secondary);
    border-color: var(--color-border-strong);
    color: var(--color-text-primary);
}

/* Card Overrides */
.card {
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow);
}

.card-header {
    background-color: var(--color-bg-secondary);
    border-bottom: 1px solid var(--color-border);
    color: var(--color-text-primary);
}

.card-body {
    background-color: var(--color-surface);
    color: var(--color-text-primary);
}

.card-footer {
    background-color: var(--color-bg-secondary);
    border-top: 1px solid var(--color-border);
    color: var(--color-text-secondary);
}

/* Form Controls */
.form-control {
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    color: var(--color-text-primary);
    transition: all var(--transition-fast);
}

.form-control:focus {
    background-color: var(--color-surface);
    border-color: var(--color-accent);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    color: var(--color-text-primary);
}

.form-label {
    color: var(--color-text-secondary);
    font-weight: 500;
}

/* Navigation */
.navbar {
    background-color: var(--color-surface);
    border-bottom: 1px solid var(--color-border);
    backdrop-filter: blur(10px);
}

.navbar-brand {
    color: var(--color-text-primary);
    font-weight: 600;
}

.nav-link {
    color: var(--color-text-secondary);
    transition: color var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
    color: var(--color-accent);
}

/* Dropdown */
.dropdown-menu {
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
}

.dropdown-item {
    color: var(--color-text-primary);
    transition: all var(--transition-fast);
}

.dropdown-item:hover {
    background-color: var(--color-accent);
    color: var(--color-text-inverse);
}

/* Table */
.table {
    color: var(--color-text-primary);
    --bs-table-bg: var(--color-surface);
    --bs-table-border-color: var(--color-border);
}

.table th {
    color: var(--color-text-primary);
    font-weight: 600;
    border-bottom: 2px solid var(--color-border);
}

/* Modal */
.modal-content {
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
}

.modal-header {
    border-bottom: 1px solid var(--color-border);
}

.modal-footer {
    border-top: 1px solid var(--color-border);
}

/* Utilities */
.border-custom {
    border-color: var(--color-border) !important;
}

.bg-surface {
    background-color: var(--color-surface) !important;
}

.bg-secondary-custom {
    background-color: var(--color-bg-secondary) !important;
}

/* Theme Toggle Button */
.theme-toggle {
    background: none;
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    color: var(--color-text-secondary);
    padding: 0.5rem;
    transition: all var(--transition-fast);
}

.theme-toggle:hover {
    background-color: var(--color-bg-secondary);
    color: var(--color-text-primary);
}
