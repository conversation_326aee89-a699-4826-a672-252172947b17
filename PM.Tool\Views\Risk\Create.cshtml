@model RiskCreateViewModel
@{
    ViewData["Title"] = "Create Risk";
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Risk Management", Href = Url.Action("Index", "Risk"), Icon = "fas fa-exclamation-triangle" },
        new { Text = "Create Risk", Href = "", Icon = "fas fa-plus" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Page Header -->
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-plus mr-3 text-danger-600 dark:text-danger-400"></i>
                Create New Risk
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Identify and document a new project risk
            </p>
        </div>
        @{
            ViewData["Text"] = "Back to Risks";
            ViewData["Variant"] = "secondary";
            ViewData["Icon"] = "fas fa-arrow-left";
            ViewData["Href"] = Url.Action("Index");
        }
        <partial name="Components/_Button" view-data="ViewData" />
    </div>
</div>

<!-- Form -->
<form asp-action="Create" method="post" id="riskForm" class="space-y-8">
    @Html.AntiForgeryToken()
    <div asp-validation-summary="ModelOnly" class="bg-danger-50 dark:bg-danger-900/20 border border-danger-200 dark:border-danger-800 rounded-lg p-4 text-danger-800 dark:text-danger-200"></div>

    <!-- Basic Information -->
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-info-circle text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Basic Information</h3>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="md:col-span-2">
                    <label asp-for="Title" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Risk Title *</label>
                    <input asp-for="Title" class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500" placeholder="Enter risk title">
                    <span asp-validation-for="Title" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                </div>

                <div>
                    <label asp-for="ProjectId" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Project *</label>
                    <select asp-for="ProjectId" id="projectSelect" class="select2-enabled w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">Select a project</option>
                    </select>
                    <span asp-validation-for="ProjectId" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                </div>

                <div>
                    <label asp-for="Category" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Category *</label>
                    <select asp-for="Category" class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="1">Technical</option>
                        <option value="2">Schedule</option>
                        <option value="3">Budget</option>
                        <option value="4">Resource</option>
                        <option value="5">Quality</option>
                        <option value="6">External</option>
                        <option value="7">Organizational</option>
                    </select>
                    <span asp-validation-for="Category" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                </div>

                <div>
                    <label asp-for="Status" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Status</label>
                    <select asp-for="Status" class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="1" selected>Identified</option>
                        <option value="2">Analyzing</option>
                        <option value="3">Mitigating</option>
                        <option value="4">Monitoring</option>
                        <option value="5">Resolved</option>
                        <option value="6">Accepted</option>
                    </select>
                    <span asp-validation-for="Status" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                </div>

                <div>
                    <label asp-for="OwnerId" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Risk Owner</label>
                    <select asp-for="OwnerId" id="ownerSelect" class="select2-enabled w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">Select owner</option>
                    </select>
                    <span asp-validation-for="OwnerId" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Description -->
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-align-left text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Description</h3>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <div>
                <label asp-for="Description" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Risk Description *</label>
                <textarea asp-for="Description" rows="4" class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500" placeholder="Describe the risk in detail..."></textarea>
                <span asp-validation-for="Description" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                <p class="mt-1 text-xs text-neutral-500 dark:text-dark-400">Provide a clear description of what could go wrong and its potential consequences.</p>
            </div>
        </div>
    </div>

    <!-- Risk Assessment -->
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-bar text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Risk Assessment</h3>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label asp-for="Probability" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Probability *</label>
                    <select asp-for="Probability" id="probabilitySelect" class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="1">1 - Very Low (0-10%)</option>
                        <option value="2">2 - Low (11-30%)</option>
                        <option value="3" selected>3 - Medium (31-60%)</option>
                        <option value="4">4 - High (61-80%)</option>
                        <option value="5">5 - Very High (81-100%)</option>
                    </select>
                    <span asp-validation-for="Probability" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                </div>

                <div>
                    <label asp-for="Impact" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Impact *</label>
                    <select asp-for="Impact" id="impactSelect" class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="1">1 - Very Low</option>
                        <option value="2">2 - Low</option>
                        <option value="3" selected>3 - Medium</option>
                        <option value="4">4 - High</option>
                        <option value="5">5 - Very High</option>
                    </select>
                    <span asp-validation-for="Impact" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                </div>

                <div>
                    <label class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Risk Score</label>
                    <div class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-neutral-50 dark:bg-dark-700">
                        <div class="text-center">
                            <div id="riskScore" class="text-2xl font-bold text-neutral-900 dark:text-dark-100">9</div>
                            <div id="riskLevel" class="text-sm text-neutral-500 dark:text-dark-400">Medium</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mitigation Plan -->
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-shield-alt text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Mitigation Plan</h3>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <div class="space-y-4">
                <div>
                    <label asp-for="MitigationPlan" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Mitigation Plan</label>
                    <textarea asp-for="MitigationPlan" rows="3" class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500" placeholder="Describe how this risk will be mitigated..."></textarea>
                    <span asp-validation-for="MitigationPlan" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                </div>

                <div>
                    <label asp-for="ContingencyPlan" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Contingency Plan</label>
                    <textarea asp-for="ContingencyPlan" rows="3" class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500" placeholder="Describe the contingency plan if the risk occurs..."></textarea>
                    <span asp-validation-for="ContingencyPlan" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                </div>

                <div>
                    <label asp-for="TargetResolutionDate" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Target Resolution Date</label>
                    <input asp-for="TargetResolutionDate" type="date" class="w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    <span asp-validation-for="TargetResolutionDate" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Form Actions -->
    @{
        ViewData["SubmitText"] = "Create Risk";
        ViewData["SubmitIcon"] = "fas fa-plus";
        ViewData["CancelUrl"] = Url.Action("Index");
    }
    <partial name="Components/_FormActions" view-data="ViewData" />
</form>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function() {
            loadProjects();
            loadUsers();
            setupRiskCalculation();
            setupFormSubmission();
        });

        function setupFormSubmission() {
            $('#riskForm').on('submit', function(e) {
                console.log('Form submission triggered');
                console.log('Form data:', $(this).serialize());

                // Check if form is valid
                if (!this.checkValidity()) {
                    console.log('Form validation failed');
                    e.preventDefault();
                    return false;
                }

                console.log('Form is valid, submitting...');
                return true;
            });
        }

        function loadProjects() {
            $.get('@Url.Action("GetProjects", "Projects")')
                .done(function(data) {
                    const select = $('#projectSelect');
                    if (data && Array.isArray(data)) {
                        data.forEach(function(project) {
                            const selected = project.id == @(ViewBag.ProjectId ?? 0) ? 'selected' : '';
                            select.append(`<option value="${project.id}" ${selected}>${project.name}</option>`);
                        });
                    }
                })
                .fail(function() {
                    console.error('Failed to load projects');
                });
        }

        function loadUsers() {
            $.get('/api/UserLookup/Users')
                .done(function(data) {
                    const select = $('#ownerSelect');
                    if (data && Array.isArray(data)) {
                        data.forEach(function(user) {
                            select.append(`<option value="${user.id}">${user.fullName || user.userName} (${user.email})</option>`);
                        });
                    }
                })
                .fail(function() {
                    console.error('Failed to load users');
                    const select = $('#ownerSelect');
                    select.append('<option value="">Failed to load users</option>');
                });
        }

        function setupRiskCalculation() {
            $('#probabilitySelect, #impactSelect').on('change', calculateRiskScore);
            calculateRiskScore(); // Initial calculation
        }

        function calculateRiskScore() {
            const probability = parseInt($('#probabilitySelect').val()) || 3;
            const impact = parseInt($('#impactSelect').val()) || 3;
            const score = probability * impact;

            $('#riskScore').text(score);

            let level = 'Low';
            let levelClass = 'text-success-600 dark:text-success-400';

            if (score >= 20) {
                level = 'Critical';
                levelClass = 'text-danger-600 dark:text-danger-400';
            } else if (score >= 15) {
                level = 'High';
                levelClass = 'text-warning-600 dark:text-warning-400';
            } else if (score >= 10) {
                level = 'Medium';
                levelClass = 'text-info-600 dark:text-info-400';
            }

            $('#riskLevel').text(level).attr('class', `text-sm ${levelClass}`);
        }
    </script>
}
