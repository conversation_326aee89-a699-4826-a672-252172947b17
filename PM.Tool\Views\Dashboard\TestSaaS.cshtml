@{
    ViewData["Title"] = "SaaS Theme Test";
    ViewBag.PageIcon = "fas fa-palette";
    ViewBag.PageDescription = "Testing the professional SaaS theme system";
    Layout = "_LayoutSaaS";
}

@section PageActions {
    <button type="button" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        Primary Action
    </button>
    <button type="button" class="btn btn-outline-secondary">
        <i class="fas fa-cog me-2"></i>
        Settings
    </button>
}

<!-- Theme Test Content -->
<div class="row g-4">
    <!-- Theme Status Card -->
    <div class="col-12">
        <div class="card surface-elevated">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2 text-primary"></i>
                    Theme System Status
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <div class="bg-success rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="fas fa-check text-white"></i>
                                </div>
                            </div>
                            <div>
                                <div class="fw-semibold">Theme Manager</div>
                                <div class="text-muted small" id="theme-status">Loading...</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <div class="bg-info rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="fas fa-palette text-white"></i>
                                </div>
                            </div>
                            <div>
                                <div class="fw-semibold">Current Theme</div>
                                <div class="text-muted small" id="current-theme">Detecting...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Component Showcase -->
    <div class="col-lg-8">
        <div class="card surface-elevated">
            <div class="card-header">
                <h5 class="card-title mb-0">Component Showcase</h5>
            </div>
            <div class="card-body">
                <!-- Buttons -->
                <div class="mb-4">
                    <h6>Buttons</h6>
                    <div class="d-flex gap-2 flex-wrap">
                        <button class="btn btn-primary">Primary</button>
                        <button class="btn btn-secondary">Secondary</button>
                        <button class="btn btn-success">Success</button>
                        <button class="btn btn-warning">Warning</button>
                        <button class="btn btn-danger">Danger</button>
                        <button class="btn btn-outline-primary">Outline</button>
                    </div>
                </div>

                <!-- Alerts -->
                <div class="mb-4">
                    <h6>Alerts</h6>
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        Success alert with proper theming
                    </div>
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Warning alert with proper theming
                    </div>
                </div>

                <!-- Form Elements -->
                <div class="mb-4">
                    <h6>Form Elements</h6>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">Text Input</label>
                            <input type="text" class="form-control" placeholder="Enter text">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Select</label>
                            <select class="form-select">
                                <option>Option 1</option>
                                <option>Option 2</option>
                                <option>Option 3</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Badges -->
                <div class="mb-4">
                    <h6>Badges</h6>
                    <div class="d-flex gap-2 flex-wrap">
                        <span class="badge bg-primary">Primary</span>
                        <span class="badge bg-secondary">Secondary</span>
                        <span class="badge bg-success">Success</span>
                        <span class="badge bg-warning">Warning</span>
                        <span class="badge bg-danger">Danger</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Theme Controls -->
    <div class="col-lg-4">
        <div class="card surface-elevated">
            <div class="card-header">
                <h5 class="card-title mb-0">Theme Controls</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <button type="button" class="btn btn-outline-primary" onclick="setTheme('light')">
                        <i class="fas fa-sun me-2"></i>
                        Light Theme
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="setTheme('dark')">
                        <i class="fas fa-moon me-2"></i>
                        Dark Theme
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="toggleTheme()">
                        <i class="fas fa-exchange-alt me-2"></i>
                        Toggle Theme
                    </button>
                </div>

                <hr>

                <div class="small text-muted">
                    <div class="mb-2">
                        <strong>CSS Variables:</strong>
                    </div>
                    <div class="font-monospace small">
                        <div>--color-bg: <span id="css-bg">-</span></div>
                        <div>--color-surface: <span id="css-surface">-</span></div>
                        <div>--color-text-primary: <span id="css-text">-</span></div>
                        <div>--color-accent: <span id="css-accent">-</span></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sample Table -->
    <div class="col-12">
        <div class="card surface-elevated">
            <div class="card-header">
                <h5 class="card-title mb-0">Sample Data Table</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Status</th>
                                <th>Progress</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Sample Project 1</td>
                                <td><span class="badge bg-success">Active</span></td>
                                <td>
                                    <div class="progress" style="height: 6px;">
                                        <div class="progress-bar bg-primary" style="width: 75%"></div>
                                    </div>
                                </td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                            Actions
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#">Edit</a></li>
                                            <li><a class="dropdown-item" href="#">View</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger" href="#">Delete</a></li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>Sample Project 2</td>
                                <td><span class="badge bg-warning">Pending</span></td>
                                <td>
                                    <div class="progress" style="height: 6px;">
                                        <div class="progress-bar bg-warning" style="width: 45%"></div>
                                    </div>
                                </td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                            Actions
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#">Edit</a></li>
                                            <li><a class="dropdown-item" href="#">View</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger" href="#">Delete</a></li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
<script>
// Theme test functions
function setTheme(theme) {
    if (window.ThemeManager) {
        window.ThemeManager.setTheme(theme);
    } else {
        // Fallback
        document.documentElement.setAttribute('data-theme', theme);
        document.body.setAttribute('data-theme', theme);
        localStorage.setItem('pm-tool-theme', theme);
    }
    updateThemeInfo();
}

function toggleTheme() {
    if (window.ThemeManager) {
        window.ThemeManager.toggleTheme();
    } else {
        // Fallback
        const current = document.documentElement.getAttribute('data-theme') || 'light';
        const newTheme = current === 'light' ? 'dark' : 'light';
        setTheme(newTheme);
    }
    updateThemeInfo();
}

function updateThemeInfo() {
    const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
    
    // Update status
    document.getElementById('theme-status').textContent = window.ThemeManager ? 'Active' : 'Fallback Mode';
    document.getElementById('current-theme').textContent = currentTheme.charAt(0).toUpperCase() + currentTheme.slice(1);
    
    // Update CSS variable display
    const computedStyle = getComputedStyle(document.documentElement);
    document.getElementById('css-bg').textContent = computedStyle.getPropertyValue('--color-bg').trim();
    document.getElementById('css-surface').textContent = computedStyle.getPropertyValue('--color-surface').trim();
    document.getElementById('css-text').textContent = computedStyle.getPropertyValue('--color-text-primary').trim();
    document.getElementById('css-accent').textContent = computedStyle.getPropertyValue('--color-accent').trim();
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    updateThemeInfo();
    
    // Listen for theme changes
    document.addEventListener('themeChanged', function(event) {
        updateThemeInfo();
    });
});

// Update info every second to catch any changes
setInterval(updateThemeInfo, 1000);
</script>
}
