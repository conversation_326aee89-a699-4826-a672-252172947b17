@using Microsoft.AspNetCore.Mvc.Localization
@model PM.Tool.Core.Entities.Requirement
@{
    ViewData["Title"] = "Requirement Details";
    var comments = ViewBag.Comments as IEnumerable<PM.Tool.Core.Entities.RequirementComment> ?? new List<PM.Tool.Core.Entities.RequirementComment>();
    var attachments = ViewBag.Attachments as IEnumerable<PM.Tool.Core.Entities.RequirementAttachment> ?? new List<PM.Tool.Core.Entities.RequirementAttachment>();
    var changes = ViewBag.Changes as IEnumerable<PM.Tool.Core.Entities.RequirementChange> ?? new List<PM.Tool.Core.Entities.RequirementChange>();
    var tasks = ViewBag.Tasks as IEnumerable<PM.Tool.Core.Entities.RequirementTask> ?? new List<PM.Tool.Core.Entities.RequirementTask>();
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Requirements", Href = Url.Action("Index", "Requirement"), Icon = "fas fa-clipboard-list" },
        new { Text = Model.Title, Href = "", Icon = "fas fa-file-alt" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
        <div>
            <div class="flex items-center space-x-3 mb-2">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200">
                    <EMAIL>("D4")
                </span>
                @{
                    var priorityClass = GetPriorityTailwindClass(Model.Priority);
                }
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @priorityClass">
                    @Model.Priority Priority
                </span>
                @{
                    var statusClass = GetStatusTailwindClass(Model.Status);
                }
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @statusClass">
                    @Model.Status
                </span>
            </div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-file-alt mr-3 text-primary-600 dark:text-primary-400"></i>
                @Model.Title
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                @Model.Type requirement • Created @Model.CreatedAt.ToString("MMM dd, yyyy")
            </p>
        </div>
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
            @if (Model.Status != PM.Tool.Core.Entities.RequirementStatus.Completed)
            {
                ViewData["Text"] = "Edit";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-edit";
                ViewData["Href"] = Url.Action("Edit", new { id = Model.Id });
                <partial name="Components/_Button" view-data="ViewData" />
            }

            @{
                ViewData["Text"] = "Back to Requirements";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Index");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-6">
        <!-- Description -->
        @{
            ViewData["Title"] = "Description";
            ViewData["Icon"] = "fas fa-align-left";
            ViewData["BodyContent"] = $"<div class='prose dark:prose-invert max-w-none'><p class='text-neutral-700 dark:text-dark-300 leading-relaxed'>{Model.Description}</p></div>";
        }
        <partial name="Components/_Card" view-data="ViewData" />

        <!-- Acceptance Criteria -->
        @if (!string.IsNullOrEmpty(Model.AcceptanceCriteria))
        {
            ViewData["Title"] = "Acceptance Criteria";
            ViewData["Icon"] = "fas fa-check-square";
            ViewData["BodyContent"] = $"<div class='prose dark:prose-invert max-w-none'><div class='text-neutral-700 dark:text-dark-300 leading-relaxed whitespace-pre-line'>{Model.AcceptanceCriteria}</div></div>";
            <partial name="Components/_Card" view-data="ViewData" />
        }

        <!-- Business Justification -->
        @if (!string.IsNullOrEmpty(Model.BusinessJustification))
        {
            ViewData["Title"] = "Business Justification";
            ViewData["Icon"] = "fas fa-business-time";
            ViewData["BodyContent"] = $"<div class='prose dark:prose-invert max-w-none'><p class='text-neutral-700 dark:text-dark-300 leading-relaxed'>{Model.BusinessJustification}</p></div>";
            <partial name="Components/_Card" view-data="ViewData" />
        }

        <!-- Related Tasks -->
        @if (tasks.Any())
        {
            <div class="card-custom">
                <div class="card-header-custom">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                            <i class="fas fa-tasks text-primary-600 dark:text-primary-400 text-sm"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Related Tasks</h3>
                        </div>
                    </div>
                </div>
                <div class="card-body-custom">
                    <div class="space-y-3">
                        @foreach (var task in tasks)
                        {
                            <div class="flex items-center justify-between p-3 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <i class="fas fa-tasks text-primary-600 dark:text-primary-400"></i>
                                    <div>
                                        <h4 class="font-medium text-neutral-900 dark:text-dark-100">@(task.Task?.Title ?? "Unknown Task")</h4>
                                        <p class="text-sm text-neutral-500 dark:text-dark-400">@(task.Notes ?? "")</p>
                                    </div>
                                </div>
                                <a href="@Url.Action("Details", "Task", new { id = task.TaskId })" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                            </div>
                        }
                    </div>
                </div>
            </div>
        }

        <!-- Comments Section -->
        @{
            ViewData["Title"] = "Comments";
            ViewData["Icon"] = "fas fa-comments";
            ViewData["BodyContent"] = @"
                <div id='commentsSection'>
                    <div class='space-y-4 mb-6' id='commentsList'>
                        <!-- Comments will be loaded here -->
                    </div>
                    <div class='border-t border-neutral-200 dark:border-dark-600 pt-4'>
                        <form id='addCommentForm' class='space-y-3'>
                            <textarea id='commentText' rows='3' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500' placeholder='Add a comment...'></textarea>
                            <div class='flex justify-end'>
                                <button type='submit' class='btn-primary-custom px-4 py-2 text-sm'>
                                    <i class='fas fa-paper-plane mr-2'></i>
                                    Add Comment
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            ";
        }
        <partial name="Components/_Card" view-data="ViewData" />
    </div>

    <!-- Sidebar -->
    <div class="space-y-6">
        <!-- Quick Info -->
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-info-circle text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Quick Info</h3>
                    </div>
                </div>
            </div>
            <div class="card-body-custom">
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-neutral-500 dark:text-dark-400">Project</span>
                        <span class="text-sm text-neutral-900 dark:text-dark-100">@(Model.Project?.Name ?? "No Project")</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-neutral-500 dark:text-dark-400">Type</span>
                        <span class="text-sm text-neutral-900 dark:text-dark-100">@Model.Type</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-neutral-500 dark:text-dark-400">Source</span>
                        <span class="text-sm text-neutral-900 dark:text-dark-100">@Model.Source</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-neutral-500 dark:text-dark-400">Stakeholder</span>
                        <span class="text-sm text-neutral-900 dark:text-dark-100">@(Model.Stakeholder?.UserName ?? "Not Assigned")</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-neutral-500 dark:text-dark-400">Analyst</span>
                        <span class="text-sm text-neutral-900 dark:text-dark-100">@(Model.Analyst?.UserName ?? "Not Assigned")</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-neutral-500 dark:text-dark-400">Developer</span>
                        <span class="text-sm text-neutral-900 dark:text-dark-100">@(Model.Developer?.UserName ?? "Not Assigned")</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-neutral-500 dark:text-dark-400">Tester</span>
                        <span class="text-sm text-neutral-900 dark:text-dark-100">@(Model.Tester?.UserName ?? "Not Assigned")</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Progress -->
        @{
            var progressPercentage = Model.Status == PM.Tool.Core.Entities.RequirementStatus.Completed ? 100 :
                                   Model.Status == PM.Tool.Core.Entities.RequirementStatus.InProgress ? 50 :
                                   Model.Status == PM.Tool.Core.Entities.RequirementStatus.Approved ? 25 : 0;
            var progressColor = progressPercentage >= 75 ? "bg-success-500" :
                              progressPercentage >= 50 ? "bg-primary-500" :
                              progressPercentage >= 25 ? "bg-warning-500" : "bg-neutral-300 dark:bg-dark-600";
        }
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Progress</h3>
                    </div>
                </div>
            </div>
            <div class="card-body-custom">
                <div class="text-center">
                    <div class="text-3xl font-bold text-neutral-900 dark:text-dark-100 mb-2">@progressPercentage%</div>
                    <div class="w-full bg-neutral-200 dark:bg-dark-600 rounded-full h-3 mb-4">
                        <div class="@progressColor h-3 rounded-full transition-all duration-500 ease-out" style="width: @progressPercentage%"></div>
                    </div>
                    <p class="text-sm text-neutral-500 dark:text-dark-400">Completion Progress</p>
                </div>
            </div>
        </div>

        <!-- Attachments -->
        @if (attachments.Any())
        {
            <div class="card-custom">
                <div class="card-header-custom">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                            <i class="fas fa-paperclip text-primary-600 dark:text-primary-400 text-sm"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Attachments</h3>
                        </div>
                    </div>
                </div>
                <div class="card-body-custom">
                    <div class="space-y-2">
                        @foreach (var attachment in attachments)
                        {
                            <div class="flex items-center justify-between p-2 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-file text-neutral-400 dark:text-dark-500"></i>
                                    <span class="text-sm text-neutral-900 dark:text-dark-100">@attachment.FileName</span>
                                </div>
                                <a href="@attachment.FilePath" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300">
                                    <i class="fas fa-download"></i>
                                </a>
                            </div>
                        }
                    </div>
                </div>
            </div>
        }

        <!-- Timeline -->
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-history text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Timeline</h3>
                    </div>
                </div>
            </div>
            <div class="card-body-custom">
                <div class="space-y-3" id="timelineSection">
                    <div class="flex items-start space-x-3">
                        <div class="w-2 h-2 bg-primary-500 rounded-full mt-2"></div>
                        <div>
                            <p class="text-sm font-medium text-neutral-900 dark:text-dark-100">Created</p>
                            <p class="text-xs text-neutral-500 dark:text-dark-400">@Model.CreatedAt.ToString("MMM dd, yyyy")</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-2 h-2 bg-neutral-300 dark:bg-dark-600 rounded-full mt-2"></div>
                        <div>
                            <p class="text-sm font-medium text-neutral-900 dark:text-dark-100">Last Updated</p>
                            <p class="text-xs text-neutral-500 dark:text-dark-400">@Model.UpdatedAt?.ToString("MMM dd, yyyy")</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            loadComments();
            setupCommentForm();
        });

        function loadComments() {
            // Load comments for this requirement
            // This would typically be an AJAX call to get comments
            $('#commentsList').html('<p class="text-neutral-500 dark:text-dark-400 text-sm">No comments yet.</p>');
        }

        function setupCommentForm() {
            $('#addCommentForm').on('submit', function(e) {
                e.preventDefault();
                const commentText = $('#commentText').val().trim();

                if (commentText) {
                    // Add comment logic here
                    console.log('Adding comment:', commentText);
                    $('#commentText').val('');
                }
            });
        }
    </script>
}

@functions {
    string GetPriorityTailwindClass(PM.Tool.Core.Entities.RequirementPriority priority)
    {
        return priority switch
        {
            PM.Tool.Core.Entities.RequirementPriority.Critical => "bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200",
            PM.Tool.Core.Entities.RequirementPriority.High => "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
            PM.Tool.Core.Entities.RequirementPriority.Medium => "bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200",
            PM.Tool.Core.Entities.RequirementPriority.Low => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
            _ => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200"
        };
    }

    string GetStatusTailwindClass(PM.Tool.Core.Entities.RequirementStatus status)
    {
        return status switch
        {
            PM.Tool.Core.Entities.RequirementStatus.Draft => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200",
            PM.Tool.Core.Entities.RequirementStatus.UnderReview => "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
            PM.Tool.Core.Entities.RequirementStatus.Approved => "bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200",
            PM.Tool.Core.Entities.RequirementStatus.InProgress => "bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200",
            PM.Tool.Core.Entities.RequirementStatus.Completed => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
            PM.Tool.Core.Entities.RequirementStatus.OnHold => "bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200",
            _ => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200"
        };
    }
}
