using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.ViewFeatures;

using PM.Tool.Models.ViewModels;

using System.Security.Claims;

namespace PM.Tool.Services
{
    /// <summary>
    /// Service to provide common form handling functionality across controllers
    /// </summary>
    public interface IFormHelperService
    {
        /// <summary>
        /// Logs validation errors for debugging purposes
        /// </summary>
        void LogValidationErrors(ModelStateDictionary modelState, ILogger logger, string context = "");

        /// <summary>
        /// Sets standard success message
        /// </summary>
        void SetSuccessMessage(ITempDataDictionary tempData, string action, string entityName);

        /// <summary>
        /// Sets standard error message
        /// </summary>
        void SetErrorMessage(ITempDataDictionary tempData, string action, string entityName, Exception? ex = null);

        /// <summary>
        /// Gets current user ID from claims
        /// </summary>
        string GetCurrentUserId(ClaimsPrincipal user);
    }

    public class FormHelperService : IFormHelperService
    {
        public void LogValidationErrors(ModelStateDictionary modelState, ILogger logger, string context = "")
        {
            if (modelState.IsValid) return;

            var errors = modelState
                .Where(x => x.Value?.Errors.Count > 0)
                .Select(x => new { 
                    Field = x.Key, 
                    Errors = x.Value?.Errors.Select(e => e.ErrorMessage) ?? Enumerable.Empty<string>()
                });

            foreach (var error in errors)
            {
                logger.LogWarning("Validation error in {Context} for field {Field}: {Errors}", 
                    context, error.Field, string.Join(", ", error.Errors));
            }
        }

        public void SetSuccessMessage(ITempDataDictionary tempData, string action, string entityName)
        {
            tempData["Success"] = $"{entityName} {action} successfully.";
        }

        public void SetErrorMessage(ITempDataDictionary tempData, string action, string entityName, Exception? ex = null)
        {
            var message = $"Error {action.ToLower()} {entityName.ToLower()}.";
            if (ex != null)
            {
                message += $" {ex.Message}";
            }
            tempData["Error"] = message;
        }

        public string GetCurrentUserId(ClaimsPrincipal user)
        {
            return user.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "";
        }
    }

    /// <summary>
    /// Extension methods for controllers to simplify common form operations
    /// </summary>
    public static class ControllerFormExtensions
    {
        /// <summary>
        /// Standard create action pattern
        /// </summary>
        public static async Task<IActionResult> HandleCreateAsync<TViewModel, TEntity>(
            this Controller controller,
            TViewModel viewModel,
            Func<TEntity, Task<TEntity>> createService,
            Func<TEntity, int> getEntityId,
            Func<Task> populateDropdowns,
            IFormHelperService formHelper,
            ILogger logger,
            string entityName,
            string successRedirectAction = "Details")
            where TViewModel : class, IEntityViewModel<TEntity>
            where TEntity : class
        {
            try
            {
                formHelper.LogValidationErrors(controller.ModelState, logger, $"Create{entityName}");

                if (controller.ModelState.IsValid)
                {
                    var entity = viewModel.ToEntity();
                    
                    // Set common audit fields if entity supports it
                    if (entity is PM.Tool.Core.Entities.BaseEntity baseEntity)
                    {
                        baseEntity.CreatedAt = DateTime.UtcNow;
                        baseEntity.UpdatedAt = DateTime.UtcNow;
                    }

                    var createdEntity = await createService(entity);
                    formHelper.SetSuccessMessage(controller.TempData, "created", entityName);
                    
                    return controller.RedirectToAction(successRedirectAction, new { id = getEntityId(createdEntity) });
                }
                else
                {
                    controller.TempData["Error"] = "Please correct the validation errors and try again.";
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error creating {EntityName}", entityName);
                formHelper.SetErrorMessage(controller.TempData, "creating", entityName, ex);
            }

            await populateDropdowns();
            return controller.View(viewModel);
        }

        /// <summary>
        /// Standard update action pattern
        /// </summary>
        public static async Task<IActionResult> HandleUpdateAsync<TViewModel, TEntity>(
            this Controller controller,
            int id,
            TViewModel viewModel,
            Func<int, Task<TEntity?>> getEntityService,
            Func<TEntity, Task<TEntity>> updateService,
            Func<Task> populateDropdowns,
            IFormHelperService formHelper,
            ILogger logger,
            string entityName,
            string successRedirectAction = "Details")
            where TViewModel : class, IEntityViewModel<TEntity>
            where TEntity : class
        {
            try
            {
                if (controller.ModelState.IsValid)
                {
                    var entity = await getEntityService(id);
                    if (entity == null) return controller.NotFound();

                    viewModel.UpdateEntity(entity);
                    
                    // Set common audit fields if entity supports it
                    if (entity is PM.Tool.Core.Entities.BaseEntity baseEntity)
                    {
                        baseEntity.UpdatedAt = DateTime.UtcNow;
                    }

                    await updateService(entity);
                    formHelper.SetSuccessMessage(controller.TempData, "updated", entityName);
                    
                    return controller.RedirectToAction(successRedirectAction, new { id });
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error updating {EntityName} {Id}", entityName, id);
                formHelper.SetErrorMessage(controller.TempData, "updating", entityName);
            }

            await populateDropdowns();
            return controller.View(viewModel);
        }
    }
}
