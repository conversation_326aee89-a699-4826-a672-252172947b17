using System.Globalization;

namespace PM.Tool.Services
{
    public interface ISimpleLocalizationService
    {
        string GetString(string key);
        string GetString(string key, string fallback);
    }

    public class SimpleLocalizationService : ISimpleLocalizationService
    {
        private readonly Dictionary<string, Dictionary<string, string>> _translations;

        public SimpleLocalizationService()
        {
            _translations = new Dictionary<string, Dictionary<string, string>>
            {
                ["en"] = new Dictionary<string, string>
                {
                    ["Nav.Dashboard"] = "Dashboard",
                    ["Nav.Projects"] = "Projects",
                    ["Nav.Tasks"] = "Tasks",
                    ["Nav.Management"] = "Management",
                    ["Nav.Analytics"] = "Analytics",
                    ["Nav.Resources"] = "Resources",
                    ["Nav.Risks"] = "Risks",
                    ["Nav.Meetings"] = "Meetings",
                    ["Nav.Requirements"] = "Requirements",
                    ["Nav.Backlog"] = "Backlog",
                    ["Nav.Kanban"] = "Kanban Board",
                    ["Nav.Documentation"] = "Documentation",
                    ["Nav.SkillsManagement"] = "Skills Management",
                    ["Nav.ResourceUtilization"] = "Resource Utilization",
                    ["Nav.MeetingCalendar"] = "Meeting Calendar",
                    ["Nav.ActionItems"] = "Action Items",
                    ["Nav.AnalyticsDashboard"] = "Analytics Dashboard",
                    ["Nav.AdvancedReports"] = "Advanced Reports",
                    ["Nav.TeamAnalytics"] = "Team Analytics",
                    ["Nav.BurndownCharts"] = "Burndown Charts",
                    ["Nav.VelocityCharts"] = "Velocity Charts",
                    ["Nav.ExportData"] = "Export Data"
                },
                ["es"] = new Dictionary<string, string>
                {
                    ["Nav.Dashboard"] = "Panel de Control",
                    ["Nav.Projects"] = "Proyectos",
                    ["Nav.Tasks"] = "Tareas",
                    ["Nav.Management"] = "Gestión",
                    ["Nav.Analytics"] = "Analíticas",
                    ["Nav.Resources"] = "Recursos",
                    ["Nav.Risks"] = "Riesgos",
                    ["Nav.Meetings"] = "Reuniones",
                    ["Nav.Requirements"] = "Requisitos",
                    ["Nav.Backlog"] = "Backlog",
                    ["Nav.Kanban"] = "Tablero Kanban",
                    ["Nav.Documentation"] = "Documentación",
                    ["Nav.SkillsManagement"] = "Gestión de Habilidades",
                    ["Nav.ResourceUtilization"] = "Utilización de Recursos",
                    ["Nav.MeetingCalendar"] = "Calendario de Reuniones",
                    ["Nav.ActionItems"] = "Elementos de Acción",
                    ["Nav.AnalyticsDashboard"] = "Panel de Analíticas",
                    ["Nav.AdvancedReports"] = "Reportes Avanzados",
                    ["Nav.TeamAnalytics"] = "Analíticas de Equipo",
                    ["Nav.BurndownCharts"] = "Gráficos Burndown",
                    ["Nav.VelocityCharts"] = "Gráficos de Velocidad",
                    ["Nav.ExportData"] = "Exportar Datos"
                }
            };
        }

        public string GetString(string key)
        {
            return GetString(key, key);
        }

        public string GetString(string key, string fallback)
        {
            var culture = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            
            if (_translations.TryGetValue(culture, out var translations) && 
                translations.TryGetValue(key, out var translation))
            {
                return translation;
            }

            // Fallback to English
            if (culture != "en" && _translations.TryGetValue("en", out var englishTranslations) && 
                englishTranslations.TryGetValue(key, out var englishTranslation))
            {
                return englishTranslation;
            }

            return fallback;
        }
    }
}
