using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities
{
    public class ApplicationUser : IdentityUser
    {
        [Required]
        [MaxLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string LastName { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? Bio { get; set; }

        public string? ProfilePictureUrl { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? LastLoginAt { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ICollection<ProjectMember> ProjectMemberships { get; set; } = new List<ProjectMember>();
        public virtual ICollection<TaskEntity> AssignedTasks { get; set; } = new List<TaskEntity>();
        public virtual ICollection<TaskComment> TaskComments { get; set; } = new List<TaskComment>();
        public virtual ICollection<Project> CreatedProjects { get; set; } = new List<Project>();
        public virtual ICollection<AuditLog> AuditLogs { get; set; } = new List<AuditLog>();
        public virtual ICollection<Notification> Notifications { get; set; } = new List<Notification>();

        // New navigation properties for enhanced features
        public virtual ICollection<Resource> Resources { get; set; } = new List<Resource>();
        public virtual ICollection<ResourceAllocation> ResourceAllocations { get; set; } = new List<ResourceAllocation>();
        public virtual ICollection<Risk> OwnedRisks { get; set; } = new List<Risk>();
        public virtual ICollection<Risk> CreatedRisks { get; set; } = new List<Risk>();
        public virtual ICollection<Dashboard> Dashboards { get; set; } = new List<Dashboard>();
        public virtual ICollection<Report> Reports { get; set; } = new List<Report>();
        public virtual ICollection<Integration> Integrations { get; set; } = new List<Integration>();
        public virtual ICollection<CalendarSync> CalendarSyncs { get; set; } = new List<CalendarSync>();

        public string FullName => $"{FirstName} {LastName}";
    }
}
