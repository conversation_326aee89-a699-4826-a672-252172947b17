<link rel="stylesheet" href="~/lib/fullcalendar/main.min.css" />
<style>
    .task-priority-critical { border-left: 4px solid #dc2626; }
    .task-priority-high { border-left: 4px solid #ea580c; }
    .task-priority-medium { border-left: 4px solid #ca8a04; }
    .task-priority-low { border-left: 4px solid #65a30d; }

    .task-status-todo { background-color: #f3f4f6; }
    .task-status-inprogress { background-color: #dbeafe; }
    .task-status-done { background-color: #d1fae5; }
    .task-status-cancelled { background-color: #fee2e2; }

    .stats-card {
        transition: all 0.3s ease;
    }
    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }

    .filter-sidebar {
        max-height: calc(100vh - 200px);
        overflow-y: auto;
    }

    .task-card {
        transition: all 0.2s ease;
    }
    .task-card:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .priority-indicator {
        display: inline-flex;
        align-items: center;
        padding: 0.125rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .priority-indicator.priority-critical {
        background-color: #fef2f2;
        color: #dc2626;
        border: 1px solid #fecaca;
    }

    .priority-indicator.priority-high {
        background-color: #fff7ed;
        color: #ea580c;
        border: 1px solid #fed7aa;
    }

    .priority-indicator.priority-medium {
        background-color: #fefce8;
        color: #ca8a04;
        border: 1px solid #fde047;
    }

    .priority-indicator.priority-low {
        background-color: #f0fdf4;
        color: #65a30d;
        border: 1px solid #bbf7d0;
    }

    [data-theme="dark"] .priority-indicator.priority-critical {
        background-color: #7f1d1d;
        color: #fca5a5;
        border-color: #dc2626;
    }

    [data-theme="dark"] .priority-indicator.priority-high {
        background-color: #7c2d12;
        color: #fdba74;
        border-color: #ea580c;
    }

    [data-theme="dark"] .priority-indicator.priority-medium {
        background-color: #713f12;
        color: #fde047;
        border-color: #ca8a04;
    }

    [data-theme="dark"] .priority-indicator.priority-low {
        background-color: #365314;
        color: #bbf7d0;
        border-color: #65a30d;
    }

    .view-toggle-btn {
        transition: all 0.2s ease;
        color: #6b7280;
        background-color: transparent;
        border: none;
    }

    .view-toggle-btn.active {
        background-color: #3b82f6;
        color: white;
    }

    .view-toggle-btn:hover:not(.active) {
        background-color: #f3f4f6;
        color: #374151;
    }

    [data-theme="dark"] .view-toggle-btn:hover:not(.active) {
        background-color: #374151;
        color: #d1d5db;
    }

    .quick-filter-btn {
        transition: all 0.2s ease;
    }

    .quick-filter-btn:hover {
        background-color: #f3f4f6;
        color: #374151;
    }

    [data-theme="dark"] .quick-filter-btn:hover {
        background-color: #374151;
        color: #d1d5db;
    }

    /* Calendar customizations */
    .fc-event {
        border: none !important;
        border-radius: 4px !important;
        padding: 2px 4px !important;
        font-size: 0.75rem !important;
    }

    .fc-event-title {
        font-weight: 500 !important;
    }

    /* Responsive adjustments */
    @@media (max-width: 768px) {
        .stats-card {
            padding: 0.75rem;
        }

        .task-card .card-body-custom {
            padding: 1rem;
        }

        .filter-sidebar {
            max-height: none;
        }
    }

    /* Table Styles */
    .task-row {
        transition: all 0.2s ease;
        border-bottom: 1px solid #f3f4f6;
    }

    .dark .task-row {
        border-bottom: 1px solid #374151;
    }

    .task-row:hover {
        background-color: #f8fafc !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .dark .task-row:hover {
        background-color: #1f2937 !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }

    .task-checkbox:checked {
        background-color: var(--primary-600);
        border-color: var(--primary-600);
    }

    .task-checkbox:indeterminate {
        background-color: var(--primary-600);
        border-color: var(--primary-600);
    }

    /* Quick Filter Buttons */
    .quick-filter-btn {
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
    }

    .quick-filter-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .quick-filter-btn.active {
        background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
        border-color: var(--primary-500);
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        transform: translateY(-1px);
    }

    .dark .quick-filter-btn.active {
        background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
        border-color: var(--primary-400);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    }

    .quick-filter-btn.active::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        animation: shimmer 2s infinite;
    }

    @@keyframes shimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    /* View Toggle Buttons */
    .view-toggle-btn {
        transition: all 0.2s ease;
        position: relative;
    }

    .view-toggle-btn:hover:not(.active) {
        background-color: rgba(59, 130, 246, 0.1);
        color: var(--primary-600);
    }

    .dark .view-toggle-btn:hover:not(.active) {
        background-color: rgba(59, 130, 246, 0.2);
        color: var(--primary-400);
    }

    .view-toggle-btn.active {
        background: white;
        color: var(--primary-600);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border: 1px solid var(--primary-200);
    }

    .dark .view-toggle-btn.active {
        background: var(--neutral-700);
        color: var(--primary-400);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        border: 1px solid var(--primary-600);
    }

    /* Bulk Actions Toolbar */
    .bulk-action-btn:hover {
        background-color: var(--primary-100);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .dark .bulk-action-btn:hover {
        background-color: var(--primary-800);
    }

    /* Table Header Sorting */
    th button:hover {
        color: var(--neutral-700);
    }

    .dark th button:hover {
        color: var(--dark-200);
    }

    /* Action Buttons */
    .task-row button {
        opacity: 0.7;
        transition: all 0.2s ease;
        min-width: 32px;
        min-height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .task-row:hover button {
        opacity: 1;
    }

    .task-row button:hover {
        transform: scale(1.1);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    /* Action dropdown */
    .task-row .relative {
        position: relative;
    }

    .task-row [id^="taskActions-"] {
        min-width: 200px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        border: 1px solid #e5e7eb;
    }

    .dark .task-row [id^="taskActions-"] {
        border-color: #374151;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
    }

    /* Compact Filter Bar */
    .quick-filter-btn {
        white-space: nowrap;
    }

    /* Responsive Table */
    @@media (max-width: 768px) {
        .task-row td:nth-child(4),
        .task-row td:nth-child(5),
        .task-row td:nth-child(6) {
            display: none;
        }

        .task-row th:nth-child(4),
        .task-row th:nth-child(5),
        .task-row th:nth-child(6) {
            display: none;
        }
    }

    /* Azure DevOps-like styling */
    table {
        border-collapse: separate;
        border-spacing: 0;
    }

    th {
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .task-row.selected {
        background-color: var(--primary-50);
    }

    .dark .task-row.selected {
        background-color: var(--primary-900/20);
    }

    /* Status Badge Colors - Override Tailwind */
    .bg-gray-100 { background-color: #f3f4f6 !important; }
    .text-gray-800 { color: #1f2937 !important; }
    .bg-blue-100 { background-color: #dbeafe !important; }
    .text-blue-800 { color: #1e40af !important; }
    .bg-green-100 { background-color: #dcfce7 !important; }
    .text-green-800 { color: #166534 !important; }
    .bg-red-100 { background-color: #fee2e2 !important; }
    .text-red-800 { color: #991b1b !important; }
    .bg-yellow-100 { background-color: #fef3c7 !important; }
    .text-yellow-800 { color: #92400e !important; }

    /* Dark mode status colors */
    .dark .bg-gray-700 { background-color: #374151 !important; }
    .dark .text-gray-200 { color: #e5e7eb !important; }
    .dark .bg-blue-900\/50 { background-color: rgba(30, 58, 138, 0.5) !important; }
    .dark .text-blue-200 { color: #bfdbfe !important; }
    .dark .bg-green-900\/50 { background-color: rgba(20, 83, 45, 0.5) !important; }
    .dark .text-green-200 { color: #bbf7d0 !important; }
    .dark .bg-red-900\/50 { background-color: rgba(127, 29, 29, 0.5) !important; }
    .dark .text-red-200 { color: #fecaca !important; }
    .dark .bg-yellow-900\/50 { background-color: rgba(146, 64, 14, 0.5) !important; }
    .dark .text-yellow-200 { color: #fde68a !important; }

    /* Priority Colors */
    .text-red-600 { color: #dc2626 !important; }
    .text-orange-600 { color: #ea580c !important; }
    .text-yellow-600 { color: #d97706 !important; }
    .text-green-600 { color: #16a34a !important; }

    .dark .text-red-400 { color: #f87171 !important; }
    .dark .text-orange-400 { color: #fb923c !important; }
    .dark .text-yellow-400 { color: #facc15 !important; }
    .dark .text-green-400 { color: #4ade80 !important; }

    /* Enhanced Form Controls */
    input[type="text"], select {
        transition: all 0.2s ease;
    }

    input[type="text"]:focus, select:focus {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    }

    /* Search Input Enhancement */
    .search-input-container {
        position: relative;
    }

    .search-input-container::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .search-input-container:focus-within::after {
        transform: scaleX(1);
    }

    /* Button Enhancements */
    button, .btn {
        transition: all 0.2s ease;
    }

    button:hover, .btn:hover {
        transform: translateY(-1px);
    }

    button:active, .btn:active {
        transform: translateY(0);
    }

    /* Stats Badge Enhancements */
    .stats-badge {
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
    }

    .stats-badge:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .stats-badge::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s ease;
    }

    .stats-badge:hover::before {
        left: 100%;
    }

    /* Calendar View Enhancements */
    .fc-event {
        border-radius: 6px !important;
        font-size: 12px !important;
        padding: 3px 6px !important;
        transition: all 0.2s ease !important;
    }

    .fc-event:hover {
        transform: scale(1.05) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
        z-index: 10 !important;
    }

    /* Visual Hierarchy Improvements */
    .page-header {
        background: linear-gradient(135deg, rgba(255,255,255,0.8), rgba(248,250,252,0.9));
        backdrop-filter: blur(10px);
    }

    .dark .page-header {
        background: linear-gradient(135deg, rgba(31,41,55,0.8), rgba(17,24,39,0.9));
    }

    .filter-bar {
        background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(248,250,252,0.98));
        backdrop-filter: blur(8px);
    }

    .dark .filter-bar {
        background: linear-gradient(135deg, rgba(31,41,55,0.95), rgba(17,24,39,0.98));
    }
</style>
