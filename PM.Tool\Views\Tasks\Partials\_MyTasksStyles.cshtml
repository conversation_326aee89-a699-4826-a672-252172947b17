<link rel="stylesheet" href="~/lib/fullcalendar/main.min.css" />
<style>
    .task-priority-critical { border-left: 4px solid #dc2626; }
    .task-priority-high { border-left: 4px solid #ea580c; }
    .task-priority-medium { border-left: 4px solid #ca8a04; }
    .task-priority-low { border-left: 4px solid #65a30d; }
    
    .task-status-todo { background-color: #f3f4f6; }
    .task-status-inprogress { background-color: #dbeafe; }
    .task-status-done { background-color: #d1fae5; }
    .task-status-cancelled { background-color: #fee2e2; }
    
    .stats-card { 
        transition: all 0.3s ease; 
    }
    .stats-card:hover { 
        transform: translateY(-2px); 
        box-shadow: 0 10px 25px rgba(0,0,0,0.1); 
    }
    
    .filter-sidebar { 
        max-height: calc(100vh - 200px); 
        overflow-y: auto; 
    }
    
    .task-card { 
        transition: all 0.2s ease; 
    }
    .task-card:hover { 
        box-shadow: 0 4px 12px rgba(0,0,0,0.1); 
    }

    .priority-indicator {
        display: inline-flex;
        align-items: center;
        padding: 0.125rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .priority-indicator.priority-critical {
        background-color: #fef2f2;
        color: #dc2626;
        border: 1px solid #fecaca;
    }

    .priority-indicator.priority-high {
        background-color: #fff7ed;
        color: #ea580c;
        border: 1px solid #fed7aa;
    }

    .priority-indicator.priority-medium {
        background-color: #fefce8;
        color: #ca8a04;
        border: 1px solid #fde047;
    }

    .priority-indicator.priority-low {
        background-color: #f0fdf4;
        color: #65a30d;
        border: 1px solid #bbf7d0;
    }

    [data-theme="dark"] .priority-indicator.priority-critical {
        background-color: #7f1d1d;
        color: #fca5a5;
        border-color: #dc2626;
    }

    [data-theme="dark"] .priority-indicator.priority-high {
        background-color: #7c2d12;
        color: #fdba74;
        border-color: #ea580c;
    }

    [data-theme="dark"] .priority-indicator.priority-medium {
        background-color: #713f12;
        color: #fde047;
        border-color: #ca8a04;
    }

    [data-theme="dark"] .priority-indicator.priority-low {
        background-color: #365314;
        color: #bbf7d0;
        border-color: #65a30d;
    }

    .view-toggle-btn {
        transition: all 0.2s ease;
        color: #6b7280;
        background-color: transparent;
        border: none;
    }

    .view-toggle-btn.active {
        background-color: #3b82f6;
        color: white;
    }

    .view-toggle-btn:hover:not(.active) {
        background-color: #f3f4f6;
        color: #374151;
    }

    [data-theme="dark"] .view-toggle-btn:hover:not(.active) {
        background-color: #374151;
        color: #d1d5db;
    }

    .quick-filter-btn {
        transition: all 0.2s ease;
    }

    .quick-filter-btn:hover {
        background-color: #f3f4f6;
        color: #374151;
    }

    [data-theme="dark"] .quick-filter-btn:hover {
        background-color: #374151;
        color: #d1d5db;
    }

    /* Calendar customizations */
    .fc-event {
        border: none !important;
        border-radius: 4px !important;
        padding: 2px 4px !important;
        font-size: 0.75rem !important;
    }

    .fc-event-title {
        font-weight: 500 !important;
    }

    /* Responsive adjustments */
    @@media (max-width: 768px) {
        .stats-card {
            padding: 0.75rem;
        }
        
        .task-card .card-body-custom {
            padding: 1rem;
        }
        
        .filter-sidebar {
            max-height: none;
        }
    }
</style>
