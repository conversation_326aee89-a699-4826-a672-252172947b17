@using Microsoft.Extensions.Localization
@using PM.Tool.Resources
@inject IStringLocalizer<SharedResources> Localizer
@inject ILogger<object> Logger
@{
    ViewData["Title"] = "Test Localization";

    // Log detailed localization information
    Logger.LogInformation("=== LOCALIZATION DEBUG START ===");
    Logger.LogInformation("Current Culture: {Culture}", System.Globalization.CultureInfo.CurrentCulture.Name);
    Logger.LogInformation("Current UI Culture: {UICulture}", System.Globalization.CultureInfo.CurrentUICulture.Name);
    Logger.LogInformation("Localizer Type: {LocalizerType}", Localizer.GetType().FullName);

    // Test key retrieval with detailed logging
    var testKeys = new[] { "Nav.Dashboard", "Nav.Projects", "Nav.Tasks", "Common.Save", "Common.Cancel" };
    foreach (var key in testKeys)
    {
        try
        {
            var value = Localizer[key];
            var resourceNotFound = value.ResourceNotFound;
            Logger.LogInformation("Key: {Key} | Value: {Value} | ResourceNotFound: {ResourceNotFound} | SearchedLocation: {SearchedLocation}",
                key, value.Value, resourceNotFound, value.SearchedLocation);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving key: {Key}", key);
        }
    }
    Logger.LogInformation("=== LOCALIZATION DEBUG END ===");
}

<h1>Localization Test</h1>

<div class="card">
    <div class="card-header">
        <h3>Culture Information</h3>
    </div>
    <div class="card-body">
        <p><strong>Current Culture:</strong> @ViewBag.CurrentCulture</p>
        <p><strong>Current UI Culture:</strong> @ViewBag.CurrentUICulture</p>
        <p><strong>Cookie Value:</strong> @ViewBag.CookieValue</p>
        <p><strong>Localizer Type:</strong> @Localizer.GetType().FullName</p>
    </div>
</div>

<div class="card mt-3">
    <div class="card-header">
        <h3>Localized Strings Test</h3>
    </div>
    <div class="card-body">
        @{
            var keys = new[] { "Nav.Dashboard", "Nav.Projects", "Nav.Tasks", "Nav.Management", "Nav.Analytics", "Common.Save", "Common.Cancel" };
        }
        @foreach (var key in keys)
        {
            var localizedString = Localizer[key];
            <p>
                <strong>@key:</strong>
                <span style="color: @(localizedString.ResourceNotFound ? "red" : "green")">@localizedString.Value</span>
                @if (localizedString.ResourceNotFound)
                {
                    <small class="text-muted">(Not Found - Searched: @localizedString.SearchedLocation)</small>
                }
            </p>
        }
    </div>
</div>

<div class="card mt-3">
    <div class="card-header">
        <h3>Language Switching</h3>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <h5>European Languages</h5>
                <a href="@Url.Action("SetLanguage", "Home", new { culture = "en", returnUrl = "/Home/TestLocalization" })" class="btn btn-primary me-2 mb-2">English</a>
                <a href="@Url.Action("SetLanguage", "Home", new { culture = "es", returnUrl = "/Home/TestLocalization" })" class="btn btn-primary me-2 mb-2">Español</a>
                <a href="@Url.Action("SetLanguage", "Home", new { culture = "fr-FR", returnUrl = "/Home/TestLocalization" })" class="btn btn-primary me-2 mb-2">Français</a>
                <a href="@Url.Action("SetLanguage", "Home", new { culture = "de-DE", returnUrl = "/Home/TestLocalization" })" class="btn btn-primary me-2 mb-2">Deutsch</a>
                <a href="@Url.Action("SetLanguage", "Home", new { culture = "it-IT", returnUrl = "/Home/TestLocalization" })" class="btn btn-primary me-2 mb-2">Italiano</a>
                <a href="@Url.Action("SetLanguage", "Home", new { culture = "pt-PT", returnUrl = "/Home/TestLocalization" })" class="btn btn-primary me-2 mb-2">Português</a>
                <a href="@Url.Action("SetLanguage", "Home", new { culture = "nl-NL", returnUrl = "/Home/TestLocalization" })" class="btn btn-primary me-2 mb-2">Nederlands</a>
                <a href="@Url.Action("SetLanguage", "Home", new { culture = "ru-RU", returnUrl = "/Home/TestLocalization" })" class="btn btn-primary me-2 mb-2">Русский</a>
            </div>
            <div class="col-md-4">
                <h5>Asian Languages</h5>
                <a href="@Url.Action("SetLanguage", "Home", new { culture = "zh-CN", returnUrl = "/Home/TestLocalization" })" class="btn btn-secondary me-2 mb-2">中文 (简体)</a>
                <a href="@Url.Action("SetLanguage", "Home", new { culture = "zh-TW", returnUrl = "/Home/TestLocalization" })" class="btn btn-secondary me-2 mb-2">中文 (繁體)</a>
                <a href="@Url.Action("SetLanguage", "Home", new { culture = "ja-JP", returnUrl = "/Home/TestLocalization" })" class="btn btn-secondary me-2 mb-2">日本語</a>
                <a href="@Url.Action("SetLanguage", "Home", new { culture = "ko-KR", returnUrl = "/Home/TestLocalization" })" class="btn btn-secondary me-2 mb-2">한국어</a>
                <a href="@Url.Action("SetLanguage", "Home", new { culture = "hi-IN", returnUrl = "/Home/TestLocalization" })" class="btn btn-secondary me-2 mb-2">हिन्दी</a>
                <a href="@Url.Action("SetLanguage", "Home", new { culture = "bn-BD", returnUrl = "/Home/TestLocalization" })" class="btn btn-secondary me-2 mb-2">বাংলা</a>
                <a href="@Url.Action("SetLanguage", "Home", new { culture = "th-TH", returnUrl = "/Home/TestLocalization" })" class="btn btn-secondary me-2 mb-2">ไทย</a>
                <a href="@Url.Action("SetLanguage", "Home", new { culture = "vi-VN", returnUrl = "/Home/TestLocalization" })" class="btn btn-secondary me-2 mb-2">Tiếng Việt</a>
            </div>
            <div class="col-md-4">
                <h5>Other Languages</h5>
                <a href="@Url.Action("SetLanguage", "Home", new { culture = "ar-SA", returnUrl = "/Home/TestLocalization" })" class="btn btn-info me-2 mb-2">العربية</a>
                <a href="@Url.Action("SetLanguage", "Home", new { culture = "he-IL", returnUrl = "/Home/TestLocalization" })" class="btn btn-info me-2 mb-2">עברית</a>
                <a href="@Url.Action("SetLanguage", "Home", new { culture = "tr-TR", returnUrl = "/Home/TestLocalization" })" class="btn btn-info me-2 mb-2">Türkçe</a>
                <a href="@Url.Action("SetLanguage", "Home", new { culture = "pl-PL", returnUrl = "/Home/TestLocalization" })" class="btn btn-info me-2 mb-2">Polski</a>
                <a href="@Url.Action("SetLanguage", "Home", new { culture = "sv-SE", returnUrl = "/Home/TestLocalization" })" class="btn btn-info me-2 mb-2">Svenska</a>
                <a href="@Url.Action("SetLanguage", "Home", new { culture = "da-DK", returnUrl = "/Home/TestLocalization" })" class="btn btn-info me-2 mb-2">Dansk</a>
                <a href="@Url.Action("SetLanguage", "Home", new { culture = "no-NO", returnUrl = "/Home/TestLocalization" })" class="btn btn-info me-2 mb-2">Norsk</a>
                <a href="@Url.Action("SetLanguage", "Home", new { culture = "fi-FI", returnUrl = "/Home/TestLocalization" })" class="btn btn-info me-2 mb-2">Suomi</a>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <p class="text-muted">
                    <strong>Total Languages Available:</strong> 27 languages with full localization support
                </p>
            </div>
        </div>
    </div>
</div>

<div class="mt-3">
    <a href="@Url.Action("Index")" class="btn btn-secondary">Back to Home</a>
</div>
