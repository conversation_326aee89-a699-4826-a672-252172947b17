using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Models.ViewModels
{
    public class DocumentCategoryViewModel
    {
        public int Id { get; set; }

        [Required]
        [Display(Name = "Category Name")]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "Description")]
        [MaxLength(500)]
        public string? Description { get; set; }

        public string CreatedByName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }

        public int ProjectAttachmentCount { get; set; }
        public int TaskAttachmentCount { get; set; }
    }

    public class DocumentVersionViewModel
    {
        public int Id { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string? ContentType { get; set; }
        public long FileSize { get; set; }
        public int Version { get; set; }
        public string? ChangeDescription { get; set; }
        public string UploadedByName { get; set; } = string.Empty;
        public DateTime UploadedAt { get; set; }

        public string FormattedFileSize => $"{(FileSize / 1024.0):F1} KB";
    }

    public class AttachmentDetailsViewModel
    {
        public int Id { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string? ContentType { get; set; }
        public long FileSize { get; set; }
        public string? Description { get; set; }
        public string? Tags { get; set; }
        public int CurrentVersion { get; set; }
        public string UploadedByName { get; set; } = string.Empty;
        public DateTime UploadedAt { get; set; }
        public DateTime? LastModifiedAt { get; set; }

        public int? CategoryId { get; set; }
        public string? CategoryName { get; set; }
        public List<DocumentCategoryViewModel> AvailableCategories { get; set; } = new();
        public List<DocumentVersionViewModel> Versions { get; set; } = new();

        // Context information
        public string AttachmentType { get; set; } = string.Empty; // "Project" or "Task"
        public int ParentId { get; set; } // ProjectId or TaskId
        public string ParentName { get; set; } = string.Empty;

        public string FormattedFileSize => $"{(FileSize / 1024.0):F1} KB";
        public bool HasVersions => Versions.Count > 1;
    }

    public class NewVersionViewModel
    {
        public int AttachmentId { get; set; }
        public string AttachmentType { get; set; } = string.Empty;

        [Required]
        [Display(Name = "File")]
        public IFormFile File { get; set; } = null!;

        [Display(Name = "Change Description")]
        [MaxLength(500)]
        public string? ChangeDescription { get; set; }
    }

    public class UpdateAttachmentMetadataViewModel
    {
        public int Id { get; set; }
        public string AttachmentType { get; set; } = string.Empty;

        [Display(Name = "Description")]
        [MaxLength(500)]
        public string? Description { get; set; }

        [Display(Name = "Tags")]
        [MaxLength(100)]
        public string? Tags { get; set; }

        [Display(Name = "Category")]
        public int? CategoryId { get; set; }
    }
}
