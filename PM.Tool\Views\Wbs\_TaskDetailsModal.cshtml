<!-- Task Details Modal -->
<div id="taskDetailsModal" class="modal-overlay hidden" onclick="hideModalOnOverlayClick(event, 'taskDetailsModal')">
    <div class="modal-container" onclick="event.stopPropagation()">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Task Details</h3>
                <button type="button" onclick="hideModal('taskDetailsModal')" class="modal-close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div id="task-details-content" class="modal-body">
                <!-- Task details will be loaded here -->
            </div>
            
            <div class="modal-footer">
                <button type="button" onclick="hideModal('taskDetailsModal')" class="btn-secondary-custom">
                    Close
                </button>
                <button type="button" onclick="editTask()" class="btn-primary-custom">
                    <i class="fas fa-edit mr-2"></i>Edit Task
                </button>
            </div>
        </div>
    </div>
</div>
