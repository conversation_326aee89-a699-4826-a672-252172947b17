@model MyTasksViewModel
@{
    ViewData["Title"] = "My Tasks";
    ViewData["PageTitle"] = "My Tasks";
    ViewData["PageDescription"] = "Manage and track your assigned tasks";
}

@section Styles {
    <partial name="Partials/_MyTasksStyles" />
}

<!-- Page Header with Stats -->
<partial name="Partials/_MyTasksHeader" model="Model" />

<!-- Main Content Area -->
<div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
    <!-- Filters Sidebar -->
    <div class="lg:col-span-1">
        <partial name="Partials/_MyTasksFilters" model="Model" />
    </div>

    <!-- Tasks Content -->
    <div class="lg:col-span-3">
        <!-- View Toggle and Bulk Actions -->
        <partial name="Partials/_MyTasksViewControls" model="Model" />

        <!-- Tasks List View -->
        <partial name="Partials/_MyTasksList" model="Model" />

        <!-- Calendar View -->
        <partial name="Partials/_MyTasksCalendar" model="Model" />

        <!-- Grid View -->
        <partial name="Partials/_MyTasksGrid" model="Model" />
    </div>
</div>

@section Scripts {
    <partial name="Partials/_MyTasksScripts" model="Model" />
}