@model MyTasksViewModel
@{
    ViewData["Title"] = "My Tasks";
    ViewData["PageTitle"] = "My Tasks";
    ViewData["PageDescription"] = "Manage and track your assigned tasks";
}

@section Styles {
    <partial name="Partials/_MyTasksStyles" />
}

<!-- Page Header with Stats -->
<partial name="Partials/_MyTasksHeader" model="Model" />

<!-- Compact Filters -->
<partial name="Partials/_MyTasksFilters" model="Model" />

<!-- View Toggle and Bulk Actions -->
<partial name="Partials/_MyTasksViewControls" model="Model" />

<!-- Tasks Content -->
<div class="space-y-6">
    <!-- Tasks List View -->
    <partial name="Partials/_MyTasksList" model="Model" />

    <!-- Calendar View -->
    <partial name="Partials/_MyTasksCalendar" model="Model" />

    <!-- Grid View -->
    <partial name="Partials/_MyTasksGrid" model="Model" />
</div>

@section Scripts {
    <partial name="Partials/_MyTasksScripts" model="Model" />
}