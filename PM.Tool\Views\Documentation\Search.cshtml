@using PM.Tool.Core.Models
@model object
@{
    ViewData["Title"] = "Search Documentation";
    ViewData["Description"] = "Search PM Tool documentation for answers and guides";
    Layout = "_DocumentationLayout";

    var query = ViewBag.Query as string ?? "";
    var results = ViewBag.Results as List<DocumentationSearchResult> ?? new List<DocumentationSearchResult>();
    var totalResults = ViewBag.TotalResults as int? ?? 0;
    var config = ViewBag.Configuration as DocumentationConfiguration;
}

<div class="max-w-4xl mx-auto">
    <!-- Search Header -->
    <div class="text-center mb-8">
        <div class="w-16 h-16 bg-info-100 dark:bg-info-900 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-search text-3xl text-info-600 dark:text-info-400"></i>
        </div>
        <h1 class="text-4xl font-bold text-neutral-900 dark:text-white mb-4">Search Documentation</h1>
        <p class="text-xl text-neutral-600 dark:text-neutral-300">
            Find answers and guides in our comprehensive documentation
        </p>
    </div>

    <!-- Search Form -->
    <div class="mb-8">
        <form method="get" class="relative">
            <div class="relative">
                <input type="text"
                       name="q"
                       value="@query"
                       placeholder="Search documentation..."
                       class="w-full px-4 py-3 pl-12 pr-16 text-lg border border-neutral-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-800 text-neutral-900 dark:text-white placeholder-neutral-500 dark:placeholder-neutral-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors">
                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <i class="fas fa-search text-neutral-400 dark:text-neutral-500"></i>
                </div>
                <button type="submit"
                        class="absolute inset-y-0 right-0 pr-4 flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors">
                    <i class="fas fa-arrow-right"></i>
                </button>
            </div>
        </form>
    </div>

    <!-- Search Results -->
    @if (!string.IsNullOrEmpty(query))
    {
        <div class="mb-6">
            <p class="text-neutral-600 dark:text-neutral-300">
                @if (totalResults > 0)
                {
                    <span>Found <strong>@totalResults</strong> result@(totalResults != 1 ? "s" : "") for "<strong>@query</strong>"</span>
                }
                else
                {
                    <span>No results found for "<strong>@query</strong>"</span>
                }
            </p>
        </div>

        @if (results.Any())
        {
            <div class="space-y-6">
                @foreach (var result in results)
                {
                    <div class="bg-white dark:bg-gray-800 rounded-xl border border-neutral-200 dark:border-gray-700 p-6 hover:shadow-lg transition-shadow">
                        <h3 class="text-xl font-semibold text-neutral-900 dark:text-white mb-2">
                            <a href="@result.Url" class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                                @result.PageTitle
                            </a>
                        </h3>
                        <p class="text-neutral-600 dark:text-neutral-300 mb-3 line-clamp-3">
                            @result.Excerpt
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4 text-sm text-neutral-500 dark:text-neutral-400">
                                @if (!string.IsNullOrEmpty(result.SectionTitle))
                                {
                                    <span class="flex items-center">
                                        <i class="fas fa-folder mr-1"></i>
                                        @result.SectionTitle
                                    </span>
                                }
                                @if (result.Relevance > 0)
                                {
                                    <span class="flex items-center">
                                        <i class="fas fa-star mr-1"></i>
                                        @result.Relevance.ToString("F1")% match
                                    </span>
                                }
                            </div>
                            <a href="@result.Url"
                               class="inline-flex items-center text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors">
                                Read more
                                <i class="fas fa-arrow-right ml-1 text-xs"></i>
                            </a>
                        </div>
                    </div>
                }
            </div>
        }
        else if (!string.IsNullOrEmpty(query))
        {
            <div class="text-center py-16">
                <div class="w-24 h-24 bg-neutral-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-search text-3xl text-neutral-400 dark:text-neutral-500"></i>
                </div>
                <h3 class="text-xl font-semibold text-neutral-900 dark:text-white mb-2">No Results Found</h3>
                <p class="text-neutral-600 dark:text-neutral-300 mb-6">
                    We couldn't find any documentation matching "<strong>@query</strong>". Try different keywords or browse our sections.
                </p>
                <a href="@Url.Action("Index")"
                   class="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-primary-600 rounded-lg hover:bg-primary-700 transition-colors">
                    <i class="fas fa-book mr-2"></i>
                    Browse Documentation
                </a>
            </div>
        }
    }
    else
    {
        <!-- Search Suggestions -->
        <div class="text-center py-16">
            <div class="w-24 h-24 bg-neutral-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-lightbulb text-3xl text-neutral-400 dark:text-neutral-500"></i>
            </div>
            <h3 class="text-xl font-semibold text-neutral-900 dark:text-white mb-2">Start Your Search</h3>
            <p class="text-neutral-600 dark:text-neutral-300 mb-6">
                Enter keywords above to search through our comprehensive documentation.
            </p>
            <div class="flex flex-wrap justify-center gap-2">
                <span class="text-sm text-neutral-500 dark:text-neutral-400">Popular searches:</span>
                <a href="?q=getting+started" class="text-sm text-primary-600 dark:text-primary-400 hover:underline">getting started</a>
                <span class="text-neutral-300 dark:text-neutral-600">•</span>
                <a href="?q=projects" class="text-sm text-primary-600 dark:text-primary-400 hover:underline">projects</a>
                <span class="text-neutral-300 dark:text-neutral-600">•</span>
                <a href="?q=tasks" class="text-sm text-primary-600 dark:text-primary-400 hover:underline">tasks</a>
                <span class="text-neutral-300 dark:text-neutral-600">•</span>
                <a href="?q=api" class="text-sm text-primary-600 dark:text-primary-400 hover:underline">api</a>
            </div>
        </div>
    }
</div>

<div class="container">
    <div class="row">
        <div class="col-12">
            <h1><i class="fas fa-search"></i> Search Documentation</h1>

            <form method="get" action="@Url.Action("Search")" class="mb-4">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text"
                           name="q"
                           value="@query"
                           class="form-control form-control-lg"
                           placeholder="Search documentation..."
                           autocomplete="off"
                           autofocus>
                    <button type="submit" class="btn btn-primary">
                        Search
                    </button>
                </div>
            </form>

            @if (!string.IsNullOrEmpty(query))
            {
                @if (totalResults > 0)
                {
                    <h2>Found @totalResults result@(totalResults != 1 ? "s" : "") for "@query"</h2>

                    @foreach (var result in results)
                    {
                        <div class="card mb-3">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <a href="@result.Url" class="text-decoration-none">
                                        @Html.Raw(result.PageTitle)
                                    </a>
                                </h5>
                                <h6 class="card-subtitle mb-2 text-muted">
                                    <i class="fas fa-folder"></i>
                                    @result.SectionTitle
                                </h6>
                                <p class="card-text">
                                    @Html.Raw(result.Excerpt)
                                </p>
                                @if (result.MatchedTerms?.Any() == true)
                                {
                                    <div>
                                        <small class="text-muted">Matched terms:</small>
                                        @foreach (var term in result.MatchedTerms)
                                        {
                                            <span class="badge bg-primary me-1">@term</span>
                                        }
                                    </div>
                                }
                            </div>
                        </div>
                    }
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h3>No results found</h3>
                        <p class="text-muted">We couldn't find any documentation matching your search for "@query".</p>

                        <div class="mt-4">
                            <h5>Try these suggestions:</h5>
                            <ul class="list-unstyled">
                                <li>Check your spelling</li>
                                <li>Use different or more general keywords</li>
                                <li>Try searching for related terms</li>
                                <li>Browse our <a href="@Url.Action("Index")">documentation sections</a></li>
                            </ul>
                        </div>
                    </div>
                }
            }
            else
            {
                <div class="row">
                    <div class="col-md-6">
                        <h3>Search Tips</h3>
                        <ul>
                            <li><strong>Use specific terms:</strong> "kanban board" instead of "board"</li>
                            <li><strong>Try different keywords:</strong> "project management" or "PM"</li>
                            <li><strong>Use quotes:</strong> "user guide" for exact phrases</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h3>Popular Searches</h3>
                        <div class="d-flex flex-wrap gap-2">
                            <a href="@Url.Action("Search", new { q = "user guide" })" class="btn btn-outline-secondary btn-sm">user guide</a>
                            <a href="@Url.Action("Search", new { q = "kanban board" })" class="btn btn-outline-secondary btn-sm">kanban board</a>
                            <a href="@Url.Action("Search", new { q = "project management" })" class="btn btn-outline-secondary btn-sm">project management</a>
                            <a href="@Url.Action("Search", new { q = "agile" })" class="btn btn-outline-secondary btn-sm">agile</a>
                            <a href="@Url.Action("Search", new { q = "deployment" })" class="btn btn-outline-secondary btn-sm">deployment</a>
                            <a href="@Url.Action("Search", new { q = "API" })" class="btn btn-outline-secondary btn-sm">API</a>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>
