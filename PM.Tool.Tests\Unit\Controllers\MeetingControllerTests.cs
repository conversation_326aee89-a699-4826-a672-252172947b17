using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using PM.Tool.Controllers;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Interfaces;
using PM.Tool.Models.ViewModels;
using PM.Tool.Services;
using System.Security.Claims;
using Xunit;
using PM.Tool.Tests.Helpers;

namespace PM.Tool.Tests.Unit.Controllers
{
    public class MeetingControllerTests
    {
        private readonly Mock<IMeetingService> _mockMeetingService;
        private readonly Mock<IProjectService> _mockProjectService;
        private readonly Mock<IFormHelperService> _mockFormHelper;
        private readonly Mock<IAuditService> _mockAuditService;
        private readonly Mock<ILogger<MeetingController>> _mockLogger;
        private readonly MeetingController _controller;
        private readonly ApplicationUser _testUser;

        public MeetingControllerTests()
        {
            _mockMeetingService = new Mock<IMeetingService>();
            _mockProjectService = new Mock<IProjectService>();
            _mockFormHelper = new Mock<IFormHelperService>();
            _mockAuditService = MockHelper.CreateMockAuditService();
            _mockLogger = MockHelper.CreateMockLogger<MeetingController>();

            _controller = new MeetingController(
                _mockMeetingService.Object,
                _mockProjectService.Object,
                _mockFormHelper.Object,
                _mockAuditService.Object,
                _mockLogger.Object);

            _testUser = MockHelper.CreateMockUser();
            var claimsPrincipal = MockHelper.CreateTestUser(_testUser.Id, _testUser.Email ?? "<EMAIL>");
            MockHelper.SetupControllerContext(_controller, claimsPrincipal);
        }

        [Fact]
        public async Task Index_WithoutProjectId_ReturnsUserMeetings()
        {
            // Arrange
            var meetings = MockHelper.CreateMockMeetings(3);
            _mockMeetingService.Setup(s => s.GetUserMeetingsAsync(It.IsAny<string>(), It.IsAny<DateTime?>(), It.IsAny<DateTime?>()))
                .ReturnsAsync(meetings);

            // Act
            var result = await _controller.Index(null);

            // Assert
            var viewResult = result.Should().BeOfType<ViewResult>().Subject;
            var model = viewResult.Model.Should().BeAssignableTo<IEnumerable<Meeting>>().Subject;
            model.Should().HaveCount(3);
            _mockMeetingService.Verify(s => s.GetUserMeetingsAsync(It.IsAny<string>(), It.IsAny<DateTime?>(), It.IsAny<DateTime?>()), Times.Once);
        }

        [Fact]
        public async Task Index_WithProjectId_ReturnsProjectMeetings()
        {
            // Arrange
            var projectId = 1;
            var meetings = MockHelper.CreateMockMeetings(2);
            var project = MockHelper.CreateMockProject();

            _mockMeetingService.Setup(s => s.GetProjectMeetingsAsync(projectId))
                .ReturnsAsync(meetings);
            _mockProjectService.Setup(s => s.GetProjectByIdAsync(projectId))
                .ReturnsAsync(project);

            // Act
            var result = await _controller.Index(projectId);

            // Assert
            var viewResult = result.Should().BeOfType<ViewResult>().Subject;
            var model = viewResult.Model.Should().BeAssignableTo<IEnumerable<Meeting>>().Subject;
            model.Should().HaveCount(2);
            viewResult.ViewData["ProjectName"].Should().Be(project.Name);
            viewResult.ViewData["ProjectId"].Should().Be(projectId);
        }

        [Fact]
        public async Task Calendar_WithValidDate_ReturnsMeetingsInRange()
        {
            // Arrange
            var selectedDate = DateTime.Today;
            var meetings = MockHelper.CreateMockMeetings(5);

            _mockMeetingService.Setup(s => s.GetMeetingsByDateRangeAsync(
                It.IsAny<DateTime>(), It.IsAny<DateTime>(), null))
                .ReturnsAsync(meetings);

            // Act
            var result = await _controller.Calendar(null, selectedDate);

            // Assert
            var viewResult = result.Should().BeOfType<ViewResult>().Subject;
            var model = viewResult.Model.Should().BeAssignableTo<IEnumerable<Meeting>>().Subject;
            model.Should().HaveCount(5);
            viewResult.ViewData["SelectedDate"].Should().Be(selectedDate);
        }

        [Fact]
        public async Task Details_WithValidId_ReturnsMeetingDetails()
        {
            // Arrange
            var meetingId = 1;
            var meeting = MockHelper.CreateMockMeeting();
            var attendees = MockHelper.CreateMockMeetingAttendees(3);
            var actionItems = MockHelper.CreateMockMeetingActionItems(2);
            var documents = MockHelper.CreateMockMeetingDocuments(1);

            _mockMeetingService.Setup(s => s.GetMeetingByIdAsync(meetingId))
                .ReturnsAsync(meeting);
            _mockMeetingService.Setup(s => s.GetMeetingAttendeesAsync(meetingId))
                .ReturnsAsync(attendees);
            _mockMeetingService.Setup(s => s.GetMeetingActionItemsAsync(meetingId))
                .ReturnsAsync(actionItems);
            _mockMeetingService.Setup(s => s.GetMeetingDocumentsAsync(meetingId))
                .ReturnsAsync(documents);

            // Act
            var result = await _controller.Details(meetingId);

            // Assert
            var viewResult = result.Should().BeOfType<ViewResult>().Subject;
            var model = viewResult.Model.Should().BeOfType<Meeting>().Subject;
            model.Should().Be(meeting);
            viewResult.ViewData["Attendees"].Should().Be(attendees);
            viewResult.ViewData["ActionItems"].Should().Be(actionItems);
            viewResult.ViewData["Documents"].Should().Be(documents);
        }

        [Fact]
        public async Task Details_WithInvalidId_ReturnsNotFound()
        {
            // Arrange
            var meetingId = 999;
            _mockMeetingService.Setup(s => s.GetMeetingByIdAsync(meetingId))
                .ReturnsAsync((Meeting?)null);

            // Act
            var result = await _controller.Details(meetingId);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task Create_Get_ReturnsViewWithDefaultMeeting()
        {
            // Arrange & Act
            var result = await _controller.Create((int?)null);

            // Assert
            var viewResult = result.Should().BeOfType<ViewResult>().Subject;
            var model = viewResult.Model.Should().BeOfType<Meeting>().Subject;
            model.DurationMinutes.Should().Be(60);
            model.ScheduledDate.Should().BeCloseTo(DateTime.Today.AddHours(9), TimeSpan.FromMinutes(1));
        }

        [Fact]
        public async Task Create_GetWithProjectId_SetsProjectId()
        {
            // Arrange
            var projectId = 1;
            var project = MockHelper.CreateMockProject();
            _mockProjectService.Setup(s => s.GetProjectByIdAsync(projectId))
                .ReturnsAsync(project);

            // Act
            var result = await _controller.Create(projectId);

            // Assert
            var viewResult = result.Should().BeOfType<ViewResult>().Subject;
            var model = viewResult.Model.Should().BeOfType<Meeting>().Subject;
            model.ProjectId.Should().Be(projectId);
            viewResult.ViewData["ProjectName"].Should().Be(project.Name);
        }

        [Fact]
        public async Task Create_Post_WithValidModel_CreatesMeetingAndRedirects()
        {
            // Arrange
            var meeting = MockHelper.CreateMockMeeting();
            var viewModel = new MeetingCreateViewModel
            {
                Title = meeting.Title,
                Description = meeting.Description,
                ScheduledDate = meeting.ScheduledDate,
                DurationMinutes = meeting.DurationMinutes,
                ProjectId = meeting.ProjectId
            };
            var createdMeeting = MockHelper.CreateMockMeeting();
            createdMeeting.Id = 1;

            _mockMeetingService.Setup(s => s.CreateMeetingAsync(It.IsAny<Meeting>()))
                .ReturnsAsync(createdMeeting);

            // Act
            var result = await _controller.Create(viewModel);

            // Assert
            var redirectResult = result.Should().BeOfType<RedirectToActionResult>().Subject;
            redirectResult.ActionName.Should().Be("Details");
            redirectResult.RouteValues!["id"].Should().Be(createdMeeting.Id);

            _mockMeetingService.Verify(s => s.CreateMeetingAsync(It.Is<Meeting>(m =>
                m.OrganizerUserId == _testUser.Id)), Times.Once);
        }

        [Fact]
        public async Task Create_Post_WithInvalidModel_ReturnsViewWithErrors()
        {
            // Arrange
            var viewModel = new MeetingCreateViewModel(); // Invalid model
            _controller.ModelState.AddModelError("Title", "Title is required");

            // Act
            var result = await _controller.Create(viewModel);

            // Assert
            var viewResult = result.Should().BeOfType<ViewResult>().Subject;
            viewResult.Model.Should().Be(viewModel);
            _mockMeetingService.Verify(s => s.CreateMeetingAsync(It.IsAny<Meeting>()), Times.Never);
        }

        [Fact]
        public async Task Edit_Get_WithValidId_ReturnsMeetingForEdit()
        {
            // Arrange
            var meetingId = 1;
            var meeting = MockHelper.CreateMockMeeting();
            _mockMeetingService.Setup(s => s.GetMeetingByIdAsync(meetingId))
                .ReturnsAsync(meeting);

            // Act
            var result = await _controller.Edit(meetingId);

            // Assert
            var viewResult = result.Should().BeOfType<ViewResult>().Subject;
            var model = viewResult.Model.Should().BeOfType<Meeting>().Subject;
            model.Should().Be(meeting);
        }

        [Fact]
        public async Task Edit_Post_WithValidModel_UpdatesMeetingAndRedirects()
        {
            // Arrange
            var meeting = MockHelper.CreateMockMeeting();
            meeting.Id = 1;

            var viewModel = MeetingEditViewModel.FromEntity(meeting);

            _mockMeetingService.Setup(s => s.GetMeetingByIdAsync(meeting.Id))
                .ReturnsAsync(meeting);
            _mockMeetingService.Setup(s => s.UpdateMeetingAsync(It.IsAny<Meeting>()))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.Edit(meeting.Id, viewModel);

            // Assert
            var redirectResult = result.Should().BeOfType<RedirectToActionResult>().Subject;
            redirectResult.ActionName.Should().Be("Details");
            redirectResult.RouteValues!["id"].Should().Be(meeting.Id);

            _mockMeetingService.Verify(s => s.UpdateMeetingAsync(It.IsAny<Meeting>()), Times.Once);
        }

        [Fact]
        public async Task Delete_Get_WithValidId_ReturnsMeetingForDeletion()
        {
            // Arrange
            var meetingId = 1;
            var meeting = MockHelper.CreateMockMeeting();
            _mockMeetingService.Setup(s => s.GetMeetingByIdAsync(meetingId))
                .ReturnsAsync(meeting);

            // Act
            var result = await _controller.Delete(meetingId);

            // Assert
            var viewResult = result.Should().BeOfType<ViewResult>().Subject;
            var model = viewResult.Model.Should().BeOfType<Meeting>().Subject;
            model.Should().Be(meeting);
        }

        [Fact]
        public async Task DeleteConfirmed_WithValidId_DeletesMeetingAndRedirects()
        {
            // Arrange
            var meetingId = 1;
            _mockMeetingService.Setup(s => s.DeleteMeetingAsync(meetingId))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.DeleteConfirmed(meetingId);

            // Assert
            var redirectResult = result.Should().BeOfType<RedirectToActionResult>().Subject;
            redirectResult.ActionName.Should().Be("Index");

            _mockMeetingService.Verify(s => s.DeleteMeetingAsync(meetingId), Times.Once);
        }

        [Fact]
        public async Task StartMeeting_WithValidId_StartsMeetingAndRedirects()
        {
            // Arrange
            var meetingId = 1;
            _mockMeetingService.Setup(s => s.StartMeetingAsync(meetingId))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.StartMeeting(meetingId);

            // Assert
            var redirectResult = result.Should().BeOfType<RedirectToActionResult>().Subject;
            redirectResult.ActionName.Should().Be("Details");
            redirectResult.RouteValues!["id"].Should().Be(meetingId);

            _mockMeetingService.Verify(s => s.StartMeetingAsync(meetingId), Times.Once);
        }

        [Fact]
        public async Task EndMeeting_WithValidId_EndsMeetingAndRedirects()
        {
            // Arrange
            var meetingId = 1;
            _mockMeetingService.Setup(s => s.EndMeetingAsync(meetingId))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.EndMeeting(meetingId);

            // Assert
            var redirectResult = result.Should().BeOfType<RedirectToActionResult>().Subject;
            redirectResult.ActionName.Should().Be("Details");
            redirectResult.RouteValues!["id"].Should().Be(meetingId);

            _mockMeetingService.Verify(s => s.EndMeetingAsync(meetingId), Times.Once);
        }

        [Fact]
        public async Task Upcoming_ReturnsUpcomingMeetings()
        {
            // Arrange
            var upcomingMeetings = MockHelper.CreateMockMeetings(4);
            _mockMeetingService.Setup(s => s.GetUpcomingMeetingsAsync(_testUser.Id, 14))
                .ReturnsAsync(upcomingMeetings);

            // Act
            var result = await _controller.Upcoming();

            // Assert
            var viewResult = result.Should().BeOfType<ViewResult>().Subject;
            var model = viewResult.Model.Should().BeAssignableTo<IEnumerable<Meeting>>().Subject;
            model.Should().HaveCount(4);
        }

        [Fact]
        public async Task AddAttendee_WithValidData_AddsAttendeeAndReturnsJson()
        {
            // Arrange
            var meetingId = 1;
            var userId = "user123";
            var role = AttendeeRole.Attendee;

            _mockMeetingService.Setup(s => s.AddAttendeeAsync(meetingId, userId, role, true))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.AddAttendee(meetingId, userId, role, true);

            // Assert
            var jsonResult = result.Should().BeOfType<JsonResult>().Subject;
            var resultValue = jsonResult.Value.Should().BeAssignableTo<object>().Subject;

            _mockMeetingService.Verify(s => s.AddAttendeeAsync(meetingId, userId, role, true), Times.Once);
        }

        [Fact]
        public async Task RespondToInvite_WithValidResponse_UpdatesAttendanceAndRedirects()
        {
            // Arrange
            var meetingId = 1;
            var response = AttendanceStatus.Accepted;

            _mockMeetingService.Setup(s => s.RespondToMeetingInviteAsync(meetingId, _testUser.Id, response))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.RespondToInvite(meetingId, response);

            // Assert
            var redirectResult = result.Should().BeOfType<RedirectToActionResult>().Subject;
            redirectResult.ActionName.Should().Be("Details");
            redirectResult.RouteValues!["id"].Should().Be(meetingId);

            _mockMeetingService.Verify(s => s.RespondToMeetingInviteAsync(meetingId, _testUser.Id, response), Times.Once);
        }
    }
}
