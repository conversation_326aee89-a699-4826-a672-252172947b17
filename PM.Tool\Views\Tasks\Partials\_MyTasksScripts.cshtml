@model MyTasksViewModel
@using PM.Tool.Core.Enums

<script src="~/lib/fullcalendar/main.min.js"></script>
<script>
    $(document).ready(function () {
        // Initialize view toggle
        initializeViewToggle();

        // Initialize bulk actions
        initializeBulkActions();

        // Initialize quick filters
        initializeQuickFilters();

        // Initialize calendar
        initializeCalendar();

        // Initialize task checkboxes
        initializeTaskCheckboxes();
    });

    function initializeViewToggle() {
        $('.view-toggle-btn').on('click', function() {
            const view = $(this).data('view');

            // Update button states
            $('.view-toggle-btn').removeClass('active bg-primary-600 text-white').addClass('text-neutral-600');
            $(this).addClass('active bg-primary-600 text-white').removeClass('text-neutral-600');

            // Show/hide views
            $('.view-content').addClass('hidden');
            $(`#${view}View`).removeClass('hidden');

            // Initialize calendar if switching to calendar view
            if (view === 'calendar') {
                setTimeout(() => {
                    if (window.taskCalendar) {
                        window.taskCalendar.render();
                    }
                }, 100);
            }
        });
    }

    function initializeBulkActions() {
        $('#bulkAction, #applyBulkAction').prop('disabled', true);

        $('#applyBulkAction').on('click', function() {
            const action = $('#bulkAction').val();
            const selectedTasks = $('.task-checkbox:checked').map(function() {
                return $(this).val();
            }).get();

            if (selectedTasks.length === 0) {
                alert('Please select at least one task.');
                return;
            }

            if (!action) {
                alert('Please select an action.');
                return;
            }

            // Handle different bulk actions
            switch(action) {
                case 'status':
                    showBulkStatusModal(selectedTasks);
                    break;
                case 'priority':
                    showBulkPriorityModal(selectedTasks);
                    break;
                case 'assign':
                    showBulkAssignModal(selectedTasks);
                    break;
                case 'delete':
                    if (confirm(`Are you sure you want to delete ${selectedTasks.length} task(s)?`)) {
                        bulkDeleteTasks(selectedTasks);
                    }
                    break;
            }
        });
    }

    function initializeQuickFilters() {
        $('.quick-filter-btn').on('click', function() {
            const filter = $(this).data('filter');
            const currentUrl = new URL(window.location);

            // Clear existing filters
            currentUrl.searchParams.delete('status');
            currentUrl.searchParams.delete('priority');
            currentUrl.searchParams.delete('isOverdue');
            currentUrl.searchParams.delete('dueDateFrom');
            currentUrl.searchParams.delete('dueDateTo');

            // Apply quick filter
            switch(filter) {
                case 'today':
                    const today = new Date().toISOString().split('T')[0];
                    currentUrl.searchParams.set('dueDateFrom', today);
                    currentUrl.searchParams.set('dueDateTo', today);
                    break;
                case 'thisweek':
                    const startOfWeek = new Date();
                    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay());
                    const endOfWeek = new Date(startOfWeek);
                    endOfWeek.setDate(endOfWeek.getDate() + 6);
                    currentUrl.searchParams.set('dueDateFrom', startOfWeek.toISOString().split('T')[0]);
                    currentUrl.searchParams.set('dueDateTo', endOfWeek.toISOString().split('T')[0]);
                    break;
                case 'overdue':
                    currentUrl.searchParams.set('isOverdue', 'true');
                    break;
                case 'highpriority':
                    currentUrl.searchParams.set('priority', 'High');
                    break;
            }

            window.location.href = currentUrl.toString();
        });
    }

    function initializeCalendar() {
        const calendarEl = document.getElementById('taskCalendar');
        if (!calendarEl) return;

        try {
            // Initialize calendar with basic configuration - no events for now to avoid JS errors
            window.taskCalendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,listWeek'
                },
                height: 'auto',
                events: [], // Empty events array to avoid serialization issues
                eventClick: function(info) {
                    info.jsEvent.preventDefault();
                    if (info.event.url) {
                        window.open(info.event.url, '_blank');
                    }
                },
                eventDidMount: function(info) {
                    // Add tooltip
                    const props = info.event.extendedProps;
                    if (props) {
                        info.el.setAttribute('title',
                            `${info.event.title}\nProject: ${props.project}\nStatus: ${props.status}\nPriority: ${props.priority}`
                        );
                    }
                }
            });

            // Render the calendar
            window.taskCalendar.render();
        } catch (error) {
            console.error('Error initializing calendar:', error);
        }
    }

    function initializeTaskCheckboxes() {
        $('.task-checkbox').on('change', function() {
            const checkedCount = $('.task-checkbox:checked').length;
            $('#bulkAction, #applyBulkAction').prop('disabled', checkedCount === 0);
        });
    }

    function updateTaskStatus(taskId, newStatus) {
        $.ajax({
            url: '@Url.Action("UpdateTaskStatus", "Api")',
            method: 'POST',
            data: {
                taskId: taskId,
                status: newStatus
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Error updating task status: ' + response.message);
                }
            },
            error: function() {
                alert('Error updating task status. Please try again.');
            }
        });
    }

    function showExportModal() {
        // Implementation for export modal
        alert('Export functionality will be implemented here.');
    }

    function toggleCalendarView() {
        $('.view-toggle-btn[data-view="calendar"]').click();
    }

    // Bulk action modal functions
    function showBulkStatusModal(taskIds) {
        // Implementation for bulk status change modal
        const newStatus = prompt('Enter new status (ToDo, InProgress, Done, Cancelled):');
        if (newStatus) {
            bulkUpdateStatus(taskIds, newStatus);
        }
    }

    function showBulkPriorityModal(taskIds) {
        // Implementation for bulk priority change modal
        const newPriority = prompt('Enter new priority (Low, Medium, High, Critical):');
        if (newPriority) {
            bulkUpdatePriority(taskIds, newPriority);
        }
    }

    function showBulkAssignModal(taskIds) {
        // Implementation for bulk assign modal
        alert('Bulk assign functionality will be implemented here.');
    }

    function bulkUpdateStatus(taskIds, status) {
        $.ajax({
            url: '@Url.Action("BulkUpdateStatus", "Api")',
            method: 'POST',
            data: {
                taskIds: taskIds,
                status: status
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Error updating tasks: ' + response.message);
                }
            },
            error: function() {
                alert('Error updating tasks. Please try again.');
            }
        });
    }

    function bulkUpdatePriority(taskIds, priority) {
        $.ajax({
            url: '@Url.Action("BulkUpdatePriority", "Api")',
            method: 'POST',
            data: {
                taskIds: taskIds,
                priority: priority
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Error updating tasks: ' + response.message);
                }
            },
            error: function() {
                alert('Error updating tasks. Please try again.');
            }
        });
    }

    function bulkDeleteTasks(taskIds) {
        $.ajax({
            url: '@Url.Action("BulkDelete", "Api")',
            method: 'POST',
            data: {
                taskIds: taskIds
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Error deleting tasks: ' + response.message);
                }
            },
            error: function() {
                alert('Error deleting tasks. Please try again.');
            }
        });
    }
</script>
