@model MyTasksViewModel

<!-- Compact View Controls -->
<div class="flex items-center justify-between py-2 mb-3">
    <!-- View Mode Toggle -->
    <div class="flex items-center space-x-3">
        <span class="text-sm font-medium text-neutral-600 dark:text-neutral-400">View:</span>
        <div class="flex rounded border border-neutral-300 dark:border-neutral-600">
            <button type="button" class="px-2 py-1 text-xs rounded-l view-toggle-btn active bg-primary-600 text-white" data-view="list">
                <i class="fas fa-list mr-1"></i>List
            </button>
            <button type="button" class="px-2 py-1 text-xs view-toggle-btn text-neutral-600 dark:text-neutral-400" data-view="grid">
                <i class="fas fa-th mr-1"></i>Grid
            </button>
            <button type="button" class="px-2 py-1 text-xs rounded-r view-toggle-btn text-neutral-600 dark:text-neutral-400" data-view="calendar">
                <i class="fas fa-calendar mr-1"></i>Calendar
            </button>
        </div>
    </div>

    <!-- Results Info and Bulk Actions -->
    <div class="flex items-center space-x-3">
        <!-- Results Count -->
        <span class="text-sm text-neutral-500 dark:text-neutral-400">
            @Model.Tasks.Count() of @Model.Pagination.TotalItems tasks
        </span>

        <!-- Bulk Actions (Hidden by default, shown when tasks selected) -->
        <div id="compactBulkActions" class="hidden flex items-center space-x-2">
            <select id="bulkAction" class="px-2 py-1 text-xs border border-neutral-300 dark:border-neutral-600 rounded focus:ring-1 focus:ring-primary-500 dark:bg-dark-700 dark:text-dark-100" disabled>
                <option value="">Bulk Actions</option>
                <option value="status">Change Status</option>
                <option value="priority">Change Priority</option>
                <option value="assign">Reassign</option>
                <option value="delete">Delete</option>
            </select>
            <button type="button" id="applyBulkAction" class="px-2 py-1 text-xs font-medium text-white bg-primary-600 hover:bg-primary-700 rounded transition-colors" disabled>
                Apply
            </button>
        </div>
    </div>
</div>
