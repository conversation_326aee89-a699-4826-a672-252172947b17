@model MyTasksViewModel

<!-- Refined View Controls -->
<div class="flex items-center justify-between py-4 mb-6 border-b border-neutral-200 dark:border-neutral-700">
    <!-- View Mode Toggle -->
    <div class="flex items-center space-x-5">
        <span class="text-sm font-semibold text-neutral-700 dark:text-neutral-300">View:</span>
        <div class="flex rounded-lg border border-neutral-300 dark:border-neutral-600 bg-neutral-50 dark:bg-neutral-800 p-1">
            <button type="button" class="px-4 py-2 text-sm font-medium rounded-md view-toggle-btn active bg-white dark:bg-neutral-700 text-primary-600 dark:text-primary-400 shadow-sm" data-view="list">
                <i class="fas fa-list mr-2"></i>List
            </button>
            <button type="button" class="px-4 py-2 text-sm font-medium rounded-md view-toggle-btn text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-all duration-200" data-view="grid">
                <i class="fas fa-th mr-2"></i>Grid
            </button>
            <button type="button" class="px-4 py-2 text-sm font-medium rounded-md view-toggle-btn text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-all duration-200" data-view="calendar">
                <i class="fas fa-calendar mr-2"></i>Calendar
            </button>
        </div>
    </div>

    <!-- Results Info and Bulk Actions -->
    <div class="flex items-center space-x-6">
        <!-- Results Count -->
        <div class="flex items-center space-x-3">
            <span class="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                Showing @Model.Tasks.Count() of @Model.Pagination.TotalItems tasks
            </span>
            @if (Model.Pagination.TotalPages > 1)
            {
                <span class="text-xs text-neutral-500 dark:text-neutral-500 px-2 py-1 bg-neutral-100 dark:bg-neutral-700 rounded">
                    Page @Model.Pagination.CurrentPage of @Model.Pagination.TotalPages
                </span>
            }
        </div>

        <!-- Bulk Actions (Hidden by default, shown when tasks selected) -->
        <div id="compactBulkActions" class="hidden flex items-center space-x-3 pl-6 border-l border-neutral-300 dark:border-neutral-600">
            <select id="bulkAction" class="px-4 py-2 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 dark:bg-neutral-700 dark:text-neutral-100 transition-all duration-200" disabled>
                <option value="">Bulk Actions</option>
                <option value="status">Change Status</option>
                <option value="priority">Change Priority</option>
                <option value="assign">Reassign</option>
                <option value="delete">Delete</option>
            </select>
            <button type="button" id="applyBulkAction" class="px-4 py-2 text-sm font-semibold text-white bg-primary-600 hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg shadow-sm transition-all duration-200 hover:shadow-md" disabled>
                <i class="fas fa-check mr-2"></i>Apply
            </button>
        </div>
    </div>
</div>
