@model MyTasksViewModel

<!-- View Toggle and Bulk Actions -->
<div class="card-custom mb-6">
    <div class="card-body-custom">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <!-- View Mode Toggle -->
            <div class="flex items-center space-x-2 mb-4 sm:mb-0">
                <span class="text-sm font-medium text-neutral-700 dark:text-neutral-300">View:</span>
                <div class="flex rounded-lg border border-neutral-300 dark:border-dark-600">
                    <button type="button" class="px-3 py-1 text-sm rounded-l-lg view-toggle-btn active" data-view="list">
                        <i class="fas fa-list mr-1"></i>List
                    </button>
                    <button type="button" class="px-3 py-1 text-sm view-toggle-btn" data-view="grid">
                        <i class="fas fa-th mr-1"></i>Grid
                    </button>
                    <button type="button" class="px-3 py-1 text-sm rounded-r-lg view-toggle-btn" data-view="calendar">
                        <i class="fas fa-calendar mr-1"></i>Calendar
                    </button>
                </div>
            </div>

            <!-- Bulk Actions -->
            <div class="flex items-center space-x-2">
                <select id="bulkAction" class="form-input-custom text-sm" disabled>
                    <option value="">Bulk Actions</option>
                    <option value="status">Change Status</option>
                    <option value="priority">Change Priority</option>
                    <option value="assign">Reassign</option>
                    <option value="delete">Delete</option>
                </select>
                <button type="button" id="applyBulkAction" class="btn-secondary-custom text-sm" disabled>
                    Apply
                </button>
            </div>
        </div>
    </div>
</div>
