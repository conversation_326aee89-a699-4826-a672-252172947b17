using PM.Tool.Core.Enums;
using PM.Tool.Core.Entities;
using System.ComponentModel.DataAnnotations;
using TaskStatus = PM.Tool.Core.Enums.TaskStatus;

namespace PM.Tool.Models.ViewModels
{
    public class TaskViewModel
    {
        public int Id { get; set; }

        [Required]
        [Display(Name = "Task Title")]
        [StringLength(200, ErrorMessage = "Task title cannot exceed 200 characters.")]
        public string Title { get; set; } = string.Empty;

        [Display(Name = "Description")]
        [StringLength(2000, ErrorMessage = "Description cannot exceed 2000 characters.")]
        public string? Description { get; set; }

        [Display(Name = "Status")]
        public TaskStatus Status { get; set; } = TaskStatus.ToDo;

        [Display(Name = "Priority")]
        public TaskPriority Priority { get; set; } = TaskPriority.Medium;

        public int ProjectId { get; set; }
        public string ProjectName { get; set; } = string.Empty;

        [Display(Name = "Assigned To")]
        public string? AssignedToUserId { get; set; }
        public string? AssignedToName { get; set; }

        public int? ParentTaskId { get; set; }
        public string? ParentTaskTitle { get; set; }

        [Display(Name = "Start Date")]
        [DataType(DataType.Date)]
        public DateTime? StartDate { get; set; }

        [Display(Name = "Due Date")]
        [DataType(DataType.Date)]
        public DateTime? DueDate { get; set; }

        [Display(Name = "Completed Date")]
        [DataType(DataType.Date)]
        public DateTime? CompletedDate { get; set; }

        [Display(Name = "Estimated Hours")]
        [Range(0, int.MaxValue, ErrorMessage = "Estimated hours must be a positive number.")]
        public int? EstimatedHours { get; set; }

        [Display(Name = "Actual Hours")]
        [Range(0, int.MaxValue, ErrorMessage = "Actual hours must be a positive number.")]
        public int ActualHours { get; set; }

        public string CreatedByUserId { get; set; } = string.Empty;
        public string? CreatedByName { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public bool IsOverdue { get; set; }
        public bool IsSubTask { get; set; }
        public int? SubTaskCount { get; set; }
        public int CompletedSubTasks { get; set; }
        public double SubTaskProgress { get; set; }
        public double Progress { get; set; }
        public string? WbsCode { get; set; }
    }

    public class TaskCreateViewModel
    {
        [Required]
        [Display(Name = "Task Title")]
        [StringLength(200, ErrorMessage = "Task title cannot exceed 200 characters.")]
        public string Title { get; set; } = string.Empty;

        [Display(Name = "Description")]
        [StringLength(2000, ErrorMessage = "Description cannot exceed 2000 characters.")]
        public string? Description { get; set; }

        [Display(Name = "Priority")]
        public TaskPriority Priority { get; set; } = TaskPriority.Medium;

        [Required]
        public int ProjectId { get; set; }
        public string ProjectName { get; set; } = string.Empty;

        [Display(Name = "Assigned To")]
        public string? AssignedToUserId { get; set; }

        public int? ParentTaskId { get; set; }
        public string? ParentTaskTitle { get; set; }

        [Display(Name = "Start Date")]
        [DataType(DataType.Date)]
        public DateTime? StartDate { get; set; }

        [Display(Name = "Due Date")]
        [DataType(DataType.Date)]
        public DateTime? DueDate { get; set; }

        [Display(Name = "Estimated Hours")]
        [Range(0, int.MaxValue, ErrorMessage = "Estimated hours must be a positive number.")]
        public int EstimatedHours { get; set; }

        // Available users for assignment
        public List<UserSelectViewModel> AvailableUsers { get; set; } = new();
    }

    public class TaskEditViewModel : TaskCreateViewModel
    {
        public int Id { get; set; }

        [Display(Name = "Status")]
        public TaskStatus Status { get; set; } = TaskStatus.ToDo;

        [Display(Name = "Actual Hours")]
        [Range(0, int.MaxValue, ErrorMessage = "Actual hours must be a positive number.")]
        public int ActualHours { get; set; }
    }

    public class TaskDetailsViewModel : TaskViewModel
    {
        public List<TaskViewModel> SubTasks { get; set; } = new();
        public List<TaskCommentViewModel> Comments { get; set; } = new();
        public List<TaskAttachmentViewModel> Attachments { get; set; } = new();
        public List<TaskHistoryViewModel> History { get; set; } = new();
        public bool CanEdit { get; set; }
        public bool CanDelete { get; set; }
        public bool CanComment { get; set; }
        public UserRole? CurrentUserRole { get; set; }
    }

    public class TaskCommentViewModel
    {
        public int Id { get; set; }
        public string Content { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class TaskAttachmentViewModel
    {
        public int Id { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string? ContentType { get; set; }
        public long FileSize { get; set; }
        public string UploadedByName { get; set; } = string.Empty;
        public DateTime UploadedAt { get; set; }
    }

    public class AddCommentViewModel
    {
        public int TaskId { get; set; }
        public string TaskTitle { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Comment")]
        [StringLength(2000, ErrorMessage = "Comment cannot exceed 2000 characters.")]
        public string Content { get; set; } = string.Empty;
    }

    public class UserSelectViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
    }

    // Enhanced ViewModels with FormHelper integration
    public class TaskCreateViewModelEnhanced : BaseCreateViewModel, IEntityViewModel<TaskEntity>
    {
        [Required]
        [MaxLength(200)]
        [Display(Name = "Task Title")]
        public string Title { get; set; } = string.Empty;

        [MaxLength(2000)]
        [Display(Name = "Description")]
        public string? Description { get; set; }

        [Display(Name = "Priority")]
        public TaskPriority Priority { get; set; } = TaskPriority.Medium;

        [Required]
        [Display(Name = "Project")]
        public int ProjectId { get; set; }

        [Display(Name = "Assigned To")]
        public string? AssignedToUserId { get; set; }

        [Display(Name = "Parent Task")]
        public int? ParentTaskId { get; set; }

        [Display(Name = "Start Date")]
        [DataType(DataType.Date)]
        public DateTime? StartDate { get; set; }

        [Display(Name = "Due Date")]
        [DataType(DataType.Date)]
        public DateTime? DueDate { get; set; }

        [Display(Name = "Estimated Hours")]
        [Range(0, int.MaxValue, ErrorMessage = "Estimated hours must be a positive number")]
        public int EstimatedHours { get; set; }

        [MaxLength(50)]
        [Display(Name = "WBS Code")]
        public string? WbsCode { get; set; }

        [Display(Name = "WBS Level")]
        [Range(1, 10, ErrorMessage = "WBS Level must be between 1 and 10")]
        public int WbsLevel { get; set; } = 1;

        [Display(Name = "Sort Order")]
        public int SortOrder { get; set; } = 0;

        [Display(Name = "Is Recurring")]
        public bool IsRecurring { get; set; } = false;

        [Display(Name = "Is Template")]
        public bool IsTemplate { get; set; } = false;

        public TaskEntity ToEntity()
        {
            return new TaskEntity
            {
                Title = Title,
                Description = Description,
                Priority = Priority,
                ProjectId = ProjectId,
                AssignedToUserId = AssignedToUserId,
                ParentTaskId = ParentTaskId,
                StartDate = StartDate,
                DueDate = DueDate,
                EstimatedHours = EstimatedHours,
                WbsCode = WbsCode,
                WbsLevel = WbsLevel,
                SortOrder = SortOrder,
                IsRecurring = IsRecurring,
                IsTemplate = IsTemplate,
                Status = TaskStatus.ToDo,
                CreatedByUserId = "" // Will be set by controller
            };
        }

        public void UpdateEntity(TaskEntity entity)
        {
            entity.Title = Title;
            entity.Description = Description;
            entity.Priority = Priority;
            entity.ProjectId = ProjectId;
            entity.AssignedToUserId = AssignedToUserId;
            entity.ParentTaskId = ParentTaskId;
            entity.StartDate = StartDate;
            entity.DueDate = DueDate;
            entity.EstimatedHours = EstimatedHours;
            entity.WbsCode = WbsCode;
            entity.WbsLevel = WbsLevel;
            entity.SortOrder = SortOrder;
            entity.IsRecurring = IsRecurring;
            entity.IsTemplate = IsTemplate;
        }
    }

    public class TaskEditViewModelEnhanced : TaskCreateViewModelEnhanced
    {
        public int Id { get; set; }

        [Display(Name = "Status")]
        public TaskStatus Status { get; set; } = TaskStatus.ToDo;

        [Display(Name = "Actual Hours")]
        [Range(0, int.MaxValue, ErrorMessage = "Actual hours must be a positive number")]
        public int ActualHours { get; set; }

        [Display(Name = "Completed Date")]
        [DataType(DataType.DateTime)]
        public DateTime? CompletedDate { get; set; }

        [Display(Name = "Is Recurring")]
        public new bool IsRecurring { get; set; } = false;

        [Display(Name = "Is Template")]
        public new bool IsTemplate { get; set; } = false;

        [Display(Name = "Created By")]
        public string CreatedByUserId { get; set; } = string.Empty;

        public static TaskEditViewModelEnhanced FromEntity(TaskEntity task)
        {
            return new TaskEditViewModelEnhanced
            {
                Id = task.Id,
                Title = task.Title,
                Description = task.Description,
                Status = task.Status,
                Priority = task.Priority,
                ProjectId = task.ProjectId,
                AssignedToUserId = task.AssignedToUserId,
                ParentTaskId = task.ParentTaskId,
                StartDate = task.StartDate,
                DueDate = task.DueDate,
                CompletedDate = task.CompletedDate,
                EstimatedHours = task.EstimatedHours,
                ActualHours = task.ActualHours,
                WbsCode = task.WbsCode,
                WbsLevel = task.WbsLevel,
                SortOrder = task.SortOrder,
                IsRecurring = task.IsRecurring,
                IsTemplate = task.IsTemplate,
                CreatedByUserId = task.CreatedByUserId
            };
        }

        public new void UpdateEntity(TaskEntity entity)
        {
            base.UpdateEntity(entity);
            entity.Status = Status;
            entity.ActualHours = ActualHours;
            entity.CompletedDate = CompletedDate;
            entity.IsRecurring = IsRecurring;
            entity.IsTemplate = IsTemplate;
            entity.CreatedByUserId = CreatedByUserId;
        }
    }

    public class TaskHistoryViewModel
    {
        public int Id { get; set; }
        public string Action { get; set; } = string.Empty;
        public string ActionDescription { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string? OldValues { get; set; }
        public string? NewValues { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? IpAddress { get; set; }

        public string GetFormattedAction()
        {
            return Action switch
            {
                "Create" => "created the task",
                "Update" => "updated the task",
                "ChangeStatus" => GetStatusChangeDescription(),
                "Delete" => "deleted the task",
                "Assign" => "assigned the task",
                "Comment" => "added a comment",
                "Attachment" => "added an attachment",
                _ => Action.ToLower()
            };
        }

        private string GetStatusChangeDescription()
        {
            if (!string.IsNullOrEmpty(OldValues) && !string.IsNullOrEmpty(NewValues))
            {
                try
                {
                    // Parse JSON values to extract status information
                    var oldStatus = ExtractStatusFromJson(OldValues);
                    var newStatus = ExtractStatusFromJson(NewValues);

                    if (!string.IsNullOrEmpty(oldStatus) && !string.IsNullOrEmpty(newStatus))
                    {
                        return $"changed status from \"{FormatStatus(oldStatus)}\" to \"{FormatStatus(newStatus)}\"";
                    }
                }
                catch
                {
                    // Fallback if JSON parsing fails
                }
            }

            return "changed the task status";
        }

        private string ExtractStatusFromJson(string json)
        {
            // Simple extraction - in a real implementation, you might use JSON parsing
            if (json.Contains("\"Status\":"))
            {
                var start = json.IndexOf("\"Status\":") + 9;
                var end = json.IndexOf(",", start);
                if (end == -1) end = json.IndexOf("}", start);

                if (start > 8 && end > start)
                {
                    return json.Substring(start, end - start).Trim('"', ' ');
                }
            }
            return string.Empty;
        }

        private string FormatStatus(string status)
        {
            return status switch
            {
                "ToDo" => "To Do",
                "InProgress" => "In Progress",
                "InReview" => "In Review",
                "Done" => "Done",
                "Cancelled" => "Cancelled",
                _ => status
            };
        }
    }
}
