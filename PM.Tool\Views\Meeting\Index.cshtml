@model IEnumerable<PM.Tool.Core.Entities.Meeting>
@using PM.Tool.Core.Enums
@{
    ViewData["Title"] = Localizer["Meeting.Management"];
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-users mr-3 text-primary-600 dark:text-primary-400"></i>
                @Localizer["Meeting.Management"]
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                @Localizer["Meeting.ManagementDescription"]
            </p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
            @{
                ViewData["Text"] = Localizer["Meeting.ScheduleMeeting"];
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-plus";
                ViewData["Href"] = Url.Action("Create");
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = Localizer["Meeting.CalendarView"];
                ViewData["Variant"] = "outline";
                ViewData["Icon"] = "fas fa-calendar";
                ViewData["Href"] = Url.Action("Calendar");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Filter and Search -->
<div class="card-custom mb-8">
    <div class="card-body-custom">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div>
                @{
                    ViewData["Label"] = "Status";
                    ViewData["Name"] = "statusFilter";
                    ViewData["Type"] = "select";
                    ViewData["ContainerClasses"] = "";
                    ViewData["Options"] = new List<object> {
                        new { Value = "", Text = "All Statuses" },
                        new { Value = "Scheduled", Text = "Scheduled" },
                        new { Value = "InProgress", Text = "In Progress" },
                        new { Value = "Completed", Text = "Completed" },
                        new { Value = "Cancelled", Text = "Cancelled" }
                    };
                }
                <partial name="Components/_FormInput" view-data="ViewData" />
            </div>
            <div>
                @{
                    ViewData["Label"] = "Type";
                    ViewData["Name"] = "typeFilter";
                    ViewData["Type"] = "select";
                    ViewData["ContainerClasses"] = "";
                    ViewData["Options"] = new List<object> {
                        new { Value = "", Text = "All Types" },
                        new { Value = "StandUp", Text = "Stand-up" },
                        new { Value = "Planning", Text = "Planning" },
                        new { Value = "Review", Text = "Review" },
                        new { Value = "Retrospective", Text = "Retrospective" },
                        new { Value = "General", Text = "General" }
                    };
                }
                <partial name="Components/_FormInput" view-data="ViewData" />
            </div>
            <div class="lg:col-span-2">
                @{
                    ViewData["Label"] = "Search";
                    ViewData["Name"] = "searchInput";
                    ViewData["Type"] = "text";
                    ViewData["Placeholder"] = "Search meetings...";
                    ViewData["ContainerClasses"] = "";
                }
                <partial name="Components/_FormInput" view-data="ViewData" />
            </div>
            <div class="flex items-end">
                @{
                    ViewData["Text"] = "Clear";
                    ViewData["Variant"] = "outline";
                    ViewData["Icon"] = "fas fa-times";
                    ViewData["FullWidth"] = true;
                    ViewData["Id"] = "clearFilters";
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
        </div>
    </div>
</div>

<!-- Meetings List -->
<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-8" id="meetingsContainer">
    @if (Model != null && Model.Any())
    {
        @foreach (var meeting in Model)
        {
            <div class="meeting-card transition-all duration-200 hover:scale-105"
                 data-status="@meeting.Status.ToString().ToLower()"
                 data-type="@meeting.Type.ToString().ToLower()">
                <div class="card-custom h-full">
                    <div class="card-header-custom">
                        <div class="flex justify-between items-center">
                            @{
                                var statusClass = meeting.Status.ToString().ToLower() switch {
                                    "scheduled" => "bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200",
                                    "inprogress" => "bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200",
                                    "completed" => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
                                    "cancelled" => "bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200",
                                    _ => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200"
                                };
                            }
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @statusClass">
                                @meeting.Status
                            </span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200">
                                @meeting.Type
                            </span>
                        </div>
                    </div>
                    <div class="card-body-custom">
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">@meeting.Title</h3>
                        <p class="text-sm text-neutral-600 dark:text-dark-300 mb-4">@meeting.Description</p>

                        <div class="space-y-2">
                            <div class="flex items-center text-sm text-neutral-600 dark:text-dark-300">
                                <i class="fas fa-calendar-alt w-4 h-4 mr-3 text-neutral-400 dark:text-dark-500"></i>
                                <span>@meeting.ScheduledDate.ToString("MMM dd, yyyy")</span>
                            </div>
                            <div class="flex items-center text-sm text-neutral-600 dark:text-dark-300">
                                <i class="fas fa-clock w-4 h-4 mr-3 text-neutral-400 dark:text-dark-500"></i>
                                <span>@meeting.ScheduledDate.ToString("HH:mm") - @meeting.ScheduledDate.AddHours(1).ToString("HH:mm")</span>
                            </div>
                            @if (!string.IsNullOrEmpty(meeting.Location))
                            {
                                <div class="flex items-center text-sm text-neutral-600 dark:text-dark-300">
                                    <i class="fas fa-map-marker-alt w-4 h-4 mr-3 text-neutral-400 dark:text-dark-500"></i>
                                    <span>@meeting.Location</span>
                                </div>
                            }
                            <div class="flex items-center text-sm text-neutral-600 dark:text-dark-300">
                                <i class="fas fa-users w-4 h-4 mr-3 text-neutral-400 dark:text-dark-500"></i>
                                <span>@meeting.Attendees.Count() participants</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer-custom">
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-neutral-500 dark:text-dark-400">
                                Created @meeting.CreatedAt.ToString("MMM dd")
                            </span>
                            <div class="flex space-x-1">
                                <a asp-action="Details" asp-route-id="@meeting.Id"
                                   class="inline-flex items-center justify-center w-8 h-8 text-xs bg-neutral-100 dark:bg-dark-700 text-neutral-600 dark:text-dark-300 hover:bg-primary-100 dark:hover:bg-primary-900 hover:text-primary-600 dark:hover:text-primary-400 rounded-md transition-colors"
                                   title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                @if (meeting.Status != MeetingStatus.Completed && meeting.Status != MeetingStatus.Cancelled)
                                {
                                    <a asp-action="Edit" asp-route-id="@meeting.Id"
                                       class="inline-flex items-center justify-center w-8 h-8 text-xs bg-neutral-100 dark:bg-dark-700 text-neutral-600 dark:text-dark-300 hover:bg-warning-100 dark:hover:bg-warning-900 hover:text-warning-600 dark:hover:text-warning-400 rounded-md transition-colors"
                                       title="Edit Meeting">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                }
                                @if (meeting.Status == MeetingStatus.Scheduled)
                                {
                                    <button onclick="startMeeting(@meeting.Id)"
                                            class="inline-flex items-center justify-center w-8 h-8 text-xs bg-neutral-100 dark:bg-dark-700 text-neutral-600 dark:text-dark-300 hover:bg-success-100 dark:hover:bg-success-900 hover:text-success-600 dark:hover:text-success-400 rounded-md transition-colors"
                                            title="Start Meeting">
                                        <i class="fas fa-play"></i>
                                    </button>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
    }
    else
    {
        <div class="col-span-full">
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-users text-2xl text-neutral-400 dark:text-dark-500"></i>
                </div>
                <h3 class="text-lg font-medium text-neutral-900 dark:text-dark-100 mb-2">No Meetings Found</h3>
                <p class="text-neutral-500 dark:text-dark-400 mb-6">Schedule your first meeting to get started.</p>
                @{
                    ViewData["Text"] = "Schedule Meeting";
                    ViewData["Variant"] = "primary";
                    ViewData["Icon"] = "fas fa-plus";
                    ViewData["Href"] = Url.Action("Create");
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
        </div>
    }
</div>

<!-- Upcoming Meetings Widget -->
<div class="card-custom">
    <div class="card-header-custom">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                <i class="fas fa-clock text-primary-600 dark:text-primary-400 text-sm"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Upcoming Meetings</h3>
                <p class="text-sm text-neutral-500 dark:text-dark-400">Next 7 days</p>
            </div>
        </div>
    </div>
    <div class="card-body-custom">
        <div id="upcomingMeetings">
            <div class="flex items-center justify-center py-8">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 dark:border-primary-400"></div>
                <span class="ml-2 text-sm text-neutral-500 dark:text-dark-400">Loading...</span>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            loadUpcomingMeetings();

            // Filter functionality
            $('#statusFilter, #typeFilter').on('change', filterMeetings);
            $('#searchInput').on('input', filterMeetings);
            $('#clearFilters').on('click', clearFilters);
        });

        function filterMeetings() {
            const statusFilter = $('#statusFilter').val().toLowerCase();
            const typeFilter = $('#typeFilter').val().toLowerCase();
            const searchTerm = $('#searchInput').val().toLowerCase();

            $('.meeting-card').each(function() {
                const card = $(this);
                const status = card.data('status');
                const type = card.data('type');
                const title = card.find('.card-title').text().toLowerCase();
                const description = card.find('.card-text').text().toLowerCase();

                let show = true;

                if (statusFilter && status !== statusFilter) show = false;
                if (typeFilter && type !== typeFilter) show = false;
                if (searchTerm && !title.includes(searchTerm) && !description.includes(searchTerm)) show = false;

                card.toggle(show);
            });
        }

        function clearFilters() {
            $('#statusFilter, #typeFilter').val('');
            $('#searchInput').val('');
            $('.meeting-card').show();
        }

        function loadUpcomingMeetings() {
            $.get('@Url.Action("GetUpcomingMeetings", "Meeting")')
                .done(function(data) {
                    $('#upcomingMeetings').html(data);
                })
                .fail(function() {
                    $('#upcomingMeetings').html('<div class="alert alert-warning">Failed to load upcoming meetings.</div>');
                });
        }

        function startMeeting(meetingId) {
            if (confirm('Start this meeting now?')) {
                $.post('@Url.Action("StartMeeting", "Meeting")', { id: meetingId })
                    .done(function() {
                        location.reload();
                    })
                    .fail(function() {
                        alert('Failed to start meeting. Please try again.');
                    });
            }
        }
    </script>
}

<style>
    .meeting-card {
        transition: transform 0.2s;
    }
    .meeting-card:hover {
        transform: translateY(-2px);
    }
    .meeting-details i {
        width: 16px;
    }
    .badge.bg-scheduled { background-color: #17a2b8 !important; }
    .badge.bg-inprogress { background-color: #007bff !important; }
    .badge.bg-completed { background-color: #28a745 !important; }
    .badge.bg-cancelled { background-color: #dc3545 !important; }
</style>

@functions {
    string GetStatusBadgeClass(MeetingStatus status)
    {
        return status switch
        {
            MeetingStatus.Scheduled => "info",
            MeetingStatus.InProgress => "primary",
            MeetingStatus.Completed => "success",
            MeetingStatus.Cancelled => "danger",
            _ => "secondary"
        };
    }
}
