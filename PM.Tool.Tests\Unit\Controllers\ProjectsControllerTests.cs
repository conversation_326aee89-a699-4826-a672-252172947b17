using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.Extensions.Logging;
using MockQueryable.Moq;
using Moq;
using PM.Tool.Controllers;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Interfaces;
using PM.Tool.Models.ViewModels;
using PM.Tool.Tests.Helpers;
using Xunit;
using FluentAssertions;

namespace PM.Tool.Tests.Unit.Controllers
{
    public class ProjectsControllerTests
    {
        private readonly Mock<IProjectService> _mockProjectService;
        private readonly Mock<ITaskService> _mockTaskService;
        private readonly Mock<UserManager<ApplicationUser>> _mockUserManager;
        private readonly Mock<ILogger<ProjectsController>> _mockLogger;
        private readonly ProjectsController _controller;
        private readonly ApplicationUser _testUser;

        public ProjectsControllerTests()
        {
            _mockProjectService = new Mock<IProjectService>();
            _mockTaskService = new Mock<ITaskService>();
            _mockUserManager = MockHelper.CreateMockUserManager();
            _mockLogger = MockHelper.CreateMockLogger<ProjectsController>();

            _controller = new ProjectsController(
                _mockProjectService.Object,
                _mockTaskService.Object,
                _mockUserManager.Object,
                _mockLogger.Object);

            _testUser = new ApplicationUser
            {
                Id = "test-user-1",
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Test",
                LastName = "User",
                IsActive = true
            };

            MockHelper.SetupControllerContext(_controller);

            // Setup TempData
            _controller.TempData = new TempDataDictionary(_controller.ControllerContext.HttpContext, Mock.Of<ITempDataProvider>());
        }

        [Fact]
        public async Task Index_WithValidUser_ReturnsViewWithProjects()
        {
            // Arrange
            var projects = new List<Project>
            {
                new Project
                {
                    Id = 1,
                    Name = "Test Project 1",
                    Description = "Test Description 1",
                    Status = ProjectStatus.Active,
                    StartDate = DateTime.UtcNow.AddDays(-30),
                    EndDate = DateTime.UtcNow.AddDays(30),
                    CreatedByUserId = _testUser.Id,
                    CreatedBy = _testUser,
                    CreatedAt = DateTime.UtcNow.AddDays(-30)
                },
                new Project
                {
                    Id = 2,
                    Name = "Test Project 2",
                    Description = "Test Description 2",
                    Status = ProjectStatus.Planning,
                    StartDate = DateTime.UtcNow.AddDays(-15),
                    EndDate = DateTime.UtcNow.AddDays(45),
                    CreatedByUserId = _testUser.Id,
                    CreatedBy = _testUser,
                    CreatedAt = DateTime.UtcNow.AddDays(-15)
                }
            };

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockProjectService.Setup(x => x.GetAllProjectsAsync())
                .ReturnsAsync(projects);
            _mockProjectService.Setup(x => x.GetUserProjectsAsync(_testUser.Id))
                .ReturnsAsync(projects);

            // Act
            var result = await _controller.Index();

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult.Model.Should().BeOfType<List<ProjectViewModel>>();
            var model = viewResult.Model as List<ProjectViewModel>;
            model.Should().HaveCount(2);
            model[0].Name.Should().Be("Test Project 1");
            model[1].Name.Should().Be("Test Project 2");
        }

        [Fact]
        public async Task Index_WithNullUser_RedirectsToLogin()
        {
            // Arrange
            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync((ApplicationUser)null);

            // Act
            var result = await _controller.Index();

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult.ActionName.Should().Be("Login");
            redirectResult.ControllerName.Should().Be("Account");
        }

        [Fact]
        public async Task Index_WithException_ReturnsEmptyView()
        {
            // Arrange
            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockProjectService.Setup(x => x.GetAllProjectsAsync())
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.Index();

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult.Model.Should().BeOfType<List<ProjectViewModel>>();
            var model = viewResult.Model as List<ProjectViewModel>;
            model.Should().BeEmpty();
        }

        [Fact]
        public async Task Details_WithValidProjectAndUser_ReturnsViewWithDetails()
        {
            // Arrange
            var projectId = 1;
            var project = new Project
            {
                Id = projectId,
                Name = "Test Project",
                Description = "Test Description",
                Status = ProjectStatus.Active,
                StartDate = DateTime.UtcNow.AddDays(-30),
                EndDate = DateTime.UtcNow.AddDays(30),
                CreatedByUserId = _testUser.Id,
                CreatedBy = _testUser,
                Members = new List<ProjectMember>(),
                Tasks = new List<TaskEntity>(),
                UpdatedAt = DateTime.UtcNow
            };

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockProjectService.Setup(x => x.GetProjectWithDetailsAsync(projectId))
                .ReturnsAsync(project);
            _mockProjectService.Setup(x => x.GetUserRoleInProjectAsync(projectId, _testUser.Id))
                .ReturnsAsync(UserRole.ProjectManager);
            _mockProjectService.Setup(x => x.GetProjectMembersAsync(projectId))
                .ReturnsAsync(new List<ProjectMember>());
            _mockTaskService.Setup(x => x.GetProjectTasksAsync(projectId))
                .ReturnsAsync(new List<TaskEntity>());

            // Act
            var result = await _controller.Details(projectId);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult.Model.Should().BeOfType<ProjectDetailsViewModel>();
            var model = viewResult.Model as ProjectDetailsViewModel;
            model.Id.Should().Be(projectId);
            model.Name.Should().Be("Test Project");
            model.CanEdit.Should().BeTrue();
        }

        [Fact]
        public async Task Details_WithNonExistentProject_ReturnsNotFound()
        {
            // Arrange
            var projectId = 999;
            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockProjectService.Setup(x => x.GetProjectWithDetailsAsync(projectId))
                .ReturnsAsync((Project)null);

            // Act
            var result = await _controller.Details(projectId);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task Details_WithUnauthorizedUser_ReturnsForbid()
        {
            // Arrange
            var projectId = 1;
            var project = new Project
            {
                Id = projectId,
                Name = "Test Project",
                CreatedByUserId = "other-user-id"
            };

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockProjectService.Setup(x => x.GetProjectWithDetailsAsync(projectId))
                .ReturnsAsync(project);
            _mockProjectService.Setup(x => x.GetUserRoleInProjectAsync(projectId, _testUser.Id))
                .ReturnsAsync((UserRole?)null);

            // Act
            var result = await _controller.Details(projectId);

            // Assert
            result.Should().BeOfType<ForbidResult>();
        }

        [Fact]
        public async Task Create_Get_ReturnsViewWithViewModel()
        {
            // Arrange
            var users = new List<ApplicationUser> { _testUser };
            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);

            // Use MockQueryable to create an async-enabled queryable
            var mockQueryable = users.AsQueryable().BuildMockDbSet();
            _mockUserManager.Setup(x => x.Users)
                .Returns(mockQueryable.Object);

            // Act
            var result = await _controller.Create();

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().BeOfType<ProjectCreateViewModel>();
            viewResult.ViewData["ProjectManagers"].Should().NotBeNull();
        }

        [Fact]
        public async Task Create_Post_WithValidModel_CreatesProjectAndRedirects()
        {
            // Arrange
            var model = new ProjectCreateViewModel
            {
                Name = "New Project",
                Description = "New Description",
                StartDate = DateTime.UtcNow,
                EndDate = DateTime.UtcNow.AddDays(30),
                Budget = 10000,
                ClientName = "Test Client",
                ManagerId = _testUser.Id
            };

            var createdProject = new Project
            {
                Id = 1,
                Name = model.Name,
                Description = model.Description,
                CreatedByUserId = _testUser.Id
            };

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockProjectService.Setup(x => x.CreateProjectAsync(It.IsAny<Project>()))
                .ReturnsAsync(createdProject);
            _mockProjectService.Setup(x => x.AddMemberToProjectAsync(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<UserRole>()))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.Create(model);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult.ActionName.Should().Be("Details");
            redirectResult.RouteValues["id"].Should().Be(1);
        }

        [Fact]
        public async Task Create_Post_WithInvalidModel_ReturnsViewWithModel()
        {
            // Arrange
            var model = new ProjectCreateViewModel
            {
                // Missing Name - this will make ModelState invalid
                Description = "Test Description"
            };
            _controller.ModelState.AddModelError("Name", "Name is required");

            var users = new List<ApplicationUser> { _testUser };
            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);

            // Setup Users mock for ViewData population when ModelState is invalid
            var mockQueryable = users.AsQueryable().BuildMockDbSet();
            _mockUserManager.Setup(x => x.Users)
                .Returns(mockQueryable.Object);

            // Act
            var result = await _controller.Create(model);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult!.Model.Should().Be(model);
        }

        [Fact]
        public async Task Edit_Get_WithValidProject_ReturnsViewWithModel()
        {
            // Arrange
            var projectId = 1;
            var project = new Project
            {
                Id = projectId,
                Name = "Test Project",
                Description = "Test Description",
                Status = ProjectStatus.Active,
                StartDate = DateTime.UtcNow.AddDays(-30),
                EndDate = DateTime.UtcNow.AddDays(30),
                CreatedByUserId = _testUser.Id
            };

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync(project);
            _mockProjectService.Setup(x => x.GetUserRoleInProjectAsync(projectId, _testUser.Id))
                .ReturnsAsync(UserRole.ProjectManager);

            // Act
            var result = await _controller.Edit(projectId);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult.Model.Should().BeOfType<ProjectEditViewModel>();
            var model = viewResult.Model as ProjectEditViewModel;
            model.Id.Should().Be(projectId);
            model.Name.Should().Be("Test Project");
        }

        [Fact]
        public async Task Edit_Get_WithUnauthorizedUser_ReturnsForbid()
        {
            // Arrange
            var projectId = 1;
            var project = new Project
            {
                Id = projectId,
                CreatedByUserId = "other-user-id"
            };

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync(project);
            _mockProjectService.Setup(x => x.GetUserRoleInProjectAsync(projectId, _testUser.Id))
                .ReturnsAsync(UserRole.TeamMember); // Not authorized to edit

            // Act
            var result = await _controller.Edit(projectId);

            // Assert
            result.Should().BeOfType<ForbidResult>();
        }

        [Fact]
        public async Task Edit_Post_WithValidModel_UpdatesProjectAndRedirects()
        {
            // Arrange
            var projectId = 1;
            var model = new ProjectEditViewModel
            {
                Id = projectId,
                Name = "Updated Project",
                Description = "Updated Description",
                Status = ProjectStatus.Active,
                StartDate = DateTime.UtcNow.AddDays(-30),
                EndDate = DateTime.UtcNow.AddDays(30)
            };

            var project = new Project
            {
                Id = projectId,
                Name = "Original Project",
                CreatedByUserId = _testUser.Id
            };

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync(project);
            _mockProjectService.Setup(x => x.GetUserRoleInProjectAsync(projectId, _testUser.Id))
                .ReturnsAsync(UserRole.ProjectManager);
            _mockProjectService.Setup(x => x.UpdateProjectAsync(It.IsAny<Project>()))
                .ReturnsAsync(project);

            // Act
            var result = await _controller.Edit(projectId, model);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult.ActionName.Should().Be("Details");
            redirectResult.RouteValues["id"].Should().Be(projectId);
        }

        [Fact]
        public async Task Delete_Get_WithValidProject_ReturnsViewWithModel()
        {
            // Arrange
            var projectId = 1;
            var project = new Project
            {
                Id = projectId,
                Name = "Test Project",
                Description = "Test Description",
                CreatedByUserId = _testUser.Id,
                CreatedBy = _testUser,
                Members = new List<ProjectMember>()
            };

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync(project);
            _mockProjectService.Setup(x => x.GetUserRoleInProjectAsync(projectId, _testUser.Id))
                .ReturnsAsync(UserRole.Admin);

            // Act
            var result = await _controller.Delete(projectId);

            // Assert
            result.Should().BeOfType<ViewResult>();
            var viewResult = result as ViewResult;
            viewResult.Model.Should().BeOfType<ProjectViewModel>();
            var model = viewResult.Model as ProjectViewModel;
            model.Id.Should().Be(projectId);
            model.Name.Should().Be("Test Project");
        }

        [Fact]
        public async Task DeleteConfirmed_WithValidProject_DeletesProjectAndRedirects()
        {
            // Arrange
            var projectId = 1;
            var project = new Project
            {
                Id = projectId,
                CreatedByUserId = _testUser.Id
            };

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockProjectService.Setup(x => x.GetProjectByIdAsync(projectId))
                .ReturnsAsync(project);
            _mockProjectService.Setup(x => x.GetUserRoleInProjectAsync(projectId, _testUser.Id))
                .ReturnsAsync(UserRole.Admin);
            _mockProjectService.Setup(x => x.DeleteProjectAsync(projectId))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.DeleteConfirmed(projectId);

            // Assert
            result.Should().BeOfType<RedirectToActionResult>();
            var redirectResult = result as RedirectToActionResult;
            redirectResult.ActionName.Should().Be("Index");
        }

        [Fact]
        public async Task GetProjects_WithValidUser_ReturnsJsonWithProjects()
        {
            // Arrange
            var projects = new List<Project>
            {
                new Project { Id = 1, Name = "Project 1" },
                new Project { Id = 2, Name = "Project 2" }
            };

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockProjectService.Setup(x => x.GetUserProjectsAsync(_testUser.Id))
                .ReturnsAsync(projects);

            // Act
            var result = await _controller.GetProjects();

            // Assert
            result.Should().BeOfType<JsonResult>();
            var jsonResult = result as JsonResult;
            jsonResult.Value.Should().NotBeNull();
        }

        [Fact]
        public async Task GetActiveProjects_WithValidUser_ReturnsJsonWithActiveProjects()
        {
            // Arrange
            var projects = new List<Project>
            {
                new Project { Id = 1, Name = "Active Project", Status = ProjectStatus.Active },
                new Project { Id = 2, Name = "Planning Project", Status = ProjectStatus.Planning }
            };

            _mockUserManager.Setup(x => x.GetUserAsync(It.IsAny<System.Security.Claims.ClaimsPrincipal>()))
                .ReturnsAsync(_testUser);
            _mockProjectService.Setup(x => x.GetUserProjectsAsync(_testUser.Id))
                .ReturnsAsync(projects);

            // Act
            var result = await _controller.GetActiveProjects();

            // Assert
            result.Should().BeOfType<JsonResult>();
            var jsonResult = result as JsonResult;
            jsonResult.Value.Should().NotBeNull();
        }
    }
}
