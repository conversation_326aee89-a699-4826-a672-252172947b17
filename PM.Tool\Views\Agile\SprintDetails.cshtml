@model PM.Tool.Core.Entities.Agile.Sprint
@{
    ViewData["Title"] = $"Sprint: {Model.Name}";
    var project = ViewBag.Project as PM.Tool.Core.Entities.Project;
    var userStories = ViewBag.UserStories as IEnumerable<PM.Tool.Core.Entities.Agile.UserStory> ?? new List<PM.Tool.Core.Entities.Agile.UserStory>();
    var sprintMetrics = ViewBag.SprintMetrics as Dictionary<string, object> ?? new Dictionary<string, object>();
    var burndownData = ViewBag.BurndownData as IEnumerable<object> ?? new List<object>();
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="@Url.Action("Index", "Projects")" class="text-neutral-500 hover:text-neutral-700 dark:text-dark-400 dark:hover:text-dark-200">
                            <i class="fas fa-project-diagram mr-2"></i>Projects
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-neutral-400 dark:text-dark-500 mx-2"></i>
                            <a href="@Url.Action("Details", "Projects", new { id = project?.Id })" class="text-neutral-500 hover:text-neutral-700 dark:text-dark-400 dark:hover:text-dark-200">
                                @project?.Name
                            </a>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-neutral-400 dark:text-dark-500 mx-2"></i>
                            <a href="@Url.Action("Sprints", new { projectId = project?.Id })" class="text-neutral-500 hover:text-neutral-700 dark:text-dark-400 dark:hover:text-dark-200">
                                Sprints
                            </a>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-neutral-400 dark:text-dark-500 mx-2"></i>
                            <span class="text-neutral-700 dark:text-dark-300">@Model.SprintKey</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <div class="flex items-center space-x-4 mb-2">
                <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100">@Model.Name</h1>
                @{
                    var statusColor = Model.Status.ToString().ToLower() switch {
                        "planning" => "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300",
                        "active" => "bg-success-100 text-success-700 dark:bg-success-900 dark:text-success-300",
                        "completed" => "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300",
                        "cancelled" => "bg-danger-100 text-danger-700 dark:bg-danger-900 dark:text-danger-300",
                        _ => "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300"
                    };
                }
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @statusColor">
                    @Model.Status
                </span>
                @if (Model.IsActive)
                {
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-warning-100 text-warning-700 dark:bg-warning-900 dark:text-warning-300">
                        @Model.DaysRemaining days left
                    </span>
                }
            </div>

            <p class="text-sm text-neutral-500 dark:text-dark-400">
                Sprint @Model.SprintKey • @Model.StartDate.ToString("MMM dd") - @Model.EndDate.ToString("MMM dd, yyyy") (@Model.DurationDays days)
            </p>
        </div>

        <div class="mt-4 sm:mt-0 flex space-x-3">
            @if (Model.Status == PM.Tool.Core.Entities.Agile.SprintStatus.Planning)
            {
                <form asp-action="StartSprint" asp-route-id="@Model.Id" method="post" class="inline">
                    @Html.AntiForgeryToken()
                    @{
                        ViewData["Text"] = "Start Sprint";
                        ViewData["Variant"] = "success";
                        ViewData["Icon"] = "fas fa-play";
                        ViewData["Type"] = "submit";
                        ViewData["OnClick"] = "return confirm('Are you sure you want to start this sprint?')";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </form>
            }
            else if (Model.Status == PM.Tool.Core.Entities.Agile.SprintStatus.Active)
            {
                <form asp-action="CompleteSprint" asp-route-id="@Model.Id" method="post" class="inline">
                    @Html.AntiForgeryToken()
                    @{
                        ViewData["Text"] = "Complete Sprint";
                        ViewData["Variant"] = "primary";
                        ViewData["Icon"] = "fas fa-check";
                        ViewData["Type"] = "submit";
                        ViewData["OnClick"] = "return confirm('Are you sure you want to complete this sprint?')";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </form>
            }

            @{
                ViewData["Text"] = "Edit Sprint";
                ViewData["Variant"] = "outline";
                ViewData["Icon"] = "fas fa-edit";
                ViewData["Href"] = Url.Action("EditSprint", new { id = Model.Id });
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Sprint Overview Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <!-- Progress Card -->
    <div class="bg-white dark:bg-surface-dark border border-neutral-200 dark:border-dark-200 rounded-xl p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-sm font-medium text-neutral-500 dark:text-dark-400">Progress</h3>
            <i class="fas fa-chart-line text-primary-600 dark:text-primary-400"></i>
        </div>
        <div class="text-2xl font-bold text-neutral-900 dark:text-dark-100 mb-2">@Model.ProgressPercentage.ToString("F1")%</div>
        <div class="w-full bg-neutral-200 dark:bg-dark-600 rounded-full h-2">
            <div class="bg-primary-600 h-2 rounded-full transition-all duration-300" style="width: @Model.ProgressPercentage%"></div>
        </div>
    </div>

    <!-- Story Points Card -->
    <div class="bg-white dark:bg-surface-dark border border-neutral-200 dark:border-dark-200 rounded-xl p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-sm font-medium text-neutral-500 dark:text-dark-400">Story Points</h3>
            <i class="fas fa-tasks text-success-600 dark:text-success-400"></i>
        </div>
        <div class="text-2xl font-bold text-neutral-900 dark:text-dark-100">@Model.CompletedStoryPoints/@Model.PlannedStoryPoints</div>
        <p class="text-sm text-neutral-500 dark:text-dark-400">Completed/Planned</p>
    </div>

    <!-- User Stories Card -->
    <div class="bg-white dark:bg-surface-dark border border-neutral-200 dark:border-dark-200 rounded-xl p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-sm font-medium text-neutral-500 dark:text-dark-400">User Stories</h3>
            <i class="fas fa-list text-warning-600 dark:text-warning-400"></i>
        </div>
        <div class="text-2xl font-bold text-neutral-900 dark:text-dark-100">@Model.CompletedUserStoryCount/@Model.UserStoryCount</div>
        <p class="text-sm text-neutral-500 dark:text-dark-400">Completed/Total</p>
    </div>

    <!-- Velocity Card -->
    <div class="bg-white dark:bg-surface-dark border border-neutral-200 dark:border-dark-200 rounded-xl p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-sm font-medium text-neutral-500 dark:text-dark-400">Velocity</h3>
            <i class="fas fa-tachometer-alt text-info-600 dark:text-info-400"></i>
        </div>
        <div class="text-2xl font-bold text-neutral-900 dark:text-dark-100">@Model.Velocity.ToString("F1")</div>
        <p class="text-sm text-neutral-500 dark:text-dark-400">Story Points</p>
    </div>
</div>

<!-- Sprint Content -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-8">
        <!-- Sprint Goal -->
        @if (!string.IsNullOrEmpty(Model.Goal))
        {
            ViewData["Title"] = "Sprint Goal";
            ViewData["Icon"] = "fas fa-bullseye";
            <partial name="Components/_Card" view-data="ViewData">
                <div class="bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-lg p-6">
                    <div class="prose dark:prose-invert max-w-none">
                        <p class="text-lg text-primary-800 dark:text-primary-200 font-medium leading-relaxed">
                            @Model.Goal
                        </p>
                    </div>
                </div>
            </partial>
        }

        <!-- Description -->
        @if (!string.IsNullOrEmpty(Model.Description))
        {
            ViewData["Title"] = "Description";
            ViewData["Icon"] = "fas fa-align-left";
            <partial name="Components/_Card" view-data="ViewData">
                <div class="prose dark:prose-invert max-w-none">
                    <p class="text-neutral-700 dark:text-dark-300 leading-relaxed">@Model.Description</p>
                </div>
            </partial>
        }

        <!-- Burndown Chart -->
        @{
            ViewData["Title"] = "Burndown Chart";
            ViewData["Icon"] = "fas fa-chart-line";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="bg-neutral-50 dark:bg-dark-700 rounded-lg p-6">
                <canvas id="burndownChart" class="w-full h-80"></canvas>
            </div>
        </partial>

        <!-- User Stories -->
        @{
            ViewData["Title"] = $"Sprint Backlog ({userStories.Count()})";
            ViewData["Icon"] = "fas fa-list";
        }
        <partial name="Components/_Card" view-data="ViewData">
            @if (userStories.Any())
            {
                <div class="space-y-4">
                    @foreach (var story in userStories.OrderBy(us => us.Priority))
                    {
                        <div class="border border-neutral-200 dark:border-dark-200 rounded-lg p-4 hover:shadow-sm transition-shadow">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3 mb-2">
                                        <h4 class="font-medium text-neutral-900 dark:text-dark-100">@story.Title</h4>
                                        @{
                                            var storyStatusColor = story.Status.ToString().ToLower() switch {
                                                "backlog" => "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300",
                                                "ready" => "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300",
                                                "inprogress" => "bg-warning-100 text-warning-700 dark:bg-warning-900 dark:text-warning-300",
                                                "review" => "bg-info-100 text-info-700 dark:bg-info-900 dark:text-info-300",
                                                "done" => "bg-success-100 text-success-700 dark:bg-success-900 dark:text-success-300",
                                                _ => "bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300"
                                            };
                                        }
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium @storyStatusColor">
                                            @story.Status
                                        </span>
                                        @if (story.StoryPoints > 0)
                                        {
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300">
                                                @story.StoryPoints SP
                                            </span>
                                        }
                                    </div>
                                    <p class="text-sm text-neutral-600 dark:text-dark-400 mb-2">@story.UserStoryFormat</p>
                                    @if (story.AssignedTo != null)
                                    {
                                        <p class="text-xs text-neutral-500 dark:text-dark-400">
                                            Assigned to: @story.AssignedTo.UserName
                                        </p>
                                    }
                                </div>
                                <div class="flex space-x-2">
                                    @{
                                        ViewData["Text"] = "";
                                        ViewData["Variant"] = "outline";
                                        ViewData["Size"] = "sm";
                                        ViewData["Icon"] = "fas fa-eye";
                                        ViewData["Href"] = Url.Action("UserStoryDetails", new { id = story.Id });
                                        ViewData["AriaLabel"] = "View User Story";
                                        ViewData["Title"] = "View Details";
                                    }
                                    <partial name="Components/_Button" view-data="ViewData" />
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-list text-2xl text-neutral-400 dark:text-dark-500"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">No User Stories</h3>
                    <p class="text-neutral-500 dark:text-dark-400 mb-6">This sprint doesn't have any user stories yet.</p>
                    @{
                        ViewData["Text"] = "Add User Stories";
                        ViewData["Variant"] = "primary";
                        ViewData["Icon"] = "fas fa-plus";
                        ViewData["Href"] = Url.Action("Backlog", new { projectId = Model.ProjectId });
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </div>
            }
        </partial>
    </div>

    <!-- Sidebar -->
    <div class="space-y-6">
        <!-- Sprint Information -->
        @{
            ViewData["Title"] = "Sprint Information";
            ViewData["Icon"] = "fas fa-info-circle";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <dl class="space-y-4">
                <div>
                    <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">Sprint Key</dt>
                    <dd class="text-sm text-neutral-900 dark:text-dark-100 font-mono">@Model.SprintKey</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">Status</dt>
                    <dd class="text-sm text-neutral-900 dark:text-dark-100">@Model.Status</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">Duration</dt>
                    <dd class="text-sm text-neutral-900 dark:text-dark-100">@Model.DurationDays days</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">Start Date</dt>
                    <dd class="text-sm text-neutral-900 dark:text-dark-100">@Model.StartDate.ToString("MMM dd, yyyy")</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">End Date</dt>
                    <dd class="text-sm text-neutral-900 dark:text-dark-100">@Model.EndDate.ToString("MMM dd, yyyy")</dd>
                </div>
                @if (Model.ScrumMaster != null)
                {
                    <div>
                        <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">Scrum Master</dt>
                        <dd class="text-sm text-neutral-900 dark:text-dark-100">@Model.ScrumMaster.UserName</dd>
                    </div>
                }
                <div>
                    <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">Created</dt>
                    <dd class="text-sm text-neutral-900 dark:text-dark-100">@Model.CreatedAt.ToString("MMM dd, yyyy")</dd>
                </div>
                @if (Model.UpdatedAt.HasValue)
                {
                    <div>
                        <dt class="text-sm font-medium text-neutral-500 dark:text-dark-400">Last Updated</dt>
                        <dd class="text-sm text-neutral-900 dark:text-dark-100">@Model.UpdatedAt.Value.ToString("MMM dd, yyyy")</dd>
                    </div>
                }
            </dl>
        </partial>

        <!-- Actions -->
        @{
            ViewData["Title"] = "Actions";
            ViewData["Icon"] = "fas fa-cog";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="space-y-3">
                @{
                    ViewData["Text"] = "Edit Sprint";
                    ViewData["Variant"] = "outline";
                    ViewData["Icon"] = "fas fa-edit";
                    ViewData["Href"] = Url.Action("EditSprint", new { id = Model.Id });
                    ViewData["Classes"] = "w-full justify-center";
                }
                <partial name="Components/_Button" view-data="ViewData" />

                @{
                    ViewData["Text"] = "View Kanban";
                    ViewData["Variant"] = "primary";
                    ViewData["Icon"] = "fas fa-columns";
                    ViewData["Href"] = Url.Action("Kanban", new { projectId = Model.ProjectId });
                    ViewData["Classes"] = "w-full justify-center";
                }
                <partial name="Components/_Button" view-data="ViewData" />

                <form asp-action="DeleteSprint" asp-route-id="@Model.Id" method="post" class="delete-form">
                    @Html.AntiForgeryToken()
                    @{
                        ViewData["Text"] = "Delete Sprint";
                        ViewData["Variant"] = "danger";
                        ViewData["Icon"] = "fas fa-trash";
                        ViewData["Type"] = "submit";
                        ViewData["Classes"] = "w-full justify-center";
                        ViewData["OnClick"] = "return confirm('Are you sure you want to delete this sprint? This action cannot be undone.')";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </form>
            </div>
        </partial>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadBurndownChart();
        });

        async function loadBurndownChart() {
            try {
                const response = await fetch('@Url.Action("GetBurndownData", new { sprintId = Model.Id })');
                const data = await response.json();

                if (data.error) {
                    console.error('Error loading burndown data:', data.error);
                    return;
                }

                renderBurndownChart(data);
            } catch (error) {
                console.error('Error loading burndown data:', error);
            }
        }

        function renderBurndownChart(data) {
            const ctx = document.getElementById('burndownChart').getContext('2d');

            new Chart(ctx, {
                type: 'line',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Sprint Burndown Chart',
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        },
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Story Points'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Sprint Days'
                            }
                        }
                    }
                }
            });
        }
    </script>
}
