using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Models.ViewModels
{
    /// <summary>
    /// Base class for all ViewModels to ensure consistency and reusability
    /// </summary>
    public abstract class BaseViewModel
    {
        public int Id { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public bool IsDeleted { get; set; }
    }

    /// <summary>
    /// Base class for Create ViewModels
    /// </summary>
    public abstract class BaseCreateViewModel
    {
        // Common properties that all create forms might need
        // Can be extended as needed
    }

    /// <summary>
    /// Base class for Edit ViewModels
    /// </summary>
    public abstract class BaseEditViewModel : BaseCreateViewModel
    {
        public int Id { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// Interface for ViewModels that can convert to/from entities
    /// </summary>
    /// <typeparam name="TEntity">The entity type</typeparam>
    public interface IEntityViewModel<TEntity> where TEntity : class
    {
        TEntity ToEntity();
        void UpdateEntity(TEntity entity);
    }

    /// <summary>
    /// Interface for ViewModels that can be created from entities
    /// </summary>
    /// <typeparam name="TEntity">The entity type</typeparam>
    /// <typeparam name="TViewModel">The ViewModel type</typeparam>
    public interface IFromEntity<TEntity, TViewModel>
        where TEntity : class
        where TViewModel : class
    {
        static abstract TViewModel FromEntity(TEntity entity);
    }
}
