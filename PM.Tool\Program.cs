using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authorization;
using Scrutor;
using PM.Tool.Application.Services;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;
using PM.Tool.Infrastructure.Repositories;
using PM.Tool.Core.Extensions;
using Serilog;
using FluentValidation.AspNetCore;
using FluentValidation;
using PM.Tool.Core.Authorization;
using PM.Tool.Core.Services;
using PM.Tool.Infrastructure.Filters;
using PM.Tool.Infrastructure.Middleware;
using PM.Tool.Infrastructure.Swagger;
using PM.Tool.Core.Localization;
using PM.Tool.Infrastructure.Localization;
using Microsoft.AspNetCore.Localization;
using Microsoft.Extensions.Localization;
using System.Globalization;
using PM.Tool.Resources;

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .WriteTo.File("logs/log-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

var builder = WebApplication.CreateBuilder(args);

// Add Serilog
builder.Host.UseSerilog();

// Add services to the container.
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection")
    ?? throw new InvalidOperationException("Connection string 'DefaultConnection' not found.");

// Configure PostgreSQL and add DbContext first
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseNpgsql(connectionString));
builder.Services.AddDatabaseDeveloperPageExceptionFilter();

// Configure infrastructure services
builder.Services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
builder.Services.AddMemoryCache();
builder.Services.AddSingleton<ICacheService, MemoryCacheService>();

// Register repositories
builder.Services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
builder.Services.AddScoped<IProjectRepository, ProjectRepository>();
builder.Services.AddScoped<ITaskRepository, TaskRepository>();

// Register core services
builder.Services.AddScoped<IAuditService, AuditService>();  // Changed from AddTransient to AddScoped
builder.Services.AddScoped<INotificationService, NotificationService>();  // Changed for consistency

// Register application services
builder.Services.AddScoped<IProjectService, ProjectService>();
builder.Services.AddScoped<ITaskService, TaskService>();
builder.Services.AddScoped<IReportingService, ReportingService>();
builder.Services.AddScoped<ITimeTrackingService, TimeTrackingService>();
builder.Services.AddScoped<IDocumentService, DocumentService>();

// Register enhanced feature services
builder.Services.AddScoped<IWbsService, WbsService>();
builder.Services.AddScoped<IResourceService, ResourceService>();
builder.Services.AddScoped<IRiskService, RiskService>();
builder.Services.AddScoped<IAnalyticsService, AnalyticsService>();

// Register meeting and requirements services
builder.Services.AddScoped<IMeetingService, MeetingService>();
builder.Services.AddScoped<IRequirementService, RequirementService>();
builder.Services.AddScoped<IStakeholderService, StakeholderService>();

// Register agile services
builder.Services.AddScoped<IAgileService, AgileService>();

// Register unified people management service
builder.Services.AddScoped<IPersonService, PersonService>();

// Register form helper service
builder.Services.AddScoped<PM.Tool.Services.IFormHelperService, PM.Tool.Services.FormHelperService>();

// Register user lookup service
builder.Services.AddScoped<PM.Tool.Services.IUserLookupService, PM.Tool.Services.UserLookupService>();

// Register debug localization service
builder.Services.AddScoped<PM.Tool.Services.DebugLocalizationService>();

// Register localization services
builder.Services.AddLocalization(options =>
{
    options.ResourcesPath = "";
});

// Add MVC localization services
builder.Services.AddControllersWithViews()
    .AddViewLocalization(Microsoft.AspNetCore.Mvc.Razor.LanguageViewLocationExpanderFormat.Suffix)
    .AddDataAnnotationsLocalization(options =>
    {
        options.DataAnnotationLocalizerProvider = (type, factory) =>
            factory.Create(typeof(PM.Tool.Resources.SharedResources));
    });

// Register custom localization services
builder.Services.AddScoped<ILocalizationService, LocalizationService>();
builder.Services.AddScoped<PM.Tool.Services.ISimpleLocalizationService, PM.Tool.Services.SimpleLocalizationService>();

// Register documentation service
builder.Services.AddScoped<IDocumentationService, PM.Tool.Infrastructure.Services.DocumentationService>();

// Register background services
builder.Services.AddHostedService<DeadlineNotificationBackgroundService>();

// Configure Identity with custom user
builder.Services.AddDefaultIdentity<ApplicationUser>(options =>
{
    options.SignIn.RequireConfirmedAccount = false; // Set to true in production
    options.Password.RequireDigit = true;
    options.Password.RequireLowercase = true;
    options.Password.RequireUppercase = true;
    options.Password.RequireNonAlphanumeric = false;
    options.Password.RequiredLength = 6;
    options.User.RequireUniqueEmail = true;
})
.AddRoles<IdentityRole>()
.AddEntityFrameworkStores<ApplicationDbContext>()
.AddDefaultTokenProviders()
.AddDefaultUI();

// Configure Authorization Policies
builder.Services.AddAuthorization(options =>
{
    Policies.ConfigurePolicies(options);
});

// Register Authorization Handlers
builder.Services.AddScoped<IAuthorizationHandler, ProjectAuthorizationHandler>();
builder.Services.AddScoped<IAuthorizationHandler, TaskAuthorizationHandler>();

// Add FluentValidation
builder.Services.AddFluentValidationAutoValidation();
builder.Services.AddValidatorsFromAssemblyContaining<Program>();

// Add AutoMapper
builder.Services.AddAutoMapper(typeof(Program).Assembly);

// Add MediatR
builder.Services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(Program).Assembly));

// Services are already registered above

// Configure localization with proper culture setup
var supportedCultures = new[]
{
    // Primary languages
    new CultureInfo("en-US"),
    new CultureInfo("es-ES"),
    new CultureInfo("en"),
    new CultureInfo("es"),

    // European languages
    new CultureInfo("fr-FR"),
    new CultureInfo("de-DE"),
    new CultureInfo("it-IT"),
    new CultureInfo("pt-BR"),
    new CultureInfo("pt-PT"),
    new CultureInfo("nl-NL"),
    new CultureInfo("sv-SE"),
    new CultureInfo("da-DK"),
    new CultureInfo("no-NO"),
    new CultureInfo("fi-FI"),

    // Eastern European
    new CultureInfo("ru-RU"),
    new CultureInfo("pl-PL"),
    new CultureInfo("tr-TR"),

    // Asian languages
    new CultureInfo("zh-CN"),
    new CultureInfo("zh-TW"),
    new CultureInfo("ja-JP"),
    new CultureInfo("ko-KR"),
    new CultureInfo("hi-IN"),
    new CultureInfo("th-TH"),
    new CultureInfo("vi-VN"),
    new CultureInfo("bn-BD"),

    // Middle Eastern (RTL)
    new CultureInfo("ar-SA"),
    new CultureInfo("he-IL"),

    // Regional variants
    new CultureInfo("en-GB"),
    new CultureInfo("es-MX")
};

builder.Services.Configure<RequestLocalizationOptions>(options =>
{
    options.DefaultRequestCulture = new RequestCulture("en-US", "en-US");
    options.SupportedCultures = supportedCultures;
    options.SupportedUICultures = supportedCultures;

    // Configure culture providers in order of priority
    options.RequestCultureProviders.Clear();
    options.RequestCultureProviders.Add(new CookieRequestCultureProvider());
    options.RequestCultureProviders.Add(new QueryStringRequestCultureProvider());
    options.RequestCultureProviders.Add(new AcceptLanguageHeaderRequestCultureProvider());

    // Configure fallback behavior
    options.FallBackToParentCultures = true;
    options.FallBackToParentUICultures = true;
});

// Add global exception filter to existing controllers configuration
builder.Services.Configure<Microsoft.AspNetCore.Mvc.MvcOptions>(options =>
{
    options.Filters.Add<GlobalExceptionFilter>();
});

// Add API Versioning
builder.Services.AddApiVersioning(options =>
{
    options.DefaultApiVersion = new Asp.Versioning.ApiVersion(1, 0);
    options.AssumeDefaultVersionWhenUnspecified = true;
    options.ApiVersionReader = Asp.Versioning.ApiVersionReader.Combine(
        new Asp.Versioning.QueryStringApiVersionReader("version"),
        new Asp.Versioning.HeaderApiVersionReader("X-Version"),
        new Asp.Versioning.UrlSegmentApiVersionReader()
    );
}).AddApiExplorer(setup =>
{
    setup.GroupNameFormat = "'v'VVV";
    setup.SubstituteApiVersionInUrl = true;
});

// Add Swagger for API documentation
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
    {
        Title = "PM.Tool API",
        Version = "v1",
        Description = "A comprehensive project management API for PM.Tool",
        Contact = new Microsoft.OpenApi.Models.OpenApiContact
        {
            Name = "PM.Tool Support",
            Email = "<EMAIL>",
            Url = new Uri("https://pmtool.com/support")
        },
        License = new Microsoft.OpenApi.Models.OpenApiLicense
        {
            Name = "MIT License",
            Url = new Uri("https://opensource.org/licenses/MIT")
        }
    });

    // Add JWT Authentication to Swagger
    c.AddSecurityDefinition("Bearer", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new Microsoft.OpenApi.Models.OpenApiSecurityRequirement
    {
        {
            new Microsoft.OpenApi.Models.OpenApiSecurityScheme
            {
                Reference = new Microsoft.OpenApi.Models.OpenApiReference
                {
                    Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });

    // Include XML comments for better documentation
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }

    // Add enum descriptions
    c.SchemaFilter<EnumSchemaFilter>();

    // Group by controller tags
    c.TagActionsBy(api => new[] { api.GroupName ?? api.ActionDescriptor.RouteValues["controller"] });
    c.DocInclusionPredicate((name, api) => true);
});

// Configure cookie settings
builder.Services.ConfigureApplicationCookie(options =>
{
    options.LoginPath = "/Identity/Account/Login";
    options.LogoutPath = "/Identity/Account/Logout";
    options.AccessDeniedPath = "/Identity/Account/AccessDenied";
    options.ExpireTimeSpan = TimeSpan.FromDays(30);
    options.SlidingExpiration = true;
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseMigrationsEndPoint();
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "PM.Tool API v1");
        c.RoutePrefix = "api/docs";
        c.DocumentTitle = "PM.Tool API Documentation";
        c.DefaultModelsExpandDepth(-1);
        c.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.None);
        c.EnableDeepLinking();
        c.EnableFilter();
        c.ShowExtensions();
        c.EnableValidator();
    });
}
else
{
    // Enable Swagger in production for API documentation
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "PM.Tool API v1");
        c.RoutePrefix = "api/docs";
        c.DocumentTitle = "PM.Tool API Documentation";
    });

    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

// Use request localization
app.UseRequestLocalization();

app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();

// Seed the database
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    try
    {
        var userManager = services.GetRequiredService<UserManager<ApplicationUser>>();
        var roleManager = services.GetRequiredService<RoleManager<IdentityRole>>();
    }
    catch (Exception ex)
    {
        var logger = services.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "An error occurred while seeding the database.");
    }
}

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");
app.MapRazorPages();

app.Run();

// Make Program class accessible for testing
public partial class Program { }