using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using PM.Tool.Data;
using PM.Tool.Core.Entities;

namespace PM.Tool.Tests.Helpers
{
    public static class TestDbContextFactory
    {
        public static ApplicationDbContext CreateInMemoryContext(string databaseName = "")
        {
            if (string.IsNullOrEmpty(databaseName))
            {
                databaseName = Guid.NewGuid().ToString();
            }

            var options = new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseInMemoryDatabase(databaseName)
                .Options;

            var context = new ApplicationDbContext(options);

            // Ensure database is created
            context.Database.EnsureCreated();

            return context;
        }

        public static async Task<ApplicationDbContext> CreateContextWithSeedDataAsync(string databaseName = "")
        {
            var context = CreateInMemoryContext(databaseName);
            await SeedTestDataAsync(context);
            return context;
        }

        public static async Task SeedTestDataAsync(ApplicationDbContext context)
        {
            // Seed test users
            var testUser = new ApplicationUser
            {
                Id = "test-user-1",
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Test",
                LastName = "User",
                EmailConfirmed = true
            };

            var adminUser = new ApplicationUser
            {
                Id = "admin-user-1",
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                FirstName = "Admin",
                LastName = "User",
                EmailConfirmed = true
            };

            context.Users.AddRange(testUser, adminUser);

            // Seed test projects
            var testProject = new Project
            {
                Id = 1,
                Name = "Test Project",
                Description = "A test project for unit testing",
                StartDate = DateTime.UtcNow.AddDays(-30),
                EndDate = DateTime.UtcNow.AddDays(30),
                Status = PM.Tool.Core.Enums.ProjectStatus.Active,
                CreatedByUserId = testUser.Id,
                ManagerId = testUser.Id,
                CreatedAt = DateTime.UtcNow.AddDays(-30)
            };

            context.Projects.Add(testProject);

            // Seed test tasks
            var testTask = new TaskEntity
            {
                Id = 1,
                Title = "Test Task",
                Description = "A test task for unit testing",
                ProjectId = testProject.Id,
                AssignedToUserId = testUser.Id,
                Status = PM.Tool.Core.Enums.TaskStatus.ToDo,
                Priority = PM.Tool.Core.Enums.TaskPriority.Medium,
                CreatedByUserId = testUser.Id,
                CreatedAt = DateTime.UtcNow.AddDays(-10)
            };

            context.Tasks.Add(testTask);

            await context.SaveChangesAsync();
        }
    }
}
