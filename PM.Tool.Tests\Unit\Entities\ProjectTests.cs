using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using Xunit;
using FluentAssertions;

namespace PM.Tool.Tests.Unit.Entities
{
    public class ProjectTests
    {
        [Fact]
        public void Project_DefaultValues_ShouldBeSetCorrectly()
        {
            // Act
            var project = new Project();

            // Assert
            project.Status.Should().Be(ProjectStatus.Planning);
            project.Budget.Should().Be(0);
            project.IsDeleted.Should().BeFalse();
            project.Members.Should().NotBeNull();
            project.Tasks.Should().NotBeNull();
            project.Milestones.Should().NotBeNull();
            project.Attachments.Should().NotBeNull();
        }

        [Fact]
        public void Project_WithValidData_ShouldSetPropertiesCorrectly()
        {
            // Arrange
            var startDate = DateTime.UtcNow;
            var endDate = DateTime.UtcNow.AddDays(30);
            var budget = 50000m;

            // Act
            var project = new Project
            {
                Name = "Test Project",
                Description = "Test Description",
                StartDate = startDate,
                EndDate = endDate,
                Budget = budget,
                ClientName = "Test Client",
                Status = ProjectStatus.Active
            };

            // Assert
            project.Name.Should().Be("Test Project");
            project.Description.Should().Be("Test Description");
            project.StartDate.Should().Be(startDate);
            project.EndDate.Should().Be(endDate);
            project.Budget.Should().Be(budget);
            project.ClientName.Should().Be("Test Client");
            project.Status.Should().Be(ProjectStatus.Active);
        }

        [Fact]
        public void TotalTasks_WithNoTasks_ShouldReturnZero()
        {
            // Arrange
            var project = new Project();

            // Act
            var totalTasks = project.TotalTasks;

            // Assert
            totalTasks.Should().Be(0);
        }

        [Fact]
        public void TotalTasks_WithTasks_ShouldReturnCorrectCount()
        {
            // Arrange
            var project = new Project
            {
                Tasks = new List<TaskEntity>
                {
                    new TaskEntity { Id = 1, Title = "Task 1", IsDeleted = false },
                    new TaskEntity { Id = 2, Title = "Task 2", IsDeleted = false },
                    new TaskEntity { Id = 3, Title = "Task 3", IsDeleted = true } // Deleted task should not be counted
                }
            };

            // Act
            var totalTasks = project.TotalTasks;

            // Assert
            totalTasks.Should().Be(2);
        }

        [Fact]
        public void CompletedTasks_WithNoCompletedTasks_ShouldReturnZero()
        {
            // Arrange
            var project = new Project
            {
                Tasks = new List<TaskEntity>
                {
                    new TaskEntity { Id = 1, Title = "Task 1", Status = PM.Tool.Core.Enums.TaskStatus.ToDo, IsDeleted = false },
                    new TaskEntity { Id = 2, Title = "Task 2", Status = PM.Tool.Core.Enums.TaskStatus.InProgress, IsDeleted = false }
                }
            };

            // Act
            var completedTasks = project.CompletedTasks;

            // Assert
            completedTasks.Should().Be(0);
        }

        [Fact]
        public void CompletedTasks_WithCompletedTasks_ShouldReturnCorrectCount()
        {
            // Arrange
            var project = new Project
            {
                Tasks = new List<TaskEntity>
                {
                    new TaskEntity { Id = 1, Title = "Task 1", Status = PM.Tool.Core.Enums.TaskStatus.Done, IsDeleted = false },
                    new TaskEntity { Id = 2, Title = "Task 2", Status = PM.Tool.Core.Enums.TaskStatus.Done, IsDeleted = false },
                    new TaskEntity { Id = 3, Title = "Task 3", Status = PM.Tool.Core.Enums.TaskStatus.InProgress, IsDeleted = false },
                    new TaskEntity { Id = 4, Title = "Task 4", Status = PM.Tool.Core.Enums.TaskStatus.Done, IsDeleted = true } // Deleted completed task should not be counted
                }
            };

            // Act
            var completedTasks = project.CompletedTasks;

            // Assert
            completedTasks.Should().Be(2);
        }

        [Fact]
        public void ProgressPercentage_WithNoTasks_ShouldReturnZero()
        {
            // Arrange
            var project = new Project();

            // Act
            var progressPercentage = project.ProgressPercentage;

            // Assert
            progressPercentage.Should().Be(0);
        }

        [Fact]
        public void ProgressPercentage_WithTasks_ShouldCalculateCorrectly()
        {
            // Arrange
            var project = new Project
            {
                Tasks = new List<TaskEntity>
                {
                    new TaskEntity { Id = 1, Title = "Task 1", Status = PM.Tool.Core.Enums.TaskStatus.Done, IsDeleted = false },
                    new TaskEntity { Id = 2, Title = "Task 2", Status = PM.Tool.Core.Enums.TaskStatus.Done, IsDeleted = false },
                    new TaskEntity { Id = 3, Title = "Task 3", Status = PM.Tool.Core.Enums.TaskStatus.InProgress, IsDeleted = false },
                    new TaskEntity { Id = 4, Title = "Task 4", Status = PM.Tool.Core.Enums.TaskStatus.ToDo, IsDeleted = false }
                }
            };

            // Act
            var progressPercentage = project.ProgressPercentage;

            // Assert
            progressPercentage.Should().Be(50); // 2 completed out of 4 total = 50%
        }

        [Fact]
        public void IsOverdue_WithNoEndDate_ShouldReturnFalse()
        {
            // Arrange
            var project = new Project
            {
                EndDate = null,
                Status = ProjectStatus.Active
            };

            // Act
            var isOverdue = project.IsOverdue;

            // Assert
            isOverdue.Should().BeFalse();
        }

        [Fact]
        public void IsOverdue_WithFutureEndDate_ShouldReturnFalse()
        {
            // Arrange
            var project = new Project
            {
                EndDate = DateTime.UtcNow.AddDays(10),
                Status = ProjectStatus.Active
            };

            // Act
            var isOverdue = project.IsOverdue;

            // Assert
            isOverdue.Should().BeFalse();
        }

        [Fact]
        public void IsOverdue_WithPastEndDateAndActiveStatus_ShouldReturnTrue()
        {
            // Arrange
            var project = new Project
            {
                EndDate = DateTime.UtcNow.AddDays(-5),
                Status = ProjectStatus.Active
            };

            // Act
            var isOverdue = project.IsOverdue;

            // Assert
            isOverdue.Should().BeTrue();
        }

        [Fact]
        public void IsOverdue_WithPastEndDateAndCompletedStatus_ShouldReturnFalse()
        {
            // Arrange
            var project = new Project
            {
                EndDate = DateTime.UtcNow.AddDays(-5),
                Status = ProjectStatus.Completed
            };

            // Act
            var isOverdue = project.IsOverdue;

            // Assert
            isOverdue.Should().BeFalse();
        }

        [Fact]
        public void IsCompleted_WithCompletedStatus_ShouldReturnTrue()
        {
            // Arrange
            var project = new Project
            {
                Status = ProjectStatus.Completed,
                IsCompleted = true
            };

            // Act & Assert
            project.IsCompleted.Should().BeTrue();
        }

        [Fact]
        public void IsCompleted_WithActiveStatus_ShouldReturnFalse()
        {
            // Arrange
            var project = new Project
            {
                Status = ProjectStatus.Active,
                IsCompleted = false
            };

            // Act & Assert
            project.IsCompleted.Should().BeFalse();
        }
    }
}
