using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities
{
    /// <summary>
    /// Master entity for all people in the system - employees, contractors, stakeholders, etc.
    /// Provides unified management of all person-related information.
    /// </summary>
    public class Person : BaseEntity
    {
        // Core Identity
        [Required]
        [MaxLength(20)]
        public string PersonCode { get; set; } = string.Empty; // Unique identifier like EMP001, STK001

        [Required]
        [MaxLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string LastName { get; set; } = string.Empty;

        [Required]
        [MaxLength(255)]
        public string Email { get; set; } = string.Empty;

        [MaxLength(20)]
        public string? Phone { get; set; }

        // Classification
        public PersonType Type { get; set; } = PersonType.Internal;
        public PersonStatus Status { get; set; } = PersonStatus.Active;

        // System Integration
        public string? UserId { get; set; } // Link to ApplicationUser for system access
        public bool HasSystemAccess { get; set; } = false;

        // Organization Information
        [MaxLength(200)]
        public string? Organization { get; set; }

        [MaxLength(100)]
        public string? Department { get; set; }

        [MaxLength(100)]
        public string? Title { get; set; }

        [MaxLength(100)]
        public string? Location { get; set; }

        [MaxLength(50)]
        public string? EmployeeId { get; set; } // For internal employees

        // Contact & Preferences
        public string? ProfilePictureUrl { get; set; }

        [MaxLength(1000)]
        public string? Bio { get; set; }

        [MaxLength(500)]
        public string? CommunicationPreferences { get; set; }

        [MaxLength(100)]
        public string? WorkingHours { get; set; } // e.g., "9:00-17:00"

        [MaxLength(50)]
        public string? TimeZone { get; set; } // e.g., "UTC+06:00"

        // Additional Information
        [MaxLength(1000)]
        public string? Notes { get; set; }

        private DateTime? _hireDate;
        public DateTime? HireDate
        {
            get => _hireDate;
            set => _hireDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        private DateTime? _terminationDate;
        public DateTime? TerminationDate
        {
            get => _terminationDate;
            set => _terminationDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        // Navigation Properties
        public virtual ApplicationUser? User { get; set; }
        public virtual ICollection<PersonRole> Roles { get; set; } = new List<PersonRole>();
        public virtual ICollection<PersonSkill> Skills { get; set; } = new List<PersonSkill>();
        public virtual ICollection<PersonProject> ProjectAssignments { get; set; } = new List<PersonProject>();
        public virtual ICollection<PersonAvailability> Availability { get; set; } = new List<PersonAvailability>();
        public virtual PersonStakeholder? StakeholderInfo { get; set; }
        public virtual PersonResource? ResourceInfo { get; set; }
        public virtual ICollection<PersonContact> Contacts { get; set; } = new List<PersonContact>();

        // Computed Properties
        public string FullName => $"{FirstName} {LastName}";
        public string DisplayName => !string.IsNullOrEmpty(Title) ? $"{FullName} ({Title})" : FullName;
        public string FullContact => $"{FullName} ({Email})";
        public bool IsEmployee => Type == PersonType.Internal && !string.IsNullOrEmpty(EmployeeId);
        public bool IsActive => Status == PersonStatus.Active;
        public bool IsSystemUser => HasSystemAccess && !string.IsNullOrEmpty(UserId);
    }

    /// <summary>
    /// Unified role system supporting system, organizational, and project-specific roles
    /// </summary>
    public class PersonRole : BaseEntity
    {
        public int PersonId { get; set; }

        [Required]
        [MaxLength(50)]
        public string RoleCode { get; set; } = string.Empty; // ADMIN, PM, DEV, STAKEHOLDER, etc.

        [Required]
        [MaxLength(100)]
        public string RoleName { get; set; } = string.Empty;

        [MaxLength(200)]
        public string? RoleDescription { get; set; }

        public RoleScope Scope { get; set; } = RoleScope.System;

        [MaxLength(50)]
        public string? ScopeId { get; set; } // ProjectId, DepartmentId, etc.

        [MaxLength(100)]
        public string? ScopeName { get; set; } // Project name, Department name, etc.

        private DateTime _effectiveFrom = DateTime.UtcNow;
        public DateTime EffectiveFrom
        {
            get => _effectiveFrom;
            set => _effectiveFrom = DateTime.SpecifyKind(value, DateTimeKind.Utc);
        }

        private DateTime? _effectiveTo;
        public DateTime? EffectiveTo
        {
            get => _effectiveTo;
            set => _effectiveTo = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        public bool IsActive { get; set; } = true;

        [MaxLength(500)]
        public string? Notes { get; set; }

        public string? AssignedByUserId { get; set; }

        // Navigation Properties
        public virtual Person Person { get; set; } = null!;
        public virtual ApplicationUser? AssignedBy { get; set; }

        // Computed Properties
        public bool IsCurrentlyActive => IsActive &&
            DateTime.UtcNow >= EffectiveFrom &&
            (EffectiveTo == null || DateTime.UtcNow <= EffectiveTo);

        public string ScopeDisplay => Scope switch
        {
            RoleScope.System => "System-wide",
            RoleScope.Organization => $"Organization: {ScopeName ?? ScopeId}",
            RoleScope.Project => $"Project: {ScopeName ?? ScopeId}",
            RoleScope.Department => $"Department: {ScopeName ?? ScopeId}",
            _ => "Unknown Scope"
        };
    }

    /// <summary>
    /// Skills and competencies management for people
    /// </summary>
    public class PersonSkill : BaseEntity
    {
        public int PersonId { get; set; }
        public int SkillId { get; set; }

        public SkillLevel Level { get; set; } = SkillLevel.Intermediate;
        public int YearsOfExperience { get; set; }

        private DateTime? _certificationDate;
        public DateTime? CertificationDate
        {
            get => _certificationDate;
            set => _certificationDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        private DateTime? _certificationExpiry;
        public DateTime? CertificationExpiry
        {
            get => _certificationExpiry;
            set => _certificationExpiry = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        [MaxLength(100)]
        public string? CertificationBody { get; set; }

        [MaxLength(100)]
        public string? CertificationNumber { get; set; }

        public bool IsVerified { get; set; } = false;

        [MaxLength(500)]
        public string? Notes { get; set; }

        public string? VerifiedByUserId { get; set; }

        private DateTime? _verificationDate;
        public DateTime? VerificationDate
        {
            get => _verificationDate;
            set => _verificationDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        // Navigation Properties
        public virtual Person Person { get; set; } = null!;
        public virtual Skill Skill { get; set; } = null!;
        public virtual ApplicationUser? VerifiedBy { get; set; }

        // Computed Properties
        public bool IsCertified => CertificationDate.HasValue;
        public bool IsCertificationValid => IsCertified &&
            (CertificationExpiry == null || DateTime.UtcNow <= CertificationExpiry);
        public int SkillScore => (int)Level * 20 + Math.Min(YearsOfExperience * 5, 20); // Max 100
    }

    /// <summary>
    /// Project assignments and roles for people
    /// </summary>
    public class PersonProject : BaseEntity
    {
        public int PersonId { get; set; }
        public int ProjectId { get; set; }

        [Required]
        [MaxLength(100)]
        public string ProjectRole { get; set; } = string.Empty; // Project Manager, Developer, Tester, etc.

        public decimal AllocationPercentage { get; set; } = 100; // 0-100%

        private DateTime _startDate = DateTime.UtcNow;
        public DateTime StartDate
        {
            get => _startDate;
            set => _startDate = DateTime.SpecifyKind(value, DateTimeKind.Utc);
        }

        private DateTime? _endDate;
        public DateTime? EndDate
        {
            get => _endDate;
            set => _endDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        public bool IsKeyMember { get; set; } = false;
        public bool ReceiveNotifications { get; set; } = true;
        public bool IsActive { get; set; } = true;

        [MaxLength(1000)]
        public string? Notes { get; set; }

        public string? AssignedByUserId { get; set; }

        // Navigation Properties
        public virtual Person Person { get; set; } = null!;
        public virtual Project Project { get; set; } = null!;
        public virtual ApplicationUser? AssignedBy { get; set; }

        // Computed Properties
        public bool IsCurrentlyAssigned => IsActive &&
            DateTime.UtcNow >= StartDate &&
            (EndDate == null || DateTime.UtcNow <= EndDate);
    }

    /// <summary>
    /// Stakeholder-specific information for people
    /// </summary>
    public class PersonStakeholder : BaseEntity
    {
        public int PersonId { get; set; }

        public StakeholderType Type { get; set; } = StakeholderType.Internal;
        public InfluenceLevel Influence { get; set; } = InfluenceLevel.Medium;
        public InterestLevel Interest { get; set; } = InterestLevel.Medium;

        [MaxLength(1000)]
        public string? StakeholderNotes { get; set; }

        [MaxLength(500)]
        public string? CommunicationStrategy { get; set; }

        public bool RequiresRegularUpdates { get; set; } = true;

        // Navigation Properties
        public virtual Person Person { get; set; } = null!;

        // Computed Properties
        public StakeholderPriority Priority => GetStakeholderPriority();

        private StakeholderPriority GetStakeholderPriority()
        {
            return (Influence, Interest) switch
            {
                (InfluenceLevel.High or InfluenceLevel.VeryHigh, InterestLevel.High or InterestLevel.VeryHigh) => StakeholderPriority.Critical,
                (InfluenceLevel.High or InfluenceLevel.VeryHigh, _) => StakeholderPriority.High,
                (_, InterestLevel.High or InterestLevel.VeryHigh) => StakeholderPriority.Medium,
                _ => StakeholderPriority.Low
            };
        }
    }

    /// <summary>
    /// Resource-specific information for people (capacity, rates, etc.)
    /// </summary>
    public class PersonResource : BaseEntity
    {
        public int PersonId { get; set; }

        public decimal HourlyRate { get; set; } = 0;
        public decimal DailyCapacity { get; set; } = 8; // Hours per day

        [MaxLength(50)]
        public string? CostCenter { get; set; }

        public int? ManagerId { get; set; } // PersonId of manager

        [MaxLength(100)]
        public string? JobGrade { get; set; }

        [MaxLength(100)]
        public string? ContractType { get; set; } // Full-time, Part-time, Contract, etc.

        public bool IsAvailableForAllocation { get; set; } = true;

        [MaxLength(500)]
        public string? ResourceNotes { get; set; }

        // Navigation Properties
        public virtual Person Person { get; set; } = null!;
        public virtual Person? Manager { get; set; }

        // Computed Properties
        public decimal WeeklyCapacity => DailyCapacity * 5; // Assuming 5-day work week
        public decimal MonthlyCapacity => DailyCapacity * 22; // Assuming 22 working days per month
    }

    /// <summary>
    /// Additional contact information for people
    /// </summary>
    public class PersonContact : BaseEntity
    {
        public int PersonId { get; set; }

        public ContactType Type { get; set; } = ContactType.Email;

        [Required]
        [MaxLength(200)]
        public string Value { get; set; } = string.Empty;

        [MaxLength(100)]
        public string? Label { get; set; } // "Work", "Personal", "Emergency", etc.

        public bool IsPrimary { get; set; } = false;
        public bool IsPublic { get; set; } = true; // Visible to other users

        // Navigation Properties
        public virtual Person Person { get; set; } = null!;
    }

    /// <summary>
    /// Availability and calendar information for people
    /// </summary>
    public class PersonAvailability : BaseEntity
    {
        public int PersonId { get; set; }

        private DateTime _startDate;
        public DateTime StartDate
        {
            get => _startDate;
            set => _startDate = DateTime.SpecifyKind(value, DateTimeKind.Utc);
        }

        private DateTime _endDate;
        public DateTime EndDate
        {
            get => _endDate;
            set => _endDate = DateTime.SpecifyKind(value, DateTimeKind.Utc);
        }

        public AvailabilityType Type { get; set; } = AvailabilityType.Available;

        [MaxLength(200)]
        public string? Reason { get; set; }

        public decimal AvailabilityPercentage { get; set; } = 100; // 0-100%

        [MaxLength(500)]
        public string? Notes { get; set; }

        // Navigation Properties
        public virtual Person Person { get; set; } = null!;

        // Computed Properties
        public bool IsCurrentlyActive => DateTime.UtcNow >= StartDate && DateTime.UtcNow <= EndDate;
        public int DurationDays => (EndDate - StartDate).Days + 1;
    }

    // Enums
    public enum PersonType
    {
        Internal = 1,       // Employees
        External = 2,       // External stakeholders
        Contractor = 3,     // Contractors/Consultants
        Vendor = 4,         // Vendor representatives
        Customer = 5,       // Customer representatives
        Partner = 6,        // Partner representatives
        Regulatory = 7      // Regulatory body representatives
    }

    public enum PersonStatus
    {
        Active = 1,
        Inactive = 2,
        OnLeave = 3,
        Terminated = 4,
        Suspended = 5,
        Pending = 6         // Pending activation
    }

    public enum RoleScope
    {
        System = 1,         // System-wide role
        Organization = 2,   // Organization-wide role
        Department = 3,     // Department-specific role
        Project = 4         // Project-specific role
    }

    public enum ContactType
    {
        Email = 1,
        Phone = 2,
        Mobile = 3,
        Fax = 4,
        LinkedIn = 5,
        Skype = 6,
        Teams = 7,
        Slack = 8,
        Other = 9
    }

    public enum AvailabilityType
    {
        Available = 1,
        Vacation = 2,
        SickLeave = 3,
        Training = 4,
        Meeting = 5,
        Travel = 6,
        PersonalTime = 7,
        Unavailable = 8
    }
}
