@model IEnumerable<PM.Tool.Core.Entities.Risk>
@{
    ViewData["Title"] = "Risk Matrix";
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Risk Management", Href = Url.Action("Index", "Risk"), Icon = "fas fa-exclamation-triangle" },
        new { Text = "Risk Matrix", Href = "", Icon = "fas fa-th" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-th mr-3 text-primary-600 dark:text-primary-400"></i>
                Risk Matrix
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Visual representation of risks by probability and impact
            </p>
        </div>
        <div class="mt-4 sm:mt-0 flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
            @{
                ViewData["Text"] = "Back to Risks";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Index");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>



<!-- Matrix Controls -->
@{
    ViewData["Title"] = "Matrix Controls";
    ViewData["Icon"] = "fas fa-cog";
    ViewData["BodyContent"] = @"
        <div class='grid grid-cols-1 md:grid-cols-3 gap-4'>
            <div>
                <label for='projectFilter' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Project</label>
                <select id='projectFilter' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                    <option value=''>All Projects</option>
                </select>
            </div>
            <div>
                <label for='statusFilter' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Status</label>
                <select id='statusFilter' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                    <option value=''>All Statuses</option>
                    <option value='Identified'>Identified</option>
                    <option value='Analyzing'>Analyzing</option>
                    <option value='Mitigating'>Mitigating</option>
                    <option value='Monitoring'>Monitoring</option>
                    <option value='Resolved'>Resolved</option>
                    <option value='Accepted'>Accepted</option>
                </select>
            </div>
            <div>
                <label for='categoryFilter' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Category</label>
                <select id='categoryFilter' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                    <option value=''>All Categories</option>
                    <option value='Technical'>Technical</option>
                    <option value='Schedule'>Schedule</option>
                    <option value='Budget'>Budget</option>
                    <option value='Resource'>Resource</option>
                    <option value='Quality'>Quality</option>
                    <option value='External'>External</option>
                    <option value='Organizational'>Organizational</option>
                </select>
            </div>
        </div>
    ";
}
<div class="mb-6">
    <partial name="Components/_Card" view-data="ViewData" />
</div>

<!-- Risk Matrix -->
@{
    ViewData["Title"] = "Risk Probability vs Impact Matrix";
    ViewData["Icon"] = "fas fa-chart-area";
    ViewData["BodyContent"] = @"
        <div class='overflow-x-auto'>
            <div class='min-w-full'>
                <!-- Matrix Header -->
                <div class='flex items-center justify-center mb-4'>
                    <div class='text-center'>
                        <h3 class='text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2'>Impact →</h3>
                        <div class='text-sm text-neutral-500 dark:text-dark-400'>Probability ↓</div>
                    </div>
                </div>

                <!-- Matrix Grid -->
                <div class='grid grid-cols-6 gap-1 bg-neutral-200 dark:bg-dark-600 p-2 rounded-lg'>
                    <!-- Header Row -->
                    <div class='bg-neutral-100 dark:bg-dark-700 p-3 text-center font-medium text-neutral-700 dark:text-dark-300 rounded'></div>
                    <div class='bg-neutral-100 dark:bg-dark-700 p-3 text-center font-medium text-neutral-700 dark:text-dark-300 rounded'>Very Low<br><span class='text-xs'>(1)</span></div>
                    <div class='bg-neutral-100 dark:bg-dark-700 p-3 text-center font-medium text-neutral-700 dark:text-dark-300 rounded'>Low<br><span class='text-xs'>(2)</span></div>
                    <div class='bg-neutral-100 dark:bg-dark-700 p-3 text-center font-medium text-neutral-700 dark:text-dark-300 rounded'>Medium<br><span class='text-xs'>(3)</span></div>
                    <div class='bg-neutral-100 dark:bg-dark-700 p-3 text-center font-medium text-neutral-700 dark:text-dark-300 rounded'>High<br><span class='text-xs'>(4)</span></div>
                    <div class='bg-neutral-100 dark:bg-dark-700 p-3 text-center font-medium text-neutral-700 dark:text-dark-300 rounded'>Very High<br><span class='text-xs'>(5)</span></div>

                    <!-- Matrix Cells -->
                    <!-- Very High Probability Row -->
                    <div class='bg-neutral-100 dark:bg-dark-700 p-3 text-center font-medium text-neutral-700 dark:text-dark-300 rounded'>Very High<br><span class='text-xs'>(5)</span></div>
                    <div id='cell-5-1' class='matrix-cell bg-warning-200 dark:bg-warning-800 p-3 rounded min-h-[80px] cursor-pointer hover:bg-warning-300 dark:hover:bg-warning-700 transition-colors' data-probability='5' data-impact='1'></div>
                    <div id='cell-5-2' class='matrix-cell bg-warning-300 dark:bg-warning-700 p-3 rounded min-h-[80px] cursor-pointer hover:bg-warning-400 dark:hover:bg-warning-600 transition-colors' data-probability='5' data-impact='2'></div>
                    <div id='cell-5-3' class='matrix-cell bg-danger-200 dark:bg-danger-800 p-3 rounded min-h-[80px] cursor-pointer hover:bg-danger-300 dark:hover:bg-danger-700 transition-colors' data-probability='5' data-impact='3'></div>
                    <div id='cell-5-4' class='matrix-cell bg-danger-300 dark:bg-danger-700 p-3 rounded min-h-[80px] cursor-pointer hover:bg-danger-400 dark:hover:bg-danger-600 transition-colors' data-probability='5' data-impact='4'></div>
                    <div id='cell-5-5' class='matrix-cell bg-danger-400 dark:bg-danger-600 p-3 rounded min-h-[80px] cursor-pointer hover:bg-danger-500 dark:hover:bg-danger-500 transition-colors' data-probability='5' data-impact='5'></div>

                    <!-- High Probability Row -->
                    <div class='bg-neutral-100 dark:bg-dark-700 p-3 text-center font-medium text-neutral-700 dark:text-dark-300 rounded'>High<br><span class='text-xs'>(4)</span></div>
                    <div id='cell-4-1' class='matrix-cell bg-success-200 dark:bg-success-800 p-3 rounded min-h-[80px] cursor-pointer hover:bg-success-300 dark:hover:bg-success-700 transition-colors' data-probability='4' data-impact='1'></div>
                    <div id='cell-4-2' class='matrix-cell bg-warning-200 dark:bg-warning-800 p-3 rounded min-h-[80px] cursor-pointer hover:bg-warning-300 dark:hover:bg-warning-700 transition-colors' data-probability='4' data-impact='2'></div>
                    <div id='cell-4-3' class='matrix-cell bg-warning-300 dark:bg-warning-700 p-3 rounded min-h-[80px] cursor-pointer hover:bg-warning-400 dark:hover:bg-warning-600 transition-colors' data-probability='4' data-impact='3'></div>
                    <div id='cell-4-4' class='matrix-cell bg-danger-200 dark:bg-danger-800 p-3 rounded min-h-[80px] cursor-pointer hover:bg-danger-300 dark:hover:bg-danger-700 transition-colors' data-probability='4' data-impact='4'></div>
                    <div id='cell-4-5' class='matrix-cell bg-danger-300 dark:bg-danger-700 p-3 rounded min-h-[80px] cursor-pointer hover:bg-danger-400 dark:hover:bg-danger-600 transition-colors' data-probability='4' data-impact='5'></div>

                    <!-- Medium Probability Row -->
                    <div class='bg-neutral-100 dark:bg-dark-700 p-3 text-center font-medium text-neutral-700 dark:text-dark-300 rounded'>Medium<br><span class='text-xs'>(3)</span></div>
                    <div id='cell-3-1' class='matrix-cell bg-success-200 dark:bg-success-800 p-3 rounded min-h-[80px] cursor-pointer hover:bg-success-300 dark:hover:bg-success-700 transition-colors' data-probability='3' data-impact='1'></div>
                    <div id='cell-3-2' class='matrix-cell bg-success-200 dark:bg-success-800 p-3 rounded min-h-[80px] cursor-pointer hover:bg-success-300 dark:hover:bg-success-700 transition-colors' data-probability='3' data-impact='2'></div>
                    <div id='cell-3-3' class='matrix-cell bg-warning-200 dark:bg-warning-800 p-3 rounded min-h-[80px] cursor-pointer hover:bg-warning-300 dark:hover:bg-warning-700 transition-colors' data-probability='3' data-impact='3'></div>
                    <div id='cell-3-4' class='matrix-cell bg-warning-300 dark:bg-warning-700 p-3 rounded min-h-[80px] cursor-pointer hover:bg-warning-400 dark:hover:bg-warning-600 transition-colors' data-probability='3' data-impact='4'></div>
                    <div id='cell-3-5' class='matrix-cell bg-danger-200 dark:bg-danger-800 p-3 rounded min-h-[80px] cursor-pointer hover:bg-danger-300 dark:hover:bg-danger-700 transition-colors' data-probability='3' data-impact='5'></div>

                    <!-- Low Probability Row -->
                    <div class='bg-neutral-100 dark:bg-dark-700 p-3 text-center font-medium text-neutral-700 dark:text-dark-300 rounded'>Low<br><span class='text-xs'>(2)</span></div>
                    <div id='cell-2-1' class='matrix-cell bg-success-100 dark:bg-success-900 p-3 rounded min-h-[80px] cursor-pointer hover:bg-success-200 dark:hover:bg-success-800 transition-colors' data-probability='2' data-impact='1'></div>
                    <div id='cell-2-2' class='matrix-cell bg-success-200 dark:bg-success-800 p-3 rounded min-h-[80px] cursor-pointer hover:bg-success-300 dark:hover:bg-success-700 transition-colors' data-probability='2' data-impact='2'></div>
                    <div id='cell-2-3' class='matrix-cell bg-success-200 dark:bg-success-800 p-3 rounded min-h-[80px] cursor-pointer hover:bg-success-300 dark:hover:bg-success-700 transition-colors' data-probability='2' data-impact='3'></div>
                    <div id='cell-2-4' class='matrix-cell bg-warning-200 dark:bg-warning-800 p-3 rounded min-h-[80px] cursor-pointer hover:bg-warning-300 dark:hover:bg-warning-700 transition-colors' data-probability='2' data-impact='4'></div>
                    <div id='cell-2-5' class='matrix-cell bg-warning-300 dark:bg-warning-700 p-3 rounded min-h-[80px] cursor-pointer hover:bg-warning-400 dark:hover:bg-warning-600 transition-colors' data-probability='2' data-impact='5'></div>

                    <!-- Very Low Probability Row -->
                    <div class='bg-neutral-100 dark:bg-dark-700 p-3 text-center font-medium text-neutral-700 dark:text-dark-300 rounded'>Very Low<br><span class='text-xs'>(1)</span></div>
                    <div id='cell-1-1' class='matrix-cell bg-success-100 dark:bg-success-900 p-3 rounded min-h-[80px] cursor-pointer hover:bg-success-200 dark:hover:bg-success-800 transition-colors' data-probability='1' data-impact='1'></div>
                    <div id='cell-1-2' class='matrix-cell bg-success-100 dark:bg-success-900 p-3 rounded min-h-[80px] cursor-pointer hover:bg-success-200 dark:hover:bg-success-800 transition-colors' data-probability='1' data-impact='2'></div>
                    <div id='cell-1-3' class='matrix-cell bg-success-200 dark:bg-success-800 p-3 rounded min-h-[80px] cursor-pointer hover:bg-success-300 dark:hover:bg-success-700 transition-colors' data-probability='1' data-impact='3'></div>
                    <div id='cell-1-4' class='matrix-cell bg-success-200 dark:bg-success-800 p-3 rounded min-h-[80px] cursor-pointer hover:bg-success-300 dark:hover:bg-success-700 transition-colors' data-probability='1' data-impact='4'></div>
                    <div id='cell-1-5' class='matrix-cell bg-warning-200 dark:bg-warning-800 p-3 rounded min-h-[80px] cursor-pointer hover:bg-warning-300 dark:hover:bg-warning-700 transition-colors' data-probability='1' data-impact='5'></div>
                </div>

                <!-- Legend -->
                <div class='mt-6 grid grid-cols-1 md:grid-cols-4 gap-4'>
                    <div class='flex items-center space-x-2'>
                        <div class='w-4 h-4 bg-success-200 dark:bg-success-800 rounded'></div>
                        <span class='text-sm text-neutral-700 dark:text-dark-300'>Low Risk (1-6)</span>
                    </div>
                    <div class='flex items-center space-x-2'>
                        <div class='w-4 h-4 bg-warning-200 dark:bg-warning-800 rounded'></div>
                        <span class='text-sm text-neutral-700 dark:text-dark-300'>Medium Risk (7-14)</span>
                    </div>
                    <div class='flex items-center space-x-2'>
                        <div class='w-4 h-4 bg-danger-200 dark:bg-danger-800 rounded'></div>
                        <span class='text-sm text-neutral-700 dark:text-dark-300'>High Risk (15-19)</span>
                    </div>
                    <div class='flex items-center space-x-2'>
                        <div class='w-4 h-4 bg-danger-400 dark:bg-danger-600 rounded'></div>
                        <span class='text-sm text-neutral-700 dark:text-dark-300'>Critical Risk (20-25)</span>
                    </div>
                </div>
            </div>
        </div>
    ";
}
<partial name="Components/_Card" view-data="ViewData" />

<!-- Risk Summary -->
<div id="riskSummary" class="mb-6 hidden">
    @{
        ViewData["Title"] = "Risk Summary";
        ViewData["Icon"] = "fas fa-chart-pie";
        ViewData["BodyContent"] = @"
            <div class='grid grid-cols-1 md:grid-cols-4 gap-4'>
                <div class='text-center p-4 bg-success-50 dark:bg-success-900/20 rounded-lg'>
                    <div class='text-2xl font-bold text-success-600 dark:text-success-400' id='lowRiskCount'>0</div>
                    <div class='text-sm text-success-700 dark:text-success-300'>Low Risk</div>
                    <div class='text-xs text-success-600 dark:text-success-400'>(Score: 1-6)</div>
                </div>
                <div class='text-center p-4 bg-warning-50 dark:bg-warning-900/20 rounded-lg'>
                    <div class='text-2xl font-bold text-warning-600 dark:text-warning-400' id='mediumRiskCount'>0</div>
                    <div class='text-sm text-warning-700 dark:text-warning-300'>Medium Risk</div>
                    <div class='text-xs text-warning-600 dark:text-warning-400'>(Score: 7-14)</div>
                </div>
                <div class='text-center p-4 bg-danger-50 dark:bg-danger-900/20 rounded-lg'>
                    <div class='text-2xl font-bold text-danger-600 dark:text-danger-400' id='highRiskCount'>0</div>
                    <div class='text-sm text-danger-700 dark:text-danger-300'>High Risk</div>
                    <div class='text-xs text-danger-600 dark:text-danger-400'>(Score: 15-19)</div>
                </div>
                <div class='text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg'>
                    <div class='text-2xl font-bold text-red-600 dark:text-red-400' id='criticalRiskCount'>0</div>
                    <div class='text-sm text-red-700 dark:text-red-300'>Critical Risk</div>
                    <div class='text-xs text-red-600 dark:text-red-400'>(Score: 20-25)</div>
                </div>
            </div>
            <div class='mt-4 text-center'>
                <div class='text-sm text-neutral-600 dark:text-dark-400'>
                    Total Risks: <span id='totalRiskCount' class='font-semibold'>0</span> |
                    Average Risk Score: <span id='avgRiskScore' class='font-semibold'>0</span>
                </div>
            </div>
        ";
    }
    <partial name="Components/_Card" view-data="ViewData" />
</div>

<!-- Empty State -->
<div id="emptyState" class="hidden">
    @{
        ViewData["Title"] = "No Risks Found";
        ViewData["Icon"] = "fas fa-shield-alt";
        ViewData["BodyContent"] = @"
            <div class='text-center py-12'>
                <div class='mx-auto w-24 h-24 bg-success-100 dark:bg-success-900 rounded-full flex items-center justify-center mb-6'>
                    <i class='fas fa-shield-alt text-3xl text-success-600 dark:text-success-400'></i>
                </div>
                <h3 class='text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2'>No Risks in Matrix</h3>
                <p class='text-neutral-600 dark:text-dark-400 mb-6 max-w-md mx-auto'>
                    No risks have been identified yet, or all risks have been filtered out.
                    Start by creating your first risk assessment.
                </p>
                <div class='space-y-3'>
                    <a href='" + Url.Action("Create", "Risk") + @"' class='inline-flex items-center px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors'>
                        <i class='fas fa-plus mr-2'></i>
                        Create First Risk
                    </a>
                    <div class='text-sm text-neutral-500 dark:text-dark-400'>
                        <p class='mb-2'><strong>Risk Matrix Guide:</strong></p>
                        <ul class='text-left inline-block space-y-1'>
                            <li>• <strong>Probability:</strong> Likelihood of risk occurring (1-5)</li>
                            <li>• <strong>Impact:</strong> Severity if risk occurs (1-5)</li>
                            <li>• <strong>Risk Score:</strong> Probability × Impact (1-25)</li>
                            <li>• <strong>Colors:</strong> Green (Low), Yellow (Medium), Red (High/Critical)</li>
                        </ul>
                    </div>
                </div>
            </div>
        ";
    }
    <partial name="Components/_Card" view-data="ViewData" />
</div>

<!-- Risk Details Modal -->
<div id="riskModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white dark:bg-surface-dark rounded-xl max-w-2xl w-full max-h-96 overflow-y-auto">
        <div class="p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                    <i class="fas fa-exclamation-triangle mr-2 text-danger-600 dark:text-danger-400"></i>
                    Risks in Cell
                </h3>
                <button onclick="closeRiskModal()" class="text-neutral-400 hover:text-neutral-600 dark:hover:text-dark-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="riskModalContent" class="space-y-4">
                <!-- Risk details will be populated here -->
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let allRisks = @Html.Raw(Json.Serialize(Model?.Select(r => new {
            Id = r.Id,
            Title = r.Title,
            Description = r.Description,
            Probability = (int)r.Probability,
            Impact = (int)r.Impact,
            RiskScore = (int)r.Probability * (int)r.Impact,
            Status = r.Status.ToString(),
            Category = r.Category.ToString(),
            ProjectId = r.ProjectId,
            RiskLevel = r.RiskLevel.ToString()
        })));

        $(document).ready(function() {
            loadProjects();
            setupFilters();
            populateMatrix();
            showEmptyStateIfNeeded();
        });

        function loadProjects() {
            const select = $('#projectFilter');

            // Load projects from API
            $.get('@Url.Action("GetProjects", "Projects")')
                .done(function(data) {
                    if (data && Array.isArray(data)) {
                        data.forEach(function(project) {
                            select.append(`<option value="${project.id}">${project.name}</option>`);
                        });
                    }
                })
                .fail(function() {
                    // Fallback: extract unique projects from risks data
                    const projectMap = new Map();
                    allRisks.forEach(function(risk) {
                        if (risk.ProjectId && !projectMap.has(risk.ProjectId)) {
                            projectMap.set(risk.ProjectId, `Project ${risk.ProjectId}`);
                        }
                    });

                    // Add projects to dropdown
                    projectMap.forEach(function(name, id) {
                        select.append(`<option value="${id}">${name}</option>`);
                    });
                });
        }

        function setupFilters() {
            $('#projectFilter, #statusFilter, #categoryFilter').on('change', function() {
                populateMatrix();
            });
        }

        function populateMatrix() {
            // Clear all cells
            $('.matrix-cell').empty().removeClass('has-risks');

            // Filter risks based on current filters
            const filteredRisks = getFilteredRisks();

            // Show/hide empty state
            showEmptyStateIfNeeded();

            // Group risks by probability and impact
            const riskGroups = {};
            filteredRisks.forEach(risk => {
                const key = `${risk.Probability}-${risk.Impact}`;
                if (!riskGroups[key]) {
                    riskGroups[key] = [];
                }
                riskGroups[key].push(risk);
            });

            // Add empty state to all cells first
            $('.matrix-cell').each(function() {
                const cell = $(this);
                if (!cell.hasClass('has-risks')) {
                    cell.html(`
                        <div class="text-center opacity-50">
                            <div class="text-sm text-neutral-500 dark:text-dark-500">0</div>
                            <div class="text-xs text-neutral-400 dark:text-dark-600">risks</div>
                        </div>
                    `);
                }
            });

            // Populate matrix cells with actual risks
            Object.keys(riskGroups).forEach(key => {
                const [probability, impact] = key.split('-');
                const cellId = `cell-${probability}-${impact}`;
                const cell = $(`#${cellId}`);
                const risks = riskGroups[key];

                cell.addClass('has-risks');
                cell.html(`
                    <div class="text-center">
                        <div class="text-lg font-bold text-neutral-900 dark:text-dark-100">${risks.length}</div>
                        <div class="text-xs text-neutral-600 dark:text-dark-400">risk${risks.length > 1 ? 's' : ''}</div>
                        <div class="text-xs text-neutral-500 dark:text-dark-500 mt-1">Click to view</div>
                    </div>
                `);

                cell.off('click').on('click', function() {
                    showRisksInCell(risks, probability, impact);
                });
            });
        }

        function showEmptyStateIfNeeded() {
            const filteredRisks = getFilteredRisks();
            const hasRisks = filteredRisks.length > 0;

            if (hasRisks) {
                $('#emptyState').addClass('hidden');
                $('#riskSummary').removeClass('hidden');
                updateRiskSummary(filteredRisks);
            } else {
                $('#emptyState').removeClass('hidden');
                $('#riskSummary').addClass('hidden');
            }
        }

        function updateRiskSummary(risks) {
            let lowCount = 0, mediumCount = 0, highCount = 0, criticalCount = 0;
            let totalScore = 0;

            risks.forEach(risk => {
                const score = risk.RiskScore;
                totalScore += score;

                if (score >= 20) criticalCount++;
                else if (score >= 15) highCount++;
                else if (score >= 7) mediumCount++;
                else lowCount++;
            });

            $('#lowRiskCount').text(lowCount);
            $('#mediumRiskCount').text(mediumCount);
            $('#highRiskCount').text(highCount);
            $('#criticalRiskCount').text(criticalCount);
            $('#totalRiskCount').text(risks.length);
            $('#avgRiskScore').text(risks.length > 0 ? (totalScore / risks.length).toFixed(1) : '0');
        }

        function getFilteredRisks() {
            const projectFilter = $('#projectFilter').val();
            const statusFilter = $('#statusFilter').val();
            const categoryFilter = $('#categoryFilter').val();

            return allRisks.filter(risk => {
                // Project filter
                if (projectFilter && projectFilter !== '' && risk.ProjectId.toString() !== projectFilter) {
                    return false;
                }

                // Status filter
                if (statusFilter && statusFilter !== '' && risk.Status !== statusFilter) {
                    return false;
                }

                // Category filter
                if (categoryFilter && categoryFilter !== '' && risk.Category !== categoryFilter) {
                    return false;
                }

                return true;
            });
        }

        function showRisksInCell(risks, probability, impact) {
            const modalContent = $('#riskModalContent');
            modalContent.empty();

            const probabilityText = ['', 'Very Low', 'Low', 'Medium', 'High', 'Very High'][probability];
            const impactText = ['', 'Very Low', 'Low', 'Medium', 'High', 'Very High'][impact];

            modalContent.append(`
                <div class="mb-4 p-3 bg-neutral-100 dark:bg-dark-700 rounded-lg">
                    <h4 class="font-medium text-neutral-900 dark:text-dark-100">
                        Probability: ${probabilityText} (${probability}) | Impact: ${impactText} (${impact})
                    </h4>
                    <p class="text-sm text-neutral-600 dark:text-dark-400">${risks.length} risk${risks.length > 1 ? 's' : ''} in this cell</p>
                </div>
            `);

            risks.forEach(risk => {
                const riskLevelClass = getRiskLevelClass(risk.RiskLevel);
                modalContent.append(`
                    <div class="border border-neutral-200 dark:border-dark-600 rounded-lg p-4">
                        <div class="flex items-start justify-between mb-2">
                            <h5 class="font-medium text-neutral-900 dark:text-dark-100">${risk.Title}</h5>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${riskLevelClass}">
                                ${risk.RiskLevel}
                            </span>
                        </div>
                        <p class="text-sm text-neutral-600 dark:text-dark-400 mb-3">${risk.Description}</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4 text-xs text-neutral-500 dark:text-dark-400">
                                <span>Score: ${risk.RiskScore}</span>
                                <span>Status: ${risk.Status}</span>
                                <span>Category: ${risk.Category}</span>
                            </div>
                            <a href="@Url.Action("Details", "Risk")/${risk.Id}" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 text-sm">
                                View Details →
                            </a>
                        </div>
                    </div>
                `);
            });

            $('#riskModal').removeClass('hidden');
        }

        function closeRiskModal() {
            $('#riskModal').addClass('hidden');
        }

        function getRiskLevelClass(level) {
            switch(level) {
                case 'Critical': return 'bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200';
                case 'High': return 'bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200';
                case 'Medium': return 'bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200';
                case 'Low': return 'bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200';
                default: return 'bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200';
            }
        }
    </script>
}
