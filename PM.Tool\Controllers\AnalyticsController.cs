using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Models;
using PM.Tool.Core.Entities;
using PM.Tool.Models.ViewModels;

namespace PM.Tool.Controllers
{
    [Authorize]
    public class AnalyticsController : SecureBaseController
    {
        private readonly IAnalyticsService _analyticsService;
        private readonly IProjectService _projectService;
        private readonly ITaskService _taskService;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<AnalyticsController> _logger;

        public AnalyticsController(
            IAnalyticsService analyticsService,
            IProjectService projectService,
            ITaskService taskService,
            UserManager<ApplicationUser> userManager,
            ILogger<AnalyticsController> logger,
            IAuditService auditService) : base(auditService)
        {
            _analyticsService = analyticsService;
            _projectService = projectService;
            _taskService = taskService;
            _userManager = userManager;
            _logger = logger;
        }

        // GET: Analytics
        public async Task<IActionResult> Index()
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null) return RedirectToAction("Login", "Account");

                var projects = await _projectService.GetUserProjectsAsync(user.Id);
                ViewBag.Projects = projects;

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading analytics dashboard");
                TempData["Error"] = "Error loading analytics dashboard.";
                return View();
            }
        }

        // GET: Analytics/Project/5
        public async Task<IActionResult> Project(int id)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(id);
                if (project == null) return NotFound();

                // For now, use basic project data - implement advanced analytics later
                ViewBag.Project = project;

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading project analytics for project {ProjectId}", id);
                TempData["Error"] = "Error loading project analytics.";
                return RedirectToAction("Index");
            }
        }

        // GET: Analytics/Team
        public async Task<IActionResult> Team()
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null) return RedirectToAction("Login", "Account");

                // For now, return a simple view - implement team analytics later
                var projects = await _projectService.GetUserProjectsAsync(user.Id);
                ViewBag.Projects = projects;

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading team analytics");
                TempData["Error"] = "Error loading team analytics.";
                return View();
            }
        }

        // GET: Analytics/Reports
        public async Task<IActionResult> Reports()
        {
            try
            {
                var user = await _userManager.GetUserAsync(User);
                if (user == null) return RedirectToAction("Login", "Account");

                var projects = await _projectService.GetUserProjectsAsync(user.Id);
                ViewBag.Projects = projects;

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading reports page");
                TempData["Error"] = "Error loading reports page.";
                return View();
            }
        }

        // GET: Analytics/Burndown/5
        public async Task<IActionResult> Burndown(int projectId)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                // For now, return a simple view - implement burndown chart later
                ViewBag.Project = project;

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading burndown chart for project {ProjectId}", projectId);
                TempData["Error"] = "Error loading burndown chart.";
                return RedirectToAction("Index");
            }
        }

        // GET: Analytics/Velocity/5
        public async Task<IActionResult> Velocity(int projectId)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                // For now, return a simple view - implement velocity chart later
                ViewBag.Project = project;

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading velocity chart for project {ProjectId}", projectId);
                TempData["Error"] = "Error loading velocity chart.";
                return RedirectToAction("Index");
            }
        }

        // GET: Analytics/ExportData
        [HttpGet]
        public async Task<IActionResult> ExportData(int projectId, string format = "csv")
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                // For now, return a simple CSV export - implement full export later
                var csvData = $"Project Name,Status,Progress\n{project.Name},{project.Status},{project.ProgressPercentage}%";
                var bytes = System.Text.Encoding.UTF8.GetBytes(csvData);

                var fileName = $"{project.Name}_Analytics_{DateTime.UtcNow:yyyyMMdd}.csv";
                return File(bytes, "text/csv", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting data for project {ProjectId}", projectId);
                TempData["Error"] = "Error exporting data.";
                return RedirectToAction("Index");
            }
        }

        // API endpoints for AJAX calls
        [HttpGet]
        public async Task<IActionResult> GetProjectMetrics(int projectId)
        {
            try
            {
                var metrics = await _analyticsService.GetProjectMetricsAsync(projectId, DateTime.UtcNow);
                return Json(metrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting project metrics for project {ProjectId}", projectId);
                return Json(new { error = "Error loading metrics" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetTaskDistribution(int projectId)
        {
            try
            {
                var distribution = await _analyticsService.GetTaskStatusDistributionAsync(projectId);
                return Json(distribution);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting task distribution for project {ProjectId}", projectId);
                return Json(new { error = "Error loading task distribution" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetCompletionTrend(int projectId, DateTime startDate, DateTime endDate)
        {
            try
            {
                var trend = await _analyticsService.GetTaskCompletionTrendAsync(projectId, startDate, endDate);
                return Json(trend);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting completion trend for project {ProjectId}", projectId);
                return Json(new { error = "Error loading completion trend" });
            }
        }
    }
}
