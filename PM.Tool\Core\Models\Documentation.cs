using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Models
{
    public class DocumentationSection
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public int Order { get; set; }
        public List<DocumentationPage> Pages { get; set; } = new List<DocumentationPage>();
    }

    public class DocumentationPage
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string HtmlContent { get; set; } = string.Empty;
        public string SectionId { get; set; } = string.Empty;
        public int Order { get; set; }
        public DateTime LastModified { get; set; }
        public List<DocumentationHeading> Headings { get; set; } = new List<DocumentationHeading>();
        public List<string> Tags { get; set; } = new List<string>();
        public string Author { get; set; } = string.Empty;
        public TimeSpan EstimatedReadTime { get; set; }
    }

    public class DocumentationHeading
    {
        public string Id { get; set; } = string.Empty;
        public string Text { get; set; } = string.Empty;
        public int Level { get; set; }
        public string Anchor { get; set; } = string.Empty;
    }

    public class DocumentationSearchResult
    {
        public string PageId { get; set; } = string.Empty;
        public string PageTitle { get; set; } = string.Empty;
        public string SectionTitle { get; set; } = string.Empty;
        public string Excerpt { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public double Relevance { get; set; }
        public List<string> MatchedTerms { get; set; } = new List<string>();
    }

    public class DocumentationNavigation
    {
        public DocumentationPage? Previous { get; set; }
        public DocumentationPage? Next { get; set; }
        public DocumentationPage? Parent { get; set; }
        public List<DocumentationPage> Children { get; set; } = new List<DocumentationPage>();
        public List<DocumentationPage> Siblings { get; set; } = new List<DocumentationPage>();
    }

    public class DocumentationTableOfContents
    {
        public List<DocumentationTocItem> Items { get; set; } = new List<DocumentationTocItem>();
    }

    public class DocumentationTocItem
    {
        public string Id { get; set; } = string.Empty;
        public string Text { get; set; } = string.Empty;
        public string Anchor { get; set; } = string.Empty;
        public int Level { get; set; }
        public List<DocumentationTocItem> Children { get; set; } = new List<DocumentationTocItem>();
    }

    public class DocumentationMetadata
    {
        public int TotalPages { get; set; }
        public int TotalSections { get; set; }
        public DateTime LastUpdated { get; set; }
        public string Version { get; set; } = string.Empty;
        public List<string> Contributors { get; set; } = new List<string>();
        public Dictionary<string, int> Statistics { get; set; } = new Dictionary<string, int>();
    }

    public class DocumentationConfiguration
    {
        public string Title { get; set; } = "PM Tool Documentation";
        public string Description { get; set; } = "Comprehensive documentation for PM Tool";
        public string Version { get; set; } = "1.0.0";
        public string BaseUrl { get; set; } = "/docs";
        public string LogoUrl { get; set; } = "/images/logo.png";
        public bool EnableSearch { get; set; } = true;
        public bool EnablePrint { get; set; } = true;
        public bool EnableDownload { get; set; } = true;
        public bool EnableFeedback { get; set; } = true;
        public string FeedbackEmail { get; set; } = "<EMAIL>";
        public List<string> SupportedFormats { get; set; } = new List<string> { "html", "pdf", "markdown" };
    }
}
