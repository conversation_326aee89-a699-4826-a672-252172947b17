using PM.Tool.Core.Entities;

namespace PM.Tool.Core.Interfaces
{
    public interface IAnalyticsService
    {
        // Project Analytics
        Task<ProjectMetrics> GetProjectMetricsAsync(int projectId, DateTime date);
        Task<IEnumerable<ProjectMetrics>> GetProjectMetricsHistoryAsync(int projectId, DateTime startDate, DateTime endDate);
        Task<Dictionary<string, object>> GetProjectDashboardDataAsync(int projectId);

        // Task Analytics
        Task<Dictionary<string, int>> GetTaskStatusDistributionAsync(int projectId);
        Task<Dictionary<string, int>> GetTaskPriorityDistributionAsync(int projectId);
        Task<IEnumerable<object>> GetTaskCompletionTrendAsync(int projectId, DateTime startDate, DateTime endDate);
        Task<double> GetProjectVelocityAsync(int projectId, DateTime startDate, DateTime endDate);

        // Team Analytics
        Task<Dictionary<string, decimal>> GetTeamWorkloadAsync(int projectId);
        Task<Dictionary<string, decimal>> GetTeamProductivityAsync(int projectId, DateTime startDate, DateTime endDate);
        Task<IEnumerable<object>> GetTeamUtilizationTrendAsync(int projectId, DateTime startDate, DateTime endDate);

        // Burndown/Burnup Charts
        Task<IEnumerable<object>> GetBurndownChartDataAsync(int projectId, DateTime startDate, DateTime endDate);
        Task<IEnumerable<object>> GetBurnupChartDataAsync(int projectId, DateTime startDate, DateTime endDate);

        // Time Analytics
        Task<Dictionary<string, decimal>> GetTimeSpentByTaskAsync(int projectId);
        Task<Dictionary<string, decimal>> GetTimeSpentByUserAsync(int projectId, DateTime startDate, DateTime endDate);
        Task<decimal> GetProjectTimeEfficiencyAsync(int projectId);

        // Budget Analytics
        Task<Dictionary<string, decimal>> GetBudgetAnalysisAsync(int projectId);
        Task<IEnumerable<object>> GetBudgetTrendAsync(int projectId, DateTime startDate, DateTime endDate);

        // Risk Analytics
        Task<Dictionary<string, int>> GetRiskDistributionAsync(int projectId);
        Task<decimal> GetProjectRiskIndexAsync(int projectId);

        // Milestone Analytics
        Task<IEnumerable<object>> GetMilestoneProgressAsync(int projectId);
        Task<Dictionary<string, object>> GetMilestonePerformanceAsync(int projectId);

        // Comparative Analytics
        Task<IEnumerable<object>> CompareProjectPerformanceAsync(IEnumerable<int> projectIds, DateTime startDate, DateTime endDate);
        Task<Dictionary<string, object>> GetPortfolioOverviewAsync(string userId);

        // Predictive Analytics
        Task<DateTime?> PredictProjectCompletionAsync(int projectId);
        Task<decimal> PredictBudgetOverrunAsync(int projectId);
        Task<IEnumerable<object>> GetProjectHealthIndicatorsAsync(int projectId);
    }
}
