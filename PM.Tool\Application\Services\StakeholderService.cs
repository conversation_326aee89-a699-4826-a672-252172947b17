using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;

namespace PM.Tool.Application.Services
{
    public class StakeholderService : IStakeholderService
    {
        private readonly ApplicationDbContext _context;

        public StakeholderService(ApplicationDbContext context)
        {
            _context = context;
        }

        // Stakeholder Management
        public async Task<IEnumerable<Stakeholder>> GetAllStakeholdersAsync()
        {
            return await _context.Stakeholders
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Stakeholder>> GetActiveStakeholdersAsync()
        {
            return await _context.Stakeholders
                .Where(s => s.IsActive)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<Stakeholder?> GetStakeholderByIdAsync(int id)
        {
            return await _context.Stakeholders
                .Include(s => s.ProjectStakeholders)
                .ThenInclude(ps => ps.Project)
                .FirstOrDefaultAsync(s => s.Id == id);
        }

        public async Task<Stakeholder> CreateStakeholderAsync(Stakeholder stakeholder)
        {
            stakeholder.CreatedAt = DateTime.UtcNow;
            stakeholder.UpdatedAt = DateTime.UtcNow;

            _context.Stakeholders.Add(stakeholder);
            await _context.SaveChangesAsync();
            return stakeholder;
        }

        public async Task<Stakeholder> UpdateStakeholderAsync(Stakeholder stakeholder)
        {
            stakeholder.UpdatedAt = DateTime.UtcNow;

            _context.Stakeholders.Update(stakeholder);
            await _context.SaveChangesAsync();
            return stakeholder;
        }

        public async Task<bool> DeleteStakeholderAsync(int id)
        {
            var stakeholder = await _context.Stakeholders.FindAsync(id);
            if (stakeholder == null) return false;

            _context.Stakeholders.Remove(stakeholder);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> DeactivateStakeholderAsync(int id)
        {
            var stakeholder = await _context.Stakeholders.FindAsync(id);
            if (stakeholder == null) return false;

            stakeholder.IsActive = false;
            stakeholder.UpdatedAt = DateTime.UtcNow;

            return await _context.SaveChangesAsync() > 0;
        }

        // Project Stakeholder Management
        public async Task<IEnumerable<ProjectStakeholder>> GetProjectStakeholdersAsync(int projectId)
        {
            return await _context.ProjectStakeholders
                .Include(ps => ps.Stakeholder)
                .Where(ps => ps.ProjectId == projectId)
                .OrderBy(ps => ps.Stakeholder.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Stakeholder>> GetStakeholdersByProjectAsync(int projectId)
        {
            return await _context.ProjectStakeholders
                .Include(ps => ps.Stakeholder)
                .Where(ps => ps.ProjectId == projectId)
                .Select(ps => ps.Stakeholder)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Stakeholder>> GetKeyStakeholdersAsync(int projectId)
        {
            return await _context.ProjectStakeholders
                .Include(ps => ps.Stakeholder)
                .Where(ps => ps.ProjectId == projectId && ps.IsKeyStakeholder)
                .Select(ps => ps.Stakeholder)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<bool> AddStakeholderToProjectAsync(int projectId, int stakeholderId, string role, bool isKeyStakeholder = false)
        {
            var existingAssignment = await _context.ProjectStakeholders
                .FirstOrDefaultAsync(ps => ps.ProjectId == projectId && ps.StakeholderId == stakeholderId);

            if (existingAssignment != null) return false; // Already assigned

            // Parse the role string to ProjectRole enum
            if (!Enum.TryParse<ProjectRole>(role, out var projectRole))
                projectRole = ProjectRole.Stakeholder;

            var projectStakeholder = new ProjectStakeholder
            {
                ProjectId = projectId,
                StakeholderId = stakeholderId,
                ProjectRole = projectRole,
                IsKeyStakeholder = isKeyStakeholder,
                AssignedDate = DateTime.UtcNow
            };

            _context.ProjectStakeholders.Add(projectStakeholder);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> RemoveStakeholderFromProjectAsync(int projectId, int stakeholderId)
        {
            var projectStakeholder = await _context.ProjectStakeholders
                .FirstOrDefaultAsync(ps => ps.ProjectId == projectId && ps.StakeholderId == stakeholderId);

            if (projectStakeholder == null) return false;

            _context.ProjectStakeholders.Remove(projectStakeholder);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> UpdateStakeholderRoleAsync(int projectId, int stakeholderId, string newRole, bool isKeyStakeholder)
        {
            var projectStakeholder = await _context.ProjectStakeholders
                .FirstOrDefaultAsync(ps => ps.ProjectId == projectId && ps.StakeholderId == stakeholderId);

            if (projectStakeholder == null) return false;

            // Parse the role string to ProjectRole enum
            if (Enum.TryParse<ProjectRole>(newRole, out var projectRole))
                projectStakeholder.ProjectRole = projectRole;

            projectStakeholder.IsKeyStakeholder = isKeyStakeholder;

            return await _context.SaveChangesAsync() > 0;
        }

        // Communication Management
        public async Task<IEnumerable<StakeholderCommunication>> GetStakeholderCommunicationsAsync(int stakeholderId)
        {
            return await _context.StakeholderCommunications
                .Include(c => c.CommunicatedBy)
                .Where(c => c.StakeholderId == stakeholderId)
                .OrderByDescending(c => c.CommunicationDate)
                .ToListAsync();
        }

        public async Task<StakeholderCommunication> LogCommunicationAsync(StakeholderCommunication communication)
        {
            communication.CommunicationDate = DateTime.UtcNow;

            _context.StakeholderCommunications.Add(communication);
            await _context.SaveChangesAsync();
            return communication;
        }

        public async Task<bool> UpdateCommunicationAsync(StakeholderCommunication communication)
        {
            _context.StakeholderCommunications.Update(communication);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> DeleteCommunicationAsync(int communicationId)
        {
            var communication = await _context.StakeholderCommunications.FindAsync(communicationId);
            if (communication == null) return false;

            _context.StakeholderCommunications.Remove(communication);
            return await _context.SaveChangesAsync() > 0;
        }

        // Communication Planning
        public async Task<IEnumerable<StakeholderCommunication>> GetPendingFollowUpsAsync(string userId)
        {
            return await _context.StakeholderCommunications
                .Include(c => c.Stakeholder)
                .Where(c => c.CommunicatedByUserId == userId &&
                           c.FollowUpDate.HasValue &&
                           c.FollowUpDate.Value <= DateTime.UtcNow &&
                           c.RequiresFollowUp)
                .OrderBy(c => c.FollowUpDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<StakeholderCommunication>> GetCommunicationsByDateRangeAsync(int projectId, DateTime startDate, DateTime endDate)
        {
            return await _context.StakeholderCommunications
                .Include(c => c.Stakeholder)
                .Include(c => c.CommunicatedBy)
                .Where(c => c.ProjectId == projectId &&
                           c.CommunicationDate >= startDate &&
                           c.CommunicationDate <= endDate)
                .OrderByDescending(c => c.CommunicationDate)
                .ToListAsync();
        }

        public async Task<bool> ScheduleFollowUpAsync(int communicationId, DateTime followUpDate)
        {
            var communication = await _context.StakeholderCommunications.FindAsync(communicationId);
            if (communication == null) return false;

            communication.FollowUpDate = followUpDate;
            communication.RequiresFollowUp = true;

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> CompleteFollowUpAsync(int communicationId)
        {
            var communication = await _context.StakeholderCommunications.FindAsync(communicationId);
            if (communication == null) return false;

            communication.RequiresFollowUp = false;

            return await _context.SaveChangesAsync() > 0;
        }

        // Stakeholder Engagement
        public async Task<Dictionary<string, object>> GetStakeholderEngagementMetricsAsync(int projectId)
        {
            var totalStakeholders = await _context.ProjectStakeholders
                .CountAsync(ps => ps.ProjectId == projectId);

            var activeCommunications = await _context.StakeholderCommunications
                .CountAsync(c => c.ProjectId == projectId &&
                               c.CommunicationDate >= DateTime.UtcNow.AddDays(-30));

            var keyStakeholders = await _context.ProjectStakeholders
                .CountAsync(ps => ps.ProjectId == projectId && ps.IsKeyStakeholder);

            return new Dictionary<string, object>
            {
                ["TotalStakeholders"] = totalStakeholders,
                ["KeyStakeholders"] = keyStakeholders,
                ["RecentCommunications"] = activeCommunications,
                ["EngagementRate"] = totalStakeholders > 0 ? (double)activeCommunications / totalStakeholders : 0
            };
        }

        public async Task<IEnumerable<object>> GetCommunicationFrequencyReportAsync(int projectId, DateTime startDate, DateTime endDate)
        {
            var communications = await _context.StakeholderCommunications
                .Include(c => c.Stakeholder)
                .Where(c => c.ProjectId == projectId &&
                           c.CommunicationDate >= startDate &&
                           c.CommunicationDate <= endDate)
                .GroupBy(c => c.StakeholderId)
                .Select(g => new
                {
                    StakeholderId = g.Key,
                    StakeholderName = g.First().Stakeholder.Name,
                    CommunicationCount = g.Count(),
                    LastCommunication = g.Max(c => c.CommunicationDate)
                })
                .ToListAsync();

            return communications.Cast<object>();
        }

        public async Task<IEnumerable<Stakeholder>> GetInactiveStakeholdersAsync(int projectId, int daysSinceLastCommunication = 30)
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-daysSinceLastCommunication);

            var inactiveStakeholderIds = await _context.ProjectStakeholders
                .Where(ps => ps.ProjectId == projectId)
                .Where(ps => !_context.StakeholderCommunications
                    .Any(c => c.StakeholderId == ps.StakeholderId &&
                             c.ProjectId == projectId &&
                             c.CommunicationDate >= cutoffDate))
                .Select(ps => ps.StakeholderId)
                .ToListAsync();

            return await _context.Stakeholders
                .Where(s => inactiveStakeholderIds.Contains(s.Id))
                .ToListAsync();
        }

        public async Task<IEnumerable<Stakeholder>> GetStakeholdersByInfluenceAsync(int projectId, string influenceLevel)
        {
            if (Enum.TryParse<InfluenceLevel>(influenceLevel, out var influence))
            {
                return await _context.ProjectStakeholders
                    .Include(ps => ps.Stakeholder)
                    .Where(ps => ps.ProjectId == projectId && ps.Stakeholder.Influence == influence)
                    .Select(ps => ps.Stakeholder)
                    .OrderBy(s => s.Name)
                    .ToListAsync();
            }
            return new List<Stakeholder>();
        }

        public async Task<IEnumerable<Stakeholder>> GetStakeholdersByInterestAsync(int projectId, string interestLevel)
        {
            if (Enum.TryParse<InterestLevel>(interestLevel, out var interest))
            {
                return await _context.ProjectStakeholders
                    .Include(ps => ps.Stakeholder)
                    .Where(ps => ps.ProjectId == projectId && ps.Stakeholder.Interest == interest)
                    .Select(ps => ps.Stakeholder)
                    .OrderBy(s => s.Name)
                    .ToListAsync();
            }
            return new List<Stakeholder>();
        }

        // Stakeholder Notifications
        public async Task<bool> NotifyStakeholdersAsync(int projectId, string subject, string message, IEnumerable<int>? stakeholderIds = null)
        {
            // TODO: Implement stakeholder notifications
            // This would integrate with the notification service
            return true;
        }

        public async Task<bool> SendProjectUpdateAsync(int projectId, string updateContent, bool keyStakeholdersOnly = false)
        {
            // TODO: Implement project update notifications
            return true;
        }

        public async Task<IEnumerable<Stakeholder>> GetStakeholdersForNotificationAsync(int projectId, bool keyStakeholdersOnly = false)
        {
            var query = _context.ProjectStakeholders
                .Include(ps => ps.Stakeholder)
                .Where(ps => ps.ProjectId == projectId);

            if (keyStakeholdersOnly)
                query = query.Where(ps => ps.IsKeyStakeholder);

            return await query
                .Select(ps => ps.Stakeholder)
                .Where(s => s.IsActive && !string.IsNullOrEmpty(s.Email))
                .ToListAsync();
        }

        // Stakeholder Reporting
        public async Task<Dictionary<string, object>> GetStakeholderReportAsync(int projectId)
        {
            var stakeholders = await GetProjectStakeholdersAsync(projectId);
            var communications = await _context.StakeholderCommunications
                .CountAsync(c => c.ProjectId == projectId);

            var influenceDistribution = stakeholders
                .GroupBy(s => s.Stakeholder.Influence)
                .ToDictionary(g => g.Key.ToString(), g => g.Count());

            var interestDistribution = stakeholders
                .GroupBy(s => s.Stakeholder.Interest)
                .ToDictionary(g => g.Key.ToString(), g => g.Count());

            return new Dictionary<string, object>
            {
                ["TotalStakeholders"] = stakeholders.Count(),
                ["TotalCommunications"] = communications,
                ["InfluenceDistribution"] = influenceDistribution,
                ["InterestDistribution"] = interestDistribution
            };
        }

        public async Task<IEnumerable<object>> GetStakeholderEngagementReportAsync(int projectId, DateTime startDate, DateTime endDate)
        {
            // TODO: Implement detailed engagement report
            return new List<object>();
        }

        public async Task<IEnumerable<object>> GetCommunicationEffectivenessReportAsync(int projectId)
        {
            // TODO: Implement communication effectiveness metrics
            return new List<object>();
        }

        // Missing interface methods
        public async Task<ProjectStakeholder> AssignStakeholderToProjectAsync(int projectId, int stakeholderId, ProjectRole role, bool isKeyStakeholder = false)
        {
            var projectStakeholder = new ProjectStakeholder
            {
                ProjectId = projectId,
                StakeholderId = stakeholderId,
                ProjectRole = role,
                IsKeyStakeholder = isKeyStakeholder,
                AssignedDate = DateTime.UtcNow
            };

            _context.ProjectStakeholders.Add(projectStakeholder);
            await _context.SaveChangesAsync();
            return projectStakeholder;
        }

        public async Task<bool> UpdateProjectStakeholderRoleAsync(int projectId, int stakeholderId, ProjectRole newRole)
        {
            var projectStakeholder = await _context.ProjectStakeholders
                .FirstOrDefaultAsync(ps => ps.ProjectId == projectId && ps.StakeholderId == stakeholderId);

            if (projectStakeholder == null) return false;

            projectStakeholder.ProjectRole = newRole;

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<IEnumerable<Stakeholder>> GetStakeholdersByInfluenceAsync(InfluenceLevel influence)
        {
            return await _context.Stakeholders
                .Where(s => s.Influence == influence)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Stakeholder>> GetStakeholdersByInterestAsync(InterestLevel interest)
        {
            return await _context.Stakeholders
                .Where(s => s.Interest == interest)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Stakeholder>> GetStakeholdersByPriorityAsync(StakeholderPriority priority)
        {
            // TODO: Implement when StakeholderPriority property is added to Stakeholder entity
            return await _context.Stakeholders
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<Dictionary<StakeholderPriority, int>> GetStakeholderPriorityDistributionAsync(int projectId)
        {
            // TODO: Implement when StakeholderPriority property is added to Stakeholder entity
            return new Dictionary<StakeholderPriority, int>();
        }

        public async Task<IEnumerable<object>> GetStakeholderInfluenceInterestMatrixAsync(int projectId)
        {
            var stakeholders = await GetStakeholdersByProjectAsync(projectId);

            return stakeholders.Select(s => new
            {
                Id = s.Id,
                Name = s.Name,
                Influence = s.Influence,
                Interest = s.Interest,
                Organization = s.Organization
            }).Cast<object>().ToList();
        }

        public async Task<IEnumerable<StakeholderCommunication>> GetProjectCommunicationsAsync(int projectId)
        {
            return await _context.StakeholderCommunications
                .Include(c => c.Stakeholder)
                .Include(c => c.CommunicatedBy)
                .Where(c => c.ProjectId == projectId)
                .OrderByDescending(c => c.CommunicationDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Stakeholder>> SearchStakeholdersAsync(string searchTerm)
        {
            return await _context.Stakeholders
                .Where(s => s.Name.Contains(searchTerm) ||
                           s.Email.Contains(searchTerm) ||
                           s.Organization.Contains(searchTerm))
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Stakeholder>> FilterStakeholdersAsync(StakeholderType? type = null, StakeholderRole? role = null, InfluenceLevel? influence = null, InterestLevel? interest = null, bool? isActive = null)
        {
            var query = _context.Stakeholders.AsQueryable();

            if (type.HasValue)
                query = query.Where(s => s.Type == type.Value);

            if (influence.HasValue)
                query = query.Where(s => s.Influence == influence.Value);

            if (interest.HasValue)
                query = query.Where(s => s.Interest == interest.Value);

            if (isActive.HasValue)
                query = query.Where(s => s.IsActive == isActive.Value);

            return await query.OrderBy(s => s.Name).ToListAsync();
        }

        public async Task<IEnumerable<Stakeholder>> GetStakeholdersByOrganizationAsync(string organization)
        {
            return await _context.Stakeholders
                .Where(s => s.Organization == organization)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<bool> ImportStakeholdersAsync(IEnumerable<Stakeholder> stakeholders)
        {
            foreach (var stakeholder in stakeholders)
            {
                stakeholder.CreatedAt = DateTime.UtcNow;
                stakeholder.UpdatedAt = DateTime.UtcNow;
                _context.Stakeholders.Add(stakeholder);
            }

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<IEnumerable<Stakeholder>> ExportProjectStakeholdersAsync(int projectId)
        {
            return await GetStakeholdersByProjectAsync(projectId);
        }

        public async Task<bool> ValidateStakeholderEmailAsync(string email)
        {
            // Simple email validation
            return !string.IsNullOrEmpty(email) && email.Contains("@") && email.Contains(".");
        }

        public async Task<bool> CheckDuplicateStakeholderAsync(string email, int? excludeId = null)
        {
            var query = _context.Stakeholders.Where(s => s.Email == email);

            if (excludeId.HasValue)
                query = query.Where(s => s.Id != excludeId.Value);

            return await query.AnyAsync();
        }

        public async Task<IEnumerable<string>> ValidateStakeholderDataAsync(Stakeholder stakeholder)
        {
            var errors = new List<string>();

            if (string.IsNullOrEmpty(stakeholder.Name))
                errors.Add("Name is required");

            if (string.IsNullOrEmpty(stakeholder.Email))
                errors.Add("Email is required");
            else if (!await ValidateStakeholderEmailAsync(stakeholder.Email))
                errors.Add("Invalid email format");

            if (await CheckDuplicateStakeholderAsync(stakeholder.Email, stakeholder.Id))
                errors.Add("Email already exists");

            return errors;
        }
    }
}
