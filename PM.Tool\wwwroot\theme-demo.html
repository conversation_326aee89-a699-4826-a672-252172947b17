<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enterprise Theme Demo - PM Tool</title>
    <link rel="stylesheet" href="css/saas-theme.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/tailwind.css">
    <style>
        body {
            font-family: var(--font-family);
            background-color: var(--color-bg);
            color: var(--color-text-primary);
            margin: 0;
            padding: 2rem;
            transition: all 0.3s ease;
        }
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .demo-section {
            margin-bottom: 3rem;
        }
        .demo-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--color-text-primary);
        }
        .demo-subtitle {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--color-text-secondary);
        }
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .theme-toggle {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="theme-toggle">
        <button id="themeToggle" class="btn-enterprise btn-primary-enterprise">
            🌙 Toggle Dark Mode
        </button>
    </div>

    <div class="demo-container">
        <header class="demo-section">
            <h1 class="demo-title">Enterprise Theme System Demo</h1>
            <p class="demo-subtitle">Professional, accessible, and responsive component library</p>
        </header>

        <!-- Cards Demo -->
        <section class="demo-section">
            <h2 class="demo-subtitle">Enhanced Cards</h2>
            <div class="demo-grid">
                <div class="card-enterprise">
                    <div class="card-header-enterprise">
                        <h3 class="card-title-enterprise">Project Alpha</h3>
                        <div class="status-pill status-in-progress">In Progress</div>
                    </div>
                    <div class="card-body-enterprise">
                        <p>This is a sample project card with enhanced styling for both light and dark modes. Notice the subtle shadows and smooth transitions.</p>
                    </div>
                    <div class="card-footer-enterprise">
                        <div class="priority-indicator priority-high">High Priority</div>
                        <div class="user-avatar">JD</div>
                    </div>
                </div>

                <div class="card-enterprise">
                    <div class="card-header-enterprise">
                        <h3 class="card-title-enterprise">Task Management</h3>
                        <div class="status-pill status-completed">Completed</div>
                    </div>
                    <div class="card-body-enterprise">
                        <p>Completed task with success indicators. The design maintains consistency across different states and themes.</p>
                    </div>
                    <div class="card-footer-enterprise">
                        <div class="priority-indicator priority-low">Low Priority</div>
                        <div class="user-avatar">SM</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Status Pills Demo -->
        <section class="demo-section">
            <h2 class="demo-subtitle">Status Pills</h2>
            <div style="display: flex; gap: 1rem; flex-wrap: wrap; margin-bottom: 1rem;">
                <div class="status-pill status-todo">To Do</div>
                <div class="status-pill status-in-progress">In Progress</div>
                <div class="status-pill status-completed">Completed</div>
                <div class="status-pill status-blocked">Blocked</div>
                <div class="status-pill status-on-hold">On Hold</div>
            </div>
        </section>

        <!-- Priority Indicators Demo -->
        <section class="demo-section">
            <h2 class="demo-subtitle">Priority Indicators</h2>
            <div style="display: flex; gap: 1rem; flex-wrap: wrap; margin-bottom: 1rem;">
                <div class="priority-indicator priority-low">Low Priority</div>
                <div class="priority-indicator priority-medium">Medium Priority</div>
                <div class="priority-indicator priority-high">High Priority</div>
                <div class="priority-indicator priority-critical">Critical Priority</div>
            </div>
        </section>

        <!-- Forms Demo -->
        <section class="demo-section">
            <h2 class="demo-subtitle">Enhanced Forms</h2>
            <div class="demo-grid">
                <div>
                    <div class="form-group-enterprise">
                        <label class="form-label-enterprise" for="demo-input">Project Name</label>
                        <input type="text" id="demo-input" class="form-input-enterprise" placeholder="Enter project name">
                        <div class="form-help-text">Choose a descriptive name for your project</div>
                    </div>
                    <div class="form-group-enterprise">
                        <label class="form-label-enterprise" for="demo-input-valid">Valid Input</label>
                        <input type="text" id="demo-input-valid" class="form-input-enterprise is-valid" value="Valid input example">
                        <div class="form-success-text">This input is valid</div>
                    </div>
                </div>
                <div>
                    <div class="form-group-enterprise">
                        <label class="form-label-enterprise" for="demo-input-error">Error Input</label>
                        <input type="text" id="demo-input-error" class="form-input-enterprise is-invalid" value="Invalid input">
                        <div class="form-error-text">This field is required</div>
                    </div>
                    <div class="form-group-enterprise">
                        <label class="form-label-enterprise" for="demo-input-disabled">Disabled Input</label>
                        <input type="text" id="demo-input-disabled" class="form-input-enterprise" disabled value="Disabled input">
                    </div>
                </div>
            </div>
        </section>

        <!-- Alerts Demo -->
        <section class="demo-section">
            <h2 class="demo-subtitle">Professional Alerts</h2>
            <div class="alert-enterprise alert-success-enterprise">
                <div class="alert-icon">✓</div>
                <div class="alert-content">
                    <div class="alert-title">Success!</div>
                    <div class="alert-message">Your project has been created successfully.</div>
                </div>
            </div>
            <div class="alert-enterprise alert-warning-enterprise">
                <div class="alert-icon">⚠</div>
                <div class="alert-content">
                    <div class="alert-title">Warning</div>
                    <div class="alert-message">Please review your project settings before proceeding.</div>
                </div>
            </div>
            <div class="alert-enterprise alert-danger-enterprise">
                <div class="alert-icon">✕</div>
                <div class="alert-content">
                    <div class="alert-title">Error</div>
                    <div class="alert-message">Unable to save changes. Please try again.</div>
                </div>
            </div>
            <div class="alert-enterprise alert-info-enterprise">
                <div class="alert-icon">ℹ</div>
                <div class="alert-content">
                    <div class="alert-title">Information</div>
                    <div class="alert-message">New features are available in this update.</div>
                </div>
            </div>
        </section>

        <!-- Buttons Demo -->
        <section class="demo-section">
            <h2 class="demo-subtitle">Enhanced Buttons</h2>
            <div style="display: flex; gap: 1rem; flex-wrap: wrap; margin-bottom: 1rem;">
                <button class="btn-enterprise btn-primary-enterprise">Primary Button</button>
                <button class="btn-enterprise btn-secondary-enterprise">Secondary Button</button>
                <button class="btn-enterprise btn-primary-enterprise" disabled>Disabled Button</button>
            </div>
        </section>

        <!-- Loading States Demo -->
        <section class="demo-section">
            <h2 class="demo-subtitle">Loading States</h2>
            <div class="demo-grid">
                <div class="card-enterprise">
                    <div class="loading-skeleton skeleton-title"></div>
                    <div class="loading-skeleton skeleton-text"></div>
                    <div class="loading-skeleton skeleton-text" style="width: 80%;"></div>
                    <div class="loading-skeleton skeleton-button"></div>
                </div>
            </div>
        </section>
    </div>

    <script>
        // Theme toggle functionality
        const themeToggle = document.getElementById('themeToggle');
        const html = document.documentElement;
        
        themeToggle.addEventListener('click', () => {
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            html.setAttribute('data-theme', newTheme);
            themeToggle.textContent = newTheme === 'dark' ? '☀️ Toggle Light Mode' : '🌙 Toggle Dark Mode';
            
            // Save preference
            localStorage.setItem('theme-preference', newTheme);
        });
        
        // Load saved theme
        const savedTheme = localStorage.getItem('theme-preference') || 'light';
        html.setAttribute('data-theme', savedTheme);
        themeToggle.textContent = savedTheme === 'dark' ? '☀️ Toggle Light Mode' : '🌙 Toggle Dark Mode';
    </script>
</body>
</html>
