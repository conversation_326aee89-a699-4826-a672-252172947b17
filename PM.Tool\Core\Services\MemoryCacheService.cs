using Microsoft.Extensions.Caching.Memory;
using PM.Tool.Core.Interfaces;
using System.Collections.Concurrent;
using System.Text.Json;

namespace PM.Tool.Core.Services
{
    public class MemoryCacheService : ICacheService
    {
        private readonly IMemoryCache _cache;
        private readonly ConcurrentDictionary<string, HashSet<string>> _keysByPrefix;

        public MemoryCacheService(IMemoryCache cache)
        {
            _cache = cache;
            _keysByPrefix = new ConcurrentDictionary<string, HashSet<string>>();
        }

        public Task<T?> GetAsync<T>(string key)
        {
            return Task.FromResult(_cache.TryGetValue<T>(key, out var value) ? value : default);
        }

        public Task SetAsync<T>(string key, T value, TimeSpan? expiration = null)
        {
            var options = new MemoryCacheEntryOptions();
            
            if (expiration.HasValue)
                options.AbsoluteExpirationRelativeToNow = expiration;
            else
                options.SlidingExpiration = TimeSpan.FromMinutes(30);

            _cache.Set(key, value, options);

            // Track key by prefix for pattern-based removal
            var prefix = GetPrefix(key);
            _keysByPrefix.AddOrUpdate(
                prefix,
                new HashSet<string> { key },
                (_, oldSet) =>
                {
                    oldSet.Add(key);
                    return oldSet;
                });

            return Task.CompletedTask;
        }

        public Task RemoveAsync(string key)
        {
            _cache.Remove(key);
            var prefix = GetPrefix(key);
            if (_keysByPrefix.TryGetValue(prefix, out var keys))
            {
                keys.Remove(key);
            }
            return Task.CompletedTask;
        }

        public Task RemoveByPrefixAsync(string prefix)
        {
            if (_keysByPrefix.TryGetValue(prefix, out var keys))
            {
                foreach (var key in keys)
                {
                    _cache.Remove(key);
                }
                keys.Clear();
            }
            return Task.CompletedTask;
        }

        private string GetPrefix(string key)
        {
            var parts = key.Split(':');
            return parts.Length > 1 ? parts[0] : string.Empty;
        }
    }
}
