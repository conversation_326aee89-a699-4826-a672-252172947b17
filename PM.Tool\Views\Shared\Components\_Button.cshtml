@{
    var text = ViewData["Text"]?.ToString() ?? "Button";
    var variant = ViewData["Variant"]?.ToString() ?? "primary"; // primary, secondary, outline, danger, success
    var size = ViewData["Size"]?.ToString() ?? "md"; // sm, md, lg
    var icon = ViewData["Icon"]?.ToString();
    var iconPosition = ViewData["IconPosition"]?.ToString() ?? "left"; // left, right
    var href = ViewData["Href"]?.ToString();
    var onclick = ViewData["OnClick"]?.ToString();
    var type = ViewData["Type"]?.ToString() ?? "button";
    var disabled = ViewData["Disabled"] as bool? ?? false;
    var fullWidth = ViewData["FullWidth"] as bool? ?? false;
    var loading = ViewData["Loading"] as bool? ?? false;
    var additionalClasses = ViewData["AdditionalClasses"]?.ToString() ?? "";
    var id = ViewData["Id"]?.ToString();
    var ariaLabel = ViewData["AriaLabel"]?.ToString();
    var title = ViewData["Title"]?.ToString();

    var baseClasses = "inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed";

    var variantClasses = variant switch {
        "primary" => "btn-primary-custom",
        "secondary" => "btn-secondary-custom",
        "outline" => "btn-outline-custom",
        "danger" => "btn-danger-custom",
        "success" => "btn-success-custom",
        _ => "btn-primary-custom"
    };

    var sizeClasses = size switch {
        "sm" => "px-3 py-1.5 text-sm rounded-md",
        "md" => "px-4 py-2.5 text-sm rounded-lg",
        "lg" => "px-6 py-3 text-base rounded-lg",
        _ => "px-4 py-2.5 text-sm rounded-lg"
    };

    var widthClass = fullWidth ? "w-full" : "";

    var allClasses = $"{baseClasses} {variantClasses} {sizeClasses} {widthClass} {additionalClasses}".Trim();
}

@if (!string.IsNullOrEmpty(href))
{
        var linkHtml = $"<a href=\"{href}\" class=\"{allClasses}\"";
        if (!string.IsNullOrEmpty(id)) linkHtml += $" id=\"{id}\"";
        if (!string.IsNullOrEmpty(ariaLabel)) linkHtml += $" aria-label=\"{Html.Encode(ariaLabel)}\"";
        if (!string.IsNullOrEmpty(onclick)) linkHtml += $" onclick=\"{Html.Encode(onclick)}\"";
        if (!string.IsNullOrEmpty(title)) linkHtml += $" title=\"{Html.Encode(title)}\"";
        linkHtml += ">";

        if (loading)
        {
            linkHtml += "<div class=\"animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2\"></div>";
        }
        else if (!string.IsNullOrEmpty(icon) && iconPosition == "left")
        {
            linkHtml += $"<i class=\"{icon} mr-2\"></i>";
        }

        linkHtml += $"<span>{Html.Encode(text)}</span>";

        if (!string.IsNullOrEmpty(icon) && iconPosition == "right")
        {
            linkHtml += $"<i class=\"{icon} ml-2\"></i>";
        }

        linkHtml += "</a>";
    @Html.Raw(linkHtml)
}
else
{
        var buttonHtml = $"<button type=\"{type}\" class=\"{allClasses}\"";
        if (!string.IsNullOrEmpty(id)) buttonHtml += $" id=\"{id}\"";
        if (!string.IsNullOrEmpty(ariaLabel)) buttonHtml += $" aria-label=\"{Html.Encode(ariaLabel)}\"";
        if (!string.IsNullOrEmpty(onclick)) buttonHtml += $" onclick=\"{Html.Encode(onclick)}\"";
        if (!string.IsNullOrEmpty(title)) buttonHtml += $" title=\"{Html.Encode(title)}\"";
        if (disabled) buttonHtml += " disabled";
        buttonHtml += ">";

        if (loading)
        {
            buttonHtml += "<div class=\"animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2\"></div>";
        }
        else if (!string.IsNullOrEmpty(icon) && iconPosition == "left")
        {
            buttonHtml += $"<i class=\"{icon} mr-2\"></i>";
        }

        buttonHtml += $"<span>{Html.Encode(text)}</span>";

        if (!string.IsNullOrEmpty(icon) && iconPosition == "right")
        {
            buttonHtml += $"<i class=\"{icon} ml-2\"></i>";
        }

        buttonHtml += "</button>";
    @Html.Raw(buttonHtml)
}
