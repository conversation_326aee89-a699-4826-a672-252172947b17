<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>

  <!-- Common UI Elements - German -->
  <data name="Common.Save" xml:space="preserve">
    <value>Speichern</value>
  </data>
  <data name="Common.Cancel" xml:space="preserve">
    <value>Abbrechen</value>
  </data>
  <data name="Common.Delete" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="Common.Edit" xml:space="preserve">
    <value>Bearbeiten</value>
  </data>
  <data name="Common.Create" xml:space="preserve">
    <value>Erstellen</value>
  </data>
  <data name="Common.Update" xml:space="preserve">
    <value>Aktualisieren</value>
  </data>
  <data name="Common.Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="Common.Back" xml:space="preserve">
    <value>Zurück</value>
  </data>
  <data name="Common.Next" xml:space="preserve">
    <value>Weiter</value>
  </data>
  <data name="Common.Previous" xml:space="preserve">
    <value>Vorherige</value>
  </data>
  <data name="Common.Search" xml:space="preserve">
    <value>Suchen</value>
  </data>
  <data name="Common.Filter" xml:space="preserve">
    <value>Filtern</value>
  </data>
  <data name="Common.Export" xml:space="preserve">
    <value>Exportieren</value>
  </data>
  <data name="Common.Import" xml:space="preserve">
    <value>Importieren</value>
  </data>
  <data name="Common.Print" xml:space="preserve">
    <value>Drucken</value>
  </data>
  <data name="Common.Download" xml:space="preserve">
    <value>Herunterladen</value>
  </data>
  <data name="Common.Upload" xml:space="preserve">
    <value>Hochladen</value>
  </data>
  <data name="Common.Yes" xml:space="preserve">
    <value>Ja</value>
  </data>
  <data name="Common.No" xml:space="preserve">
    <value>Nein</value>
  </data>
  <data name="Common.OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Common.Close" xml:space="preserve">
    <value>Schließen</value>
  </data>
  <data name="Common.Loading" xml:space="preserve">
    <value>Laden...</value>
  </data>
  <data name="Common.NoData" xml:space="preserve">
    <value>Keine Daten verfügbar</value>
  </data>
  <data name="Common.Error" xml:space="preserve">
    <value>Fehler</value>
  </data>
  <data name="Common.Success" xml:space="preserve">
    <value>Erfolg</value>
  </data>
  <data name="Common.Warning" xml:space="preserve">
    <value>Warnung</value>
  </data>
  <data name="Common.Information" xml:space="preserve">
    <value>Information</value>
  </data>

  <!-- Navigation - German -->
  <data name="Nav.Dashboard" xml:space="preserve">
    <value>Dashboard</value>
  </data>
  <data name="Nav.Projects" xml:space="preserve">
    <value>Projekte</value>
  </data>
  <data name="Nav.Tasks" xml:space="preserve">
    <value>Aufgaben</value>
  </data>
  <data name="Nav.Management" xml:space="preserve">
    <value>Verwaltung</value>
  </data>
  <data name="Nav.Analytics" xml:space="preserve">
    <value>Analytik</value>
  </data>
  <data name="Nav.Resources" xml:space="preserve">
    <value>Ressourcen</value>
  </data>
  <data name="Nav.Risks" xml:space="preserve">
    <value>Risiken</value>
  </data>
  <data name="Nav.Meetings" xml:space="preserve">
    <value>Besprechungen</value>
  </data>
  <data name="Nav.Requirements" xml:space="preserve">
    <value>Anforderungen</value>
  </data>
  <data name="Nav.Backlog" xml:space="preserve">
    <value>Backlog</value>
  </data>
  <data name="Nav.Kanban" xml:space="preserve">
    <value>Kanban-Board</value>
  </data>
  <data name="Nav.Documentation" xml:space="preserve">
    <value>Dokumentation</value>
  </data>
  <data name="Nav.SkillsManagement" xml:space="preserve">
    <value>Kompetenzmanagement</value>
  </data>
  <data name="Nav.ResourceUtilization" xml:space="preserve">
    <value>Ressourcenauslastung</value>
  </data>
  <data name="Nav.MeetingCalendar" xml:space="preserve">
    <value>Besprechungskalender</value>
  </data>
  <data name="Nav.ActionItems" xml:space="preserve">
    <value>Aktionspunkte</value>
  </data>
  <data name="Nav.AnalyticsDashboard" xml:space="preserve">
    <value>Analytik-Dashboard</value>
  </data>
  <data name="Nav.AdvancedReports" xml:space="preserve">
    <value>Erweiterte Berichte</value>
  </data>
  <data name="Nav.TeamAnalytics" xml:space="preserve">
    <value>Team-Analytik</value>
  </data>
  <data name="Nav.BurndownCharts" xml:space="preserve">
    <value>Burndown-Diagramme</value>
  </data>
  <data name="Nav.VelocityCharts" xml:space="preserve">
    <value>Velocity-Diagramme</value>
  </data>
  <data name="Nav.ExportData" xml:space="preserve">
    <value>Daten exportieren</value>
  </data>

  <!-- Project Management - German -->
  <data name="Project.Title" xml:space="preserve">
    <value>Titel</value>
  </data>
  <data name="Project.Description" xml:space="preserve">
    <value>Beschreibung</value>
  </data>
  <data name="Project.StartDate" xml:space="preserve">
    <value>Startdatum</value>
  </data>
  <data name="Project.EndDate" xml:space="preserve">
    <value>Enddatum</value>
  </data>
  <data name="Project.Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="Project.Priority" xml:space="preserve">
    <value>Priorität</value>
  </data>
  <data name="Project.Budget" xml:space="preserve">
    <value>Budget</value>
  </data>
  <data name="Project.Progress" xml:space="preserve">
    <value>Fortschritt</value>
  </data>
  <data name="Project.Manager" xml:space="preserve">
    <value>Projektleiter</value>
  </data>
  <data name="Project.Team" xml:space="preserve">
    <value>Team</value>
  </data>

  <!-- Task Management - German -->
  <data name="Task.Title" xml:space="preserve">
    <value>Aufgabentitel</value>
  </data>
  <data name="Task.Description" xml:space="preserve">
    <value>Aufgabenbeschreibung</value>
  </data>
  <data name="Task.AssignedTo" xml:space="preserve">
    <value>Zugewiesen an</value>
  </data>
  <data name="Task.DueDate" xml:space="preserve">
    <value>Fälligkeitsdatum</value>
  </data>
  <data name="Task.EstimatedHours" xml:space="preserve">
    <value>Geschätzte Stunden</value>
  </data>
  <data name="Task.ActualHours" xml:space="preserve">
    <value>Tatsächliche Stunden</value>
  </data>
  <data name="Task.StoryPoints" xml:space="preserve">
    <value>Story Points</value>
  </data>

  <!-- Agile Terms - German -->
  <data name="Agile.Epic" xml:space="preserve">
    <value>Epic</value>
  </data>
  <data name="Agile.UserStory" xml:space="preserve">
    <value>User Story</value>
  </data>
  <data name="Agile.Sprint" xml:space="preserve">
    <value>Sprint</value>
  </data>
  <data name="Agile.Backlog" xml:space="preserve">
    <value>Backlog</value>
  </data>
  <data name="Agile.Kanban" xml:space="preserve">
    <value>Kanban</value>
  </data>
  <data name="Agile.Scrum" xml:space="preserve">
    <value>Scrum</value>
  </data>
  <data name="Agile.Velocity" xml:space="preserve">
    <value>Velocity</value>
  </data>
  <data name="Agile.Burndown" xml:space="preserve">
    <value>Burndown</value>
  </data>

  <!-- Status Values - German -->
  <data name="Status.Active" xml:space="preserve">
    <value>Aktiv</value>
  </data>
  <data name="Status.Inactive" xml:space="preserve">
    <value>Inaktiv</value>
  </data>
  <data name="Status.Completed" xml:space="preserve">
    <value>Abgeschlossen</value>
  </data>
  <data name="Status.InProgress" xml:space="preserve">
    <value>In Bearbeitung</value>
  </data>
  <data name="Status.Pending" xml:space="preserve">
    <value>Ausstehend</value>
  </data>
  <data name="Status.Cancelled" xml:space="preserve">
    <value>Abgebrochen</value>
  </data>

  <!-- Priority Values - German -->
  <data name="Priority.Critical" xml:space="preserve">
    <value>Kritisch</value>
  </data>
  <data name="Priority.High" xml:space="preserve">
    <value>Hoch</value>
  </data>
  <data name="Priority.Medium" xml:space="preserve">
    <value>Mittel</value>
  </data>
  <data name="Priority.Low" xml:space="preserve">
    <value>Niedrig</value>
  </data>

  <!-- Messages - German -->
  <data name="Message.SaveSuccess" xml:space="preserve">
    <value>Element erfolgreich gespeichert</value>
  </data>
  <data name="Message.DeleteSuccess" xml:space="preserve">
    <value>Element erfolgreich gelöscht</value>
  </data>
  <data name="Message.UpdateSuccess" xml:space="preserve">
    <value>Element erfolgreich aktualisiert</value>
  </data>
  <data name="Message.ErrorOccurred" xml:space="preserve">
    <value>Ein Fehler ist aufgetreten. Bitte versuchen Sie es erneut.</value>
  </data>
  <data name="Message.ConfirmDelete" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie dieses Element löschen möchten?</value>
  </data>

  <!-- Meeting Management - German -->
  <data name="Meeting.Title" xml:space="preserve">
    <value>Besprechungstitel</value>
  </data>
  <data name="Meeting.Description" xml:space="preserve">
    <value>Beschreibung</value>
  </data>
  <data name="Meeting.StartTime" xml:space="preserve">
    <value>Startzeit</value>
  </data>
  <data name="Meeting.EndTime" xml:space="preserve">
    <value>Endzeit</value>
  </data>
  <data name="Meeting.Location" xml:space="preserve">
    <value>Ort</value>
  </data>
  <data name="Meeting.Type" xml:space="preserve">
    <value>Besprechungstyp</value>
  </data>
  <data name="Meeting.Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="Meeting.Organizer" xml:space="preserve">
    <value>Organisator</value>
  </data>
  <data name="Meeting.Attendees" xml:space="preserve">
    <value>Teilnehmer</value>
  </data>
  <data name="Meeting.ActionItems" xml:space="preserve">
    <value>Aktionspunkte</value>
  </data>
  <data name="Meeting.Documents" xml:space="preserve">
    <value>Dokumente</value>
  </data>
  <data name="Meeting.Minutes" xml:space="preserve">
    <value>Protokoll</value>
  </data>
  <data name="Meeting.Agenda" xml:space="preserve">
    <value>Tagesordnung</value>
  </data>

  <!-- Requirements Management - German -->
  <data name="Requirement.Title" xml:space="preserve">
    <value>Anforderungstitel</value>
  </data>
  <data name="Requirement.Description" xml:space="preserve">
    <value>Beschreibung</value>
  </data>
  <data name="Requirement.Type" xml:space="preserve">
    <value>Typ</value>
  </data>
  <data name="Requirement.Priority" xml:space="preserve">
    <value>Priorität</value>
  </data>
  <data name="Requirement.Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="Requirement.Source" xml:space="preserve">
    <value>Quelle</value>
  </data>
  <data name="Requirement.Stakeholder" xml:space="preserve">
    <value>Stakeholder</value>
  </data>
  <data name="Requirement.AcceptanceCriteria" xml:space="preserve">
    <value>Akzeptanzkriterien</value>
  </data>
  <data name="Requirement.BusinessValue" xml:space="preserve">
    <value>Geschäftswert</value>
  </data>
  <data name="Requirement.Comments" xml:space="preserve">
    <value>Kommentare</value>
  </data>
  <data name="Requirement.Attachments" xml:space="preserve">
    <value>Anhänge</value>
  </data>
  <data name="Requirement.Changes" xml:space="preserve">
    <value>Änderungshistorie</value>
  </data>
  <data name="Requirement.Tasks" xml:space="preserve">
    <value>Zugehörige Aufgaben</value>
  </data>

  <!-- Risk Management - German -->
  <data name="Risk.Title" xml:space="preserve">
    <value>Risikotitel</value>
  </data>
  <data name="Risk.Description" xml:space="preserve">
    <value>Beschreibung</value>
  </data>
  <data name="Risk.Category" xml:space="preserve">
    <value>Kategorie</value>
  </data>
  <data name="Risk.Probability" xml:space="preserve">
    <value>Wahrscheinlichkeit</value>
  </data>
  <data name="Risk.Impact" xml:space="preserve">
    <value>Auswirkung</value>
  </data>
  <data name="Risk.Score" xml:space="preserve">
    <value>Risikobewertung</value>
  </data>
  <data name="Risk.Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="Risk.Owner" xml:space="preserve">
    <value>Risikoverantwortlicher</value>
  </data>
  <data name="Risk.MitigationPlan" xml:space="preserve">
    <value>Minderungsplan</value>
  </data>
  <data name="Risk.MitigationActions" xml:space="preserve">
    <value>Minderungsmaßnahmen</value>
  </data>
  <data name="Risk.ContingencyPlan" xml:space="preserve">
    <value>Notfallplan</value>
  </data>

  <!-- Resource Management - German -->
  <data name="Resource.Name" xml:space="preserve">
    <value>Ressourcenname</value>
  </data>
  <data name="Resource.Type" xml:space="preserve">
    <value>Typ</value>
  </data>
  <data name="Resource.Department" xml:space="preserve">
    <value>Abteilung</value>
  </data>
  <data name="Resource.Location" xml:space="preserve">
    <value>Standort</value>
  </data>
  <data name="Resource.HourlyRate" xml:space="preserve">
    <value>Stundensatz</value>
  </data>
  <data name="Resource.Capacity" xml:space="preserve">
    <value>Kapazität</value>
  </data>
  <data name="Resource.Skills" xml:space="preserve">
    <value>Fähigkeiten</value>
  </data>
  <data name="Resource.Availability" xml:space="preserve">
    <value>Verfügbarkeit</value>
  </data>
  <data name="Resource.Utilization" xml:space="preserve">
    <value>Auslastung</value>
  </data>
  <data name="Resource.Allocation" xml:space="preserve">
    <value>Zuteilung</value>
  </data>

  <!-- Agile/Scrum Terms - German -->
  <data name="Agile.SprintPlanning" xml:space="preserve">
    <value>Sprint-Planung</value>
  </data>
  <data name="Agile.SprintReview" xml:space="preserve">
    <value>Sprint-Review</value>
  </data>
  <data name="Agile.SprintRetrospective" xml:space="preserve">
    <value>Sprint-Retrospektive</value>
  </data>
  <data name="Agile.DailyStandup" xml:space="preserve">
    <value>Daily Standup</value>
  </data>
  <data name="Agile.ProductBacklog" xml:space="preserve">
    <value>Product Backlog</value>
  </data>
  <data name="Agile.SprintBacklog" xml:space="preserve">
    <value>Sprint Backlog</value>
  </data>
  <data name="Agile.Definition" xml:space="preserve">
    <value>Definition of Done</value>
  </data>
  <data name="Agile.StoryPoints" xml:space="preserve">
    <value>Story Points</value>
  </data>
  <data name="Agile.SprintStarted" xml:space="preserve">
    <value>Sprint erfolgreich gestartet</value>
  </data>
  <data name="Agile.SprintCompleted" xml:space="preserve">
    <value>Sprint erfolgreich abgeschlossen</value>
  </data>

  <!-- Enum Values - Meeting Types - German -->
  <data name="Enum.MeetingType.General" xml:space="preserve">
    <value>Allgemein</value>
  </data>
  <data name="Enum.MeetingType.Standup" xml:space="preserve">
    <value>Daily Standup</value>
  </data>
  <data name="Enum.MeetingType.Planning" xml:space="preserve">
    <value>Planung</value>
  </data>
  <data name="Enum.MeetingType.Review" xml:space="preserve">
    <value>Review</value>
  </data>
  <data name="Enum.MeetingType.Retrospective" xml:space="preserve">
    <value>Retrospektive</value>
  </data>
  <data name="Enum.MeetingType.Stakeholder" xml:space="preserve">
    <value>Stakeholder</value>
  </data>
  <data name="Enum.MeetingType.Requirements" xml:space="preserve">
    <value>Anforderungen</value>
  </data>
  <data name="Enum.MeetingType.Technical" xml:space="preserve">
    <value>Technisch</value>
  </data>
  <data name="Enum.MeetingType.Status" xml:space="preserve">
    <value>Status</value>
  </data>

  <!-- Enum Values - Meeting Status - German -->
  <data name="Enum.MeetingStatus.Scheduled" xml:space="preserve">
    <value>Geplant</value>
  </data>
  <data name="Enum.MeetingStatus.InProgress" xml:space="preserve">
    <value>In Bearbeitung</value>
  </data>
  <data name="Enum.MeetingStatus.Completed" xml:space="preserve">
    <value>Abgeschlossen</value>
  </data>
  <data name="Enum.MeetingStatus.Cancelled" xml:space="preserve">
    <value>Abgebrochen</value>
  </data>
  <data name="Enum.MeetingStatus.Postponed" xml:space="preserve">
    <value>Verschoben</value>
  </data>

  <!-- Enum Values - Requirement Types - German -->
  <data name="Enum.RequirementType.Functional" xml:space="preserve">
    <value>Funktional</value>
  </data>
  <data name="Enum.RequirementType.NonFunctional" xml:space="preserve">
    <value>Nicht-funktional</value>
  </data>
  <data name="Enum.RequirementType.Business" xml:space="preserve">
    <value>Geschäftlich</value>
  </data>
  <data name="Enum.RequirementType.Technical" xml:space="preserve">
    <value>Technisch</value>
  </data>
  <data name="Enum.RequirementType.Performance" xml:space="preserve">
    <value>Leistung</value>
  </data>
  <data name="Enum.RequirementType.Security" xml:space="preserve">
    <value>Sicherheit</value>
  </data>
  <data name="Enum.RequirementType.Usability" xml:space="preserve">
    <value>Benutzerfreundlichkeit</value>
  </data>
  <data name="Enum.RequirementType.Compliance" xml:space="preserve">
    <value>Compliance</value>
  </data>
  <data name="Enum.RequirementType.Integration" xml:space="preserve">
    <value>Integration</value>
  </data>

  <!-- Enum Values - Risk Categories - German -->
  <data name="Enum.RiskCategory.Technical" xml:space="preserve">
    <value>Technisch</value>
  </data>
  <data name="Enum.RiskCategory.Schedule" xml:space="preserve">
    <value>Zeitplan</value>
  </data>
  <data name="Enum.RiskCategory.Budget" xml:space="preserve">
    <value>Budget</value>
  </data>
  <data name="Enum.RiskCategory.Resource" xml:space="preserve">
    <value>Ressource</value>
  </data>
  <data name="Enum.RiskCategory.Quality" xml:space="preserve">
    <value>Qualität</value>
  </data>
  <data name="Enum.RiskCategory.External" xml:space="preserve">
    <value>Extern</value>
  </data>
  <data name="Enum.RiskCategory.Organizational" xml:space="preserve">
    <value>Organisatorisch</value>
  </data>

  <!-- Enum Values - Resource Types - German -->
  <data name="Enum.ResourceType.Human" xml:space="preserve">
    <value>Personalressource</value>
  </data>
  <data name="Enum.ResourceType.Equipment" xml:space="preserve">
    <value>Ausrüstung</value>
  </data>
  <data name="Enum.ResourceType.Material" xml:space="preserve">
    <value>Material</value>
  </data>
  <data name="Enum.ResourceType.Software" xml:space="preserve">
    <value>Software</value>
  </data>
  <data name="Enum.ResourceType.Facility" xml:space="preserve">
    <value>Einrichtung</value>
  </data>

  <!-- WBS Specific Messages - German -->
  <data name="WBS.ErrorLoadingStructure" xml:space="preserve">
    <value>Fehler beim Laden der WBS-Struktur</value>
  </data>
  <data name="WBS.TaskCreatedSuccessfully" xml:space="preserve">
    <value>Aufgabe erfolgreich erstellt</value>
  </data>
  <data name="WBS.TaskDeletedSuccessfully" xml:space="preserve">
    <value>Aufgabe erfolgreich gelöscht</value>
  </data>
  <data name="WBS.TaskDuplicatedSuccessfully" xml:space="preserve">
    <value>Aufgabe erfolgreich dupliziert</value>
  </data>
  <data name="WBS.TaskMovedSuccessfully" xml:space="preserve">
    <value>Aufgabe erfolgreich {0} verschoben</value>
  </data>
  <data name="WBS.TaskStatusUpdated" xml:space="preserve">
    <value>Aufgabenstatus auf {0} aktualisiert</value>
  </data>
  <data name="WBS.WbsCodesGenerated" xml:space="preserve">
    <value>WBS-Codes erfolgreich generiert</value>
  </data>
  <data name="WBS.ExportingTo" xml:space="preserve">
    <value>WBS wird nach {0} exportiert...</value>
  </data>
  <data name="WBS.ExportingTask" xml:space="preserve">
    <value>Aufgabe wird exportiert...</value>
  </data>
  <data name="WBS.PrintingWbs" xml:space="preserve">
    <value>Druckdialog wird geöffnet...</value>
  </data>
  <data name="WBS.CompactViewEnabled" xml:space="preserve">
    <value>Kompakte Ansicht aktiviert</value>
  </data>
  <data name="WBS.NormalViewEnabled" xml:space="preserve">
    <value>Normale Ansicht aktiviert</value>
  </data>
  <data name="WBS.DetailedViewEnabled" xml:space="preserve">
    <value>Detailansicht aktiviert</value>
  </data>
  <data name="WBS.RefreshingView" xml:space="preserve">
    <value>WBS-Ansicht wird aktualisiert...</value>
  </data>
  <data name="WBS.FilteredToShow" xml:space="preserve">
    <value>Gefiltert um {0} Aufgaben anzuzeigen</value>
  </data>
  <data name="WBS.ShowingAllTasks" xml:space="preserve">
    <value>Alle Aufgaben werden angezeigt</value>
  </data>
  <data name="WBS.FilteredOverdue" xml:space="preserve">
    <value>Gefiltert um überfällige Aufgaben anzuzeigen</value>
  </data>
  <data name="WBS.TaskTitleRequired" xml:space="preserve">
    <value>Aufgabentitel ist erforderlich</value>
  </data>
  <data name="WBS.ProjectIdMissing" xml:space="preserve">
    <value>Projekt-ID fehlt</value>
  </data>
  <data name="WBS.ValidationError" xml:space="preserve">
    <value>Validierungsfehler. Bitte überprüfen Sie Ihre Eingabe.</value>
  </data>
  <data name="WBS.AccessDenied" xml:space="preserve">
    <value>Zugriff verweigert. Bitte aktualisieren Sie die Seite und versuchen Sie es erneut.</value>
  </data>
  <data name="WBS.RequestFormatError" xml:space="preserve">
    <value>Anfrage-Formatfehler. Bitte versuchen Sie es erneut.</value>
  </data>
  <data name="WBS.ErrorCreatingTask" xml:space="preserve">
    <value>Fehler beim Erstellen der Aufgabe</value>
  </data>
  <data name="WBS.ErrorLoadingTaskDetails" xml:space="preserve">
    <value>Fehler beim Laden der Aufgabendetails</value>
  </data>
  <data name="WBS.ErrorDeletingTask" xml:space="preserve">
    <value>Fehler beim Löschen der Aufgabe</value>
  </data>
  <data name="WBS.ErrorDuplicatingTask" xml:space="preserve">
    <value>Fehler beim Duplizieren der Aufgabe</value>
  </data>
  <data name="WBS.ErrorMovingTask" xml:space="preserve">
    <value>Fehler beim Verschieben der Aufgabe {0}</value>
  </data>
  <data name="WBS.ErrorUpdatingTaskStatus" xml:space="preserve">
    <value>Fehler beim Aktualisieren des Aufgabenstatus</value>
  </data>
  <data name="WBS.ErrorGeneratingCodes" xml:space="preserve">
    <value>Fehler beim Generieren der WBS-Codes</value>
  </data>
  <data name="WBS.ConfirmDeleteTask" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie diese Aufgabe löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.</value>
  </data>
  <data name="WBS.ConfirmDuplicateTask" xml:space="preserve">
    <value>Ein Duplikat dieser Aufgabe erstellen?</value>
  </data>
  <data name="WBS.ConfirmGenerateCodes" xml:space="preserve">
    <value>Dies wird alle WBS-Codes neu generieren. Fortfahren?</value>
  </data>
  <data name="WBS.CreateNewTask" xml:space="preserve">
    <value>Neue Aufgabe erstellen</value>
  </data>
  <data name="WBS.CreateChildTaskFor" xml:space="preserve">
    <value>Unteraufgabe erstellen für: {0}</value>
  </data>
  <data name="WBS.TaskNotFound" xml:space="preserve">
    <value>Aufgabe nicht gefunden</value>
  </data>
  <data name="WBS.UnsupportedExportFormat" xml:space="preserve">
    <value>Nicht unterstütztes Exportformat</value>
  </data>
  <data name="WBS.GanttViewComingSoon" xml:space="preserve">
    <value>Gantt-Ansicht kommt bald!</value>
  </data>

  <!-- WBS Task Actions - German -->
  <data name="WBS.Action.ViewDetails" xml:space="preserve">
    <value>Details anzeigen</value>
  </data>
  <data name="WBS.Action.EditTask" xml:space="preserve">
    <value>Aufgabe bearbeiten</value>
  </data>
  <data name="WBS.Action.Duplicate" xml:space="preserve">
    <value>Duplizieren</value>
  </data>
  <data name="WBS.Action.AddChild" xml:space="preserve">
    <value>Unteraufgabe hinzufügen</value>
  </data>
  <data name="WBS.Action.MoreActions" xml:space="preserve">
    <value>Weitere Aktionen</value>
  </data>
  <data name="WBS.Action.MoveUp" xml:space="preserve">
    <value>Nach oben verschieben</value>
  </data>
  <data name="WBS.Action.MoveDown" xml:space="preserve">
    <value>Nach unten verschieben</value>
  </data>
  <data name="WBS.Action.StartTask" xml:space="preserve">
    <value>Aufgabe starten</value>
  </data>
  <data name="WBS.Action.MarkInReview" xml:space="preserve">
    <value>Als in Überprüfung markieren</value>
  </data>
  <data name="WBS.Action.MarkComplete" xml:space="preserve">
    <value>Als abgeschlossen markieren</value>
  </data>
  <data name="WBS.Action.CancelTask" xml:space="preserve">
    <value>Aufgabe abbrechen</value>
  </data>
  <data name="WBS.Action.ExportTask" xml:space="preserve">
    <value>Aufgabe exportieren</value>
  </data>
  <data name="WBS.Action.DeleteTask" xml:space="preserve">
    <value>Aufgabe löschen</value>
  </data>

  <!-- WBS Labels - German -->
  <data name="WBS.Label.Progress" xml:space="preserve">
    <value>Fortschritt</value>
  </data>
  <data name="WBS.Label.Unassigned" xml:space="preserve">
    <value>Nicht zugewiesen</value>
  </data>
  <data name="WBS.Label.Overdue" xml:space="preserve">
    <value>Überfällig</value>
  </data>
  <data name="WBS.Label.Start" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="WBS.Label.Due" xml:space="preserve">
    <value>Fällig</value>
  </data>
  <data name="WBS.Label.GeneratedOn" xml:space="preserve">
    <value>Generiert am</value>
  </data>
  <data name="WBS.Label.WorkBreakdownStructure" xml:space="preserve">
    <value>Projektstrukturplan</value>
  </data>

</root>
