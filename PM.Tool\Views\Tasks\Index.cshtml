@using PM.Tool.Core.Enums
@using TaskStatus = PM.Tool.Core.Enums.TaskStatus
@model IEnumerable<TaskViewModel>

@{
    ViewData["Title"] = "Tasks";
    ViewData["Subtitle"] = "Manage and track task progress across all projects";
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-tasks mr-3 text-primary-600 dark:text-primary-400"></i>
                Tasks
            </h1>
            <div class="mt-1">
                @if (ViewBag.ProjectName != null)
                {
                    <p class="text-sm text-neutral-500 dark:text-dark-400">
                        Project: <span class="font-medium text-primary-600 dark:text-primary-400">@ViewBag.ProjectName</span>
                    </p>
                }
                else
                {
                    <p class="text-sm text-neutral-500 dark:text-dark-400">
                        Manage and track task progress across all projects
                    </p>
                }
            </div>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
            @{
                ViewData["Text"] = "Create New Task";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-plus";
                ViewData["Href"] = Url.Action("Create");
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "Kanban Board";
                ViewData["Variant"] = "outline";
                ViewData["Icon"] = "fas fa-columns";
                ViewData["Href"] = Url.Action("Index", "Agile");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card-custom mb-8">
    <div class="card-header-custom">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                <i class="fas fa-filter text-primary-600 dark:text-primary-400 text-sm"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Filter Tasks</h3>
            </div>
        </div>
    </div>
    <div class="card-body-custom">
        <form asp-action="Index" method="get" class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <!-- Project Filter -->
            @{
                var projectOptions = "<option value=\"\">All Projects</option>";
                if (ViewBag.Projects != null)
                {
                    foreach (var project in (IEnumerable<SelectListItem>)ViewBag.Projects)
                    {
                        var selected = ViewBag.ProjectId?.ToString() == project.Value ? "selected" : "";
                        projectOptions += $"<option value=\"{project.Value}\" {selected}>{project.Text}</option>";
                    }
                }

                ViewData["Label"] = "Project";
                ViewData["Name"] = "projectId";
                ViewData["Type"] = "select";
                ViewData["Required"] = false;
                ViewData["Icon"] = "fas fa-project-diagram";
                ViewData["Options"] = projectOptions;
                ViewData["ContainerClasses"] = "";
                ViewData["Value"] = ViewBag.ProjectId?.ToString();
                ViewData["UseSelect2"] = true;
                ViewData["Select2Options"] = "{\"placeholder\": \"All Projects\", \"allowClear\": true, \"theme\": \"tailwind\"}";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

            <!-- Status Filter -->
            @{
                var statusOptions = "<option value=\"\">All Statuses</option>";
                if (ViewBag.Statuses != null)
                {
                    foreach (var status in (IEnumerable<SelectListItem>)ViewBag.Statuses)
                    {
                        var selected = ViewBag.Status?.ToString() == status.Value ? "selected" : "";
                        statusOptions += $"<option value=\"{status.Value}\" {selected}>{status.Text}</option>";
                    }
                }

                ViewData["Label"] = "Status";
                ViewData["Name"] = "status";
                ViewData["Type"] = "select";
                ViewData["Required"] = false;
                ViewData["Icon"] = "fas fa-info-circle";
                ViewData["Options"] = statusOptions;
                ViewData["ContainerClasses"] = "";
                ViewData["Value"] = ViewBag.Status?.ToString();
                ViewData["UseSelect2"] = true;
                ViewData["Select2Options"] = "{\"placeholder\": \"All Statuses\", \"allowClear\": true, \"theme\": \"tailwind\"}";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

            <!-- Assigned To Filter -->
            @{
                var userOptions = "<option value=\"\">All Users</option>";
                if (ViewBag.Users != null)
                {
                    foreach (var user in (IEnumerable<SelectListItem>)ViewBag.Users)
                    {
                        var selected = ViewBag.AssignedTo?.ToString() == user.Value ? "selected" : "";
                        userOptions += $"<option value=\"{user.Value}\" {selected}>{user.Text}</option>";
                    }
                }

                ViewData["Label"] = "Assigned To";
                ViewData["Name"] = "assignedTo";
                ViewData["Type"] = "select";
                ViewData["Required"] = false;
                ViewData["Icon"] = "fas fa-user";
                ViewData["Options"] = userOptions;
                ViewData["ContainerClasses"] = "";
                ViewData["Value"] = ViewBag.AssignedTo?.ToString();
                ViewData["UseSelect2"] = true;
                ViewData["Select2Options"] = "{\"placeholder\": \"All Users\", \"allowClear\": true, \"theme\": \"tailwind\"}";
            }
            <partial name="Components/_FormInput" view-data="ViewData" />

            <!-- Apply Button -->
            <div class="flex items-end">
                @{
                    ViewData["Text"] = "Apply Filters";
                    ViewData["Variant"] = "primary";
                    ViewData["Icon"] = "fas fa-search";
                    ViewData["Type"] = "submit";
                    ViewData["FullWidth"] = true;
                    ViewData["Href"] = null;
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
        </form>
    </div>
</div>

<!-- Tasks List -->
<div class="card-custom">
    <div class="card-header-custom">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                <i class="fas fa-list text-primary-600 dark:text-primary-400 text-sm"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Tasks List</h3>
            </div>
        </div>
    </div>
    <div class="card-body-custom">
        @if (Model.Any())
        {
            <div class="overflow-x-auto">
                <table class="table-custom" id="tasksTable">
                    <thead class="table-header-custom">
                        <tr>
                            <th class="table-header-cell-custom">
                                <div class="flex items-center">
                                    <i class="fas fa-tag mr-2 text-neutral-400 dark:text-dark-500"></i>
                                    @Html.DisplayNameFor(model => model.Title)
                                </div>
                            </th>
                            <th class="table-header-cell-custom">
                                <div class="flex items-center">
                                    <i class="fas fa-project-diagram mr-2 text-neutral-400 dark:text-dark-500"></i>
                                    @Html.DisplayNameFor(model => model.ProjectName)
                                </div>
                            </th>
                            <th class="table-header-cell-custom">
                                <div class="flex items-center">
                                    <i class="fas fa-info-circle mr-2 text-neutral-400 dark:text-dark-500"></i>
                                    @Html.DisplayNameFor(model => model.Status)
                                </div>
                            </th>
                            <th class="table-header-cell-custom">
                                <div class="flex items-center">
                                    <i class="fas fa-exclamation-triangle mr-2 text-neutral-400 dark:text-dark-500"></i>
                                    @Html.DisplayNameFor(model => model.Priority)
                                </div>
                            </th>
                            <th class="table-header-cell-custom">
                                <div class="flex items-center">
                                    <i class="fas fa-user mr-2 text-neutral-400 dark:text-dark-500"></i>
                                    @Html.DisplayNameFor(model => model.AssignedToName)
                                </div>
                            </th>
                            <th class="table-header-cell-custom">
                                <div class="flex items-center">
                                    <i class="fas fa-calendar-alt mr-2 text-neutral-400 dark:text-dark-500"></i>
                                    @Html.DisplayNameFor(model => model.DueDate)
                                </div>
                            </th>
                            <th class="table-header-cell-custom">
                                <div class="flex items-center">
                                    <i class="fas fa-cogs mr-2 text-neutral-400 dark:text-dark-500"></i>
                                    Actions
                                </div>
                            </th>
                        </tr>
                    </thead>
            <tbody>
                @foreach (var item in Model)
                {
                    <tr class="hover:bg-neutral-50 dark:hover:bg-dark-800 transition-colors">
                        <td class="table-cell-custom">
                            <div class="flex items-center">
                                @if (item.ParentTaskId.HasValue)
                                {
                                    <span class="text-neutral-400 dark:text-dark-500 mr-2">↳</span>
                                }
                                <a asp-action="Details" asp-route-id="@item.Id" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors">
                                    @item.Title
                                </a>
                            </div>
                        </td>
                        <td class="table-cell-custom">
                            <a asp-controller="Projects" asp-action="Details" asp-route-id="@item.ProjectId" class="text-neutral-600 dark:text-dark-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                                @item.ProjectName
                            </a>
                        </td>
                        <td class="table-cell-custom">
                            @{
                                var statusClass = item.Status switch {
                                    TaskStatus.Done => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
                                    TaskStatus.InProgress => "bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200",
                                    TaskStatus.InReview => "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
                                    _ => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200"
                                };
                            }
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @statusClass">
                                @item.Status
                            </span>
                        </td>
                        <td class="table-cell-custom">
                            @{
                                var priorityClass = item.Priority switch {
                                    TaskPriority.High => "bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200",
                                    TaskPriority.Medium => "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
                                    TaskPriority.Low => "bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200",
                                    _ => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200"
                                };
                            }
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @priorityClass">
                                @item.Priority
                            </span>
                        </td>
                        <td class="table-cell-custom">
                            <div class="flex items-center">
                                @if (!string.IsNullOrEmpty(item.AssignedToName))
                                {
                                    <div class="w-6 h-6 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mr-2">
                                        <span class="text-xs font-medium text-primary-600 dark:text-primary-400">
                                            @item.AssignedToName.Substring(0, 1).ToUpper()
                                        </span>
                                    </div>
                                    <span class="text-sm text-neutral-900 dark:text-dark-100">@item.AssignedToName</span>
                                }
                                else
                                {
                                    <span class="text-sm text-neutral-400 dark:text-dark-500">Unassigned</span>
                                }
                            </div>
                        </td>
                        <td class="table-cell-custom">
                            @if (item.DueDate.HasValue)
                            {
                                <div class="flex items-center">
                                    <span class="text-sm @(item.IsOverdue ? "text-danger-600 dark:text-danger-400" : "text-neutral-600 dark:text-dark-300")">
                                        @item.DueDate.Value.ToString("MMM dd, yyyy")
                                    </span>
                                    @if (item.IsOverdue)
                                    {
                                        <i class="fas fa-exclamation-triangle text-danger-500 ml-2" title="Overdue"></i>
                                    }
                                </div>
                            }
                            else
                            {
                                <span class="text-sm text-neutral-400 dark:text-dark-500">-</span>
                            }
                        </td>
                        <td class="table-cell-custom">
                            <div class="flex items-center space-x-2">
                                @{
                                    ViewData["Text"] = "";
                                    ViewData["Variant"] = "outline";
                                    ViewData["Size"] = "sm";
                                    ViewData["Icon"] = "fas fa-edit";
                                    ViewData["Href"] = Url.Action("Edit", new { id = item.Id });
                                    ViewData["AriaLabel"] = "Edit Task";
                                }
                                <partial name="Components/_Button" view-data="ViewData" />

                                @{
                                    ViewData["Icon"] = "fas fa-eye";
                                    ViewData["Href"] = Url.Action("Details", new { id = item.Id });
                                    ViewData["AriaLabel"] = "View Details";
                                }
                                <partial name="Components/_Button" view-data="ViewData" />

                                @if (!item.SubTaskCount.HasValue || item.SubTaskCount == 0)
                                {

                                        ViewData["Icon"] = "fas fa-trash";
                                        ViewData["Variant"] = "outline";
                                        ViewData["Href"] = Url.Action("Delete", new { id = item.Id });
                                        ViewData["AriaLabel"] = "Delete Task";
                                        ViewData["AdditionalClasses"] = "text-danger-600 dark:text-danger-400 border-danger-300 dark:border-danger-600 hover:bg-danger-50 dark:hover:bg-danger-900/20";

                                    <partial name="Components/_Button" view-data="ViewData" />
                                }
                            </div>
                        </td>
                    </tr>
                }
            </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-16">
                <div class="w-24 h-24 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-tasks text-4xl text-neutral-400 dark:text-dark-500"></i>
                </div>
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">No Tasks Found</h3>
                <p class="text-neutral-500 dark:text-dark-400 mb-8 max-w-md mx-auto">
                    No tasks found matching the selected criteria. Try adjusting your filters or create a new task.
                </p>
                @{
                    ViewData["Text"] = "Create Your First Task";
                    ViewData["Variant"] = "primary";
                    ViewData["Icon"] = "fas fa-plus";
                    ViewData["Href"] = Url.Action("Create");
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
        }
    </div>
</div>

@section Scripts {
    <partial name="Partials/_TaskIndexScripts" />
}
