@using PM.Tool.Core.Models
@model DocumentationPage
@{
    ViewData["Title"] = Model.Title;
    ViewData["Description"] = $"Documentation for {Model.Title} - PM Tool";
    Layout = "_DocumentationLayout";

    var relatedPages = ViewBag.RelatedPages as List<DocumentationPage>;
    var config = ViewBag.Configuration as DocumentationConfiguration;
}

<div class="max-w-4xl mx-auto">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-4xl font-bold text-neutral-900 dark:text-white mb-4">@Model.Title</h1>

        <!-- Tags -->
        @if (Model.Tags?.Any() == true)
        {
            <div class="flex flex-wrap gap-2 mb-4">
                @foreach (var tag in Model.Tags)
                {
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-neutral-100 dark:bg-gray-700 text-neutral-800 dark:text-neutral-200">
                        @tag
                    </span>
                }
            </div>
        }

        <!-- Metadata -->
        <div class="flex flex-wrap items-center gap-6 text-sm text-neutral-500 dark:text-neutral-400">
            @if (Model.EstimatedReadTime.TotalMinutes > 0)
            {
                <div class="flex items-center">
                    <i class="fas fa-clock mr-2"></i>
                    <span>@((int)Model.EstimatedReadTime.TotalMinutes) min read</span>
                </div>
            }
            @if (Model.LastModified != default)
            {
                <div class="flex items-center">
                    <i class="fas fa-calendar-alt mr-2"></i>
                    <span>Updated @Model.LastModified.ToString("MMM dd, yyyy")</span>
                </div>
            }
        </div>
    </div>

    <!-- Content -->
    <div class="prose prose-lg dark:prose-invert max-w-none">
        @Html.Raw(Model.HtmlContent)
    </div>

            @if (!string.IsNullOrEmpty(Model.Author))
            {
                <div class="mt-4 text-muted">
                    <i class="fas fa-user"></i>
                    Written by @Model.Author
                </div>
            }
        </div>
    </div>
</div>
