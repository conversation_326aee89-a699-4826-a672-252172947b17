using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Entities;

namespace PM.Tool.Services
{
    /// <summary>
    /// Centralized service for user lookup operations across the application
    /// </summary>
    public interface IUserLookupService
    {
        /// <summary>
        /// Get all active users for dropdown lists
        /// </summary>
        Task<IActionResult> GetUsersAsync();

        /// <summary>
        /// Get users for a specific project
        /// </summary>
        Task<IActionResult> GetProjectUsersAsync(int projectId);

        /// <summary>
        /// Get people from the Person entity
        /// </summary>
        Task<IActionResult> GetPeopleAsync();
    }

    public class UserLookupService : IUserLookupService
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IProjectService _projectService;
        private readonly IPersonService _personService;

        public UserLookupService(
            UserManager<ApplicationUser> userManager,
            IProjectService projectService,
            IPersonService personService)
        {
            _userManager = userManager;
            _projectService = projectService;
            _personService = personService;
        }

        public async Task<IActionResult> GetUsersAsync()
        {
            try
            {
                var users = _userManager.Users
                    .Where(u => u.IsActive && (!u.LockoutEnabled || u.LockoutEnd == null || u.LockoutEnd <= DateTimeOffset.UtcNow))
                    .Select(u => new
                    {
                        id = u.Id,
                        userName = u.UserName,
                        email = u.Email,
                        fullName = $"{u.FirstName} {u.LastName}".Trim(),
                        firstName = u.FirstName,
                        lastName = u.LastName
                    })
                    .ToList();

                return new JsonResult(users);
            }
            catch (Exception)
            {
                return new JsonResult(new List<object>());
            }
        }

        public async Task<IActionResult> GetProjectUsersAsync(int projectId)
        {
            try
            {
                var projectMembers = await _projectService.GetProjectMembersAsync(projectId);
                var users = projectMembers.Select(pm => new
                {
                    id = pm.UserId,
                    userName = pm.User?.UserName ?? "Unknown",
                    email = pm.User?.Email ?? "",
                    fullName = pm.User != null ? $"{pm.User.FirstName} {pm.User.LastName}".Trim() : "Unknown",
                    firstName = pm.User?.FirstName ?? "",
                    lastName = pm.User?.LastName ?? "",
                    role = pm.Role.ToString()
                }).ToList();

                return new JsonResult(users);
            }
            catch (Exception)
            {
                return new JsonResult(new List<object>());
            }
        }

        public async Task<IActionResult> GetPeopleAsync()
        {
            try
            {
                var people = await _personService.GetAllPeopleAsync();
                var result = people.Select(p => new
                {
                    id = p.Id.ToString(),
                    userName = $"{p.FirstName} {p.LastName}",
                    email = p.Email,
                    fullName = $"{p.FirstName} {p.LastName}",
                    department = p.Department,
                    role = p.Title ?? "Unknown" // Use Title instead of Role
                }).ToList();

                return new JsonResult(result);
            }
            catch (Exception)
            {
                return new JsonResult(new List<object>());
            }
        }
    }

    /// <summary>
    /// Controller to provide standardized user lookup endpoints
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class UserLookupController : ControllerBase
    {
        private readonly IUserLookupService _userLookupService;

        public UserLookupController(IUserLookupService userLookupService)
        {
            _userLookupService = userLookupService;
        }

        /// <summary>
        /// GET: api/UserLookup/Users
        /// Standard endpoint for getting all users
        /// </summary>
        [HttpGet("Users")]
        public async Task<IActionResult> GetUsers()
        {
            return await _userLookupService.GetUsersAsync();
        }

        /// <summary>
        /// GET: api/UserLookup/ProjectUsers/{projectId}
        /// Get users for a specific project
        /// </summary>
        [HttpGet("ProjectUsers/{projectId}")]
        public async Task<IActionResult> GetProjectUsers(int projectId)
        {
            return await _userLookupService.GetProjectUsersAsync(projectId);
        }

        /// <summary>
        /// GET: api/UserLookup/People
        /// Get people from Person entity
        /// </summary>
        [HttpGet("People")]
        public async Task<IActionResult> GetPeople()
        {
            return await _userLookupService.GetPeopleAsync();
        }
    }
}
