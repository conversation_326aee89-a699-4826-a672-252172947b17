@using PM.Tool.Controllers
@model List<SitemapItem>
@{
    ViewData["Title"] = "Documentation Sitemap";
    ViewData["Description"] = "Complete sitemap of PM Tool documentation";
    Layout = "_DocumentationLayout";

    var config = ViewBag.Configuration as PM.Tool.Core.Models.DocumentationConfiguration;
}

<div class="max-w-6xl mx-auto">
    <!-- Sitemap Header -->
    <div class="text-center mb-12">
        <div class="w-16 h-16 bg-success-100 dark:bg-success-900 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-sitemap text-3xl text-success-600 dark:text-success-400"></i>
        </div>
        <h1 class="text-4xl font-bold text-neutral-900 dark:text-white mb-4">Documentation Sitemap</h1>
        <p class="text-xl text-neutral-600 dark:text-neutral-300 max-w-2xl mx-auto">
            Complete overview of all documentation pages and sections in PM Tool
        </p>
    </div>

    @if (Model?.Any() == true)
    {
        var sections = Model.Where(item => item.Type == "section").ToList();
        var pages = Model.Where(item => item.Type == "page").ToList();

        <!-- Sections and Pages -->
        <div class="space-y-8">
            @foreach (var sitemapSection in sections)
            {
                <div class="bg-white dark:bg-gray-800 rounded-xl border border-neutral-200 dark:border-gray-700 overflow-hidden">
                    <!-- Section Header -->
                    <div class="bg-neutral-50 dark:bg-gray-700 px-6 py-4 border-b border-neutral-200 dark:border-gray-600">
                        <h3 class="text-xl font-semibold text-neutral-900 dark:text-white mb-2">
                            <a href="@(sitemapSection.Url)" class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors flex items-center">
                                <i class="fas fa-folder mr-3 text-primary-600 dark:text-primary-400"></i>
                                @(sitemapSection.Title)
                            </a>
                        </h3>
                        @if (!string.IsNullOrEmpty(sitemapSection.Description))
                        {
                            <p class="text-neutral-600 dark:text-neutral-300">@(sitemapSection.Description)</p>
                        }
                    </div>

                    <!-- Section Pages -->
                    <div class="p-6">
                        @{
                            var sectionPages = pages.Where(p => p.Section == sitemapSection.Title).ToList();
                        }

                        @if (sectionPages.Any())
                        {
                            <div class="space-y-2">
                                @foreach (var page in sectionPages)
                                {
                                    <a href="@(page.Url)"
                                       class="flex items-center justify-between p-3 rounded-lg hover:bg-neutral-50 dark:hover:bg-gray-700 transition-colors group">
                                        <div class="flex items-center">
                                            <i class="fas fa-file-alt mr-3 text-neutral-400 dark:text-neutral-500 group-hover:text-primary-500"></i>
                                            <span class="font-medium text-neutral-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400">
                                                @(page.Title)
                                            </span>
                                        </div>
                                        @if (page.LastModified.HasValue)
                                        {
                                            <div class="flex items-center text-sm text-neutral-500 dark:text-neutral-400">
                                                <i class="fas fa-calendar-alt mr-2"></i>
                                                <span>@(page.LastModified.Value.ToString("MMM dd, yyyy"))</span>
                                            </div>
                                        }
                                    </a>
                                }
                            </div>
                        }
                        else
                        {
                            <p class="text-neutral-500 dark:text-neutral-400 italic text-center py-4">
                                No pages in this section yet.
                            </p>
                        }
                    </div>
                </div>
            }
        </div>

        <!-- Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-12">
            <div class="text-center p-6 bg-white dark:bg-gray-800 rounded-xl border border-neutral-200 dark:border-gray-700">
                <div class="text-3xl font-bold text-success-600 dark:text-success-400 mb-2">@sections.Count</div>
                <div class="text-neutral-600 dark:text-neutral-300">Sections</div>
            </div>
            <div class="text-center p-6 bg-white dark:bg-gray-800 rounded-xl border border-neutral-200 dark:border-gray-700">
                <div class="text-3xl font-bold text-info-600 dark:text-info-400 mb-2">@pages.Count</div>
                <div class="text-neutral-600 dark:text-neutral-300">Pages</div>
            </div>
        </div>
    }
    else
    {
        <!-- Empty State -->
        <div class="text-center py-16">
            <div class="w-24 h-24 bg-neutral-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-file-alt text-3xl text-neutral-400 dark:text-neutral-500"></i>
            </div>
            <h3 class="text-xl font-semibold text-neutral-900 dark:text-white mb-2">No Documentation Found</h3>
            <p class="text-neutral-600 dark:text-neutral-300 mb-6">
                Documentation is being prepared. Please check back later.
            </p>
            <a href="@Url.Action("Index")"
               class="inline-flex items-center px-6 py-3 text-sm font-medium text-white bg-primary-600 rounded-lg hover:bg-primary-700 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Documentation
            </a>
        </div>
    }

    <!-- Navigation Actions -->
    <div class="flex flex-wrap justify-center gap-4 mt-12 pt-8 border-t border-neutral-200 dark:border-gray-700">
        <a href="@Url.Action("Index")"
           class="inline-flex items-center px-6 py-3 text-sm font-medium text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900 rounded-lg hover:bg-primary-100 dark:hover:bg-primary-800 transition-colors">
            <i class="fas fa-home mr-2"></i>
            Documentation Home
        </a>
        <a href="@Url.Action("Search")"
           class="inline-flex items-center px-6 py-3 text-sm font-medium text-neutral-600 dark:text-neutral-300 bg-neutral-100 dark:bg-gray-700 rounded-lg hover:bg-neutral-200 dark:hover:bg-gray-600 transition-colors">
            <i class="fas fa-search mr-2"></i>
            Search Documentation
        </a>
    </div>
</div>
