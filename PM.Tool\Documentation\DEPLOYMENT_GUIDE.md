# PM Tool - Deployment Guide

## 🚀 Deployment Overview

This guide provides comprehensive instructions for deploying PM Too<PERSON> across different environments and platforms, from development to production-scale deployments.

## 📋 Pre-Deployment Checklist

### System Requirements Verification
- [ ] .NET 9.0 Runtime installed
- [ ] Database server configured (PostgreSQL/SQL Server)
- [ ] Web server configured (IIS/Nginx/Apache)
- [ ] SSL certificates obtained and configured
- [ ] Domain name configured and DNS updated
- [ ] Firewall rules configured
- [ ] Backup strategy implemented
- [ ] Monitoring tools configured

### Security Checklist
- [ ] Strong passwords for all accounts
- [ ] Database access restricted
- [ ] HTTPS enforced
- [ ] Security headers configured
- [ ] File upload restrictions in place
- [ ] Rate limiting configured
- [ ] Audit logging enabled
- [ ] Regular security updates planned

### Performance Checklist
- [ ] Database indexes optimized
- [ ] Caching strategy implemented
- [ ] CDN configured for static assets
- [ ] Compression enabled
- [ ] Connection pooling configured
- [ ] Resource limits set
- [ ] Load balancing configured (if applicable)

## 🐳 Docker Deployment (Recommended)

### Single Container Deployment
```bash
# Build the Docker image
docker build -t pmtool:latest .

# Run with environment variables
docker run -d \
  --name pmtool \
  -p 80:8080 \
  -p 443:8443 \
  -e ASPNETCORE_ENVIRONMENT=Production \
  -e ConnectionStrings__DefaultConnection="Host=db-server;Database=pmtool_prod;Username=pmtool_user;Password=secure_password" \
  -e JwtSettings__SecretKey="your-256-bit-secret-key" \
  -v /var/pmtool/uploads:/app/uploads \
  -v /var/pmtool/logs:/app/logs \
  pmtool:latest
```

### Docker Compose Deployment
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  pmtool:
    image: pmtool:latest
    container_name: pmtool-app
    restart: unless-stopped
    ports:
      - "80:8080"
      - "443:8443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=Host=db;Database=pmtool_prod;Username=pmtool_user;Password=${DB_PASSWORD}
      - JwtSettings__SecretKey=${JWT_SECRET}
      - EmailSettings__SmtpPassword=${SMTP_PASSWORD}
    volumes:
      - pmtool_uploads:/app/uploads
      - pmtool_logs:/app/logs
      - ./certs:/app/certs:ro
    depends_on:
      - db
      - redis
    networks:
      - pmtool-network

  db:
    image: postgres:15
    container_name: pmtool-db
    restart: unless-stopped
    environment:
      - POSTGRES_DB=pmtool_prod
      - POSTGRES_USER=pmtool_user
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - pmtool_db_data:/var/lib/postgresql/data
      - ./backups:/backups
    ports:
      - "5432:5432"
    networks:
      - pmtool-network

  redis:
    image: redis:7-alpine
    container_name: pmtool-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - pmtool_redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - pmtool-network

  nginx:
    image: nginx:alpine
    container_name: pmtool-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./certs:/etc/nginx/certs:ro
      - pmtool_static:/var/www/static:ro
    depends_on:
      - pmtool
    networks:
      - pmtool-network

volumes:
  pmtool_db_data:
  pmtool_redis_data:
  pmtool_uploads:
  pmtool_logs:
  pmtool_static:

networks:
  pmtool-network:
    driver: bridge
```

### Environment Variables File
```bash
# .env
DB_PASSWORD=your_secure_database_password
JWT_SECRET=your-256-bit-jwt-secret-key-here
SMTP_PASSWORD=your_email_password
REDIS_PASSWORD=your_redis_password
```

### Nginx Configuration
```nginx
# nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream pmtool_backend {
        server pmtool:8080;
    }

    server {
        listen 80;
        server_name your-domain.com www.your-domain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-domain.com www.your-domain.com;

        ssl_certificate /etc/nginx/certs/fullchain.pem;
        ssl_certificate_key /etc/nginx/certs/privkey.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
        ssl_prefer_server_ciphers off;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        # Gzip compression
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

        # Static files
        location /css/ {
            alias /var/www/static/css/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        location /js/ {
            alias /var/www/static/js/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        location /images/ {
            alias /var/www/static/images/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Application
        location / {
            proxy_pass http://pmtool_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection keep-alive;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
        }

        # Health check
        location /health {
            proxy_pass http://pmtool_backend/health;
            access_log off;
        }
    }
}
```

## ☁️ Cloud Platform Deployments

### Azure App Service
```bash
# Create resource group
az group create --name pmtool-rg --location "East US"

# Create App Service plan
az appservice plan create \
  --name pmtool-plan \
  --resource-group pmtool-rg \
  --sku P1V2 \
  --is-linux

# Create web app
az webapp create \
  --resource-group pmtool-rg \
  --plan pmtool-plan \
  --name pmtool-app \
  --runtime "DOTNETCORE:9.0"

# Configure app settings
az webapp config appsettings set \
  --resource-group pmtool-rg \
  --name pmtool-app \
  --settings \
    ASPNETCORE_ENVIRONMENT=Production \
    ConnectionStrings__DefaultConnection="Server=pmtool-db.database.windows.net;Database=pmtool_prod;User Id=pmtool_user;Password=secure_password;" \
    JwtSettings__SecretKey="your-256-bit-secret-key"

# Deploy application
az webapp deployment source config-zip \
  --resource-group pmtool-rg \
  --name pmtool-app \
  --src pmtool-deployment.zip
```

### AWS Elastic Beanstalk
```bash
# Initialize EB application
eb init pmtool --platform "64bit Amazon Linux 2 v2.2.0 running .NET Core" --region us-east-1

# Create environment
eb create pmtool-prod --instance-type t3.medium --database.engine postgres

# Set environment variables
eb setenv \
  ASPNETCORE_ENVIRONMENT=Production \
  ConnectionStrings__DefaultConnection="Host=your-rds-endpoint;Database=pmtool_prod;Username=pmtool_user;Password=secure_password" \
  JwtSettings__SecretKey="your-256-bit-secret-key"

# Deploy application
eb deploy
```

### Google Cloud Platform
```bash
# Create project
gcloud projects create pmtool-project

# Enable required APIs
gcloud services enable appengine.googleapis.com sqladmin.googleapis.com

# Create App Engine application
gcloud app create --region=us-central

# Create Cloud SQL instance
gcloud sql instances create pmtool-db \
  --database-version=POSTGRES_13 \
  --tier=db-f1-micro \
  --region=us-central1

# Deploy application
gcloud app deploy app.yaml
```

## 🖥️ Traditional Server Deployment

### Windows Server (IIS)
```powershell
# Install IIS and ASP.NET Core Hosting Bundle
Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole, IIS-WebServer, IIS-CommonHttpFeatures, IIS-HttpErrors, IIS-HttpLogging, IIS-RequestFiltering, IIS-StaticContent, IIS-DefaultDocument, IIS-DirectoryBrowsing, IIS-ASPNET45

# Download and install ASP.NET Core Hosting Bundle
Invoke-WebRequest -Uri "https://download.visualstudio.microsoft.com/download/pr/..." -OutFile "dotnet-hosting-bundle.exe"
Start-Process -FilePath "dotnet-hosting-bundle.exe" -ArgumentList "/quiet" -Wait

# Create application directory
New-Item -ItemType Directory -Path "C:\inetpub\pmtool" -Force

# Copy application files
Copy-Item -Path ".\publish\*" -Destination "C:\inetpub\pmtool" -Recurse -Force

# Create IIS application
Import-Module WebAdministration
New-WebApplication -Site "Default Web Site" -Name "pmtool" -PhysicalPath "C:\inetpub\pmtool"

# Configure application pool
Set-ItemProperty -Path "IIS:\AppPools\DefaultAppPool" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
Set-ItemProperty -Path "IIS:\AppPools\DefaultAppPool" -Name "enable32BitAppOnWin64" -Value $false
```

### Linux Server (Ubuntu/CentOS)
```bash
# Install .NET 9.0 Runtime
wget https://packages.microsoft.com/config/ubuntu/20.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb
sudo apt-get update
sudo apt-get install -y aspnetcore-runtime-9.0

# Create application user
sudo useradd -r -s /bin/false pmtool

# Create application directory
sudo mkdir -p /var/www/pmtool
sudo chown pmtool:pmtool /var/www/pmtool

# Copy application files
sudo cp -r ./publish/* /var/www/pmtool/
sudo chown -R pmtool:pmtool /var/www/pmtool

# Create systemd service
sudo tee /etc/systemd/system/pmtool.service > /dev/null <<EOF
[Unit]
Description=PM Tool Application
After=network.target

[Service]
Type=notify
User=pmtool
Group=pmtool
WorkingDirectory=/var/www/pmtool
ExecStart=/usr/bin/dotnet /var/www/pmtool/PM.Tool.dll
Restart=always
RestartSec=10
KillSignal=SIGINT
SyslogIdentifier=pmtool
Environment=ASPNETCORE_ENVIRONMENT=Production
Environment=DOTNET_PRINT_TELEMETRY_MESSAGE=false

[Install]
WantedBy=multi-user.target
EOF

# Enable and start service
sudo systemctl enable pmtool.service
sudo systemctl start pmtool.service
sudo systemctl status pmtool.service
```

## 🔧 Database Setup

### PostgreSQL Production Setup
```sql
-- Create production database and user
CREATE DATABASE pmtool_prod;
CREATE USER pmtool_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE pmtool_prod TO pmtool_user;

-- Configure PostgreSQL for production
-- Edit postgresql.conf
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200

-- Edit pg_hba.conf for security
host    pmtool_prod    pmtool_user    10.0.0.0/8    md5
```

### SQL Server Production Setup
```sql
-- Create production database
CREATE DATABASE pmtool_prod;

-- Create login and user
CREATE LOGIN pmtool_user WITH PASSWORD = 'SecurePassword123!';
USE pmtool_prod;
CREATE USER pmtool_user FOR LOGIN pmtool_user;
ALTER ROLE db_owner ADD MEMBER pmtool_user;

-- Configure database settings
ALTER DATABASE pmtool_prod SET RECOVERY FULL;
ALTER DATABASE pmtool_prod SET AUTO_CLOSE OFF;
ALTER DATABASE pmtool_prod SET AUTO_SHRINK OFF;
```

### Database Migration
```bash
# Run migrations in production
dotnet ef database update --project PM.Tool.Data --connection "Host=prod-server;Database=pmtool_prod;Username=pmtool_user;Password=secure_password"

# Seed initial data
dotnet run --project PM.Tool -- --seed-data
```

## 📊 Monitoring & Health Checks

### Application Health Checks
```csharp
// Configure health checks in Program.cs
builder.Services.AddHealthChecks()
    .AddDbContextCheck<ApplicationDbContext>()
    .AddUrlGroup(new Uri("https://api.github.com"), "GitHub API")
    .AddRedis(builder.Configuration.GetConnectionString("Redis"))
    .AddCheck("disk-space", () => 
    {
        var drive = new DriveInfo("/");
        var freeSpaceGB = drive.AvailableFreeSpace / (1024 * 1024 * 1024);
        return freeSpaceGB > 5 ? HealthCheckResult.Healthy() : HealthCheckResult.Unhealthy();
    });

app.MapHealthChecks("/health", new HealthCheckOptions
{
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});
```

### Monitoring Setup
```bash
# Install monitoring tools
# Prometheus
docker run -d --name prometheus -p 9090:9090 prom/prometheus

# Grafana
docker run -d --name grafana -p 3000:3000 grafana/grafana

# Application Insights (Azure)
# Add Application Insights SDK to project
dotnet add package Microsoft.ApplicationInsights.AspNetCore
```

## 🔒 SSL/TLS Configuration

### Let's Encrypt (Free SSL)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Custom SSL Certificate
```bash
# Install certificate
sudo cp your-certificate.crt /etc/ssl/certs/
sudo cp your-private-key.key /etc/ssl/private/
sudo chmod 600 /etc/ssl/private/your-private-key.key

# Configure in Nginx
ssl_certificate /etc/ssl/certs/your-certificate.crt;
ssl_certificate_key /etc/ssl/private/your-private-key.key;
```

## 🔄 Backup & Recovery

### Automated Backup Script
```bash
#!/bin/bash
# backup-production.sh

BACKUP_DIR="/var/backups/pmtool"
DATE=$(date +%Y%m%d_%H%M%S)
DB_BACKUP="pmtool_backup_$DATE.sql"
APP_BACKUP="pmtool_app_backup_$DATE.tar.gz"

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
pg_dump -h localhost -U pmtool_user -d pmtool_prod > $BACKUP_DIR/$DB_BACKUP
gzip $BACKUP_DIR/$DB_BACKUP

# Application backup
tar -czf $BACKUP_DIR/$APP_BACKUP -C /var/www/pmtool .

# Upload to cloud storage (optional)
aws s3 cp $BACKUP_DIR/$DB_BACKUP.gz s3://pmtool-backups/database/
aws s3 cp $BACKUP_DIR/$APP_BACKUP s3://pmtool-backups/application/

# Clean old backups (keep 30 days)
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete

echo "Backup completed: $DB_BACKUP.gz, $APP_BACKUP"
```

### Recovery Procedure
```bash
# Database recovery
gunzip pmtool_backup_20240320_020000.sql.gz
psql -h localhost -U pmtool_user -d pmtool_prod < pmtool_backup_20240320_020000.sql

# Application recovery
sudo systemctl stop pmtool
sudo rm -rf /var/www/pmtool/*
sudo tar -xzf pmtool_app_backup_20240320_020000.tar.gz -C /var/www/pmtool/
sudo chown -R pmtool:pmtool /var/www/pmtool
sudo systemctl start pmtool
```

## 🚨 Troubleshooting

### Common Deployment Issues

#### Application Won't Start
```bash
# Check logs
sudo journalctl -u pmtool.service -f

# Check configuration
dotnet PM.Tool.dll --environment Production --urls http://localhost:5000

# Verify dependencies
ldd /usr/share/dotnet/dotnet
```

#### Database Connection Issues
```bash
# Test database connection
psql -h localhost -U pmtool_user -d pmtool_prod -c "SELECT 1;"

# Check connection string
echo $ConnectionStrings__DefaultConnection

# Verify firewall
sudo ufw status
sudo netstat -tlnp | grep 5432
```

#### Performance Issues
```bash
# Monitor resources
htop
iotop
nethogs

# Check application metrics
curl http://localhost:5000/health
curl http://localhost:5000/metrics
```

### Rollback Procedure
```bash
# Stop current version
sudo systemctl stop pmtool

# Restore previous version
sudo cp -r /var/backups/pmtool/previous-version/* /var/www/pmtool/

# Rollback database (if needed)
psql -h localhost -U pmtool_user -d pmtool_prod < previous_backup.sql

# Start application
sudo systemctl start pmtool

# Verify rollback
curl http://localhost:5000/health
```

## 📞 Post-Deployment Checklist

### Verification Steps
- [ ] Application starts successfully
- [ ] Database connection works
- [ ] User authentication functions
- [ ] File uploads work
- [ ] Email notifications send
- [ ] SSL certificate valid
- [ ] Health checks pass
- [ ] Monitoring alerts configured
- [ ] Backup scripts tested
- [ ] Performance acceptable
- [ ] Security scan completed

### Go-Live Activities
- [ ] DNS updated to production
- [ ] Load balancer configured
- [ ] CDN configured
- [ ] Monitoring dashboards set up
- [ ] Support team notified
- [ ] Documentation updated
- [ ] User training completed
- [ ] Rollback plan tested

---

*This deployment guide provides comprehensive instructions for deploying PM Tool in various environments. For specific platform configurations or troubleshooting, please refer to the platform-specific documentation or contact the development team.*
