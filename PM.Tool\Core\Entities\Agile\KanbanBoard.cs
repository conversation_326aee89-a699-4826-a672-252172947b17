using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities.Agile
{
    public class KanbanBoard
    {
        public int ProjectId { get; set; }
        public virtual Project Project { get; set; } = null!;
        public virtual ICollection<KanbanColumn> Columns { get; set; } = new List<KanbanColumn>();
        public virtual ICollection<UserStory> UserStories { get; set; } = new List<UserStory>();
    }

    public class KanbanColumn : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? Description { get; set; }

        public int ProjectId { get; set; }

        public int Order { get; set; }

        [MaxLength(7)]
        public string Color { get; set; } = "#64748b"; // Default color

        public int WipLimit { get; set; } = 0; // 0 = no limit

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual Project Project { get; set; } = null!;
        public virtual ICollection<UserStory> UserStories { get; set; } = new List<UserStory>();
    }
}
