using System.Collections.Generic;

namespace PM.Tool.Models.ViewModels
{
    public class ChartDataViewModel
    {
        public List<ChartDataPoint> StatusByProject { get; set; } = new();
        public List<ChartDataPoint> StatusByTask { get; set; } = new();
        public List<ChartDataPoint> ProgressByTimelineProject { get; set; } = new();
        public List<ChartDataPoint> PriorityByTask { get; set; } = new();
        public List<ChartDataPoint> WorkloadByTeamMember { get; set; } = new();
    }
}
