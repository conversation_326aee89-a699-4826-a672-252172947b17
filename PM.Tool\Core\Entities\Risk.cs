using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities
{
    public class Risk : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [MaxLength(2000)]
        public string Description { get; set; } = string.Empty;

        [Required]
        public int ProjectId { get; set; }

        public int? TaskId { get; set; }

        public RiskCategory Category { get; set; } = RiskCategory.Technical;

        public RiskProbability Probability { get; set; } = RiskProbability.Medium;

        public RiskImpact Impact { get; set; } = RiskImpact.Medium;

        public RiskStatus Status { get; set; } = RiskStatus.Identified;

        public string? OwnerId { get; set; }

        private DateTime? _identifiedDate = DateTime.UtcNow;
        public DateTime? IdentifiedDate
        {
            get => _identifiedDate;
            set => _identifiedDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        private DateTime? _targetResolutionDate;
        public DateTime? TargetResolutionDate
        {
            get => _targetResolutionDate;
            set => _targetResolutionDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        private DateTime? _actualResolutionDate;
        public DateTime? ActualResolutionDate
        {
            get => _actualResolutionDate;
            set => _actualResolutionDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        [MaxLength(2000)]
        public string? MitigationPlan { get; set; }

        [MaxLength(2000)]
        public string? ContingencyPlan { get; set; }

        public decimal EstimatedCost { get; set; }

        public int EstimatedDelayDays { get; set; }

        public string CreatedByUserId { get; set; } = string.Empty;

        // Navigation properties
        public virtual Project Project { get; set; } = null!;
        public virtual TaskEntity? Task { get; set; }
        public virtual ApplicationUser? Owner { get; set; }
        public virtual ApplicationUser CreatedBy { get; set; } = null!;
        public virtual ICollection<RiskMitigationAction> MitigationActions { get; set; } = new List<RiskMitigationAction>();

        // Computed properties
        public int RiskScore => (int)Probability * (int)Impact;
        public RiskLevel RiskLevel => GetRiskLevel();
        public bool IsOverdue => TargetResolutionDate.HasValue && DateTime.UtcNow > TargetResolutionDate.Value && Status != RiskStatus.Resolved;
        public bool IsResolved => Status == RiskStatus.Resolved;

        private RiskLevel GetRiskLevel()
        {
            var score = RiskScore;
            return score switch
            {
                >= 15 => RiskLevel.Critical,
                >= 10 => RiskLevel.High,
                >= 6 => RiskLevel.Medium,
                _ => RiskLevel.Low
            };
        }
    }

    public class RiskMitigationAction : BaseEntity
    {
        public int RiskId { get; set; }

        [Required]
        [MaxLength(500)]
        public string Action { get; set; } = string.Empty;

        [MaxLength(1000)]
        public string? Description { get; set; }

        public string? AssignedToUserId { get; set; }

        private DateTime? _dueDate;
        public DateTime? DueDate
        {
            get => _dueDate;
            set => _dueDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        private DateTime? _completedDate;
        public DateTime? CompletedDate
        {
            get => _completedDate;
            set => _completedDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        public ActionStatus Status { get; set; } = ActionStatus.Planned;

        [MaxLength(1000)]
        public string? Notes { get; set; }

        // Navigation properties
        public virtual Risk Risk { get; set; } = null!;
        public virtual ApplicationUser? AssignedTo { get; set; }

        // Computed properties
        public bool IsCompleted => Status == ActionStatus.Completed;
        public bool IsOverdue => DueDate.HasValue && DateTime.UtcNow > DueDate.Value && Status != ActionStatus.Completed;
    }

    public enum RiskCategory
    {
        Technical = 1,
        Schedule = 2,
        Budget = 3,
        Resource = 4,
        Quality = 5,
        External = 6,
        Organizational = 7
    }

    public enum RiskProbability
    {
        VeryLow = 1,
        Low = 2,
        Medium = 3,
        High = 4,
        VeryHigh = 5
    }

    public enum RiskImpact
    {
        VeryLow = 1,
        Low = 2,
        Medium = 3,
        High = 4,
        VeryHigh = 5
    }

    public enum RiskStatus
    {
        Identified = 1,
        Analyzing = 2,
        Mitigating = 3,
        Monitoring = 4,
        Resolved = 5,
        Accepted = 6
    }

    public enum RiskLevel
    {
        Low = 1,
        Medium = 2,
        High = 3,
        Critical = 4
    }

    public enum ActionStatus
    {
        Planned = 1,
        InProgress = 2,
        Completed = 3,
        Cancelled = 4
    }
}
