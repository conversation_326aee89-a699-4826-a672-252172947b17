using PM.Tool.Core.Enums;
using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities
{
    public class AuditLog
    {
        public int Id { get; set; }

        public AuditAction Action { get; set; }

        [Required]
        [MaxLength(100)]
        public string EntityName { get; set; } = string.Empty;

        public int? EntityId { get; set; }

        [MaxLength(2000)]
        public string? OldValues { get; set; }

        [MaxLength(2000)]
        public string? NewValues { get; set; }

        public string UserId { get; set; } = string.Empty;

        [MaxLength(45)]
        public string? IpAddress { get; set; }

        [MaxLength(500)]
        public string? UserAgent { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual ApplicationUser User { get; set; } = null!;
    }
}
