using PM.Tool.Core.Entities;

namespace PM.Tool.Core.Interfaces
{
    public interface IWbsService
    {
        /// <summary>
        /// Generates WBS codes for all tasks in a project
        /// </summary>
        Task<bool> GenerateWbsCodesAsync(int projectId);
        
        /// <summary>
        /// Generates WBS code for a specific task based on its position
        /// </summary>
        Task<string> GenerateWbsCodeAsync(int projectId, int? parentTaskId, int sortOrder);
        
        /// <summary>
        /// Updates WBS codes when task hierarchy changes
        /// </summary>
        Task<bool> UpdateWbsCodesAsync(int projectId);
        
        /// <summary>
        /// Moves a task to a new position in the WBS
        /// </summary>
        Task<bool> MoveTaskAsync(int taskId, int? newParentTaskId, int newSortOrder);
        
        /// <summary>
        /// Gets the hierarchical task structure for WBS display
        /// </summary>
        Task<IEnumerable<TaskEntity>> GetWbsStructureAsync(int projectId);
        
        /// <summary>
        /// Validates WBS structure integrity
        /// </summary>
        Task<bool> ValidateWbsStructureAsync(int projectId);
        
        /// <summary>
        /// Gets the next available sort order for a parent task
        /// </summary>
        Task<int> GetNextSortOrderAsync(int projectId, int? parentTaskId);
        
        /// <summary>
        /// Reorders tasks within the same parent
        /// </summary>
        Task<bool> ReorderTasksAsync(int projectId, int? parentTaskId, List<int> taskIds);
    }
}
