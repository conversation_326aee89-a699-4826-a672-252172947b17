2025-06-14 09:57:39.096 +06:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 09:57:41.333 +06:00 [INF] Executed DbCommand (85ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 09:57:41.409 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 09:57:41.416 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 09:57:41.443 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__normalizedEmail_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
2025-06-14 09:57:41.994 +06:00 [INF] Executed DbCommand (13ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE t."DueDate" IS NOT NULL AND t."DueDate" <= @__upcomingDeadlines_0 AND t."DueDate" > now() AND t."Status" <> 4
2025-06-14 09:57:42.169 +06:00 [INF] Executed DbCommand (72ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", p0."Id", p0."IsActive", p0."JoinedAt", p0."ProjectId", p0."Role", p0."UserId"
FROM "Projects" AS p
LEFT JOIN "ProjectMembers" AS p0 ON p."Id" = p0."ProjectId"
WHERE p."EndDate" IS NOT NULL AND p."EndDate" <= @__upcomingDeadlines_0 AND p."EndDate" > now() AND p."Status" <> 5
ORDER BY p."Id"
2025-06-14 09:57:42.179 +06:00 [INF] Deadline check completed at: "2025-06-14T09:57:42.1743864+06:00"
2025-06-14 09:57:42.245 +06:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5045: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Only one usage of each socket address (protocol/network address/port) is normally permitted.
 ---> System.Net.Sockets.SocketException (10048): Only one usage of each socket address (protocol/network address/port) is normally permitted.
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__14_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-06-14 09:58:05.285 +06:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 09:58:07.778 +06:00 [INF] Executed DbCommand (89ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 09:58:07.849 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 09:58:07.865 +06:00 [INF] Executed DbCommand (3ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 09:58:07.885 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__normalizedEmail_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
2025-06-14 09:58:08.335 +06:00 [INF] Executed DbCommand (15ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE t."DueDate" IS NOT NULL AND t."DueDate" <= @__upcomingDeadlines_0 AND t."DueDate" > now() AND t."Status" <> 4
2025-06-14 09:58:08.471 +06:00 [INF] Executed DbCommand (30ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", p0."Id", p0."IsActive", p0."JoinedAt", p0."ProjectId", p0."Role", p0."UserId"
FROM "Projects" AS p
LEFT JOIN "ProjectMembers" AS p0 ON p."Id" = p0."ProjectId"
WHERE p."EndDate" IS NOT NULL AND p."EndDate" <= @__upcomingDeadlines_0 AND p."EndDate" > now() AND p."Status" <> 5
ORDER BY p."Id"
2025-06-14 09:58:08.478 +06:00 [INF] Deadline check completed at: "2025-06-14T09:58:08.4766660+06:00"
2025-06-14 09:58:08.557 +06:00 [INF] Now listening on: https://localhost:7029
2025-06-14 09:58:08.559 +06:00 [INF] Now listening on: http://localhost:5045
2025-06-14 09:58:08.623 +06:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 09:58:08.627 +06:00 [INF] Hosting environment: Development
2025-06-14 09:58:08.631 +06:00 [INF] Content root path: D:\TGI\PM.Tool\PM.Tool
2025-06-14 09:58:10.146 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/ - null null
2025-06-14 09:58:10.507 +06:00 [INF] Executed DbCommand (24ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-14 09:58:10.545 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__user_Id_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ClaimType", a."ClaimValue", a."UserId"
FROM "AspNetUserClaims" AS a
WHERE a."UserId" = @__user_Id_0
2025-06-14 09:58:10.570 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a0."Name"
FROM "AspNetUserRoles" AS a
INNER JOIN "AspNetRoles" AS a0 ON a."RoleId" = a0."Id"
WHERE a."UserId" = @__userId_0
2025-06-14 09:58:10.580 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 09:58:10.596 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__role_Id_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."ClaimType", a."ClaimValue"
FROM "AspNetRoleClaims" AS a
WHERE a."RoleId" = @__role_Id_0
2025-06-14 09:58:10.620 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-14 09:58:10.646 +06:00 [INF] Route matched with {action = "Index", controller = "Home", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-14 09:58:10.704 +06:00 [WRN] Compiling a query which loads related collections for more than one collection navigation, either via 'Include' or through projection, but no 'QuerySplittingBehavior' has been configured. By default, Entity Framework will use 'QuerySplittingBehavior.SingleQuery', which can potentially result in slow query performance. See https://go.microsoft.com/fwlink/?linkid=2134277 for more information. To identify the query that's triggering this warning call 'ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning))'.
2025-06-14 09:58:10.745 +06:00 [INF] Executed DbCommand (24ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-14 09:58:10.864 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-14 09:58:10.896 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" < now()
ORDER BY t."DueDate"
2025-06-14 09:58:10.916 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__dueDate_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
FROM "Tasks" AS t
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" <= @__dueDate_0
2025-06-14 09:58:10.949 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT n."Id", n."Title", n."Message", n."Type", n."IsRead", n."CreatedAt", n."RelatedProjectId", n."RelatedTaskId"
FROM "Notifications" AS n
WHERE n."UserId" = @__user_Id_0
ORDER BY n."CreatedAt" DESC
LIMIT @__p_1
2025-06-14 09:58:10.985 +06:00 [INF] Executed DbCommand (13ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."Name", m."Description", m."ProjectId", p."Name", m."DueDate", m."CompletedDate", m."IsCompleted", m."CreatedAt", m."IsDeleted", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."StartDate", p."Status", p."UpdatedAt"
FROM "Milestones" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
WHERE EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__user_Id_0 AND p0."IsActive") AND NOT (m."IsCompleted") AND m."DueDate" >= now()
ORDER BY m."DueDate"
LIMIT @__p_1
2025-06-14 09:58:11.001 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-14 09:58:11.204 +06:00 [INF] Executed ViewResult - view Index executed in 195.808ms.
2025-06-14 09:58:11.204 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/js/tailwind-theme-manager.js?v=QS9OsoodixBTZAmxGj2gDzPtp60VU7kbda0nOueg4T0 - null null
2025-06-14 09:58:11.205 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/css/tailwind.css?v=Vk1BkLOsT7-Yh4o1pDln8tZyZ3_rFi9yXuRHMX2g50E - null null
2025-06-14 09:58:11.204 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/lib/jquery/dist/jquery.min.js - null null
2025-06-14 09:58:11.267 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/js/site.js?v=gfa0xw_jEM_w5r3HMCk1LwRfuj7T6ktfvUiYt5ZeuS4 - null null
2025-06-14 09:58:11.276 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.Index (PM.Tool) in 625.1853ms
2025-06-14 09:58:11.287 +06:00 [INF] The file /js/tailwind-theme-manager.js was not modified
2025-06-14 09:58:11.287 +06:00 [INF] The file /css/tailwind.css was not modified
2025-06-14 09:58:11.298 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-14 09:58:11.290 +06:00 [INF] The file /lib/jquery/dist/jquery.min.js was not modified
2025-06-14 09:58:11.295 +06:00 [INF] The file /js/site.js was not modified
2025-06-14 09:58:11.309 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/css/tailwind.css?v=Vk1BkLOsT7-Yh4o1pDln8tZyZ3_rFi9yXuRHMX2g50E - 304 null text/css 96.8832ms
2025-06-14 09:58:11.309 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/js/tailwind-theme-manager.js?v=QS9OsoodixBTZAmxGj2gDzPtp60VU7kbda0nOueg4T0 - 304 null text/javascript 97.2905ms
2025-06-14 09:58:11.312 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/lib/jquery/dist/jquery.min.js - 304 null text/javascript 107.959ms
2025-06-14 09:58:11.314 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/js/site.js?v=gfa0xw_jEM_w5r3HMCk1LwRfuj7T6ktfvUiYt5ZeuS4 - 304 null text/javascript 47.7634ms
2025-06-14 09:58:11.317 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/ - 200 null text/html; charset=utf-8 1179.901ms
2025-06-14 09:58:11.987 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-14 09:58:11.987 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-14 09:58:12.001 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 14.9995ms
2025-06-14 09:58:13.820 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 1833.8459ms
2025-06-14 09:59:11.651 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Home/SetLanguage?culture=en-US&returnUrl=%2F - null null
2025-06-14 09:59:11.860 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.SetLanguage (PM.Tool)'
2025-06-14 09:59:11.869 +06:00 [INF] Route matched with {action = "SetLanguage", controller = "Home", page = "", area = ""}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult SetLanguage(System.String, System.String) on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-14 09:59:11.894 +06:00 [INF] Language set to en-US, Cookie: c=en-US|uic=en-US
2025-06-14 09:59:11.898 +06:00 [INF] Executing RedirectResult, redirecting to /.
2025-06-14 09:59:11.901 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.SetLanguage (PM.Tool) in 27.8418ms
2025-06-14 09:59:11.904 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.SetLanguage (PM.Tool)'
2025-06-14 09:59:11.906 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Home/SetLanguage?culture=en-US&returnUrl=%2F - 302 0 null 255.3675ms
2025-06-14 09:59:11.912 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/ - null null
2025-06-14 09:59:11.948 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-14 09:59:11.952 +06:00 [INF] Route matched with {action = "Index", controller = "Home", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller PM.Tool.Controllers.HomeController (PM.Tool).
2025-06-14 09:59:11.966 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."Id" = @__p_0
LIMIT 1
2025-06-14 09:59:11.985 +06:00 [INF] Executed DbCommand (11ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName", p2."Id", p2."IsActive", p2."JoinedAt", p2."ProjectId", p2."Role", p2."UserId", t0."Id", t0."ActualHours", t0."AssignedToUserId", t0."CompletedDate", t0."CreatedAt", t0."CreatedByUserId", t0."DeletedAt", t0."Description", t0."DueDate", t0."EstimatedHours", t0."IsDeleted", t0."IsRecurring", t0."IsTemplate", t0."ParentTaskId", t0."Priority", t0."ProjectId", t0."RecurrenceEndDate", t0."RecurrencePattern", t0."SortOrder", t0."StartDate", t0."Status", t0."TemplateId", t0."Title", t0."UpdatedAt", t0."UserStoryId", t0."WbsCode", t0."WbsLevel"
FROM "Projects" AS p
INNER JOIN "AspNetUsers" AS a ON p."CreatedByUserId" = a."Id"
INNER JOIN "AspNetUsers" AS a0 ON p."ManagerId" = a0."Id"
LEFT JOIN (
    SELECT p1."Id", p1."IsActive", p1."JoinedAt", p1."ProjectId", p1."Role", p1."UserId"
    FROM "ProjectMembers" AS p1
    WHERE p1."IsActive"
) AS p2 ON p."Id" = p2."ProjectId"
LEFT JOIN (
    SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
    FROM "Tasks" AS t
    WHERE NOT (t."IsDeleted")
) AS t0 ON p."Id" = t0."ProjectId"
WHERE NOT (p."IsDeleted") AND NOT (p."IsDeleted") AND p."Status" IN (2, 3, 1) AND (p."CreatedByUserId" = @__userId_0 OR EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__userId_0 AND p0."IsActive"))
ORDER BY p."UpdatedAt" DESC, p."Id", a."Id", a0."Id", p2."Id"
2025-06-14 09:59:12.000 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__userId_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
INNER JOIN "AspNetUsers" AS a ON t."CreatedByUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND t."AssignedToUserId" = @__userId_0 AND NOT (t."IsDeleted") AND t."Status" <> 4
ORDER BY t."DueDate" DESC
2025-06-14 09:59:12.014 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" < now()
ORDER BY t."DueDate"
2025-06-14 09:59:12.029 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__dueDate_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel"
FROM "Tasks" AS t
WHERE NOT (t."IsDeleted") AND NOT (t."IsDeleted") AND t."Status" <> 4 AND t."DueDate" IS NOT NULL AND t."DueDate" <= @__dueDate_0
2025-06-14 09:59:12.045 +06:00 [INF] Executed DbCommand (6ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT n."Id", n."Title", n."Message", n."Type", n."IsRead", n."CreatedAt", n."RelatedProjectId", n."RelatedTaskId"
FROM "Notifications" AS n
WHERE n."UserId" = @__user_Id_0
ORDER BY n."CreatedAt" DESC
LIMIT @__p_1
2025-06-14 09:59:12.064 +06:00 [INF] Executed DbCommand (9ms) [Parameters=[@__user_Id_0='?', @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT m."Id", m."Name", m."Description", m."ProjectId", p."Name", m."DueDate", m."CompletedDate", m."IsCompleted", m."CreatedAt", m."IsDeleted", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."StartDate", p."Status", p."UpdatedAt"
FROM "Milestones" AS m
INNER JOIN "Projects" AS p ON m."ProjectId" = p."Id"
WHERE EXISTS (
    SELECT 1
    FROM "ProjectMembers" AS p0
    WHERE p."Id" = p0."ProjectId" AND p0."UserId" = @__user_Id_0 AND p0."IsActive") AND NOT (m."IsCompleted") AND m."DueDate" >= now()
ORDER BY m."DueDate"
LIMIT @__p_1
2025-06-14 09:59:12.075 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-14 09:59:12.110 +06:00 [INF] Executed ViewResult - view Index executed in 35.5427ms.
2025-06-14 09:59:12.115 +06:00 [INF] Executed action PM.Tool.Controllers.HomeController.Index (PM.Tool) in 158.8962ms
2025-06-14 09:59:12.120 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.HomeController.Index (PM.Tool)'
2025-06-14 09:59:12.122 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-14 09:59:12.123 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-14 09:59:12.124 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/ - 200 null text/html; charset=utf-8 212.5755ms
2025-06-14 09:59:12.132 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 9.7716ms
2025-06-14 09:59:12.142 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 19.3504ms
2025-06-14 09:59:23.234 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Requirement - null null
2025-06-14 09:59:23.244 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RequirementController.Index (PM.Tool)'
2025-06-14 09:59:23.255 +06:00 [INF] Route matched with {action = "Index", controller = "Requirement", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index(System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RequirementController (PM.Tool).
2025-06-14 09:59:23.325 +06:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT r."Id", r."AcceptanceCriteria", r."AnalystUserId", r."ApprovedByUserId", r."ApprovedDate", r."BlockedReason", r."BusinessJustification", r."CreatedAt", r."DeletedAt", r."Description", r."DeveloperUserId", r."EstimatedEffort", r."IsBlocked", r."IsDeleted", r."Notes", r."ParentRequirementId", r."Priority", r."ProjectId", r."RequirementId", r."Source", r."StakeholderUserId", r."Status", r."Tags", r."TargetDate", r."TestCases", r."TesterUserId", r."Title", r."Type", r."UpdatedAt", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName", a0."Id", a0."AccessFailedCount", a0."Bio", a0."ConcurrencyStamp", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."FirstName", a0."IsActive", a0."LastLoginAt", a0."LastName", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."ProfilePictureUrl", a0."SecurityStamp", a0."TwoFactorEnabled", a0."UserName"
FROM "Requirements" AS r
INNER JOIN "Projects" AS p ON r."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON r."DeveloperUserId" = a."Id"
LEFT JOIN "AspNetUsers" AS a0 ON r."StakeholderUserId" = a0."Id"
ORDER BY r."CreatedAt" DESC
2025-06-14 09:59:23.340 +06:00 [INF] Executing ViewResult, running view Index.
2025-06-14 09:59:23.380 +06:00 [INF] Executed ViewResult - view Index executed in 40.7446ms.
2025-06-14 09:59:23.384 +06:00 [INF] Executed action PM.Tool.Controllers.RequirementController.Index (PM.Tool) in 123.6781ms
2025-06-14 09:59:23.387 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RequirementController.Index (PM.Tool)'
2025-06-14 09:59:23.390 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Requirement - 200 null text/html; charset=utf-8 155.4593ms
2025-06-14 09:59:23.392 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-14 09:59:23.392 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-14 09:59:23.404 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 11.7652ms
2025-06-14 09:59:23.420 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 28.4772ms
2025-06-14 09:59:23.472 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Project/GetProjects - null null
2025-06-14 09:59:23.483 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Project/GetProjects - 404 0 null 11.1351ms
2025-06-14 09:59:23.491 +06:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7029/Project/GetProjects, Response status code: 404
2025-06-14 09:59:25.400 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Requirement/Create - null null
2025-06-14 09:59:25.406 +06:00 [INF] Executing endpoint 'PM.Tool.Controllers.RequirementController.Create (PM.Tool)'
2025-06-14 09:59:25.414 +06:00 [INF] Route matched with {action = "Create", controller = "Requirement", page = "", area = ""}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Create(System.Nullable`1[System.Int32], System.Nullable`1[System.Int32]) on controller PM.Tool.Controllers.RequirementController (PM.Tool).
2025-06-14 09:59:25.479 +06:00 [INF] Executing ViewResult, running view Create.
2025-06-14 09:59:25.522 +06:00 [INF] Executed ViewResult - view Create executed in 49.7458ms.
2025-06-14 09:59:25.525 +06:00 [INF] Executed action PM.Tool.Controllers.RequirementController.Create (PM.Tool) in 105.0538ms
2025-06-14 09:59:25.531 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - null null
2025-06-14 09:59:25.532 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/lib/jquery-validation/dist/jquery.validate.min.js - null null
2025-06-14 09:59:25.549 +06:00 [INF] The file /lib/jquery-validation/dist/jquery.validate.min.js was not modified
2025-06-14 09:59:25.534 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/_vs/browserLink - null null
2025-06-14 09:59:25.536 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_framework/aspnetcore-browser-refresh.js - 200 13766 application/javascript; charset=utf-8 4.0156ms
2025-06-14 09:59:25.532 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - null null
2025-06-14 09:59:25.532 +06:00 [INF] Executed endpoint 'PM.Tool.Controllers.RequirementController.Create (PM.Tool)'
2025-06-14 09:59:25.551 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/lib/jquery-validation/dist/jquery.validate.min.js - 304 null text/javascript 19.2754ms
2025-06-14 09:59:25.564 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/_vs/browserLink - 200 null text/javascript; charset=UTF-8 30.1889ms
2025-06-14 09:59:25.570 +06:00 [INF] The file /lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js was not modified
2025-06-14 09:59:25.571 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Requirement/Create - 200 null text/html; charset=utf-8 171.3416ms
2025-06-14 09:59:25.592 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js - 304 null text/javascript 60.2867ms
2025-06-14 09:59:25.620 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/User/GetUsers - null null
2025-06-14 09:59:25.620 +06:00 [INF] Request starting HTTP/2 GET https://localhost:7029/Project/GetProjects - null null
2025-06-14 09:59:25.626 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/User/GetUsers - 404 0 null 6.2183ms
2025-06-14 09:59:25.631 +06:00 [INF] Request finished HTTP/2 GET https://localhost:7029/Project/GetProjects - 404 0 null 10.8365ms
2025-06-14 09:59:25.634 +06:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7029/User/GetUsers, Response status code: 404
2025-06-14 09:59:25.641 +06:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7029/Project/GetProjects, Response status code: 404
2025-06-14 11:30:05.970 +06:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-14 11:30:07.919 +06:00 [INF] Executed DbCommand (40ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 11:30:07.998 +06:00 [INF] Executed DbCommand (7ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 11:30:08.006 +06:00 [INF] Executed DbCommand (4ms) [Parameters=[@__normalizedName_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."ConcurrencyStamp", a."Name", a."NormalizedName"
FROM "AspNetRoles" AS a
WHERE a."NormalizedName" = @__normalizedName_0
LIMIT 1
2025-06-14 11:30:08.027 +06:00 [INF] Executed DbCommand (5ms) [Parameters=[@__normalizedEmail_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2
2025-06-14 11:30:08.438 +06:00 [INF] Executed DbCommand (16ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."ActualHours", t."AssignedToUserId", t."CompletedDate", t."CreatedAt", t."CreatedByUserId", t."DeletedAt", t."Description", t."DueDate", t."EstimatedHours", t."IsDeleted", t."IsRecurring", t."IsTemplate", t."ParentTaskId", t."Priority", t."ProjectId", t."RecurrenceEndDate", t."RecurrencePattern", t."SortOrder", t."StartDate", t."Status", t."TemplateId", t."Title", t."UpdatedAt", t."UserStoryId", t."WbsCode", t."WbsLevel", p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", a."Id", a."AccessFailedCount", a."Bio", a."ConcurrencyStamp", a."CreatedAt", a."Email", a."EmailConfirmed", a."FirstName", a."IsActive", a."LastLoginAt", a."LastName", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PhoneNumber", a."PhoneNumberConfirmed", a."ProfilePictureUrl", a."SecurityStamp", a."TwoFactorEnabled", a."UserName"
FROM "Tasks" AS t
INNER JOIN "Projects" AS p ON t."ProjectId" = p."Id"
LEFT JOIN "AspNetUsers" AS a ON t."AssignedToUserId" = a."Id"
WHERE t."DueDate" IS NOT NULL AND t."DueDate" <= @__upcomingDeadlines_0 AND t."DueDate" > now() AND t."Status" <> 4
2025-06-14 11:30:08.464 +06:00 [INF] Now listening on: http://localhost:5045
2025-06-14 11:30:08.470 +06:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-14 11:30:08.473 +06:00 [INF] Hosting environment: Development
2025-06-14 11:30:08.477 +06:00 [INF] Content root path: D:\TGI\PM.Tool\PM.Tool
2025-06-14 11:30:08.488 +06:00 [INF] Executed DbCommand (8ms) [Parameters=[@__upcomingDeadlines_0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."ActualEndDate", p."Budget", p."ClientName", p."CreatedAt", p."CreatedByUserId", p."DeletedAt", p."Description", p."EndDate", p."IsCompleted", p."IsDeleted", p."ManagerId", p."Name", p."StartDate", p."Status", p."UpdatedAt", p0."Id", p0."IsActive", p0."JoinedAt", p0."ProjectId", p0."Role", p0."UserId"
FROM "Projects" AS p
LEFT JOIN "ProjectMembers" AS p0 ON p."Id" = p0."ProjectId"
WHERE p."EndDate" IS NOT NULL AND p."EndDate" <= @__upcomingDeadlines_0 AND p."EndDate" > now() AND p."Status" <> 5
ORDER BY p."Id"
2025-06-14 11:30:08.499 +06:00 [INF] Deadline check completed at: "2025-06-14T11:30:08.4989954+06:00"
