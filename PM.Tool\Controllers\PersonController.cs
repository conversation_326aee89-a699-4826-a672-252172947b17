using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Enums;
using PM.Tool.Services;
using PM.Tool.Models.ViewModels;

namespace PM.Tool.Controllers
{
    /// <summary>
    /// Unified People Management Controller
    /// Handles all person-related operations including employees, stakeholders, contractors, etc.
    /// </summary>
    public class PersonController : SecureBaseController
    {
        private readonly IPersonService _personService;
        private readonly IProjectService _projectService;
        private readonly IFormHelperService _formHelper;
        private readonly ILogger<PersonController> _logger;

        public PersonController(
            IPersonService personService,
            IProjectService projectService,
            IFormHelperService formHelper,
            IAuditService auditService,
            ILogger<PersonController> logger) : base(auditService)
        {
            _personService = personService;
            _projectService = projectService;
            _formHelper = formHelper;
            _logger = logger;
        }

        // GET: Person
        public async Task<IActionResult> Index(PersonType? type = null, PersonStatus? status = null, string? department = null)
        {
            try
            {
                IEnumerable<Person> people;

                if (type.HasValue)
                {
                    people = await _personService.GetPeopleByTypeAsync(type.Value);
                }
                else if (status.HasValue)
                {
                    people = await _personService.GetPeopleByStatusAsync(status.Value);
                }
                else if (!string.IsNullOrEmpty(department))
                {
                    people = await _personService.GetPeopleByDepartmentAsync(department);
                }
                else
                {
                    people = await _personService.GetAllPeopleAsync();
                }

                ViewBag.CurrentType = type;
                ViewBag.CurrentStatus = status;
                ViewBag.CurrentDepartment = department;

                return View(people);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading people.";
                return View(new List<Person>());
            }
        }

        // GET: Person/Details/5
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var person = await _personService.GetPersonByIdAsync(id);
                if (person == null) return NotFound();

                return View(person);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading person details.";
                return RedirectToAction("Index");
            }
        }

        // GET: Person/Create
        public async Task<IActionResult> Create(PersonType? type = null)
        {
            try
            {
                var viewModel = new PersonCreateViewModel();
                if (type.HasValue)
                {
                    viewModel.Type = type.Value;
                }

                await PopulateDropdowns();
                return View(viewModel);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading person creation form.";
                return RedirectToAction("Index");
            }
        }

        // POST: Person/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(PersonCreateViewModel viewModel)
        {
            return await this.HandleCreateAsync<PersonCreateViewModel, Person>(
                viewModel,
                async (person) => {
                    var createdPerson = await _personService.CreatePersonAsync(person);
                    await LogAuditAsync(AuditAction.Create, "Person", createdPerson.Id);
                    return createdPerson;
                },
                person => person.Id,
                PopulateDropdowns,
                _formHelper,
                _logger,
                "Person"
            );
        }

        // GET: Person/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                var person = await _personService.GetPersonByIdAsync(id);
                if (person == null) return NotFound();

                var viewModel = PersonEditViewModel.FromEntity(person);
                await PopulateDropdowns();
                return View(viewModel);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading person for editing.";
                return RedirectToAction("Index");
            }
        }

        // POST: Person/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, PersonEditViewModel viewModel)
        {
            if (id != viewModel.Id) return NotFound();

            return await this.HandleUpdateAsync<PersonEditViewModel, Person>(
                id,
                viewModel,
                async (personId) => await _personService.GetPersonByIdAsync(personId),
                async (person) => {
                    await _personService.UpdatePersonAsync(person);
                    await LogAuditAsync(AuditAction.Update, "Person", id);
                    return person;
                },
                PopulateDropdowns,
                _formHelper,
                _logger,
                "Person"
            );
        }

        // GET: Person/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var person = await _personService.GetPersonByIdAsync(id);
                if (person == null) return NotFound();

                return View(person);
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading person for deletion.";
                return RedirectToAction("Index");
            }
        }

        // POST: Person/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var success = await _personService.DeletePersonAsync(id);
                if (success)
                {
                    await LogAuditAsync(AuditAction.Delete, "Person", id);
                    TempData["Success"] = "Person deleted successfully.";
                }
                else
                {
                    TempData["Error"] = "Error deleting person.";
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = ex.Message.Contains("active project") ? ex.Message : "Error deleting person.";
            }

            return RedirectToAction("Index");
        }

        // POST: Person/Deactivate/5
        [HttpPost]
        public async Task<IActionResult> Deactivate(int id)
        {
            try
            {
                var success = await _personService.DeactivatePersonAsync(id);
                if (success)
                {
                    await LogAuditAsync(AuditAction.Update, "Person", id);
                    return Json(new { success = true, message = "Person deactivated successfully." });
                }
                return Json(new { success = false, message = "Error deactivating person." });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Error deactivating person." });
            }
        }

        // POST: Person/Activate/5
        [HttpPost]
        public async Task<IActionResult> Activate(int id)
        {
            try
            {
                var success = await _personService.ActivatePersonAsync(id);
                if (success)
                {
                    await LogAuditAsync(AuditAction.Update, "Person", id);
                    return Json(new { success = true, message = "Person activated successfully." });
                }
                return Json(new { success = false, message = "Error activating person." });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Error activating person." });
            }
        }

        // POST: Person/ToggleSystemAccess
        [HttpPost]
        public async Task<IActionResult> ToggleSystemAccess(int id, bool hasAccess)
        {
            try
            {
                var person = await _personService.GetPersonByIdAsync(id);
                if (person == null)
                {
                    return Json(new { success = false, message = "Person not found." });
                }

                person.HasSystemAccess = hasAccess;
                await _personService.UpdatePersonAsync(person);
                await LogAuditAsync(AuditAction.Update, "Person", id);

                var action = hasAccess ? "granted" : "revoked";
                return Json(new { success = true, message = $"System access {action} successfully." });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Error updating system access." });
            }
        }

        // POST: Person/ToggleStatus
        [HttpPost]
        public async Task<IActionResult> ToggleStatus(int id, string status)
        {
            try
            {
                var person = await _personService.GetPersonByIdAsync(id);
                if (person == null)
                {
                    return Json(new { success = false, message = "Person not found." });
                }

                if (Enum.TryParse<PersonStatus>(status, out var personStatus))
                {
                    person.Status = personStatus;
                    await _personService.UpdatePersonAsync(person);
                    await LogAuditAsync(AuditAction.Update, "Person", id);

                    return Json(new { success = true, message = $"Person status updated to {status} successfully." });
                }

                return Json(new { success = false, message = "Invalid status value." });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Error updating person status." });
            }
        }

        // GET: Person/Search
        public async Task<IActionResult> Search(string term)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(term))
                {
                    return Json(new List<object>());
                }

                var people = await _personService.SearchPeopleAsync(term);
                var result = people.Take(20).Select(p => new {
                    id = p.Id,
                    personCode = p.PersonCode,
                    name = p.FullName,
                    email = p.Email,
                    title = p.Title,
                    organization = p.Organization,
                    department = p.Department,
                    type = p.Type.ToString(),
                    status = p.Status.ToString(),
                    hasSystemAccess = p.HasSystemAccess
                });

                return Json(result);
            }
            catch (Exception ex)
            {
                return Json(new List<object>());
            }
        }

        // API: Get People for dropdowns
        [HttpGet]
        public async Task<IActionResult> GetPeople(PersonType? type = null, bool activeOnly = true)
        {
            try
            {
                IEnumerable<Person> people;

                if (type.HasValue)
                {
                    people = await _personService.GetPeopleByTypeAsync(type.Value);
                }
                else if (activeOnly)
                {
                    people = await _personService.GetActivePeopleAsync();
                }
                else
                {
                    people = await _personService.GetAllPeopleAsync();
                }

                var result = people.Select(p => new {
                    id = p.Id,
                    personCode = p.PersonCode,
                    name = p.FullName,
                    email = p.Email,
                    title = p.Title,
                    organization = p.Organization,
                    type = p.Type.ToString(),
                    displayName = p.DisplayName,
                    fullContact = p.FullContact
                });

                return Json(result);
            }
            catch (Exception ex)
            {
                return Json(new List<object>());
            }
        }

        // GET: Person/Employees
        public async Task<IActionResult> Employees()
        {
            return await Index(PersonType.Internal);
        }

        // GET: Person/Contractors
        public async Task<IActionResult> Contractors()
        {
            return await Index(PersonType.Contractor);
        }

        // GET: Person/Stakeholders
        public async Task<IActionResult> Stakeholders()
        {
            return await Index(PersonType.External);
        }

        // GET: Person/Analytics
        public async Task<IActionResult> Analytics()
        {
            try
            {
                var typeDistribution = await _personService.GetPersonTypeDistributionAsync();
                var statusDistribution = await _personService.GetPersonStatusDistributionAsync();
                var departmentDistribution = await _personService.GetDepartmentDistributionAsync();

                ViewBag.TypeDistribution = typeDistribution;
                ViewBag.StatusDistribution = statusDistribution;
                ViewBag.DepartmentDistribution = departmentDistribution;

                return View();
            }
            catch (Exception ex)
            {
                TempData["Error"] = "Error loading analytics.";
                return RedirectToAction("Index");
            }
        }

        private async Task PopulateDropdowns()
        {
            // Person Types
            ViewBag.PersonTypes = Enum.GetValues<PersonType>()
                .Select(t => new SelectListItem
                {
                    Value = ((int)t).ToString(),
                    Text = t.ToString()
                }).ToList();

            // Person Status
            ViewBag.PersonStatuses = Enum.GetValues<PersonStatus>()
                .Select(s => new SelectListItem
                {
                    Value = ((int)s).ToString(),
                    Text = s.ToString()
                }).ToList();

            // Time Zones (simplified list)
            ViewBag.TimeZones = new List<SelectListItem>
            {
                new SelectListItem { Value = "UTC", Text = "UTC" },
                new SelectListItem { Value = "UTC+06:00", Text = "UTC+06:00 (Dhaka)" },
                new SelectListItem { Value = "UTC-05:00", Text = "UTC-05:00 (Eastern)" },
                new SelectListItem { Value = "UTC-08:00", Text = "UTC-08:00 (Pacific)" },
                new SelectListItem { Value = "UTC+01:00", Text = "UTC+01:00 (Central European)" },
                new SelectListItem { Value = "UTC+05:30", Text = "UTC+05:30 (India)" }
            };
        }
    }
}
