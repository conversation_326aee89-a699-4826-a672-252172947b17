# PM.Tool User Flow Guide

## 📋 **Table of Contents**
1. [System Overview](#system-overview)
2. [User Roles & Permissions](#user-roles--permissions)
3. [Core User Flows](#core-user-flows)
4. [Agile/Scrum Workflow](#agilescrum-workflow)
5. [Azure DevOps Alignment](#azure-devops-alignment)
6. [Integration Points](#integration-points)

---

## 🏗️ **System Overview**

```ascii
┌─────────────────────────────────────────────────────────────────────────────┐
│                            PM.Tool Architecture                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   Project   │  │    Agile    │  │  Resource   │  │  Document   │        │
│  │ Management  │  │   Module    │  │ Management  │  │ Management  │        │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘        │
│         │                 │                 │                 │            │
│         └─────────────────┼─────────────────┼─────────────────┘            │
│                           │                 │                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │    Risk     │  │ Requirements│  │   Meeting   │  │   Reports   │        │
│  │ Management  │  │ Management  │  │ Management  │  │ & Analytics │        │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘        │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

---

## 👥 **User Roles & Permissions**

```ascii
┌─────────────────────────────────────────────────────────────────────────────┐
│                              User Hierarchy                                 │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│                           ┌─────────────┐                                   │
│                           │    Admin    │                                   │
│                           │ (Full Access)│                                  │
│                           └─────────────┘                                   │
│                                   │                                         │
│                           ┌─────────────┐                                   │
│                           │   Project   │                                   │
│                           │   Manager   │                                   │
│                           │(Project Scope)│                                 │
│                           └─────────────┘                                   │
│                                   │                                         │
│                    ┌──────────────┼──────────────┐                          │
│                    │              │              │                          │
│            ┌─────────────┐ ┌─────────────┐ ┌─────────────┐                  │
│            │    Team     │ │   Scrum     │ │ Stakeholder │                  │
│            │   Member    │ │   Master    │ │             │                  │
│            │(Task Level) │ │(Sprint Mgmt)│ │(View Only)  │                  │
│            └─────────────┘ └─────────────┘ └─────────────┘                  │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

---

## 🔄 **Core User Flows**

### **1. Project Creation & Setup Flow**

```ascii
┌─────────────────────────────────────────────────────────────────────────────┐
│                          Project Creation Flow                              │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  [Login] → [Dashboard] → [Projects] → [Create Project]                      │
│                                              │                              │
│                                              ▼                              │
│                                    ┌─────────────────┐                      │
│                                    │  Project Setup  │                      │
│                                    │ • Name & Desc   │                      │
│                                    │ • Dates & Budget│                      │
│                                    │ • Manager       │                      │
│                                    │ • Client Info   │                      │
│                                    └─────────────────┘                      │
│                                              │                              │
│                                              ▼                              │
│                                    ┌─────────────────┐                      │
│                                    │  Team Assembly  │                      │
│                                    │ • Add Members   │                      │
│                                    │ • Assign Roles  │                      │
│                                    │ • Set Permissions│                     │
│                                    └─────────────────┘                      │
│                                              │                              │
│                                              ▼                              │
│                                    ┌─────────────────┐                      │
│                                    │ Methodology     │                      │
│                                    │ Selection       │                      │
│                                    │ • Traditional   │                      │
│                                    │ • Agile/Scrum   │                      │
│                                    │ • Hybrid        │                      │
│                                    └─────────────────┘                      │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### **2. Traditional Project Management Flow**

```ascii
┌─────────────────────────────────────────────────────────────────────────────┐
│                      Traditional Project Flow                               │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐   │
│  │ Requirements│ →  │    WBS      │ →  │   Tasks     │ →  │ Scheduling  │   │
│  │ Gathering   │    │ Creation    │    │ Definition  │    │ & Timeline  │   │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘   │
│         │                   │                   │                   │       │
│         ▼                   ▼                   ▼                   ▼       │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐   │
│  │ Stakeholder │    │ Hierarchical│    │ Dependencies│    │ Resource    │   │
│  │ Analysis    │    │ Breakdown   │    │ & Estimates │    │ Allocation  │   │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘   │
│                                                                             │
│                              ┌─────────────┐                                │
│                              │ Execution   │                                │
│                              │ & Tracking  │                                │
│                              │ • Progress  │                                │
│                              │ • Issues    │                                │
│                              │ • Changes   │                                │
│                              └─────────────┘                                │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

---

## 🏃‍♂️ **Agile/Scrum Workflow**

### **Current Implementation vs Azure DevOps**

```ascii
┌─────────────────────────────────────────────────────────────────────────────┐
│                    Agile Workflow Comparison                                │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  CURRENT PM.Tool Implementation:                                           │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐                     │
│  │    Epic     │ →  │ User Story  │ →  │    Task     │                     │
│  │ (EP-001)    │    │ (US-001)    │    │ (Regular)   │                     │
│  └─────────────┘    └─────────────┘    └─────────────┘                     │
│         │                   │                   │                          │
│         ▼                   ▼                   ▼                          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐                     │
│  │ • Title     │    │ • As/I/So   │    │ • Standard  │                     │
│  │ • Desc      │    │ • Story Pts │    │   Task Mgmt │                     │
│  │ • Story Pts │    │ • Sprint    │    │ • Status    │                     │
│  │ • Priority  │    │ • Kanban    │    │ • Priority  │                     │
│  └─────────────┘    └─────────────┘    └─────────────┘                     │
│                                                                             │
│  AZURE DEVOPS STANDARD:                                                    │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐  │
│  │   Feature   │ →  │ User Story  │ →  │    Task     │ →  │    Bug      │  │
│  │ (Feature)   │    │ (User Story)│    │ (Task)      │    │ (Bug)       │  │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘  │
│         │                   │                   │                   │      │
│         ▼                   ▼                   ▼                   ▼      │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐  │
│  │ • Epic Link │    │ • Acceptance│    │ • Remaining │    │ • Severity  │  │
│  │ • Business  │    │   Criteria  │    │   Work      │    │ • Steps to  │  │
│  │   Value     │    │ • Definition│    │ • Activity  │    │   Reproduce │  │
│  │ • Risk      │    │   of Done   │    │ • Discipline│    │ • Found In  │  │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘  │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### **Sprint Management Flow**

```ascii
┌─────────────────────────────────────────────────────────────────────────────┐
│                          Sprint Lifecycle                                   │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐   │
│  │   Sprint    │ →  │   Sprint    │ →  │   Sprint    │ →  │   Sprint    │   │
│  │  Planning   │    │  Execution  │    │   Review    │    │Retrospective│   │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘   │
│         │                   │                   │                   │       │
│         ▼                   ▼                   ▼                   ▼       │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐   │
│  │ • Backlog   │    │ • Daily     │    │ • Demo      │    │ • What went │   │
│  │   Grooming  │    │   Standups  │    │ • Metrics   │    │   well?     │   │
│  │ • Capacity  │    │ • Kanban    │    │ • Feedback  │    │ • What can  │   │
│  │   Planning  │    │   Board     │    │ • Velocity  │    │   improve?  │   │
│  │ • Story     │    │ • Burndown  │    │ • Acceptance│    │ • Action    │   │
│  │   Selection │    │   Chart     │    │ • Closure   │    │   Items     │   │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘   │
│                                                                             │
│                              ┌─────────────┐                                │
│                              │ Continuous  │                                │
│                              │ Activities  │                                │
│                              │ • Backlog   │                                │
│                              │   Refinement│                                │
│                              │ • Story     │                                │
│                              │   Estimation│                                │
│                              │ • Risk Mgmt │                                │
│                              └─────────────┘                                │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### **Kanban Board Flow**

```ascii
┌─────────────────────────────────────────────────────────────────────────────┐
│                            Kanban Board                                     │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Backlog   │  │   To Do     │  │ In Progress │  │   Review    │         │
│  │             │  │             │  │             │  │             │         │
│  │ ┌─────────┐ │  │ ┌─────────┐ │  │ ┌─────────┐ │  │ ┌─────────┐ │         │
│  │ │ US-001  │ │  │ │ US-003  │ │  │ │ US-005  │ │  │ │ US-007  │ │         │
│  │ │ 5 pts   │ │  │ │ 3 pts   │ │  │ │ 8 pts   │ │  │ │ 2 pts   │ │         │
│  │ └─────────┘ │  │ └─────────┘ │  │ └─────────┘ │  │ └─────────┘ │         │
│  │             │  │             │  │             │  │             │         │
│  │ ┌─────────┐ │  │ ┌─────────┐ │  │ ┌─────────┐ │  │ ┌─────────┐ │         │
│  │ │ US-002  │ │  │ │ US-004  │ │  │ │ US-006  │ │  │ │ US-008  │ │         │
│  │ │ 3 pts   │ │  │ │ 5 pts   │ │  │ │ 13 pts  │ │  │ │ 1 pt    │ │         │
│  │ └─────────┘ │  │ └─────────┘ │  │ └─────────┘ │  │ └─────────┘ │         │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘         │
│                                                                             │
│                                    ┌─────────────┐                          │
│                                    │    Done     │                          │
│                                    │             │                          │
│                                    │ ┌─────────┐ │                          │
│                                    │ │ US-009  │ │                          │
│                                    │ │ 5 pts   │ │                          │
│                                    │ └─────────┘ │                          │
│                                    │             │                          │
│                                    │ ┌─────────┐ │                          │
│                                    │ │ US-010  │ │                          │
│                                    │ │ 8 pts   │ │                          │
│                                    │ └─────────┘ │                          │
│                                    └─────────────┘                          │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

---

## 🔗 **Azure DevOps Alignment**

### **Work Item Hierarchy Comparison**

```ascii
┌─────────────────────────────────────────────────────────────────────────────┐
│                    Azure DevOps vs PM.Tool Mapping                         │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  AZURE DEVOPS HIERARCHY:           PM.Tool CURRENT:                        │
│                                                                             │
│  ┌─────────────┐                   ┌─────────────┐                         │
│  │    Epic     │ ←────────────────→ │    Epic     │                         │
│  │ (Portfolio) │                   │ (EP-001)    │                         │
│  └─────────────┘                   └─────────────┘                         │
│         │                                 │                                │
│         ▼                                 ▼                                │
│  ┌─────────────┐                   ┌─────────────┐                         │
│  │   Feature   │ ←────────────────→ │ User Story  │ ⚠️ MAPPING ISSUE       │
│  │ (Capability)│                   │ (US-001)    │                         │
│  └─────────────┘                   └─────────────┘                         │
│         │                                 │                                │
│         ▼                                 ▼                                │
│  ┌─────────────┐                   ┌─────────────┐                         │
│  │ User Story  │ ←────────────────→ │    Task     │ ⚠️ MAPPING ISSUE       │
│  │ (Requirement)│                  │ (Regular)   │                         │
│  └─────────────┘                   └─────────────┘                         │
│         │                                                                  │
│         ▼                                                                  │
│  ┌─────────────┐                   ❌ MISSING                              │
│  │    Task     │ ←────────────────→ │             │                         │
│  │ (Activity)  │                   │             │                         │
│  └─────────────┘                   └─────────────┘                         │
│         │                                                                  │
│         ▼                                                                  │
│  ┌─────────────┐                   ❌ MISSING                              │
│  │    Bug      │ ←────────────────→ │             │                         │
│  │ (Defect)    │                   │             │                         │
│  └─────────────┘                   └─────────────┘                         │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### **Critical Gaps in Agile Implementation**

```ascii
┌─────────────────────────────────────────────────────────────────────────────┐
│                          Agile Module Gaps                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ❌ MISSING WORK ITEM TYPES:                                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   Feature   │  │    Bug      │  │ Test Case   │  │   Issue     │        │
│  │             │  │             │  │             │  │             │        │
│  │ • Business  │  │ • Severity  │  │ • Steps     │  │ • Impediment│        │
│  │   Capability│  │ • Priority  │  │ • Expected  │  │ • Blocker   │        │
│  │ • Epic Link │  │ • Repro     │  │   Result    │  │ • Risk      │        │
│  │ • Value     │  │   Steps     │  │ • Actual    │  │ • Decision  │        │
│  │   Stream    │  │ • Found In  │  │   Result    │  │   Needed    │        │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘        │
│                                                                             │
│  ❌ MISSING AGILE PROCESSES:                                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   Backlog   │  │ Definition  │  │   Sprint    │  │  Velocity   │        │
│  │ Refinement  │  │  of Done    │  │  Burndown   │  │  Tracking   │        │
│  │             │  │             │  │             │  │             │        │
│  │ • Story     │  │ • Acceptance│  │ • Daily     │  │ • Team      │        │
│  │   Pointing  │  │   Criteria  │  │   Progress  │  │   Capacity  │        │
│  │ • Effort    │  │ • Quality   │  │ • Scope     │  │ • Historical│        │
│  │   Estimation│  │   Gates     │  │   Changes   │  │   Data      │        │
│  │ • Priority  │  │ • Review    │  │ • Impediment│  │ • Forecasting│       │
│  │   Ranking   │  │   Process   │  │   Tracking  │  │             │        │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘        │
│                                                                             │
│  ❌ MISSING REPORTING & METRICS:                                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ Cumulative  │  │   Sprint    │  │    Team     │  │  Portfolio  │        │
│  │    Flow     │  │  Burndown   │  │  Velocity   │  │   Metrics   │        │
│  │   Diagram   │  │             │  │             │  │             │        │
│  │             │  │ • Planned   │  │ • Story Pts │  │ • Epic      │        │
│  │ • Work Item │  │   vs Actual │  │   per Sprint│  │   Progress  │        │
│  │   States    │  │ • Scope     │  │ • Cycle Time│  │ • Feature   │        │
│  │ • Cycle     │  │   Changes   │  │ • Lead Time │  │   Delivery  │        │
│  │   Time      │  │ • Capacity  │  │ • Throughput│  │ • Value     │        │
│  │ • Lead Time │  │   Utilization│ │             │  │   Delivery  │        │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘        │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

---

## 🔄 **Integration Points**

### **Cross-Module Data Flow**

```ascii
┌─────────────────────────────────────────────────────────────────────────────┐
│                        System Integration Flow                              │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐                     │
│  │   Project   │ ←→ │    Agile    │ ←→ │  Resource   │                     │
│  │ Management  │    │   Module    │    │ Management  │                     │
│  └─────────────┘    └─────────────┘    └─────────────┘                     │
│         │                   │                   │                          │
│         ▼                   ▼                   ▼                          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐                     │
│  │ • Projects  │    │ • Epics     │    │ • Resources │                     │
│  │ • Tasks     │    │ • Stories   │    │ • Skills    │                     │
│  │ • Milestones│    │ • Sprints   │    │ • Capacity  │                     │
│  │ • Timeline  │    │ • Backlog   │    │ • Allocation│                     │
│  └─────────────┘    └─────────────┘    └─────────────┘                     │
│                                                                             │
│                              ┌─────────────┐                                │
│                              │  Document   │                                │
│                              │ Management  │                                │
│                              └─────────────┘                                │
│                                     │                                       │
│                                     ▼                                       │
│                              ┌─────────────┐                                │
│                              │ • Files     │                                │
│                              │ • Versions  │                                │
│                              │ • Categories│                                │
│                              │ • Metadata  │                                │
│                              └─────────────┘                                │
│                                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐                     │
│  │    Risk     │ ←→ │Requirements │ ←→ │   Meeting   │                     │
│  │ Management  │    │ Management  │    │ Management  │                     │
│  └─────────────┘    └─────────────┘    └─────────────┘                     │
│         │                   │                   │                          │
│         ▼                   ▼                   ▼                          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐                     │
│  │ • Risks     │    │ • Functional│    │ • Meetings  │                     │
│  │ • Issues    │    │ • Business  │    │ • Agendas   │                     │
│  │ • Mitigation│    │ • Technical │    │ • Minutes   │                     │
│  │ • Tracking  │    │ • Traceability│  │ • Actions   │                     │
│  └─────────────┘    └─────────────┘    └─────────────┘                     │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### **User Journey Map**

```ascii
┌─────────────────────────────────────────────────────────────────────────────┐
│                           User Journey Map                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  PROJECT MANAGER JOURNEY:                                                  │
│  ┌─────────────┐ → ┌─────────────┐ → ┌─────────────┐ → ┌─────────────┐     │
│  │   Login &   │   │   Project   │   │    Team     │   │ Methodology │     │
│  │  Dashboard  │   │   Setup     │   │  Assembly   │   │  Selection  │     │
│  └─────────────┘   └─────────────┘   └─────────────┘   └─────────────┘     │
│         │                   │                   │                   │       │
│         ▼                   ▼                   ▼                   ▼       │
│  ┌─────────────┐ → ┌─────────────┐ → ┌─────────────┐ → ┌─────────────┐     │
│  │ Requirements│   │   Planning  │   │  Execution  │   │ Monitoring  │     │
│  │  Gathering  │   │ & Scheduling│   │ & Tracking  │   │ & Control   │     │
│  └─────────────┘   └─────────────┘   └─────────────┘   └─────────────┘     │
│                                                                             │
│  SCRUM MASTER JOURNEY:                                                     │
│  ┌─────────────┐ → ┌─────────────┐ → ┌─────────────┐ → ┌─────────────┐     │
│  │   Backlog   │   │   Sprint    │   │   Daily     │   │   Sprint    │     │
│  │ Refinement  │   │  Planning   │   │  Standups   │   │   Review    │     │
│  └─────────────┘   └─────────────┘   └─────────────┘   └─────────────┘     │
│         │                   │                   │                   │       │
│         ▼                   ▼                   ▼                   ▼       │
│  ┌─────────────┐ → ┌─────────────┐ → ┌─────────────┐ → ┌─────────────┐     │
│  │ Story Point │   │ Capacity    │   │ Impediment  │   │Retrospective│     │
│  │ Estimation  │   │  Planning   │   │  Removal    │   │ & Metrics   │     │
│  └─────────────┘   └─────────────┘   └─────────────┘   └─────────────┘     │
│                                                                             │
│  TEAM MEMBER JOURNEY:                                                      │
│  ┌─────────────┐ → ┌─────────────┐ → ┌─────────────┐ → ┌─────────────┐     │
│  │   Task      │   │    Work     │   │   Progress  │   │ Collaboration│     │
│  │ Assignment  │   │  Execution  │   │   Updates   │   │ & Feedback  │     │
│  └─────────────┘   └─────────────┘   └─────────────┘   └─────────────┘     │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

