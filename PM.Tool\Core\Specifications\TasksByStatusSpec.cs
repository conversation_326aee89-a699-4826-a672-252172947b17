using PM.Tool.Core.Entities;
using TaskStatus = PM.Tool.Core.Enums.TaskStatus;

namespace PM.Tool.Core.Specifications
{
    public class TasksByStatusSpec : BaseSpecification<TaskEntity>
    {
        public TasksByStatusSpec(TaskStatus status)
        {
            Criteria = t => t.Status == status;
            AddInclude(t => t.Project);
            AddInclude(t => t.AssignedTo);
        }
    }
}
