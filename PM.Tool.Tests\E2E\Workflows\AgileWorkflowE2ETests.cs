using FluentAssertions;
using PM.Tool.Tests.E2E.Infrastructure;
using System.Net;
using Xunit;

namespace PM.Tool.Tests.E2E.Workflows
{
    [Collection("E2E")]
    public class AgileWorkflowE2ETests : E2ETestBase
    {
        public AgileWorkflowE2ETests(E2EWebApplicationFactory factory) : base(factory)
        {
        }

        [Fact]
        public async Task CompleteAgileWorkflow_FromEpicToSprintCompletion_WorksEndToEnd()
        {
            // This test validates the complete Agile/Scrum workflow:
            // 1. User views Agile dashboard
            // 2. User creates an Epic
            // 3. User creates User Stories for the Epic
            // 4. User creates a Sprint
            // 5. User adds User Stories to Sprint
            // 6. User views Kanban board
            // 7. User moves cards through workflow
            // 8. User views Sprint analytics

            // Step 1: Navigate to Agile Dashboard
            var agileResponse = await _client.GetAsync("/Agile");
            AssertSuccessResponse(agileResponse);
            
            var agileContent = await agileResponse.Content.ReadAsStringAsync();
            AssertPageContains(agileContent, "Agile", "Dashboard");

            // Step 2: Test Epic Management (requires project context)
            // First, let's test if we can access epic creation for project 1
            var epicCreateResponse = await _client.GetAsync("/Agile/CreateEpic/1");
            
            if (epicCreateResponse.StatusCode == HttpStatusCode.OK)
            {
                var (epicPageContent, epicToken) = await GetPageWithTokenAsync("/Agile/CreateEpic/1");
                AssertPageContains(epicPageContent, "Create Epic", "Title", "Description");

                var epicData = new Dictionary<string, string>
                {
                    ["Title"] = "User Management Epic",
                    ["Description"] = "Complete user management functionality",
                    ["ProjectId"] = "1",
                    ["Priority"] = "3", // High
                    ["Status"] = "1" // Draft
                };

                var createEpicResponse = await PostFormAsync("/Agile/CreateEpic", epicData, epicToken);
                createEpicResponse.StatusCode.Should().BeOneOf(HttpStatusCode.Redirect, HttpStatusCode.Found);
            }
            else
            {
                // Expected if project doesn't exist
                epicCreateResponse.StatusCode.Should().BeOneOf(HttpStatusCode.NotFound, HttpStatusCode.Redirect);
            }

            // Step 3: Test User Story Creation
            var userStoryCreateResponse = await _client.GetAsync("/Agile/CreateUserStory/1");
            
            if (userStoryCreateResponse.StatusCode == HttpStatusCode.OK)
            {
                var (storyPageContent, storyToken) = await GetPageWithTokenAsync("/Agile/CreateUserStory/1");
                AssertPageContains(storyPageContent, "Create User Story", "Title", "As a", "I want", "So that");

                var storyData = new Dictionary<string, string>
                {
                    ["Title"] = "User Login Story",
                    ["AsA"] = "registered user",
                    ["IWant"] = "to log into the system",
                    ["SoThat"] = "I can access my account",
                    ["ProjectId"] = "1",
                    ["StoryPoints"] = "5",
                    ["Priority"] = "3" // High
                };

                var createStoryResponse = await PostFormAsync("/Agile/CreateUserStory", storyData, storyToken);
                createStoryResponse.StatusCode.Should().BeOneOf(HttpStatusCode.Redirect, HttpStatusCode.Found);
            }
            else
            {
                // Expected if project doesn't exist
                userStoryCreateResponse.StatusCode.Should().BeOneOf(HttpStatusCode.NotFound, HttpStatusCode.Redirect);
            }

            // Step 4: Test Sprint Creation
            var sprintCreateResponse = await _client.GetAsync("/Agile/CreateSprint/1");
            
            if (sprintCreateResponse.StatusCode == HttpStatusCode.OK)
            {
                var (sprintPageContent, sprintToken) = await GetPageWithTokenAsync("/Agile/CreateSprint/1");
                AssertPageContains(sprintPageContent, "Create Sprint", "Sprint Name", "Start Date", "End Date");

                var sprintData = new Dictionary<string, string>
                {
                    ["Name"] = "Sprint 1 - User Management",
                    ["Description"] = "First sprint focusing on user management features",
                    ["ProjectId"] = "1",
                    ["StartDate"] = DateTime.Today.ToString("yyyy-MM-dd"),
                    ["EndDate"] = DateTime.Today.AddDays(14).ToString("yyyy-MM-dd"),
                    ["Goal"] = "Implement basic user authentication"
                };

                var createSprintResponse = await PostFormAsync("/Agile/CreateSprint", sprintData, sprintToken);
                createSprintResponse.StatusCode.Should().BeOneOf(HttpStatusCode.Redirect, HttpStatusCode.Found);
            }
            else
            {
                // Expected if project doesn't exist
                sprintCreateResponse.StatusCode.Should().BeOneOf(HttpStatusCode.NotFound, HttpStatusCode.Redirect);
            }
        }

        [Fact]
        public async Task KanbanBoardWorkflow_ViewAndInteractWithBoard_WorksEndToEnd()
        {
            // This test validates the Kanban board functionality

            // Step 1: Access Kanban Board
            var kanbanResponse = await _client.GetAsync("/Agile/Kanban/1");
            
            if (kanbanResponse.StatusCode == HttpStatusCode.OK)
            {
                var kanbanContent = await kanbanResponse.Content.ReadAsStringAsync();
                AssertPageContains(kanbanContent, "Kanban", "Board");
                
                // Verify typical Kanban columns are present
                AssertPageContains(kanbanContent, "To Do", "In Progress", "Done");
            }
            else
            {
                // Expected if project doesn't exist
                kanbanResponse.StatusCode.Should().BeOneOf(HttpStatusCode.NotFound, HttpStatusCode.Redirect);
            }

            // Step 2: Test Kanban API endpoints (these would be AJAX calls in real usage)
            var kanbanDataResponse = await _client.GetAsync("/Agile/GetKanbanData/1");
            
            if (kanbanDataResponse.StatusCode == HttpStatusCode.OK)
            {
                kanbanDataResponse.Content.Headers.ContentType?.MediaType.Should().Be("application/json");
            }
            else
            {
                // Expected if project doesn't exist
                kanbanDataResponse.StatusCode.Should().BeOneOf(HttpStatusCode.NotFound, HttpStatusCode.BadRequest);
            }
        }

        [Fact]
        public async Task BacklogManagement_ViewAndManageBacklog_WorksEndToEnd()
        {
            // This test validates the product backlog functionality

            // Step 1: Access Product Backlog
            var backlogResponse = await _client.GetAsync("/Agile/Backlog/1");
            
            if (backlogResponse.StatusCode == HttpStatusCode.OK)
            {
                var backlogContent = await backlogResponse.Content.ReadAsStringAsync();
                AssertPageContains(backlogContent, "Backlog", "User Stories");
                
                // Verify backlog management features
                AssertPageContains(backlogContent, "Priority", "Story Points");
            }
            else
            {
                // Expected if project doesn't exist
                backlogResponse.StatusCode.Should().BeOneOf(HttpStatusCode.NotFound, HttpStatusCode.Redirect);
            }
        }

        [Fact]
        public async Task EpicManagement_ViewAndManageEpics_WorksEndToEnd()
        {
            // This test validates epic management functionality

            // Step 1: Access Epics List
            var epicsResponse = await _client.GetAsync("/Agile/Epics/1");
            
            if (epicsResponse.StatusCode == HttpStatusCode.OK)
            {
                var epicsContent = await epicsResponse.Content.ReadAsStringAsync();
                AssertPageContains(epicsContent, "Epics");
                
                // Verify epic management features
                AssertPageContains(epicsContent, "Create Epic");
            }
            else
            {
                // Expected if project doesn't exist
                epicsResponse.StatusCode.Should().BeOneOf(HttpStatusCode.NotFound, HttpStatusCode.Redirect);
            }

            // Step 2: Test Epic Details (if epic exists)
            var epicDetailsResponse = await _client.GetAsync("/Agile/EpicDetails/1");
            
            if (epicDetailsResponse.StatusCode == HttpStatusCode.OK)
            {
                var epicDetailsContent = await epicDetailsResponse.Content.ReadAsStringAsync();
                AssertPageContains(epicDetailsContent, "Epic Details");
            }
            else
            {
                // Expected if epic doesn't exist
                epicDetailsResponse.StatusCode.Should().BeOneOf(HttpStatusCode.NotFound, HttpStatusCode.Redirect);
            }
        }

        [Fact]
        public async Task SprintManagement_ViewAndManageSprints_WorksEndToEnd()
        {
            // This test validates sprint management functionality

            // Step 1: Access Sprints List
            var sprintsResponse = await _client.GetAsync("/Agile/Sprints/1");
            
            if (sprintsResponse.StatusCode == HttpStatusCode.OK)
            {
                var sprintsContent = await sprintsResponse.Content.ReadAsStringAsync();
                AssertPageContains(sprintsContent, "Sprints");
                
                // Verify sprint management features
                AssertPageContains(sprintsContent, "Create Sprint");
            }
            else
            {
                // Expected if project doesn't exist
                sprintsResponse.StatusCode.Should().BeOneOf(HttpStatusCode.NotFound, HttpStatusCode.Redirect);
            }

            // Step 2: Test Sprint Details (if sprint exists)
            var sprintDetailsResponse = await _client.GetAsync("/Agile/SprintDetails/1");
            
            if (sprintDetailsResponse.StatusCode == HttpStatusCode.OK)
            {
                var sprintDetailsContent = await sprintDetailsResponse.Content.ReadAsStringAsync();
                AssertPageContains(sprintDetailsContent, "Sprint Details");
                
                // Verify sprint details features
                AssertPageContains(sprintDetailsContent, "Burndown", "Velocity");
            }
            else
            {
                // Expected if sprint doesn't exist
                sprintDetailsResponse.StatusCode.Should().BeOneOf(HttpStatusCode.NotFound, HttpStatusCode.Redirect);
            }
        }

        [Fact]
        public async Task AgileAnalytics_ViewProjectAnalytics_WorksEndToEnd()
        {
            // This test validates Agile analytics functionality

            // Step 1: Access Analytics Dashboard
            var analyticsResponse = await _client.GetAsync("/Agile/Analytics/1");
            
            if (analyticsResponse.StatusCode == HttpStatusCode.OK)
            {
                var analyticsContent = await analyticsResponse.Content.ReadAsStringAsync();
                AssertPageContains(analyticsContent, "Analytics");
                
                // Verify analytics features
                AssertPageContains(analyticsContent, "Velocity", "Burndown", "Epic Progress");
            }
            else
            {
                // Expected if project doesn't exist
                analyticsResponse.StatusCode.Should().BeOneOf(HttpStatusCode.NotFound, HttpStatusCode.Redirect);
            }
        }

        [Fact]
        public async Task UserStoryManagement_CreateAndViewUserStories_WorksEndToEnd()
        {
            // This test validates user story management

            // Step 1: Test User Story Details (if user story exists)
            var storyDetailsResponse = await _client.GetAsync("/Agile/UserStoryDetails/1");
            
            if (storyDetailsResponse.StatusCode == HttpStatusCode.OK)
            {
                var storyDetailsContent = await storyDetailsResponse.Content.ReadAsStringAsync();
                AssertPageContains(storyDetailsContent, "User Story Details");
                
                // Verify user story details features
                AssertPageContains(storyDetailsContent, "Story Points", "Acceptance Criteria");
            }
            else
            {
                // Expected if user story doesn't exist
                storyDetailsResponse.StatusCode.Should().BeOneOf(HttpStatusCode.NotFound, HttpStatusCode.Redirect);
            }
        }

        [Fact]
        public async Task AgileAPIEndpoints_TestAjaxEndpoints_WorkCorrectly()
        {
            // This test validates AJAX endpoints used by the Agile module

            var apiEndpoints = new[]
            {
                "/Agile/GetKanbanData/1",
                "/Agile/GetSprintMetrics/1",
                "/Agile/GetVelocityData/1"
            };

            foreach (var endpoint in apiEndpoints)
            {
                var response = await _client.GetAsync(endpoint);
                
                if (response.StatusCode == HttpStatusCode.OK)
                {
                    // Should return JSON
                    response.Content.Headers.ContentType?.MediaType.Should().Be("application/json");
                }
                else
                {
                    // Expected if project/data doesn't exist
                    response.StatusCode.Should().BeOneOf(
                        HttpStatusCode.NotFound, 
                        HttpStatusCode.BadRequest,
                        HttpStatusCode.NoContent);
                }
            }
        }

        [Fact]
        public async Task AgileNavigation_AllAgileRoutesAccessible_WorksEndToEnd()
        {
            // This test validates that all Agile routes are properly configured

            var agileRoutes = new Dictionary<string, string[]>
            {
                ["/Agile"] = new[] { "Agile", "Dashboard" },
                ["/Agile/Backlog/1"] = new[] { "Backlog" },
                ["/Agile/Kanban/1"] = new[] { "Kanban" },
                ["/Agile/Epics/1"] = new[] { "Epics" },
                ["/Agile/Sprints/1"] = new[] { "Sprints" },
                ["/Agile/Analytics/1"] = new[] { "Analytics" }
            };

            foreach (var route in agileRoutes)
            {
                var response = await _client.GetAsync(route.Key);
                
                if (response.StatusCode == HttpStatusCode.OK)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    AssertPageContains(content, route.Value);
                }
                else
                {
                    // Expected for routes that require existing data
                    response.StatusCode.Should().BeOneOf(
                        HttpStatusCode.NotFound, 
                        HttpStatusCode.Redirect,
                        HttpStatusCode.BadRequest);
                }
            }
        }
    }
}
