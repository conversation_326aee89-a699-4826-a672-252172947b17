using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;

namespace PM.Tool.Core.Interfaces
{
    public interface IMeetingService
    {
        // Meeting Management
        Task<IEnumerable<Meeting>> GetAllMeetingsAsync();
        Task<IEnumerable<Meeting>> GetProjectMeetingsAsync(int projectId);
        Task<IEnumerable<Meeting>> GetUserMeetingsAsync(string userId, DateTime? startDate = null, DateTime? endDate = null);
        Task<Meeting?> GetMeetingByIdAsync(int id);
        Task<Meeting> CreateMeetingAsync(Meeting meeting);
        Task<bool> UpdateMeetingAsync(Meeting meeting);
        Task<bool> DeleteMeetingAsync(int id);
        Task<bool> CancelMeetingAsync(int id, string reason);

        // Meeting Scheduling
        Task<IEnumerable<Meeting>> GetUpcomingMeetingsAsync(string userId, int days = 7);
        Task<IEnumerable<Meeting>> GetMeetingsByDateRangeAsync(DateTime startDate, DateTime endDate, int? projectId = null);
        Task<bool> CheckMeetingConflictAsync(DateTime scheduledDate, int durationMinutes, IEnumerable<string> attendeeIds, int? excludeMeetingId = null);
        Task<IEnumerable<DateTime>> SuggestMeetingTimesAsync(IEnumerable<string> attendeeIds, int durationMinutes, DateTime preferredDate, int alternatives = 3);

        // Attendee Management
        Task<IEnumerable<MeetingAttendee>> GetMeetingAttendeesAsync(int meetingId);
        Task<bool> AddAttendeeAsync(int meetingId, string userId, AttendeeRole role = AttendeeRole.Attendee, bool isRequired = true);
        Task<bool> RemoveAttendeeAsync(int meetingId, string userId);
        Task<bool> UpdateAttendeeStatusAsync(int meetingId, string userId, AttendanceStatus status);
        Task<bool> RespondToMeetingInviteAsync(int meetingId, string userId, AttendanceStatus response);

        // Meeting Execution
        Task<bool> StartMeetingAsync(int meetingId);
        Task<bool> EndMeetingAsync(int meetingId);
        Task<bool> UpdateMeetingMinutesAsync(int meetingId, string minutes);
        Task<bool> MarkAttendanceAsync(int meetingId, string userId, bool attended);

        // Action Items
        Task<IEnumerable<MeetingActionItem>> GetMeetingActionItemsAsync(int meetingId);
        Task<IEnumerable<MeetingActionItem>> GetUserActionItemsAsync(string userId, bool includeCompleted = false);
        Task<MeetingActionItem> CreateActionItemAsync(MeetingActionItem actionItem);
        Task<MeetingActionItem> UpdateActionItemAsync(MeetingActionItem actionItem);
        Task<bool> CompleteActionItemAsync(int actionItemId);
        Task<bool> DeleteActionItemAsync(int actionItemId);

        // Meeting Analytics
        Task<Dictionary<string, object>> GetMeetingAnalyticsAsync(int projectId, DateTime startDate, DateTime endDate);
        Task<IEnumerable<object>> GetMeetingAttendanceReportAsync(int projectId, DateTime startDate, DateTime endDate);
        Task<decimal> GetAverageMeetingDurationAsync(int projectId, MeetingType? type = null);
        Task<int> GetActionItemCompletionRateAsync(int projectId);

        // Recurring Meetings
        Task<IEnumerable<Meeting>> CreateRecurringMeetingsAsync(Meeting templateMeeting, string recurrencePattern, DateTime endDate);
        Task<bool> UpdateRecurringMeetingSeriesAsync(int meetingId, Meeting updatedMeeting, bool updateFutureOnly = true);
        Task<bool> CancelRecurringMeetingSeriesAsync(int meetingId, bool cancelFutureOnly = true);

        // Meeting Documents
        Task<IEnumerable<MeetingDocument>> GetMeetingDocumentsAsync(int meetingId);
        Task<MeetingDocument> AttachDocumentToMeetingAsync(int meetingId, int documentId, DocumentType type);
        Task<bool> RemoveDocumentFromMeetingAsync(int meetingId, int documentId);

        // Notifications and Reminders
        Task<bool> SendMeetingInvitesAsync(int meetingId);
        Task<bool> SendMeetingRemindersAsync(int meetingId, int minutesBefore = 15);
        Task<bool> SendMeetingCancellationAsync(int meetingId, string reason);
        Task<bool> SendActionItemRemindersAsync(string userId);
    }
}
