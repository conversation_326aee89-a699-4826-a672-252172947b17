# PowerShell script to generate missing resource files for all 26 supported languages
# This script creates the basic structure for all missing .resx files

$languages = @(
    @{Code="fr-FR"; Name="French"},
    @{Code="de-DE"; Name="German"},
    @{Code="it-IT"; Name="Italian"},
    @{Code="pt-BR"; Name="Portuguese (Brazil)"},
    @{Code="pt-PT"; Name="Portuguese (Portugal)"},
    @{Code="nl-NL"; Name="Dutch"},
    @{Code="sv-SE"; Name="Swedish"},
    @{Code="da-DK"; Name="Danish"},
    @{Code="no-NO"; Name="Norwegian"},
    @{Code="fi-FI"; Name="Finnish"},
    @{Code="ru-RU"; Name="Russian"},
    @{Code="pl-PL"; Name="Polish"},
    @{Code="tr-TR"; Name="Turkish"},
    @{Code="zh-CN"; Name="Chinese (Simplified)"},
    @{Code="zh-TW"; Name="Chinese (Traditional)"},
    @{Code="ja-JP"; Name="Japanese"},
    @{Code="ko-KR"; Name="Korean"},
    @{Code="hi-IN"; Name="Hindi"},
    @{Code="th-TH"; Name="Thai"},
    @{Code="vi-VN"; Name="Vietnamese"},
    @{Code="ar-SA"; Name="Arabic"},
    @{Code="he-IL"; Name="Hebrew"},
    @{Code="en-GB"; Name="English (UK)"},
    @{Code="es-MX"; Name="Spanish (Mexico)"}
)

$resourcesPath = "PM.Tool/Resources"
$templateFile = "$resourcesPath/SharedResources.resx"

Write-Host "Generating resource files for 24 missing languages..." -ForegroundColor Green

foreach ($lang in $languages) {
    $targetFile = "$resourcesPath/SharedResources.$($lang.Code).resx"
    
    if (Test-Path $targetFile) {
        Write-Host "File already exists: $targetFile" -ForegroundColor Yellow
        continue
    }
    
    Write-Host "Creating: $targetFile for $($lang.Name)" -ForegroundColor Cyan
    
    # Copy the template and modify for the target language
    $content = Get-Content $templateFile -Raw
    
    # Replace the content with placeholder translations
    $content = $content -replace '<value>([^<]+)</value>', '<value>[$1 - $($lang.Name)]</value>'
    
    # Add language-specific comment
    $content = $content -replace '<!-- Common UI Elements -->', "<!-- Common UI Elements - $($lang.Name) -->"
    
    # Save the file
    $content | Out-File -FilePath $targetFile -Encoding UTF8
}

Write-Host "`nResource file generation complete!" -ForegroundColor Green
Write-Host "Note: All files contain placeholder translations that need to be replaced with actual translations." -ForegroundColor Yellow

# Generate a summary report
$existingFiles = Get-ChildItem "$resourcesPath/SharedResources.*.resx" | Measure-Object
$totalExpected = 26 # 24 new + 2 existing (en, es)

Write-Host "`nSummary:" -ForegroundColor Magenta
Write-Host "- Expected total files: $totalExpected" -ForegroundColor White
Write-Host "- Existing files: $($existingFiles.Count)" -ForegroundColor White
Write-Host "- Missing files: $($totalExpected - $existingFiles.Count)" -ForegroundColor White

if ($existingFiles.Count -eq $totalExpected) {
    Write-Host "✅ All resource files are now present!" -ForegroundColor Green
} else {
    Write-Host "⚠️  Some resource files are still missing." -ForegroundColor Red
}
