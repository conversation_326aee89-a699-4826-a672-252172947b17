using System.ComponentModel.DataAnnotations;

using PM.Tool.Core.Entities;

namespace PM.Tool.Models.ViewModels
{
    public class StakeholderCreateViewModel : BaseCreateViewModel, IEntityViewModel<Stakeholder>
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Role")]
        public StakeholderRole Role { get; set; } = StakeholderRole.User;

        [Required]
        [EmailAddress]
        [MaxLength(100)]
        public string Email { get; set; } = string.Empty;

        [MaxLength(20)]
        public string? Phone { get; set; }

        [MaxLength(100)]
        public string? Department { get; set; }

        [MaxLength(100)]
        public string? Organization { get; set; }

        [Display(Name = "Title")]
        [MaxLength(100)]
        public string? Title { get; set; }

        [Display(Name = "Communication Preferences")]
        [MaxLength(500)]
        public string? CommunicationPreferences { get; set; }

        [Display(Name = "User ID")]
        public string? UserId { get; set; }

        public StakeholderType Type { get; set; } = StakeholderType.Internal;

        public InfluenceLevel Influence { get; set; } = InfluenceLevel.Medium;

        public InterestLevel Interest { get; set; } = InterestLevel.Medium;

        [MaxLength(500)]
        public string? Notes { get; set; }

        public bool IsActive { get; set; } = true;

        public Stakeholder ToEntity()
        {
            return new Stakeholder
            {
                Name = Name,
                Title = Title,
                Role = Role,
                Email = Email,
                Phone = Phone,
                Department = Department,
                Organization = Organization,
                Type = Type,
                Influence = Influence,
                Interest = Interest,
                Notes = Notes,
                CommunicationPreferences = CommunicationPreferences,
                UserId = UserId,
                IsActive = IsActive
            };
        }

        public void UpdateEntity(Stakeholder stakeholder)
        {
            stakeholder.Name = Name;
            stakeholder.Title = Title;
            stakeholder.Role = Role;
            stakeholder.Email = Email;
            stakeholder.Phone = Phone;
            stakeholder.Department = Department;
            stakeholder.Organization = Organization;
            stakeholder.Type = Type;
            stakeholder.Influence = Influence;
            stakeholder.Interest = Interest;
            stakeholder.Notes = Notes;
            stakeholder.CommunicationPreferences = CommunicationPreferences;
            stakeholder.UserId = UserId;
            stakeholder.IsActive = IsActive;
        }
    }

    public class StakeholderEditViewModel : StakeholderCreateViewModel, IFromEntity<Stakeholder, StakeholderEditViewModel>
    {
        public int Id { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        public static StakeholderEditViewModel FromEntity(Stakeholder stakeholder)
        {
            return new StakeholderEditViewModel
            {
                Id = stakeholder.Id,
                Name = stakeholder.Name,
                Title = stakeholder.Title,
                Role = stakeholder.Role,
                Email = stakeholder.Email,
                Phone = stakeholder.Phone,
                Department = stakeholder.Department,
                Organization = stakeholder.Organization,
                Type = stakeholder.Type,
                Influence = stakeholder.Influence,
                Interest = stakeholder.Interest,
                Notes = stakeholder.Notes,
                CommunicationPreferences = stakeholder.CommunicationPreferences,
                UserId = stakeholder.UserId,
                IsActive = stakeholder.IsActive,
                CreatedAt = stakeholder.CreatedAt,
                UpdatedAt = stakeholder.UpdatedAt
            };
        }

        // For backward compatibility
        public static StakeholderEditViewModel FromStakeholder(Stakeholder stakeholder) => FromEntity(stakeholder);
    }
}
