using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Enums;
using PM.Tool.Data;

namespace PM.Tool.Application.Services
{
    public class MeetingService : IMeetingService
    {
        private readonly ApplicationDbContext _context;

        public MeetingService(ApplicationDbContext context)
        {
            _context = context;
        }

        // Meeting Management
        public async Task<IEnumerable<Meeting>> GetAllMeetingsAsync()
        {
            return await _context.Meetings
                .Include(m => m.Project)
                .Include(m => m.Organizer)
                .OrderByDescending(m => m.ScheduledDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Meeting>> GetProjectMeetingsAsync(int projectId)
        {
            return await _context.Meetings
                .Include(m => m.Project)
                .Include(m => m.Organizer)
                .Where(m => m.ProjectId == projectId)
                .OrderByDescending(m => m.ScheduledDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Meeting>> GetUserMeetingsAsync(string userId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = _context.Meetings
                .Include(m => m.Project)
                .Include(m => m.Organizer)
                .Where(m => m.OrganizerUserId == userId ||
                           m.Attendees.Any(a => a.UserId == userId));

            if (startDate.HasValue)
                query = query.Where(m => m.ScheduledDate >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(m => m.ScheduledDate <= endDate.Value);

            return await query.OrderByDescending(m => m.ScheduledDate).ToListAsync();
        }

        public async Task<Meeting?> GetMeetingByIdAsync(int id)
        {
            return await _context.Meetings
                .Include(m => m.Project)
                .Include(m => m.Organizer)
                .Include(m => m.Attendees)
                .FirstOrDefaultAsync(m => m.Id == id);
        }

        public async Task<Meeting> CreateMeetingAsync(Meeting meeting)
        {
            meeting.CreatedAt = DateTime.UtcNow;
            meeting.UpdatedAt = DateTime.UtcNow;

            _context.Meetings.Add(meeting);
            await _context.SaveChangesAsync();
            return meeting;
        }

        public async Task<bool> UpdateMeetingAsync(Meeting meeting)
        {
            meeting.UpdatedAt = DateTime.UtcNow;

            _context.Meetings.Update(meeting);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> DeleteMeetingAsync(int id)
        {
            var meeting = await _context.Meetings.FindAsync(id);
            if (meeting == null) return false;

            _context.Meetings.Remove(meeting);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> CancelMeetingAsync(int id, string reason)
        {
            var meeting = await _context.Meetings.FindAsync(id);
            if (meeting == null) return false;

            meeting.Status = MeetingStatus.Cancelled;
            meeting.CancellationReason = reason;
            meeting.UpdatedAt = DateTime.UtcNow;

            return await _context.SaveChangesAsync() > 0;
        }



        // Meeting Scheduling
        public async Task<IEnumerable<Meeting>> GetUpcomingMeetingsAsync(string userId, int days = 7)
        {
            var endDate = DateTime.UtcNow.AddDays(days);

            return await _context.Meetings
                .Include(m => m.Project)
                .Where(m => (m.OrganizerUserId == userId || m.Attendees.Any(a => a.UserId == userId)) &&
                           m.ScheduledDate >= DateTime.UtcNow &&
                           m.ScheduledDate <= endDate &&
                           m.Status != MeetingStatus.Cancelled)
                .OrderBy(m => m.ScheduledDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Meeting>> GetMeetingsByDateRangeAsync(DateTime startDate, DateTime endDate, int? projectId = null)
        {
            var query = _context.Meetings
                .Include(m => m.Project)
                .Where(m => m.ScheduledDate >= startDate && m.ScheduledDate <= endDate);

            if (projectId.HasValue)
                query = query.Where(m => m.ProjectId == projectId.Value);

            return await query.OrderBy(m => m.ScheduledDate).ToListAsync();
        }

        public async Task<bool> CheckMeetingConflictAsync(DateTime scheduledDate, int durationMinutes, IEnumerable<string> attendeeIds, int? excludeMeetingId = null)
        {
            var endTime = scheduledDate.AddMinutes(durationMinutes);

            var conflictingMeetings = await _context.Meetings
                .Where(m => m.Id != excludeMeetingId &&
                           m.Status != MeetingStatus.Cancelled &&
                           m.Attendees.Any(a => attendeeIds.Contains(a.UserId)) &&
                           ((m.ScheduledDate <= scheduledDate && m.ScheduledDate.AddMinutes(m.DurationMinutes) > scheduledDate) ||
                            (m.ScheduledDate < endTime && m.ScheduledDate >= scheduledDate)))
                .AnyAsync();

            return conflictingMeetings;
        }

        public async Task<IEnumerable<DateTime>> SuggestMeetingTimesAsync(IEnumerable<string> attendeeIds, int durationMinutes, DateTime preferredDate, int alternatives = 3)
        {
            var suggestions = new List<DateTime>();
            var currentDate = preferredDate.Date;

            // Simple implementation - suggest times during business hours
            for (int day = 0; day < 7 && suggestions.Count < alternatives; day++)
            {
                var checkDate = currentDate.AddDays(day);

                for (int hour = 9; hour <= 17 && suggestions.Count < alternatives; hour++)
                {
                    var suggestedTime = checkDate.AddHours(hour);

                    if (!await CheckMeetingConflictAsync(suggestedTime, durationMinutes, attendeeIds))
                    {
                        suggestions.Add(suggestedTime);
                    }
                }
            }

            return suggestions;
        }

        // Attendee Management
        public async Task<IEnumerable<MeetingAttendee>> GetMeetingAttendeesAsync(int meetingId)
        {
            return await _context.MeetingAttendees
                .Include(a => a.User)
                .Where(a => a.MeetingId == meetingId)
                .ToListAsync();
        }

        public async Task<bool> AddAttendeeAsync(int meetingId, string userId, AttendeeRole role = AttendeeRole.Attendee, bool isRequired = true)
        {
            var attendee = new MeetingAttendee
            {
                MeetingId = meetingId,
                UserId = userId,
                Role = role,
                IsRequired = isRequired,
                Status = AttendanceStatus.Invited,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.MeetingAttendees.Add(attendee);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> RemoveAttendeeAsync(int meetingId, string userId)
        {
            var attendee = await _context.MeetingAttendees
                .FirstOrDefaultAsync(a => a.MeetingId == meetingId && a.UserId == userId);

            if (attendee == null) return false;

            _context.MeetingAttendees.Remove(attendee);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> UpdateAttendeeStatusAsync(int meetingId, string userId, AttendanceStatus status)
        {
            var attendee = await _context.MeetingAttendees
                .FirstOrDefaultAsync(a => a.MeetingId == meetingId && a.UserId == userId);

            if (attendee == null) return false;

            attendee.Status = status;
            attendee.ResponseDate = DateTime.UtcNow;
            attendee.UpdatedAt = DateTime.UtcNow;

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> RespondToMeetingInviteAsync(int meetingId, string userId, AttendanceStatus response)
        {
            return await UpdateAttendeeStatusAsync(meetingId, userId, response);
        }

        // Meeting Execution
        public async Task<bool> StartMeetingAsync(int meetingId)
        {
            var meeting = await _context.Meetings.FindAsync(meetingId);
            if (meeting == null) return false;

            meeting.Status = MeetingStatus.InProgress;
            meeting.ActualStartTime = DateTime.UtcNow;
            meeting.UpdatedAt = DateTime.UtcNow;

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> EndMeetingAsync(int meetingId)
        {
            var meeting = await _context.Meetings.FindAsync(meetingId);
            if (meeting == null) return false;

            meeting.Status = MeetingStatus.Completed;
            meeting.ActualEndTime = DateTime.UtcNow;
            meeting.UpdatedAt = DateTime.UtcNow;

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> UpdateMeetingMinutesAsync(int meetingId, string minutes)
        {
            var meeting = await _context.Meetings.FindAsync(meetingId);
            if (meeting == null) return false;

            meeting.Minutes = minutes;
            meeting.UpdatedAt = DateTime.UtcNow;

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> MarkAttendanceAsync(int meetingId, string userId, bool attended)
        {
            var status = attended ? AttendanceStatus.Attended : AttendanceStatus.NoShow;
            return await UpdateAttendeeStatusAsync(meetingId, userId, status);
        }

        // Action Items
        public async Task<IEnumerable<MeetingActionItem>> GetMeetingActionItemsAsync(int meetingId)
        {
            return await _context.MeetingActionItems
                .Include(a => a.AssignedTo)
                .Where(a => a.MeetingId == meetingId)
                .OrderBy(a => a.DueDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<MeetingActionItem>> GetUserActionItemsAsync(string userId, bool includeCompleted = false)
        {
            var query = _context.MeetingActionItems
                .Include(a => a.Meeting)
                .Include(a => a.AssignedTo)
                .Where(a => a.AssignedToUserId == userId);

            if (!includeCompleted)
                query = query.Where(a => a.Status != ActionItemStatus.Completed);

            return await query.OrderBy(a => a.DueDate).ToListAsync();
        }

        public async Task<MeetingActionItem> CreateActionItemAsync(MeetingActionItem actionItem)
        {
            actionItem.CreatedAt = DateTime.UtcNow;
            actionItem.UpdatedAt = DateTime.UtcNow;

            _context.MeetingActionItems.Add(actionItem);
            await _context.SaveChangesAsync();
            return actionItem;
        }

        public async Task<MeetingActionItem> UpdateActionItemAsync(MeetingActionItem actionItem)
        {
            actionItem.UpdatedAt = DateTime.UtcNow;

            _context.MeetingActionItems.Update(actionItem);
            await _context.SaveChangesAsync();
            return actionItem;
        }

        public async Task<bool> CompleteActionItemAsync(int actionItemId)
        {
            var actionItem = await _context.MeetingActionItems.FindAsync(actionItemId);
            if (actionItem == null) return false;

            actionItem.Status = ActionItemStatus.Completed;
            actionItem.CompletedDate = DateTime.UtcNow;
            actionItem.UpdatedAt = DateTime.UtcNow;

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> DeleteActionItemAsync(int actionItemId)
        {
            var actionItem = await _context.MeetingActionItems.FindAsync(actionItemId);
            if (actionItem == null) return false;

            _context.MeetingActionItems.Remove(actionItem);
            return await _context.SaveChangesAsync() > 0;
        }

        // Analytics and Reporting
        public async Task<Dictionary<string, object>> GetMeetingAnalyticsAsync(int projectId, DateTime startDate, DateTime endDate)
        {
            var meetings = await _context.Meetings
                .Where(m => m.ProjectId == projectId &&
                           m.ScheduledDate >= startDate &&
                           m.ScheduledDate <= endDate)
                .Include(m => m.Attendees)
                .Include(m => m.MeetingActionItems)
                .ToListAsync();

            var totalMeetings = meetings.Count;
            var completedMeetings = meetings.Count(m => m.Status == MeetingStatus.Completed);
            var cancelledMeetings = meetings.Count(m => m.Status == MeetingStatus.Cancelled);
            var averageDuration = meetings.Where(m => m.ActualEndTime.HasValue && m.ActualStartTime.HasValue)
                .Select(m => (m.ActualEndTime!.Value - m.ActualStartTime!.Value).TotalMinutes)
                .DefaultIfEmpty(0).Average();
            var totalActionItems = meetings.SelectMany(m => m.MeetingActionItems).Count();
            var completedActionItems = meetings.SelectMany(m => m.MeetingActionItems)
                .Count(ai => ai.Status == ActionItemStatus.Completed);

            return new Dictionary<string, object>
            {
                ["TotalMeetings"] = totalMeetings,
                ["CompletedMeetings"] = completedMeetings,
                ["CancelledMeetings"] = cancelledMeetings,
                ["CompletionRate"] = totalMeetings > 0 ? (double)completedMeetings / totalMeetings * 100 : 0,
                ["AverageDuration"] = Math.Round(averageDuration, 1),
                ["TotalActionItems"] = totalActionItems,
                ["CompletedActionItems"] = completedActionItems,
                ["ActionItemCompletionRate"] = totalActionItems > 0 ? (double)completedActionItems / totalActionItems * 100 : 0,
                ["MeetingsByType"] = meetings.GroupBy(m => m.Type).ToDictionary(g => g.Key.ToString(), g => g.Count())
            };
        }

        public async Task<IEnumerable<object>> GetMeetingAttendanceReportAsync(int projectId, DateTime startDate, DateTime endDate)
        {
            var attendanceData = await _context.MeetingAttendees
                .Include(a => a.Meeting)
                .Include(a => a.User)
                .Where(a => a.Meeting.ProjectId == projectId &&
                           a.Meeting.ScheduledDate >= startDate &&
                           a.Meeting.ScheduledDate <= endDate)
                .GroupBy(a => new { a.UserId, a.User.FirstName, a.User.LastName, a.User.Email })
                .Select(g => new
                {
                    UserId = g.Key.UserId,
                    UserName = $"{g.Key.FirstName} {g.Key.LastName}",
                    Email = g.Key.Email,
                    TotalInvited = g.Count(),
                    Attended = g.Count(a => a.Status == AttendanceStatus.Attended),
                    NoShow = g.Count(a => a.Status == AttendanceStatus.NoShow),
                    Declined = g.Count(a => a.Status == AttendanceStatus.Declined),
                    AttendanceRate = g.Count() > 0 ? (double)g.Count(a => a.Status == AttendanceStatus.Attended) / g.Count() * 100 : 0
                })
                .ToListAsync();

            return attendanceData.Cast<object>();
        }

        public async Task<decimal> GetAverageMeetingDurationAsync(int projectId, MeetingType? type = null)
        {
            var query = _context.Meetings
                .Where(m => m.ProjectId == projectId &&
                           m.ActualStartTime.HasValue &&
                           m.ActualEndTime.HasValue);

            if (type.HasValue)
            {
                query = query.Where(m => m.Type == type.Value);
            }

            var durations = await query
                .Select(m => (m.ActualEndTime!.Value - m.ActualStartTime!.Value).TotalMinutes)
                .ToListAsync();

            return durations.Any() ? (decimal)durations.Average() : 60m;
        }

        public async Task<int> GetActionItemCompletionRateAsync(int projectId)
        {
            var actionItems = await _context.MeetingActionItems
                .Include(ai => ai.Meeting)
                .Where(ai => ai.Meeting.ProjectId == projectId)
                .ToListAsync();

            if (!actionItems.Any()) return 0;

            var completedItems = actionItems.Count(ai => ai.Status == ActionItemStatus.Completed);
            return (int)Math.Round((double)completedItems / actionItems.Count * 100);
        }

        public async Task<IEnumerable<Meeting>> CreateRecurringMeetingsAsync(Meeting templateMeeting, string recurrencePattern, DateTime endDate)
        {
            var createdMeetings = new List<Meeting>();
            var currentDate = templateMeeting.ScheduledDate;

            // Parse recurrence pattern (simple implementation)
            // Patterns: "daily", "weekly", "biweekly", "monthly"
            var interval = recurrencePattern.ToLower() switch
            {
                "daily" => TimeSpan.FromDays(1),
                "weekly" => TimeSpan.FromDays(7),
                "biweekly" => TimeSpan.FromDays(14),
                "monthly" => TimeSpan.FromDays(30), // Approximate
                _ => TimeSpan.FromDays(7) // Default to weekly
            };

            while (currentDate <= endDate)
            {
                var recurringMeeting = new Meeting
                {
                    Title = templateMeeting.Title,
                    Description = templateMeeting.Description,
                    ProjectId = templateMeeting.ProjectId,
                    Type = templateMeeting.Type,
                    ScheduledDate = currentDate,
                    DurationMinutes = templateMeeting.DurationMinutes,
                    Location = templateMeeting.Location,
                    Status = MeetingStatus.Scheduled,
                    OrganizerUserId = templateMeeting.OrganizerUserId,
                    Agenda = templateMeeting.Agenda,
                    RecurrencePattern = recurrencePattern,
                    ParentMeetingId = templateMeeting.Id > 0 ? templateMeeting.Id : null,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.Meetings.Add(recurringMeeting);
                createdMeetings.Add(recurringMeeting);

                currentDate = currentDate.Add(interval);
            }

            await _context.SaveChangesAsync();
            return createdMeetings;
        }

        public async Task<bool> UpdateRecurringMeetingSeriesAsync(int meetingId, Meeting updatedMeeting, bool updateFutureOnly = true)
        {
            var baseMeeting = await _context.Meetings.FindAsync(meetingId);
            if (baseMeeting == null) return false;

            // Find all meetings in the series
            var parentId = baseMeeting.ParentMeetingId ?? baseMeeting.Id;
            var seriesMeetings = await _context.Meetings
                .Where(m => m.ParentMeetingId == parentId || m.Id == parentId)
                .ToListAsync();

            var meetingsToUpdate = updateFutureOnly
                ? seriesMeetings.Where(m => m.ScheduledDate >= baseMeeting.ScheduledDate).ToList()
                : seriesMeetings;

            foreach (var meeting in meetingsToUpdate)
            {
                // Update properties but preserve the scheduled date
                var originalDate = meeting.ScheduledDate;
                meeting.Title = updatedMeeting.Title;
                meeting.Description = updatedMeeting.Description;
                meeting.DurationMinutes = updatedMeeting.DurationMinutes;
                meeting.Location = updatedMeeting.Location;
                meeting.Agenda = updatedMeeting.Agenda;
                meeting.ScheduledDate = originalDate; // Preserve original date
                meeting.UpdatedAt = DateTime.UtcNow;
            }

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> CancelRecurringMeetingSeriesAsync(int meetingId, bool cancelFutureOnly = true)
        {
            // TODO: Implement recurring meeting cancellation
            return false;
        }

        public async Task<IEnumerable<MeetingDocument>> GetMeetingDocumentsAsync(int meetingId)
        {
            return await _context.MeetingDocuments
                .Where(d => d.MeetingId == meetingId)
                .ToListAsync();
        }

        public async Task<MeetingDocument> AttachDocumentToMeetingAsync(int meetingId, int documentId, DocumentType type)
        {
            // For now, create a placeholder document since we don't have a separate Document entity
            var document = new MeetingDocument
            {
                MeetingId = meetingId,
                FileName = $"Document_{documentId}",
                FilePath = $"/documents/{documentId}",
                Type = type,
                CreatedAt = DateTime.UtcNow,
                UploadedByUserId = "" // This should be passed as parameter
            };

            _context.MeetingDocuments.Add(document);
            await _context.SaveChangesAsync();
            return document;
        }

        public async Task<bool> RemoveDocumentFromMeetingAsync(int meetingId, int documentId)
        {
            var document = await _context.MeetingDocuments
                .FirstOrDefaultAsync(d => d.MeetingId == meetingId && d.FileName == $"Document_{documentId}");

            if (document == null) return false;

            _context.MeetingDocuments.Remove(document);
            return await _context.SaveChangesAsync() > 0;
        }

        // Notification methods - placeholder implementations
        public async Task<bool> SendMeetingInvitesAsync(int meetingId)
        {
            // TODO: Implement meeting invites
            return true;
        }

        public async Task<bool> SendMeetingRemindersAsync(int meetingId, int minutesBefore = 15)
        {
            // TODO: Implement meeting reminders
            return true;
        }

        public async Task<bool> SendMeetingCancellationAsync(int meetingId, string reason)
        {
            // TODO: Implement meeting cancellation notifications
            return true;
        }

        public async Task<bool> SendActionItemRemindersAsync(string userId)
        {
            // TODO: Implement action item reminders
            return true;
        }
    }
}
