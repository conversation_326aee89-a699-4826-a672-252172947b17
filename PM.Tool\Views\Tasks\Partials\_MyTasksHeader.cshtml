@model MyTasksViewModel

<!-- Refined Page Header -->
<div class="mb-6 page-header rounded-xl border border-neutral-200 dark:border-neutral-700">
    <!-- Title and Actions Row -->
    <div class="flex items-center justify-between p-6 border-b border-neutral-200 dark:border-neutral-700">
        <div class="flex items-baseline space-x-6">
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-white tracking-tight">My Tasks</h1>
            <!-- Inline Stats -->
            <div class="hidden lg:flex items-center space-x-3">
                <div class="stats-badge flex items-center px-3 py-1.5 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-lg border border-blue-200 dark:border-blue-800">
                    <i class="fas fa-tasks mr-2 text-blue-600 dark:text-blue-400"></i>
                    <span class="text-sm font-semibold">@Model.Stats.TotalTasks</span>
                    <span class="text-xs text-blue-600 dark:text-blue-400 ml-1">total</span>
                </div>
                <div class="stats-badge flex items-center px-3 py-1.5 bg-amber-50 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300 rounded-lg border border-amber-200 dark:border-amber-800">
                    <i class="fas fa-clock mr-2 text-amber-600 dark:text-amber-400"></i>
                    <span class="text-sm font-semibold">@Model.Stats.DueTodayTasks</span>
                    <span class="text-xs text-amber-600 dark:text-amber-400 ml-1">today</span>
                </div>
                @if (Model.Stats.OverdueTasks > 0)
                {
                    <div class="stats-badge flex items-center px-3 py-1.5 bg-red-50 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-lg border border-red-200 dark:border-red-800">
                        <i class="fas fa-exclamation-triangle mr-2 text-red-600 dark:text-red-400"></i>
                        <span class="text-sm font-semibold">@Model.Stats.OverdueTasks</span>
                        <span class="text-xs text-red-600 dark:text-red-400 ml-1">overdue</span>
                    </div>
                }
                <div class="stats-badge flex items-center px-3 py-1.5 bg-emerald-50 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 rounded-lg border border-emerald-200 dark:border-emerald-800">
                    <i class="fas fa-chart-line mr-2 text-emerald-600 dark:text-emerald-400"></i>
                    <span class="text-sm font-semibold">@Model.Stats.CompletionRate.ToString("F0")%</span>
                    <span class="text-xs text-emerald-600 dark:text-emerald-400 ml-1">complete</span>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center space-x-3">
            <a href="@Url.Action("Create")" class="inline-flex items-center px-4 py-2 text-sm font-semibold text-white bg-primary-600 hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg shadow-sm transition-all duration-200 hover:shadow-md">
                <i class="fas fa-plus mr-2"></i>New Task
            </a>
            <button type="button" onclick="showExportModal()" class="inline-flex items-center px-4 py-2 text-sm font-medium text-neutral-700 dark:text-neutral-300 bg-white dark:bg-neutral-800 border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg shadow-sm transition-all duration-200 hover:shadow-md">
                <i class="fas fa-download mr-2"></i>Export
            </button>
            <button type="button" id="toggleStatsBtn" onclick="toggleDetailedStats()" class="lg:hidden inline-flex items-center px-3 py-2 text-sm font-medium text-neutral-700 dark:text-neutral-300 bg-white dark:bg-neutral-800 border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg shadow-sm transition-all duration-200">
                <i class="fas fa-chart-bar mr-2"></i>Stats
            </button>
        </div>
    </div>

    <!-- Detailed Stats (Collapsible on Mobile) -->
    <div id="detailedStats" class="hidden lg:hidden mt-4">
        <div class="grid grid-cols-2 sm:grid-cols-4 gap-4 p-4 bg-gradient-to-r from-neutral-50 to-neutral-100 dark:from-neutral-800 dark:to-neutral-700 rounded-xl border border-neutral-200 dark:border-neutral-600">
            <div class="text-center p-3 bg-white dark:bg-neutral-800 rounded-lg shadow-sm">
                <div class="text-xl font-bold text-blue-600 dark:text-blue-400">@Model.Stats.InProgressTasks</div>
                <div class="text-xs font-medium text-neutral-600 dark:text-neutral-400 mt-1">In Progress</div>
            </div>
            <div class="text-center p-3 bg-white dark:bg-neutral-800 rounded-lg shadow-sm">
                <div class="text-xl font-bold text-emerald-600 dark:text-emerald-400">@Model.Stats.CompletedTasks</div>
                <div class="text-xs font-medium text-neutral-600 dark:text-neutral-400 mt-1">Completed</div>
            </div>
            <div class="text-center p-3 bg-white dark:bg-neutral-800 rounded-lg shadow-sm">
                <div class="text-xl font-bold text-orange-600 dark:text-orange-400">@Model.Stats.HighPriorityTasks</div>
                <div class="text-xs font-medium text-neutral-600 dark:text-neutral-400 mt-1">High Priority</div>
            </div>
            <div class="text-center p-3 bg-white dark:bg-neutral-800 rounded-lg shadow-sm">
                <div class="text-xl font-bold text-neutral-600 dark:text-neutral-400">@Model.Stats.TodoTasks</div>
                <div class="text-xs font-medium text-neutral-600 dark:text-neutral-400 mt-1">To Do</div>
            </div>
        </div>
    </div>
</div>
