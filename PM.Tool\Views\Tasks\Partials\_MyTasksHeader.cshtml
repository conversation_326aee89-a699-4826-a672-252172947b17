@model MyTasksViewModel

<!-- Refined Page Header -->
<div class="mb-8 page-header rounded-xl border border-neutral-200 dark:border-neutral-700">
    <!-- Title and Actions Row -->
    <div class="flex items-center justify-between px-8 py-6 border-b border-neutral-200 dark:border-neutral-700">
        <div class="flex items-center space-x-8">
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-white tracking-tight">My Tasks</h1>
            <!-- Compact Analytics Badges -->
            <div class="hidden md:flex items-center space-x-4">
                <span class="stats-badge inline-flex items-center px-3 py-1.5 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200 rounded-full text-xs font-medium">
                    <i class="fas fa-tasks mr-2 text-blue-600 dark:text-blue-400"></i>
                    @Model.Stats.TotalTasks Total
                </span>
                <span class="stats-badge inline-flex items-center px-3 py-1.5 bg-amber-100 dark:bg-amber-900/50 text-amber-800 dark:text-amber-200 rounded-full text-xs font-medium">
                    <i class="fas fa-clock mr-2 text-amber-600 dark:text-amber-400"></i>
                    @Model.Stats.DueTodayTasks Today
                </span>
                @if (Model.Stats.OverdueTasks > 0)
                {
                    <span class="stats-badge inline-flex items-center px-3 py-1.5 bg-red-100 dark:bg-red-900/50 text-red-800 dark:text-red-200 rounded-full text-xs font-medium">
                        <i class="fas fa-exclamation-triangle mr-2 text-red-600 dark:text-red-400"></i>
                        @Model.Stats.OverdueTasks Overdue
                    </span>
                }
                <span class="stats-badge inline-flex items-center px-3 py-1.5 bg-emerald-100 dark:bg-emerald-900/50 text-emerald-800 dark:text-emerald-200 rounded-full text-xs font-medium">
                    <i class="fas fa-chart-line mr-2 text-emerald-600 dark:text-emerald-400"></i>
                    @Model.Stats.CompletionRate.ToString("F0")% Done
                </span>
                <span class="stats-badge inline-flex items-center px-3 py-1.5 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200 rounded-full text-xs font-medium">
                    <i class="fas fa-play mr-2 text-blue-600 dark:text-blue-400"></i>
                    @Model.Stats.InProgressTasks Active
                </span>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center space-x-4">
            <a href="@Url.Action("Create")" class="inline-flex items-center px-5 py-2.5 text-sm font-semibold text-white bg-primary-600 hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg shadow-sm transition-all duration-200 hover:shadow-md">
                <i class="fas fa-plus mr-2.5"></i>New Task
            </a>
            <button type="button" onclick="showExportModal()" class="inline-flex items-center px-5 py-2.5 text-sm font-medium text-neutral-700 dark:text-neutral-300 bg-white dark:bg-neutral-800 border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg shadow-sm transition-all duration-200 hover:shadow-md">
                <i class="fas fa-download mr-2.5"></i>Export
            </button>
        </div>
    </div>
</div>
