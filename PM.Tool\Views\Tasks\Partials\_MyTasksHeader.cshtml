@model MyTasksViewModel

<!-- Page Header with Stats -->
<div class="mb-8">
    <!-- Page Title and Actions -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
        <div>
            <h1 class="text-3xl font-bold text-neutral-900 dark:text-white">My Tasks</h1>
            <p class="mt-2 text-neutral-600 dark:text-neutral-400">Manage and track your assigned tasks efficiently</p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
            @{
                ViewData["Text"] = "Create New Task";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-plus";
                ViewData["Href"] = Url.Action("Create");
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "Export Tasks";
                ViewData["Variant"] = "outline";
                ViewData["Icon"] = "fas fa-download";
                ViewData["Href"] = "#";
                ViewData["OnClick"] = "showExportModal()";
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "Calendar View";
                ViewData["Variant"] = "outline";
                ViewData["Icon"] = "fas fa-calendar";
                ViewData["Href"] = "#";
                ViewData["OnClick"] = "toggleCalendarView()";
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>

    <!-- Statistics Cards -->
    <partial name="Partials/_MyTasksStats" model="Model.Stats" />
</div>
