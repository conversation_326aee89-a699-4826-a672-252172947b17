@model MyTasksViewModel

<!-- Compact Page Header -->
<div class="mb-4">
    <!-- Title and Actions Row -->
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-4">
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-white">My Tasks</h1>
            <!-- Inline Stats -->
            <div class="hidden md:flex items-center space-x-4 text-sm">
                <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full">
                    <i class="fas fa-tasks mr-1"></i>@Model.Stats.TotalTasks Total
                </span>
                <span class="px-2 py-1 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded-full">
                    <i class="fas fa-clock mr-1"></i>@Model.Stats.DueTodayTasks Due Today
                </span>
                @if (Model.Stats.OverdueTasks > 0)
                {
                    <span class="px-2 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-full">
                        <i class="fas fa-exclamation-triangle mr-1"></i>@Model.Stats.OverdueTasks Overdue
                    </span>
                }
                <span class="px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full">
                    <i class="fas fa-chart-line mr-1"></i>@Model.Stats.CompletionRate.ToString("F0")% Complete
                </span>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex space-x-2">
            <a href="@Url.Action("Create")" class="px-3 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md transition-colors">
                <i class="fas fa-plus mr-1"></i>New Task
            </a>
            <button type="button" onclick="showExportModal()" class="px-3 py-2 text-sm font-medium text-neutral-600 dark:text-neutral-300 border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 rounded-md transition-colors">
                <i class="fas fa-download mr-1"></i>Export
            </button>
            <button type="button" id="toggleStatsBtn" onclick="toggleDetailedStats()" class="md:hidden px-3 py-2 text-sm font-medium text-neutral-600 dark:text-neutral-300 border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-neutral-700 rounded-md transition-colors">
                <i class="fas fa-chart-bar mr-1"></i>Stats
            </button>
        </div>
    </div>

    <!-- Detailed Stats (Collapsible on Mobile) -->
    <div id="detailedStats" class="hidden md:hidden">
        <div class="grid grid-cols-2 sm:grid-cols-4 gap-3 p-3 bg-neutral-50 dark:bg-neutral-800 rounded-lg">
            <div class="text-center">
                <div class="text-lg font-bold text-blue-600 dark:text-blue-400">@Model.Stats.InProgressTasks</div>
                <div class="text-xs text-neutral-500 dark:text-neutral-400">In Progress</div>
            </div>
            <div class="text-center">
                <div class="text-lg font-bold text-green-600 dark:text-green-400">@Model.Stats.CompletedTasks</div>
                <div class="text-xs text-neutral-500 dark:text-neutral-400">Completed</div>
            </div>
            <div class="text-center">
                <div class="text-lg font-bold text-orange-600 dark:text-orange-400">@Model.Stats.HighPriorityTasks</div>
                <div class="text-xs text-neutral-500 dark:text-neutral-400">High Priority</div>
            </div>
            <div class="text-center">
                <div class="text-lg font-bold text-gray-600 dark:text-gray-400">@Model.Stats.TodoTasks</div>
                <div class="text-xs text-neutral-500 dark:text-neutral-400">To Do</div>
            </div>
        </div>
    </div>
</div>
