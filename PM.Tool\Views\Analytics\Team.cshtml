@{
    ViewData["Title"] = "Team Analytics";
    var projects = ViewBag.Projects as IEnumerable<PM.Tool.Core.Entities.Project> ?? new List<PM.Tool.Core.Entities.Project>();
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-users mr-3 text-primary-600 dark:text-primary-400"></i>
                Team Analytics
            </h1>
            <p class="mt-2 text-lg text-neutral-600 dark:text-dark-300">
                Team performance metrics and collaboration insights
            </p>
        </div>
        <div class="flex space-x-3">
            @{
                ViewData["Text"] = "Back to Dashboard";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Index");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Team Overview Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
    <!-- Team Members -->
    <div class="stats-card-custom">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
                <i class="fas fa-users text-white text-lg"></i>
            </div>
            <div class="ml-4">
                <p class="text-2xl font-bold text-neutral-900 dark:text-dark-100">@projects.Where(p => p.Members != null).SelectMany(p => p.Members).Select(m => m.UserId).Distinct().Count()</p>
                <p class="text-sm text-neutral-500 dark:text-dark-400">Team Members</p>
            </div>
        </div>
        <div class="mt-4">
            <div class="flex items-center text-xs text-neutral-500 dark:text-dark-400">
                <i class="fas fa-users mr-1"></i>
                Active across all projects
            </div>
        </div>
    </div>

    <!-- Completed Tasks -->
    <div class="stats-card-custom">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-success-500 to-success-600 rounded-xl flex items-center justify-center">
                <i class="fas fa-check-circle text-white text-lg"></i>
            </div>
            <div class="ml-4">
                <p class="text-2xl font-bold text-neutral-900 dark:text-dark-100">@projects.Sum(p => p.CompletedTasks)</p>
                <p class="text-sm text-neutral-500 dark:text-dark-400">Completed Tasks</p>
            </div>
        </div>
        <div class="mt-4">
            <div class="flex items-center text-xs text-neutral-500 dark:text-dark-400">
                <i class="fas fa-check mr-1"></i>
                Successfully finished
            </div>
        </div>
    </div>

    <!-- Pending Tasks -->
    <div class="stats-card-custom">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-info-500 to-info-600 rounded-xl flex items-center justify-center">
                <i class="fas fa-clock text-white text-lg"></i>
            </div>
            <div class="ml-4">
                <p class="text-2xl font-bold text-neutral-900 dark:text-dark-100">@projects.Sum(p => p.TotalTasks - p.CompletedTasks)</p>
                <p class="text-sm text-neutral-500 dark:text-dark-400">Pending Tasks</p>
            </div>
        </div>
        <div class="mt-4">
            <div class="flex items-center text-xs text-neutral-500 dark:text-dark-400">
                <i class="fas fa-hourglass-half mr-1"></i>
                In progress or waiting
            </div>
        </div>
    </div>

    <!-- Completion Rate -->
    <div class="stats-card-custom">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-warning-500 to-warning-600 rounded-xl flex items-center justify-center">
                <i class="fas fa-percentage text-white text-lg"></i>
            </div>
            <div class="ml-4">
                <p class="text-2xl font-bold text-neutral-900 dark:text-dark-100">@(projects.Any() && projects.Sum(p => p.TotalTasks) > 0 ? ((double)projects.Sum(p => p.CompletedTasks) / projects.Sum(p => p.TotalTasks) * 100).ToString("F0") : "0")%</p>
                <p class="text-sm text-neutral-500 dark:text-dark-400">Completion Rate</p>
            </div>
        </div>
        <div class="mt-4">
            @{
                var completionRate = projects.Any() && projects.Sum(p => p.TotalTasks) > 0 ?
                    (double)projects.Sum(p => p.CompletedTasks) / projects.Sum(p => p.TotalTasks) * 100 : 0;
                var rateClass = completionRate >= 80 ? "bg-success-500" : completionRate >= 60 ? "bg-warning-500" : "bg-danger-500";
            }
            <div class="w-full bg-neutral-200 dark:bg-dark-600 rounded-full h-2">
                <div class="@rateClass h-2 rounded-full transition-all duration-300" style="width: @completionRate%"></div>
            </div>
        </div>
    </div>
</div>

<!-- Team Performance Section -->
<div class="grid grid-cols-1 xl:grid-cols-3 gap-6 mb-8">
    <!-- Team Performance Overview -->
    <div class="xl:col-span-2">
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Team Performance Overview</h3>
                        <p class="text-sm text-neutral-500 dark:text-dark-400">Project progress by team</p>
                    </div>
                </div>
            </div>
            <div class="card-body-custom">
                @if (projects.Any())
                {
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        @foreach (var project in projects.Take(6))
                        {
                            <div class="space-y-3">
                                <div class="flex items-center justify-between">
                                    <h4 class="text-sm font-semibold text-neutral-900 dark:text-dark-100">@project.Name</h4>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200">
                                        <i class="fas fa-users mr-1"></i>
                                        @(project.Members?.Count ?? 0) members
                                    </span>
                                </div>
                                <div class="space-y-2">
                                    @{
                                        var progressClass = project.ProgressPercentage >= 80 ? "bg-success-500" :
                                                          project.ProgressPercentage >= 60 ? "bg-primary-500" :
                                                          project.ProgressPercentage >= 30 ? "bg-warning-500" : "bg-danger-500";
                                    }
                                    <div class="w-full bg-neutral-200 dark:bg-dark-600 rounded-full h-2">
                                        <div class="@progressClass h-2 rounded-full transition-all duration-500 progress-bar-animate"
                                             data-width="@project.ProgressPercentage" style="width: 0%"></div>
                                    </div>
                                    <div class="flex items-center justify-between text-xs">
                                        <span class="text-neutral-500 dark:text-dark-400">@project.ProgressPercentage.ToString("F0")% Complete</span>
                                        <span class="text-neutral-400 dark:text-dark-500">@project.CompletedTasks/@project.TotalTasks tasks</span>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center py-12">
                        <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-chart-line text-2xl text-neutral-400 dark:text-dark-500"></i>
                        </div>
                        <p class="text-neutral-500 dark:text-dark-400">No project data available</p>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Top Performers -->
    <div class="xl:col-span-1">
        <div class="card-custom h-full">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-warning-100 dark:bg-warning-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-trophy text-warning-600 dark:text-warning-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Top Performers</h3>
                        <p class="text-sm text-neutral-500 dark:text-dark-400">Team achievements</p>
                    </div>
                </div>
            </div>
            <div class="card-body-custom">
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-warning-100 dark:bg-warning-900 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-chart-bar text-2xl text-warning-600 dark:text-warning-400"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">Performance Tracking</h4>
                    <p class="text-sm text-neutral-500 dark:text-dark-400 mb-4">
                        Individual performance metrics will be available once team members complete more tasks.
                    </p>
                    <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200">
                        <i class="fas fa-clock mr-1"></i>
                        Coming Soon
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Team Collaboration Section -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Collaboration Metrics -->
    <div class="card-custom h-full">
        <div class="card-header-custom">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-success-100 dark:bg-success-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-comments text-success-600 dark:text-success-400 text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Collaboration Metrics</h3>
                    <p class="text-sm text-neutral-500 dark:text-dark-400">Team communication insights</p>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-success-100 dark:bg-success-900 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-handshake text-2xl text-success-600 dark:text-success-400"></i>
                </div>
                <h4 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">Coming Soon</h4>
                <p class="text-sm text-neutral-500 dark:text-dark-400 mb-4">
                    Team collaboration insights including communication patterns, meeting frequency, and cross-project collaboration.
                </p>
                <div class="space-y-2">
                    <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200">
                        <i class="fas fa-comments mr-1"></i>
                        Communication Tracking
                    </div>
                    <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200">
                        <i class="fas fa-calendar mr-1"></i>
                        Meeting Analytics
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Resource Utilization -->
    <div class="card-custom h-full">
        <div class="card-header-custom">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-info-100 dark:bg-info-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-chart-pie text-info-600 dark:text-info-400 text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Resource Utilization</h3>
                    <p class="text-sm text-neutral-500 dark:text-dark-400">Team capacity and allocation</p>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-info-100 dark:bg-info-900 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-chart-pie text-2xl text-info-600 dark:text-info-400"></i>
                </div>
                <h4 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">Coming Soon</h4>
                <p class="text-sm text-neutral-500 dark:text-dark-400 mb-4">
                    Resource allocation and utilization metrics across projects and team members.
                </p>
                <div class="space-y-2">
                    <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200">
                        <i class="fas fa-users mr-1"></i>
                        Capacity Planning
                    </div>
                    <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200">
                        <i class="fas fa-clock mr-1"></i>
                        Time Allocation
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Animate progress bars
            $('.progress-bar-animate').each(function(index) {
                const $this = $(this);
                const targetWidth = $this.data('width') + '%';

                setTimeout(function() {
                    $this.css('width', targetWidth);
                }, index * 200);
            });

            // Animate stats cards
            $('.stats-card-custom').each(function(index) {
                $(this).delay(index * 150).queue(function() {
                    $(this).addClass('animate-fade-in-up').dequeue();
                });
            });

            // Add hover effects to performance cards
            $('.card-custom').hover(
                function() {
                    $(this).addClass('transform scale-105 transition-transform duration-200');
                },
                function() {
                    $(this).removeClass('transform scale-105 transition-transform duration-200');
                }
            );
        });
    </script>
}
