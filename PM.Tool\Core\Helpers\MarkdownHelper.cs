using Markdig;

namespace PM.Tool.Core.Helpers
{
    public static class MarkdownHelper
    {
        private static readonly MarkdownPipeline Pipeline = new MarkdownPipelineBuilder()
            .UseAdvancedExtensions()
            .DisableHtml()
            .Build();

        public static string FormatMarkdown(string content)
        {
            if (string.IsNullOrEmpty(content))
                return string.Empty;

            return Markdown.ToHtml(content, Pipeline);
        }
    }
}