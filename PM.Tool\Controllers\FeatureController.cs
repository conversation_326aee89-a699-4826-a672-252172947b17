using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authorization;
using PM.Tool.Core.Entities.Agile;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Enums;
using PM.Tool.Services;
using PM.Tool.Models.ViewModels;
using PM.Tool.Models.DTOs;

namespace PM.Tool.Controllers
{
    [Authorize]
    public class FeatureController : SecureBaseController
    {
        private readonly IAgileService _agileService;
        private readonly IProjectService _projectService;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<FeatureController> _logger;
        private readonly IFormHelperService _formHelper;

        public FeatureController(
            IAgileService agileService,
            IProjectService projectService,
            UserManager<ApplicationUser> userManager,
            ILogger<FeatureController> logger,
            IFormHelperService formHelper,
            IAuditService auditService) : base(auditService)
        {
            _agileService = agileService;
            _projectService = projectService;
            _userManager = userManager;
            _logger = logger;
            _formHelper = formHelper;
        }

        #region List and Index

        // GET: Feature
        public async Task<IActionResult> Index(int projectId, int? epicId = null, FeatureStatus? status = null, FeaturePriority? priority = null, string? searchTerm = null)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                var features = await _agileService.GetProjectFeaturesAsync(projectId);
                var epics = await _agileService.GetProjectEpicsAsync(projectId);

                // Apply filters
                if (epicId.HasValue)
                    features = features.Where(f => f.EpicId == epicId.Value);

                if (status.HasValue)
                    features = features.Where(f => f.Status == status.Value);

                if (priority.HasValue)
                    features = features.Where(f => f.Priority == priority.Value);

                if (!string.IsNullOrEmpty(searchTerm))
                    features = features.Where(f => f.Title.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                                                  f.Description.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));

                var featureSummaries = features.Select(f => new FeatureSummaryDto
                {
                    Id = f.Id,
                    FeatureKey = f.FeatureKey,
                    Title = f.Title,
                    Status = f.Status,
                    Priority = f.Priority,
                    ProgressPercentage = (decimal)f.ProgressPercentage,
                    UserStoryCount = f.UserStoryCount,
                    CompletedUserStoryCount = f.CompletedUserStoryCount,
                    EpicTitle = f.Epic?.Title ?? string.Empty,
                    OwnerName = f.Owner?.UserName,
                    TargetDate = f.TargetDate
                });

                var viewModel = new FeatureListViewModel
                {
                    ProjectId = projectId,
                    ProjectName = project.Name,
                    Features = featureSummaries,
                    Epics = epics,
                    FilterOptions = new FeatureFilterOptions
                    {
                        EpicId = epicId,
                        Status = status,
                        Priority = priority,
                        SearchTerm = searchTerm
                    },
                    Metrics = new FeatureListMetrics
                    {
                        TotalFeatures = features.Count(),
                        CompletedFeatures = features.Count(f => f.IsCompleted),
                        InProgressFeatures = features.Count(f => f.Status == FeatureStatus.InProgress),
                        OverdueFeatures = features.Count(f => f.IsOverdue),
                        OverallProgress = features.Any() ? (decimal)features.Average(f => f.ProgressPercentage) : 0,
                        TotalStoryPoints = features.Sum(f => f.EstimatedStoryPoints),
                        CompletedStoryPoints = features.Sum(f => f.ActualStoryPoints)
                    }
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading features for project {ProjectId}", projectId);
                TempData["Error"] = "An error occurred while loading features.";
                return RedirectToAction("Details", "Projects", new { id = projectId });
            }
        }

        #endregion

        #region Details

        // GET: Feature/Details/5
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var feature = await _agileService.GetFeatureByIdAsync(id);
                if (feature == null) return NotFound();

                var userStories = feature.UserStories;
                var bugs = await _agileService.GetFeatureBugsAsync(id);
                var testCases = await _agileService.GetFeatureTestCasesAsync(id);

                var viewModel = new FeatureDetailsViewModel
                {
                    Feature = feature,
                    UserStories = userStories,
                    Bugs = bugs,
                    TestCases = testCases,
                    Metrics = new FeatureMetrics
                    {
                        TotalUserStories = userStories.Count(),
                        CompletedUserStories = userStories.Count(us => us.IsCompleted),
                        CompletionPercentage = userStories.Any() ?
                            (decimal)userStories.Count(us => us.IsCompleted) / userStories.Count() * 100 : 0,
                        TotalBugs = bugs.Count(),
                        OpenBugs = bugs.Count(b => !b.IsResolved),
                        TotalTestCases = testCases.Count(),
                        PassedTestCases = testCases.Count(tc => tc.LastExecutionResult == TestExecutionResult.Passed),
                        TestPassRate = testCases.Any() ?
                            (decimal)testCases.Count(tc => tc.LastExecutionResult == TestExecutionResult.Passed) / testCases.Count() * 100 : 0,
                        TotalStoryPoints = feature.EstimatedStoryPoints,
                        CompletedStoryPoints = feature.ActualStoryPoints
                    }
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading feature details for {FeatureId}", id);
                TempData["Error"] = "An error occurred while loading feature details.";
                return RedirectToAction("Index");
            }
        }

        #endregion

        #region Create

        // GET: Feature/Create
        public async Task<IActionResult> Create(int projectId, int? epicId = null)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                var viewModel = new FeatureCreateViewModel
                {
                    ProjectId = projectId,
                    EpicId = epicId ?? 0
                };

                await PopulateDropdowns(projectId);
                ViewBag.Project = project;

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading create feature form for project {ProjectId}", projectId);
                TempData["Error"] = "An error occurred while loading the create form.";
                return RedirectToAction("Index", new { projectId });
            }
        }

        // POST: Feature/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(FeatureCreateViewModel viewModel)
        {
            return await this.HandleCreateAsync<FeatureCreateViewModel, Feature>(
                viewModel,
                async (feature) => {
                    var user = await _userManager.GetUserAsync(User);
                    feature.OwnerId = user?.Id;

                    // Generate feature key
                    var existingFeatures = await _agileService.GetProjectFeaturesAsync(feature.ProjectId);
                    feature.FeatureKey = $"FT-{existingFeatures.Count() + 1:D3}";

                    var createdFeature = await _agileService.CreateFeatureAsync(feature);
                    await LogAuditAsync(AuditAction.Create, "Feature", createdFeature.Id);
                    return createdFeature;
                },
                feature => feature.Id,
                async () => await PopulateDropdowns(viewModel.ProjectId),
                _formHelper,
                _logger,
                "Feature",
                "Details"
            );
        }

        #endregion

        #region Edit

        // GET: Feature/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                var feature = await _agileService.GetFeatureByIdAsync(id);
                if (feature == null) return NotFound();

                var viewModel = FeatureEditViewModel.FromEntity(feature);
                await PopulateDropdowns(feature.ProjectId);
                ViewBag.Project = await _projectService.GetProjectByIdAsync(feature.ProjectId);

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit form for feature {FeatureId}", id);
                TempData["Error"] = "An error occurred while loading the edit form.";
                return RedirectToAction("Details", new { id });
            }
        }

        // POST: Feature/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, FeatureEditViewModel viewModel)
        {
            if (id != viewModel.Id) return NotFound();

            return await this.HandleUpdateAsync<FeatureEditViewModel, Feature>(
                id,
                viewModel,
                async (featureId) => await _agileService.GetFeatureByIdAsync(featureId),
                async (feature) => {
                    var updatedFeature = await _agileService.UpdateFeatureAsync(feature);
                    await LogAuditAsync(AuditAction.Update, "Feature", feature.Id);
                    return updatedFeature;
                },
                async () => await PopulateDropdowns(viewModel.ProjectId),
                _formHelper,
                _logger,
                "Feature",
                "Details"
            );
        }

        #endregion

        #region Delete

        // GET: Feature/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var feature = await _agileService.GetFeatureByIdAsync(id);
                if (feature == null) return NotFound();

                return View(feature);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading delete confirmation for feature {FeatureId}", id);
                TempData["Error"] = "An error occurred while loading the delete confirmation.";
                return RedirectToAction("Details", new { id });
            }
        }

        // POST: Feature/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var feature = await _agileService.GetFeatureByIdAsync(id);
                if (feature == null) return NotFound();

                var projectId = feature.ProjectId;
                await _agileService.DeleteFeatureAsync(id);
                await LogAuditAsync(AuditAction.Delete, "Feature", id);

                TempData["Success"] = "Feature deleted successfully.";
                return RedirectToAction("Index", new { projectId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting feature {FeatureId}", id);
                TempData["Error"] = "An error occurred while deleting the feature.";
                return RedirectToAction("Details", new { id });
            }
        }

        #endregion

        #region Helper Methods

        private async Task PopulateDropdowns(int projectId)
        {
            var epics = await _agileService.GetProjectEpicsAsync(projectId);
            ViewBag.EpicId = new SelectList(epics, "Id", "Title");

            // Add priority and status dropdowns
            ViewBag.Priority = new SelectList(Enum.GetValues<FeaturePriority>());
            ViewBag.Status = new SelectList(Enum.GetValues<FeatureStatus>());
        }

        #endregion
    }
}
