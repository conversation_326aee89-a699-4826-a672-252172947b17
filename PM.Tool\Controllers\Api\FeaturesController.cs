using Microsoft.AspNetCore.Mvc;
using PM.Tool.Core.Entities.Agile;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Enums;
using PM.Tool.Models.Api;
using PM.Tool.Models.DTOs;
using System.ComponentModel.DataAnnotations;
using Asp.Versioning;

namespace PM.Tool.Controllers.Api
{
    /// <summary>
    /// API controller for managing features
    /// </summary>
    [ApiVersion("1.0")]
    [Tags("Features")]
    public class FeaturesController : BaseApiController
    {
        private readonly IAgileService _agileService;
        private readonly IProjectService _projectService;

        public FeaturesController(
            IAgileService agileService,
            IProjectService projectService,
            IAuditService auditService,
            ILogger<FeaturesController> logger)
            : base(auditService, logger)
        {
            _agileService = agileService;
            _projectService = projectService;
        }

        /// <summary>
        /// Get all features for a project
        /// </summary>
        /// <param name="projectId">Project ID</param>
        /// <param name="request">Query parameters</param>
        /// <returns>Paginated list of features</returns>
        [HttpGet("projects/{projectId:int}/features")]
        [ProducesResponseType(typeof(ApiPagedResponse<FeatureSummaryDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetProjectFeatures(
            [FromRoute] int projectId,
            [FromQuery] FeatureQueryRequest request)
        {
            try
            {
                var validationResult = ValidateModelState();
                if (validationResult != null) return validationResult;

                // Check project access
                if (!await HasProjectAccessAsync(projectId, _projectService))
                {
                    return NotFound(NotFoundError("Project", projectId));
                }

                var features = await _agileService.GetProjectFeaturesAsync(projectId);

                // Apply filters
                if (request.EpicId.HasValue)
                    features = features.Where(f => f.EpicId == request.EpicId.Value);

                if (request.Status.HasValue)
                    features = features.Where(f => f.Status == request.Status.Value);

                if (request.Priority.HasValue)
                    features = features.Where(f => f.Priority == request.Priority.Value);

                if (!string.IsNullOrEmpty(request.Search))
                {
                    var searchTerm = request.Search.ToLower();
                    features = features.Where(f =>
                        f.Title.ToLower().Contains(searchTerm) ||
                        f.Description.ToLower().Contains(searchTerm) ||
                        f.FeatureKey.ToLower().Contains(searchTerm));
                }

                // Apply sorting
                features = request.SortBy?.ToLower() switch
                {
                    "title" => request.SortOrder == "desc" ? features.OrderByDescending(f => f.Title) : features.OrderBy(f => f.Title),
                    "status" => request.SortOrder == "desc" ? features.OrderByDescending(f => f.Status) : features.OrderBy(f => f.Status),
                    "priority" => request.SortOrder == "desc" ? features.OrderByDescending(f => f.Priority) : features.OrderBy(f => f.Priority),
                    "progress" => request.SortOrder == "desc" ? features.OrderByDescending(f => f.ProgressPercentage) : features.OrderBy(f => f.ProgressPercentage),
                    "targetdate" => request.SortOrder == "desc" ? features.OrderByDescending(f => f.TargetDate) : features.OrderBy(f => f.TargetDate),
                    _ => features.OrderBy(f => f.SortOrder).ThenBy(f => f.Title)
                };

                var totalCount = features.Count();
                var pagedFeatures = features
                    .Skip((request.Page - 1) * request.PageSize)
                    .Take(request.PageSize);

                var featureDtos = pagedFeatures.Select(f => new FeatureSummaryDto
                {
                    Id = f.Id,
                    FeatureKey = f.FeatureKey,
                    Title = f.Title,
                    Status = f.Status,
                    Priority = f.Priority,
                    ProgressPercentage = (decimal)f.ProgressPercentage,
                    UserStoryCount = f.UserStoryCount,
                    CompletedUserStoryCount = f.CompletedUserStoryCount,
                    EpicTitle = f.Epic?.Title ?? string.Empty,
                    OwnerName = f.Owner?.UserName,
                    TargetDate = f.TargetDate
                });

                return Ok(PagedSuccess(featureDtos, totalCount, request.Page, request.PageSize));
            }
            catch (Exception ex)
            {
                return HandleException(ex, $"GetProjectFeatures for project {projectId}");
            }
        }

        /// <summary>
        /// Get a specific feature by ID
        /// </summary>
        /// <param name="id">Feature ID</param>
        /// <returns>Feature details</returns>
        [HttpGet("{id:int}")]
        [ProducesResponseType(typeof(ApiResponse<FeatureDetailDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetFeature([FromRoute] int id)
        {
            try
            {
                var feature = await _agileService.GetFeatureByIdAsync(id);
                if (feature == null)
                {
                    return NotFound(NotFoundError("Feature", id));
                }

                // Check project access
                if (!await HasProjectAccessAsync(feature.ProjectId, _projectService))
                {
                    return NotFound(NotFoundError("Feature", id));
                }

                var featureDto = new FeatureDetailDto
                {
                    Id = feature.Id,
                    FeatureKey = feature.FeatureKey,
                    Title = feature.Title,
                    Description = feature.Description,
                    BusinessValue = feature.BusinessValue,
                    AcceptanceCriteria = feature.AcceptanceCriteria,
                    Status = feature.Status,
                    Priority = feature.Priority,
                    EstimatedStoryPoints = (int)feature.EstimatedStoryPoints,
                    ActualStoryPoints = (int)feature.ActualStoryPoints,
                    ProgressPercentage = (decimal)feature.ProgressPercentage,
                    TargetDate = feature.TargetDate,
                    Tags = feature.Tags,
                    SortOrder = feature.SortOrder,
                    ProjectId = feature.ProjectId,
                    EpicId = feature.EpicId,
                    EpicTitle = feature.Epic?.Title,
                    OwnerName = feature.Owner?.UserName,
                    UserStoryCount = feature.UserStoryCount,
                    CompletedUserStoryCount = feature.CompletedUserStoryCount,
                    CreatedAt = feature.CreatedAt,
                    UpdatedAt = feature.UpdatedAt
                };

                return Ok(Success(featureDto));
            }
            catch (Exception ex)
            {
                return HandleException(ex, $"GetFeature {id}");
            }
        }

        /// <summary>
        /// Create a new feature
        /// </summary>
        /// <param name="request">Feature creation data</param>
        /// <returns>Created feature</returns>
        [HttpPost]
        [ProducesResponseType(typeof(ApiResponse<FeatureDetailDto>), StatusCodes.Status201Created)]
        [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> CreateFeature([FromBody] CreateFeatureRequest request)
        {
            try
            {
                var validationResult = ValidateModelState();
                if (validationResult != null) return validationResult;

                // Check project access
                if (!await HasProjectAccessAsync(request.ProjectId, _projectService))
                {
                    return NotFound(NotFoundError("Project", request.ProjectId));
                }

                // Generate feature key
                var existingFeatures = await _agileService.GetProjectFeaturesAsync(request.ProjectId);
                var featureKey = $"FT-{existingFeatures.Count() + 1:D3}";

                var feature = new Feature
                {
                    FeatureKey = featureKey,
                    Title = request.Title,
                    Description = request.Description,
                    BusinessValue = request.BusinessValue,
                    AcceptanceCriteria = request.AcceptanceCriteria,
                    Status = request.Status,
                    Priority = request.Priority,
                    EstimatedStoryPoints = request.EstimatedStoryPoints,
                    TargetDate = request.TargetDate,
                    Tags = request.Tags,
                    SortOrder = request.SortOrder,
                    ProjectId = request.ProjectId,
                    EpicId = request.EpicId ?? 0,
                    OwnerId = CurrentUserId
                };

                var createdFeature = await _agileService.CreateFeatureAsync(feature);
                await LogAuditAsync(AuditAction.Create, "Feature", createdFeature.Id);

                var featureDto = new FeatureDetailDto
                {
                    Id = createdFeature.Id,
                    FeatureKey = createdFeature.FeatureKey,
                    Title = createdFeature.Title,
                    Description = createdFeature.Description,
                    BusinessValue = createdFeature.BusinessValue,
                    AcceptanceCriteria = createdFeature.AcceptanceCriteria,
                    Status = createdFeature.Status,
                    Priority = createdFeature.Priority,
                    EstimatedStoryPoints = (int)createdFeature.EstimatedStoryPoints,
                    ActualStoryPoints = (int)createdFeature.ActualStoryPoints,
                    ProgressPercentage = (decimal)createdFeature.ProgressPercentage,
                    TargetDate = createdFeature.TargetDate,
                    Tags = createdFeature.Tags,
                    SortOrder = createdFeature.SortOrder,
                    ProjectId = createdFeature.ProjectId,
                    EpicId = createdFeature.EpicId,
                    CreatedAt = createdFeature.CreatedAt,
                    UpdatedAt = createdFeature.UpdatedAt
                };

                return CreatedWithLocation(nameof(GetFeature), new { id = createdFeature.Id }, featureDto, "Feature created successfully");
            }
            catch (Exception ex)
            {
                return HandleException(ex, "CreateFeature");
            }
        }

        /// <summary>
        /// Update an existing feature
        /// </summary>
        /// <param name="id">Feature ID</param>
        /// <param name="request">Feature update data</param>
        /// <returns>Updated feature</returns>
        [HttpPut("{id:int}")]
        [ProducesResponseType(typeof(ApiResponse<FeatureDetailDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateFeature([FromRoute] int id, [FromBody] UpdateFeatureRequest request)
        {
            try
            {
                if (id != request.Id)
                {
                    return BadRequest(ValidationError("Route ID does not match request ID"));
                }

                var validationResult = ValidateModelState();
                if (validationResult != null) return validationResult;

                var existingFeature = await _agileService.GetFeatureByIdAsync(id);
                if (existingFeature == null)
                {
                    return NotFound(NotFoundError("Feature", id));
                }

                // Check project access
                if (!await HasProjectAccessAsync(existingFeature.ProjectId, _projectService))
                {
                    return NotFound(NotFoundError("Feature", id));
                }

                // Update properties
                existingFeature.Title = request.Title;
                existingFeature.Description = request.Description;
                existingFeature.BusinessValue = request.BusinessValue;
                existingFeature.AcceptanceCriteria = request.AcceptanceCriteria;
                existingFeature.Status = request.Status;
                existingFeature.Priority = request.Priority;
                existingFeature.EstimatedStoryPoints = request.EstimatedStoryPoints;
                existingFeature.TargetDate = request.TargetDate;
                existingFeature.Tags = request.Tags;
                existingFeature.SortOrder = request.SortOrder;
                existingFeature.EpicId = request.EpicId ?? 0;

                var updatedFeature = await _agileService.UpdateFeatureAsync(existingFeature);
                await LogAuditAsync(AuditAction.Update, "Feature", id);

                var featureDto = new FeatureDetailDto
                {
                    Id = updatedFeature.Id,
                    FeatureKey = updatedFeature.FeatureKey,
                    Title = updatedFeature.Title,
                    Description = updatedFeature.Description,
                    BusinessValue = updatedFeature.BusinessValue,
                    AcceptanceCriteria = updatedFeature.AcceptanceCriteria,
                    Status = updatedFeature.Status,
                    Priority = updatedFeature.Priority,
                    EstimatedStoryPoints = (int)updatedFeature.EstimatedStoryPoints,
                    ActualStoryPoints = (int)updatedFeature.ActualStoryPoints,
                    ProgressPercentage = (decimal)updatedFeature.ProgressPercentage,
                    TargetDate = updatedFeature.TargetDate,
                    Tags = updatedFeature.Tags,
                    SortOrder = updatedFeature.SortOrder,
                    ProjectId = updatedFeature.ProjectId,
                    EpicId = updatedFeature.EpicId,
                    CreatedAt = updatedFeature.CreatedAt,
                    UpdatedAt = updatedFeature.UpdatedAt
                };

                return Ok(Success(featureDto, "Feature updated successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex, $"UpdateFeature {id}");
            }
        }

        /// <summary>
        /// Delete a feature
        /// </summary>
        /// <param name="id">Feature ID</param>
        /// <returns>Success confirmation</returns>
        [HttpDelete("{id:int}")]
        [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteFeature([FromRoute] int id)
        {
            try
            {
                var feature = await _agileService.GetFeatureByIdAsync(id);
                if (feature == null)
                {
                    return NotFound(NotFoundError("Feature", id));
                }

                // Check project access
                if (!await HasProjectAccessAsync(feature.ProjectId, _projectService))
                {
                    return NotFound(NotFoundError("Feature", id));
                }

                await _agileService.DeleteFeatureAsync(id);
                await LogAuditAsync(AuditAction.Delete, "Feature", id);

                return Ok(Success("Feature deleted successfully"));
            }
            catch (Exception ex)
            {
                return HandleException(ex, $"DeleteFeature {id}");
            }
        }

        /// <summary>
        /// Get user stories for a feature
        /// </summary>
        /// <param name="id">Feature ID</param>
        /// <returns>List of user stories</returns>
        [HttpGet("{id:int}/user-stories")]
        [ProducesResponseType(typeof(ApiResponse<IEnumerable<object>>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetFeatureUserStories([FromRoute] int id)
        {
            try
            {
                var feature = await _agileService.GetFeatureByIdAsync(id);
                if (feature == null)
                {
                    return NotFound(NotFoundError("Feature", id));
                }

                // Check project access
                if (!await HasProjectAccessAsync(feature.ProjectId, _projectService))
                {
                    return NotFound(NotFoundError("Feature", id));
                }

                var userStories = feature.UserStories.Select(us => new
                {
                    us.Id,
                    us.StoryKey,
                    us.Title,
                    us.Status,
                    us.StoryPoints,
                    us.Priority
                });

                return Ok(Success(userStories));
            }
            catch (Exception ex)
            {
                return HandleException(ex, $"GetFeatureUserStories {id}");
            }
        }
    }

    /// <summary>
    /// Request model for querying features
    /// </summary>
    public class FeatureQueryRequest : BaseApiRequest
    {
        /// <summary>
        /// Filter by epic ID
        /// </summary>
        public int? EpicId { get; set; }

        /// <summary>
        /// Filter by feature status
        /// </summary>
        public FeatureStatus? Status { get; set; }

        /// <summary>
        /// Filter by feature priority
        /// </summary>
        public FeaturePriority? Priority { get; set; }
    }

    /// <summary>
    /// Request model for creating a feature
    /// </summary>
    public class CreateFeatureRequest : CreateApiRequest
    {
        /// <summary>
        /// Feature title
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Feature description
        /// </summary>
        [Required]
        [StringLength(2000)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Business value description
        /// </summary>
        [StringLength(1000)]
        public string? BusinessValue { get; set; }

        /// <summary>
        /// Acceptance criteria
        /// </summary>
        [StringLength(2000)]
        public string? AcceptanceCriteria { get; set; }

        /// <summary>
        /// Feature status
        /// </summary>
        [Required]
        public FeatureStatus Status { get; set; }

        /// <summary>
        /// Feature priority
        /// </summary>
        [Required]
        public FeaturePriority Priority { get; set; }

        /// <summary>
        /// Estimated story points
        /// </summary>
        [Range(0, int.MaxValue)]
        public int EstimatedStoryPoints { get; set; }

        /// <summary>
        /// Target completion date
        /// </summary>
        public DateTime? TargetDate { get; set; }

        /// <summary>
        /// Tags (comma-separated)
        /// </summary>
        [StringLength(500)]
        public string? Tags { get; set; }

        /// <summary>
        /// Sort order
        /// </summary>
        [Range(0, int.MaxValue)]
        public int SortOrder { get; set; }

        /// <summary>
        /// Project ID
        /// </summary>
        [Required]
        public int ProjectId { get; set; }

        /// <summary>
        /// Epic ID (optional)
        /// </summary>
        public int? EpicId { get; set; }
    }

    /// <summary>
    /// Request model for updating a feature
    /// </summary>
    public class UpdateFeatureRequest : UpdateApiRequest
    {
        /// <summary>
        /// Feature title
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Feature description
        /// </summary>
        [Required]
        [StringLength(2000)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Business value description
        /// </summary>
        [StringLength(1000)]
        public string? BusinessValue { get; set; }

        /// <summary>
        /// Acceptance criteria
        /// </summary>
        [StringLength(2000)]
        public string? AcceptanceCriteria { get; set; }

        /// <summary>
        /// Feature status
        /// </summary>
        [Required]
        public FeatureStatus Status { get; set; }

        /// <summary>
        /// Feature priority
        /// </summary>
        [Required]
        public FeaturePriority Priority { get; set; }

        /// <summary>
        /// Estimated story points
        /// </summary>
        [Range(0, int.MaxValue)]
        public int EstimatedStoryPoints { get; set; }

        /// <summary>
        /// Target completion date
        /// </summary>
        public DateTime? TargetDate { get; set; }

        /// <summary>
        /// Tags (comma-separated)
        /// </summary>
        [StringLength(500)]
        public string? Tags { get; set; }

        /// <summary>
        /// Sort order
        /// </summary>
        [Range(0, int.MaxValue)]
        public int SortOrder { get; set; }

        /// <summary>
        /// Epic ID (optional)
        /// </summary>
        public int? EpicId { get; set; }
    }
}
