@model MyTasksStatsViewModel

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-8 gap-4 mb-6">
    <!-- Total Tasks -->
    <div class="stats-card card-custom p-4">
        <div class="flex items-center">
            <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                <i class="fas fa-tasks text-blue-600 dark:text-blue-400"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-neutral-500 dark:text-neutral-400">Total</p>
                <p class="text-2xl font-bold text-neutral-900 dark:text-white">@Model.TotalTasks</p>
            </div>
        </div>
    </div>

    <!-- To Do Tasks -->
    <div class="stats-card card-custom p-4">
        <div class="flex items-center">
            <div class="w-10 h-10 bg-gray-100 dark:bg-gray-900 rounded-lg flex items-center justify-center">
                <i class="fas fa-circle text-gray-600 dark:text-gray-400"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-neutral-500 dark:text-neutral-400">To Do</p>
                <p class="text-2xl font-bold text-neutral-900 dark:text-white">@Model.TodoTasks</p>
            </div>
        </div>
    </div>

    <!-- In Progress Tasks -->
    <div class="stats-card card-custom p-4">
        <div class="flex items-center">
            <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                <i class="fas fa-play-circle text-blue-600 dark:text-blue-400"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-neutral-500 dark:text-neutral-400">In Progress</p>
                <p class="text-2xl font-bold text-neutral-900 dark:text-white">@Model.InProgressTasks</p>
            </div>
        </div>
    </div>

    <!-- Completed Tasks -->
    <div class="stats-card card-custom p-4">
        <div class="flex items-center">
            <div class="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-neutral-500 dark:text-neutral-400">Completed</p>
                <p class="text-2xl font-bold text-neutral-900 dark:text-white">@Model.CompletedTasks</p>
            </div>
        </div>
    </div>

    <!-- Overdue Tasks -->
    <div class="stats-card card-custom p-4">
        <div class="flex items-center">
            <div class="w-10 h-10 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center">
                <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-neutral-500 dark:text-neutral-400">Overdue</p>
                <p class="text-2xl font-bold text-red-600 dark:text-red-400">@Model.OverdueTasks</p>
            </div>
        </div>
    </div>

    <!-- High Priority -->
    <div class="stats-card card-custom p-4">
        <div class="flex items-center">
            <div class="w-10 h-10 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                <i class="fas fa-flag text-orange-600 dark:text-orange-400"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-neutral-500 dark:text-neutral-400">High Priority</p>
                <p class="text-2xl font-bold text-orange-600 dark:text-orange-400">@Model.HighPriorityTasks</p>
            </div>
        </div>
    </div>

    <!-- Due Today -->
    <div class="stats-card card-custom p-4">
        <div class="flex items-center">
            <div class="w-10 h-10 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center">
                <i class="fas fa-clock text-yellow-600 dark:text-yellow-400"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-neutral-500 dark:text-neutral-400">Due Today</p>
                <p class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">@Model.DueTodayTasks</p>
            </div>
        </div>
    </div>

    <!-- Completion Rate -->
    <div class="stats-card card-custom p-4">
        <div class="flex items-center">
            <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                <i class="fas fa-chart-line text-purple-600 dark:text-purple-400"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-neutral-500 dark:text-neutral-400">Completion Rate</p>
                <p class="text-2xl font-bold text-purple-600 dark:text-purple-400">@Model.CompletionRate.ToString("F1")%</p>
            </div>
        </div>
    </div>
</div>
