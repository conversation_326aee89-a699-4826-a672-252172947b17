@*
    Card Component - Usage Examples:

    1. Simple card with title and icon:
    @{
        ViewData["Title"] = "Card Title";
        ViewData["Icon"] = "fas fa-icon";
        ViewData["BodyContent"] = "<p>Card content here</p>";
    }
    <partial name="Components/_Card" view-data="ViewData" />

    2. Card with header actions:
    @{
        ViewData["Title"] = "Card Title";
        ViewData["HeaderActions"] = "<button class='btn-primary-custom'>Action</button>";
        ViewData["BodyContent"] = "<p>Card content here</p>";
    }
    <partial name="Components/_Card" view-data="ViewData" />
*@

@{
    var title = ViewData["Title"]?.ToString();
    var subtitle = ViewData["Subtitle"]?.ToString();
    var icon = ViewData["Icon"]?.ToString();
    var headerActions = ViewData["HeaderActions"]?.ToString();
    var footerContent = ViewData["FooterContent"]?.ToString();
    var bodyContent = ViewData["BodyContent"]?.ToString();
    var showHeader = ViewData["ShowHeader"] as bool? ?? !string.IsNullOrEmpty(title);
    var showFooter = ViewData["ShowFooter"] as bool? ?? !string.IsNullOrEmpty(footerContent);
    var cardClass = ViewData["CardClass"]?.ToString() ?? "";
    var headerClass = ViewData["HeaderClass"]?.ToString() ?? "";
    var bodyClass = ViewData["BodyClass"]?.ToString() ?? "";
    var footerClass = ViewData["FooterClass"]?.ToString() ?? "";
}

<div class="card-custom @cardClass">
    @if (showHeader)
    {
        <div class="card-header-custom @headerClass">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    @if (!string.IsNullOrEmpty(icon))
                    {
                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                            <i class="@icon text-primary-600 dark:text-primary-400 text-sm"></i>
                        </div>
                    }
                    <div>
                        @if (!string.IsNullOrEmpty(title))
                        {
                            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">@title</h3>
                        }
                        @if (!string.IsNullOrEmpty(subtitle))
                        {
                            <p class="text-sm text-neutral-500 dark:text-dark-400">@subtitle</p>
                        }
                    </div>
                </div>

                @if (!string.IsNullOrEmpty(headerActions))
                {
                    <div class="flex items-center space-x-2">
                        @Html.Raw(headerActions)
                    </div>
                }
            </div>
        </div>
    }

    <div class="card-body-custom @bodyClass">
        @if (!string.IsNullOrEmpty(bodyContent))
        {
            @Html.Raw(bodyContent)
        }
    </div>

    @if (showFooter)
    {
        <div class="card-footer-custom @footerClass">
            @Html.Raw(footerContent)
        </div>
    }
</div>
