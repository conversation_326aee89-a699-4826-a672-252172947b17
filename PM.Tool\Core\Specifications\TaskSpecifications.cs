using System.Linq.Expressions;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using TaskStatus = PM.Tool.Core.Enums.TaskStatus;

namespace PM.Tool.Core.Specifications
{
    public class TasksByProjectSpec : BaseSpecification<TaskEntity>
    {
        public TasksByProjectSpec(int projectId, bool includeCompleted = false) 
            : base(t => t.ProjectId == projectId && (!t.IsDeleted && (includeCompleted || t.Status != TaskStatus.Done)))
        {
            AddInclude(t => t.AssignedTo);
            AddInclude(t => t.CreatedBy);
            AddInclude(t => t.Project);
            ApplyOrderByDescending(t => t.CreatedAt);
        }
    }

    public class TasksByUserSpec : BaseSpecification<TaskEntity>
    {
        public TasksByUserSpec(string userId, bool includeCompleted = false)
            : base(t => t.AssignedToUserId == userId && (!t.IsDeleted && (includeCompleted || t.Status != TaskStatus.Done)))
        {
            AddInclude(t => t.Project);
            AddInclude(t => t.CreatedBy);
            ApplyOrderByDescending(t => t.DueDate);
        }
    }

    public class OverdueTasksSpec : BaseSpecification<TaskEntity>
    {
        public OverdueTasksSpec()
            : base(t => !t.IsDeleted && t.Status != TaskStatus.Done && t.DueDate.HasValue && t.DueDate < DateTime.UtcNow)
        {
            AddInclude(t => t.Project);
            AddInclude(t => t.AssignedTo);
            ApplyOrderBy(t => t.DueDate);
        }
    }    public class TaskWithFullDetailsSpec : BaseSpecification<TaskEntity>
    {
        public TaskWithFullDetailsSpec(int taskId)
            : base(t => t.Id == taskId)
        {
            AddInclude(t => t.Project);
            AddInclude(t => t.AssignedTo);
            AddInclude(t => t.CreatedBy);
            AddInclude(t => t.Comments);
            AddInclude(t => t.Attachments);
            AddInclude(t => t.SubTasks);
        }
    }

    public class TaskSpecifications : BaseSpecification<TaskEntity>
    {
        public static Expression<Func<TaskEntity, bool>> HasStatus(TaskStatus status)
        {
            return t => t.Status == status;
        }

        public static Expression<Func<TaskEntity, bool>> IsAssignedTo(string userId)
        {
            return t => t.AssignedToUserId == userId;
        }
    }
}
