using PM.Tool.Core.Entities;

namespace PM.Tool.Core.Interfaces
{
    public interface IRiskService
    {
        // Risk Management
        Task<IEnumerable<Risk>> GetAllRisksAsync();
        Task<IEnumerable<Risk>> GetProjectRisksAsync(int projectId);
        Task<IEnumerable<Risk>> GetTaskRisksAsync(int taskId);
        Task<Risk?> GetRiskByIdAsync(int id);
        Task<Risk> CreateRiskAsync(Risk risk);
        Task<Risk> UpdateRiskAsync(Risk risk);
        Task<bool> DeleteRiskAsync(int id);
        
        // Risk Assessment
        Task<IEnumerable<Risk>> GetRisksByLevelAsync(RiskLevel level);
        Task<IEnumerable<Risk>> GetOverdueRisksAsync();
        Task<IEnumerable<Risk>> GetRisksByStatusAsync(RiskStatus status);
        Task<IEnumerable<Risk>> GetRisksByCategoryAsync(RiskCategory category);
        
        // Risk Mitigation
        Task<IEnumerable<RiskMitigationAction>> GetRiskMitigationActionsAsync(int riskId);
        Task<RiskMitigationAction> CreateMitigationActionAsync(RiskMitigationAction action);
        Task<RiskMitigationAction> UpdateMitigationActionAsync(RiskMitigationAction action);
        Task<bool> DeleteMitigationActionAsync(int id);
        Task<bool> CompleteMitigationActionAsync(int id);
        
        // Risk Analytics
        Task<Dictionary<RiskCategory, int>> GetRiskDistributionByCategoryAsync(int projectId);
        Task<Dictionary<RiskLevel, int>> GetRiskDistributionByLevelAsync(int projectId);
        Task<decimal> GetProjectRiskScoreAsync(int projectId);
        Task<IEnumerable<Risk>> GetTopRisksByScoreAsync(int projectId, int count = 10);
        
        // Risk Monitoring
        Task<IEnumerable<Risk>> GetRisksRequiringAttentionAsync(int projectId);
        Task<bool> UpdateRiskStatusAsync(int riskId, RiskStatus status);
        Task<IEnumerable<Risk>> GetRisksDueForReviewAsync(DateTime reviewDate);
    }
}
