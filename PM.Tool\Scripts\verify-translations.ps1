# PowerShell script to verify all resource files have complete translations
# This script checks for missing keys and reports the status of all translations

$resourcesPath = "PM.Tool/Resources"
$templateFile = "$resourcesPath/SharedResources.resx"

Write-Host "Verifying translation completeness..." -ForegroundColor Green

# Get the main resource file content to extract all keys
if (-not (Test-Path $templateFile)) {
    Write-Host "Template file not found: $templateFile" -ForegroundColor Red
    exit 1
}

[xml]$mainContent = Get-Content $templateFile -Encoding UTF8

# Extract all data keys from the main file
$allKeys = $mainContent.root.data | ForEach-Object { $_.name }
$totalKeys = $allKeys.Count

Write-Host "Found $totalKeys keys in main resource file" -ForegroundColor Cyan
Write-Host ""

# Get all culture files
$cultureFiles = Get-ChildItem "$resourcesPath/SharedResources.*.resx" | Where-Object { $_.Name -ne "SharedResources.resx" }

$results = @()

foreach ($cultureFile in $cultureFiles) {
    $cultureName = $cultureFile.BaseName -replace "SharedResources\.", ""
    
    try {
        [xml]$cultureContent = Get-Content $cultureFile.FullName -Encoding UTF8
        
        # Get existing keys in this culture file
        $existingKeys = @()
        if ($cultureContent.root.data) {
            $existingKeys = $cultureContent.root.data | ForEach-Object { $_.name }
        }
        
        $missingKeys = $allKeys | Where-Object { $_ -notin $existingKeys }
        $completionPercentage = [math]::Round((($totalKeys - $missingKeys.Count) / $totalKeys) * 100, 1)
        
        $status = if ($missingKeys.Count -eq 0) { "✅ Complete" } 
                 elseif ($missingKeys.Count -le 5) { "⚠️ Nearly Complete" }
                 else { "❌ Incomplete" }
        
        $results += [PSCustomObject]@{
            Culture = $cultureName
            TotalKeys = $totalKeys
            ExistingKeys = $existingKeys.Count
            MissingKeys = $missingKeys.Count
            CompletionPercentage = $completionPercentage
            Status = $status
        }
        
        Write-Host "Culture: $cultureName" -ForegroundColor Yellow
        Write-Host "  Total Keys: $totalKeys" -ForegroundColor White
        Write-Host "  Existing Keys: $($existingKeys.Count)" -ForegroundColor Green
        Write-Host "  Missing Keys: $($missingKeys.Count)" -ForegroundColor $(if ($missingKeys.Count -eq 0) { "Green" } else { "Red" })
        Write-Host "  Completion: $completionPercentage%" -ForegroundColor $(if ($completionPercentage -eq 100) { "Green" } elseif ($completionPercentage -ge 95) { "Yellow" } else { "Red" })
        Write-Host "  Status: $status" -ForegroundColor White
        
        if ($missingKeys.Count -gt 0 -and $missingKeys.Count -le 10) {
            Write-Host "  Missing Keys:" -ForegroundColor Red
            $missingKeys | ForEach-Object { Write-Host "    - $_" -ForegroundColor Red }
        } elseif ($missingKeys.Count -gt 10) {
            Write-Host "  Missing Keys: (showing first 10)" -ForegroundColor Red
            $missingKeys[0..9] | ForEach-Object { Write-Host "    - $_" -ForegroundColor Red }
            Write-Host "    ... and $($missingKeys.Count - 10) more" -ForegroundColor Red
        }
        
        Write-Host ""
    }
    catch {
        Write-Host "Error processing $($cultureFile.Name): $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
    }
}

# Summary report
Write-Host "=== SUMMARY REPORT ===" -ForegroundColor Cyan
Write-Host ""

$completeCount = ($results | Where-Object { $_.MissingKeys -eq 0 }).Count
$nearlyCompleteCount = ($results | Where-Object { $_.MissingKeys -gt 0 -and $_.MissingKeys -le 5 }).Count
$incompleteCount = ($results | Where-Object { $_.MissingKeys -gt 5 }).Count

Write-Host "Total Cultures: $($results.Count)" -ForegroundColor White
Write-Host "Complete (100%): $completeCount" -ForegroundColor Green
Write-Host "Nearly Complete (95%+): $nearlyCompleteCount" -ForegroundColor Yellow
Write-Host "Incomplete (<95%): $incompleteCount" -ForegroundColor Red
Write-Host ""

# Show top and bottom performers
$sortedResults = $results | Sort-Object CompletionPercentage -Descending

Write-Host "Top 5 Most Complete:" -ForegroundColor Green
$sortedResults[0..4] | ForEach-Object {
    Write-Host "  $($_.Culture): $($_.CompletionPercentage)% ($($_.Status))" -ForegroundColor Green
}
Write-Host ""

if ($incompleteCount -gt 0) {
    Write-Host "Cultures Needing Attention:" -ForegroundColor Red
    $sortedResults | Where-Object { $_.CompletionPercentage -lt 95 } | ForEach-Object {
        Write-Host "  $($_.Culture): $($_.CompletionPercentage)% (Missing: $($_.MissingKeys))" -ForegroundColor Red
    }
    Write-Host ""
}

# Check for Bengali (Bangladesh) specifically
$bengaliResult = $results | Where-Object { $_.Culture -eq "bn-BD" }
if ($bengaliResult) {
    Write-Host "Bengali (Bangladesh) Status:" -ForegroundColor Magenta
    Write-Host "  Completion: $($bengaliResult.CompletionPercentage)%" -ForegroundColor Magenta
    Write-Host "  Status: $($bengaliResult.Status)" -ForegroundColor Magenta
} else {
    Write-Host "Bengali (Bangladesh) resource file not found!" -ForegroundColor Red
}

Write-Host ""
Write-Host "Verification completed!" -ForegroundColor Green
