@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer Localizer
@model IEnumerable<PM.Tool.Core.Entities.Risk>
@{
    ViewData["Title"] = "Risk Analytics";
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Risk Management", Href = Url.Action("Index", "Risk"), Icon = "fas fa-exclamation-triangle" },
        new { Text = "Analytics", Href = "", Icon = "fas fa-chart-bar" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-chart-bar mr-3 text-primary-600 dark:text-primary-400"></i>
                Risk Analytics
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Comprehensive risk analysis and insights
            </p>
        </div>
        <div class="mt-4 sm:mt-0 flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
            @{
                ViewData["Text"] = "Export Report";
                ViewData["Variant"] = "outline";
                ViewData["Icon"] = "fas fa-download";
                ViewData["OnClick"] = "exportReport()";
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "Back to Risks";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Index");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Analytics Filters -->
@{
    ViewData["Title"] = "Analytics Filters";
    ViewData["Icon"] = "fas fa-filter";
    ViewData["BodyContent"] = @"
        <div class='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div>
                <label for='projectFilter' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Project</label>
                <select id='projectFilter' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                    <option value=''>All Projects</option>
                </select>
            </div>
            <div>
                <label for='dateRange' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Date Range</label>
                <select id='dateRange' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                    <option value='30'>Last 30 Days</option>
                    <option value='90' selected>Last 90 Days</option>
                    <option value='180'>Last 6 Months</option>
                    <option value='365'>Last Year</option>
                    <option value='all'>All Time</option>
                </select>
            </div>
            <div>
                <label for='statusFilter' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Status</label>
                <select id='statusFilter' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                    <option value=''>All Statuses</option>
                    <option value='Identified'>Identified</option>
                    <option value='Analyzing'>Analyzing</option>
                    <option value='Mitigating'>Mitigating</option>
                    <option value='Resolved'>Resolved</option>
                </select>
            </div>
            <div class='flex items-end'>
                <button id='refreshAnalytics' class='w-full px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors'>
                    <i class='fas fa-sync-alt mr-2'></i>
                    Refresh
                </button>
            </div>
        </div>
    ";
}
<div class="mb-8">
    <partial name="Components/_Card" view-data="ViewData" />
</div>

<!-- Key Metrics -->
<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
    @{
        ViewData["Title"] = "Total Risks";
        ViewData["Icon"] = "fas fa-exclamation-triangle";
        ViewData["IconColor"] = "bg-gradient-to-br from-primary-500 to-primary-600";
        ViewData["Value"] = Model.Count().ToString();
        ViewData["Description"] = "All identified risks";
        ViewData["Id"] = "totalRisksCard";
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />

    @{
        ViewData["Title"] = "High/Critical";
        ViewData["Icon"] = "fas fa-fire";
        ViewData["IconColor"] = "bg-gradient-to-br from-danger-500 to-danger-600";
        ViewData["Value"] = Model.Count(r => r.RiskLevel == PM.Tool.Core.Entities.RiskLevel.High || r.RiskLevel == PM.Tool.Core.Entities.RiskLevel.Critical).ToString();
        ViewData["Description"] = "Require immediate attention";
        ViewData["Id"] = "highCriticalCard";
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />

    @{
        ViewData["Title"] = "Open Risks";
        ViewData["Icon"] = "fas fa-folder-open";
        ViewData["IconColor"] = "bg-gradient-to-br from-warning-500 to-warning-600";
        ViewData["Value"] = Model.Count(r => r.Status != PM.Tool.Core.Entities.RiskStatus.Resolved).ToString();
        ViewData["Description"] = "Active risks being managed";
        ViewData["Id"] = "openRisksCard";
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />

    @{
        ViewData["Title"] = "Avg Risk Score";
        ViewData["Icon"] = "fas fa-calculator";
        ViewData["IconColor"] = "bg-gradient-to-br from-info-500 to-info-600";
        ViewData["Value"] = Model.Any() ? Math.Round(Model.Average(r => (int)r.Probability * (int)r.Impact), 1).ToString() : "0";
        ViewData["Description"] = "Average risk impact";
        ViewData["Id"] = "avgScoreCard";
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />
</div>

<!-- Charts Row 1 -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Risk Distribution by Level -->
    @{
        ViewData["Title"] = "Risk Distribution by Level";
        ViewData["Icon"] = "fas fa-chart-pie";
        ViewData["BodyContent"] = @"
            <div class='h-80'>
                <canvas id='riskLevelChart'></canvas>
            </div>
        ";
    }
    <partial name="Components/_Card" view-data="ViewData" />

    <!-- Risk Status Distribution -->
    @{
        ViewData["Title"] = "Risk Status Distribution";
        ViewData["Icon"] = "fas fa-chart-donut";
        ViewData["BodyContent"] = @"
            <div class='h-80'>
                <canvas id='riskStatusChart'></canvas>
            </div>
        ";
    }
    <partial name="Components/_Card" view-data="ViewData" />
</div>

<!-- Charts Row 2 -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Risk Category Analysis -->
    @{
        ViewData["Title"] = "Risk Category Analysis";
        ViewData["Icon"] = "fas fa-chart-bar";
        ViewData["BodyContent"] = @"
            <div class='h-80'>
                <canvas id='riskCategoryChart'></canvas>
            </div>
        ";
    }
    <partial name="Components/_Card" view-data="ViewData" />

    <!-- Risk Trend Over Time -->
    @{
        ViewData["Title"] = "Risk Trend Over Time";
        ViewData["Icon"] = "fas fa-chart-line";
        ViewData["BodyContent"] = @"
            <div class='h-80'>
                <canvas id='riskTrendChart'></canvas>
            </div>
        ";
    }
    <partial name="Components/_Card" view-data="ViewData" />
</div>

<!-- Risk Heat Map -->
@{
    ViewData["Title"] = "Risk Heat Map (Probability vs Impact)";
    ViewData["Icon"] = "fas fa-fire";
    ViewData["BodyContent"] = @"
        <div class='h-96'>
            <canvas id='riskHeatMapChart'></canvas>
        </div>
    ";
}
<div class="mb-8">
    <partial name="Components/_Card" view-data="ViewData" />
</div>

<!-- Top Risks Table -->
@{
    ViewData["Title"] = "Top 10 Highest Risk Items";
    ViewData["Icon"] = "fas fa-list-ol";
    ViewData["BodyContent"] = @"
        <div class='overflow-x-auto'>
            <table class='min-w-full divide-y divide-neutral-200 dark:divide-dark-600'>
                <thead class='bg-neutral-50 dark:bg-dark-700'>
                    <tr>
                        <th class='px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wider'>Risk</th>
                        <th class='px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wider'>Score</th>
                        <th class='px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wider'>Level</th>
                        <th class='px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wider'>Status</th>
                        <th class='px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wider'>Category</th>
                        <th class='px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wider'>Actions</th>
                    </tr>
                </thead>
                <tbody id='topRisksTable' class='bg-white dark:bg-dark-800 divide-y divide-neutral-200 dark:divide-dark-600'>
                    <!-- Table content will be populated by JavaScript -->
                </tbody>
            </table>
        </div>
    ";
}
<partial name="Components/_Card" view-data="ViewData" />

@section Scripts {
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        let allRisks = @Html.Raw(Json.Serialize(Model.Select(r => new {
            Id = r.Id,
            Title = r.Title,
            Description = r.Description,
            Probability = (int)r.Probability,
            Impact = (int)r.Impact,
            RiskScore = (int)r.Probability * (int)r.Impact,
            Status = r.Status.ToString(),
            Category = r.Category.ToString(),
            ProjectId = r.ProjectId,
            RiskLevel = r.RiskLevel.ToString(),
            CreatedAt = r.CreatedAt.ToString("yyyy-MM-dd"),
            TargetResolutionDate = r.TargetResolutionDate?.ToString("yyyy-MM-dd")
        })));

        let charts = {};

        $(document).ready(function() {
            loadProjects();
            setupFilters();
            initializeCharts();
            updateAnalytics();
            populateTopRisksTable();
        });

        function loadProjects() {
            $.get('@Url.Action("GetProjects", "Projects")')
                .done(function(data) {
                    const select = $('#projectFilter');
                    if (data && Array.isArray(data)) {
                        data.forEach(function(project) {
                            select.append(`<option value="${project.id}">${project.name}</option>`);
                        });
                    }
                })
                .fail(function() {
                    console.error('Failed to load projects');
                });
        }

        function setupFilters() {
            $('#projectFilter, #dateRange, #statusFilter').on('change', updateAnalytics);
            $('#refreshAnalytics').on('click', updateAnalytics);
        }

        function getFilteredRisks() {
            const projectFilter = $('#projectFilter').val();
            const dateRange = $('#dateRange').val();
            const statusFilter = $('#statusFilter').val();

            let filtered = allRisks.filter(risk => {
                if (projectFilter && risk.ProjectId != projectFilter) return false;
                if (statusFilter && risk.Status !== statusFilter) return false;

                if (dateRange !== 'all') {
                    const days = parseInt(dateRange);
                    const cutoffDate = new Date();
                    cutoffDate.setDate(cutoffDate.getDate() - days);
                    const riskDate = new Date(risk.CreatedAt);
                    if (riskDate < cutoffDate) return false;
                }

                return true;
            });

            return filtered;
        }

        function updateAnalytics() {
            const filteredRisks = getFilteredRisks();
            updateMetrics(filteredRisks);
            updateCharts(filteredRisks);
            populateTopRisksTable(filteredRisks);
        }

        function updateMetrics(risks) {
            $('#totalRisksCard .text-2xl').text(risks.length);

            const highCritical = risks.filter(r => r.RiskLevel === 'High' || r.RiskLevel === 'Critical').length;
            $('#highCriticalCard .text-2xl').text(highCritical);

            const openRisks = risks.filter(r => r.Status !== 'Resolved').length;
            $('#openRisksCard .text-2xl').text(openRisks);

            const avgScore = risks.length > 0 ? (risks.reduce((sum, r) => sum + r.RiskScore, 0) / risks.length).toFixed(1) : '0';
            $('#avgScoreCard .text-2xl').text(avgScore);
        }

        function initializeCharts() {
            // Risk Level Chart
            const riskLevelCtx = document.getElementById('riskLevelChart').getContext('2d');
            charts.riskLevel = new Chart(riskLevelCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Low', 'Medium', 'High', 'Critical'],
                    datasets: [{
                        data: [0, 0, 0, 0],
                        backgroundColor: ['#10b981', '#f59e0b', '#f97316', '#ef4444'],
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Risk Status Chart
            const riskStatusCtx = document.getElementById('riskStatusChart').getContext('2d');
            charts.riskStatus = new Chart(riskStatusCtx, {
                type: 'pie',
                data: {
                    labels: ['Identified', 'Analyzing', 'Mitigating', 'Resolved'],
                    datasets: [{
                        data: [0, 0, 0, 0],
                        backgroundColor: ['#6b7280', '#3b82f6', '#f59e0b', '#10b981'],
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Risk Category Chart
            const riskCategoryCtx = document.getElementById('riskCategoryChart').getContext('2d');
            charts.riskCategory = new Chart(riskCategoryCtx, {
                type: 'bar',
                data: {
                    labels: ['Technical', 'Business', 'External', 'Organizational', 'Project Management'],
                    datasets: [{
                        label: 'Number of Risks',
                        data: [0, 0, 0, 0, 0],
                        backgroundColor: '#3b82f6',
                        borderColor: '#1d4ed8',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });

            // Risk Trend Chart
            const riskTrendCtx = document.getElementById('riskTrendChart').getContext('2d');
            charts.riskTrend = new Chart(riskTrendCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'New Risks',
                        data: [],
                        borderColor: '#ef4444',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Resolved Risks',
                        data: [],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });

            // Risk Heat Map Chart
            const riskHeatMapCtx = document.getElementById('riskHeatMapChart').getContext('2d');
            charts.riskHeatMap = new Chart(riskHeatMapCtx, {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: 'Risks',
                        data: [],
                        backgroundColor: function(context) {
                            if (context.parsed) {
                                const score = context.parsed.x * context.parsed.y;
                                if (score >= 20) return '#ef4444';
                                if (score >= 15) return '#f97316';
                                if (score >= 10) return '#f59e0b';
                                return '#10b981';
                            }
                            return '#10b981';
                        },
                        borderColor: '#ffffff',
                        borderWidth: 2,
                        pointRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Probability'
                            },
                            min: 0,
                            max: 6,
                            ticks: {
                                stepSize: 1
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Impact'
                            },
                            min: 0,
                            max: 6,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const risk = context.raw.risk;
                                    return `${risk.Title} (Score: ${risk.RiskScore})`;
                                }
                            }
                        }
                    }
                }
            });
        }

        function updateCharts(risks) {
            // Update Risk Level Chart
            const levelCounts = {
                'Low': risks.filter(r => r.RiskLevel === 'Low').length,
                'Medium': risks.filter(r => r.RiskLevel === 'Medium').length,
                'High': risks.filter(r => r.RiskLevel === 'High').length,
                'Critical': risks.filter(r => r.RiskLevel === 'Critical').length
            };
            charts.riskLevel.data.datasets[0].data = Object.values(levelCounts);
            charts.riskLevel.update();

            // Update Risk Status Chart
            const statusCounts = {
                'Identified': risks.filter(r => r.Status === 'Identified').length,
                'Analyzing': risks.filter(r => r.Status === 'Analyzing').length,
                'Mitigating': risks.filter(r => r.Status === 'Mitigating').length,
                'Resolved': risks.filter(r => r.Status === 'Resolved').length
            };
            charts.riskStatus.data.datasets[0].data = Object.values(statusCounts);
            charts.riskStatus.update();

            // Update Risk Category Chart
            const categoryCounts = {
                'Technical': risks.filter(r => r.Category === 'Technical').length,
                'Business': risks.filter(r => r.Category === 'Business').length,
                'External': risks.filter(r => r.Category === 'External').length,
                'Organizational': risks.filter(r => r.Category === 'Organizational').length,
                'ProjectManagement': risks.filter(r => r.Category === 'ProjectManagement').length
            };
            charts.riskCategory.data.datasets[0].data = Object.values(categoryCounts);
            charts.riskCategory.update();

            // Update Heat Map Chart
            const heatMapData = risks.map(risk => ({
                x: risk.Probability,
                y: risk.Impact,
                risk: risk
            }));
            charts.riskHeatMap.data.datasets[0].data = heatMapData;
            charts.riskHeatMap.update();

            // Update Trend Chart (simplified - would need more complex logic for real trend data)
            const last30Days = Array.from({length: 30}, (_, i) => {
                const date = new Date();
                date.setDate(date.getDate() - (29 - i));
                return date.toISOString().split('T')[0];
            });

            charts.riskTrend.data.labels = last30Days.map(date => new Date(date).toLocaleDateString());
            charts.riskTrend.data.datasets[0].data = last30Days.map(date =>
                risks.filter(r => r.CreatedAt === date).length
            );
            charts.riskTrend.data.datasets[1].data = last30Days.map(date =>
                risks.filter(r => r.Status === 'Resolved' && r.CreatedAt === date).length
            );
            charts.riskTrend.update();
        }

        function populateTopRisksTable(risks = null) {
            const risksToShow = risks || allRisks;
            const topRisks = risksToShow
                .sort((a, b) => b.RiskScore - a.RiskScore)
                .slice(0, 10);

            const tbody = $('#topRisksTable');
            tbody.empty();

            topRisks.forEach(risk => {
                const riskLevelClass = getRiskLevelClass(risk.RiskLevel);
                const statusClass = getStatusClass(risk.Status);

                tbody.append(`
                    <tr class="hover:bg-neutral-50 dark:hover:bg-dark-700">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-neutral-900 dark:text-dark-100">${risk.Title}</div>
                            <div class="text-sm text-neutral-500 dark:text-dark-400 truncate max-w-xs">${risk.Description}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-lg font-bold text-neutral-900 dark:text-dark-100">${risk.RiskScore}</div>
                            <div class="text-xs text-neutral-500 dark:text-dark-400">${risk.Probability}×${risk.Impact}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${riskLevelClass}">
                                ${risk.RiskLevel}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClass}">
                                ${risk.Status}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-neutral-900 dark:text-dark-100">
                            ${risk.Category}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="@Url.Action("Details", "Risk")/${risk.Id}" class="text-primary-600 dark:text-primary-400 hover:text-primary-900 dark:hover:text-primary-300">
                                View
                            </a>
                        </td>
                    </tr>
                `);
            });
        }

        function getRiskLevelClass(level) {
            switch(level) {
                case 'Critical': return 'bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200';
                case 'High': return 'bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200';
                case 'Medium': return 'bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200';
                case 'Low': return 'bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200';
                default: return 'bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200';
            }
        }

        function getStatusClass(status) {
            switch(status) {
                case 'Identified': return 'bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200';
                case 'Analyzing': return 'bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200';
                case 'Mitigating': return 'bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200';
                case 'Resolved': return 'bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200';
                default: return 'bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200';
            }
        }

        function exportReport() {
            // Implementation for exporting analytics report
            alert('Export functionality would be implemented here');
        }
    </script>
}
