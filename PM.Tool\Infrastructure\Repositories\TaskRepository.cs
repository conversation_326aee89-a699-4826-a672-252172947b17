using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Common;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Specifications;
using PM.Tool.Data;
using TaskStatus = PM.Tool.Core.Enums.TaskStatus;

namespace PM.Tool.Infrastructure.Repositories
{
    public class TaskRepository : Repository<TaskEntity>, ITaskRepository
    {
        public TaskRepository(ApplicationDbContext context) : base(context)
        {
        }

        public async Task<IEnumerable<TaskEntity>> GetTasksByProjectIdAsync(int projectId)
        {
            var spec = new TasksByProjectSpec(projectId);
            return await FindWithSpecificationAsync(spec);
        }

        public async Task<IEnumerable<TaskEntity>> GetTasksByUserIdAsync(string userId)
        {
            var spec = new TasksByUserSpec(userId);
            return await FindWithSpecificationAsync(spec);
        }

        public async Task<IEnumerable<TaskEntity>> GetTasksByStatusAsync(TaskStatus status)
        {
            var spec = new TasksByStatusSpec(status);
            return await FindWithSpecificationAsync(spec);
        }

        public async Task<IEnumerable<TaskEntity>> GetOverdueTasksAsync()
        {
            var spec = new OverdueTasksSpec();
            return await FindWithSpecificationAsync(spec);
        }

        public async Task<IEnumerable<TaskEntity>> GetTasksDueInDaysAsync(int days)
        {
            var dueDate = DateTime.UtcNow.AddDays(days);
            return await FindAsync(t => !t.IsDeleted && 
                                      t.Status != TaskStatus.Done && 
                                      t.DueDate.HasValue && 
                                      t.DueDate <= dueDate);
        }

        public async Task<TaskEntity?> GetTaskWithSubTasksAsync(int taskId)
        {
            var spec = new TaskWithFullDetailsSpec(taskId);
            return (await FindWithSpecificationAsync(spec)).FirstOrDefault();
        }

        public async Task<TaskEntity?> GetTaskWithCommentsAsync(int taskId)
        {
            var spec = new TaskWithFullDetailsSpec(taskId);
            return (await FindWithSpecificationAsync(spec)).FirstOrDefault();
        }

        public async Task<IEnumerable<TaskEntity>> GetSubTasksAsync(int parentTaskId)
        {
            return await FindAsync(t => !t.IsDeleted && t.ParentTaskId == parentTaskId);
        }

        public async Task<IEnumerable<TaskEntity>> GetTasksByPriorityAsync(TaskPriority priority)
        {
            return await FindAsync(t => !t.IsDeleted && t.Priority == priority);
        }
    }
}
