using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;

namespace PM.Tool.Application.Services
{
    public class WbsService : IWbsService
    {
        private readonly ApplicationDbContext _context;
        private readonly ITaskRepository _taskRepository;

        public WbsService(ApplicationDbContext context, ITaskRepository taskRepository)
        {
            _context = context;
            _taskRepository = taskRepository;
        }

        public async Task<bool> GenerateWbsCodesAsync(int projectId)
        {
            try
            {
                var tasks = await _context.Tasks
                    .Where(t => t.ProjectId == projectId && !t.IsDeleted)
                    .OrderBy(t => t.ParentTaskId ?? 0)
                    .ThenBy(t => t.SortOrder)
                    .ThenBy(t => t.CreatedAt)
                    .ToListAsync();

                await GenerateCodesRecursively(tasks, null, "");
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        private async Task GenerateCodesRecursively(List<TaskEntity> allTasks, int? parentId, string parentCode)
        {
            var childTasks = allTasks
                .Where(t => t.ParentTaskId == parentId)
                .OrderBy(t => t.SortOrder)
                .ThenBy(t => t.CreatedAt)
                .ToList();

            for (int i = 0; i < childTasks.Count; i++)
            {
                var task = childTasks[i];
                var code = string.IsNullOrEmpty(parentCode)
                    ? (i + 1).ToString()
                    : $"{parentCode}.{i + 1}";

                task.WbsCode = code;
                task.WbsLevel = string.IsNullOrEmpty(parentCode) ? 1 : parentCode.Split('.').Length + 1;
                task.SortOrder = i;

                await GenerateCodesRecursively(allTasks, task.Id, code);
            }
        }

        public async Task<string> GenerateWbsCodeAsync(int projectId, int? parentTaskId, int sortOrder)
        {
            if (!parentTaskId.HasValue)
            {
                // Root level task
                var rootTaskCount = await _context.Tasks
                    .CountAsync(t => t.ProjectId == projectId && !t.ParentTaskId.HasValue && !t.IsDeleted);
                return (rootTaskCount + 1).ToString();
            }

            var parentTask = await _context.Tasks
                .FirstOrDefaultAsync(t => t.Id == parentTaskId.Value && !t.IsDeleted);

            if (parentTask?.WbsCode == null)
            {
                await GenerateWbsCodesAsync(projectId);
                parentTask = await _context.Tasks
                    .FirstOrDefaultAsync(t => t.Id == parentTaskId.Value && !t.IsDeleted);
            }

            var siblingCount = await _context.Tasks
                .CountAsync(t => t.ParentTaskId == parentTaskId && !t.IsDeleted);

            return $"{parentTask?.WbsCode}.{siblingCount + 1}";
        }

        public async Task<bool> UpdateWbsCodesAsync(int projectId)
        {
            return await GenerateWbsCodesAsync(projectId);
        }

        public async Task<bool> MoveTaskAsync(int taskId, int? newParentTaskId, int newSortOrder)
        {
            try
            {
                var task = await _context.Tasks
                    .FirstOrDefaultAsync(t => t.Id == taskId && !t.IsDeleted);

                if (task == null) return false;

                // Prevent circular references
                if (newParentTaskId.HasValue && await IsCircularReference(taskId, newParentTaskId.Value))
                    return false;

                var oldParentId = task.ParentTaskId;
                task.ParentTaskId = newParentTaskId;
                task.SortOrder = newSortOrder;

                // Update sort orders for affected siblings
                await UpdateSortOrdersAfterMove(task.ProjectId, oldParentId, newParentTaskId, newSortOrder);

                // Regenerate WBS codes
                await GenerateWbsCodesAsync(task.ProjectId);

                return true;
            }
            catch
            {
                return false;
            }
        }

        private async Task<bool> IsCircularReference(int taskId, int potentialParentId)
        {
            int? currentParentId = potentialParentId;

            while (currentParentId.HasValue)
            {
                if (currentParentId == taskId) return true;

                var parent = await _context.Tasks
                    .FirstOrDefaultAsync(t => t.Id == currentParentId.Value && !t.IsDeleted);
                currentParentId = parent?.ParentTaskId;
            }

            return false;
        }

        private async Task UpdateSortOrdersAfterMove(int projectId, int? oldParentId, int? newParentId, int newSortOrder)
        {
            // Update old siblings
            if (oldParentId != newParentId)
            {
                var oldSiblings = await _context.Tasks
                    .Where(t => t.ProjectId == projectId && t.ParentTaskId == oldParentId && !t.IsDeleted)
                    .OrderBy(t => t.SortOrder)
                    .ToListAsync();

                for (int i = 0; i < oldSiblings.Count; i++)
                {
                    oldSiblings[i].SortOrder = i;
                }
            }

            // Update new siblings
            var newSiblings = await _context.Tasks
                .Where(t => t.ProjectId == projectId && t.ParentTaskId == newParentId && !t.IsDeleted)
                .OrderBy(t => t.SortOrder)
                .ToListAsync();

            for (int i = newSortOrder; i < newSiblings.Count; i++)
            {
                if (newSiblings[i].SortOrder >= newSortOrder)
                {
                    newSiblings[i].SortOrder++;
                }
            }

            await _context.SaveChangesAsync();
        }

        public async Task<IEnumerable<TaskEntity>> GetWbsStructureAsync(int projectId)
        {
            return await _context.Tasks
                .Include(t => t.AssignedTo)
                .Include(t => t.SubTasks)
                .Where(t => t.ProjectId == projectId && !t.IsDeleted)
                .OrderBy(t => t.WbsCode)
                .ToListAsync();
        }

        public async Task<bool> ValidateWbsStructureAsync(int projectId)
        {
            var tasks = await _context.Tasks
                .Where(t => t.ProjectId == projectId && !t.IsDeleted)
                .ToListAsync();

            // Check for duplicate WBS codes
            var duplicateCodes = tasks
                .Where(t => !string.IsNullOrEmpty(t.WbsCode))
                .GroupBy(t => t.WbsCode)
                .Where(g => g.Count() > 1)
                .Any();

            return !duplicateCodes;
        }

        public async Task<int> GetNextSortOrderAsync(int projectId, int? parentTaskId)
        {
            var maxSortOrder = await _context.Tasks
                .Where(t => t.ProjectId == projectId && t.ParentTaskId == parentTaskId && !t.IsDeleted)
                .MaxAsync(t => (int?)t.SortOrder) ?? -1;

            return maxSortOrder + 1;
        }

        public async Task<bool> ReorderTasksAsync(int projectId, int? parentTaskId, List<int> taskIds)
        {
            try
            {
                var tasks = await _context.Tasks
                    .Where(t => taskIds.Contains(t.Id) && t.ProjectId == projectId && !t.IsDeleted)
                    .ToListAsync();

                for (int i = 0; i < taskIds.Count; i++)
                {
                    var task = tasks.FirstOrDefault(t => t.Id == taskIds[i]);
                    if (task != null)
                    {
                        task.SortOrder = i;
                        task.ParentTaskId = parentTaskId;
                    }
                }

                await _context.SaveChangesAsync();
                await GenerateWbsCodesAsync(projectId);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
