using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authorization;
using PM.Tool.Core.Entities.Agile;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Enums;
using PM.Tool.Services;
using PM.Tool.Models.ViewModels;
using PM.Tool.Models.DTOs;

namespace PM.Tool.Controllers
{
    [Authorize]
    public class TestCaseController : SecureBaseController
    {
        private readonly IAgileService _agileService;
        private readonly IProjectService _projectService;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<TestCaseController> _logger;
        private readonly IFormHelperService _formHelper;

        public TestCaseController(
            IAgileService agileService,
            IProjectService projectService,
            UserManager<ApplicationUser> userManager,
            ILogger<TestCaseController> logger,
            IFormHelperService formHelper,
            IAuditService auditService) : base(auditService)
        {
            _agileService = agileService;
            _projectService = projectService;
            _userManager = userManager;
            _logger = logger;
            _formHelper = formHelper;
        }

        #region List and Index

        // GET: TestCase
        public async Task<IActionResult> Index(int projectId, int? featureId = null, int? userStoryId = null, 
            TestCaseType? type = null, TestCasePriority? priority = null, TestCaseStatus? status = null, 
            TestExecutionResult? lastExecutionResult = null, string? searchTerm = null, bool? neverExecuted = null)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                var testCases = await _agileService.GetProjectTestCasesAsync(projectId);
                var features = await _agileService.GetProjectFeaturesAsync(projectId);
                var userStories = await _agileService.GetProjectUserStoriesAsync(projectId);

                // Apply filters
                if (featureId.HasValue)
                    testCases = testCases.Where(tc => tc.FeatureId == featureId.Value);
                
                if (userStoryId.HasValue)
                    testCases = testCases.Where(tc => tc.UserStoryId == userStoryId.Value);
                
                if (type.HasValue)
                    testCases = testCases.Where(tc => tc.Type == type.Value);
                
                if (priority.HasValue)
                    testCases = testCases.Where(tc => tc.Priority == priority.Value);
                
                if (status.HasValue)
                    testCases = testCases.Where(tc => tc.Status == status.Value);
                
                if (lastExecutionResult.HasValue)
                    testCases = testCases.Where(tc => tc.LastExecutionResult == lastExecutionResult.Value);
                
                if (neverExecuted.HasValue && neverExecuted.Value)
                    testCases = testCases.Where(tc => !tc.TestExecutions.Any());
                
                if (!string.IsNullOrEmpty(searchTerm))
                    testCases = testCases.Where(tc => tc.Title.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                                                     tc.Description != null && tc.Description.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                                                     tc.TestCaseKey.Contains(searchTerm, StringComparison.OrdinalIgnoreCase));

                var testCaseSummaries = testCases.Select(tc => new TestCaseSummaryDto
                {
                    Id = tc.Id,
                    TestCaseKey = tc.TestCaseKey,
                    Title = tc.Title,
                    Type = tc.Type,
                    Priority = tc.Priority,
                    Status = tc.Status,
                    LastExecutionResult = tc.LastExecutionResult,
                    LastExecutedAt = tc.TestExecutions?.OrderByDescending(te => te.ExecutedAt).FirstOrDefault()?.ExecutedAt,
                    AssignedToName = tc.AssignedTo?.UserName,
                    FeatureTitle = tc.Feature?.Title,
                    ExecutionCount = tc.TestExecutions?.Count() ?? 0,
                    PassRate = tc.TestExecutions?.Any() == true ? 
                        (decimal)tc.TestExecutions.Count(te => te.Result == TestExecutionResult.Passed) / tc.TestExecutions.Count() * 100 : 0
                });

                var viewModel = new TestCaseListViewModel
                {
                    ProjectId = projectId,
                    ProjectName = project.Name,
                    TestCases = testCaseSummaries,
                    Features = features,
                    UserStories = userStories,
                    FilterOptions = new TestCaseFilterOptions
                    {
                        FeatureId = featureId,
                        UserStoryId = userStoryId,
                        Type = type,
                        Priority = priority,
                        Status = status,
                        LastExecutionResult = lastExecutionResult,
                        SearchTerm = searchTerm,
                        NeverExecuted = neverExecuted
                    },
                    Metrics = new TestCaseListMetrics
                    {
                        TotalTestCases = testCases.Count(),
                        ExecutedTestCases = testCases.Count(tc => tc.TestExecutions.Any()),
                        PassedTestCases = testCases.Count(tc => tc.LastExecutionResult == TestExecutionResult.Passed),
                        FailedTestCases = testCases.Count(tc => tc.LastExecutionResult == TestExecutionResult.Failed),
                        NeverExecutedTestCases = testCases.Count(tc => !tc.TestExecutions.Any()),
                        ExecutionRate = testCases.Any() ? (decimal)testCases.Count(tc => tc.TestExecutions.Any()) / testCases.Count() * 100 : 0,
                        PassRate = testCases.Where(tc => tc.TestExecutions.Any()).Any() ? 
                            (decimal)testCases.Count(tc => tc.LastExecutionResult == TestExecutionResult.Passed) / 
                            testCases.Count(tc => tc.TestExecutions.Any()) * 100 : 0,
                        TestCasesByType = testCases.GroupBy(tc => tc.Type).ToDictionary(g => g.Key, g => g.Count()),
                        TestCoveragePercentage = userStories.Any() ? (double)testCases.Count() / userStories.Count() * 100 : 0
                    }
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading test cases for project {ProjectId}", projectId);
                TempData["Error"] = "An error occurred while loading test cases.";
                return RedirectToAction("Details", "Projects", new { id = projectId });
            }
        }

        #endregion

        #region Details

        // GET: TestCase/Details/5
        public async Task<IActionResult> Details(int id)
        {
            try
            {
                var testCase = await _agileService.GetTestCaseByIdAsync(id);
                if (testCase == null) return NotFound();

                var executions = await _agileService.GetTestCaseExecutionsAsync(id);
                var latestExecution = await _agileService.GetLatestTestExecutionAsync(id);

                var viewModel = new TestCaseDetailsViewModel
                {
                    TestCase = testCase,
                    Executions = executions,
                    LatestExecution = latestExecution,
                    Metrics = new TestCaseMetrics
                    {
                        TotalExecutions = executions.Count(),
                        PassedExecutions = executions.Count(e => e.Result == TestExecutionResult.Passed),
                        FailedExecutions = executions.Count(e => e.Result == TestExecutionResult.Failed),
                        PassRate = executions.Any() ? (decimal)executions.Count(e => e.Result == TestExecutionResult.Passed) / executions.Count() * 100 : 0,
                        LastExecutionResult = latestExecution?.Result,
                        LastExecutedAt = latestExecution?.ExecutedAt,
                        AverageExecutionTime = executions.Where(e => e.ExecutionTime > 0).Any() ? 
                            (int)executions.Where(e => e.ExecutionTime > 0).Average(e => e.ExecutionTime) : null,
                        IsOverdue = false // TODO: Implement overdue logic based on business rules
                    }
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading test case details for {TestCaseId}", id);
                TempData["Error"] = "An error occurred while loading test case details.";
                return RedirectToAction("Index");
            }
        }

        #endregion

        #region Create

        // GET: TestCase/Create
        public async Task<IActionResult> Create(int projectId, int? featureId = null, int? userStoryId = null)
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(projectId);
                if (project == null) return NotFound();

                var viewModel = new TestCaseCreateViewModel
                {
                    ProjectId = projectId,
                    FeatureId = featureId,
                    UserStoryId = userStoryId
                };

                await PopulateDropdowns(projectId);
                ViewBag.Project = project;

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading create test case form for project {ProjectId}", projectId);
                TempData["Error"] = "An error occurred while loading the create form.";
                return RedirectToAction("Index", new { projectId });
            }
        }

        // POST: TestCase/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(TestCaseCreateViewModel viewModel)
        {
            return await this.HandleCreateAsync<TestCaseCreateViewModel, TestCase>(
                viewModel,
                async (testCase) => {
                    var user = await _userManager.GetUserAsync(User);
                    testCase.CreatedByUserId = user?.Id;

                    // Generate test case key
                    var existingTestCases = await _agileService.GetProjectTestCasesAsync(testCase.ProjectId);
                    testCase.TestCaseKey = $"TC-{existingTestCases.Count() + 1:D3}";

                    var createdTestCase = await _agileService.CreateTestCaseAsync(testCase);
                    await LogAuditAsync(AuditAction.Create, "TestCase", createdTestCase.Id);
                    return createdTestCase;
                },
                testCase => testCase.Id,
                async () => await PopulateDropdowns(viewModel.ProjectId),
                _formHelper,
                _logger,
                "TestCase",
                "Details"
            );
        }

        #endregion

        #region Edit

        // GET: TestCase/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            try
            {
                var testCase = await _agileService.GetTestCaseByIdAsync(id);
                if (testCase == null) return NotFound();

                var viewModel = TestCaseEditViewModel.FromEntity(testCase);
                await PopulateDropdowns(testCase.ProjectId);
                ViewBag.Project = await _projectService.GetProjectByIdAsync(testCase.ProjectId);

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit form for test case {TestCaseId}", id);
                TempData["Error"] = "An error occurred while loading the edit form.";
                return RedirectToAction("Details", new { id });
            }
        }

        // POST: TestCase/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, TestCaseEditViewModel viewModel)
        {
            if (id != viewModel.Id) return NotFound();

            return await this.HandleUpdateAsync<TestCaseEditViewModel, TestCase>(
                id,
                viewModel,
                async (testCaseId) => await _agileService.GetTestCaseByIdAsync(testCaseId),
                async (testCase) => {
                    var updatedTestCase = await _agileService.UpdateTestCaseAsync(testCase);
                    await LogAuditAsync(AuditAction.Update, "TestCase", testCase.Id);
                    return updatedTestCase;
                },
                async () => await PopulateDropdowns(viewModel.ProjectId),
                _formHelper,
                _logger,
                "TestCase",
                "Details"
            );
        }

        #endregion

        #region Delete

        // GET: TestCase/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var testCase = await _agileService.GetTestCaseByIdAsync(id);
                if (testCase == null) return NotFound();

                return View(testCase);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading delete confirmation for test case {TestCaseId}", id);
                TempData["Error"] = "An error occurred while loading the delete confirmation.";
                return RedirectToAction("Details", new { id });
            }
        }

        // POST: TestCase/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var testCase = await _agileService.GetTestCaseByIdAsync(id);
                if (testCase == null) return NotFound();

                var projectId = testCase.ProjectId;
                await _agileService.DeleteTestCaseAsync(id);
                await LogAuditAsync(AuditAction.Delete, "TestCase", id);

                TempData["Success"] = "Test case deleted successfully.";
                return RedirectToAction("Index", new { projectId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting test case {TestCaseId}", id);
                TempData["Error"] = "An error occurred while deleting the test case.";
                return RedirectToAction("Details", new { id });
            }
        }

        #endregion

        #region Test Execution

        // POST: TestCase/Execute
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Execute(TestExecutionCreateViewModel viewModel)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    var user = await _userManager.GetUserAsync(User);
                    var execution = viewModel.ToEntity();
                    execution.ExecutedByUserId = user?.Id ?? string.Empty;

                    await _agileService.CreateTestExecutionAsync(execution);
                    await LogAuditAsync(AuditAction.Create, "TestExecution", viewModel.TestCaseId);
                    TempData["Success"] = "Test execution recorded successfully.";
                }
                else
                {
                    TempData["Error"] = "Please provide valid execution details.";
                }

                return RedirectToAction("Details", new { id = viewModel.TestCaseId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error recording test execution for test case {TestCaseId}", viewModel.TestCaseId);
                TempData["Error"] = "An error occurred while recording the test execution.";
                return RedirectToAction("Details", new { id = viewModel.TestCaseId });
            }
        }

        #endregion

        #region Helper Methods

        private async Task PopulateDropdowns(int projectId)
        {
            var features = await _agileService.GetProjectFeaturesAsync(projectId);
            var userStories = await _agileService.GetProjectUserStoriesAsync(projectId);
            
            ViewBag.FeatureId = new SelectList(features, "Id", "Title");
            ViewBag.UserStoryId = new SelectList(userStories, "Id", "Title");
            
            // Add enum dropdowns
            ViewBag.Type = new SelectList(Enum.GetValues<TestCaseType>());
            ViewBag.Priority = new SelectList(Enum.GetValues<TestCasePriority>());
            ViewBag.Status = new SelectList(Enum.GetValues<TestCaseStatus>());
        }

        #endregion
    }
}
