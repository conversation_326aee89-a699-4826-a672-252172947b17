@model dynamic

<!-- Create Task Modal -->
<div id="createTaskModal" class="modal-overlay hidden" onclick="hideModalOnOverlayClick(event, 'createTaskModal')">
    <div class="modal-container">
        <div class="modal-content" onclick="event.stopPropagation()">
            <div class="modal-header">
                <h3 id="createTaskModalTitle" class="modal-title">Create New Task</h3>
                <button type="button" onclick="hideModal('createTaskModal')" class="modal-close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form id="createTaskForm" class="modal-body space-y-6">
                <input type="hidden" id="parentTaskId" name="parentTaskId" value="">
                <input type="hidden" name="projectId" value="@ViewBag.ProjectId">

                <div class="form-group">
                    <label for="taskTitle" class="form-label">
                        Task Title <span class="text-red-500">*</span>
                    </label>
                    <input type="text"
                           id="taskTitle"
                           name="title"
                           required
                           class="form-input">
                </div>

                <div class="form-group">
                    <label for="taskDescription" class="form-label">
                        Description
                    </label>
                    <textarea id="taskDescription"
                              name="description"
                              rows="3"
                              class="form-input"></textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="form-group">
                        <label for="taskPriority" class="form-label">
                            Priority
                        </label>
                        <select id="taskPriority"
                                name="priority"
                                class="form-input wbs-modal-select"
                                data-select2-exclude="true">
                            <option value="Low">Low</option>
                            <option value="Medium" selected>Medium</option>
                            <option value="High">High</option>
                            <option value="Critical">Critical</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="taskAssignedTo" class="form-label">
                            Assigned To
                        </label>
                        <select id="taskAssignedTo"
                                name="assignedToUserId"
                                class="form-input wbs-modal-select"
                                data-select2-exclude="true"
                                data-placeholder="-- Select User --">
                            <option value="">-- Select User --</option>
                            @if (ViewBag.Users != null)
                            {
                                @foreach (var user in ViewBag.Users)
                                {
                                    <option value="@user.UserId">@user.User.FullName</option>
                                }
                            }
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="form-group">
                        <label for="taskStartDate" class="form-label">
                            Start Date
                        </label>
                        <input type="date"
                               id="taskStartDate"
                               name="startDate"
                               class="form-input">
                    </div>

                    <div class="form-group">
                        <label for="taskDueDate" class="form-label">
                            Due Date
                        </label>
                        <input type="date"
                               id="taskDueDate"
                               name="dueDate"
                               class="form-input">
                    </div>
                </div>

                <div class="form-group">
                    <label for="taskEstimatedHours" class="form-label">
                        Estimated Hours
                    </label>
                    <input type="number"
                           id="taskEstimatedHours"
                           name="estimatedHours"
                           min="0"
                           step="1"
                           class="form-input">
                </div>
            </form>

            <div class="modal-footer">
                <button type="button" onclick="hideModal('createTaskModal')" class="btn-secondary-custom">
                    Cancel
                </button>
                <button type="button" onclick="createTask()" class="btn-primary-custom">
                    <i class="fas fa-plus mr-2"></i>Create Task
                </button>
            </div>
        </div>
    </div>
</div>
