using System;
using System.Collections.Generic;

namespace PM.Tool.Models.ViewModels
{
    //public class ProductivityMetricsViewModel
    //{
    //    public Dictionary<string, int> TasksCompletedPerUser { get; set; } = new();
    //    public Dictionary<string, double> AverageCompletionTimePerUser { get; set; } = new();
    //    public Dictionary<string, double> TasksOverduePerUser { get; set; } = new();
    //    public Dictionary<string, int> TasksByPriorityPerUser { get; set; } = new();
    //    public double TeamAverageCompletionTime { get; set; }
    //    public double TeamOverduePercentage { get; set; }
    //    public DateTime StartDate { get; set; }
    //    public DateTime EndDate { get; set; }
    //}



    public class SprintMetrics
    {
        public int SprintNumber { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int PlannedPoints { get; set; }
        public int CompletedPoints { get; set; }
        public List<TaskMetric> CompletedTasks { get; set; } = new();
    }

    public class TaskMetric
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public int StoryPoints { get; set; }
        public string AssignedTo { get; set; } = string.Empty;
        public TimeSpan CompletionTime { get; set; }
        public bool WasOverdue { get; set; }
    }
}
