using System.ComponentModel.DataAnnotations;

using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;

namespace PM.Tool.Models.ViewModels
{
    public class MeetingCreateViewModel : BaseCreateViewModel, IEntityViewModel<Meeting>
    {
        [Required]
        [MaxLength(200)]
        [Display(Name = "Meeting Title")]
        public string Title { get; set; } = string.Empty;

        [MaxLength(2000)]
        [Display(Name = "Description")]
        public string? Description { get; set; }

        [Required]
        [Display(Name = "Project")]
        public int ProjectId { get; set; }

        [Display(Name = "Meeting Type")]
        public MeetingType Type { get; set; } = MeetingType.General;

        [Required]
        [Display(Name = "Scheduled Date & Time")]
        [DataType(DataType.DateTime)]
        public DateTime ScheduledDate { get; set; } = DateTime.Today.AddHours(9);

        [Required]
        [Display(Name = "Duration (Minutes)")]
        [Range(15, 480, ErrorMessage = "Duration must be between 15 minutes and 8 hours")]
        public int DurationMinutes { get; set; } = 60;

        [MaxLength(500)]
        [Display(Name = "Location")]
        public string? Location { get; set; }

        [MaxLength(500)]
        [Display(Name = "Meeting Link")]
        [Url(ErrorMessage = "Please enter a valid URL")]
        public string? MeetingLink { get; set; }

        [Display(Name = "Status")]
        public MeetingStatus Status { get; set; } = MeetingStatus.Scheduled;

        [MaxLength(2000)]
        [Display(Name = "Agenda")]
        public string? Agenda { get; set; }

        public Meeting ToEntity()
        {
            return new Meeting
            {
                Title = Title,
                Description = Description,
                ProjectId = ProjectId,
                Type = Type,
                ScheduledDate = ScheduledDate,
                DurationMinutes = DurationMinutes,
                Location = Location,
                MeetingLink = MeetingLink,
                Status = Status,
                Agenda = Agenda
            };
        }

        public void UpdateEntity(Meeting entity)
        {
            entity.Title = Title;
            entity.Description = Description;
            entity.ProjectId = ProjectId;
            entity.Type = Type;
            entity.ScheduledDate = ScheduledDate;
            entity.DurationMinutes = DurationMinutes;
            entity.Location = Location;
            entity.MeetingLink = MeetingLink;
            entity.Status = Status;
            entity.Agenda = Agenda;
        }
    }

    public class MeetingEditViewModel : MeetingCreateViewModel
    {
        public int Id { get; set; }

        [Display(Name = "Organizer")]
        public string OrganizerUserId { get; set; } = string.Empty;

        [MaxLength(5000)]
        [Display(Name = "Meeting Minutes")]
        public string? Minutes { get; set; }

        [MaxLength(2000)]
        [Display(Name = "Action Items")]
        public string? ActionItems { get; set; }

        [Display(Name = "Actual Start Time")]
        [DataType(DataType.DateTime)]
        public DateTime? ActualStartTime { get; set; }

        [Display(Name = "Actual End Time")]
        [DataType(DataType.DateTime)]
        public DateTime? ActualEndTime { get; set; }

        [Display(Name = "Send Reminders")]
        public bool SendReminders { get; set; } = true;

        [Display(Name = "Is Recurring")]
        public bool IsRecurring { get; set; } = false;

        [Display(Name = "Created At")]
        [DataType(DataType.DateTime)]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Display(Name = "Updated At")]
        [DataType(DataType.DateTime)]
        public DateTime? UpdatedAt { get; set; }

        [Display(Name = "Organizer")]
        public string? Organizer { get; set; }

        public static MeetingEditViewModel FromEntity(Meeting meeting)
        {
            return new MeetingEditViewModel
            {
                Id = meeting.Id,
                Title = meeting.Title,
                Description = meeting.Description,
                ProjectId = meeting.ProjectId,
                Type = meeting.Type,
                ScheduledDate = meeting.ScheduledDate,
                DurationMinutes = meeting.DurationMinutes,
                Location = meeting.Location,
                MeetingLink = meeting.MeetingLink,
                Status = meeting.Status,
                OrganizerUserId = meeting.OrganizerUserId,
                Agenda = meeting.Agenda,
                Minutes = meeting.Minutes,
                ActionItems = meeting.ActionItems,
                ActualStartTime = meeting.ActualStartTime,
                ActualEndTime = meeting.ActualEndTime,
                SendReminders = true, // Default value, could be from entity if it exists
                IsRecurring = false, // Default value, could be from entity if it exists
                CreatedAt = meeting.CreatedAt,
                UpdatedAt = meeting.UpdatedAt,
                Organizer = meeting.OrganizerUserId // Map from UserId to display name if needed
            };
        }

        public new void UpdateEntity(Meeting entity)
        {
            base.UpdateEntity(entity);
            entity.OrganizerUserId = OrganizerUserId;
            entity.Minutes = Minutes;
            entity.ActionItems = ActionItems;
            entity.ActualStartTime = ActualStartTime;
            entity.ActualEndTime = ActualEndTime;
        }
    }
}
