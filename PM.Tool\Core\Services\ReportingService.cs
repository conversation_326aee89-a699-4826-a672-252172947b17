using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text;

using CsvHelper;

using Microsoft.EntityFrameworkCore;

using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;
using PM.Tool.Models.ViewModels;
using TaskStatus = PM.Tool.Core.Enums.TaskStatus;

namespace PM.Tool.Core.Services
{
    public class ReportingService : IReportingService
    {
        private readonly ApplicationDbContext _context;

        public ReportingService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<byte[]> GenerateTasksReportAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var tasks = await _context.Tasks
                .Include(t => t.Project)
                .Include(t => t.AssignedTo)
                .Include(t => t.CreatedBy)
                .Where(t => (!startDate.HasValue || t.CreatedAt >= startDate.Value) &&
                           (!endDate.HasValue || t.CreatedAt <= endDate.Value))
                .ToListAsync();

            var taskReports = tasks.Select(t => new TaskReportModel
            {
                Title = t.Title,
                Description = t.Description ?? "",
                Status = t.Status.ToString(),
                Priority = t.Priority.ToString(),
                AssignedTo = t.AssignedTo?.FullName ?? "Unassigned",
                StartDate = t.StartDate,
                DueDate = t.DueDate,
                CompletedDate = t.CompletedDate,
                EstimatedHours = t.EstimatedHours,
                ActualHours = t.ActualHours,
                Progress = t.Progress
            });

            return GenerateCsvReport(taskReports);
        }

        public async Task<byte[]> GenerateProjectReportAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var projects = await _context.Projects
                .Include(p => p.Manager)
                .Include(p => p.Tasks)
                .Where(p => (!startDate.HasValue || p.CreatedAt >= startDate.Value) &&
                           (!endDate.HasValue || p.CreatedAt <= endDate.Value))
                .ToListAsync();

            var projectReports = projects.Select(p => new ProjectReportModel
            {
                Name = p.Name,
                Description = p.Description ?? "",
                Status = p.Status.ToString(),
                CreatedBy = p.CreatedBy?.FullName ?? "",
                StartDate = p.StartDate,
                EndDate = p.EndDate,
                CreatedAt = p.CreatedAt,
                Budget = p.Budget,
                ClientName = p.ClientName ?? "",
                ProgressPercentage = p.ProgressPercentage,
                TotalTasks = p.Tasks.Count,
                CompletedTasks = p.Tasks.Count(t => t.Status == TaskStatus.Done)
            });

            return GenerateCsvReport(projectReports);
        }

        public async Task<VelocityMetricsViewModel> GetTeamVelocityMetricsAsync(string teamId, DateTime startDate, DateTime endDate)
        {
            var tasks = await _context.Tasks
                .Include(t => t.Project)
                .Include(t => t.AssignedTo)
                .Where(t => t.Project.Members.Any(m => m.UserId == teamId) &&
                           t.CompletedDate.HasValue &&
                           t.CompletedDate.Value >= startDate &&
                           t.CompletedDate.Value <= endDate)
                .ToListAsync();

            var weeklyData = tasks
                .GroupBy(t => GetWeekStartDate(t.CompletedDate!.Value))
                .OrderBy(g => g.Key)
                .Select(g => new WeeklyVelocityData
                {
                    WeekStartDate = g.Key,
                    CompletedTasks = g.Count(),
                    Velocity = (double)g.Count() / 7 // Tasks per day
                })
                .ToList();

            var teamMembers = await _context.Users
                .Where(u => u.Id == teamId)
                .Select(u => new TeamMemberMetrics
                {
                    MemberName = u.FullName,
                    CompletedTasks = tasks.Count(t => t.AssignedToUserId == u.Id && t.Status == TaskStatus.Done),
                    TotalHours = tasks.Where(t => t.AssignedToUserId == u.Id).Sum(t => t.ActualHours),
                    EfficiencyRatio = CalculateEfficiencyRatio(tasks.Where(t => t.AssignedToUserId == u.Id).ToList())
                })
                .ToListAsync();

            return new VelocityMetricsViewModel
            {
                TotalCompletedTasks = tasks.Count,
                WeeklyVelocity = weeklyData,
                AverageCycleTime = CalculateAverageCycleTime(tasks),
                TeamMembers = teamMembers,
                AverageVelocity = weeklyData.Any() ? weeklyData.Average(w => w.Velocity) : 0
            };
        }

        public async Task<ProductivityMetricsViewModel> GetTeamProductivityMetricsAsync(string teamId, DateTime startDate, DateTime endDate)
        {
            var tasks = await _context.Tasks
                .Include(t => t.Project)
                .Include(t => t.AssignedTo)
                .Include(t => t.TimeEntries)
                .Where(t => t.Project.Members.Any(m => m.UserId == teamId) &&
                           t.CreatedAt >= startDate &&
                           t.CreatedAt <= endDate)
                .ToListAsync() ?? new List<TaskEntity>();

            var teamMembers = await _context.Users
                .Where(u => u.Id == teamId)
                .Select(u => new TeamMemberMetrics
                {
                    MemberName = u.FullName,
                    CompletedTasks = tasks.Count(t => t.AssignedToUserId == u.Id && t.Status == TaskStatus.Done),
                    TotalHours = tasks.Where(t => t.AssignedToUserId == u.Id).Sum(t => t.ActualHours),
                    EfficiencyRatio = CalculateEfficiencyRatio(tasks.Where(t => t.AssignedToUserId == u.Id).ToList())
                })
                .ToListAsync() ?? new List<TeamMemberMetrics>();

            var userProductivityData = tasks
                .GroupBy(t => t.AssignedToUserId)
                .Select(g => new UserProductivityData
                {
                    UserId = g.Key ?? string.Empty,
                    UserName = g.First()?.AssignedTo?.UserName ?? "Unassigned",
                    CompletedTasks = g.Count(t => t.Status == TaskStatus.Done),
                    TotalTimeSpent = g.SelectMany(t => t.TimeEntries ?? new List<TimeEntry>()).Sum(te => te.Duration),
                    AverageTimePerTask = g.SelectMany(t => t.TimeEntries ?? new List<TimeEntry>()).Any()
                        ? g.SelectMany(t => t.TimeEntries ?? new List<TimeEntry>()).Average(te => te.Duration)
                        : 0
                })
                .ToList();

            return new ProductivityMetricsViewModel
            {
                TotalTasksCompleted = tasks.Count(t => t.Status == TaskStatus.Done),
                TotalTimeSpent = tasks.SelectMany(t => t.TimeEntries ?? new List<TimeEntry>()).Sum(te => te.Duration),
                UserProductivityData = userProductivityData,
                TeamMembers = teamMembers,
                AverageTasksPerMember = teamMembers.Any() ? teamMembers.Average(m => m.CompletedTasks) : 0,
                AverageEfficiencyRatio = teamMembers.Any() ? teamMembers.Average(m => m.EfficiencyRatio) : 0,
                MemberEfficiencyRatios = teamMembers.ToDictionary(m => m.MemberName, m => m.EfficiencyRatio)
            };
        }

        private DateTime GetWeekStartDate(DateTime date)
        {
            var diff = date.DayOfWeek - DayOfWeek.Monday;
            if (diff < 0) diff += 7;
            return date.AddDays(-diff).Date;
        }

        private double CalculateAverageCycleTime(IEnumerable<TaskEntity> tasks)
        {
            var completedTasks = tasks.Where(t => t.CompletedDate.HasValue && t.StartDate.HasValue);
            return completedTasks.Any()
                ? completedTasks.Average(t => (t.CompletedDate!.Value - t.StartDate!.Value).TotalDays)
                : 0;
        }

        private double CalculateEfficiencyRatio(IEnumerable<TaskEntity> tasks)
        {
            var completedTasks = tasks.Where(t => t.Status == TaskStatus.Done);
            if (!completedTasks.Any()) return 0;

            var totalEstimated = completedTasks.Sum(t => t.EstimatedHours);
            var totalActual = completedTasks.Sum(t => t.ActualHours);

            return totalActual > 0 ? totalEstimated / totalActual : 0;
        }

        private byte[] GenerateCsvReport<T>(IEnumerable<T> data)
        {
            using var memoryStream = new MemoryStream();
            using var writer = new StreamWriter(memoryStream, Encoding.UTF8);
            using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);
            
            csv.WriteRecords(data);
            writer.Flush();
            return memoryStream.ToArray();
        }
    }
}
