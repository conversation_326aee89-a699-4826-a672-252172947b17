<style>
    /* Meeting Management Styles - Following UI Design System */
    
    /* Page Header Enhancements */
    .page-header {
        background: linear-gradient(135deg, rgba(255,255,255,0.8), rgba(248,250,252,0.9));
        backdrop-filter: blur(10px);
    }

    .dark .page-header {
        background: linear-gradient(135deg, rgba(31,41,55,0.8), rgba(17,24,39,0.9));
    }

    /* Filter Bar Enhancements */
    .filter-bar {
        background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(248,250,252,0.98));
        backdrop-filter: blur(8px);
    }

    .dark .filter-bar {
        background: linear-gradient(135deg, rgba(31,41,55,0.95), rgba(17,24,39,0.98));
    }

    /* Stats Badge Enhancements */
    .stats-badge {
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
    }

    .stats-badge:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .stats-badge::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s ease;
    }

    .stats-badge:hover::before {
        left: 100%;
    }

    /* Quick Filter Button Enhancements */
    .quick-filter-btn {
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
    }

    .quick-filter-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .quick-filter-btn.active {
        background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
        border-color: var(--primary-500);
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        transform: translateY(-1px);
    }

    .dark .quick-filter-btn.active {
        background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
        border-color: var(--primary-400);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    }

    /* View Toggle Button Enhancements */
    .view-toggle-btn {
        transition: all 0.2s ease;
        position: relative;
    }

    .view-toggle-btn:hover:not(.active) {
        background-color: rgba(59, 130, 246, 0.1);
        color: var(--primary-600);
    }

    .dark .view-toggle-btn:hover:not(.active) {
        background-color: rgba(59, 130, 246, 0.2);
        color: var(--primary-400);
    }

    .view-toggle-btn.active {
        background: white;
        color: var(--primary-600);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border: 1px solid var(--primary-200);
    }

    .dark .view-toggle-btn.active {
        background: var(--neutral-700);
        color: var(--primary-400);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        border: 1px solid var(--primary-600);
    }

    /* Meeting Card Enhancements */
    .meeting-card {
        transition: all 0.2s ease;
    }

    .meeting-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .dark .meeting-card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }

    /* Meeting List Item Enhancements */
    .meeting-list-item {
        transition: all 0.2s ease;
    }

    .meeting-list-item:hover {
        transform: translateX(4px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border-color: var(--primary-300);
    }

    .dark .meeting-list-item:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        border-color: var(--primary-600);
    }

    /* Enhanced Form Controls */
    input[type="text"], select {
        transition: all 0.2s ease;
    }

    input[type="text"]:focus, select:focus {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    }

    /* Button Enhancements */
    button, .btn {
        transition: all 0.2s ease;
    }

    button:hover, .btn:hover {
        transform: translateY(-1px);
    }

    button:active, .btn:active {
        transform: translateY(0);
    }

    /* Status Color Enhancements */
    .status-scheduled {
        background-color: #dbeafe;
        color: #1e40af;
        border-color: #93c5fd;
    }

    .dark .status-scheduled {
        background-color: rgba(59, 130, 246, 0.2);
        color: #93c5fd;
        border-color: #3b82f6;
    }

    .status-inprogress {
        background-color: #dcfce7;
        color: #166534;
        border-color: #86efac;
    }

    .dark .status-inprogress {
        background-color: rgba(34, 197, 94, 0.2);
        color: #86efac;
        border-color: #22c55e;
    }

    .status-completed {
        background-color: #d1fae5;
        color: #065f46;
        border-color: #6ee7b7;
    }

    .dark .status-completed {
        background-color: rgba(16, 185, 129, 0.2);
        color: #6ee7b7;
        border-color: #10b981;
    }

    .status-cancelled {
        background-color: #fee2e2;
        color: #991b1b;
        border-color: #fca5a5;
    }

    .dark .status-cancelled {
        background-color: rgba(239, 68, 68, 0.2);
        color: #fca5a5;
        border-color: #ef4444;
    }

    /* Meeting Type Icons */
    .meeting-type-standup .type-icon {
        color: #6366f1;
    }

    .meeting-type-planning .type-icon {
        color: #8b5cf6;
    }

    .meeting-type-review .type-icon {
        color: #f59e0b;
    }

    .meeting-type-retrospective .type-icon {
        color: #ec4899;
    }

    .meeting-type-general .type-icon {
        color: #6b7280;
    }

    /* Responsive Enhancements */
    @@media (max-width: 768px) {
        .meeting-card {
            margin-bottom: 1rem;
        }
        
        .meeting-list-item {
            padding: 1rem;
        }
        
        .stats-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.75rem;
        }
    }

    /* Animation Keyframes */
    @@keyframes shimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    @@keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .fade-in {
        animation: fadeIn 0.3s ease-out;
    }

    /* Loading States */
    .loading-skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }

    .dark .loading-skeleton {
        background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
        background-size: 200% 100%;
    }

    @@keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }

    /* Utility Classes */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .truncate {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
</style>
