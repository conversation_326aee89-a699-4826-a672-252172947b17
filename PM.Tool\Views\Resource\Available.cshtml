@model IEnumerable<PM.Tool.Core.Entities.Resource>

@{
    ViewData["Title"] = "Available Resources";
    ViewData["PageTitle"] = "Available Resources";
    ViewData["PageDescription"] = "Find resources available for allocation";
    ViewData["BreadcrumbItems"] = new List<(string Text, string? Url)>
    {
        ("Resources", Url.Action("Index")),
        ("Available", null)
    };

    var startDate = ViewBag.StartDate as DateTime? ?? DateTime.Today;
    var endDate = ViewBag.EndDate as DateTime? ?? DateTime.Today.AddDays(7);
    var requiredHours = ViewBag.RequiredHours as decimal? ?? 8;
}

<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100">Available Resources</h1>
            <p class="text-neutral-600 dark:text-dark-300">
                Find resources available from @startDate.ToString("MMM dd") to @endDate.ToString("MMM dd, yyyy")
            </p>
        </div>

        <div class="flex space-x-3">
            <button type="button" onclick="exportResults()" class="btn-secondary">
                <i class="fas fa-download mr-2"></i>
                Export
            </button>
            <a asp-action="Index" class="btn-outline-secondary">
                <i class="fas fa-list mr-2"></i>
                All Resources
            </a>
        </div>
    </div>

    <!-- Search Criteria -->
    <div class="card-custom">
        <div class="card-header-custom">
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                <i class="fas fa-search mr-2 text-primary-500"></i>
                Search Criteria
            </h3>
        </div>
        <div class="card-body-custom">
            <form method="get" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label for="startDate" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Start Date</label>
                        <input type="date" id="startDate" name="startDate" value="@startDate.ToString("yyyy-MM-dd")" class="form-input" required />
                    </div>

                    <div>
                        <label for="endDate" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">End Date</label>
                        <input type="date" id="endDate" name="endDate" value="@endDate.ToString("yyyy-MM-dd")" class="form-input" required />
                    </div>

                    <div>
                        <label for="requiredHours" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Required Hours/Day</label>
                        <div class="relative">
                            <input type="number" id="requiredHours" name="requiredHours" value="@requiredHours"
                                   min="0.5" max="24" step="0.5" class="form-input pr-12" required />
                            <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500">hrs</span>
                        </div>
                    </div>

                    <div class="flex items-end">
                        <button type="submit" class="btn-primary w-full">
                            <i class="fas fa-search mr-2"></i>
                            Search
                        </button>
                    </div>
                </div>

                <!-- Advanced Filters -->
                <div class="border-t border-neutral-200 dark:border-dark-600 pt-4">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label for="resourceType" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Resource Type</label>
                            <select id="resourceType" name="resourceType" class="select2-enabled form-select">
                                <option value="">All Types</option>
                                <option value="Human">Human</option>
                                <option value="Equipment">Equipment</option>
                                <option value="Material">Material</option>
                                <option value="Facility">Facility</option>
                            </select>
                        </div>

                        <div>
                            <label for="department" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Department</label>
                            <input type="text" id="department" name="department" class="form-input" placeholder="Enter department" />
                        </div>

                        <div>
                            <label for="skills" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Required Skills</label>
                            <input type="text" id="skills" name="skills" class="form-input" placeholder="e.g., JavaScript, React" />
                        </div>

                        <div>
                            <label for="maxRate" class="block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2">Max Hourly Rate</label>
                            <div class="relative">
                                <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-500">$</span>
                                <input type="number" id="maxRate" name="maxRate" class="form-input pl-8" placeholder="0.00" step="0.01" />
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Search Results Summary -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        @{
            var totalFound = Model.Count();
            var humanResources = Model.Count(r => r.Type == PM.Tool.Core.Entities.ResourceType.Human);
            var avgRate = Model.Any() ? Model.Average(r => r.HourlyRate) : 0;
            var totalCapacity = Model.Sum(r => r.Capacity);
        }

        @{
            ViewData["Title"] = "Available Resources";
            ViewData["Icon"] = "fas fa-check-circle";
            ViewData["IconColor"] = "bg-gradient-to-br from-green-500 to-green-600";
            ViewData["Value"] = totalFound.ToString();
            ViewData["Description"] = "resources found";
        }
        <partial name="Components/_StatsCard" view-data="ViewData" />

        @{
            ViewData["Title"] = "Human Resources";
            ViewData["Icon"] = "fas fa-users";
            ViewData["IconColor"] = "bg-gradient-to-br from-blue-500 to-blue-600";
            ViewData["Value"] = humanResources.ToString();
            ViewData["Description"] = "team members";
        }
        <partial name="Components/_StatsCard" view-data="ViewData" />

        @{
            ViewData["Title"] = "Average Rate";
            ViewData["Icon"] = "fas fa-dollar-sign";
            ViewData["IconColor"] = "bg-gradient-to-br from-yellow-500 to-yellow-600";
            ViewData["Value"] = avgRate.ToString("C");
            ViewData["Description"] = "per hour";
        }
        <partial name="Components/_StatsCard" view-data="ViewData" />

        @{
            ViewData["Title"] = "Total Capacity";
            ViewData["Icon"] = "fas fa-clock";
            ViewData["IconColor"] = "bg-gradient-to-br from-purple-500 to-purple-600";
            ViewData["Value"] = $"{totalCapacity} hrs";
            ViewData["Description"] = "per day";
        }
        <partial name="Components/_StatsCard" view-data="ViewData" />
    </div>

    <!-- Available Resources List -->
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                    <i class="fas fa-list mr-2 text-primary-500"></i>
                    Available Resources (@totalFound)
                </h3>

                <div class="flex space-x-2">
                    <button type="button" onclick="selectAll()" class="btn-outline-secondary text-sm">
                        Select All
                    </button>
                    <button type="button" onclick="createBulkAllocation()" class="btn-primary text-sm">
                        <i class="fas fa-plus mr-1"></i>
                        Bulk Allocate
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            @if (Model.Any())
            {
                <div class="space-y-4" id="resourcesList">
                    @foreach (var resource in Model.OrderBy(r => r.Type).ThenBy(r => r.Name))
                    {
                        <div class="resource-card p-4 border border-neutral-200 dark:border-dark-600 rounded-lg hover:shadow-md transition-shadow"
                             data-type="@resource.Type.ToString().ToLower()"
                             data-department="@(resource.Department ?? "")"
                             data-rate="@resource.HourlyRate">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <input type="checkbox" class="resource-checkbox form-checkbox" value="@resource.Id" />

                                    <div class="w-12 h-12 @GetResourceTypeIconBg(resource.Type) rounded-lg flex items-center justify-center">
                                        <i class="@GetResourceTypeIcon(resource.Type) text-white text-xl"></i>
                                    </div>

                                    <div class="flex-1">
                                        <div class="flex items-center space-x-3">
                                            <h4 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                                                @resource.Name
                                            </h4>
                                            <span class="badge-info">@resource.Type</span>
                                        </div>

                                        <div class="flex items-center space-x-4 mt-1 text-sm text-neutral-600 dark:text-dark-300">
                                            @if (!string.IsNullOrEmpty(resource.Department))
                                            {
                                                <span><i class="fas fa-building mr-1"></i>@resource.Department</span>
                                            }
                                            @if (!string.IsNullOrEmpty(resource.Location))
                                            {
                                                <span><i class="fas fa-map-marker-alt mr-1"></i>@resource.Location</span>
                                            }
                                            <span><i class="fas fa-clock mr-1"></i>@resource.Capacity hrs/day</span>
                                            <span><i class="fas fa-dollar-sign mr-1"></i>@resource.HourlyRate.ToString("C")/hr</span>
                                        </div>

                                        @if (!string.IsNullOrEmpty(resource.Skills))
                                        {
                                            <div class="flex flex-wrap gap-1 mt-2">
                                                @foreach (var skill in resource.Skills.Split(',', StringSplitOptions.RemoveEmptyEntries).Take(5))
                                                {
                                                    <span class="badge-secondary text-xs">@skill.Trim()</span>
                                                }
                                                @if (resource.Skills.Split(',', StringSplitOptions.RemoveEmptyEntries).Length > 5)
                                                {
                                                    <span class="text-xs text-neutral-500 dark:text-dark-400">
                                                        +@(resource.Skills.Split(',', StringSplitOptions.RemoveEmptyEntries).Length - 5) more
                                                    </span>
                                                }
                                            </div>
                                        }
                                    </div>
                                </div>

                                <div class="flex items-center space-x-3">
                                    <div class="text-right">
                                        <div class="text-sm font-medium text-neutral-900 dark:text-dark-100">
                                            Available: @resource.Capacity hrs/day
                                        </div>
                                        <div class="text-sm text-neutral-600 dark:text-dark-300">
                                            Cost: @((resource.Capacity * resource.HourlyRate).ToString("C"))/day
                                        </div>
                                    </div>

                                    <div class="flex flex-col space-y-2">
                                        <a asp-action="Details" asp-route-id="@resource.Id" class="btn-outline-primary text-sm">
                                            <i class="fas fa-eye mr-1"></i>
                                            View
                                        </a>
                                        <a asp-action="CreateAllocation" asp-route-resourceId="@resource.Id" class="btn-primary text-sm">
                                            <i class="fas fa-plus mr-1"></i>
                                            Allocate
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-12">
                    <i class="fas fa-search text-6xl text-neutral-400 dark:text-dark-500 mb-4"></i>
                    <h3 class="text-lg font-medium text-neutral-900 dark:text-dark-100 mb-2">No Available Resources</h3>
                    <p class="text-neutral-600 dark:text-dark-300 mb-6">
                        No resources are available for the specified criteria.
                    </p>
                    <div class="space-x-4">
                        <button type="button" onclick="adjustCriteria()" class="btn-primary">
                            <i class="fas fa-edit mr-2"></i>
                            Adjust Search Criteria
                        </button>
                        <a asp-action="Index" class="btn-secondary">
                            <i class="fas fa-list mr-2"></i>
                            View All Resources
                        </a>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Validate end date is after start date
        document.getElementById('endDate').addEventListener('change', function() {
            const startDate = new Date(document.getElementById('startDate').value);
            const endDate = new Date(this.value);

            if (startDate && endDate && endDate < startDate) {
                alert('End date must be after start date');
                this.value = '';
            }
        });

        // Select all functionality
        function selectAll() {
            const checkboxes = document.querySelectorAll('.resource-checkbox');
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);

            checkboxes.forEach(cb => {
                cb.checked = !allChecked;
            });
        }

        // Bulk allocation
        function createBulkAllocation() {
            const selectedResources = Array.from(document.querySelectorAll('.resource-checkbox:checked'))
                .map(cb => cb.value);

            if (selectedResources.length === 0) {
                alert('Please select at least one resource');
                return;
            }

            // Implement bulk allocation logic
            console.log('Creating bulk allocation for resources:', selectedResources);
            alert(`Creating allocation for ${selectedResources.length} resources`);
        }

        // Export results
        function exportResults() {
            // Implement export functionality
            alert('Export functionality would be implemented here');
        }

        // Adjust criteria
        function adjustCriteria() {
            document.getElementById('startDate').focus();
        }

        // Filter resources by type, department, etc.
        function filterResources() {
            const typeFilter = document.getElementById('resourceType').value.toLowerCase();
            const departmentFilter = document.getElementById('department').value.toLowerCase();
            const maxRate = parseFloat(document.getElementById('maxRate').value) || Infinity;

            const cards = document.querySelectorAll('.resource-card');

            cards.forEach(card => {
                let show = true;

                if (typeFilter && !card.dataset.type.includes(typeFilter)) {
                    show = false;
                }

                if (departmentFilter && !card.dataset.department.toLowerCase().includes(departmentFilter)) {
                    show = false;
                }

                if (parseFloat(card.dataset.rate) > maxRate) {
                    show = false;
                }

                card.style.display = show ? '' : 'none';
            });
        }

        // Add event listeners for real-time filtering
        document.getElementById('resourceType').addEventListener('change', filterResources);
        document.getElementById('department').addEventListener('input', filterResources);
        document.getElementById('maxRate').addEventListener('input', filterResources);
    </script>
}

@functions {
    private string GetResourceTypeIcon(PM.Tool.Core.Entities.ResourceType type)
    {
        return type switch
        {
            PM.Tool.Core.Entities.ResourceType.Human => "fas fa-user",
            PM.Tool.Core.Entities.ResourceType.Equipment => "fas fa-tools",
            PM.Tool.Core.Entities.ResourceType.Material => "fas fa-boxes",
            PM.Tool.Core.Entities.ResourceType.Facility => "fas fa-building",
            _ => "fas fa-cube"
        };
    }

    private string GetResourceTypeIconBg(PM.Tool.Core.Entities.ResourceType type)
    {
        return type switch
        {
            PM.Tool.Core.Entities.ResourceType.Human => "bg-gradient-to-br from-blue-500 to-blue-600",
            PM.Tool.Core.Entities.ResourceType.Equipment => "bg-gradient-to-br from-orange-500 to-orange-600",
            PM.Tool.Core.Entities.ResourceType.Material => "bg-gradient-to-br from-green-500 to-green-600",
            PM.Tool.Core.Entities.ResourceType.Facility => "bg-gradient-to-br from-purple-500 to-purple-600",
            _ => "bg-gradient-to-br from-neutral-500 to-neutral-600"
        };
    }
}
