@*
    Professional Notification/Toast Component - Usage Examples:

    1. Basic notification:
    @{
        ViewData["Type"] = "success"; // success, error, warning, info
        ViewData["Title"] = "Success!";
        ViewData["Message"] = "Your changes have been saved.";
        ViewData["AutoDismiss"] = true;
        ViewData["Duration"] = 5000;
    }
    <partial name="Components/_Notification" view-data="ViewData" />

    2. Persistent notification with actions:
    @{
        ViewData["Type"] = "warning";
        ViewData["Title"] = "Action Required";
        ViewData["Message"] = "Please review and approve the pending changes.";
        ViewData["AutoDismiss"] = false;
        ViewData["ShowActions"] = true;
        ViewData["PrimaryAction"] = "Review";
        ViewData["SecondaryAction"] = "Dismiss";
    }
    <partial name="Components/_Notification" view-data="ViewData" />
*@

@{
    var type = ViewData["Type"]?.ToString() ?? "info";
    var title = ViewData["Title"]?.ToString() ?? "";
    var message = ViewData["Message"]?.ToString() ?? "";
    var icon = ViewData["Icon"]?.ToString();
    var autoDismiss = ViewData["AutoDismiss"] as bool? ?? true;
    var duration = ViewData["Duration"] as int? ?? 5000;
    var showActions = ViewData["ShowActions"] as bool? ?? false;
    var primaryAction = ViewData["PrimaryAction"]?.ToString();
    var secondaryAction = ViewData["SecondaryAction"]?.ToString();
    var primaryActionHref = ViewData["PrimaryActionHref"]?.ToString();
    var secondaryActionHref = ViewData["SecondaryActionHref"]?.ToString();
    var primaryActionOnClick = ViewData["PrimaryActionOnClick"]?.ToString();
    var secondaryActionOnClick = ViewData["SecondaryActionOnClick"]?.ToString();
    var position = ViewData["Position"]?.ToString() ?? "top-right"; // top-right, top-left, bottom-right, bottom-left, top-center
    var notificationId = ViewData["NotificationId"]?.ToString() ?? $"notification_{Guid.NewGuid().ToString("N")[..8]}";
    
    var typeConfig = type switch {
        "success" => new { 
            bgClass = "bg-success-50 dark:bg-success-900/20", 
            borderClass = "border-success-200 dark:border-success-800",
            iconClass = "text-success-600 dark:text-success-400",
            titleClass = "text-success-800 dark:text-success-200",
            messageClass = "text-success-700 dark:text-success-300",
            defaultIcon = "fas fa-check-circle"
        },
        "error" => new { 
            bgClass = "bg-danger-50 dark:bg-danger-900/20", 
            borderClass = "border-danger-200 dark:border-danger-800",
            iconClass = "text-danger-600 dark:text-danger-400",
            titleClass = "text-danger-800 dark:text-danger-200",
            messageClass = "text-danger-700 dark:text-danger-300",
            defaultIcon = "fas fa-exclamation-circle"
        },
        "warning" => new { 
            bgClass = "bg-warning-50 dark:bg-warning-900/20", 
            borderClass = "border-warning-200 dark:border-warning-800",
            iconClass = "text-warning-600 dark:text-warning-400",
            titleClass = "text-warning-800 dark:text-warning-200",
            messageClass = "text-warning-700 dark:text-warning-300",
            defaultIcon = "fas fa-exclamation-triangle"
        },
        "info" => new { 
            bgClass = "bg-info-50 dark:bg-info-900/20", 
            borderClass = "border-info-200 dark:border-info-800",
            iconClass = "text-info-600 dark:text-info-400",
            titleClass = "text-info-800 dark:text-info-200",
            messageClass = "text-info-700 dark:text-info-300",
            defaultIcon = "fas fa-info-circle"
        },
        _ => new { 
            bgClass = "bg-neutral-50 dark:bg-dark-700", 
            borderClass = "border-neutral-200 dark:border-dark-200",
            iconClass = "text-neutral-600 dark:text-dark-300",
            titleClass = "text-neutral-800 dark:text-dark-100",
            messageClass = "text-neutral-700 dark:text-dark-200",
            defaultIcon = "fas fa-bell"
        }
    };
    
    var finalIcon = icon ?? typeConfig.defaultIcon;
    
    var positionClasses = position switch {
        "top-right" => "top-4 right-4",
        "top-left" => "top-4 left-4",
        "bottom-right" => "bottom-4 right-4",
        "bottom-left" => "bottom-4 left-4",
        "top-center" => "top-4 left-1/2 transform -translate-x-1/2",
        _ => "top-4 right-4"
    };
}

<!-- Notification Container (for positioning) -->
<div id="@notificationId" class="fixed @positionClasses z-50 max-w-sm w-full notification-item animate-slide-in" style="display: none;">
    <div class="@typeConfig.bgClass @typeConfig.borderClass border rounded-xl shadow-lg p-4 backdrop-blur-sm">
        <div class="flex items-start space-x-3">
            <!-- Icon -->
            <div class="flex-shrink-0">
                <i class="@finalIcon @typeConfig.iconClass text-lg"></i>
            </div>
            
            <!-- Content -->
            <div class="flex-1 min-w-0">
                @if (!string.IsNullOrEmpty(title))
                {
                    <h4 class="text-sm font-semibold @typeConfig.titleClass mb-1">@title</h4>
                }
                @if (!string.IsNullOrEmpty(message))
                {
                    <p class="text-sm @typeConfig.messageClass">@message</p>
                }
                
                <!-- Actions -->
                @if (showActions && (!string.IsNullOrEmpty(primaryAction) || !string.IsNullOrEmpty(secondaryAction)))
                {
                    <div class="mt-3 flex space-x-2">
                        @if (!string.IsNullOrEmpty(primaryAction))
                        {
                            @if (!string.IsNullOrEmpty(primaryActionHref))
                            {
                                <a href="@primaryActionHref" class="text-xs font-medium @typeConfig.iconClass hover:underline">@primaryAction</a>
                            }
                            else
                            {
                                <button onclick="@(primaryActionOnClick ?? $"dismissNotification('{notificationId}')")" class="text-xs font-medium @typeConfig.iconClass hover:underline">@primaryAction</button>
                            }
                        }
                        
                        @if (!string.IsNullOrEmpty(secondaryAction))
                        {
                            @if (!string.IsNullOrEmpty(secondaryActionHref))
                            {
                                <a href="@secondaryActionHref" class="text-xs font-medium @typeConfig.messageClass hover:underline">@secondaryAction</a>
                            }
                            else
                            {
                                <button onclick="@(secondaryActionOnClick ?? $"dismissNotification('{notificationId}')")" class="text-xs font-medium @typeConfig.messageClass hover:underline">@secondaryAction</button>
                            }
                        }
                    </div>
                }
            </div>
            
            <!-- Close Button -->
            <div class="flex-shrink-0">
                <button onclick="dismissNotification('@notificationId')" class="@typeConfig.iconClass hover:@typeConfig.titleClass transition-colors">
                    <i class="fas fa-times text-sm"></i>
                </button>
            </div>
        </div>
        
        <!-- Progress Bar (for auto-dismiss) -->
        @if (autoDismiss)
        {
            <div class="mt-3 w-full bg-white dark:bg-dark-600 bg-opacity-30 rounded-full h-1">
                <div id="@(notificationId)Progress" class="@typeConfig.iconClass bg-current h-1 rounded-full transition-all ease-linear" style="width: 100%; transition-duration: @(duration)ms;"></div>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Show notification
            showNotification('@notificationId', @autoDismiss.ToString().ToLower(), @duration);
        });

        function showNotification(notificationId, autoDismiss, duration) {
            var notification = document.getElementById(notificationId);
            if (!notification) return;
            
            // Show notification
            notification.style.display = 'block';
            
            // Start progress bar animation
            if (autoDismiss) {
                var progressBar = document.getElementById(notificationId + 'Progress');
                if (progressBar) {
                    setTimeout(function() {
                        progressBar.style.width = '0%';
                    }, 100);
                }
                
                // Auto dismiss
                setTimeout(function() {
                    dismissNotification(notificationId);
                }, duration);
            }
        }

        function dismissNotification(notificationId) {
            var notification = document.getElementById(notificationId);
            if (!notification) return;
            
            // Add fade out animation
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            
            // Remove from DOM after animation
            setTimeout(function() {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }

        // Global notification system
        window.NotificationSystem = {
            show: function(type, title, message, options = {}) {
                var notificationId = 'notification_' + Date.now();
                var autoDismiss = options.autoDismiss !== false;
                var duration = options.duration || 5000;
                var position = options.position || 'top-right';
                
                var notification = this.createNotification(notificationId, type, title, message, {
                    autoDismiss: autoDismiss,
                    duration: duration,
                    position: position,
                    showActions: options.showActions || false,
                    primaryAction: options.primaryAction,
                    secondaryAction: options.secondaryAction,
                    primaryActionOnClick: options.primaryActionOnClick,
                    secondaryActionOnClick: options.secondaryActionOnClick
                });
                
                document.body.appendChild(notification);
                showNotification(notificationId, autoDismiss, duration);
                
                return notificationId;
            },
            
            success: function(title, message, options = {}) {
                return this.show('success', title, message, options);
            },
            
            error: function(title, message, options = {}) {
                return this.show('error', title, message, { ...options, autoDismiss: false });
            },
            
            warning: function(title, message, options = {}) {
                return this.show('warning', title, message, options);
            },
            
            info: function(title, message, options = {}) {
                return this.show('info', title, message, options);
            },
            
            dismiss: function(notificationId) {
                dismissNotification(notificationId);
            },
            
            dismissAll: function() {
                document.querySelectorAll('.notification-item').forEach(function(notification) {
                    dismissNotification(notification.id);
                });
            },
            
            createNotification: function(notificationId, type, title, message, options) {
                // This would create the notification HTML dynamically
                // For now, we'll use the server-side component
                var div = document.createElement('div');
                div.innerHTML = `<!-- Notification would be created here -->`;
                return div.firstElementChild;
            }
        };
        
        // Expose to global scope
        window.showNotification = showNotification;
        window.dismissNotification = dismissNotification;
    </script>
}
