using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities
{
    public class Dashboard : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(1000)]
        public string? Description { get; set; }

        public string UserId { get; set; } = string.Empty;

        public bool IsDefault { get; set; } = false;

        public bool IsPublic { get; set; } = false;

        public string Layout { get; set; } = string.Empty; // JSON layout configuration

        // Navigation properties
        public virtual ApplicationUser User { get; set; } = null!;
        public virtual ICollection<DashboardWidget> Widgets { get; set; } = new List<DashboardWidget>();
    }

    public class DashboardWidget : BaseEntity
    {
        public int DashboardId { get; set; }

        [Required]
        [MaxLength(100)]
        public string Title { get; set; } = string.Empty;

        public WidgetType Type { get; set; } = WidgetType.Chart;

        public string Configuration { get; set; } = string.Empty; // JSON configuration

        public int PositionX { get; set; }

        public int PositionY { get; set; }

        public int Width { get; set; } = 4;

        public int Height { get; set; } = 3;

        public bool IsVisible { get; set; } = true;

        public int RefreshInterval { get; set; } = 300; // seconds

        // Navigation properties
        public virtual Dashboard Dashboard { get; set; } = null!;
    }

    public class Report : BaseEntity
    {
        [Required]
        [MaxLength(200)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(1000)]
        public string? Description { get; set; }

        public ReportType Type { get; set; } = ReportType.Project;

        public string Parameters { get; set; } = string.Empty; // JSON parameters

        public string CreatedByUserId { get; set; } = string.Empty;

        public bool IsScheduled { get; set; } = false;

        public string? ScheduleCron { get; set; }

        public string? Recipients { get; set; } // JSON array of email addresses

        private DateTime? _lastRun;
        public DateTime? LastRun
        {
            get => _lastRun;
            set => _lastRun = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        private DateTime? _nextRun;
        public DateTime? NextRun
        {
            get => _nextRun;
            set => _nextRun = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        // Navigation properties
        public virtual ApplicationUser CreatedBy { get; set; } = null!;
        public virtual ICollection<ReportExecution> Executions { get; set; } = new List<ReportExecution>();
    }

    public class ReportExecution : BaseEntity
    {
        public int ReportId { get; set; }

        private DateTime _executedAt = DateTime.UtcNow;
        public DateTime ExecutedAt
        {
            get => _executedAt;
            set => _executedAt = DateTime.SpecifyKind(value, DateTimeKind.Utc);
        }

        public string ExecutedByUserId { get; set; } = string.Empty;

        public ExecutionStatus Status { get; set; } = ExecutionStatus.Running;

        public string? FilePath { get; set; }

        public string? ErrorMessage { get; set; }

        public int RecordCount { get; set; }

        public long FileSizeBytes { get; set; }

        // Navigation properties
        public virtual Report Report { get; set; } = null!;
        public virtual ApplicationUser ExecutedBy { get; set; } = null!;
    }

    public class ProjectMetrics : BaseEntity
    {
        public int ProjectId { get; set; }

        private DateTime _date;
        public DateTime Date
        {
            get => _date;
            set => _date = DateTime.SpecifyKind(value, DateTimeKind.Utc);
        }

        public int TotalTasks { get; set; }

        public int CompletedTasks { get; set; }

        public int InProgressTasks { get; set; }

        public int OverdueTasks { get; set; }

        public decimal BudgetSpent { get; set; }

        public decimal BudgetRemaining { get; set; }

        public double ProgressPercentage { get; set; }

        public double Velocity { get; set; } // Tasks completed per day

        public int TeamSize { get; set; }

        public double UtilizationRate { get; set; }

        // Navigation properties
        public virtual Project Project { get; set; } = null!;
    }

    public enum WidgetType
    {
        Chart = 1,
        Table = 2,
        Metric = 3,
        Progress = 4,
        Calendar = 5,
        List = 6
    }

    public enum ReportType
    {
        Project = 1,
        Task = 2,
        Resource = 3,
        Time = 4,
        Budget = 5,
        Risk = 6,
        Custom = 7
    }

    public enum ExecutionStatus
    {
        Running = 1,
        Completed = 2,
        Failed = 3,
        Cancelled = 4
    }
}
