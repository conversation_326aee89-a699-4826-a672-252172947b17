<script>
    // WBS Core JavaScript - Global variables and initialization

    // Global variables
    let wbsData = [];
    let selectedTaskId = null;

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('WBS Core Script Loaded');
        console.log('Current wbsData:', wbsData);
        console.log('Selected Task ID:', selectedTaskId);

        // Prevent any automatic modal opening
        window.wbsInitialized = false;
        // Setup AJAX to include antiforgery token for form-based requests
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                if (settings.type === 'POST' && !settings.crossDomain) {
                    const token = $('input[name="__RequestVerificationToken"]').val();

                    // For JSON requests, add token to header
                    if (settings.contentType && settings.contentType.indexOf('application/json') !== -1) {
                        if (token) {
                            xhr.setRequestHeader('RequestVerificationToken', token);
                        }
                    }
                    // For form requests, add token to data
                    else {
                        if (!settings.contentType) {
                            settings.contentType = 'application/x-www-form-urlencoded; charset=UTF-8';
                        }
                        if (token && settings.data && settings.data.indexOf('__RequestVerificationToken') === -1) {
                            settings.data += (settings.data ? '&' : '') + '__RequestVerificationToken=' + encodeURIComponent(token);
                        }
                    }
                }
            }
        });

        loadWbsStructure();

        // Update statistics after WBS is loaded
        setTimeout(updateWbsStatistics, 1000);

        // Initialize accessibility enhancements
        setTimeout(enhanceAccessibility, 500);

        // Mark WBS as fully initialized
        setTimeout(() => {
            window.wbsInitialized = true;
        }, 1500);
    });

    // Simple Select2 initialization for modal
    function initializeModalSelect2() {
        try {
            $('#taskPriority, #taskAssignedTo').each(function() {
                const $element = $(this);

                if (!$element.hasClass('select2-hidden-accessible')) {
                    $element.select2({
                        width: '100%',
                        dropdownParent: $('#createTaskModal'),
                        minimumResultsForSearch: 0, // Always show search
                        placeholder: $element.data('placeholder') || 'Select an option',
                        allowClear: $element.attr('id') === 'taskAssignedTo'
                    });
                }
            });
        } catch (e) {
            console.warn('Error initializing modal Select2:', e);
        }
    }

    // Load WBS structure
    function loadWbsStructure() {
        $('#wbs-loading').removeClass('hidden');
        $('#wbs-tree').empty();
        $('#wbs-empty').addClass('hidden');

        const projectId = window.projectId || @ViewBag.ProjectId;

        $.get(`@Url.Action("GetStructure", "Wbs")?projectId=${projectId}`)
            .done(function(data) {
                wbsData = data;
                if (data && data.length > 0) {
                    renderWbsTree(data);
                    $('#wbs-empty').addClass('hidden');
                } else {
                    $('#wbs-empty').removeClass('hidden');
                }
                updateWbsStatistics();
            })
            .fail(function() {
                showAlert('Error loading WBS structure', 'danger');
                $('#wbs-empty').removeClass('hidden');
            })
            .always(function() {
                $('#wbs-loading').addClass('hidden');
            });
    }

    // Find task by ID
    function findTaskById(tasks, taskId) {
        for (let task of tasks) {
            if (task.id === taskId) {
                return task;
            }
            if (task.children) {
                const found = findTaskById(task.children, taskId);
                if (found) return found;
            }
        }
        return null;
    }

    // Generate WBS codes
    function generateWbsCodes() {
        if (confirm('This will regenerate all WBS codes. Continue?')) {
            const projectId = window.projectId || @ViewBag.ProjectId;

            $.post('@Url.Action("GenerateCodes", "Wbs")', { projectId: projectId })
                .done(function(response) {
                    if (response.success) {
                        showAlert('WBS codes generated successfully', 'success');
                        loadWbsStructure();
                    } else {
                        showAlert(response.message || 'Error generating WBS codes', 'danger');
                    }
                })
                .fail(function(xhr, status, error) {
                    console.error('Generate codes error:', xhr.responseText);
                    if (xhr.status === 400) {
                        showAlert('Validation error. Please try again.', 'danger');
                    } else if (xhr.status === 403) {
                        showAlert('Access denied. Please refresh the page and try again.', 'danger');
                    } else {
                        showAlert('Error generating WBS codes', 'danger');
                    }
                });
        }
    }
</script>
