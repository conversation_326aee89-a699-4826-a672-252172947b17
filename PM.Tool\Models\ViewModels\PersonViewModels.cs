using System.ComponentModel.DataAnnotations;

using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;

namespace PM.Tool.Models.ViewModels
{
    public class PersonCreateViewModel : BaseCreateViewModel, IEntityViewModel<Person>
    {
        [Required]
        [MaxLength(100)]
        [Display(Name = "First Name")]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        [Display(Name = "Last Name")]
        public string LastName { get; set; } = string.Empty;

        [Required]
        [MaxLength(200)]
        [Display(Name = "Email")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        public string Email { get; set; } = string.Empty;

        [MaxLength(20)]
        [Display(Name = "Phone")]
        [Phone(ErrorMessage = "Please enter a valid phone number")]
        public string? Phone { get; set; }

        [Display(Name = "Person Type")]
        public PersonType Type { get; set; } = PersonType.Internal;

        [Display(Name = "Status")]
        public PersonStatus Status { get; set; } = PersonStatus.Active;

        [Display(Name = "Linked User Account")]
        public string? UserId { get; set; }

        [Display(Name = "Has System Access")]
        public bool HasSystemAccess { get; set; } = false;

        [MaxLength(200)]
        [Display(Name = "Organization")]
        public string? Organization { get; set; }

        [MaxLength(100)]
        [Display(Name = "Department")]
        public string? Department { get; set; }

        [MaxLength(100)]
        [Display(Name = "Job Title")]
        public string? Title { get; set; }

        [MaxLength(100)]
        [Display(Name = "Location")]
        public string? Location { get; set; }

        [MaxLength(50)]
        [Display(Name = "Employee ID")]
        public string? EmployeeId { get; set; }

        [Display(Name = "Profile Picture URL")]
        [Url(ErrorMessage = "Please enter a valid URL")]
        public string? ProfilePictureUrl { get; set; }

        [MaxLength(1000)]
        [Display(Name = "Bio")]
        public string? Bio { get; set; }

        [MaxLength(1000)]
        [Display(Name = "Communication Preferences")]
        public string? CommunicationPreferences { get; set; }

        [MaxLength(100)]
        [Display(Name = "Working Hours")]
        public string? WorkingHours { get; set; }

        [Display(Name = "Hire Date")]
        [DataType(DataType.Date)]
        public DateTime? HireDate { get; set; }

        [MaxLength(50)]
        [Display(Name = "Time Zone")]
        public string? TimeZone { get; set; }

        [Display(Name = "Termination Date")]
        [DataType(DataType.Date)]
        public DateTime? TerminationDate { get; set; }

        [MaxLength(1000)]
        [Display(Name = "Notes")]
        public string? Notes { get; set; }

        public Person ToEntity()
        {
            return new Person
            {
                FirstName = FirstName,
                LastName = LastName,
                Email = Email,
                Phone = Phone,
                Type = Type,
                Status = Status,
                UserId = UserId,
                HasSystemAccess = HasSystemAccess,
                Organization = Organization,
                Department = Department,
                Title = Title,
                Location = Location,
                EmployeeId = EmployeeId,
                ProfilePictureUrl = ProfilePictureUrl,
                Bio = Bio,
                CommunicationPreferences = CommunicationPreferences,
                WorkingHours = WorkingHours,
                TimeZone = TimeZone,
                HireDate = HireDate,
                TerminationDate = TerminationDate,
                Notes = Notes
            };
        }

        public void UpdateEntity(Person entity)
        {
            entity.FirstName = FirstName;
            entity.LastName = LastName;
            entity.Email = Email;
            entity.Phone = Phone;
            entity.Type = Type;
            entity.Status = Status;
            entity.UserId = UserId;
            entity.HasSystemAccess = HasSystemAccess;
            entity.Organization = Organization;
            entity.Department = Department;
            entity.Title = Title;
            entity.Location = Location;
            entity.EmployeeId = EmployeeId;
            entity.ProfilePictureUrl = ProfilePictureUrl;
            entity.Bio = Bio;
            entity.CommunicationPreferences = CommunicationPreferences;
            entity.WorkingHours = WorkingHours;
            entity.TimeZone = TimeZone;
            entity.HireDate = HireDate;
            entity.TerminationDate = TerminationDate;
            entity.Notes = Notes;
        }
    }

    public class PersonEditViewModel : PersonCreateViewModel
    {
        public int Id { get; set; }

        [Display(Name = "Person Code")]
        public string PersonCode { get; set; } = string.Empty;

        // Additional properties for editing can be added here

        public static PersonEditViewModel FromEntity(Person person)
        {
            return new PersonEditViewModel
            {
                Id = person.Id,
                PersonCode = person.PersonCode,
                FirstName = person.FirstName,
                LastName = person.LastName,
                Email = person.Email,
                Phone = person.Phone,
                Type = person.Type,
                Status = person.Status,
                UserId = person.UserId,
                HasSystemAccess = person.HasSystemAccess,
                Organization = person.Organization,
                Department = person.Department,
                Title = person.Title,
                Location = person.Location,
                EmployeeId = person.EmployeeId,
                ProfilePictureUrl = person.ProfilePictureUrl,
                Bio = person.Bio,
                CommunicationPreferences = person.CommunicationPreferences,
                WorkingHours = person.WorkingHours,
                TimeZone = person.TimeZone,
                HireDate = person.HireDate,
                TerminationDate = person.TerminationDate,
                Notes = person.Notes
            };
        }

        public new void UpdateEntity(Person entity)
        {
            base.UpdateEntity(entity);
            entity.PersonCode = PersonCode;
        }
    }
}
