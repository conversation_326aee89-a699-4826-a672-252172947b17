@*
    Professional Search Component - Usage Examples:

    1. Basic search:
    @{
        ViewData["Placeholder"] = "Search projects...";
        ViewData["SearchAction"] = Url.Action("Search", "Projects");
        ViewData["ShowFilters"] = true;
    }
    <partial name="Components/_Search" view-data="ViewData" />

    2. Advanced search with suggestions:
    @{
        ViewData["Placeholder"] = "Search anything...";
        ViewData["ShowSuggestions"] = true;
        ViewData["ShowRecentSearches"] = true;
        ViewData["Categories"] = new[] { "Projects", "Tasks", "Documents", "People" };
    }
    <partial name="Components/_Search" view-data="ViewData" />
*@

@{
    var placeholder = ViewData["Placeholder"]?.ToString() ?? "Search...";
    var searchAction = ViewData["SearchAction"]?.ToString() ?? Url.Action("Search", "Home");
    var searchMethod = ViewData["SearchMethod"]?.ToString() ?? "GET";
    var searchParam = ViewData["SearchParam"]?.ToString() ?? "q";
    var showFilters = ViewData["ShowFilters"] as bool? ?? false;
    var showSuggestions = ViewData["ShowSuggestions"] as bool? ?? false;
    var showRecentSearches = ViewData["ShowRecentSearches"] as bool? ?? false;
    var showCategories = ViewData["ShowCategories"] as bool? ?? false;
    var categories = ViewData["Categories"] as string[] ?? new string[0];
    var size = ViewData["Size"]?.ToString() ?? "md"; // sm, md, lg
    var variant = ViewData["Variant"]?.ToString() ?? "default"; // default, compact, expanded
    var searchId = ViewData["SearchId"]?.ToString() ?? $"search_{Guid.NewGuid().ToString("N")[..8]}";
    var autoFocus = ViewData["AutoFocus"] as bool? ?? false;
    var showShortcuts = ViewData["ShowShortcuts"] as bool? ?? true;
    
    var sizeClasses = size switch {
        "sm" => "h-10 text-sm",
        "md" => "h-12 text-base",
        "lg" => "h-14 text-lg",
        _ => "h-12 text-base"
    };
    
    var containerClasses = variant switch {
        "compact" => "max-w-md",
        "expanded" => "max-w-2xl",
        _ => "max-w-lg"
    };
}

<div class="relative @containerClasses w-full">
    <form action="@searchAction" method="@searchMethod" class="relative">
        <!-- Search Input -->
        <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <i class="fas fa-search text-neutral-400 dark:text-dark-500"></i>
            </div>
            
            <input type="text" 
                   id="@searchId" 
                   name="@searchParam"
                   class="form-input-custom pl-12 pr-20 @sizeClasses w-full rounded-xl border-neutral-300 dark:border-neutral-600 focus:border-primary-500 focus:ring-primary-500"
                   placeholder="@placeholder"
                   autocomplete="off"
                   @(autoFocus ? "autofocus" : "")
                   data-search-input>
            
            <!-- Search Actions -->
            <div class="absolute inset-y-0 right-0 flex items-center pr-3 space-x-2">
                @if (showShortcuts)
                {
                    <div class="hidden sm:flex items-center space-x-1 text-xs text-neutral-400 dark:text-dark-500">
                        <kbd class="px-2 py-1 bg-neutral-100 dark:bg-dark-600 rounded border border-neutral-300 dark:border-dark-400">⌘</kbd>
                        <kbd class="px-2 py-1 bg-neutral-100 dark:bg-dark-600 rounded border border-neutral-300 dark:border-dark-400">K</kbd>
                    </div>
                }
                
                @if (showFilters)
                {
                    <button type="button" 
                            class="p-1 text-neutral-400 dark:text-dark-500 hover:text-neutral-600 dark:hover:text-dark-300 transition-colors"
                            onclick="toggleSearchFilters('@searchId')">
                        <i class="fas fa-filter"></i>
                    </button>
                }
                
                <button type="submit" 
                        class="p-1 text-neutral-400 dark:text-dark-500 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                    <i class="fas fa-arrow-right"></i>
                </button>
            </div>
        </div>
        
        <!-- Search Filters -->
        @if (showFilters)
        {
            <div id="@(searchId)Filters" class="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-surface-dark border border-neutral-200 dark:border-dark-200 rounded-xl shadow-lg p-4 hidden z-50">
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-2">Date Range</label>
                        <select name="dateRange" class="form-select-custom w-full">
                            <option value="">Any time</option>
                            <option value="today">Today</option>
                            <option value="week">This week</option>
                            <option value="month">This month</option>
                            <option value="year">This year</option>
                        </select>
                    </div>
                    
                    @if (showCategories && categories.Any())
                    {
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-2">Category</label>
                            <select name="category" class="form-select-custom w-full">
                                <option value="">All categories</option>
                                @foreach (var category in categories)
                                {
                                    <option value="@category.ToLower()">@category</option>
                                }
                            </select>
                        </div>
                    }
                </div>
                
                <div class="flex justify-between items-center mt-4 pt-4 border-t border-neutral-200 dark:border-dark-200">
                    <button type="button" 
                            onclick="clearSearchFilters('@searchId')"
                            class="text-sm text-neutral-500 dark:text-dark-400 hover:text-neutral-700 dark:hover:text-dark-200">
                        Clear filters
                    </button>
                    <button type="button" 
                            onclick="toggleSearchFilters('@searchId')"
                            class="btn-primary-custom px-4 py-2 text-sm">
                        Apply filters
                    </button>
                </div>
            </div>
        }
    </form>
    
    <!-- Search Suggestions/Results -->
    <div id="@(searchId)Suggestions" class="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-surface-dark border border-neutral-200 dark:border-dark-200 rounded-xl shadow-lg max-h-96 overflow-y-auto hidden z-50">
        <!-- Loading State -->
        <div id="@(searchId)Loading" class="p-4 text-center hidden">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 mx-auto mb-2"></div>
            <p class="text-sm text-neutral-500 dark:text-dark-400">Searching...</p>
        </div>
        
        <!-- No Results -->
        <div id="@(searchId)NoResults" class="p-6 text-center hidden">
            <div class="w-12 h-12 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-3">
                <i class="fas fa-search text-neutral-400 dark:text-dark-500"></i>
            </div>
            <p class="text-sm text-neutral-500 dark:text-dark-400">No results found</p>
        </div>
        
        <!-- Recent Searches -->
        @if (showRecentSearches)
        {
            <div id="@(searchId)Recent" class="border-b border-neutral-200 dark:border-dark-200">
                <div class="p-3 bg-neutral-50 dark:bg-dark-700">
                    <h4 class="text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wide">Recent Searches</h4>
                </div>
                <div id="@(searchId)RecentList" class="py-2">
                    <!-- Recent searches will be populated here -->
                </div>
            </div>
        }
        
        <!-- Search Results -->
        <div id="@(searchId)Results" class="py-2">
            <!-- Search results will be populated here -->
        </div>
        
        <!-- Quick Actions -->
        <div class="border-t border-neutral-200 dark:border-dark-200 p-3 bg-neutral-50 dark:bg-dark-700">
            <div class="flex items-center justify-between text-xs text-neutral-500 dark:text-dark-400">
                <span>Press <kbd class="px-1 bg-neutral-200 dark:bg-dark-600 rounded">↵</kbd> to search</span>
                <span>Press <kbd class="px-1 bg-neutral-200 dark:bg-dark-600 rounded">Esc</kbd> to close</span>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            initializeSearch('@searchId');
            
            // Global search shortcut (Cmd/Ctrl + K)
            $(document).on('keydown', function(e) {
                if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
                    e.preventDefault();
                    $('#@searchId').focus();
                }
            });
        });

        function initializeSearch(searchId) {
            var input = $('#' + searchId);
            var suggestions = $('#' + searchId + 'Suggestions');
            var searchTimeout;

            // Input events
            input.on('input', function() {
                var query = $(this).val().trim();
                
                clearTimeout(searchTimeout);
                
                if (query.length >= 2) {
                    searchTimeout = setTimeout(function() {
                        performSearch(searchId, query);
                    }, 300);
                } else {
                    hideSuggestions(searchId);
                }
            });

            // Focus events
            input.on('focus', function() {
                var query = $(this).val().trim();
                if (query.length >= 2) {
                    showSuggestions(searchId);
                } else {
                    showRecentSearches(searchId);
                }
            });

            // Keyboard navigation
            input.on('keydown', function(e) {
                if (e.key === 'Escape') {
                    hideSuggestions(searchId);
                } else if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    navigateSuggestions(searchId, 'down');
                } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    navigateSuggestions(searchId, 'up');
                } else if (e.key === 'Enter') {
                    var selected = suggestions.find('.suggestion-item.selected');
                    if (selected.length) {
                        e.preventDefault();
                        selected.click();
                    }
                }
            });

            // Click outside to close
            $(document).on('click', function(e) {
                if (!$(e.target).closest('#' + searchId + ', #' + searchId + 'Suggestions').length) {
                    hideSuggestions(searchId);
                }
            });
        }

        function performSearch(searchId, query) {
            var suggestions = $('#' + searchId + 'Suggestions');
            var loading = $('#' + searchId + 'Loading');
            var results = $('#' + searchId + 'Results');
            var noResults = $('#' + searchId + 'NoResults');

            // Show loading
            showSuggestions(searchId);
            loading.removeClass('hidden');
            results.addClass('hidden');
            noResults.addClass('hidden');

            // Simulate API call (replace with actual search endpoint)
            setTimeout(function() {
                loading.addClass('hidden');
                
                // Mock results (replace with actual API response)
                var mockResults = generateMockResults(query);
                
                if (mockResults.length > 0) {
                    displayResults(searchId, mockResults);
                    results.removeClass('hidden');
                } else {
                    noResults.removeClass('hidden');
                }
                
                // Save to recent searches
                saveRecentSearch(query);
            }, 500);
        }

        function generateMockResults(query) {
            // Mock search results - replace with actual API call
            return [
                {
                    type: 'project',
                    title: 'Project Alpha',
                    description: 'Enterprise web application development',
                    url: '/projects/1',
                    icon: 'fas fa-project-diagram'
                },
                {
                    type: 'task',
                    title: 'Implement user authentication',
                    description: 'Add login and registration functionality',
                    url: '/tasks/123',
                    icon: 'fas fa-tasks'
                }
            ];
        }

        function displayResults(searchId, results) {
            var container = $('#' + searchId + 'Results');
            var html = '';

            results.forEach(function(result) {
                html += `
                    <a href="${result.url}" class="suggestion-item flex items-center p-3 hover:bg-neutral-50 dark:hover:bg-dark-700 transition-colors">
                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center mr-3">
                            <i class="${result.icon} text-primary-600 dark:text-primary-400 text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-sm font-medium text-neutral-900 dark:text-dark-100">${result.title}</h4>
                            <p class="text-xs text-neutral-500 dark:text-dark-400">${result.description}</p>
                        </div>
                        <div class="text-xs text-neutral-400 dark:text-dark-500 capitalize">${result.type}</div>
                    </a>
                `;
            });

            container.html(html);
        }

        function showSuggestions(searchId) {
            $('#' + searchId + 'Suggestions').removeClass('hidden');
        }

        function hideSuggestions(searchId) {
            $('#' + searchId + 'Suggestions').addClass('hidden');
        }

        function showRecentSearches(searchId) {
            var recent = getRecentSearches();
            if (recent.length > 0) {
                var container = $('#' + searchId + 'RecentList');
                var html = '';

                recent.forEach(function(search) {
                    html += `
                        <button type="button" class="suggestion-item w-full text-left flex items-center p-3 hover:bg-neutral-50 dark:hover:bg-dark-700 transition-colors" onclick="selectRecentSearch('${searchId}', '${search}')">
                            <i class="fas fa-clock text-neutral-400 dark:text-dark-500 mr-3"></i>
                            <span class="text-sm text-neutral-700 dark:text-dark-200">${search}</span>
                        </button>
                    `;
                });

                container.html(html);
                showSuggestions(searchId);
            }
        }

        function selectRecentSearch(searchId, query) {
            $('#' + searchId).val(query);
            performSearch(searchId, query);
        }

        function saveRecentSearch(query) {
            var recent = getRecentSearches();
            recent = recent.filter(item => item !== query);
            recent.unshift(query);
            recent = recent.slice(0, 5); // Keep only 5 recent searches
            localStorage.setItem('recentSearches', JSON.stringify(recent));
        }

        function getRecentSearches() {
            try {
                return JSON.parse(localStorage.getItem('recentSearches') || '[]');
            } catch {
                return [];
            }
        }

        function toggleSearchFilters(searchId) {
            $('#' + searchId + 'Filters').toggleClass('hidden');
        }

        function clearSearchFilters(searchId) {
            $('#' + searchId + 'Filters').find('select').val('');
        }

        function navigateSuggestions(searchId, direction) {
            var items = $('#' + searchId + 'Suggestions .suggestion-item');
            var selected = items.filter('.selected');
            
            items.removeClass('selected');
            
            if (direction === 'down') {
                if (selected.length === 0) {
                    items.first().addClass('selected');
                } else {
                    var next = selected.next('.suggestion-item');
                    if (next.length) {
                        next.addClass('selected');
                    } else {
                        items.first().addClass('selected');
                    }
                }
            } else {
                if (selected.length === 0) {
                    items.last().addClass('selected');
                } else {
                    var prev = selected.prev('.suggestion-item');
                    if (prev.length) {
                        prev.addClass('selected');
                    } else {
                        items.last().addClass('selected');
                    }
                }
            }
        }
    </script>
}
