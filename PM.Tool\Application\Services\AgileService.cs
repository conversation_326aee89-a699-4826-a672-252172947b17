using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Entities.Agile;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;

namespace PM.Tool.Application.Services
{
    public class AgileService : IAgileService
    {
        private readonly ApplicationDbContext _context;

        public AgileService(ApplicationDbContext context)
        {
            _context = context;
        }

        // Sprint Management
        public async Task<IEnumerable<Sprint>> GetProjectSprintsAsync(int projectId)
        {
            return await _context.Sprints
                .Where(s => s.ProjectId == projectId)
                .OrderByDescending(s => s.StartDate)
                .ToListAsync();
        }

        public async Task<Sprint?> GetSprintByIdAsync(int sprintId)
        {
            return await _context.Sprints
                .Include(s => s.Project)
                .Include(s => s.UserStories)
                .FirstOrDefaultAsync(s => s.Id == sprintId);
        }

        public async Task<Sprint?> GetActiveSprintAsync(int projectId)
        {
            return await _context.Sprints
                .Include(s => s.UserStories)
                .FirstOrDefaultAsync(s => s.ProjectId == projectId && s.Status == SprintStatus.Active);
        }

        public async Task<Sprint> CreateSprintAsync(Sprint sprint)
        {
            sprint.CreatedAt = DateTime.UtcNow;
            sprint.UpdatedAt = DateTime.UtcNow;

            _context.Sprints.Add(sprint);
            await _context.SaveChangesAsync();
            return sprint;
        }

        public async Task<Sprint> UpdateSprintAsync(Sprint sprint)
        {
            sprint.UpdatedAt = DateTime.UtcNow;

            _context.Sprints.Update(sprint);
            await _context.SaveChangesAsync();
            return sprint;
        }

        public async Task<bool> DeleteSprintAsync(int sprintId)
        {
            var sprint = await _context.Sprints.FindAsync(sprintId);
            if (sprint == null) return false;

            _context.Sprints.Remove(sprint);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> StartSprintAsync(int sprintId)
        {
            var sprint = await _context.Sprints.FindAsync(sprintId);
            if (sprint == null) return false;

            // Deactivate any other active sprints for this project
            var activeSprints = await _context.Sprints
                .Where(s => s.ProjectId == sprint.ProjectId && s.Status == SprintStatus.Active && s.Id != sprintId)
                .ToListAsync();

            foreach (var activeSprint in activeSprints)
            {
                activeSprint.Status = SprintStatus.Completed;
                activeSprint.UpdatedAt = DateTime.UtcNow;
            }

            sprint.Status = SprintStatus.Active;
            sprint.UpdatedAt = DateTime.UtcNow;

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> CompleteSprint(int sprintId)
        {
            var sprint = await _context.Sprints.FindAsync(sprintId);
            if (sprint == null) return false;

            sprint.Status = SprintStatus.Completed;
            sprint.UpdatedAt = DateTime.UtcNow;

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> CompleteSprintAsync(int sprintId)
        {
            return await CompleteSprint(sprintId);
        }

        // User Story Management
        public async Task<IEnumerable<UserStory>> GetProjectUserStoriesAsync(int projectId)
        {
            return await _context.UserStories
                .Include(us => us.Epic)
                .Include(us => us.AssignedTo)
                .Where(us => us.ProjectId == projectId)
                .OrderBy(us => us.Priority)
                .ToListAsync();
        }

        public async Task<IEnumerable<UserStory>> GetEpicUserStoriesAsync(int epicId)
        {
            return await _context.UserStories
                .Include(us => us.Epic)
                .Include(us => us.AssignedTo)
                .Where(us => us.EpicId == epicId)
                .OrderBy(us => us.Priority)
                .ToListAsync();
        }

        public async Task<IEnumerable<UserStory>> GetSprintUserStoriesAsync(int sprintId)
        {
            return await _context.UserStories
                .Include(us => us.Epic)
                .Include(us => us.AssignedTo)
                .Where(us => us.SprintId == sprintId)
                .OrderBy(us => us.Priority)
                .ToListAsync();
        }

        public async Task<IEnumerable<UserStory>> GetBacklogUserStoriesAsync(int projectId)
        {
            return await _context.UserStories
                .Include(us => us.Epic)
                .Include(us => us.AssignedTo)
                .Where(us => us.ProjectId == projectId && us.SprintId == null)
                .OrderBy(us => us.Priority)
                .ToListAsync();
        }

        public async Task<IEnumerable<UserStory>> GetProjectBacklogAsync(int projectId)
        {
            return await GetBacklogUserStoriesAsync(projectId);
        }

        public async Task<IEnumerable<UserStory>> GetSprintBacklogAsync(int sprintId)
        {
            return await _context.UserStories
                .Include(us => us.Epic)
                .Include(us => us.AssignedTo)
                .Where(us => us.SprintId == sprintId)
                .OrderBy(us => us.Priority)
                .ToListAsync();
        }

        public async Task<UserStory?> GetUserStoryByIdAsync(int userStoryId)
        {
            return await _context.UserStories
                .Include(us => us.Epic)
                .Include(us => us.AssignedTo)
                .Include(us => us.Sprint)
                .FirstOrDefaultAsync(us => us.Id == userStoryId);
        }

        public async Task<UserStory> CreateUserStoryAsync(UserStory userStory)
        {
            userStory.CreatedAt = DateTime.UtcNow;
            userStory.UpdatedAt = DateTime.UtcNow;

            _context.UserStories.Add(userStory);
            await _context.SaveChangesAsync();
            return userStory;
        }

        public async Task<UserStory> UpdateUserStoryAsync(UserStory userStory)
        {
            userStory.UpdatedAt = DateTime.UtcNow;

            _context.UserStories.Update(userStory);
            await _context.SaveChangesAsync();
            return userStory;
        }

        public async Task<bool> DeleteUserStoryAsync(int userStoryId)
        {
            var userStory = await _context.UserStories.FindAsync(userStoryId);
            if (userStory == null) return false;

            _context.UserStories.Remove(userStory);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> ReorderBacklogAsync(int projectId, List<int> userStoryIds)
        {
            for (int i = 0; i < userStoryIds.Count; i++)
            {
                var userStory = await _context.UserStories.FindAsync(userStoryIds[i]);
                if (userStory != null && userStory.ProjectId == projectId)
                {
                    userStory.BacklogOrder = i + 1;
                }
            }

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> MoveUserStoryToSprintAsync(int userStoryId, int? sprintId)
        {
            var userStory = await _context.UserStories.FindAsync(userStoryId);
            if (userStory == null) return false;

            userStory.SprintId = sprintId;
            userStory.UpdatedAt = DateTime.UtcNow;

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> MoveUserStoryToColumnAsync(int userStoryId, int columnId)
        {
            var userStory = await _context.UserStories.FindAsync(userStoryId);
            if (userStory == null) return false;

            userStory.KanbanColumn = columnId;
            userStory.UpdatedAt = DateTime.UtcNow;

            return await _context.SaveChangesAsync() > 0;
        }

        // Epic Management
        public async Task<IEnumerable<Epic>> GetProjectEpicsAsync(int projectId)
        {
            return await _context.Epics
                .Include(e => e.UserStories)
                .Where(e => e.ProjectId == projectId)
                .OrderBy(e => e.Priority)
                .ToListAsync();
        }

        public async Task<Epic?> GetEpicByIdAsync(int epicId)
        {
            return await _context.Epics
                .Include(e => e.UserStories)
                .Include(e => e.Project)
                .FirstOrDefaultAsync(e => e.Id == epicId);
        }

        public async Task<Epic> CreateEpicAsync(Epic epic)
        {
            epic.CreatedAt = DateTime.UtcNow;
            epic.UpdatedAt = DateTime.UtcNow;

            _context.Epics.Add(epic);
            await _context.SaveChangesAsync();
            return epic;
        }

        public async Task<Epic> UpdateEpicAsync(Epic epic)
        {
            epic.UpdatedAt = DateTime.UtcNow;

            _context.Epics.Update(epic);
            await _context.SaveChangesAsync();
            return epic;
        }

        public async Task<bool> DeleteEpicAsync(int epicId)
        {
            var epic = await _context.Epics.FindAsync(epicId);
            if (epic == null) return false;

            _context.Epics.Remove(epic);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> ReorderEpicsAsync(int projectId, List<int> epicIds)
        {
            for (int i = 0; i < epicIds.Count; i++)
            {
                var epic = await _context.Epics.FindAsync(epicIds[i]);
                if (epic != null && epic.ProjectId == projectId)
                {
                    epic.SortOrder = i + 1;
                }
            }

            return await _context.SaveChangesAsync() > 0;
        }

        // Kanban Board
        public async Task<Dictionary<int, IEnumerable<UserStory>>> GetKanbanBoardAsync(int projectId)
        {
            var userStories = await _context.UserStories
                .Include(us => us.AssignedTo)
                .Include(us => us.Epic)
                .Where(us => us.ProjectId == projectId)
                .ToListAsync();

            // Group user stories by kanban column
            var kanbanBoard = new Dictionary<int, IEnumerable<UserStory>>();

            for (int column = 1; column <= 5; column++)
            {
                kanbanBoard[column] = userStories.Where(us => us.KanbanColumn == column).ToList();
            }

            return kanbanBoard;
        }

        public async Task<KanbanBoard> GetKanbanBoardStructureAsync(int projectId)
        {
            // For now, create a simple kanban board structure
            // In a real implementation, you might want to store column configurations in the database
            var userStories = await _context.UserStories
                .Include(us => us.AssignedTo)
                .Include(us => us.Epic)
                .Where(us => us.ProjectId == projectId)
                .ToListAsync();

            return new KanbanBoard
            {
                ProjectId = projectId,
                Columns = new List<KanbanColumn>
                {
                    new KanbanColumn { Id = 1, Name = "Backlog", Order = 1, Color = "#64748b", ProjectId = projectId },
                    new KanbanColumn { Id = 2, Name = "To Do", Order = 2, Color = "#3b82f6", ProjectId = projectId },
                    new KanbanColumn { Id = 3, Name = "In Progress", Order = 3, Color = "#f59e0b", ProjectId = projectId },
                    new KanbanColumn { Id = 4, Name = "Review", Order = 4, Color = "#8b5cf6", ProjectId = projectId },
                    new KanbanColumn { Id = 5, Name = "Done", Order = 5, Color = "#10b981", ProjectId = projectId }
                },
                UserStories = userStories
            };
        }

        public async Task<IEnumerable<KanbanColumn>> GetKanbanColumnsAsync(int projectId)
        {
            // Return default columns for now
            return new List<KanbanColumn>
            {
                new KanbanColumn { Id = 1, Name = "Backlog", Order = 1, Color = "#64748b", ProjectId = projectId },
                new KanbanColumn { Id = 2, Name = "To Do", Order = 2, Color = "#3b82f6", ProjectId = projectId },
                new KanbanColumn { Id = 3, Name = "In Progress", Order = 3, Color = "#f59e0b", ProjectId = projectId },
                new KanbanColumn { Id = 4, Name = "Review", Order = 4, Color = "#8b5cf6", ProjectId = projectId },
                new KanbanColumn { Id = 5, Name = "Done", Order = 5, Color = "#10b981", ProjectId = projectId }
            };
        }

        public async Task<KanbanColumn> CreateKanbanColumnAsync(KanbanColumn column)
        {
            // TODO: Implement when KanbanColumn is added to DbContext
            return column;
        }

        public async Task<bool> UpdateKanbanColumnOrderAsync(int projectId, Dictionary<int, int> columnOrders)
        {
            // TODO: Implement when KanbanColumn is added to DbContext
            return true;
        }

        public async Task<bool> ReorderKanbanColumnAsync(int projectId, int columnId, List<int> userStoryIds)
        {
            for (int i = 0; i < userStoryIds.Count; i++)
            {
                var userStory = await _context.UserStories.FindAsync(userStoryIds[i]);
                if (userStory != null && userStory.ProjectId == projectId && userStory.KanbanColumn == columnId)
                {
                    userStory.BacklogOrder = i + 1;
                }
            }

            return await _context.SaveChangesAsync() > 0;
        }

        // Sprint Planning
        public async Task<bool> AddUserStoryToSprintAsync(int userStoryId, int sprintId)
        {
            return await MoveUserStoryToSprintAsync(userStoryId, sprintId);
        }

        public async Task<bool> RemoveUserStoryFromSprintAsync(int userStoryId)
        {
            return await MoveUserStoryToSprintAsync(userStoryId, null);
        }

        public async Task<IEnumerable<UserStory>> GetAvailableUserStoriesForSprintAsync(int projectId)
        {
            return await GetBacklogUserStoriesAsync(projectId);
        }

        public async Task<decimal> GetSprintCapacityAsync(int sprintId)
        {
            var sprint = await _context.Sprints.FindAsync(sprintId);
            if (sprint == null) return 0;

            return sprint.PlannedStoryPoints;
        }

        // Agile Analytics
        public async Task<Dictionary<string, object>> GetAgileAnalyticsAsync(int projectId)
        {
            var totalUserStories = await _context.UserStories.CountAsync(us => us.ProjectId == projectId);
            var completedUserStories = await _context.UserStories.CountAsync(us => us.ProjectId == projectId && us.Status == UserStoryStatus.Done);
            var totalStoryPoints = await _context.UserStories.Where(us => us.ProjectId == projectId).SumAsync(us => us.StoryPoints);
            var completedStoryPoints = await _context.UserStories.Where(us => us.ProjectId == projectId && us.Status == UserStoryStatus.Done).SumAsync(us => us.StoryPoints);

            return new Dictionary<string, object>
            {
                ["TotalUserStories"] = totalUserStories,
                ["CompletedUserStories"] = completedUserStories,
                ["CompletionRate"] = totalUserStories > 0 ? (double)completedUserStories / totalUserStories * 100 : 0,
                ["TotalStoryPoints"] = totalStoryPoints,
                ["CompletedStoryPoints"] = completedStoryPoints,
                ["StoryPointsCompletionRate"] = totalStoryPoints > 0 ? completedStoryPoints / totalStoryPoints * 100 : 0
            };
        }

        public async Task<IEnumerable<object>> GetVelocityDataAsync(int projectId)
        {
            return await GetVelocityDataAsync(projectId, 6);
        }

        public async Task<IEnumerable<object>> GetBurndownDataAsync(int sprintId)
        {
            var sprint = await _context.Sprints.FindAsync(sprintId);
            if (sprint == null) return new List<object>();

            var totalStoryPoints = await _context.UserStories
                .Where(us => us.SprintId == sprintId)
                .SumAsync(us => us.StoryPoints);

            // TODO: Implement actual burndown calculation based on daily completion data
            // For now, return a simple linear burndown
            var burndownData = new List<object>();
            var sprintDays = (sprint.EndDate - sprint.StartDate).Days;
            var dailyBurnRate = (decimal)totalStoryPoints / sprintDays;

            for (int day = 0; day <= sprintDays; day++)
            {
                var date = sprint.StartDate.AddDays(day);
                var remainingPoints = Math.Max(0, totalStoryPoints - (day * dailyBurnRate));

                burndownData.Add(new
                {
                    Date = date,
                    RemainingStoryPoints = remainingPoints,
                    IdealBurndown = Math.Max(0, totalStoryPoints - (day * dailyBurnRate))
                });
            }

            return burndownData;
        }

        // Agile Reporting
        public async Task<Dictionary<string, object>> GetEpicProgressReportAsync(int projectId)
        {
            var epics = await _context.Epics
                .Include(e => e.UserStories)
                .Where(e => e.ProjectId == projectId)
                .ToListAsync();

            var epicProgress = epics.Select(epic => new
            {
                EpicName = epic.Title,
                TotalStories = epic.UserStories.Count,
                CompletedStories = epic.UserStories.Count(us => us.Status == UserStoryStatus.Done),
                TotalStoryPoints = epic.UserStories.Sum(us => us.StoryPoints),
                CompletedStoryPoints = epic.UserStories.Where(us => us.Status == UserStoryStatus.Done).Sum(us => us.StoryPoints)
            }).ToList();

            return new Dictionary<string, object>
            {
                ["EpicProgress"] = epicProgress
            };
        }

        public async Task<Dictionary<string, object>> GetSprintReportAsync(int sprintId)
        {
            var sprint = await GetSprintByIdAsync(sprintId);
            if (sprint == null) return new Dictionary<string, object>();

            var userStories = sprint.UserStories ?? new List<UserStory>();
            var totalStoryPoints = userStories.Sum(us => us.StoryPoints);
            var completedStoryPoints = userStories.Where(us => us.Status == UserStoryStatus.Done).Sum(us => us.StoryPoints);

            return new Dictionary<string, object>
            {
                ["SprintName"] = sprint.Name,
                ["StartDate"] = sprint.StartDate,
                ["EndDate"] = sprint.EndDate,
                ["TotalUserStories"] = userStories.Count,
                ["CompletedUserStories"] = userStories.Count(us => us.Status == UserStoryStatus.Done),
                ["TotalStoryPoints"] = totalStoryPoints,
                ["CompletedStoryPoints"] = completedStoryPoints,
                ["Velocity"] = completedStoryPoints
            };
        }

        public async Task<IEnumerable<object>> GetCumulativeFlowDiagramDataAsync(int projectId, DateTime startDate, DateTime endDate)
        {
            // TODO: Implement cumulative flow diagram data
            // This would require tracking daily snapshots of user story statuses
            return new List<object>();
        }

        // Backlog Grooming
        public async Task<IEnumerable<UserStory>> GetUserStoriesNeedingGroomingAsync(int projectId)
        {
            return await _context.UserStories
                .Include(us => us.Epic)
                .Where(us => us.ProjectId == projectId &&
                           us.SprintId == null &&
                           (us.StoryPoints == 0 || us.AcceptanceCriteria == null || us.AcceptanceCriteria.Length < 10))
                .OrderBy(us => us.Priority)
                .ToListAsync();
        }

        public async Task<bool> MarkUserStoryAsReadyAsync(int userStoryId)
        {
            var userStory = await _context.UserStories.FindAsync(userStoryId);
            if (userStory == null) return false;

            userStory.Status = UserStoryStatus.Ready;
            userStory.UpdatedAt = DateTime.UtcNow;

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<IEnumerable<UserStory>> GetReadyUserStoriesAsync(int projectId)
        {
            return await _context.UserStories
                .Include(us => us.Epic)
                .Include(us => us.AssignedTo)
                .Where(us => us.ProjectId == projectId &&
                           us.SprintId == null &&
                           us.Status == UserStoryStatus.Ready)
                .OrderBy(us => us.Priority)
                .ToListAsync();
        }

        // Additional missing methods
        public async Task<IEnumerable<object>> GetVelocityDataAsync(int projectId, int numberOfSprints = 6)
        {
            var sprints = await _context.Sprints
                .Where(s => s.ProjectId == projectId && s.Status == SprintStatus.Completed)
                .OrderByDescending(s => s.StartDate)
                .Take(numberOfSprints)
                .OrderBy(s => s.StartDate)
                .ToListAsync();

            var velocityData = new List<object>();

            foreach (var sprint in sprints)
            {
                var completedStoryPoints = await _context.UserStories
                    .Where(us => us.SprintId == sprint.Id && us.Status == UserStoryStatus.Done)
                    .SumAsync(us => us.StoryPoints);

                velocityData.Add(new
                {
                    SprintName = sprint.Name,
                    StartDate = sprint.StartDate,
                    EndDate = sprint.EndDate,
                    CompletedStoryPoints = completedStoryPoints
                });
            }

            return velocityData;
        }

        public async Task<Dictionary<string, object>> GetSprintMetricsAsync(int sprintId)
        {
            return await GetSprintReportAsync(sprintId);
        }

        // Story Point Estimation
        public async Task<bool> EstimateUserStoryAsync(int userStoryId, decimal storyPoints)
        {
            var userStory = await _context.UserStories.FindAsync(userStoryId);
            if (userStory == null) return false;

            userStory.StoryPoints = storyPoints;
            userStory.UpdatedAt = DateTime.UtcNow;

            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<IEnumerable<UserStory>> GetUnestimatedUserStoriesAsync(int projectId)
        {
            return await _context.UserStories
                .Include(us => us.Epic)
                .Include(us => us.AssignedTo)
                .Where(us => us.ProjectId == projectId && us.StoryPoints == 0)
                .OrderBy(us => us.Priority)
                .ToListAsync();
        }

        public async Task<decimal> GetAverageVelocityAsync(int projectId, int numberOfSprints = 3)
        {
            var velocityData = await GetVelocityDataAsync(projectId, numberOfSprints);
            var velocities = velocityData.Cast<dynamic>().Select(v => (decimal)v.GetType().GetProperty("CompletedStoryPoints").GetValue(v)).ToList();

            return velocities.Any() ? velocities.Average() : 0;
        }

        // User Story Comments
        public async Task<IEnumerable<UserStoryComment>> GetUserStoryCommentsAsync(int userStoryId)
        {
            return await _context.UserStoryComments
                .Include(c => c.User)
                .Where(c => c.UserStoryId == userStoryId)
                .OrderByDescending(c => c.CreatedAt)
                .ToListAsync();
        }

        public async Task<UserStoryComment> AddCommentAsync(int userStoryId, string userId, string content, PM.Tool.Core.Entities.Agile.CommentType type = PM.Tool.Core.Entities.Agile.CommentType.General)
        {
            var comment = new UserStoryComment
            {
                UserStoryId = userStoryId,
                UserId = userId,
                Content = content,
                Type = type,
                CreatedAt = DateTime.UtcNow
            };

            _context.UserStoryComments.Add(comment);
            await _context.SaveChangesAsync();
            return comment;
        }

        public async Task<bool> DeleteCommentAsync(int commentId)
        {
            var comment = await _context.UserStoryComments.FindAsync(commentId);
            if (comment == null) return false;

            _context.UserStoryComments.Remove(comment);
            return await _context.SaveChangesAsync() > 0;
        }

        #region Feature Management

        public async Task<IEnumerable<Feature>> GetProjectFeaturesAsync(int projectId)
        {
            return await _context.Features
                .Include(f => f.Epic)
                .Include(f => f.Owner)
                .Include(f => f.UserStories)
                .Where(f => f.ProjectId == projectId)
                .OrderBy(f => f.SortOrder)
                .ThenBy(f => f.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Feature>> GetEpicFeaturesAsync(int epicId)
        {
            return await _context.Features
                .Include(f => f.Epic)
                .Include(f => f.Owner)
                .Include(f => f.UserStories)
                .Where(f => f.EpicId == epicId)
                .OrderBy(f => f.SortOrder)
                .ThenBy(f => f.CreatedAt)
                .ToListAsync();
        }

        public async Task<Feature?> GetFeatureByIdAsync(int featureId)
        {
            return await _context.Features
                .Include(f => f.Epic)
                .Include(f => f.Project)
                .Include(f => f.Owner)
                .Include(f => f.UserStories)
                    .ThenInclude(us => us.Tasks)
                .FirstOrDefaultAsync(f => f.Id == featureId);
        }

        public async Task<Feature> CreateFeatureAsync(Feature feature)
        {
            // Generate feature key if not provided
            if (string.IsNullOrEmpty(feature.FeatureKey))
            {
                var existingFeatures = await _context.Features
                    .Where(f => f.ProjectId == feature.ProjectId)
                    .CountAsync();
                feature.FeatureKey = $"FT-{existingFeatures + 1:D3}";
            }

            // Set sort order
            var maxSortOrder = await _context.Features
                .Where(f => f.EpicId == feature.EpicId)
                .MaxAsync(f => (int?)f.SortOrder) ?? 0;
            feature.SortOrder = maxSortOrder + 1;

            _context.Features.Add(feature);
            await _context.SaveChangesAsync();

            return await GetFeatureByIdAsync(feature.Id) ?? feature;
        }

        public async Task<Feature> UpdateFeatureAsync(Feature feature)
        {
            var existingFeature = await _context.Features.FindAsync(feature.Id);
            if (existingFeature == null)
                throw new InvalidOperationException($"Feature with ID {feature.Id} not found");

            // Update properties
            existingFeature.Title = feature.Title;
            existingFeature.Description = feature.Description;
            existingFeature.Status = feature.Status;
            existingFeature.Priority = feature.Priority;
            existingFeature.BusinessValue = feature.BusinessValue;
            existingFeature.AcceptanceCriteria = feature.AcceptanceCriteria;
            existingFeature.EstimatedStoryPoints = feature.EstimatedStoryPoints;
            existingFeature.OwnerId = feature.OwnerId;
            existingFeature.TargetDate = feature.TargetDate;
            existingFeature.Tags = feature.Tags;
            existingFeature.UpdatedAt = DateTime.UtcNow;

            // Update actual story points based on completed user stories
            var completedStoryPoints = await _context.UserStories
                .Where(us => us.FeatureId == feature.Id && us.Status == UserStoryStatus.Done)
                .SumAsync(us => us.StoryPoints);
            existingFeature.ActualStoryPoints = completedStoryPoints;

            await _context.SaveChangesAsync();
            return await GetFeatureByIdAsync(feature.Id) ?? existingFeature;
        }

        public async Task<bool> DeleteFeatureAsync(int featureId)
        {
            var feature = await _context.Features
                .Include(f => f.UserStories)
                .FirstOrDefaultAsync(f => f.Id == featureId);

            if (feature == null) return false;

            // Check if feature has user stories
            if (feature.UserStories.Any())
            {
                throw new InvalidOperationException("Cannot delete feature that has user stories. Move or delete user stories first.");
            }

            _context.Features.Remove(feature);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> MoveFeatureToEpicAsync(int featureId, int epicId)
        {
            var feature = await _context.Features.FindAsync(featureId);
            if (feature == null) return false;

            var epic = await _context.Epics.FindAsync(epicId);
            if (epic == null) return false;

            feature.EpicId = epicId;
            feature.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<Feature>> SearchFeaturesAsync(int projectId, string searchTerm)
        {
            var query = _context.Features
                .Include(f => f.Epic)
                .Include(f => f.Owner)
                .Where(f => f.ProjectId == projectId);

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                query = query.Where(f =>
                    f.Title.Contains(searchTerm) ||
                    f.Description.Contains(searchTerm) ||
                    f.FeatureKey.Contains(searchTerm) ||
                    (f.Tags != null && f.Tags.Contains(searchTerm)));
            }

            return await query
                .OrderBy(f => f.SortOrder)
                .ThenBy(f => f.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Feature>> GetFeaturesByStatusAsync(int projectId, FeatureStatus status)
        {
            return await _context.Features
                .Include(f => f.Epic)
                .Include(f => f.Owner)
                .Where(f => f.ProjectId == projectId && f.Status == status)
                .OrderBy(f => f.SortOrder)
                .ThenBy(f => f.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Feature>> GetFeaturesByPriorityAsync(int projectId, FeaturePriority priority)
        {
            return await _context.Features
                .Include(f => f.Epic)
                .Include(f => f.Owner)
                .Where(f => f.ProjectId == projectId && f.Priority == priority)
                .OrderBy(f => f.SortOrder)
                .ThenBy(f => f.CreatedAt)
                .ToListAsync();
        }

        #endregion

        #region Bug Management

        public async Task<IEnumerable<Bug>> GetProjectBugsAsync(int projectId)
        {
            return await _context.Bugs
                .Include(b => b.AssignedTo)
                .Include(b => b.ReportedBy)
                .Include(b => b.UserStory)
                .Include(b => b.Feature)
                .Where(b => b.ProjectId == projectId)
                .OrderByDescending(b => b.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Bug>> GetUserStoryBugsAsync(int userStoryId)
        {
            return await _context.Bugs
                .Include(b => b.AssignedTo)
                .Include(b => b.ReportedBy)
                .Include(b => b.UserStory)
                .Include(b => b.Feature)
                .Where(b => b.UserStoryId == userStoryId)
                .OrderByDescending(b => b.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Bug>> GetFeatureBugsAsync(int featureId)
        {
            return await _context.Bugs
                .Include(b => b.AssignedTo)
                .Include(b => b.ReportedBy)
                .Include(b => b.UserStory)
                .Include(b => b.Feature)
                .Where(b => b.FeatureId == featureId)
                .OrderByDescending(b => b.CreatedAt)
                .ToListAsync();
        }

        public async Task<Bug?> GetBugByIdAsync(int bugId)
        {
            return await _context.Bugs
                .Include(b => b.Project)
                .Include(b => b.AssignedTo)
                .Include(b => b.ReportedBy)
                .Include(b => b.UserStory)
                .Include(b => b.Feature)
                .Include(b => b.Comments)
                    .ThenInclude(c => c.User)
                .Include(b => b.Attachments)
                    .ThenInclude(a => a.UploadedBy)
                .FirstOrDefaultAsync(b => b.Id == bugId);
        }

        public async Task<Bug> CreateBugAsync(Bug bug)
        {
            // Generate bug key if not provided
            if (string.IsNullOrEmpty(bug.BugKey))
            {
                var existingBugs = await _context.Bugs
                    .Where(b => b.ProjectId == bug.ProjectId)
                    .CountAsync();
                bug.BugKey = $"BG-{existingBugs + 1:D3}";
            }

            _context.Bugs.Add(bug);
            await _context.SaveChangesAsync();

            return await GetBugByIdAsync(bug.Id) ?? bug;
        }

        public async Task<Bug> UpdateBugAsync(Bug bug)
        {
            var existingBug = await _context.Bugs.FindAsync(bug.Id);
            if (existingBug == null)
                throw new InvalidOperationException($"Bug with ID {bug.Id} not found");

            // Update properties
            existingBug.Title = bug.Title;
            existingBug.Description = bug.Description;
            existingBug.Severity = bug.Severity;
            existingBug.Priority = bug.Priority;
            existingBug.Status = bug.Status;
            existingBug.StepsToReproduce = bug.StepsToReproduce;
            existingBug.ExpectedResult = bug.ExpectedResult;
            existingBug.ActualResult = bug.ActualResult;
            existingBug.FoundInVersion = bug.FoundInVersion;
            existingBug.FixedInVersion = bug.FixedInVersion;
            existingBug.AssignedToUserId = bug.AssignedToUserId;
            existingBug.Tags = bug.Tags;
            existingBug.EstimatedHours = bug.EstimatedHours;
            existingBug.ActualHours = bug.ActualHours;
            existingBug.TargetDate = bug.TargetDate;
            existingBug.UpdatedAt = DateTime.UtcNow;

            // Set resolution date if bug is resolved
            if (bug.Status == BugStatus.Resolved && existingBug.ResolvedDate == null)
            {
                existingBug.ResolvedDate = DateTime.UtcNow;
            }

            // Set verified date if bug is verified
            if (bug.Status == BugStatus.Verified && existingBug.VerifiedDate == null)
            {
                existingBug.VerifiedDate = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
            return await GetBugByIdAsync(bug.Id) ?? existingBug;
        }

        public async Task<bool> DeleteBugAsync(int bugId)
        {
            var bug = await _context.Bugs
                .Include(b => b.Comments)
                .Include(b => b.Attachments)
                .FirstOrDefaultAsync(b => b.Id == bugId);

            if (bug == null) return false;

            // Remove related comments and attachments
            _context.BugComments.RemoveRange(bug.Comments);
            _context.BugAttachments.RemoveRange(bug.Attachments);
            _context.Bugs.Remove(bug);

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> AssignBugAsync(int bugId, string userId)
        {
            var bug = await _context.Bugs.FindAsync(bugId);
            if (bug == null) return false;

            bug.AssignedToUserId = userId;
            bug.Status = BugStatus.Assigned;
            bug.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ResolveBugAsync(int bugId, string resolution)
        {
            var bug = await _context.Bugs.FindAsync(bugId);
            if (bug == null) return false;

            bug.Status = BugStatus.Resolved;
            bug.ResolvedDate = DateTime.UtcNow;
            bug.UpdatedAt = DateTime.UtcNow;

            // Add resolution comment
            var comment = new BugComment
            {
                BugId = bugId,
                Content = $"Resolution: {resolution}",
                UserId = bug.AssignedToUserId ?? "system",
                Type = PM.Tool.Core.Entities.Agile.CommentType.Resolution,
                CreatedAt = DateTime.UtcNow
            };
            _context.BugComments.Add(comment);

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<Bug>> SearchBugsAsync(int projectId, string searchTerm)
        {
            var query = _context.Bugs
                .Include(b => b.AssignedTo)
                .Include(b => b.ReportedBy)
                .Where(b => b.ProjectId == projectId);

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                query = query.Where(b =>
                    b.Title.Contains(searchTerm) ||
                    b.Description.Contains(searchTerm) ||
                    b.BugKey.Contains(searchTerm) ||
                    (b.Tags != null && b.Tags.Contains(searchTerm)));
            }

            return await query
                .OrderByDescending(b => b.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Bug>> GetBugsBySeverityAsync(int projectId, BugSeverity severity)
        {
            return await _context.Bugs
                .Include(b => b.AssignedTo)
                .Include(b => b.ReportedBy)
                .Where(b => b.ProjectId == projectId && b.Severity == severity)
                .OrderByDescending(b => b.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Bug>> GetBugsByStatusAsync(int projectId, BugStatus status)
        {
            return await _context.Bugs
                .Include(b => b.AssignedTo)
                .Include(b => b.ReportedBy)
                .Where(b => b.ProjectId == projectId && b.Status == status)
                .OrderByDescending(b => b.CreatedAt)
                .ToListAsync();
        }

        #endregion

        #region Bug Comments

        public async Task<IEnumerable<BugComment>> GetBugCommentsAsync(int bugId)
        {
            return await _context.BugComments
                .Include(c => c.User)
                .Where(c => c.BugId == bugId)
                .OrderByDescending(c => c.CreatedAt)
                .ToListAsync();
        }

        public async Task<BugComment> AddBugCommentAsync(int bugId, string userId, string content, PM.Tool.Core.Entities.Agile.CommentType type = PM.Tool.Core.Entities.Agile.CommentType.General)
        {
            var comment = new BugComment
            {
                BugId = bugId,
                UserId = userId,
                Content = content,
                Type = type,
                CreatedAt = DateTime.UtcNow
            };

            _context.BugComments.Add(comment);
            await _context.SaveChangesAsync();
            return comment;
        }

        public async Task<bool> DeleteBugCommentAsync(int commentId)
        {
            var comment = await _context.BugComments.FindAsync(commentId);
            if (comment == null) return false;

            _context.BugComments.Remove(comment);
            return await _context.SaveChangesAsync() > 0;
        }

        #endregion

        #region Test Case Management

        public async Task<IEnumerable<TestCase>> GetProjectTestCasesAsync(int projectId)
        {
            return await _context.TestCases
                .Include(tc => tc.CreatedBy)
                .Include(tc => tc.AssignedTo)
                .Include(tc => tc.UserStory)
                .Include(tc => tc.Feature)
                .Include(tc => tc.TestExecutions)
                .Where(tc => tc.ProjectId == projectId)
                .OrderBy(tc => tc.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<TestCase>> GetUserStoryTestCasesAsync(int userStoryId)
        {
            return await _context.TestCases
                .Include(tc => tc.CreatedBy)
                .Include(tc => tc.AssignedTo)
                .Include(tc => tc.TestExecutions)
                .Where(tc => tc.UserStoryId == userStoryId)
                .OrderBy(tc => tc.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<TestCase>> GetFeatureTestCasesAsync(int featureId)
        {
            return await _context.TestCases
                .Include(tc => tc.CreatedBy)
                .Include(tc => tc.AssignedTo)
                .Include(tc => tc.TestExecutions)
                .Where(tc => tc.FeatureId == featureId)
                .OrderBy(tc => tc.CreatedAt)
                .ToListAsync();
        }

        public async Task<TestCase?> GetTestCaseByIdAsync(int testCaseId)
        {
            return await _context.TestCases
                .Include(tc => tc.Project)
                .Include(tc => tc.CreatedBy)
                .Include(tc => tc.AssignedTo)
                .Include(tc => tc.UserStory)
                .Include(tc => tc.Feature)
                .Include(tc => tc.TestExecutions)
                    .ThenInclude(te => te.ExecutedBy)
                .FirstOrDefaultAsync(tc => tc.Id == testCaseId);
        }

        public async Task<TestCase> CreateTestCaseAsync(TestCase testCase)
        {
            // Generate test case key if not provided
            if (string.IsNullOrEmpty(testCase.TestCaseKey))
            {
                var existingTestCases = await _context.TestCases
                    .Where(tc => tc.ProjectId == testCase.ProjectId)
                    .CountAsync();
                testCase.TestCaseKey = $"TC-{existingTestCases + 1:D3}";
            }

            _context.TestCases.Add(testCase);
            await _context.SaveChangesAsync();

            return await GetTestCaseByIdAsync(testCase.Id) ?? testCase;
        }

        public async Task<TestCase> UpdateTestCaseAsync(TestCase testCase)
        {
            var existingTestCase = await _context.TestCases.FindAsync(testCase.Id);
            if (existingTestCase == null)
                throw new InvalidOperationException($"Test case with ID {testCase.Id} not found");

            // Update properties
            existingTestCase.Title = testCase.Title;
            existingTestCase.Description = testCase.Description;
            existingTestCase.Type = testCase.Type;
            existingTestCase.Priority = testCase.Priority;
            existingTestCase.Status = testCase.Status;
            existingTestCase.PreConditions = testCase.PreConditions;
            existingTestCase.TestSteps = testCase.TestSteps;
            existingTestCase.ExpectedResult = testCase.ExpectedResult;
            existingTestCase.Tags = testCase.Tags;
            existingTestCase.AssignedToUserId = testCase.AssignedToUserId;
            existingTestCase.EstimatedExecutionTime = testCase.EstimatedExecutionTime;
            existingTestCase.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return await GetTestCaseByIdAsync(testCase.Id) ?? existingTestCase;
        }

        public async Task<bool> DeleteTestCaseAsync(int testCaseId)
        {
            var testCase = await _context.TestCases
                .Include(tc => tc.TestExecutions)
                .FirstOrDefaultAsync(tc => tc.Id == testCaseId);

            if (testCase == null) return false;

            // Remove related test executions
            _context.TestExecutions.RemoveRange(testCase.TestExecutions);
            _context.TestCases.Remove(testCase);

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<TestCase>> SearchTestCasesAsync(int projectId, string searchTerm)
        {
            var query = _context.TestCases
                .Include(tc => tc.CreatedBy)
                .Include(tc => tc.AssignedTo)
                .Where(tc => tc.ProjectId == projectId);

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                query = query.Where(tc =>
                    tc.Title.Contains(searchTerm) ||
                    tc.Description!.Contains(searchTerm) ||
                    tc.TestCaseKey.Contains(searchTerm) ||
                    (tc.Tags != null && tc.Tags.Contains(searchTerm)));
            }

            return await query
                .OrderBy(tc => tc.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<TestCase>> GetTestCasesByTypeAsync(int projectId, TestCaseType type)
        {
            return await _context.TestCases
                .Include(tc => tc.CreatedBy)
                .Include(tc => tc.AssignedTo)
                .Where(tc => tc.ProjectId == projectId && tc.Type == type)
                .OrderBy(tc => tc.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<TestCase>> GetTestCasesByStatusAsync(int projectId, TestCaseStatus status)
        {
            return await _context.TestCases
                .Include(tc => tc.CreatedBy)
                .Include(tc => tc.AssignedTo)
                .Where(tc => tc.ProjectId == projectId && tc.Status == status)
                .OrderBy(tc => tc.CreatedAt)
                .ToListAsync();
        }

        #endregion

        #region Test Execution Management

        public async Task<IEnumerable<TestExecution>> GetTestCaseExecutionsAsync(int testCaseId)
        {
            return await _context.TestExecutions
                .Include(te => te.ExecutedBy)
                .Include(te => te.Bug)
                .Where(te => te.TestCaseId == testCaseId)
                .OrderByDescending(te => te.ExecutedAt)
                .ToListAsync();
        }

        public async Task<TestExecution> CreateTestExecutionAsync(TestExecution testExecution)
        {
            testExecution.ExecutedAt = DateTime.UtcNow;
            _context.TestExecutions.Add(testExecution);
            await _context.SaveChangesAsync();

            return testExecution;
        }

        public async Task<TestExecution> UpdateTestExecutionAsync(TestExecution testExecution)
        {
            var existingExecution = await _context.TestExecutions.FindAsync(testExecution.Id);
            if (existingExecution == null)
                throw new InvalidOperationException($"Test execution with ID {testExecution.Id} not found");

            existingExecution.Result = testExecution.Result;
            existingExecution.ActualResult = testExecution.ActualResult;
            existingExecution.Notes = testExecution.Notes;
            existingExecution.ExecutionTime = testExecution.ExecutionTime;
            existingExecution.BugId = testExecution.BugId;
            existingExecution.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return existingExecution;
        }

        public async Task<bool> DeleteTestExecutionAsync(int executionId)
        {
            var execution = await _context.TestExecutions.FindAsync(executionId);
            if (execution == null) return false;

            _context.TestExecutions.Remove(execution);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<TestExecution?> GetLatestTestExecutionAsync(int testCaseId)
        {
            return await _context.TestExecutions
                .Include(te => te.ExecutedBy)
                .Include(te => te.Bug)
                .Where(te => te.TestCaseId == testCaseId)
                .OrderByDescending(te => te.ExecutedAt)
                .FirstOrDefaultAsync();
        }

        #endregion

        #region Enhanced Reporting & Analytics

        public async Task<IEnumerable<object>> GetVelocityTrendsAsync(int projectId, int numberOfSprints = 6)
        {
            var sprints = await _context.Sprints
                .Where(s => s.ProjectId == projectId && s.Status == SprintStatus.Completed)
                .OrderByDescending(s => s.StartDate)
                .Take(numberOfSprints)
                .OrderBy(s => s.StartDate)
                .ToListAsync();

            var velocityTrends = new List<object>();

            foreach (var sprint in sprints)
            {
                var plannedStoryPoints = await _context.UserStories
                    .Where(us => us.SprintId == sprint.Id)
                    .SumAsync(us => us.StoryPoints);

                var completedStoryPoints = await _context.UserStories
                    .Where(us => us.SprintId == sprint.Id && us.Status == UserStoryStatus.Done)
                    .SumAsync(us => us.StoryPoints);

                var teamCapacity = sprint.PlannedStoryPoints;

                velocityTrends.Add(new
                {
                    SprintName = sprint.Name,
                    StartDate = sprint.StartDate,
                    EndDate = sprint.EndDate,
                    PlannedStoryPoints = plannedStoryPoints,
                    CompletedStoryPoints = completedStoryPoints,
                    TeamCapacity = teamCapacity,
                    CapacityUtilization = teamCapacity > 0 ? (completedStoryPoints / teamCapacity) * 100 : 0
                });
            }

            return velocityTrends;
        }

        public async Task<IEnumerable<object>> GetSprintBurndownDataAsync(int sprintId)
        {
            var sprint = await _context.Sprints.FindAsync(sprintId);
            if (sprint == null) return new List<object>();

            var userStories = await _context.UserStories
                .Where(us => us.SprintId == sprintId)
                .ToListAsync();

            var totalStoryPoints = userStories.Sum(us => us.StoryPoints);
            var burndownData = new List<object>();

            // Generate daily burndown data
            var currentDate = sprint.StartDate;
            var endDate = sprint.EndDate;
            while (currentDate <= endDate)
            {
                var completedStoryPoints = userStories
                    .Where(us => us.Status == UserStoryStatus.Done && us.UpdatedAt <= currentDate)
                    .Sum(us => us.StoryPoints);

                var remainingStoryPoints = totalStoryPoints - completedStoryPoints;
                var idealBurndown = CalculateIdealBurndown(totalStoryPoints, sprint.StartDate, endDate, currentDate);

                burndownData.Add(new
                {
                    Date = currentDate,
                    RemainingWork = remainingStoryPoints,
                    IdealBurndown = idealBurndown,
                    CompletedWork = completedStoryPoints
                });

                currentDate = currentDate.AddDays(1);
            }

            return burndownData;
        }

        public async Task<IEnumerable<object>> GetReleaseBurndownDataAsync(int projectId)
        {
            var features = await _context.Features
                .Where(f => f.ProjectId == projectId)
                .ToListAsync();

            var totalStoryPoints = features.Sum(f => f.EstimatedStoryPoints);
            var completedStoryPoints = features.Sum(f => f.ActualStoryPoints);

            return new List<object>
            {
                new
                {
                    Date = DateTime.UtcNow,
                    TotalStoryPoints = totalStoryPoints,
                    CompletedStoryPoints = completedStoryPoints,
                    RemainingStoryPoints = totalStoryPoints - completedStoryPoints,
                    ProgressPercentage = totalStoryPoints > 0 ? (completedStoryPoints / totalStoryPoints) * 100 : 0
                }
            };
        }

        public async Task<Dictionary<string, object>> GetTeamPerformanceAsync(int projectId, DateTime startDate, DateTime endDate)
        {
            var userStories = await _context.UserStories
                .Include(us => us.AssignedTo)
                .Where(us => us.ProjectId == projectId && us.UpdatedAt >= startDate && us.UpdatedAt <= endDate)
                .ToListAsync();

            var bugs = await _context.Bugs
                .Include(b => b.AssignedTo)
                .Where(b => b.ProjectId == projectId && b.UpdatedAt >= startDate && b.UpdatedAt <= endDate)
                .ToListAsync();

            var completedUserStories = userStories.Where(us => us.Status == UserStoryStatus.Done).ToList();
            var resolvedBugs = bugs.Where(b => b.IsResolved).ToList();

            return new Dictionary<string, object>
            {
                ["TotalUserStories"] = userStories.Count,
                ["CompletedUserStories"] = completedUserStories.Count,
                ["CompletionRate"] = userStories.Count > 0 ? (double)completedUserStories.Count / userStories.Count * 100 : 0,
                ["TotalBugs"] = bugs.Count,
                ["ResolvedBugs"] = resolvedBugs.Count,
                ["BugResolutionRate"] = bugs.Count > 0 ? (double)resolvedBugs.Count / bugs.Count * 100 : 0,
                ["AverageStoryPoints"] = completedUserStories.Any() ? completedUserStories.Average(us => us.StoryPoints) : 0,
                ["TotalStoryPointsCompleted"] = completedUserStories.Sum(us => us.StoryPoints)
            };
        }

        public async Task<Dictionary<string, object>> GetFeatureProgressReportAsync(int projectId)
        {
            var features = await _context.Features
                .Include(f => f.UserStories)
                .Where(f => f.ProjectId == projectId)
                .ToListAsync();

            var totalFeatures = features.Count;
            var completedFeatures = features.Count(f => f.IsCompleted);
            var inProgressFeatures = features.Count(f => f.Status == FeatureStatus.InProgress);

            return new Dictionary<string, object>
            {
                ["TotalFeatures"] = totalFeatures,
                ["CompletedFeatures"] = completedFeatures,
                ["InProgressFeatures"] = inProgressFeatures,
                ["CompletionRate"] = totalFeatures > 0 ? (double)completedFeatures / totalFeatures * 100 : 0,
                ["Features"] = features.Select(f => new
                {
                    f.Id,
                    f.Title,
                    f.FeatureKey,
                    f.Status,
                    f.Priority,
                    f.ProgressPercentage,
                    f.UserStoryCount,
                    f.CompletedUserStoryCount
                })
            };
        }

        public async Task<Dictionary<string, object>> GetBugAnalyticsAsync(int projectId)
        {
            var bugs = await _context.Bugs
                .Where(b => b.ProjectId == projectId)
                .ToListAsync();

            var totalBugs = bugs.Count;
            var openBugs = bugs.Count(b => !b.IsResolved);
            var resolvedBugs = bugs.Count(b => b.IsResolved);

            var bugsBySeverity = bugs.GroupBy(b => b.Severity)
                .ToDictionary(g => g.Key.ToString(), g => g.Count());

            var bugsByStatus = bugs.GroupBy(b => b.Status)
                .ToDictionary(g => g.Key.ToString(), g => g.Count());

            var averageResolutionTime = bugs.Where(b => b.ResolvedDate.HasValue)
                .Select(b => (b.ResolvedDate!.Value - b.CreatedAt).TotalDays)
                .DefaultIfEmpty(0)
                .Average();

            return new Dictionary<string, object>
            {
                ["TotalBugs"] = totalBugs,
                ["OpenBugs"] = openBugs,
                ["ResolvedBugs"] = resolvedBugs,
                ["ResolutionRate"] = totalBugs > 0 ? (double)resolvedBugs / totalBugs * 100 : 0,
                ["BugsBySeverity"] = bugsBySeverity,
                ["BugsByStatus"] = bugsByStatus,
                ["AverageResolutionTimeDays"] = averageResolutionTime
            };
        }

        public async Task<Dictionary<string, object>> GetTestCoverageReportAsync(int projectId)
        {
            var testCases = await _context.TestCases
                .Include(tc => tc.TestExecutions)
                .Where(tc => tc.ProjectId == projectId)
                .ToListAsync();

            var userStories = await _context.UserStories
                .Where(us => us.ProjectId == projectId)
                .CountAsync();

            var features = await _context.Features
                .Where(f => f.ProjectId == projectId)
                .CountAsync();

            var totalTestCases = testCases.Count;
            var executedTestCases = testCases.Count(tc => tc.TestExecutions.Any());
            var passedTestCases = testCases.Count(tc => tc.LastExecutionResult == TestExecutionResult.Passed);
            var failedTestCases = testCases.Count(tc => tc.LastExecutionResult == TestExecutionResult.Failed);

            var testCasesByType = testCases.GroupBy(tc => tc.Type)
                .ToDictionary(g => g.Key.ToString(), g => g.Count());

            return new Dictionary<string, object>
            {
                ["TotalTestCases"] = totalTestCases,
                ["ExecutedTestCases"] = executedTestCases,
                ["PassedTestCases"] = passedTestCases,
                ["FailedTestCases"] = failedTestCases,
                ["ExecutionRate"] = totalTestCases > 0 ? (double)executedTestCases / totalTestCases * 100 : 0,
                ["PassRate"] = executedTestCases > 0 ? (double)passedTestCases / executedTestCases * 100 : 0,
                ["TestCasesByType"] = testCasesByType,
                ["TestCoverageByUserStory"] = userStories > 0 ? (double)totalTestCases / userStories * 100 : 0,
                ["TestCoverageByFeature"] = features > 0 ? (double)totalTestCases / features * 100 : 0
            };
        }

        #endregion

        #region Helper Methods

        private decimal CalculateIdealBurndown(decimal totalWork, DateTime startDate, DateTime endDate, DateTime currentDate)
        {
            if (currentDate <= startDate) return totalWork;
            if (currentDate >= endDate) return 0;

            var totalDays = (endDate - startDate).TotalDays;
            var elapsedDays = (currentDate - startDate).TotalDays;
            var remainingPercentage = 1 - (elapsedDays / totalDays);

            return totalWork * (decimal)remainingPercentage;
        }

        #endregion
    }
}
