@model IEnumerable<PM.Tool.Models.ViewModels.ProjectViewModel>

@{
    ViewData["Title"] = "Projects";
    ViewData["Subtitle"] = "Manage and track your project portfolio";
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-project-diagram mr-3 text-primary-600 dark:text-primary-400"></i>
                Projects
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Manage and track your project portfolio
            </p>
        </div>
        <div class="mt-4 sm:mt-0">
            @{
                ViewData["Text"] = "Create New Project";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-plus";
                ViewData["Href"] = Url.Action("Create");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Project Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
    <!-- Total Projects Card -->
    <div class="card-custom hover:shadow-medium transition-all duration-300">
        <div class="card-body-custom">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-neutral-500 dark:text-dark-400">Total Projects</p>
                    <p class="text-3xl font-bold text-neutral-900 dark:text-dark-100">@Model.Count()</p>
                </div>
                <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-tasks text-primary-600 dark:text-primary-400 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Projects Card -->
    <div class="card-custom hover:shadow-medium transition-all duration-300">
        <div class="card-body-custom">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-neutral-500 dark:text-dark-400">Active Projects</p>
                    <p class="text-3xl font-bold text-neutral-900 dark:text-dark-100">@Model.Count(p => p.Status == PM.Tool.Core.Enums.ProjectStatus.Active)</p>
                </div>
                <div class="w-12 h-12 bg-success-100 dark:bg-success-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-check-circle text-success-600 dark:text-success-400 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Planning Projects Card -->
    <div class="card-custom hover:shadow-medium transition-all duration-300">
        <div class="card-body-custom">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-neutral-500 dark:text-dark-400">In Planning</p>
                    <p class="text-3xl font-bold text-neutral-900 dark:text-dark-100">@Model.Count(p => p.Status == PM.Tool.Core.Enums.ProjectStatus.Planning)</p>
                </div>
                <div class="w-12 h-12 bg-warning-100 dark:bg-warning-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-clock text-warning-600 dark:text-warning-400 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Completed Projects Card -->
    <div class="card-custom hover:shadow-medium transition-all duration-300">
        <div class="card-body-custom">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-neutral-500 dark:text-dark-400">Completed</p>
                    <p class="text-3xl font-bold text-neutral-900 dark:text-dark-100">@Model.Count(p => p.Status == PM.Tool.Core.Enums.ProjectStatus.Completed)</p>
                </div>
                <div class="w-12 h-12 bg-info-100 dark:bg-info-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-flag-checkered text-info-600 dark:text-info-400 text-xl"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Project Portfolio Table -->
<div class="card-custom">
    <div class="card-header-custom">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                <i class="fas fa-list text-primary-600 dark:text-primary-400 text-sm"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Project Portfolio</h3>
            </div>
        </div>
    </div>
    <div class="card-body-custom">
        @if (Model.Any())
        {
            <div class="overflow-x-auto">
                <table class="table-custom" id="projectsTable">
                    <thead class="table-header-custom">
                        <tr>
                            <th class="table-header-cell-custom">
                                <div class="flex items-center">
                                    <i class="fas fa-project-diagram mr-2 text-neutral-400 dark:text-dark-500"></i>
                                    @Html.DisplayNameFor(model => model.Name)
                                </div>
                            </th>
                            <th class="table-header-cell-custom">
                                <div class="flex items-center">
                                    <i class="fas fa-info-circle mr-2 text-neutral-400 dark:text-dark-500"></i>
                                    @Html.DisplayNameFor(model => model.Status)
                                </div>
                            </th>
                            <th class="table-header-cell-custom">
                                <div class="flex items-center">
                                    <i class="fas fa-calendar-alt mr-2 text-neutral-400 dark:text-dark-500"></i>
                                    @Html.DisplayNameFor(model => model.StartDate)
                                </div>
                            </th>
                            <th class="table-header-cell-custom">
                                <div class="flex items-center">
                                    <i class="fas fa-calendar-check mr-2 text-neutral-400 dark:text-dark-500"></i>
                                    @Html.DisplayNameFor(model => model.EndDate)
                                </div>
                            </th>
                            <th class="table-header-cell-custom">
                                <div class="flex items-center">
                                    <i class="fas fa-chart-line mr-2 text-neutral-400 dark:text-dark-500"></i>
                                    Progress
                                </div>
                            </th>
                            <th class="table-header-cell-custom">
                                <div class="flex items-center">
                                    <i class="fas fa-users mr-2 text-neutral-400 dark:text-dark-500"></i>
                                    Team
                                </div>
                            </th>
                            <th class="table-header-cell-custom">
                                <div class="flex items-center">
                                    <i class="fas fa-cogs mr-2 text-neutral-400 dark:text-dark-500"></i>
                                    Actions
                                </div>
                            </th>
                        </tr>
                    </thead>
                <tbody>
                    @foreach (var item in Model)
                    {
                        <tr class="hover:bg-neutral-50 dark:hover:bg-dark-800 transition-colors">
                            <td class="table-cell-custom">
                                <div>
                                    <h6 class="font-semibold text-neutral-900 dark:text-dark-100 mb-1">
                                        <a asp-action="Details" asp-route-id="@item.Id" class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors">
                                            @item.Name
                                        </a>
                                    </h6>
                                    @if (!string.IsNullOrEmpty(item.Description))
                                    {
                                        <p class="text-sm text-neutral-500 dark:text-dark-400">
                                            @item.Description.Substring(0, Math.Min(50, item.Description.Length))@(item.Description.Length > 50 ? "..." : "")
                                        </p>
                                    }
                                </div>
                            </td>
                            <td class="table-cell-custom">
                                @{
                                    var statusClass = item.StatusColor switch {
                                        "success" => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
                                        "primary" => "bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200",
                                        "warning" => "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
                                        "danger" => "bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200",
                                        "info" => "bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200",
                                        _ => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200"
                                    };
                                }
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @statusClass">
                                    @item.Status
                                </span>
                            </td>
                            <td class="table-cell-custom">
                                <span class="text-sm text-neutral-600 dark:text-dark-300">
                                    @item.StartDate.ToString("MMM dd, yyyy")
                                </span>
                            </td>
                            <td class="table-cell-custom">
                                @if (item.EndDate.HasValue)
                                {
                                    <div class="flex items-center">
                                        <span class="text-sm @(item.IsOverdue ? "text-danger-600 dark:text-danger-400" : "text-neutral-600 dark:text-dark-300")">
                                            @item.EndDate.Value.ToString("MMM dd, yyyy")
                                        </span>
                                        @if (item.IsOverdue)
                                        {
                                            <i class="fas fa-exclamation-triangle text-danger-500 ml-2" title="Overdue"></i>
                                        }
                                    </div>
                                }
                                else
                                {
                                    <span class="text-sm text-neutral-400 dark:text-dark-500">Not set</span>
                                }
                            </td>
                            <td class="table-cell-custom">
                                <div class="space-y-2">
                                    <div class="w-full bg-neutral-200 dark:bg-dark-700 rounded-full h-2">
                                        <div class="bg-primary-600 h-2 rounded-full transition-all duration-300"
                                             style="width: @item.ProgressPercentage%"
                                             role="progressbar"
                                             aria-valuenow="@item.ProgressPercentage"
                                             aria-valuemin="0"
                                             aria-valuemax="100">
                                        </div>
                                    </div>
                                    <span class="text-xs text-neutral-500 dark:text-dark-400">@item.ProgressPercentage.ToString("F0")%</span>
                                </div>
                            </td>
                            <td class="table-cell-custom">
                                <div class="flex items-center text-sm text-neutral-600 dark:text-dark-300">
                                    <i class="fas fa-users mr-2 text-neutral-400 dark:text-dark-500"></i>
                                    <span>@item.MemberCount members</span>
                                </div>
                            </td>
                            <td class="table-cell-custom">
                                <div class="flex items-center space-x-2">
                                    @{
                                        ViewData["Text"] = "";
                                        ViewData["Variant"] = "outline";
                                        ViewData["Size"] = "sm";
                                        ViewData["Icon"] = "fas fa-eye";
                                        ViewData["Href"] = Url.Action("Details", new { id = item.Id });
                                        ViewData["AriaLabel"] = "View Details";
                                    }
                                    <partial name="Components/_Button" view-data="ViewData" />

                                    @{
                                        ViewData["Icon"] = "fas fa-edit";
                                        ViewData["Href"] = Url.Action("Edit", new { id = item.Id });
                                        ViewData["AriaLabel"] = "Edit Project";
                                    }
                                    <partial name="Components/_Button" view-data="ViewData" />

                                    @{
                                        ViewData["Icon"] = "fas fa-tasks";
                                        ViewData["Href"] = Url.Action("Index", "Tasks", new { projectId = item.Id });
                                        ViewData["AriaLabel"] = "View Tasks";
                                    }
                                    <partial name="Components/_Button" view-data="ViewData" />
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }
        else
        {
            <div class="text-center py-16">
                <div class="w-24 h-24 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-project-diagram text-4xl text-neutral-400 dark:text-dark-500"></i>
                </div>
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">No Projects Found</h3>
                <p class="text-neutral-500 dark:text-dark-400 mb-8 max-w-md mx-auto">
                    Get started by creating your first project to begin managing your work efficiently.
                </p>
                @{
                    ViewData["Text"] = "Create Your First Project";
                    ViewData["Variant"] = "primary";
                    ViewData["Icon"] = "fas fa-plus";
                    ViewData["Href"] = Url.Action("Create");
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Initialize DataTables for enhanced table functionality
            if (typeof $.fn.DataTable !== 'undefined') {
                $('#projectsTable').DataTable({
                    responsive: true,
                    pageLength: 10,
                    order: [[2, 'desc']], // Sort by start date descending
                    columnDefs: [
                        { orderable: false, targets: [4, 6] } // Disable sorting for progress and actions columns
                    ],
                    language: {
                        search: "Search projects:",
                        lengthMenu: "Show _MENU_ projects per page",
                        info: "Showing _START_ to _END_ of _TOTAL_ projects",
                        paginate: {
                            first: "First",
                            last: "Last",
                            next: "Next",
                            previous: "Previous"
                        }
                    },
                    // Custom styling for dark mode compatibility
                    initComplete: function() {
                        // Apply Tailwind classes to DataTables elements
                        $('.dataTables_wrapper .dataTables_length select').addClass('form-select-custom');
                        $('.dataTables_wrapper .dataTables_filter input').addClass('form-input-custom');
                        $('.dataTables_wrapper .dataTables_paginate .paginate_button').addClass('px-3 py-2 text-sm');
                    }
                });
            }

            // Animate progress bars on page load
            setTimeout(function() {
                document.querySelectorAll('[role="progressbar"]').forEach(function(bar) {
                    const width = bar.style.width;
                    bar.style.width = '0%';
                    bar.style.transition = 'width 1s ease-in-out';
                    setTimeout(function() {
                        bar.style.width = width;
                    }, 100);
                });
            }, 500);

            // Add smooth hover animations to stats cards
            document.querySelectorAll('.card-custom').forEach(function(card) {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
}