using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Interfaces;
using PM.Tool.Models;
using PM.Tool.Models.ViewModels;

namespace PM.Tool.Infrastructure.Filters
{
    public class GlobalExceptionFilter : IExceptionFilter
    {
        private readonly ILogger<GlobalExceptionFilter> _logger;
        private readonly IWebHostEnvironment _env;
        private readonly IAuditService _auditService;

        public GlobalExceptionFilter(
            ILogger<GlobalExceptionFilter> logger,
            IWebHostEnvironment env,
            IAuditService auditService)
        {
            _logger = logger;
            _env = env;
            _auditService = auditService;
        }

        public async void OnException(ExceptionContext context)
        {
            var exception = context.Exception;
            var isDevelopment = _env.IsDevelopment();

            _logger.LogError(exception, "An unhandled exception occurred");

            // Log the error in audit trail
            await _auditService.LogAsync(
                AuditAction.Error,
                "System",
                null,
                $"Exception: {exception.Message}");

            // Create error model with appropriate details
            var errorModel = new ErrorViewModel
            {
                RequestId = System.Diagnostics.Activity.Current?.Id ?? context.HttpContext.TraceIdentifier,
                Message = isDevelopment ? exception.Message : "An error occurred while processing your request.",
                Details = isDevelopment ? exception.ToString() : null,
                StatusCode = GetStatusCode(exception)
            };

            // Set the result based on request type
            if (context.HttpContext.Request.Headers["X-Requested-With"] == "XMLHttpRequest")
            {
                context.Result = new JsonResult(new
                {
                    error = errorModel.Message,
                    details = errorModel.Details
                })
                {
                    StatusCode = errorModel.StatusCode
                };
            }
            else
            {
                context.Result = new ViewResult
                {
                    ViewName = "~/Views/Shared/Error.cshtml",
                    StatusCode = errorModel.StatusCode,
                    ViewData = new Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary(
                        new Microsoft.AspNetCore.Mvc.ModelBinding.EmptyModelMetadataProvider(),
                        new Microsoft.AspNetCore.Mvc.ModelBinding.ModelStateDictionary())
                    {
                        Model = errorModel
                    }
                };
            }

            context.ExceptionHandled = true;
        }

        private int GetStatusCode(Exception exception)
        {
            return exception switch
            {
                UnauthorizedAccessException => StatusCodes.Status403Forbidden,
                KeyNotFoundException => StatusCodes.Status404NotFound,
                InvalidOperationException => StatusCodes.Status400BadRequest,
                _ => StatusCodes.Status500InternalServerError
            };
        }
    }
}
