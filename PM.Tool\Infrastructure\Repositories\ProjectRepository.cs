using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Common;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Interfaces;
using PM.Tool.Core.Specifications;
using PM.Tool.Data;

namespace PM.Tool.Infrastructure.Repositories
{
    public class ProjectRepository : Repository<Project>, IProjectRepository
    {
        public ProjectRepository(ApplicationDbContext context) : base(context)
        {
        }

        public async Task<IEnumerable<Project>> GetProjectsByUserIdAsync(string userId)
        {
            var spec = new ProjectsByUserSpec(userId);
            return await FindWithSpecificationAsync(spec);
        }

        public async Task<IEnumerable<Project>> GetProjectsByStatusAsync(ProjectStatus status)
        {
            var spec = new ProjectsByStatusSpec(status);
            return await FindWithSpecificationAsync(spec);
        }

        public async Task<Project?> GetProjectWithMembersAsync(int projectId)
        {
            var spec = new ProjectWithFullDetailsSpec(projectId);
            return (await FindWithSpecificationAsync(spec)).FirstOrDefault();
        }

        public async Task<Project?> GetProjectWithTasksAsync(int projectId)
        {
            var spec = new ProjectWithFullDetailsSpec(projectId);
            return (await FindWithSpecificationAsync(spec)).FirstOrDefault();
        }

        public async Task<Project?> GetProjectWithMilestonesAsync(int projectId)
        {
            var spec = new ProjectWithFullDetailsSpec(projectId);
            return (await FindWithSpecificationAsync(spec)).FirstOrDefault();
        }

        public async Task<IEnumerable<Project>> GetOverdueProjectsAsync()
        {
            var spec = new OverdueProjectsSpec();
            return await FindWithSpecificationAsync(spec);
        }

        public async Task<IEnumerable<Project>> GetProjectsEndingInDaysAsync(int days)
        {
            var endDate = DateTime.UtcNow.AddDays(days);
            return await FindAsync(p => !p.IsDeleted && 
                                      p.Status != ProjectStatus.Completed && 
                                      p.EndDate.HasValue && 
                                      p.EndDate <= endDate);
        }
    }
}
