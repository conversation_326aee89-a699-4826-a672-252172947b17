using System.Net;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Interfaces;

namespace PM.Tool.Infrastructure.Middleware
{
    public class ErrorHandlingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<ErrorHandlingMiddleware> _logger;
        private readonly IWebHostEnvironment _env;
        private readonly IAuditService _auditService;

        public ErrorHandlingMiddleware(
            RequestDelegate next,
            ILogger<ErrorHandlingMiddleware> logger,
            IWebHostEnvironment env,
            IAuditService auditService)
        {
            _next = next;
            _logger = logger;
            _env = env;
            _auditService = auditService;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unhandled exception occurred");

                await _auditService.LogAsync(
                    AuditAction.Error,
                    "System",
                    null,
                    $"Middleware Exception: {ex.Message}");

                var isDevelopment = _env.IsDevelopment();

                context.Response.ContentType = "application/json";
                context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;

                await context.Response.WriteAsJsonAsync(new
                {
                    error = isDevelopment ? ex.Message : "An error occurred while processing your request.",
                    details = isDevelopment ? ex.ToString() : null
                });
            }
        }
    }

    public static class ErrorHandlingMiddlewareExtensions
    {
        public static IApplicationBuilder UseErrorHandling(this IApplicationBuilder app)
        {
            return app.UseMiddleware<ErrorHandlingMiddleware>();
        }
    }
}
