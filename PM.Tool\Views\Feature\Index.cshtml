@model PM.Tool.Models.ViewModels.FeatureListViewModel
@using PM.Tool.Core.Entities.Agile

@{
    ViewData["Title"] = "Features";
    ViewData["PageTitle"] = $"Features - {Model.ProjectName}";
    ViewData["PageDescription"] = "Manage project features, track progress, and monitor development status.";
}

@section Styles {
    <style>
        .feature-card {
            transition: all 0.2s ease-in-out;
        }
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .progress-bar {
            transition: width 0.3s ease-in-out;
        }
        .filter-chip {
            transition: all 0.2s ease-in-out;
        }
        .filter-chip:hover {
            transform: scale(1.05);
        }
        .filter-chip.active {
            background: #3b82f6;
            color: white;
        }
    </style>
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-puzzle-piece mr-3 text-primary-600 dark:text-primary-400"></i>
                Features
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                @Model.ProjectName - Manage and track feature development
            </p>
        </div>
        <div class="mt-4 sm:mt-0">
            @{
                ViewData["Text"] = "New Feature";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-plus";
                ViewData["Href"] = Url.Action("Create", new { projectId = Model.ProjectId });
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Metrics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    @{
        ViewData["Title"] = "Total Features";
        ViewData["Value"] = Model.Metrics.TotalFeatures.ToString();
        ViewData["Icon"] = "fas fa-puzzle-piece";
        ViewData["Color"] = "blue";
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />

    @{
        ViewData["Title"] = "Completed";
        ViewData["Value"] = Model.Metrics.CompletedFeatures.ToString();
        ViewData["Icon"] = "fas fa-check-circle";
        ViewData["Color"] = "green";
        ViewData["Subtitle"] = $"{(Model.Metrics.TotalFeatures > 0 ? (decimal)Model.Metrics.CompletedFeatures / Model.Metrics.TotalFeatures * 100 : 0):F1}%";
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />

    @{
        ViewData["Title"] = "In Progress";
        ViewData["Value"] = Model.Metrics.InProgressFeatures.ToString();
        ViewData["Icon"] = "fas fa-clock";
        ViewData["Color"] = "yellow";
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />

    @{
        ViewData["Title"] = "Overdue";
        ViewData["Value"] = Model.Metrics.OverdueFeatures.ToString();
        ViewData["Icon"] = "fas fa-exclamation-triangle";
        ViewData["Color"] = "red";
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />
</div>

<!-- Filters -->
@{
    ViewData["Title"] = "Filters & Search";
    ViewData["Icon"] = "fas fa-filter";
    ViewData["ShowHeader"] = true;
}
<partial name="Components/_Card" view-data="ViewData">
    <div class="space-y-4">
        <!-- Search -->
        <div class="flex flex-col sm:flex-row gap-4">
            <div class="flex-1">
                @{
                    ViewData["Label"] = "Search Features";
                    ViewData["Name"] = "searchTerm";
                    ViewData["Type"] = "text";
                    ViewData["Icon"] = "fas fa-search";
                    ViewData["Placeholder"] = "Search by title, description, or key...";
                    ViewData["Value"] = Model.FilterOptions.SearchTerm;
                }
                <partial name="Components/_FormInput" view-data="ViewData" />
            </div>
        </div>

        <!-- Filter Chips -->
        <div class="flex flex-wrap gap-2">
            <!-- Epic Filter -->
            @if (Model.Epics.Any())
            {
                <div class="flex flex-wrap gap-2">
                    <span class="text-sm font-medium text-neutral-700 dark:text-dark-300">Epic:</span>
                    <button class="filter-chip px-3 py-1 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-300 text-neutral-700 dark:text-dark-200 @(Model.FilterOptions.EpicId == null ? "active" : "")"
                            data-filter="epic" data-value="">All</button>
                    @foreach (var epic in Model.Epics)
                    {
                        <button class="filter-chip px-3 py-1 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-300 text-neutral-700 dark:text-dark-200 @(Model.FilterOptions.EpicId == epic.Id ? "active" : "")"
                                data-filter="epic" data-value="@epic.Id">@epic.Title</button>
                    }
                </div>
            }

            <!-- Status Filter -->
            <div class="flex flex-wrap gap-2">
                <span class="text-sm font-medium text-neutral-700 dark:text-dark-300">Status:</span>
                <button class="filter-chip px-3 py-1 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-300 text-neutral-700 dark:text-dark-200 @(Model.FilterOptions.Status == null ? "active" : "")"
                        data-filter="status" data-value="">All</button>
                @foreach (FeatureStatus status in Enum.GetValues<FeatureStatus>())
                {
                    <button class="filter-chip px-3 py-1 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-300 text-neutral-700 dark:text-dark-200 @(Model.FilterOptions.Status == status ? "active" : "")"
                            data-filter="status" data-value="@((int)status)">@status</button>
                }
            </div>

            <!-- Priority Filter -->
            <div class="flex flex-wrap gap-2">
                <span class="text-sm font-medium text-neutral-700 dark:text-dark-300">Priority:</span>
                <button class="filter-chip px-3 py-1 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-300 text-neutral-700 dark:text-dark-200 @(Model.FilterOptions.Priority == null ? "active" : "")"
                        data-filter="priority" data-value="">All</button>
                @foreach (FeaturePriority priority in Enum.GetValues<FeaturePriority>())
                {
                    <button class="filter-chip px-3 py-1 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-300 text-neutral-700 dark:text-dark-200 @(Model.FilterOptions.Priority == priority ? "active" : "")"
                            data-filter="priority" data-value="@((int)priority)">@priority</button>
                }
            </div>
        </div>
    </div>
</partial>

<!-- Features Grid -->
@{
    ViewData["Title"] = "Features";
    ViewData["Icon"] = "fas fa-list";
    ViewData["ShowHeader"] = true;
}
<partial name="Components/_Card" view-data="ViewData">
    @if (Model.Features.Any())
    {
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6" id="featuresGrid">
            @foreach (var feature in Model.Features)
            {
                <div class="feature-card bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6 hover:shadow-lg transition-all duration-200"
                     data-epic="@feature.EpicTitle" data-status="@feature.Status" data-priority="@feature.Priority"
                     data-search="@feature.Title.ToLower() @feature.FeatureKey.ToLower()">

                    <!-- Feature Header -->
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                                <span class="text-xs font-mono text-neutral-500 dark:text-dark-400 bg-neutral-100 dark:bg-dark-300 px-2 py-1 rounded">
                                    @feature.FeatureKey
                                </span>
                                <span class="px-2 py-1 text-xs font-medium rounded-full @GetStatusBadgeClass(feature.Status)">
                                    @feature.Status
                                </span>
                                <span class="px-2 py-1 text-xs font-medium rounded-full @GetPriorityBadgeClass(feature.Priority)">
                                    @feature.Priority
                                </span>
                            </div>
                            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">
                                <a href="@Url.Action("Details", new { id = feature.Id })" class="hover:text-primary-600 dark:hover:text-primary-400">
                                    @feature.Title
                                </a>
                            </h3>
                            <p class="text-sm text-neutral-600 dark:text-dark-300 mb-3">
                                Epic: @feature.EpicTitle
                            </p>
                        </div>
                    </div>

                    <!-- Progress Bar -->
                    <div class="mb-4">
                        <div class="flex justify-between text-sm mb-1">
                            <span class="text-neutral-600 dark:text-dark-300">Progress</span>
                            <span class="font-medium text-neutral-900 dark:text-dark-100">@feature.ProgressPercentage.ToString("F1")%</span>
                        </div>
                        <div class="w-full bg-neutral-200 dark:bg-dark-300 rounded-full h-2">
                            <div class="progress-bar bg-primary-600 dark:bg-primary-500 h-2 rounded-full" style="width: @feature.ProgressPercentage%"></div>
                        </div>
                    </div>

                    <!-- User Stories Count -->
                    <div class="flex items-center justify-between text-sm text-neutral-600 dark:text-dark-300 mb-4">
                        <span>User Stories: @feature.CompletedUserStoryCount/@feature.UserStoryCount</span>
                        @if (feature.TargetDate.HasValue)
                        {
                            <span class="@(feature.TargetDate < DateTime.Now ? "text-danger-600 dark:text-danger-400" : "")">
                                Due: @feature.TargetDate.Value.ToString("MMM dd")
                            </span>
                        }
                    </div>

                    <!-- Owner -->
                    @if (!string.IsNullOrEmpty(feature.OwnerName))
                    {
                        <div class="flex items-center text-sm text-neutral-600 dark:text-dark-300 mb-4">
                            <i class="fas fa-user mr-2"></i>
                            <span>@feature.OwnerName</span>
                        </div>
                    }

                    <!-- Actions -->
                    <div class="flex space-x-2 pt-4 border-t border-neutral-200 dark:border-dark-200">
                        @{
                            ViewData["Text"] = "View";
                            ViewData["Variant"] = "secondary";
                            ViewData["Size"] = "sm";
                            ViewData["Icon"] = "fas fa-eye";
                            ViewData["Href"] = Url.Action("Details", new { id = feature.Id });
                        }
                        <partial name="Components/_Button" view-data="ViewData" />

                        @{
                            ViewData["Text"] = "Edit";
                            ViewData["Variant"] = "primary";
                            ViewData["Size"] = "sm";
                            ViewData["Icon"] = "fas fa-edit";
                            ViewData["Href"] = Url.Action("Edit", new { id = feature.Id });
                        }
                        <partial name="Components/_Button" view-data="ViewData" />
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="text-center py-12">
            <i class="fas fa-puzzle-piece text-6xl text-neutral-300 dark:text-dark-400 mb-4"></i>
            <h3 class="text-lg font-medium text-neutral-900 dark:text-dark-100 mb-2">No Features Found</h3>
            <p class="text-neutral-500 dark:text-dark-400 mb-6">Get started by creating your first feature.</p>
            @{
                ViewData["Text"] = "Create Feature";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-plus";
                ViewData["Href"] = Url.Action("Create", new { projectId = Model.ProjectId });
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    }
</partial>

@functions {
    private string GetStatusBadgeClass(FeatureStatus status)
    {
        return status switch
        {
            FeatureStatus.Draft => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100",
            FeatureStatus.InProgress => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
            FeatureStatus.Testing => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            FeatureStatus.Completed => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            FeatureStatus.OnHold => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
            FeatureStatus.Cancelled => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100"
        };
    }

    private string GetPriorityBadgeClass(FeaturePriority priority)
    {
        return priority switch
        {
            FeaturePriority.Low => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            FeaturePriority.Medium => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            FeaturePriority.High => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
            FeaturePriority.Critical => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100"
        };
    }
}

@section Scripts {
    <script>
        $(document).ready(function() {
            setupFilters();
            setupSearch();
        });

        function setupFilters() {
            $('.filter-chip').on('click', function() {
                const filterType = $(this).data('filter');
                const filterValue = $(this).data('value');

                // Update active state
                $(this).siblings(`[data-filter="${filterType}"]`).removeClass('active');
                $(this).addClass('active');

                // Apply filter
                applyFilters();
            });
        }

        function setupSearch() {
            let searchTimeout;
            $('input[name="searchTerm"]').on('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    applyFilters();
                }, 300);
            });
        }

        function applyFilters() {
            const searchTerm = $('input[name="searchTerm"]').val().toLowerCase();
            const epicFilter = $('.filter-chip[data-filter="epic"].active').data('value');
            const statusFilter = $('.filter-chip[data-filter="status"].active').data('value');
            const priorityFilter = $('.filter-chip[data-filter="priority"].active').data('value');

            $('.feature-card').each(function() {
                const $card = $(this);
                const searchText = $card.data('search');
                const epic = $card.data('epic');
                const status = $card.data('status');
                const priority = $card.data('priority');

                let show = true;

                // Search filter
                if (searchTerm && !searchText.includes(searchTerm)) {
                    show = false;
                }

                // Epic filter
                if (epicFilter && epic !== epicFilter) {
                    show = false;
                }

                // Status filter
                if (statusFilter && status != statusFilter) {
                    show = false;
                }

                // Priority filter
                if (priorityFilter && priority != priorityFilter) {
                    show = false;
                }

                $card.toggle(show);
            });
        }
    </script>
}
