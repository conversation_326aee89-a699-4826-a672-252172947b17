@model ProjectViewModel

@{
    ViewData["Title"] = "Delete Project";
}

<!-- <PERSON> Header -->
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-exclamation-triangle mr-3 text-danger-600 dark:text-danger-400"></i>
                Delete Project
            </h1>
            <p class="mt-1 text-sm text-danger-600 dark:text-danger-400">
                Are you sure you want to delete this project? This action cannot be undone.
            </p>
        </div>
        <div class="flex space-x-3">
            @{
                ViewData["Text"] = "Back to Project";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Details", new { id = Model.Id });
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Delete Confirmation Card -->
<div class="max-w-4xl mx-auto">
    <div class="card-custom">
        <div class="card-header-custom">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-danger-100 dark:bg-danger-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-project-diagram text-danger-600 dark:text-danger-400 text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Project Details</h3>
                    <p class="text-sm text-neutral-500 dark:text-dark-400">Review the project information before deletion</p>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <!-- Warning Alert -->
            <div class="alert-danger-custom mb-6">
                <i class="fas fa-exclamation-triangle text-danger-600 dark:text-danger-400"></i>
                <div>
                    <p class="font-medium">Permanent Deletion Warning</p>
                    <p class="text-sm">This project and all associated data (tasks, documents, time logs, team assignments) will be permanently deleted. This action cannot be undone.</p>
                </div>
            </div>

            <!-- Project Information -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Basic Information -->
                <div class="space-y-6">
                    <div>
                        <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Project Name</h4>
                        <p class="text-lg font-semibold text-neutral-900 dark:text-dark-100">@Model.Name</p>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.Description))
                    {
                        <div>
                            <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Description</h4>
                            <p class="text-neutral-700 dark:text-dark-200 bg-neutral-50 dark:bg-dark-700 p-3 rounded-lg">@Model.Description</p>
                        </div>
                    }

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Status</h4>
                            @{
                                var statusClass = Model.Status.ToString().ToLower() switch {
                                    "completed" => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
                                    "active" => "bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200",
                                    "onhold" => "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
                                    "cancelled" => "bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200",
                                    _ => "bg-neutral-100 dark:bg-dark-600 text-neutral-800 dark:text-dark-200"
                                };
                            }
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium @statusClass">
                                @Model.Status
                            </span>
                        </div>

                        <div>
                            <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Team Size</h4>
                            <p class="text-neutral-900 dark:text-dark-100 font-medium">
                                <i class="fas fa-users mr-2 text-neutral-400 dark:text-dark-500"></i>
                                @Model.MemberCount members
                            </p>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.ClientName))
                    {
                        <div>
                            <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Client</h4>
                            <p class="text-neutral-900 dark:text-dark-100 font-medium">
                                <i class="fas fa-building mr-2 text-neutral-400 dark:text-dark-500"></i>
                                @Model.ClientName
                            </p>
                        </div>
                    }
                </div>

                <!-- Dates and Progress -->
                <div class="space-y-6">
                    <div class="space-y-4">
                        <div>
                            <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Start Date</h4>
                            <p class="text-neutral-900 dark:text-dark-100 font-medium">
                                <i class="fas fa-calendar-alt mr-2 text-neutral-400 dark:text-dark-500"></i>
                                @Model.StartDate.ToString("MMM dd, yyyy")
                            </p>
                        </div>

                        @if (Model.EndDate.HasValue)
                        {
                            <div>
                                <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">End Date</h4>
                                <p class="font-medium @(Model.IsOverdue ? "text-danger-600 dark:text-danger-400" : "text-neutral-900 dark:text-dark-100")">
                                    <i class="fas fa-calendar-check mr-2 @(Model.IsOverdue ? "text-danger-600 dark:text-danger-400" : "text-neutral-400 dark:text-dark-500")"></i>
                                    @Model.EndDate.Value.ToString("MMM dd, yyyy")
                                    @if (Model.IsOverdue)
                                    {
                                        <span class="ml-2 text-xs">(Overdue)</span>
                                    }
                                </p>
                            </div>
                        }
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-3">Progress</h4>
                        <div class="bg-neutral-50 dark:bg-dark-700 p-4 rounded-lg">
                            <div class="flex justify-between text-sm mb-2">
                                <span class="text-neutral-600 dark:text-dark-300">Completion</span>
                                <span class="font-medium text-neutral-900 dark:text-dark-100">@Model.ProgressPercentage.ToString("F1")%</span>
                            </div>
                            <div class="w-full bg-neutral-200 dark:bg-dark-600 rounded-full h-3">
                                @{
                                    var progressColor = Model.IsOverdue ? "bg-danger-500" :
                                                       Model.ProgressPercentage >= 75 ? "bg-success-500" :
                                                       Model.ProgressPercentage >= 50 ? "bg-primary-500" :
                                                       "bg-warning-500";
                                }
                                <div class="@progressColor h-3 rounded-full transition-all duration-500" style="width: @Model.ProgressPercentage%"></div>
                            </div>
                            <p class="text-xs text-neutral-500 dark:text-dark-400 mt-2">@Model.CompletedTasks of @Model.TotalTasks tasks completed</p>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Budget</h4>
                        <p class="text-neutral-900 dark:text-dark-100 font-medium">
                            <i class="fas fa-dollar-sign mr-2 text-neutral-400 dark:text-dark-500"></i>
                            @Model.Budget.ToString("C")
                        </p>
                    </div>

                    <div class="space-y-4">
                        <div>
                            <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Created By</h4>
                            <p class="text-neutral-900 dark:text-dark-100 font-medium">
                                <i class="fas fa-user mr-2 text-neutral-400 dark:text-dark-500"></i>
                                @Model.CreatedByName
                            </p>
                        </div>

                        <div>
                            <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 mb-2">Created On</h4>
                            <p class="text-neutral-900 dark:text-dark-100 font-medium">
                                <i class="fas fa-calendar-plus mr-2 text-neutral-400 dark:text-dark-500"></i>
                                @Model.CreatedAt.ToString("MMM dd, yyyy HH:mm")
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0 pt-8 border-t border-neutral-200 dark:border-dark-200">
                <div>
                    @{
                        ViewData["Text"] = "Cancel";
                        ViewData["Variant"] = "secondary";
                        ViewData["Icon"] = "fas fa-times";
                        ViewData["Href"] = Url.Action("Details", new { id = Model.Id });
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </div>
                <div>
                    <form asp-action="Delete" method="post" class="inline">
                        <input type="hidden" asp-for="Id" />
                        @Html.AntiForgeryToken()
                        @{
                            ViewData["Text"] = "Confirm Delete";
                            ViewData["Variant"] = "danger";
                            ViewData["Icon"] = "fas fa-trash";
                            ViewData["Type"] = "submit";
                            ViewData["Href"] = null;
                            ViewData["OnClick"] = "return confirm('Are you absolutely sure you want to delete this project?\\n\\nThis will permanently delete:\\n• All project tasks and subtasks\\n• All project documents and files\\n• All time tracking records\\n• All team member assignments\\n• All project milestones\\n\\nThis action cannot be undone.')";
                        }
                        <partial name="Components/_Button" view-data="ViewData" />
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
