using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;

using TaskStatus = PM.Tool.Core.Enums.TaskStatus;

namespace PM.Tool.Application.Services
{
    public class TaskService : ITaskService
    {
        private readonly ITaskRepository _taskRepository;
        private readonly ApplicationDbContext _context;

        public TaskService(ITaskRepository taskRepository, ApplicationDbContext context)
        {
            _taskRepository = taskRepository;
            _context = context;
        }

        public async Task<IEnumerable<TaskEntity>> GetAllTasksAsync()
        {
            return await _taskRepository.GetAllAsync();
        }

        public async Task<IEnumerable<TaskEntity>> GetProjectTasksAsync(int projectId)
        {
            return await _taskRepository.GetTasksByProjectIdAsync(projectId);
        }

        public async Task<IEnumerable<TaskEntity>> GetUserTasksAsync(string userId)
        {
            return await _taskRepository.GetTasksByUserIdAsync(userId);
        }

        public IQueryable<TaskEntity> GetUserTasksQuery(string userId)
        {
            return _context.Tasks
                .Include(t => t.Project)
                .Include(t => t.AssignedTo)
                .Include(t => t.CreatedBy)
                .Include(t => t.SubTasks)
                .Where(t => t.AssignedToUserId == userId && !t.IsDeleted);
        }

        public async Task<TaskEntity?> GetTaskByIdAsync(int id)
        {
            return await _taskRepository.GetByIdAsync(id);
        }

        public async Task<TaskEntity?> GetTaskWithDetailsAsync(int id)
        {
            return await _taskRepository.GetTaskWithSubTasksAsync(id);
        }

        public async Task<TaskEntity> CreateTaskAsync(TaskEntity task)
        {
            task.CreatedAt = DateTime.UtcNow;
            task.UpdatedAt = DateTime.UtcNow;

            var createdTask = await _taskRepository.AddAsync(task);
            await _taskRepository.SaveChangesAsync();

            return createdTask;
        }

        public async Task<TaskEntity> UpdateTaskAsync(TaskEntity task)
        {
            task.UpdatedAt = DateTime.UtcNow;

            if (task.Status == TaskStatus.Done && task.CompletedDate == null)
            {
                task.CompletedDate = DateTime.UtcNow;
            }

            await _taskRepository.UpdateAsync(task);
            await _taskRepository.SaveChangesAsync();

            return task;
        }

        public async Task<bool> DeleteTaskAsync(int id)
        {
            var task = await _taskRepository.GetByIdAsync(id);
            if (task == null) return false;

            await _taskRepository.DeleteAsync(task);
            return await _taskRepository.SaveChangesAsync();
        }

        public async Task<bool> AssignTaskAsync(int taskId, string userId)
        {
            var task = await _taskRepository.GetByIdAsync(taskId);
            if (task == null) return false;

            task.AssignedToUserId = userId;
            task.UpdatedAt = DateTime.UtcNow;

            await _taskRepository.UpdateAsync(task);
            return await _taskRepository.SaveChangesAsync();
        }

        public async Task<bool> UpdateTaskStatusAsync(int taskId, TaskStatus status)
        {
            var task = await _taskRepository.GetByIdAsync(taskId);
            if (task == null) return false;

            task.Status = status;
            task.UpdatedAt = DateTime.UtcNow;

            if (status == TaskStatus.Done && task.CompletedDate == null)
            {
                task.CompletedDate = DateTime.UtcNow;
            }

            await _taskRepository.UpdateAsync(task);
            return await _taskRepository.SaveChangesAsync();
        }

        public async Task<bool> UpdateTaskPriorityAsync(int taskId, TaskPriority priority)
        {
            var task = await _taskRepository.GetByIdAsync(taskId);
            if (task == null) return false;

            task.Priority = priority;
            task.UpdatedAt = DateTime.UtcNow;

            await _taskRepository.UpdateAsync(task);
            return await _taskRepository.SaveChangesAsync();
        }

        public async Task<IEnumerable<TaskEntity>> GetSubTasksAsync(int parentTaskId)
        {
            return await _taskRepository.GetSubTasksAsync(parentTaskId);
        }

        public async Task<TaskEntity> CreateSubTaskAsync(int parentTaskId, TaskEntity subTask)
        {
            subTask.ParentTaskId = parentTaskId;
            return await CreateTaskAsync(subTask);
        }

        public async Task<IEnumerable<TaskEntity>> GetOverdueTasksAsync()
        {
            return await _taskRepository.GetOverdueTasksAsync();
        }

        public async Task<IEnumerable<TaskEntity>> GetTasksDueSoonAsync(int days = 3)
        {
            return await _taskRepository.GetTasksDueInDaysAsync(days);
        }

        public async Task<bool> AddCommentToTaskAsync(int taskId, string userId, string content)
        {
            var comment = new TaskComment
            {
                TaskId = taskId,
                UserId = userId,
                Content = content,
                CreatedAt = DateTime.UtcNow
            };

            _context.TaskComments.Add(comment);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<IEnumerable<TaskComment>> GetTaskCommentsAsync(int taskId)
        {
            return await _context.TaskComments
                .Include(c => c.User)
                .Where(c => c.TaskId == taskId)
                .OrderByDescending(c => c.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<TaskEntity>> GetSiblingTasksAsync(int taskId)
        {
            var task = await _taskRepository.GetByIdAsync(taskId);
            if (task == null) return Enumerable.Empty<TaskEntity>();

            return await _context.Tasks
                .Where(t => t.ProjectId == task.ProjectId &&
                           t.ParentTaskId == task.ParentTaskId &&
                           !t.IsDeleted &&
                           t.Id != taskId)
                .OrderBy(t => t.SortOrder)
                .ToListAsync();
        }

        public async Task<bool> MoveTaskUpAsync(int taskId)
        {
            try
            {
                var task = await _taskRepository.GetByIdAsync(taskId);
                if (task == null) return false;

                var siblings = await GetSiblingTasksAsync(taskId);
                var taskAbove = siblings.Where(t => t.SortOrder < task.SortOrder)
                                      .OrderByDescending(t => t.SortOrder)
                                      .FirstOrDefault();

                if (taskAbove == null) return false; // Already at top

                // Swap sort orders
                var tempSortOrder = task.SortOrder;
                task.SortOrder = taskAbove.SortOrder;
                taskAbove.SortOrder = tempSortOrder;

                task.UpdatedAt = DateTime.UtcNow;
                taskAbove.UpdatedAt = DateTime.UtcNow;

                await _taskRepository.UpdateAsync(task);
                await _taskRepository.UpdateAsync(taskAbove);

                return await _taskRepository.SaveChangesAsync();
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> MoveTaskDownAsync(int taskId)
        {
            try
            {
                var task = await _taskRepository.GetByIdAsync(taskId);
                if (task == null) return false;

                var siblings = await GetSiblingTasksAsync(taskId);
                var taskBelow = siblings.Where(t => t.SortOrder > task.SortOrder)
                                      .OrderBy(t => t.SortOrder)
                                      .FirstOrDefault();

                if (taskBelow == null) return false; // Already at bottom

                // Swap sort orders
                var tempSortOrder = task.SortOrder;
                task.SortOrder = taskBelow.SortOrder;
                taskBelow.SortOrder = tempSortOrder;

                task.UpdatedAt = DateTime.UtcNow;
                taskBelow.UpdatedAt = DateTime.UtcNow;

                await _taskRepository.UpdateAsync(task);
                await _taskRepository.UpdateAsync(taskBelow);

                return await _taskRepository.SaveChangesAsync();
            }
            catch
            {
                return false;
            }
        }

        public async Task<TaskEntity> DuplicateTaskAsync(int taskId, string userId)
        {
            var originalTask = await _taskRepository.GetByIdAsync(taskId);
            if (originalTask == null) throw new ArgumentException("Task not found");

            var duplicatedTask = new TaskEntity
            {
                Title = $"{originalTask.Title} (Copy)",
                Description = originalTask.Description,
                Priority = originalTask.Priority,
                ProjectId = originalTask.ProjectId,
                ParentTaskId = originalTask.ParentTaskId,
                EstimatedHours = originalTask.EstimatedHours,
                CreatedByUserId = userId,
                Status = TaskStatus.ToDo,
                SortOrder = await GetNextSortOrderForParent(originalTask.ProjectId, originalTask.ParentTaskId)
            };

            await _taskRepository.AddAsync(duplicatedTask);
            await _taskRepository.SaveChangesAsync();

            return duplicatedTask;
        }

        public async Task<bool> ExportTaskAsync(int taskId, string format)
        {
            // This would typically generate a file and return a download link
            // For now, we'll just return true to indicate the operation was successful
            var task = await _taskRepository.GetByIdAsync(taskId);
            return task != null;
        }

        private async Task<int> GetNextSortOrderForParent(int projectId, int? parentTaskId)
        {
            var maxSortOrder = await _context.Tasks
                .Where(t => t.ProjectId == projectId &&
                           t.ParentTaskId == parentTaskId &&
                           !t.IsDeleted)
                .MaxAsync(t => (int?)t.SortOrder) ?? 0;

            return maxSortOrder + 1;
        }
    }
}
