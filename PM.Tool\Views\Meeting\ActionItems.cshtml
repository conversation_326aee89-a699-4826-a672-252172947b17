@model PM.Tool.Core.Entities.Meeting
@using PM.Tool.Core.Enums
@{
    ViewData["Title"] = "Meeting Action Items";
    var actionItems = ViewBag.ActionItems as IEnumerable<PM.Tool.Core.Entities.MeetingActionItem> ?? new List<PM.Tool.Core.Entities.MeetingActionItem>();
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Meetings", Href = Url.Action("Index", "Meeting"), Icon = "fas fa-calendar" },
        new { Text = Model.Title, Href = Url.Action("Details", new { id = Model.Id }), Icon = "fas fa-info-circle" },
        new { Text = "Action Items", Href = "", Icon = "fas fa-tasks" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- <PERSON> Header -->
<div class="mb-8">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-tasks mr-3 text-primary-600 dark:text-primary-400"></i>
                Action Items
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Manage follow-up tasks from "@Model.Title"
            </p>
        </div>
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
            @{
                ViewData["Text"] = "Add Action Item";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-plus";
                ViewData["OnClick"] = "showAddActionItemModal()";
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "Back to Meeting";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Details", new { id = Model.Id });
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Meeting Info Card -->
<div class="card-custom mb-8">
    <div class="card-body-custom">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-xl flex items-center justify-center">
                    <i class="fas fa-calendar text-primary-600 dark:text-primary-400"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">@Model.Title</h3>
                    <p class="text-sm text-neutral-500 dark:text-dark-400">
                        @Model.ScheduledDate.ToString("MMM dd, yyyy 'at' HH:mm") • @Model.Type
                    </p>
                </div>
            </div>
            <div class="text-right">
                <p class="text-sm text-neutral-500 dark:text-dark-400">Total Action Items</p>
                <p class="text-2xl font-bold text-neutral-900 dark:text-dark-100">@actionItems.Count()</p>
            </div>
        </div>
    </div>
</div>

<!-- Action Items Stats -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    @{
        var completedCount = actionItems.Count(ai => ai.Status == ActionItemStatus.Completed);
        var inProgressCount = actionItems.Count(ai => ai.Status == ActionItemStatus.InProgress);
        var pendingCount = actionItems.Count(ai => ai.Status == ActionItemStatus.Open);
        var overdueCount = actionItems.Count(ai => ai.IsOverdue);
    }

    @{
        ViewData["Title"] = "Completed";
        ViewData["Value"] = completedCount.ToString();
        ViewData["Icon"] = "fas fa-check-circle";
        ViewData["IconColor"] = "bg-gradient-to-br from-success-500 to-success-600";
        ViewData["Description"] = "Tasks finished";
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />

    @{
        ViewData["Title"] = "In Progress";
        ViewData["Value"] = inProgressCount.ToString();
        ViewData["Icon"] = "fas fa-clock";
        ViewData["IconColor"] = "bg-gradient-to-br from-primary-500 to-primary-600";
        ViewData["Description"] = "Currently active";
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />

    @{
        ViewData["Title"] = "Open";
        ViewData["Value"] = pendingCount.ToString();
        ViewData["Icon"] = "fas fa-pause-circle";
        ViewData["IconColor"] = "bg-gradient-to-br from-warning-500 to-warning-600";
        ViewData["Description"] = "Not started";
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />

    @{
        ViewData["Title"] = "Overdue";
        ViewData["Value"] = overdueCount.ToString();
        ViewData["Icon"] = "fas fa-exclamation-triangle";
        ViewData["IconColor"] = "bg-gradient-to-br from-danger-500 to-danger-600";
        ViewData["Description"] = "Past due date";
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />
</div>

<!-- Action Items List -->
<div class="card-custom">
    <div class="card-header-custom">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                    <i class="fas fa-list text-primary-600 dark:text-primary-400 text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Action Items</h3>
                    <p class="text-sm text-neutral-500 dark:text-dark-400">Follow-up tasks and assignments</p>
                </div>
            </div>

            <!-- Filter Controls -->
            <div class="flex items-center space-x-3">
                <select id="statusFilter" class="form-select-custom">
                    <option value="">All Status</option>
                    <option value="open">Open</option>
                    <option value="inprogress">In Progress</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                    <option value="deferred">Deferred</option>
                </select>

                <select id="assigneeFilter" class="form-select-custom">
                    <option value="">All Assignees</option>
                    <!-- Will be populated via JavaScript -->
                </select>
            </div>
        </div>
    </div>
    <div class="card-body-custom">
        @if (actionItems.Any())
        {
            <div class="space-y-4" id="actionItemsList">
                @foreach (var item in actionItems.OrderBy(ai => ai.DueDate))
                {
                    <div class="action-item-card p-6 border border-neutral-200 dark:border-dark-200 rounded-lg hover:shadow-md transition-shadow"
                         data-status="@item.Status.ToString().ToLower()"
                         data-assignee="@(item.AssignedTo?.UserName ?? "unassigned")">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-3 mb-3">
                                    @{
                                        var statusIcon = item.Status switch {
                                            ActionItemStatus.Completed => "fas fa-check-circle text-success-600 dark:text-success-400",
                                            ActionItemStatus.InProgress => "fas fa-clock text-primary-600 dark:text-primary-400",
                                            ActionItemStatus.Cancelled => "fas fa-times-circle text-danger-600 dark:text-danger-400",
                                            _ => "fas fa-circle text-neutral-400 dark:text-dark-500"
                                        };

                                        var priorityColor = item.Priority switch {
                                            1 => "bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200",
                                            2 => "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
                                            4 => "bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200",
                                            5 => "bg-neutral-100 dark:bg-dark-600 text-neutral-800 dark:text-dark-200",
                                            _ => "bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200"
                                        };

                                        var priorityText = item.Priority switch {
                                            1 => "Critical",
                                            2 => "High",
                                            3 => "Medium",
                                            4 => "Low",
                                            5 => "Lowest",
                                            _ => "Medium"
                                        };
                                    }

                                    <i class="@statusIcon"></i>
                                    <h4 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">@item.Description</h4>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @priorityColor">
                                        @priorityText
                                    </span>

                                    @if (item.IsOverdue)
                                    {
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200">
                                            <i class="fas fa-exclamation-triangle mr-1"></i>
                                            Overdue
                                        </span>
                                    }
                                </div>

                                @if (!string.IsNullOrEmpty(item.Description))
                                {
                                    <p class="text-neutral-600 dark:text-dark-300 mb-4">@item.Description</p>
                                }

                                <div class="flex items-center space-x-6 text-sm text-neutral-500 dark:text-dark-400">
                                    @if (item.AssignedTo != null)
                                    {
                                        <div class="flex items-center space-x-2">
                                            <i class="fas fa-user"></i>
                                            <span>@item.AssignedTo.UserName</span>
                                        </div>
                                    }

                                    @if (item.DueDate.HasValue)
                                    {
                                        <div class="flex items-center space-x-2">
                                            <i class="fas fa-calendar"></i>
                                            <span class="@(item.IsOverdue ? "text-danger-600 dark:text-danger-400" : "")">
                                                Due @item.DueDate.Value.ToString("MMM dd, yyyy")
                                            </span>
                                        </div>
                                    }

                                    <div class="flex items-center space-x-2">
                                        <i class="fas fa-flag"></i>
                                        <span class="capitalize">@item.Status</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex items-center space-x-2 ml-4">
                                <button onclick="editActionItem(@item.Id)" class="btn-outline-custom p-2" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </button>

                                @if (item.Status != ActionItemStatus.Completed)
                                {
                                    <button onclick="markComplete(@item.Id)" class="btn-success-custom p-2" title="Mark Complete">
                                        <i class="fas fa-check"></i>
                                    </button>
                                }

                                <button onclick="deleteActionItem(@item.Id)" class="btn-danger-custom p-2" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                }
            </div>
        }
        else
        {
            <!-- Empty State -->
            <div class="text-center py-16">
                <div class="w-24 h-24 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-tasks text-4xl text-neutral-400 dark:text-dark-500"></i>
                </div>
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">No Action Items</h3>
                <p class="text-neutral-500 dark:text-dark-400 mb-8 max-w-md mx-auto">
                    No action items have been created for this meeting yet. Add your first action item to get started.
                </p>
                @{
                    ViewData["Text"] = "Add First Action Item";
                    ViewData["Variant"] = "primary";
                    ViewData["Icon"] = "fas fa-plus";
                    ViewData["OnClick"] = "showAddActionItemModal()";
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
        }
    </div>
</div>

<!-- Add/Edit Action Item Modal -->
<div id="actionItemModal" class="fixed inset-0 z-50 hidden">
    <div class="fixed inset-0 bg-black bg-opacity-50" onclick="closeActionItemModal()"></div>
    <div class="fixed inset-0 flex items-center justify-center p-4">
        <div class="bg-white dark:bg-surface-dark rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100" id="modalTitle">Add Action Item</h3>
                    <button onclick="closeActionItemModal()" class="text-neutral-400 dark:text-dark-500 hover:text-neutral-600 dark:hover:text-dark-300">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form id="actionItemForm" class="space-y-6">
                    <input type="hidden" id="actionItemId" />
                    <input type="hidden" name="MeetingId" value="@Model.Id" />

                    <div>
                        @{
                            ViewData["Label"] = "Action Item";
                            ViewData["Name"] = "Description";
                            ViewData["Type"] = "text";
                            ViewData["Placeholder"] = "Enter action item description";
                            ViewData["Required"] = true;
                            ViewData["Id"] = "actionItemDescription";
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />
                    </div>

                    <div>
                        @{
                            ViewData["Label"] = "Notes";
                            ViewData["Name"] = "Notes";
                            ViewData["Type"] = "textarea";
                            ViewData["Placeholder"] = "Additional notes (optional)";
                            ViewData["Rows"] = 3;
                            ViewData["Id"] = "actionItemNotes";
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            @{
                                ViewData["Label"] = "Assigned To";
                                ViewData["Name"] = "AssignedToUserId";
                                ViewData["Type"] = "select";
                                ViewData["Options"] = new List<object> { new { Value = "", Text = "Unassigned" } };
                                ViewData["Id"] = "actionItemAssignee";
                            }
                            <partial name="Components/_FormInput" view-data="ViewData" />
                        </div>

                        <div>
                            @{
                                ViewData["Label"] = "Due Date";
                                ViewData["Name"] = "DueDate";
                                ViewData["Type"] = "date";
                                ViewData["Id"] = "actionItemDueDate";
                            }
                            <partial name="Components/_FormInput" view-data="ViewData" />
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            @{
                                ViewData["Label"] = "Priority";
                                ViewData["Name"] = "Priority";
                                ViewData["Type"] = "select";
                                ViewData["Options"] = new List<object> {
                                    new { Value = "1", Text = "Critical" },
                                    new { Value = "2", Text = "High" },
                                    new { Value = "3", Text = "Medium" },
                                    new { Value = "4", Text = "Low" },
                                    new { Value = "5", Text = "Lowest" }
                                };
                                ViewData["Value"] = "3";
                                ViewData["Id"] = "actionItemPriority";
                            }
                            <partial name="Components/_FormInput" view-data="ViewData" />
                        </div>

                        <div>
                            @{
                                ViewData["Label"] = "Status";
                                ViewData["Name"] = "Status";
                                ViewData["Type"] = "select";
                                ViewData["Options"] = Html.GetEnumSelectList<ActionItemStatus>().Select(x => new { Value = x.Value, Text = x.Text });
                                ViewData["Value"] = "0";
                                ViewData["Id"] = "actionItemStatus";
                            }
                            <partial name="Components/_FormInput" view-data="ViewData" />
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3 pt-6 border-t border-neutral-200 dark:border-dark-200">
                        <button type="button" onclick="closeActionItemModal()" class="btn-secondary-custom">
                            Cancel
                        </button>
                        <button type="submit" class="btn-primary-custom">
                            <i class="fas fa-save mr-2"></i>
                            Save Action Item
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            loadUsers();
            setupFilters();
        });

        function loadUsers() {
            $.get('@Url.Action("GetUsers", "Meeting")')
                .done(function(data) {
                    const assigneeSelect = $('#actionItemAssignee');
                    const filterSelect = $('#assigneeFilter');

                    data.forEach(function(user) {
                        assigneeSelect.append(`<option value="${user.id}">${user.name}</option>`);
                        filterSelect.append(`<option value="${user.name.toLowerCase()}">${user.name}</option>`);
                    });
                })
                .fail(function() {
                    console.error('Failed to load users');
                });
        }

        function setupFilters() {
            $('#statusFilter, #assigneeFilter').on('change', function() {
                filterActionItems();
            });
        }

        function filterActionItems() {
            const statusFilter = $('#statusFilter').val().toLowerCase();
            const assigneeFilter = $('#assigneeFilter').val().toLowerCase();

            $('.action-item-card').each(function() {
                const card = $(this);
                const status = card.data('status');
                const assignee = card.data('assignee');

                let show = true;
                if (statusFilter && status !== statusFilter) show = false;
                if (assigneeFilter && assignee !== assigneeFilter) show = false;

                card.toggle(show);
            });
        }

        function showAddActionItemModal() {
            document.getElementById('modalTitle').textContent = 'Add Action Item';
            document.getElementById('actionItemForm').reset();
            document.getElementById('actionItemId').value = '';
            document.getElementById('actionItemModal').classList.remove('hidden');
        }

        function editActionItem(id) {
            // Load action item data and populate form
            $.get('@Url.Action("GetActionItem", "Meeting")', { id: id })
                .done(function(data) {
                    document.getElementById('modalTitle').textContent = 'Edit Action Item';
                    document.getElementById('actionItemId').value = data.id;
                    document.getElementById('actionItemDescription').value = data.description || '';
                    document.getElementById('actionItemNotes').value = data.notes || '';
                    document.getElementById('actionItemAssignee').value = data.assignedToUserId || '';
                    document.getElementById('actionItemDueDate').value = data.dueDate ? data.dueDate.split('T')[0] : '';
                    document.getElementById('actionItemPriority').value = data.priority;
                    document.getElementById('actionItemStatus').value = data.status;
                    document.getElementById('actionItemModal').classList.remove('hidden');
                })
                .fail(function() {
                    alert('Failed to load action item details.');
                });
        }

        function closeActionItemModal() {
            document.getElementById('actionItemModal').classList.add('hidden');
        }

        function markComplete(id) {
            if (confirm('Mark this action item as complete?')) {
                $.post('@Url.Action("CompleteActionItem", "Meeting")', { id: id })
                    .done(function() {
                        location.reload();
                    })
                    .fail(function() {
                        alert('Failed to update action item status.');
                    });
            }
        }

        function deleteActionItem(id) {
            if (confirm('Are you sure you want to delete this action item?')) {
                $.post('@Url.Action("DeleteActionItem", "Meeting")', { id: id })
                    .done(function() {
                        location.reload();
                    })
                    .fail(function() {
                        alert('Failed to delete action item.');
                    });
            }
        }

        // Form submission
        $('#actionItemForm').on('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const actionItemId = document.getElementById('actionItemId').value;
            const url = actionItemId ? '@Url.Action("UpdateActionItem", "Meeting")' : '@Url.Action("CreateActionItem", "Meeting")';

            if (actionItemId) {
                formData.append('Id', actionItemId);
            }

            $.ajax({
                url: url,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function() {
                    closeActionItemModal();
                    location.reload();
                },
                error: function() {
                    alert('Failed to save action item.');
                }
            });
        });
    </script>
}
