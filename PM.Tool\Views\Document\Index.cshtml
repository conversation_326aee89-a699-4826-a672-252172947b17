@using PM.Tool.Core.Entities
@{
    ViewData["Title"] = "Document List";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Documents</h2>
        <div>
            <a asp-action="Categories" class="btn btn-outline-secondary me-2">
                <i class="bi bi-folder me-2"></i>Categories
            </a>
            <a asp-action="Upload" class="btn btn-primary">
                <i class="bi bi-cloud-upload me-2"></i>Upload Document
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#projectDocs" type="button" role="tab">
                        Project Documents
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" data-bs-toggle="tab" data-bs-target="#taskDocs" type="button" role="tab">
                        Task Documents
                    </button>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content">
                <div class="tab-pane fade show active" id="projectDocs" role="tabpanel">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Filename</th>
                                    <th>Project</th>
                                    <th>Category</th>
                                    <th>Size</th>
                                    <th>Uploaded</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model.ProjectAttachments.Any())
                                {
                                    @foreach (var doc in Model.ProjectAttachments)
                                    {
                                        <tr>
                                            <td>
                                                <i class="bi bi-file-earmark me-2"></i>
                                                @doc.FileName
                                            </td>
                                            <td>
                                                <a asp-controller="Projects" asp-action="Details" asp-route-id="@doc.ParentId">
                                                    @doc.ParentName
                                                </a>
                                            </td>
                                            <td>@(doc.CategoryName ?? "-")</td>
                                            <td>@doc.FormattedFileSize</td>
                                            <td class="text-muted">
                                                <small>
                                                    @doc.UploadedAt.ToString("MMM dd, yyyy")
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a asp-action="AttachmentDetails" asp-route-id="@doc.Id" asp-route-type="Project" 
                                                       class="btn btn-outline-primary" title="View Details">
                                                        <i class="bi bi-info-circle"></i>
                                                    </a>
                                                    <a href="@doc.FilePath" class="btn btn-outline-secondary" title="Download" 
                                                       download>
                                                        <i class="bi bi-download"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="6" class="text-center text-muted py-4">
                                            No project documents found.
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="tab-pane fade" id="taskDocs" role="tabpanel">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Filename</th>
                                    <th>Task</th>
                                    <th>Project</th>
                                    <th>Category</th>
                                    <th>Size</th>
                                    <th>Uploaded</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model.TaskAttachments.Any())
                                {
                                    @foreach (var doc in Model.TaskAttachments)
                                    {
                                        <tr>
                                            <td>
                                                <i class="bi bi-file-earmark me-2"></i>
                                                @doc.FileName
                                            </td>
                                            <td>
                                                <a asp-controller="Tasks" asp-action="Details" asp-route-id="@doc.ParentId">
                                                    @doc.ParentName
                                                </a>
                                            </td>
                                            <td>
                                                <a asp-controller="Projects" asp-action="Details" asp-route-id="@doc.ProjectId">
                                                    @doc.ProjectName
                                                </a>
                                            </td>
                                            <td>@(doc.CategoryName ?? "-")</td>
                                            <td>@doc.FormattedFileSize</td>
                                            <td class="text-muted">
                                                <small>
                                                    @doc.UploadedAt.ToString("MMM dd, yyyy")
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a asp-action="AttachmentDetails" asp-route-id="@doc.Id" asp-route-type="Task" 
                                                       class="btn btn-outline-primary" title="View Details">
                                                        <i class="bi bi-info-circle"></i>
                                                    </a>
                                                    <a href="@doc.FilePath" class="btn btn-outline-secondary" title="Download" 
                                                       download>
                                                        <i class="bi bi-download"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="7" class="text-center text-muted py-4">
                                            No task documents found.
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
