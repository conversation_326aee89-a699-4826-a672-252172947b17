using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.ViewFeatures;

namespace PM.Tool.Extensions
{
    /// <summary>
    /// Extension methods for ViewData to simplify common operations
    /// </summary>
    public static class ViewDataExtensions
    {
        /// <summary>
        /// Gets the validation error message for a specific field from ModelState
        /// </summary>
        /// <param name="viewData">The ViewData dictionary</param>
        /// <param name="fieldName">The name of the field to get the error message for</param>
        /// <returns>The error message if found, otherwise empty string</returns>
        public static string GetValidationErrorMessage(this ViewDataDictionary viewData, string fieldName)
        {
            if (viewData?.ModelState == null)
                return string.Empty;

            if (viewData.ModelState.ContainsKey(fieldName) && viewData.ModelState[fieldName].Errors.Any())
            {
                return viewData.ModelState[fieldName].Errors.First().ErrorMessage;
            }

            return string.Empty;
        }

        /// <summary>
        /// Checks if a field has validation errors
        /// </summary>
        /// <param name="viewData">The ViewData dictionary</param>
        /// <param name="fieldName">The name of the field to check</param>
        /// <returns>True if the field has validation errors, otherwise false</returns>
        public static bool HasValidationError(this ViewDataDictionary viewData, string fieldName)
        {
            if (viewData?.ModelState == null)
                return false;

            return viewData.ModelState.ContainsKey(fieldName) && viewData.ModelState[fieldName].Errors.Any();
        }

        /// <summary>
        /// Gets all validation error messages for a field
        /// </summary>
        /// <param name="viewData">The ViewData dictionary</param>
        /// <param name="fieldName">The name of the field to get error messages for</param>
        /// <returns>Collection of error messages</returns>
        public static IEnumerable<string> GetAllValidationErrorMessages(this ViewDataDictionary viewData, string fieldName)
        {
            if (viewData?.ModelState == null)
                return Enumerable.Empty<string>();

            if (viewData.ModelState.ContainsKey(fieldName))
            {
                return viewData.ModelState[fieldName].Errors.Select(e => e.ErrorMessage);
            }

            return Enumerable.Empty<string>();
        }

        /// <summary>
        /// Gets the validation state for a field
        /// </summary>
        /// <param name="viewData">The ViewData dictionary</param>
        /// <param name="fieldName">The name of the field to check</param>
        /// <returns>The ModelValidationState for the field</returns>
        public static ModelValidationState GetValidationState(this ViewDataDictionary viewData, string fieldName)
        {
            if (viewData?.ModelState == null)
                return ModelValidationState.Unvalidated;

            if (viewData.ModelState.ContainsKey(fieldName))
            {
                return viewData.ModelState[fieldName].ValidationState;
            }

            return ModelValidationState.Unvalidated;
        }

        /// <summary>
        /// Safely gets a string value from ViewData
        /// </summary>
        /// <param name="viewData">The ViewData dictionary</param>
        /// <param name="key">The key to look up</param>
        /// <param name="defaultValue">Default value if key is not found or value is null</param>
        /// <returns>The string value or default value</returns>
        public static string GetString(this ViewDataDictionary viewData, string key, string defaultValue = "")
        {
            if (viewData?.ContainsKey(key) == true && viewData[key] != null)
            {
                return viewData[key].ToString() ?? defaultValue;
            }

            return defaultValue;
        }

        /// <summary>
        /// Safely gets a boolean value from ViewData
        /// </summary>
        /// <param name="viewData">The ViewData dictionary</param>
        /// <param name="key">The key to look up</param>
        /// <param name="defaultValue">Default value if key is not found or value is null</param>
        /// <returns>The boolean value or default value</returns>
        public static bool GetBool(this ViewDataDictionary viewData, string key, bool defaultValue = false)
        {
            if (viewData?.ContainsKey(key) == true && viewData[key] != null)
            {
                if (viewData[key] is bool boolValue)
                    return boolValue;

                if (bool.TryParse(viewData[key].ToString(), out bool parsedValue))
                    return parsedValue;
            }

            return defaultValue;
        }

        /// <summary>
        /// Safely gets an integer value from ViewData
        /// </summary>
        /// <param name="viewData">The ViewData dictionary</param>
        /// <param name="key">The key to look up</param>
        /// <param name="defaultValue">Default value if key is not found or value is null</param>
        /// <returns>The integer value or default value</returns>
        public static int GetInt(this ViewDataDictionary viewData, string key, int defaultValue = 0)
        {
            if (viewData?.ContainsKey(key) == true && viewData[key] != null)
            {
                if (viewData[key] is int intValue)
                    return intValue;

                if (int.TryParse(viewData[key].ToString(), out int parsedValue))
                    return parsedValue;
            }

            return defaultValue;
        }
    }
}
