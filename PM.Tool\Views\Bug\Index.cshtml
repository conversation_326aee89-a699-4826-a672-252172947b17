@model PM.Tool.Models.ViewModels.BugListViewModel
@using PM.Tool.Core.Entities.Agile

@{
    ViewData["Title"] = "Bugs";
    ViewData["PageTitle"] = $"Bugs - {Model.ProjectName}";
    ViewData["PageDescription"] = "Track and manage project bugs, monitor resolution progress, and analyze bug trends.";
}

@section Styles {
    <style>
        .bug-card {
            transition: all 0.2s ease-in-out;
        }
        .bug-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .severity-indicator {
            width: 4px;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
            border-radius: 0.5rem 0 0 0.5rem;
        }
        .filter-chip {
            transition: all 0.2s ease-in-out;
        }
        .filter-chip:hover {
            transform: scale(1.05);
        }
        .filter-chip.active {
            background: #3b82f6;
            color: white;
        }
    </style>
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-bug mr-3 text-primary-600 dark:text-primary-400"></i>
                Bugs
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                @Model.ProjectName - Track and resolve project bugs
            </p>
        </div>
        <div class="mt-4 sm:mt-0">
            @{
                ViewData["Text"] = "Report Bug";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-plus";
                ViewData["Href"] = Url.Action("Create", new { projectId = Model.ProjectId });
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Metrics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
    @{
        ViewData["Title"] = "Total Bugs";
        ViewData["Value"] = Model.Metrics.TotalBugs.ToString();
        ViewData["Icon"] = "fas fa-bug";
        ViewData["Color"] = "blue";
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />

    @{
        ViewData["Title"] = "Open";
        ViewData["Value"] = Model.Metrics.OpenBugs.ToString();
        ViewData["Icon"] = "fas fa-exclamation-circle";
        ViewData["Color"] = "red";
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />

    @{
        ViewData["Title"] = "In Progress";
        ViewData["Value"] = Model.Metrics.InProgressBugs.ToString();
        ViewData["Icon"] = "fas fa-cog";
        ViewData["Color"] = "yellow";
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />

    @{
        ViewData["Title"] = "Resolved";
        ViewData["Value"] = Model.Metrics.ResolvedBugs.ToString();
        ViewData["Icon"] = "fas fa-check-circle";
        ViewData["Color"] = "green";
        ViewData["Subtitle"] = $"{Model.Metrics.ResolutionRate:F1}%";
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />

    @{
        ViewData["Title"] = "Critical";
        ViewData["Value"] = Model.Metrics.CriticalBugs.ToString();
        ViewData["Icon"] = "fas fa-exclamation-triangle";
        ViewData["Color"] = "red";
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />
</div>

<!-- Filters -->
@{
    ViewData["Title"] = "Filters & Search";
    ViewData["Icon"] = "fas fa-filter";
    ViewData["ShowHeader"] = true;
}
<partial name="Components/_Card" view-data="ViewData">
    <div class="space-y-4">
        <!-- Search -->
        <div class="flex flex-col sm:flex-row gap-4">
            <div class="flex-1">
                @{
                    ViewData["Label"] = "Search Bugs";
                    ViewData["Name"] = "searchTerm";
                    ViewData["Type"] = "text";
                    ViewData["Icon"] = "fas fa-search";
                    ViewData["Placeholder"] = "Search by title, description, or bug key...";
                    ViewData["Value"] = Model.FilterOptions.SearchTerm;
                }
                <partial name="Components/_FormInput" view-data="ViewData" />
            </div>
        </div>

        <!-- Filter Chips -->
        <div class="flex flex-wrap gap-4">
            <!-- Severity Filter -->
            <div class="flex flex-wrap gap-2">
                <span class="text-sm font-medium text-neutral-700 dark:text-dark-300">Severity:</span>
                <button class="filter-chip px-3 py-1 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-300 text-neutral-700 dark:text-dark-200 @(Model.FilterOptions.Severity == null ? "active" : "")"
                        data-filter="severity" data-value="">All</button>
                @foreach (BugSeverity severity in Enum.GetValues<BugSeverity>())
                {
                    <button class="filter-chip px-3 py-1 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-300 text-neutral-700 dark:text-dark-200 @(Model.FilterOptions.Severity == severity ? "active" : "")"
                            data-filter="severity" data-value="@((int)severity)">@severity</button>
                }
            </div>

            <!-- Status Filter -->
            <div class="flex flex-wrap gap-2">
                <span class="text-sm font-medium text-neutral-700 dark:text-dark-300">Status:</span>
                <button class="filter-chip px-3 py-1 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-300 text-neutral-700 dark:text-dark-200 @(Model.FilterOptions.Status == null ? "active" : "")"
                        data-filter="status" data-value="">All</button>
                @foreach (BugStatus status in Enum.GetValues<BugStatus>())
                {
                    <button class="filter-chip px-3 py-1 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-300 text-neutral-700 dark:text-dark-200 @(Model.FilterOptions.Status == status ? "active" : "")"
                            data-filter="status" data-value="@((int)status)">@status</button>
                }
            </div>

            <!-- Priority Filter -->
            <div class="flex flex-wrap gap-2">
                <span class="text-sm font-medium text-neutral-700 dark:text-dark-300">Priority:</span>
                <button class="filter-chip px-3 py-1 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-300 text-neutral-700 dark:text-dark-200 @(Model.FilterOptions.Priority == null ? "active" : "")"
                        data-filter="priority" data-value="">All</button>
                @foreach (BugPriority priority in Enum.GetValues<BugPriority>())
                {
                    <button class="filter-chip px-3 py-1 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-300 text-neutral-700 dark:text-dark-200 @(Model.FilterOptions.Priority == priority ? "active" : "")"
                            data-filter="priority" data-value="@((int)priority)">@priority</button>
                }
            </div>
        </div>

        <!-- Quick Filters -->
        <div class="flex flex-wrap gap-2 pt-2 border-t border-neutral-200 dark:border-dark-200">
            <span class="text-sm font-medium text-neutral-700 dark:text-dark-300">Quick Filters:</span>
            <button class="filter-chip px-3 py-1 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-300 text-neutral-700 dark:text-dark-200"
                    data-quick-filter="overdue">Overdue</button>
            <button class="filter-chip px-3 py-1 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-300 text-neutral-700 dark:text-dark-200"
                    data-quick-filter="critical">Critical & High</button>
            <button class="filter-chip px-3 py-1 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-300 text-neutral-700 dark:text-dark-200"
                    data-quick-filter="unassigned">Unassigned</button>
        </div>
    </div>
</partial>

<!-- Bugs Grid -->
@{
    ViewData["Title"] = "Bug Reports";
    ViewData["Icon"] = "fas fa-list";
    ViewData["ShowHeader"] = true;
}
<partial name="Components/_Card" view-data="ViewData">
    @if (Model.Bugs.Any())
    {
        <div class="space-y-4" id="bugsGrid">
            @foreach (var bug in Model.Bugs)
            {
                <div class="bug-card relative bg-white dark:bg-surface-dark rounded-xl shadow-sm border border-neutral-200 dark:border-dark-200 p-6 hover:shadow-lg transition-all duration-200"
                     data-severity="@bug.Severity" data-status="@bug.Status" data-priority="@bug.Priority"
                     data-overdue="@bug.IsOverdue.ToString().ToLower()" data-assigned="@(!string.IsNullOrEmpty(bug.AssignedToName)).ToString().ToLower()"
                     data-search="@bug.Title.ToLower() @bug.BugKey.ToLower()">

                    <!-- Severity Indicator -->
                    <div class="severity-indicator @GetSeverityIndicatorClass(bug.Severity)"></div>

                    <!-- Bug Header -->
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex-1 ml-4">
                            <div class="flex items-center space-x-2 mb-2">
                                <span class="text-xs font-mono text-neutral-500 dark:text-dark-400 bg-neutral-100 dark:bg-dark-300 px-2 py-1 rounded">
                                    @bug.BugKey
                                </span>
                                <span class="px-2 py-1 text-xs font-medium rounded-full @GetSeverityBadgeClass(bug.Severity)">
                                    @bug.Severity
                                </span>
                                <span class="px-2 py-1 text-xs font-medium rounded-full @GetStatusBadgeClass(bug.Status)">
                                    @bug.Status
                                </span>
                                <span class="px-2 py-1 text-xs font-medium rounded-full @GetPriorityBadgeClass(bug.Priority)">
                                    @bug.Priority
                                </span>
                                @if (bug.IsOverdue)
                                {
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                        <i class="fas fa-clock mr-1"></i>Overdue
                                    </span>
                                }
                            </div>
                            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-2">
                                <a href="@Url.Action("Details", new { id = bug.Id })" class="hover:text-primary-600 dark:hover:text-primary-400">
                                    @bug.Title
                                </a>
                            </h3>
                            @if (!string.IsNullOrEmpty(bug.FeatureTitle))
                            {
                                <p class="text-sm text-neutral-600 dark:text-dark-300 mb-2">
                                    Feature: @bug.FeatureTitle
                                </p>
                            }
                        </div>
                        <div class="text-right">
                            <div class="text-sm text-neutral-500 dark:text-dark-400">
                                @bug.CreatedAt.ToString("MMM dd")
                            </div>
                            @if (bug.TargetDate.HasValue)
                            {
                                <div class="text-sm @(bug.IsOverdue ? "text-red-600 dark:text-red-400" : "text-neutral-600 dark:text-dark-300")">
                                    Due: @bug.TargetDate.Value.ToString("MMM dd")
                                </div>
                            }
                        </div>
                    </div>

                    <!-- Bug Details -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div>
                            <span class="text-xs text-neutral-500 dark:text-dark-400">Assigned To</span>
                            <div class="font-medium text-neutral-900 dark:text-dark-100">
                                @(bug.AssignedToName ?? "Unassigned")
                            </div>
                        </div>
                        <div>
                            <span class="text-xs text-neutral-500 dark:text-dark-400">Created</span>
                            <div class="font-medium text-neutral-900 dark:text-dark-100">
                                @bug.CreatedAt.ToString("MMM dd, yyyy")
                            </div>
                        </div>
                        <div>
                            <span class="text-xs text-neutral-500 dark:text-dark-400">Days Open</span>
                            <div class="font-medium text-neutral-900 dark:text-dark-100">
                                @((DateTime.Now - bug.CreatedAt).Days) days
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex space-x-2 pt-4 border-t border-neutral-200 dark:border-dark-200">
                        @{
                            ViewData["Text"] = "View";
                            ViewData["Variant"] = "secondary";
                            ViewData["Size"] = "sm";
                            ViewData["Icon"] = "fas fa-eye";
                            ViewData["Href"] = Url.Action("Details", new { id = bug.Id });
                        }
                        <partial name="Components/_Button" view-data="ViewData" />

                        @{
                            ViewData["Text"] = "Edit";
                            ViewData["Variant"] = "primary";
                            ViewData["Size"] = "sm";
                            ViewData["Icon"] = "fas fa-edit";
                            ViewData["Href"] = Url.Action("Edit", new { id = bug.Id });
                        }
                        <partial name="Components/_Button" view-data="ViewData" />

                        @if (bug.Status != BugStatus.Resolved && bug.Status != BugStatus.Closed)
                        {
                            ViewData["Text"] = "Resolve";
                            ViewData["Variant"] = "success";
                            ViewData["Size"] = "sm";
                            ViewData["Icon"] = "fas fa-check";
                            ViewData["Href"] = "#";
                            ViewData["OnClick"] = $"resolveBug({bug.Id})";
                            <partial name="Components/_Button" view-data="ViewData" />
                        }
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="text-center py-12">
            <i class="fas fa-bug text-6xl text-neutral-300 dark:text-dark-400 mb-4"></i>
            <h3 class="text-lg font-medium text-neutral-900 dark:text-dark-100 mb-2">No Bugs Found</h3>
            <p class="text-neutral-500 dark:text-dark-400 mb-6">Great! No bugs match your current filters.</p>
            @{
                ViewData["Text"] = "Report Bug";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-plus";
                ViewData["Href"] = Url.Action("Create", new { projectId = Model.ProjectId });
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    }
</partial>

@functions {
    private string GetSeverityIndicatorClass(BugSeverity severity)
    {
        return severity switch
        {
            BugSeverity.Critical => "bg-red-500",
            BugSeverity.High => "bg-orange-500",
            BugSeverity.Medium => "bg-yellow-500",
            BugSeverity.Low => "bg-green-500",
            _ => "bg-neutral-300"
        };
    }

    private string GetSeverityBadgeClass(BugSeverity severity)
    {
        return severity switch
        {
            BugSeverity.Critical => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            BugSeverity.High => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
            BugSeverity.Medium => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            BugSeverity.Low => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100"
        };
    }

    private string GetStatusBadgeClass(BugStatus status)
    {
        return status switch
        {
            BugStatus.New => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            BugStatus.Assigned => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            BugStatus.InProgress => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
            BugStatus.Resolved => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            BugStatus.Closed => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100"
        };
    }

    private string GetPriorityBadgeClass(BugPriority priority)
    {
        return priority switch
        {
            BugPriority.Low => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            BugPriority.Medium => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            BugPriority.High => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
            BugPriority.Critical => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100"
        };
    }
}

@section Scripts {
    <script>
        $(document).ready(function() {
            setupFilters();
            setupSearch();
            setupQuickFilters();
        });

        function setupFilters() {
            $('.filter-chip[data-filter]').on('click', function() {
                const filterType = $(this).data('filter');
                const filterValue = $(this).data('value');

                // Update active state
                $(this).siblings(`[data-filter="${filterType}"]`).removeClass('active');
                $(this).addClass('active');

                // Apply filter
                applyFilters();
            });
        }

        function setupQuickFilters() {
            $('.filter-chip[data-quick-filter]').on('click', function() {
                const quickFilter = $(this).data('quick-filter');

                // Clear other quick filters
                $('.filter-chip[data-quick-filter]').removeClass('active');
                $(this).addClass('active');

                // Apply quick filter
                applyQuickFilter(quickFilter);
            });
        }

        function setupSearch() {
            let searchTimeout;
            $('input[name="searchTerm"]').on('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    applyFilters();
                }, 300);
            });
        }

        function applyFilters() {
            const searchTerm = $('input[name="searchTerm"]').val().toLowerCase();
            const severityFilter = $('.filter-chip[data-filter="severity"].active').data('value');
            const statusFilter = $('.filter-chip[data-filter="status"].active').data('value');
            const priorityFilter = $('.filter-chip[data-filter="priority"].active').data('value');

            $('.bug-card').each(function() {
                const $card = $(this);
                const searchText = $card.data('search');
                const severity = $card.data('severity');
                const status = $card.data('status');
                const priority = $card.data('priority');

                let show = true;

                // Search filter
                if (searchTerm && !searchText.includes(searchTerm)) {
                    show = false;
                }

                // Severity filter
                if (severityFilter && severity != severityFilter) {
                    show = false;
                }

                // Status filter
                if (statusFilter && status != statusFilter) {
                    show = false;
                }

                // Priority filter
                if (priorityFilter && priority != priorityFilter) {
                    show = false;
                }

                $card.toggle(show);
            });
        }

        function applyQuickFilter(filterType) {
            $('.bug-card').each(function() {
                const $card = $(this);
                let show = false;

                switch (filterType) {
                    case 'overdue':
                        show = $card.data('overdue') === true;
                        break;
                    case 'critical':
                        const severity = $card.data('severity');
                        const priority = $card.data('priority');
                        show = severity == '@((int)BugSeverity.Critical)' || severity == '@((int)BugSeverity.High)' ||
                               priority == '@((int)BugPriority.Critical)' || priority == '@((int)BugPriority.High)';
                        break;
                    case 'unassigned':
                        show = $card.data('assigned') === false;
                        break;
                }

                $card.toggle(show);
            });
        }

        function resolveBug(bugId) {
            if (confirm('Mark this bug as resolved?')) {
                // This would typically make an AJAX call to resolve the bug
                window.location.href = '@Url.Action("Edit")' + '/' + bugId + '?status=@((int)BugStatus.Resolved)';
            }
        }
    </script>
}
