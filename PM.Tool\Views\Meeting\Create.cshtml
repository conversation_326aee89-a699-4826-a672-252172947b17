@model PM.Tool.Models.ViewModels.MeetingCreateViewModel
@using PM.Tool.Core.Enums
@{
    ViewData["Title"] = "Schedule Meeting";
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Meetings", Href = Url.Action("Index", "Meeting"), Icon = "fas fa-calendar" },
        new { Text = "Schedule Meeting", Href = "", Icon = "fas fa-plus" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Page Header -->
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-calendar-plus mr-3 text-primary-600 dark:text-primary-400"></i>
                Schedule New Meeting
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Create a new meeting and invite participants
            </p>
        </div>
        @{
            ViewData["Text"] = "Back to Meetings";
            ViewData["Variant"] = "secondary";
            ViewData["Icon"] = "fas fa-arrow-left";
            ViewData["Href"] = Url.Action("Index");
        }
        <partial name="Components/_Button" view-data="ViewData" />
    </div>
</div>

<div class="max-w-4xl mx-auto">
    <div class="card-custom">
        <div class="card-body-custom">
            <form asp-action="Create" method="post" id="meetingForm">
                <div asp-validation-summary="ModelOnly" class="alert-danger-custom mb-6"></div>

                <!-- Basic Information -->
                <div class="space-y-6">
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-4">Basic Information</h3>
                        <div class="grid grid-cols-1 gap-6">
                            <div>
                                @{
                                    ViewData["Label"] = "Meeting Title";
                                    ViewData["Name"] = "Title";
                                    ViewData["Type"] = "text";
                                    ViewData["Placeholder"] = "Enter meeting title";
                                    ViewData["Required"] = true;
                                    ViewData["Value"] = Model.Title;
                                }
                                <partial name="Components/_FormInput" view-data="ViewData" />
                                <span asp-validation-for="Title" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                            </div>

                            <div>
                                @{
                                    ViewData["Label"] = "Description";
                                    ViewData["Name"] = "Description";
                                    ViewData["Type"] = "textarea";
                                    ViewData["Placeholder"] = "Meeting description and agenda";
                                    ViewData["Rows"] = 3;
                                    ViewData["Value"] = Model.Description;
                                }
                                <partial name="Components/_FormInput" view-data="ViewData" />
                                <span asp-validation-for="Description" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                            </div>
                        </div>
                    </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                @{
                                    ViewData["Label"] = "Meeting Type";
                                    ViewData["Name"] = "Type";
                                    ViewData["Type"] = "select";
                                    ViewData["Required"] = true;
                                    ViewData["Options"] = Html.GetEnumSelectList<MeetingType>().Select(x => new { Value = x.Value, Text = x.Text }).Prepend(new { Value = "", Text = "Select meeting type" });
                                    ViewData["Value"] = ((int?)Model.Type)?.ToString() ?? "";
                                }
                                <partial name="Components/_FormInput" view-data="ViewData" />
                                <span asp-validation-for="Type" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                            </div>
                            <div>
                                @{
                                    ViewData["Label"] = "Project";
                                    ViewData["Name"] = "ProjectId";
                                    ViewData["Type"] = "select";
                                    ViewData["Options"] = new List<object> { new { Value = "", Text = "Select project (optional)" } };
                                    ViewData["Value"] = Model.ProjectId.ToString() ?? "";
                                    ViewData["Id"] = "projectSelect";
                                    ViewData["UseSelect2"] = true;
                                    ViewData["Select2Options"] = "{\"placeholder\": \"Select project (optional)\", \"allowClear\": true, \"theme\": \"tailwind\"}";
                                }
                                <partial name="Components/_FormInput" view-data="ViewData" />
                                <span asp-validation-for="ProjectId" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                @{
                                    ViewData["Label"] = "Date";
                                    ViewData["Name"] = "meetingDate";
                                    ViewData["Type"] = "date";
                                    ViewData["Required"] = true;
                                    ViewData["Id"] = "meetingDate";
                                }
                                <partial name="Components/_FormInput" view-data="ViewData" />
                            </div>
                            <div>
                                @{
                                    ViewData["Label"] = "Start Time";
                                    ViewData["Name"] = "startTime";
                                    ViewData["Type"] = "time";
                                    ViewData["Required"] = true;
                                    ViewData["Id"] = "startTime";
                                }
                                <partial name="Components/_FormInput" view-data="ViewData" />
                            </div>
                            <div>
                                @{
                                    ViewData["Label"] = "Duration (minutes)";
                                    ViewData["Name"] = "DurationMinutes";
                                    ViewData["Type"] = "number";
                                    ViewData["Required"] = true;
                                    ViewData["Value"] = "60";
                                    ViewData["Min"] = "15";
                                    ViewData["Max"] = "480";
                                    ViewData["Step"] = "15";
                                }
                                <partial name="Components/_FormInput" view-data="ViewData" />
                                <span asp-validation-for="DurationMinutes" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                            </div>
                        </div>

                        <!-- Hidden fields for ScheduledDate -->
                        <input asp-for="ScheduledDate" type="hidden" id="hiddenScheduledDate">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                @{
                                    ViewData["Label"] = "Location";
                                    ViewData["Name"] = "Location";
                                    ViewData["Type"] = "text";
                                    ViewData["Placeholder"] = "Meeting room or online link";
                                    ViewData["Value"] = Model.Location;
                                }
                                <partial name="Components/_FormInput" view-data="ViewData" />
                                <span asp-validation-for="Location" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                            </div>
                            <div>
                                @{
                                    ViewData["Label"] = "Meeting Link";
                                    ViewData["Name"] = "MeetingLink";
                                    ViewData["Type"] = "url";
                                    ViewData["Placeholder"] = "https://zoom.us/j/...";
                                    ViewData["Value"] = Model.MeetingLink;
                                }
                                <partial name="Components/_FormInput" view-data="ViewData" />
                                <span asp-validation-for="MeetingLink" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                            </div>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" id="isRecurring" class="form-checkbox-custom" />
                            <label for="isRecurring" class="ml-3 text-sm font-medium text-neutral-700 dark:text-dark-200">
                                This is a recurring meeting
                            </label>
                        </div>

                        <!-- Recurring Options (hidden by default) -->
                        <div id="recurringOptions" class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6" style="display: none;">
                            <div>
                                @{
                                    ViewData["Label"] = "Recurrence Pattern";
                                    ViewData["Name"] = "recurrencePattern";
                                    ViewData["Type"] = "select";
                                    ViewData["Options"] = new List<object> {
                                        new { Value = "Daily", Text = "Daily" },
                                        new { Value = "Weekly", Text = "Weekly" },
                                        new { Value = "BiWeekly", Text = "Bi-weekly" },
                                        new { Value = "Monthly", Text = "Monthly" }
                                    };
                                    ViewData["Id"] = "recurrencePattern";
                                }
                                <partial name="Components/_FormInput" view-data="ViewData" />
                            </div>
                            <div>
                                @{
                                    ViewData["Label"] = "End Date";
                                    ViewData["Name"] = "recurrenceEnd";
                                    ViewData["Type"] = "date";
                                    ViewData["Id"] = "recurrenceEnd";
                                }
                                <partial name="Components/_FormInput" view-data="ViewData" />
                            </div>
                        </div>

                        <!-- Agenda -->
                        <div>
                            @{
                                ViewData["Label"] = "Meeting Agenda";
                                ViewData["Name"] = "Agenda";
                                ViewData["Type"] = "textarea";
                                ViewData["Placeholder"] = "Enter meeting agenda items...";
                                ViewData["Rows"] = 6;
                                ViewData["Value"] = Model.Agenda;
                            }
                            <partial name="Components/_FormInput" view-data="ViewData" />
                            <span asp-validation-for="Agenda" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                        </div>

                        <!-- Participants -->
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-4">Participants</label>
                            <div class="card-custom">
                                <div class="card-body-custom">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <label for="participantSearch" class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-2">Search Users:</label>
                                            <input type="text" id="participantSearch" class="form-input-custom" placeholder="Type to search users...">
                                            <div id="userSearchResults" class="mt-2"></div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-2">Selected Participants:</label>
                                            <div id="selectedParticipants" class="border border-neutral-300 dark:border-dark-200 rounded-lg p-3 min-h-[100px] bg-neutral-50 dark:bg-dark-700">
                                                <small class="text-neutral-500 dark:text-dark-400">No participants selected</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Meeting Options -->
                        <div>
                            <label class="block text-sm font-medium text-neutral-700 dark:text-dark-200 mb-4">Meeting Options</label>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div class="flex items-center">
                                    <input type="checkbox" id="sendInvitations" class="form-checkbox-custom" checked />
                                    <label for="sendInvitations" class="ml-3 text-sm font-medium text-neutral-700 dark:text-dark-200">
                                        Send invitations
                                    </label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="requiresPreparation" class="form-checkbox-custom" />
                                    <label for="requiresPreparation" class="ml-3 text-sm font-medium text-neutral-700 dark:text-dark-200">
                                        Requires preparation
                                    </label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="recordMeeting" class="form-checkbox-custom" />
                                    <label for="recordMeeting" class="ml-3 text-sm font-medium text-neutral-700 dark:text-dark-200">
                                        Record meeting
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        @{
                            ViewData["SubmitText"] = "Schedule Meeting";
                            ViewData["SubmitIcon"] = "fas fa-calendar-check";
                            ViewData["CancelUrl"] = Url.Action("Index");
                            ViewData["AdditionalButtons"] = "<button type='button' class='btn-outline-custom' onclick='saveDraft()'><i class='fas fa-save mr-2'></i>Save as Draft</button>";
                        }
                        <partial name="Components/_FormActions" view-data="ViewData" />
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        let selectedParticipants = [];

        $(document).ready(function() {
            loadProjects();
            setupDateTimeHandlers();
            setupParticipantSearch();

            $('#isRecurring').on('change', function() {
                $('#recurringOptions').toggle(this.checked);
            });

            // Set minimum date to today
            $('#meetingDate').attr('min', new Date().toISOString().split('T')[0]);
        });

        function setupDateTimeHandlers() {
            $('#meetingDate, #startTime, #endTime').on('change', function() {
                updateHiddenDateTimeFields();
            });
        }

        function updateHiddenDateTimeFields() {
            const date = $('#meetingDate').val();
            const startTime = $('#startTime').val();

            if (date && startTime) {
                $('#hiddenScheduledDate').val(date + 'T' + startTime);
            }
        }

        function loadProjects() {
            $.get('/api/UserLookup/Projects')
                .done(function(data) {
                    const select = $('#projectSelect');
                    data.forEach(function(project) {
                        select.append(`<option value="${project.id}">${project.name}</option>`);
                    });
                })
                .fail(function() {
                    console.error('Failed to load projects');
                });
        }

        function setupParticipantSearch() {
            let searchTimeout;
            $('#participantSearch').on('input', function() {
                clearTimeout(searchTimeout);
                const query = $(this).val();

                if (query.length >= 2) {
                    searchTimeout = setTimeout(() => searchUsers(query), 300);
                } else {
                    $('#userSearchResults').empty();
                }
            });
        }

        function searchUsers(query) {
            $.get('/api/UserLookup/Users', { query: query })
                .done(function(data) {
                    const results = $('#userSearchResults');
                    results.empty();

                    data.forEach(function(user) {
                        if (!selectedParticipants.find(p => p.id === user.id)) {
                            results.append(`
                                <div class="user-result p-2 border-bottom" style="cursor: pointer;" onclick="addParticipant('${user.id}', '${user.fullName}', '${user.email}')">
                                    <strong>${user.fullName}</strong><br>
                                    <small class="text-muted">${user.email}</small>
                                </div>
                            `);
                        }
                    });
                })
                .fail(function() {
                    console.error('Failed to search users');
                });
        }

        function addParticipant(id, name, email) {
            selectedParticipants.push({ id, name, email });
            updateParticipantsDisplay();
            $('#participantSearch').val('');
            $('#userSearchResults').empty();
        }

        function removeParticipant(id) {
            selectedParticipants = selectedParticipants.filter(p => p.id !== id);
            updateParticipantsDisplay();
        }

        function updateParticipantsDisplay() {
            const container = $('#selectedParticipants');

            if (selectedParticipants.length === 0) {
                container.html('<small class="text-muted">No participants selected</small>');
            } else {
                let html = '';
                selectedParticipants.forEach(function(participant) {
                    html += `
                        <div class="participant-badge badge bg-primary me-2 mb-2 p-2">
                            ${participant.name}
                            <button type="button" class="btn-close btn-close-white ms-2" onclick="removeParticipant('${participant.id}')"></button>
                        </div>
                    `;
                });
                container.html(html);
            }
        }



        function saveDraft() {
            // Implementation for saving as draft
            alert('Draft functionality to be implemented');
        }

        // Form submission handler
        $('#meetingForm').on('submit', function(e) {
            updateHiddenDateTimeFields();

            // Add selected participants to form data
            selectedParticipants.forEach(function(participant, index) {
                $(this).append(`<input type="hidden" name="ParticipantIds[${index}]" value="${participant.id}">`);
            });
        });
    </script>
}

<style>
    .user-result:hover {
        background-color: rgb(248 250 252);
    }

    .dark .user-result:hover {
        background-color: rgb(55 65 81);
    }

    .participant-badge {
        display: inline-flex;
        align-items: center;
    }
</style>
