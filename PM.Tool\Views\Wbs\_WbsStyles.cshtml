<style>
    /* WBS Module Styles - Tailwind Compatible */

    /* Note: @@apply directives are used where possible to maintain Tailwind consistency */

    /* Project Selection Card Styles */
    .project-selection-card {
        background-color: white;
        border-radius: 0.75rem;
        border: 1px solid #e5e7eb;
        padding: 1.5rem;
        height: 100%;
        display: flex;
        flex-direction: column;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
    }

    .project-selection-card:hover {
        transform: translateY(-0.25rem);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        border-color: #bfdbfe;
    }

    .dark .project-selection-card {
        background-color: #1f2937;
        border-color: #374151;
    }

    .dark .project-selection-card:hover {
        border-color: #1d4ed8;
    }

    /* Status Pills for Project Selection */
    .status-pill {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.625rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .status-active {
        background-color: #dcfce7;
        color: #166534;
    }

    .status-completed {
        background-color: #dbeafe;
        color: #1e40af;
    }

    .status-planning {
        background-color: #fef3c7;
        color: #92400e;
    }

    .status-on-hold,
    .status-inactive {
        background-color: #f3f4f6;
        color: #374151;
    }

    .dark .status-active {
        background-color: #14532d;
        color: #bbf7d0;
    }

    .dark .status-completed {
        background-color: #1e3a8a;
        color: #bfdbfe;
    }

    .dark .status-planning {
        background-color: #92400e;
        color: #fde68a;
    }

    .dark .status-on-hold,
    .dark .status-inactive {
        background-color: #374151;
        color: #d1d5db;
    }

    /* Text line clamping utility */
    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* Animation for project cards */
    .project-selection-card {
        animation: fadeInUp 0.6s ease-out forwards;
        opacity: 0;
        transform: translateY(20px);
    }

    .project-selection-card:nth-child(1) { animation-delay: 0.1s; }
    .project-selection-card:nth-child(2) { animation-delay: 0.2s; }
    .project-selection-card:nth-child(3) { animation-delay: 0.3s; }
    .project-selection-card:nth-child(4) { animation-delay: 0.4s; }
    .project-selection-card:nth-child(5) { animation-delay: 0.5s; }
    .project-selection-card:nth-child(6) { animation-delay: 0.6s; }

    @@keyframes fadeInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Search and Filter Styles */
    .search-controls input:focus,
    .search-controls select:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .dark .search-controls input,
    .dark .search-controls select {
        background-color: #374151;
        border-color: #4b5563;
        color: white;
    }

    .dark .search-controls input:focus,
    .dark .search-controls select:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    /* Pagination Styles */
    .pagination-link {
        transition: all 0.2s ease-in-out;
    }

    .pagination-link:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .pagination-current {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3);
    }

    /* Responsive adjustments */
    @@media (max-width: 768px) {
        .project-selection-card {
            margin-bottom: 1rem;
        }

        .search-controls {
            flex-direction: column;
        }

        .search-controls > div {
            width: 100%;
            margin-bottom: 1rem;
        }

        .pagination-controls {
            flex-direction: column;
            gap: 1rem;
        }

        .pagination-controls nav {
            order: 1;
        }

        .pagination-controls .results-info {
            order: 2;
            text-align: center;
        }
    }

    /* Loading states */
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
        border-radius: 0.75rem;
    }

    .dark .loading-overlay {
        background: rgba(31, 41, 55, 0.8);
    }

    .loading-spinner {
        width: 2rem;
        height: 2rem;
        border: 2px solid #e5e7eb;
        border-top: 2px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @@keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Modal Styles */
    .modal-overlay {
        position: fixed;
        inset: 0;
        z-index: 1050;
        overflow-y: auto;
        background-color: rgba(0, 0, 0, 0.5);
        display: none !important;
        align-items: center;
        justify-content: center;
        padding: 1rem;
        backdrop-filter: blur(2px);
        animation: fadeIn 0.2s ease-out;
    }

    .modal-overlay.show {
        display: flex !important;
    }

    .modal-container {
        width: 100%;
        max-width: 42rem;
        margin: 0 auto;
        position: relative;
        animation: modalSlideIn 0.3s ease-out;
    }

    .modal-content {
        background-color: white;
        border-radius: 0.75rem;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        border: 1px solid #e5e7eb;
        max-height: 90vh;
        overflow-y: auto;
        position: relative;
        z-index: 1051;
    }

    .dark .modal-content {
        background-color: #1f2937;
        border-color: #374151;
    }

    .modal-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1.5rem;
        border-bottom: 1px solid #e5e7eb;
    }

    .dark .modal-header {
        border-bottom-color: #374151;
    }

    .modal-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #111827;
    }

    .dark .modal-title {
        color: white;
    }

    .modal-close-btn {
        color: #9ca3af;
        transition: color 0.2s;
    }

    .modal-close-btn:hover {
        color: #4b5563;
    }

    .dark .modal-close-btn:hover {
        color: #d1d5db;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 0.75rem;
        padding: 1.5rem;
        border-top: 1px solid #e5e7eb;
        background-color: #f9fafb;
    }

    .dark .modal-footer {
        border-top-color: #374151;
        background-color: #111827;
    }





    /* Form Styles */
    .form-group {
        margin-bottom: 1rem;
    }

    .form-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.5rem;
    }

    .dark .form-label {
        color: #d1d5db;
    }

    .form-input {
        width: 100%;
        padding: 0.5rem 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        transition: all 0.2s;
    }

    .form-input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .dark .form-input {
        background-color: #374151;
        border-color: #4b5563;
        color: white;
    }

    .dark .form-input:focus {
        border-color: #3b82f6;
    }

    /* Essential Select2 Styles for Modal */
    #createTaskModal .select2-container {
        width: 100% !important;
    }

    #createTaskModal .select2-dropdown {
        z-index: 9999 !important;
        border: 1px solid #d1d5db !important;
        border-radius: 0.375rem !important;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
    }

    #createTaskModal .select2-container--default .select2-selection--single {
        height: 2.5rem !important;
        border: 1px solid #d1d5db !important;
        border-radius: 0.375rem !important;
        background-color: white !important;
        display: flex !important;
        align-items: center !important;
    }

    #createTaskModal .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 2.25rem !important;
        padding-left: 0.75rem !important;
        color: #111827 !important;
    }

    #createTaskModal .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 2.25rem !important;
        right: 0.75rem !important;
    }

    #createTaskModal .select2-container--default .select2-results__option {
        padding: 0.5rem 0.75rem !important;
        color: #111827 !important;
    }

    #createTaskModal .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: #3b82f6 !important;
        color: white !important;
    }

    /* Search box styling */
    #createTaskModal .select2-search--dropdown {
        padding: 0.5rem !important;
    }

    #createTaskModal .select2-search--dropdown .select2-search__field {
        width: 100% !important;
        padding: 0.5rem 0.75rem !important;
        border: 1px solid #d1d5db !important;
        border-radius: 0.375rem !important;
        background-color: white !important;
        color: #111827 !important;
        font-size: 0.875rem !important;
    }

    #createTaskModal .select2-search--dropdown .select2-search__field:focus {
        outline: none !important;
        border-color: #3b82f6 !important;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
    }

    /* Dark mode support */
    .dark #createTaskModal .select2-container--default .select2-selection--single {
        background-color: #374151 !important;
        border-color: #4b5563 !important;
    }

    .dark #createTaskModal .select2-container--default .select2-selection--single .select2-selection__rendered {
        color: white !important;
    }

    .dark #createTaskModal .select2-dropdown {
        background-color: #374151 !important;
        border-color: #4b5563 !important;
    }

    .dark #createTaskModal .select2-container--default .select2-results__option {
        background-color: #374151 !important;
        color: white !important;
    }

    /* Dark mode search box */
    .dark #createTaskModal .select2-search--dropdown .select2-search__field {
        background-color: #374151 !important;
        border-color: #4b5563 !important;
        color: white !important;
    }

    .dark #createTaskModal .select2-search--dropdown .select2-search__field:focus {
        border-color: #3b82f6 !important;
    }

    /* Ensure Select2 works within modal */
    .modal-content .select2-container {
        position: relative !important;
    }

    .modal-content .select2-dropdown {
        position: absolute !important;
    }

    /* Dropdown Styles */
    .dropdown-menu {
        position: absolute;
        right: 0;
        z-index: 10;
        margin-top: 0.5rem;
        width: 12rem;
        border-radius: 0.5rem;
        background-color: white;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        padding: 0.25rem 0;
    }

    .dark .dropdown-menu {
        background-color: #1f2937;
        border-color: #374151;
    }

    .dropdown-item {
        display: flex;
        align-items: center;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        color: #374151;
        transition: background-color 0.2s;
        cursor: pointer;
    }

    .dropdown-item:hover {
        background-color: #f3f4f6;
    }

    .dark .dropdown-item {
        color: #d1d5db;
    }

    .dark .dropdown-item:hover {
        background-color: #374151;
    }

    .dropdown-divider {
        margin: 0.25rem 0;
        border-top: 1px solid #e5e7eb;
    }

    .dark .dropdown-divider {
        border-top-color: #4b5563;
    }

    /* Context Menu Styles */
    .context-menu {
        position: fixed;
        z-index: 50;
        background-color: white;
        border-radius: 0.5rem;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        padding: 0.5rem 0;
        min-width: 12rem;
    }

    .dark .context-menu {
        background-color: #1f2937;
        border-color: #374151;
    }

    .context-menu-item {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        color: #374151;
        cursor: pointer;
        display: flex;
        align-items: center;
        transition: background-color 0.2s;
    }

    .context-menu-item:hover {
        background-color: #f3f4f6;
    }

    .dark .context-menu-item {
        color: #d1d5db;
    }

    .dark .context-menu-item:hover {
        background-color: #374151;
    }

    .context-menu-divider {
        margin: 0.25rem 0;
        border-top: 1px solid #e5e7eb;
    }

    .dark .context-menu-divider {
        border-top-color: #4b5563;
    }

    /* WBS Container and Layout - Tailwind Compatible */
    .wbs-container {
        width: 100%;
        margin: 0 auto;
        padding: 2rem 1.5rem;
        min-height: 100vh;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    }

    .dark .wbs-container {
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    }

    .wbs-content {
        max-width: 80rem;
        margin: 0 auto;
        padding: 0 1rem;
    }

    /* Enhanced WBS Header Styles */
    .wbs-header-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        overflow: visible; /* Changed from hidden to visible to allow dropdowns */
        position: relative;
        z-index: 10; /* Ensure header has proper stacking context */
    }

    .wbs-header-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        pointer-events: none;
        border-radius: 1.5rem; /* Match parent border radius */
        z-index: 1; /* Ensure overlay doesn't interfere with dropdowns */
    }

    .dark .wbs-header-container {
        background: linear-gradient(135deg, #4c1d95 0%, #581c87 100%);
    }

    .wbs-header-content {
        padding: 1.5rem;
        position: relative;
        z-index: 2; /* Higher than overlay but lower than dropdowns */
    }

    .wbs-header-main {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .wbs-header-title-section {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .wbs-header-icon {
        width: 3rem;
        height: 3rem;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        flex-shrink: 0;
    }

    .wbs-header-icon i {
        font-size: 1.25rem;
        color: white;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .wbs-header-text {
        flex: 1;
    }

    .wbs-header-title {
        font-size: 1.875rem;
        font-weight: 700;
        color: white;
        margin: 0 0 0.25rem 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        letter-spacing: -0.025em;
        line-height: 1.2;
    }

    .wbs-header-subtitle {
        font-size: 1rem;
        color: rgba(255, 255, 255, 0.9);
        margin: 0;
        font-weight: 400;
        line-height: 1.3;
    }

    /* WBS Toolbar */
    .wbs-toolbar {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
        align-items: center;
        justify-content: flex-end;
        position: relative; /* Ensure proper positioning context for dropdowns */
        z-index: 20; /* Higher than header but lower than dropdowns */
    }

    /* Dropdown container positioning */
    .wbs-toolbar .relative {
        position: relative;
        z-index: 21; /* Ensure dropdown containers are properly stacked */
    }

    /* Enhanced Statistics Container */
    .wbs-stats-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
        margin-top: 1.5rem;
        padding-top: 1.5rem;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
    }

    .wbs-stat-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 0.75rem;
        padding: 1rem;
        text-align: center;
        transition: all 0.3s ease;
    }

    .wbs-stat-card:hover {
        transform: translateY(-2px);
        background: rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .wbs-stat-card .stat-label {
        font-size: 0.75rem;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 0.25rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .wbs-stat-card .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: white;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    /* Stat Card Color Variants */
    .stat-card-blue .stat-value { color: #93c5fd; }
    .stat-card-green .stat-value { color: #86efac; }
    .stat-card-yellow .stat-value { color: #fde047; }
    .stat-card-purple .stat-value { color: #c4b5fd; }
    .stat-card-red .stat-value { color: #fca5a5; }
    .stat-card-gray .stat-value { color: #d1d5db; }

    .wbs-tasks-container {
        border-radius: 1.5rem;
        padding: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(12px);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        background: rgba(255, 255, 255, 0.8);
    }

    .dark .wbs-tasks-container {
        background: rgba(31, 41, 55, 0.8);
        border-color: rgba(75, 85, 99, 0.3);
    }

    /* WBS Node Styles - Tailwind Compatible */
    .wbs-node {
        position: relative;
        padding: 2rem;
        margin-bottom: 2rem;
        border-radius: 1rem;
        border: 1px solid rgba(229, 231, 235, 0.8);
        backdrop-filter: blur(8px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        background-color: rgba(255, 255, 255, 0.95);
        animation: fadeInUp 0.3s ease-out;
    }

    .wbs-node:hover {
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        transform: translateY(-0.25rem);
        border-color: #d1d5db;
    }

    .dark .wbs-node {
        background-color: rgba(31, 41, 55, 0.95);
        border-color: rgba(75, 85, 99, 0.8);
    }

    .dark .wbs-node:hover {
        border-color: #6b7280;
    }

    .wbs-node.selected {
        border-color: #3b82f6;
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
    }

    .wbs-node.menu-active {
        z-index: 50;
        position: relative;
    }

    /* Task Actions - Tailwind Compatible */
    .task-actions {
        opacity: 0;
        display: flex;
        gap: 0.5rem;
        align-items: center;
        margin-left: 1rem;
        transition: all 0.3s ease-in-out;
    }

    .wbs-node:hover .task-actions {
        opacity: 1;
    }

    .action-btn {
        padding: 0.5rem;
        border-radius: 0.5rem;
        border: 1px solid transparent;
        transition: all 0.2s ease-in-out;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 2rem;
        height: 2rem;
        font-size: 0.875rem;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        backdrop-filter: blur(4px);
        background-color: rgba(255, 255, 255, 0.9);
    }

    .action-btn:hover {
        transform: translateY(-0.125rem);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border-color: rgba(0, 0, 0, 0.1);
    }

    .dark .action-btn {
        background-color: rgba(31, 41, 55, 0.9);
        border-color: rgba(75, 85, 99, 0.3);
    }

    .dark .action-btn:hover {
        border-color: rgba(156, 163, 175, 0.5);
    }

    /* Task Menu - Tailwind Compatible */
    .task-menu {
        position: absolute;
        right: 0;
        top: 2.5rem;
        border: 1px solid #e5e7eb;
        border-radius: 0.75rem;
        padding: 0.5rem;
        backdrop-filter: blur(12px);
        min-width: 14rem;
        z-index: 50;
        background-color: rgba(255, 255, 255, 0.98);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        animation: slideDown 0.2s ease-out;
    }

    .dark .task-menu {
        background-color: rgba(31, 41, 55, 0.98);
        border-color: #6b7280;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 20px 25px -5px rgba(0, 0, 0, 0.3);
    }

    .menu-item {
        display: flex;
        align-items: center;
        width: 100%;
        padding: 0.75rem 1rem;
        text-align: left;
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151;
        transition: all 0.2s ease-in-out;
        border: none;
        background: transparent;
        cursor: pointer;
        border-radius: 0.5rem;
        margin-bottom: 0.125rem;
    }

    .menu-item:hover {
        background-color: #f8fafc;
        color: #1e293b;
        transform: translateX(0.125rem);
    }

    .dark .menu-item {
        color: #d1d5db;
    }

    .dark .menu-item:hover {
        background-color: rgba(55, 65, 81, 0.8);
        color: #f1f5f9;
    }

    .menu-divider {
        height: 1px;
        margin: 0.5rem 0;
        background: linear-gradient(90deg, transparent, #e5e7eb, transparent);
    }

    .dark .menu-divider {
        background: linear-gradient(90deg, transparent, #6b7280, transparent);
    }

    /* Enterprise Dropdown Menus */
    .dropdown-menu {
        position: absolute;
        right: 0;
        top: 3rem;
        background-color: white;
        border: 1px solid #e5e7eb;
        border-radius: 0.75rem;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        backdrop-filter: blur(12px);
        padding: 0.5rem;
        min-width: 14rem;
        z-index: 9999; /* Increased z-index to ensure dropdowns appear above everything */
        animation: slideDown 0.2s ease-out;
        max-height: 400px; /* Add max height to prevent extremely long dropdowns */
        overflow-y: auto; /* Add scroll if content is too long */
    }

    .dark .dropdown-menu {
        background-color: #1f2937;
        border-color: #374151;
    }

    .dropdown-item {
        display: flex;
        align-items: center;
        width: 100%;
        padding: 0.75rem 1rem;
        text-align: left;
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151;
        transition: all 0.2s ease-in-out;
        border: none;
        background: transparent;
        cursor: pointer;
        border-radius: 0.5rem;
        margin-bottom: 0.25rem;
    }

    .dropdown-item:hover {
        background-color: #f9fafb;
        color: #111827;
    }

    .dark .dropdown-item {
        color: #d1d5db;
    }

    .dark .dropdown-item:hover {
        background-color: #374151;
        color: #f9fafb;
    }

    .dropdown-divider {
        height: 1px;
        margin: 0.5rem 0;
        background-color: #e5e7eb;
    }

    .dark .dropdown-divider {
        background-color: #374151;
    }

    /* WBS Statistics */
    .wbs-stats {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        margin-top: 1rem;
    }

    @@media (min-width: 768px) {
        .wbs-stats {
            grid-template-columns: repeat(4, 1fr);
        }

        .wbs-stats-container {
            grid-template-columns: repeat(3, 1fr);
        }

        .wbs-header-main {
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
        }

        .wbs-toolbar {
            justify-content: flex-end;
        }
    }

    @@media (min-width: 1024px) {
        .wbs-stats {
            grid-template-columns: repeat(6, 1fr);
        }

        .wbs-stats-container {
            grid-template-columns: repeat(6, 1fr);
        }

        .wbs-header-title {
            font-size: 2.25rem;
        }

        .wbs-header-subtitle {
            font-size: 1.125rem;
        }
    }

    .stat-card {
        border-radius: 0.5rem;
        padding: 0.75rem;
        backdrop-filter: blur(4px);
    }

    .stat-label {
        font-size: 0.875rem;
        font-weight: 500;
        margin-bottom: 0.25rem;
    }

    .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
    }

    /* WBS Code Styles */
    .wbs-code {
        font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
        font-weight: 700;
        letter-spacing: 0.05em;
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
        border-radius: 0.375rem;
        border: 1px solid rgba(0, 0, 0, 0.15);
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        backdrop-filter: blur(4px);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .dark .wbs-code {
        border-color: rgba(255, 255, 255, 0.1);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    /* Badge Styles */
    .priority-badge {
        font-weight: 700;
        letter-spacing: 0.025em;
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
        border-radius: 9999px;
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        position: relative;
        overflow: hidden;
        color: white;
        border: 1px solid transparent;
        transition: all 0.2s ease-in-out;
    }

    /* Priority Badge Variants */
    .priority-critical {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        border-color: rgba(220, 38, 38, 0.3);
        box-shadow: 0 2px 4px rgba(220, 38, 38, 0.2);
    }

    .priority-high {
        background: linear-gradient(135deg, #ea580c 0%, #c2410c 100%);
        border-color: rgba(234, 88, 12, 0.3);
        box-shadow: 0 2px 4px rgba(234, 88, 12, 0.2);
    }

    .priority-medium {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        border-color: rgba(37, 99, 235, 0.3);
        box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
    }

    .priority-low {
        background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
        border-color: rgba(22, 163, 74, 0.3);
        box-shadow: 0 2px 4px rgba(22, 163, 74, 0.2);
    }

    /* Badge Hover Effects */
    .priority-badge:hover,
    .status-badge:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .priority-badge::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .priority-badge:hover::before {
        left: 100%;
    }

    .status-badge {
        font-weight: 600;
        letter-spacing: 0.025em;
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
        border-radius: 0.5rem;
        backdrop-filter: blur(4px);
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        color: white;
        border: 1px solid transparent;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        transition: all 0.2s ease-in-out;
    }

    /* Status Badge Variants */
    .status-done {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        border-color: rgba(16, 185, 129, 0.3);
        box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
    }

    .status-inprogress {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        border-color: rgba(59, 130, 246, 0.3);
        box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
    }

    .status-inreview {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        border-color: rgba(139, 92, 246, 0.3);
        box-shadow: 0 2px 4px rgba(139, 92, 246, 0.2);
    }

    .status-cancelled {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        border-color: rgba(239, 68, 68, 0.3);
        box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
    }

    .status-todo {
        background: linear-gradient(135deg, #64748b 0%, #475569 100%);
        border-color: rgba(100, 116, 139, 0.3);
        box-shadow: 0 2px 4px rgba(100, 116, 139, 0.2);
    }

    .overdue-badge {
        font-weight: 700;
        animation: pulse 2s infinite;
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
        border: 1px solid #fca5a5;
        box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1), 0 2px 4px 0 rgba(239, 68, 68, 0.2);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        padding: 0.375rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
    }

    /* Task Details */
    .task-details {
        background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);
        backdrop-filter: blur(4px);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin: 1.5rem 0 0 2rem;
        border: 1px solid rgba(226, 232, 240, 0.8);
        font-size: 0.875rem;
        box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    }

    .dark .task-details {
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        border-color: #475569;
        color: #cbd5e1;
    }

    /* Progress Bar Colors */
    .progress-bar-red {
        background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
        box-shadow: 0 1px 2px 0 rgba(239, 68, 68, 0.3);
    }
    .progress-bar-orange {
        background: linear-gradient(90deg, #f97316 0%, #ea580c 100%);
        box-shadow: 0 1px 2px 0 rgba(249, 115, 22, 0.3);
    }
    .progress-bar-yellow {
        background: linear-gradient(90deg, #eab308 0%, #ca8a04 100%);
        box-shadow: 0 1px 2px 0 rgba(234, 179, 8, 0.3);
    }
    .progress-bar-blue {
        background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
        box-shadow: 0 1px 2px 0 rgba(59, 130, 246, 0.3);
    }
    .progress-bar-green {
        background: linear-gradient(90deg, #22c55e 0%, #16a34a 100%);
        box-shadow: 0 1px 2px 0 rgba(34, 197, 94, 0.3);
    }

    /* Progress Bar Container */
    .progress-container {
        background-color: #e5e7eb;
        border-radius: 9999px;
        height: 0.5rem;
        overflow: hidden;
        box-shadow: inset 0 1px 2px 0 rgba(0, 0, 0, 0.1);
    }

    .dark .progress-container {
        background-color: #374151;
    }

    /* Task Title */
    .task-title {
        font-weight: 600;
        font-size: 1.125rem;
        line-height: 1.4;
        color: #1f2937;
        margin: 0.5rem 0;
        letter-spacing: -0.025em;
    }

    .dark .task-title {
        color: #f9fafb;
    }

    /* Task Description */
    .task-description {
        line-height: 1.6;
        max-width: 65ch;
        color: #6b7280;
        font-size: 0.875rem;
        margin: 1rem 0 1.5rem 0;
        padding: 1rem 0 1rem 2rem;
        border-left: 4px solid #e5e7eb;
        background: rgba(248, 250, 252, 0.5);
        border-radius: 0 0.5rem 0.5rem 0;
    }

    .dark .task-description {
        color: #9ca3af;
        border-left-color: #374151;
    }

    /* Compact View */
    .compact-view .wbs-node {
        padding: 1rem;
        margin-bottom: 0.75rem;
    }

    .compact-view .task-details {
        display: none;
    }

    .compact-view .task-description {
        display: none;
    }

    .compact-view .task-title {
        font-size: 1rem;
        margin: 0.25rem 0;
    }

    .compact-view .action-btn {
        min-width: 1.5rem;
        height: 1.5rem;
        padding: 0.25rem;
        font-size: 0.75rem;
    }

    .compact-view .priority-badge,
    .compact-view .status-badge {
        font-size: 0.625rem;
        padding: 0.25rem 0.5rem;
    }

    .compact-view .wbs-code {
        font-size: 0.625rem;
        padding: 0.25rem 0.5rem;
    }

    /* Animations */
    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @@keyframes fadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }

    @@keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    /* Responsive adjustments */
    @@media (max-width: 768px) {
        .wbs-container {
            padding: 1.5rem 1rem;
        }

        .wbs-content {
            padding: 0 0.5rem;
        }

        .wbs-header-content {
            padding: 1rem;
        }

        .wbs-header-title-section {
            flex-direction: column;
            text-align: center;
            gap: 0.75rem;
        }

        .wbs-header-title {
            font-size: 1.5rem;
        }

        .wbs-header-subtitle {
            font-size: 0.875rem;
        }

        .wbs-toolbar {
            justify-content: center;
            gap: 0.5rem;
        }

        .wbs-stats-container {
            grid-template-columns: 1fr;
            gap: 0.5rem;
            margin-top: 1rem;
            padding-top: 1rem;
        }

        .wbs-stat-card {
            padding: 0.75rem;
        }

        .wbs-stat-card .stat-value {
            font-size: 1.25rem;
        }

        .wbs-stat-card .stat-label {
            font-size: 0.6875rem;
        }

        .wbs-tasks-container {
            padding: 1.5rem;
        }

        .task-actions {
            opacity: 1;
            margin-left: 0.5rem;
            gap: 0.25rem;
        }

        .modal-container {
            max-width: 100%;
            margin: 0 1rem;
        }

        .wbs-node {
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .task-details {
            margin: 1rem 0 0 0;
            padding: 1rem;
        }

        .action-btn {
            min-width: 1.75rem;
            height: 1.75rem;
            font-size: 0.75rem;
            padding: 0.375rem;
        }

        .task-menu {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            border-radius: 1rem 1rem 0 0;
            max-width: none;
            min-width: auto;
            box-shadow: 0 -20px 25px -5px rgba(0, 0, 0, 0.1);
            padding: 1rem;
        }

        .task-title {
            font-size: 1rem;
            margin: 0.375rem 0;
        }

        .priority-badge,
        .status-badge {
            font-size: 0.625rem;
            padding: 0.25rem 0.5rem;
        }

        .wbs-code {
            font-size: 0.625rem;
            padding: 0.25rem 0.5rem;
        }

        .task-description {
            margin: 0.75rem 0 0 0;
            padding: 0.75rem 0 0.75rem 1.5rem;
            font-size: 0.8125rem;
        }
    }

    @@media (max-width: 480px) {
        .wbs-container {
            padding: 1rem 0.75rem;
        }

        .wbs-header {
            padding: 1.5rem 1rem;
            margin-bottom: 1.5rem;
        }

        .wbs-header h1 {
            font-size: 1.75rem;
        }

        .wbs-tasks-container {
            padding: 1rem;
        }

        .wbs-node {
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .task-title {
            font-size: 0.9375rem;
            margin: 0.25rem 0;
        }

        .task-description {
            font-size: 0.75rem;
            margin: 0.375rem 0 0 0;
            padding-left: 0.75rem;
        }

        .task-details {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
        }

        .action-btn {
            min-width: 1.5rem;
            height: 1.5rem;
            font-size: 0.6875rem;
        }

        .priority-badge,
        .status-badge,
        .wbs-code {
            font-size: 0.5625rem;
            padding: 0.1875rem 0.375rem;
        }
    }

    /* Animation keyframes */
    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @@keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @@keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.7;
        }
    }

    /* Utility Classes */
    .text-shadow {
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    /* Focus styles for accessibility */
    .action-btn:focus,
    .menu-item:focus {
        outline: 2px solid #3b82f6;
        outline-offset: 2px;
    }

    /* High contrast mode support */
    @@media (prefers-contrast: high) {
        .wbs-node {
            border-width: 2px;
        }

        .priority-badge,
        .status-badge {
            border-width: 1px;
            border-style: solid;
        }
    }
</style>
