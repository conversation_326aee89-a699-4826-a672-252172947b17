@model PM.Tool.Models.ViewModels.MeetingEditViewModel
@using PM.Tool.Core.Enums
@{
    ViewData["Title"] = "Edit Meeting";
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Meetings", Href = Url.Action("Index", "Meeting"), Icon = "fas fa-calendar" },
        new { Text = Model.Title, Href = Url.Action("Details", new { id = Model.Id }), Icon = "fas fa-info-circle" },
        new { Text = "Edit", Href = "", Icon = "fas fa-edit" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Page Header -->
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-edit mr-3 text-primary-600 dark:text-primary-400"></i>
                Edit Meeting
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Update meeting details and settings
            </p>
        </div>
        <div class="flex space-x-3">
            @{
                ViewData["Text"] = "View Details";
                ViewData["Variant"] = "outline";
                ViewData["Icon"] = "fas fa-eye";
                ViewData["Href"] = Url.Action("Details", new { id = Model.Id });
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "Back to Meetings";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Index");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<div class="max-w-4xl mx-auto">
    <form asp-action="Edit" method="post" id="meetingForm">
        <input type="hidden" asp-for="Id" />
        <input type="hidden" asp-for="OrganizerUserId" />
        <input type="hidden" asp-for="CreatedAt" />

        <div asp-validation-summary="ModelOnly" class="alert-danger-custom mb-6"></div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Form -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Basic Information -->
                <div class="card-custom">
                    <div class="card-header-custom">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                                <i class="fas fa-info-circle text-primary-600 dark:text-primary-400 text-sm"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Basic Information</h3>
                                <p class="text-sm text-neutral-500 dark:text-dark-400">Meeting title and description</p>
                            </div>
                        </div>
                    </div>
                    <div class="card-body-custom space-y-6">
                        <div>
                            @{
                                ViewData["Label"] = "Meeting Title";
                                ViewData["Name"] = "Title";
                                ViewData["Type"] = "text";
                                ViewData["Placeholder"] = "Enter meeting title";
                                ViewData["Required"] = true;
                                ViewData["Value"] = Model.Title;
                            }
                            <partial name="Components/_FormInput" view-data="ViewData" />
                            <span asp-validation-for="Title" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                        </div>

                        <div>
                            @{
                                ViewData["Label"] = "Description";
                                ViewData["Name"] = "Description";
                                ViewData["Type"] = "textarea";
                                ViewData["Placeholder"] = "Meeting description and agenda";
                                ViewData["Rows"] = 4;
                                ViewData["Value"] = Model.Description;
                            }
                            <partial name="Components/_FormInput" view-data="ViewData" />
                            <span asp-validation-for="Description" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                @{
                                    ViewData["Label"] = "Meeting Type";
                                    ViewData["Name"] = "Type";
                                    ViewData["Type"] = "select";
                                    ViewData["Required"] = true;
                                    ViewData["Options"] = Html.GetEnumSelectList<MeetingType>().Select(x => new { Value = x.Value, Text = x.Text });
                                    ViewData["Value"] = ((int)Model.Type).ToString();
                                }
                                <partial name="Components/_FormInput" view-data="ViewData" />
                                <span asp-validation-for="Type" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                            </div>

                            <div>
                                @{
                                    ViewData["Label"] = "Status";
                                    ViewData["Name"] = "Status";
                                    ViewData["Type"] = "select";
                                    ViewData["Required"] = true;
                                    ViewData["Options"] = Html.GetEnumSelectList<MeetingStatus>().Select(x => new { Value = x.Value, Text = x.Text });
                                    ViewData["Value"] = ((int)Model.Status).ToString();
                                }
                                <partial name="Components/_FormInput" view-data="ViewData" />
                                <span asp-validation-for="Status" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Date and Time -->
                <div class="card-custom">
                    <div class="card-header-custom">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-warning-100 dark:bg-warning-900 rounded-lg flex items-center justify-center">
                                <i class="fas fa-clock text-warning-600 dark:text-warning-400 text-sm"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Date & Time</h3>
                                <p class="text-sm text-neutral-500 dark:text-dark-400">When the meeting will take place</p>
                            </div>
                        </div>
                    </div>
                    <div class="card-body-custom space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                @{
                                    ViewData["Label"] = "Date";
                                    ViewData["Name"] = "meetingDate";
                                    ViewData["Type"] = "date";
                                    ViewData["Required"] = true;
                                    ViewData["Value"] = Model.ScheduledDate.ToString("yyyy-MM-dd");
                                    ViewData["Id"] = "meetingDate";
                                }
                                <partial name="Components/_FormInput" view-data="ViewData" />
                            </div>

                            <div>
                                @{
                                    ViewData["Label"] = "Start Time";
                                    ViewData["Name"] = "startTime";
                                    ViewData["Type"] = "time";
                                    ViewData["Required"] = true;
                                    ViewData["Value"] = Model.ScheduledDate.ToString("HH:mm");
                                    ViewData["Id"] = "startTime";
                                }
                                <partial name="Components/_FormInput" view-data="ViewData" />
                            </div>

                            <div>
                                @{
                                    ViewData["Label"] = "Duration (minutes)";
                                    ViewData["Name"] = "DurationMinutes";
                                    ViewData["Type"] = "number";
                                    ViewData["Required"] = true;
                                    ViewData["Value"] = Model.DurationMinutes.ToString();
                                    ViewData["Min"] = "15";
                                    ViewData["Max"] = "480";
                                    ViewData["Step"] = "15";
                                }
                                <partial name="Components/_FormInput" view-data="ViewData" />
                                <span asp-validation-for="DurationMinutes" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                            </div>
                        </div>

                        <!-- Hidden field for ScheduledDate -->
                        <input asp-for="ScheduledDate" type="hidden" id="hiddenScheduledDate" />
                    </div>
                </div>

                <!-- Location and Settings -->
                <div class="card-custom">
                    <div class="card-header-custom">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-success-100 dark:bg-success-900 rounded-lg flex items-center justify-center">
                                <i class="fas fa-map-marker-alt text-success-600 dark:text-success-400 text-sm"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Location & Settings</h3>
                                <p class="text-sm text-neutral-500 dark:text-dark-400">Where the meeting will take place</p>
                            </div>
                        </div>
                    </div>
                    <div class="card-body-custom space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                @{
                                    ViewData["Label"] = "Location";
                                    ViewData["Name"] = "Location";
                                    ViewData["Type"] = "text";
                                    ViewData["Placeholder"] = "Meeting room or address";
                                    ViewData["Value"] = Model.Location;
                                }
                                <partial name="Components/_FormInput" view-data="ViewData" />
                                <span asp-validation-for="Location" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                            </div>

                            <div>
                                @{
                                    ViewData["Label"] = "Meeting Link";
                                    ViewData["Name"] = "MeetingLink";
                                    ViewData["Type"] = "url";
                                    ViewData["Placeholder"] = "https://zoom.us/j/...";
                                    ViewData["Value"] = Model.MeetingLink;
                                }
                                <partial name="Components/_FormInput" view-data="ViewData" />
                                <span asp-validation-for="MeetingLink" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                            </div>
                        </div>

                        <div class="space-y-4">
                            <div class="flex items-center">
                                <input type="checkbox" asp-for="SendReminders" class="form-checkbox-custom" />
                                <label asp-for="SendReminders" class="ml-3 text-sm font-medium text-neutral-700 dark:text-dark-200">
                                    Send reminder notifications
                                </label>
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" asp-for="IsRecurring" class="form-checkbox-custom" id="isRecurring" />
                                <label for="isRecurring" class="ml-3 text-sm font-medium text-neutral-700 dark:text-dark-200">
                                    Recurring meeting
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Agenda -->
                <div class="card-custom">
                    <div class="card-header-custom">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-info-100 dark:bg-info-900 rounded-lg flex items-center justify-center">
                                <i class="fas fa-list text-info-600 dark:text-info-400 text-sm"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Agenda</h3>
                                <p class="text-sm text-neutral-500 dark:text-dark-400">Meeting topics and discussion points</p>
                            </div>
                        </div>
                    </div>
                    <div class="card-body-custom">
                        @{
                            ViewData["Label"] = "Meeting Agenda";
                            ViewData["Name"] = "Agenda";
                            ViewData["Type"] = "textarea";
                            ViewData["Placeholder"] = "Enter meeting agenda items...";
                            ViewData["Rows"] = 6;
                            ViewData["Value"] = Model.Agenda;
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />
                        <span asp-validation-for="Agenda" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Project Assignment -->
                <div class="card-custom">
                    <div class="card-header-custom">
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Project</h3>
                    </div>
                    <div class="card-body-custom">
                        @{
                            ViewData["Label"] = "Assign to Project";
                            ViewData["Name"] = "ProjectId";
                            ViewData["Type"] = "select";
                            ViewData["Options"] = new List<object> { new { Value = "", Text = "No project" } };
                            ViewData["Value"] = Model.ProjectId.ToString();
                            ViewData["Id"] = "projectSelect";
                        }
                        <partial name="Components/_FormInput" view-data="ViewData" />
                        <span asp-validation-for="ProjectId" class="text-danger-600 dark:text-danger-400 text-sm"></span>
                    </div>
                </div>

                <!-- Meeting Actions -->
                <div class="card-custom">
                    <div class="card-header-custom">
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Actions</h3>
                    </div>
                    <div class="card-body-custom">
                        @{
                            ViewData["SubmitText"] = "Save Changes";
                            ViewData["SubmitIcon"] = "fas fa-save";
                            ViewData["CancelUrl"] = Url.Action("Details", new { id = Model.Id });
                            ViewData["ContainerClass"] = "space-y-3";
                            ViewData["AdditionalButtons"] = Model.Status == MeetingStatus.Scheduled ?
                                $"<button type='button' class='btn-danger-custom w-full' onclick='cancelMeeting({Model.Id})'><i class='fas fa-ban mr-2'></i>Cancel Meeting</button>" : "";
                        }
                        <partial name="Components/_FormActions" view-data="ViewData" />
                    </div>
                </div>

                <!-- Meeting Info -->
                <div class="card-custom">
                    <div class="card-header-custom">
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Meeting Info</h3>
                    </div>
                    <div class="card-body-custom space-y-4">
                        <div>
                            <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400">Created</h4>
                            <p class="text-sm text-neutral-900 dark:text-dark-100">@Model.CreatedAt.ToString("MMM dd, yyyy 'at' HH:mm")</p>
                        </div>

                        <div>
                            <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400">Organizer</h4>
                            <p class="text-sm text-neutral-900 dark:text-dark-100">@Model.Organizer</p>
                        </div>

                        @if (Model.UpdatedAt.HasValue)
                        {
                            <div>
                                <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400">Last Updated</h4>
                                <p class="text-sm text-neutral-900 dark:text-dark-100">@Model.UpdatedAt.Value.ToString("MMM dd, yyyy 'at' HH:mm")</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function() {
            loadProjects();
            setupDateTimeHandlers();

            // Set minimum date to today for future meetings
            if (new Date('@Model.ScheduledDate.ToString("yyyy-MM-dd")') >= new Date()) {
                $('#meetingDate').attr('min', new Date().toISOString().split('T')[0]);
            }
        });

        function setupDateTimeHandlers() {
            $('#meetingDate, #startTime').on('change', function() {
                updateHiddenDateTimeFields();
            });
        }

        function updateHiddenDateTimeFields() {
            const date = $('#meetingDate').val();
            const startTime = $('#startTime').val();

            if (date && startTime) {
                $('#hiddenScheduledDate').val(date + 'T' + startTime);
            }
        }

        function loadProjects() {
            $.get('/api/UserLookup/Projects')
                .done(function(data) {
                    const select = $('#projectSelect');
                    data.forEach(function(project) {
                        const selected = project.id == @Model.ProjectId ? 'selected' : '';
                        select.append(`<option value="${project.id}" ${selected}>${project.name}</option>`);
                    });
                })
                .fail(function() {
                    console.error('Failed to load projects');
                });
        }

        function cancelMeeting(meetingId) {
            const reason = prompt('Please provide a reason for cancelling this meeting:');
            if (reason) {
                $.post('@Url.Action("Cancel", "Meeting")', { id: meetingId, reason: reason })
                    .done(function() {
                        alert('Meeting cancelled successfully.');
                        window.location.href = '@Url.Action("Index")';
                    })
                    .fail(function() {
                        alert('Failed to cancel meeting. Please try again.');
                    });
            }
        }

        // Form submission handler
        $('#meetingForm').on('submit', function(e) {
            updateHiddenDateTimeFields();
        });
    </script>
}
