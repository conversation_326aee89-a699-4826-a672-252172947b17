@{
    ViewData["Title"] = "Analytics Dashboard";
    var projects = ViewBag.Projects as IEnumerable<PM.Tool.Core.Entities.Project> ?? new List<PM.Tool.Core.Entities.Project>();
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div class="mb-4 sm:mb-0">
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="@Url.Action("Index", "Home")" class="text-neutral-500 hover:text-neutral-700 dark:text-dark-400 dark:hover:text-dark-200">
                            <i class="fas fa-home mr-2"></i>Dashboard
                        </a>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-neutral-400 dark:text-dark-500 mx-2"></i>
                            <span class="text-neutral-700 dark:text-dark-300">Analytics</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-chart-line mr-3 text-primary-600 dark:text-primary-400"></i>
                Analytics Dashboard
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Comprehensive project insights and performance metrics
            </p>
        </div>

        <div class="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
            <!-- Export Data Button -->
            @{
                ViewData["Text"] = "Export Data";
                ViewData["Variant"] = "outline";
                ViewData["Icon"] = "fas fa-download";
                ViewData["OnClick"] = "exportAnalyticsData()";
            }
            <partial name="Components/_Button" view-data="ViewData" />

            <!-- Project Filter Dropdown -->
            <div class="relative">
                @{
                    ViewData["Text"] = "Filter Projects";
                    ViewData["Variant"] = "primary";
                    ViewData["Icon"] = "fas fa-filter";
                    ViewData["OnClick"] = "toggleProjectFilter()";
                    ViewData["Id"] = "project-filter-toggle";
                }
                <partial name="Components/_Button" view-data="ViewData" />

                <div id="project-filter-menu"
                     class="absolute right-0 mt-2 w-64 bg-white dark:bg-surface-dark rounded-xl shadow-lg border border-neutral-200 dark:border-dark-200 py-2 hidden max-h-64 overflow-y-auto z-50">
                    @if (projects.Any())
                    {
                        <div class="px-4 py-2 border-b border-neutral-200 dark:border-dark-200">
                            <p class="text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wide">Select Project</p>
                        </div>
                        @foreach (var project in projects)
                        {
                            <a href="@Url.Action("Project", new { id = project.Id })"
                               class="flex items-center px-4 py-3 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-50 dark:hover:bg-dark-700 transition-colors">
                                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-project-diagram text-primary-600 dark:text-primary-400 text-xs"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="font-medium">@project.Name</p>
                                    <p class="text-xs text-neutral-500 dark:text-dark-400">@project.Status</p>
                                </div>
                            </a>
                        }
                    }
                    else
                    {
                        <div class="px-4 py-8 text-center">
                            <div class="w-12 h-12 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-project-diagram text-neutral-400 dark:text-dark-500"></i>
                            </div>
                            <p class="text-sm text-neutral-500 dark:text-dark-400">No projects available</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Overview Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
    <!-- Total Projects -->
    @{
        ViewData["Title"] = "Total Projects";
        ViewData["Icon"] = "fas fa-project-diagram";
        ViewData["IconColor"] = "bg-gradient-to-br from-purple-500 to-purple-600";
        ViewData["Value"] = projects.Count().ToString();
        ViewData["Description"] = "All projects in system";
        ViewData["Trend"] = "+12% from last month";
        ViewData["TrendIcon"] = "fas fa-arrow-up";
        ViewData["TrendColor"] = "text-success-600 dark:text-success-400";
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />

    <!-- Active Projects -->
    @{
        ViewData["Title"] = "Active Projects";
        ViewData["Icon"] = "fas fa-check-circle";
        ViewData["IconColor"] = "bg-gradient-to-br from-success-500 to-success-600";
        ViewData["Value"] = projects.Count(p => p.Status == PM.Tool.Core.Enums.ProjectStatus.Active).ToString();
        ViewData["Description"] = "Currently in progress";
        ViewData["Trend"] = "+5% from last month";
        ViewData["TrendIcon"] = "fas fa-arrow-up";
        ViewData["TrendColor"] = "text-success-600 dark:text-success-400";
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />

    <!-- Total Tasks -->
    @{
        ViewData["Title"] = "Total Tasks";
        ViewData["Icon"] = "fas fa-tasks";
        ViewData["IconColor"] = "bg-gradient-to-br from-info-500 to-info-600";
        ViewData["Value"] = projects.Sum(p => p.TotalTasks).ToString();
        ViewData["Description"] = "Across all projects";
        ViewData["Trend"] = "+18% from last month";
        ViewData["TrendIcon"] = "fas fa-arrow-up";
        ViewData["TrendColor"] = "text-success-600 dark:text-success-400";
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />

    <!-- Average Progress -->
    @{
        var avgProgress = projects.Any() ? projects.Average(p => p.ProgressPercentage) : 0;
        ViewData["Title"] = "Average Progress";
        ViewData["Icon"] = "fas fa-chart-line";
        ViewData["IconColor"] = "bg-gradient-to-br from-warning-500 to-warning-600";
        ViewData["Value"] = avgProgress.ToString("F0") + "%";
        ViewData["Description"] = "Overall completion rate";
        ViewData["Progress"] = avgProgress;
        ViewData["Trend"] = "+8% from last month";
        ViewData["TrendIcon"] = "fas fa-arrow-up";
        ViewData["TrendColor"] = "text-success-600 dark:text-success-400";
    }
    <partial name="Components/_StatsCard" view-data="ViewData" />
</div>

<!-- Analytics Options -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <!-- Project Analytics -->
    @{
        ViewData["Title"] = "Project Analytics";
        ViewData["Icon"] = "fas fa-chart-bar";
        ViewData["IconColor"] = "bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-400";
    }
    <partial name="Components/_Card" view-data="ViewData">
        <div class="text-center">
            <div class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-chart-bar text-2xl text-primary-600 dark:text-primary-400"></i>
            </div>
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-3">Project Analytics</h3>
            <p class="text-neutral-600 dark:text-dark-300 mb-6 text-sm leading-relaxed">
                Detailed insights into individual project performance, task completion rates, and team productivity metrics.
            </p>

            @if (projects.Any())
            {
                <div class="relative">
                    @{
                        ViewData["Text"] = "Select Project";
                        ViewData["Variant"] = "primary";
                        ViewData["Icon"] = "fas fa-chevron-down";
                        ViewData["Classes"] = "w-full justify-center";
                        ViewData["Id"] = "project-analytics-toggle";
                        ViewData["OnClick"] = "toggleProjectAnalytics()";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    <div id="project-analytics-menu"
                         class="absolute left-0 right-0 mt-2 bg-white dark:bg-surface-dark rounded-xl shadow-lg border border-neutral-200 dark:border-dark-200 py-2 hidden max-h-48 overflow-y-auto z-40">
                        <div class="px-4 py-2 border-b border-neutral-200 dark:border-dark-200">
                            <p class="text-xs font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wide">Available Projects</p>
                        </div>
                        @foreach (var project in projects)
                        {
                            <a href="@Url.Action("Project", new { id = project.Id })"
                               class="flex items-center px-4 py-3 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-50 dark:hover:bg-dark-700 transition-colors">
                                <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-project-diagram text-primary-600 dark:text-primary-400 text-xs"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="font-medium">@project.Name</p>
                                    <p class="text-xs text-neutral-500 dark:text-dark-400">@project.Status • @project.ProgressPercentage.ToString("F0")% complete</p>
                                </div>
                            </a>
                        }
                    </div>
                </div>
            }
            else
            {
                <div class="text-center py-6">
                    <div class="w-12 h-12 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-project-diagram text-neutral-400 dark:text-dark-500"></i>
                    </div>
                    <p class="text-neutral-500 dark:text-dark-400 text-sm">No projects available</p>
                    @{
                        ViewData["Text"] = "Create Project";
                        ViewData["Variant"] = "outline";
                        ViewData["Icon"] = "fas fa-plus";
                        ViewData["Href"] = Url.Action("Create", "Projects");
                        ViewData["Classes"] = "mt-3";
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </div>
            }
        </div>
    </partial>

    <!-- Team Analytics -->
    @{
        ViewData["Title"] = "Team Analytics";
        ViewData["Icon"] = "fas fa-users";
        ViewData["IconColor"] = "bg-success-100 dark:bg-success-900 text-success-600 dark:text-success-400";
    }
    <partial name="Components/_Card" view-data="ViewData">
        <div class="text-center">
            <div class="w-16 h-16 bg-success-100 dark:bg-success-900 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-users text-2xl text-success-600 dark:text-success-400"></i>
            </div>
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-3">Team Analytics</h3>
            <p class="text-neutral-600 dark:text-dark-300 mb-6 text-sm leading-relaxed">
                Team performance metrics, resource utilization, and collaboration insights across all projects and departments.
            </p>

            @{
                ViewData["Text"] = "View Team Analytics";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-users";
                ViewData["Href"] = Url.Action("Team");
                ViewData["Classes"] = "w-full justify-center";
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </partial>

    <!-- Advanced Reports -->
    @{
        ViewData["Title"] = "Advanced Reports";
        ViewData["Icon"] = "fas fa-file-chart-line";
        ViewData["IconColor"] = "bg-warning-100 dark:bg-warning-900 text-warning-600 dark:text-warning-400";
    }
    <partial name="Components/_Card" view-data="ViewData">
        <div class="text-center">
            <div class="w-16 h-16 bg-warning-100 dark:bg-warning-900 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-file-chart-line text-2xl text-warning-600 dark:text-warning-400"></i>
            </div>
            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100 mb-3">Advanced Reports</h3>
            <p class="text-neutral-600 dark:text-dark-300 mb-6 text-sm leading-relaxed">
                Generate comprehensive reports, export data, and create custom analytics dashboards for stakeholders.
            </p>

            @{
                ViewData["Text"] = "Generate Reports";
                ViewData["Variant"] = "primary";
                ViewData["Icon"] = "fas fa-file-chart-line";
                ViewData["Href"] = Url.Action("Reports");
                ViewData["Classes"] = "w-full justify-center";
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </partial>
</div>

<!-- Analytics Charts Section -->
@if (projects.Any())
{
    <div class="grid grid-cols-1 xl:grid-cols-2 gap-6 mb-8">
        <!-- Project Status Distribution -->
        @{
            ViewData["Title"] = "Project Status Distribution";
            ViewData["Icon"] = "fas fa-chart-pie";
            ViewData["Description"] = "Overview of project statuses across your portfolio";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="relative h-80">
                <canvas id="projectStatusChart" class="w-full h-full"></canvas>
            </div>
            <div class="mt-4 grid grid-cols-2 gap-4">
                <div class="text-center">
                    <div class="text-lg font-semibold text-neutral-900 dark:text-dark-100">@projects.Count(p => p.Status == PM.Tool.Core.Enums.ProjectStatus.Active)</div>
                    <div class="text-xs text-neutral-500 dark:text-dark-400">Active Projects</div>
                </div>
                <div class="text-center">
                    <div class="text-lg font-semibold text-neutral-900 dark:text-dark-100">@projects.Count(p => p.Status == PM.Tool.Core.Enums.ProjectStatus.Completed)</div>
                    <div class="text-xs text-neutral-500 dark:text-dark-400">Completed Projects</div>
                </div>
            </div>
        </partial>

        <!-- Progress Overview -->
        @{
            ViewData["Title"] = "Progress Overview";
            ViewData["Icon"] = "fas fa-chart-bar";
            ViewData["Description"] = "Project completion progress and performance metrics";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="relative h-80">
                <canvas id="progressChart" class="w-full h-full"></canvas>
            </div>
            <div class="mt-4 flex justify-between text-center">
                <div>
                    <div class="text-lg font-semibold text-neutral-900 dark:text-dark-100">@(projects.Any() ? projects.Average(p => p.ProgressPercentage).ToString("F1") : "0")%</div>
                    <div class="text-xs text-neutral-500 dark:text-dark-400">Average Progress</div>
                </div>
                <div>
                    <div class="text-lg font-semibold text-neutral-900 dark:text-dark-100">@projects.Count(p => p.ProgressPercentage >= 75)</div>
                    <div class="text-xs text-neutral-500 dark:text-dark-400">Near Completion</div>
                </div>
                <div>
                    <div class="text-lg font-semibold text-neutral-900 dark:text-dark-100">@projects.Count(p => p.ProgressPercentage < 25)</div>
                    <div class="text-xs text-neutral-500 dark:text-dark-400">Just Started</div>
                </div>
            </div>
        </partial>
    </div>

    <!-- Additional Analytics Row -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Recent Activity -->
        @{
            ViewData["Title"] = "Recent Activity";
            ViewData["Icon"] = "fas fa-clock";
            ViewData["Description"] = "Latest project updates and milestones";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="space-y-4">
                @for (int i = 0; i < Math.Min(5, projects.Count()); i++)
                {
                    var project = projects.Skip(i).First();
                    <div class="flex items-center space-x-3 p-3 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                            <i class="fas fa-project-diagram text-primary-600 dark:text-primary-400 text-xs"></i>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-neutral-900 dark:text-dark-100 truncate">@project.Name</p>
                            <p class="text-xs text-neutral-500 dark:text-dark-400">@project.ProgressPercentage.ToString("F0")% complete</p>
                        </div>
                        <div class="text-xs text-neutral-400 dark:text-dark-500">
                            @project.UpdatedAt?.ToString("MMM dd") ?? "N/A"
                        </div>
                    </div>
                }
            </div>
        </partial>

        <!-- Performance Metrics -->
        @{
            ViewData["Title"] = "Performance Metrics";
            ViewData["Icon"] = "fas fa-tachometer-alt";
            ViewData["Description"] = "Key performance indicators and trends";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="space-y-4">
                <div class="flex justify-between items-center p-3 bg-success-50 dark:bg-success-900/20 rounded-lg">
                    <div>
                        <p class="text-sm font-medium text-success-800 dark:text-success-200">On-Time Delivery</p>
                        <p class="text-xs text-success-600 dark:text-success-400">Projects completed on schedule</p>
                    </div>
                    <div class="text-lg font-bold text-success-800 dark:text-success-200">87%</div>
                </div>

                <div class="flex justify-between items-center p-3 bg-primary-50 dark:bg-primary-900/20 rounded-lg">
                    <div>
                        <p class="text-sm font-medium text-primary-800 dark:text-primary-200">Resource Utilization</p>
                        <p class="text-xs text-primary-600 dark:text-primary-400">Team capacity usage</p>
                    </div>
                    <div class="text-lg font-bold text-primary-800 dark:text-primary-200">92%</div>
                </div>

                <div class="flex justify-between items-center p-3 bg-warning-50 dark:bg-warning-900/20 rounded-lg">
                    <div>
                        <p class="text-sm font-medium text-warning-800 dark:text-warning-200">Budget Efficiency</p>
                        <p class="text-xs text-warning-600 dark:text-warning-400">Cost vs. planned budget</p>
                    </div>
                    <div class="text-lg font-bold text-warning-800 dark:text-warning-200">95%</div>
                </div>
            </div>
        </partial>

        <!-- Quick Actions -->
        @{
            ViewData["Title"] = "Quick Actions";
            ViewData["Icon"] = "fas fa-bolt";
            ViewData["Description"] = "Frequently used analytics tools";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="space-y-3">
                @{
                    ViewData["Text"] = "Export All Data";
                    ViewData["Variant"] = "outline";
                    ViewData["Icon"] = "fas fa-download";
                    ViewData["Classes"] = "w-full justify-center";
                    ViewData["OnClick"] = "exportAllData()";
                }
                <partial name="Components/_Button" view-data="ViewData" />

                @{
                    ViewData["Text"] = "Schedule Report";
                    ViewData["Variant"] = "outline";
                    ViewData["Icon"] = "fas fa-calendar";
                    ViewData["Classes"] = "w-full justify-center";
                    ViewData["OnClick"] = "scheduleReport()";
                }
                <partial name="Components/_Button" view-data="ViewData" />

                @{
                    ViewData["Text"] = "Custom Dashboard";
                    ViewData["Variant"] = "primary";
                    ViewData["Icon"] = "fas fa-chart-line";
                    ViewData["Classes"] = "w-full justify-center";
                    ViewData["Href"] = Url.Action("CustomDashboard");
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>
        </partial>
    </div>
}
else
{
    <!-- Empty State -->
        ViewData["Title"] = "Analytics Dashboard";
        ViewData["Icon"] = "fas fa-chart-line";
    <partial name="Components/_Card" view-data="ViewData">
        <div class="text-center py-16">
            <div class="w-24 h-24 bg-gradient-to-br from-primary-100 to-primary-200 dark:from-primary-900 dark:to-primary-800 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-chart-line text-3xl text-primary-600 dark:text-primary-400"></i>
            </div>
            <h3 class="text-xl font-semibold text-neutral-900 dark:text-dark-100 mb-3">No Analytics Data Available</h3>
            <p class="text-neutral-600 dark:text-dark-300 mb-8 max-w-md mx-auto leading-relaxed">
                Create your first project to start seeing comprehensive analytics and insights about your team's performance and productivity.
            </p>

            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                @{
                    ViewData["Text"] = "Create First Project";
                    ViewData["Variant"] = "primary";
                    ViewData["Icon"] = "fas fa-plus";
                    ViewData["Href"] = Url.Action("Create", "Projects");
                }
                <partial name="Components/_Button" view-data="ViewData" />

                @{
                    ViewData["Text"] = "Learn About Analytics";
                    ViewData["Variant"] = "outline";
                    ViewData["Icon"] = "fas fa-question-circle";
                    ViewData["OnClick"] = "showAnalyticsHelp()";
                }
                <partial name="Components/_Button" view-data="ViewData" />
            </div>

            <!-- Feature Preview -->
            <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6 text-left">
                <div class="p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                    <div class="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center mb-3">
                        <i class="fas fa-chart-bar text-primary-600 dark:text-primary-400"></i>
                    </div>
                    <h4 class="font-semibold text-neutral-900 dark:text-dark-100 mb-2">Project Insights</h4>
                    <p class="text-sm text-neutral-600 dark:text-dark-300">Track progress, deadlines, and team performance across all projects.</p>
                </div>

                <div class="p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                    <div class="w-10 h-10 bg-success-100 dark:bg-success-900 rounded-lg flex items-center justify-center mb-3">
                        <i class="fas fa-users text-success-600 dark:text-success-400"></i>
                    </div>
                    <h4 class="font-semibold text-neutral-900 dark:text-dark-100 mb-2">Team Analytics</h4>
                    <p class="text-sm text-neutral-600 dark:text-dark-300">Monitor resource utilization and collaboration patterns.</p>
                </div>

                <div class="p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                    <div class="w-10 h-10 bg-warning-100 dark:bg-warning-900 rounded-lg flex items-center justify-center mb-3">
                        <i class="fas fa-file-chart-line text-warning-600 dark:text-warning-400"></i>
                    </div>
                    <h4 class="font-semibold text-neutral-900 dark:text-dark-100 mb-2">Custom Reports</h4>
                    <p class="text-sm text-neutral-600 dark:text-dark-300">Generate detailed reports and export data for stakeholders.</p>
                </div>
            </div>
        </div>
    </partial>
}

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        $(document).ready(function() {
            // Initialize dropdown toggles
            const projectFilterToggle = document.getElementById('project-filter-toggle');
            const projectFilterMenu = document.getElementById('project-filter-menu');
            const projectAnalyticsToggle = document.getElementById('project-analytics-toggle');
            const projectAnalyticsMenu = document.getElementById('project-analytics-menu');

            function toggleDropdown(toggle, menu) {
                if (toggle && menu) {
                    toggle.addEventListener('click', function(e) {
                        e.stopPropagation();
                        menu.classList.toggle('hidden');

                        // Close other dropdowns
                        document.querySelectorAll('[id$="-menu"]').forEach(function(otherMenu) {
                            if (otherMenu !== menu) {
                                otherMenu.classList.add('hidden');
                            }
                        });
                    });
                }
            }

            toggleDropdown(projectFilterToggle, projectFilterMenu);
            toggleDropdown(projectAnalyticsToggle, projectAnalyticsMenu);

            // Close dropdowns when clicking outside
            document.addEventListener('click', function() {
                document.querySelectorAll('[id$="-menu"]').forEach(function(menu) {
                    menu.classList.add('hidden');
                });
            });

            // Initialize charts if Chart.js is loaded and projects exist
            if (typeof Chart !== 'undefined' && @projects.Count() > 0) {
                try {
                    // Configure Chart.js defaults for dark mode
                    Chart.defaults.color = document.documentElement.classList.contains('dark') ? '#e5e7eb' : '#374151';
                    Chart.defaults.borderColor = document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb';

                    // Project Status Chart
                    const statusCtx = document.getElementById('projectStatusChart');
                    if (statusCtx) {
                        new Chart(statusCtx.getContext('2d'), {
                            type: 'doughnut',
                            data: {
                                labels: ['Active', 'Planning', 'Completed', 'On Hold'],
                                datasets: [{
                                    data: [
                                        @projects.Count(p => p.Status == PM.Tool.Core.Enums.ProjectStatus.Active),
                                        @projects.Count(p => p.Status == PM.Tool.Core.Enums.ProjectStatus.Planning),
                                        @projects.Count(p => p.Status == PM.Tool.Core.Enums.ProjectStatus.Completed),
                                        @projects.Count(p => p.Status == PM.Tool.Core.Enums.ProjectStatus.OnHold)
                                    ],
                                    backgroundColor: [
                                        '#10b981', // success-500
                                        '#3b82f6', // primary-500
                                        '#8b5cf6', // purple-500
                                        '#f59e0b'  // warning-500
                                    ],
                                    borderWidth: 2,
                                    borderColor: document.documentElement.classList.contains('dark') ? '#1f2937' : '#ffffff'
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        position: 'bottom',
                                        labels: {
                                            padding: 20,
                                            usePointStyle: true,
                                            font: {
                                                size: 12
                                            }
                                        }
                                    },
                                    tooltip: {
                                        backgroundColor: document.documentElement.classList.contains('dark') ? '#1f2937' : '#ffffff',
                                        titleColor: document.documentElement.classList.contains('dark') ? '#f9fafb' : '#111827',
                                        bodyColor: document.documentElement.classList.contains('dark') ? '#e5e7eb' : '#374151',
                                        borderColor: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb',
                                        borderWidth: 1
                                    }
                                }
                            }
                        });
                    }

                    // Progress Chart
                    const progressCtx = document.getElementById('progressChart');
                    if (progressCtx) {
                        new Chart(progressCtx.getContext('2d'), {
                            type: 'bar',
                            data: {
                                labels: [@Html.Raw(string.Join(",", projects.Take(8).Select(p => $"'{p.Name}'").ToArray()))],
                                datasets: [{
                                    label: 'Progress %',
                                    data: [@string.Join(",", projects.Take(8).Select(p => p.ProgressPercentage.ToString("F0")).ToArray())],
                                    backgroundColor: 'rgba(59, 130, 246, 0.8)', // primary-500 with opacity
                                    borderColor: '#3b82f6', // primary-500
                                    borderWidth: 1,
                                    borderRadius: 4,
                                    borderSkipped: false,
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        display: false
                                    },
                                    tooltip: {
                                        backgroundColor: document.documentElement.classList.contains('dark') ? '#1f2937' : '#ffffff',
                                        titleColor: document.documentElement.classList.contains('dark') ? '#f9fafb' : '#111827',
                                        bodyColor: document.documentElement.classList.contains('dark') ? '#e5e7eb' : '#374151',
                                        borderColor: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb',
                                        borderWidth: 1
                                    }
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        max: 100,
                                        ticks: {
                                            callback: function(value) {
                                                return value + '%';
                                            }
                                        },
                                        grid: {
                                            color: document.documentElement.classList.contains('dark') ? '#374151' : '#f3f4f6'
                                        }
                                    },
                                    x: {
                                        grid: {
                                            display: false
                                        },
                                        ticks: {
                                            maxRotation: 45,
                                            minRotation: 0
                                        }
                                    }
                                }
                            }
                        });
                    }
                } catch (error) {
                    console.error('Chart initialization error:', error);
                }
            }

            // Animate stats cards on load
            $('.stats-card-custom').each(function(index) {
                $(this).delay(index * 150).queue(function() {
                    $(this).addClass('animate-fade-in-up').dequeue();
                });
            });
        });

        // Helper functions for new features
        function toggleProjectFilter() {
            const menu = document.getElementById('project-filter-menu');
            if (menu) {
                menu.classList.toggle('hidden');
            }
        }

        function toggleProjectAnalytics() {
            const menu = document.getElementById('project-analytics-menu');
            if (menu) {
                menu.classList.toggle('hidden');
            }
        }

        function exportAnalyticsData() {
            // Implementation for exporting analytics data
            alert('Export functionality will be implemented');
        }

        function exportAllData() {
            // Implementation for exporting all data
            alert('Export all data functionality will be implemented');
        }

        function scheduleReport() {
            // Implementation for scheduling reports
            alert('Schedule report functionality will be implemented');
        }

        function showAnalyticsHelp() {
            // Implementation for showing analytics help
            alert('Analytics help will be implemented');
        }
    </script>
}
