using PM.Tool.Core.Entities;
using PM.Tool.Core.Enums;

namespace PM.Tool.Core.Specifications
{
    public class ProjectsByStatusSpec : BaseSpecification<Project>
    {
        public ProjectsByStatusSpec(ProjectStatus status)
        {
            Criteria = p => p.Status == status;
            AddInclude(p => p.Manager);
            AddInclude(p => p.Members.Where(m => m.IsActive));
            ApplyOrderByDescending(p => p.CreatedAt);
        }
    }
}
