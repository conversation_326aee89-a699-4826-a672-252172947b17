@model PM.Tool.Core.Entities.ResourceAllocation

@{
    ViewData["Title"] = "Create Allocation";
    var resource = ViewBag.Resource as PM.Tool.Core.Entities.Resource;
    ViewData["PageTitle"] = $"Create Allocation - {resource?.Name}";
    ViewData["PageDescription"] = "Allocate resource to a project";
    ViewData["BreadcrumbItems"] = new List<(string Text, string? Url)>
    {
        ("Resources", Url.Action("Index")),
        (resource?.Name ?? "Resource", Url.Action("Details", new { id = resource?.Id })),
        ("Allocations", Url.Action("Allocations", new { id = resource?.Id })),
        ("Create", null)
    };
}

<div class="max-w-4xl mx-auto">
    <!-- Resource Summary -->
    @if (resource != null)
    {
        <div class="card-custom mb-6">
            <div class="card-body-custom">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 @GetResourceTypeIconBg(resource.Type) rounded-lg flex items-center justify-center">
                        <i class="@GetResourceTypeIcon(resource.Type) text-white text-xl"></i>
                    </div>
                    <div>
                        <h2 class="text-xl font-semibold text-neutral-900 dark:text-dark-100">@resource.Name</h2>
                        <p class="text-neutral-600 dark:text-dark-300">
                            @resource.Type • @resource.Capacity hrs/day • @resource.HourlyRate.ToString("C")/hr
                        </p>
                    </div>
                </div>
            </div>
        </div>
    }

    <form asp-action="CreateAllocation" method="post" class="space-y-6">
        <input asp-for="ResourceId" type="hidden" />
        <input asp-for="CreatedByUserId" type="hidden" />

        <div asp-validation-summary="ModelOnly" class="validation-summary"></div>

        <!-- Project & Basic Info -->
        <div class="card-custom">
            <div class="card-header-custom">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                    <i class="fas fa-project-diagram mr-2 text-primary-500"></i>
                    Project & Basic Information
                </h3>
            </div>
            <div class="card-body-custom">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="form-group">
                        <label asp-for="ProjectId" class="form-label">Project</label>
                        <select asp-for="ProjectId" class="form-select" required>
                            <option value="">Select a project</option>
                            <!-- Projects will be populated by the controller -->
                        </select>
                        <span asp-validation-for="ProjectId" class="form-error"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Status" class="form-label">Status</label>
                        <select asp-for="Status" asp-items="@ViewBag.AllocationStatuses" class="form-select">
                            <option value="">Select status</option>
                        </select>
                        <span asp-validation-for="Status" class="form-error"></span>
                    </div>

                    <div class="form-group md:col-span-2">
                        <label asp-for="Notes" class="form-label">Notes</label>
                        <textarea asp-for="Notes" class="form-textarea" rows="3" placeholder="Enter allocation notes"></textarea>
                        <span asp-validation-for="Notes" class="form-error"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Schedule -->
        <div class="card-custom">
            <div class="card-header-custom">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                    <i class="fas fa-calendar-alt mr-2 text-primary-500"></i>
                    Schedule
                </h3>
            </div>
            <div class="card-body-custom">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="form-group">
                        <label asp-for="StartDate" class="form-label">Start Date</label>
                        <input asp-for="StartDate" class="form-input" type="date" required />
                        <span asp-validation-for="StartDate" class="form-error"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="EndDate" class="form-label">End Date</label>
                        <input asp-for="EndDate" class="form-input" type="date" required />
                        <span asp-validation-for="EndDate" class="form-error"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Allocation Details -->
        <div class="card-custom">
            <div class="card-header-custom">
                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">
                    <i class="fas fa-percentage mr-2 text-primary-500"></i>
                    Allocation Details
                </h3>
            </div>
            <div class="card-body-custom">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="form-group">
                        <label asp-for="AllocationPercentage" class="form-label">Allocation Percentage</label>
                        <div class="relative">
                            <input asp-for="AllocationPercentage" class="form-input pr-8" type="number" min="1" max="100" step="1" required />
                            <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500">%</span>
                        </div>
                        <span asp-validation-for="AllocationPercentage" class="form-error"></span>
                        <p class="form-help">Percentage of daily capacity allocated to this project</p>
                    </div>

                    <div class="form-group">
                        <label asp-for="AllocatedHours" class="form-label">Allocated Hours</label>
                        <div class="relative">
                            <input asp-for="AllocatedHours" class="form-input pr-12" type="number" min="0.5" step="0.5" required />
                            <span class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500">hrs/day</span>
                        </div>
                        <span asp-validation-for="AllocatedHours" class="form-error"></span>
                        <p class="form-help">Hours per day allocated to this project</p>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Hourly Rate (from Resource)</label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-500">$</span>
                            <input type="number" class="form-input pl-8" value="@(ViewBag.Resource as PM.Tool.Core.Entities.Resource)?.HourlyRate" readonly />
                        </div>
                        <p class="form-help">Rate is inherited from the resource</p>
                    </div>
                </div>

                <!-- Calculated Values -->
                <div class="mt-6 p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                    <h4 class="text-sm font-medium text-neutral-700 dark:text-dark-300 mb-3">Calculated Values</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                            <span class="text-neutral-600 dark:text-dark-400">Total Days:</span>
                            <span id="totalDays" class="font-medium text-neutral-900 dark:text-dark-100 ml-2">-</span>
                        </div>
                        <div>
                            <span class="text-neutral-600 dark:text-dark-400">Total Hours:</span>
                            <span id="totalHours" class="font-medium text-neutral-900 dark:text-dark-100 ml-2">-</span>
                        </div>
                        <div>
                            <span class="text-neutral-600 dark:text-dark-400">Total Cost:</span>
                            <span id="totalCost" class="font-medium text-neutral-900 dark:text-dark-100 ml-2">-</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-4 pt-6 border-t border-neutral-200 dark:border-dark-600">
            <a asp-action="Allocations" asp-route-id="@resource?.Id" class="btn-secondary">
                <i class="fas fa-times mr-2"></i>
                Cancel
            </a>
            <button type="submit" class="btn-primary">
                <i class="fas fa-save mr-2"></i>
                Create Allocation
            </button>
        </div>
    </form>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />

    <script>
        const resourceCapacity = @(resource?.Capacity ?? 8);
        const resourceRate = @(resource?.HourlyRate ?? 0);

        // Auto-calculate allocation percentage when hours change
        document.getElementById('AllocatedHours').addEventListener('input', function() {
            const hours = parseFloat(this.value) || 0;
            const percentage = Math.round((hours / resourceCapacity) * 100);
            document.getElementById('AllocationPercentage').value = Math.min(percentage, 100);
            updateCalculations();
        });

        // Auto-calculate hours when percentage changes
        document.getElementById('AllocationPercentage').addEventListener('input', function() {
            const percentage = parseFloat(this.value) || 0;
            const hours = (percentage / 100) * resourceCapacity;
            document.getElementById('AllocatedHours').value = hours.toFixed(1);
            updateCalculations();
        });

        // Update calculations when dates change
        document.getElementById('StartDate').addEventListener('change', updateCalculations);
        document.getElementById('EndDate').addEventListener('change', updateCalculations);

        function updateCalculations() {
            const startDate = new Date(document.getElementById('StartDate').value);
            const endDate = new Date(document.getElementById('EndDate').value);
            const hoursPerDay = parseFloat(document.getElementById('AllocatedHours').value) || 0;
            const hourlyRate = resourceRate;

            if (startDate && endDate && startDate <= endDate) {
                // Calculate business days (excluding weekends)
                let totalDays = 0;
                let currentDate = new Date(startDate);

                while (currentDate <= endDate) {
                    const dayOfWeek = currentDate.getDay();
                    if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Not Sunday (0) or Saturday (6)
                        totalDays++;
                    }
                    currentDate.setDate(currentDate.getDate() + 1);
                }

                const totalHours = totalDays * hoursPerDay;
                const totalCost = totalHours * hourlyRate;

                document.getElementById('totalDays').textContent = totalDays;
                document.getElementById('totalHours').textContent = totalHours.toFixed(1);
                document.getElementById('totalCost').textContent = '$' + totalCost.toFixed(2);
            } else {
                document.getElementById('totalDays').textContent = '-';
                document.getElementById('totalHours').textContent = '-';
                document.getElementById('totalCost').textContent = '-';
            }
        }

        // Validate end date is after start date
        document.getElementById('EndDate').addEventListener('change', function() {
            const startDate = new Date(document.getElementById('StartDate').value);
            const endDate = new Date(this.value);

            if (startDate && endDate && endDate < startDate) {
                alert('End date must be after start date');
                this.value = '';
            }
        });

        // Initialize calculations
        updateCalculations();
    </script>
}

@functions {
    private string GetResourceTypeIcon(PM.Tool.Core.Entities.ResourceType type)
    {
        return type switch
        {
            PM.Tool.Core.Entities.ResourceType.Human => "fas fa-user",
            PM.Tool.Core.Entities.ResourceType.Equipment => "fas fa-tools",
            PM.Tool.Core.Entities.ResourceType.Material => "fas fa-boxes",
            PM.Tool.Core.Entities.ResourceType.Facility => "fas fa-building",
            _ => "fas fa-cube"
        };
    }

    private string GetResourceTypeIconBg(PM.Tool.Core.Entities.ResourceType type)
    {
        return type switch
        {
            PM.Tool.Core.Entities.ResourceType.Human => "bg-gradient-to-br from-blue-500 to-blue-600",
            PM.Tool.Core.Entities.ResourceType.Equipment => "bg-gradient-to-br from-orange-500 to-orange-600",
            PM.Tool.Core.Entities.ResourceType.Material => "bg-gradient-to-br from-green-500 to-green-600",
            PM.Tool.Core.Entities.ResourceType.Facility => "bg-gradient-to-br from-purple-500 to-purple-600",
            _ => "bg-gradient-to-br from-neutral-500 to-neutral-600"
        };
    }
}
