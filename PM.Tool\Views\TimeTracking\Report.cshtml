@model TimeTrackingReportViewModel
@{
    ViewData["Title"] = "Time Tracking Report";
}

<div class="container-fluid">
    <h2>Time Tracking Report</h2>

    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Filter</h5>
                </div>
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="startDate" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="startDate" name="startDate" 
                                   value="@Model.StartDate.ToString("yyyy-MM-dd")" />
                        </div>
                        <div class="col-md-4">
                            <label for="endDate" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="endDate" name="endDate" 
                                   value="@Model.EndDate.ToString("yyyy-MM-dd")" />
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary">Apply Filter</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Time by Task</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Task</th>
                                    <th>Hours</th>
                                    <th>Percentage</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var entry in Model.TimeByTask)
                                {
                                    <tr>
                                        <td>@entry.TaskName</td>
                                        <td>@entry.Hours.ToString("F1")</td>
                                        <td>
                                            <div class="progress">
                                                <div class="progress-bar" role="progressbar" 
                                                     style="width: @(entry.Hours / Model.TotalHours * 100)%">
                                                    @((entry.Hours / Model.TotalHours * 100).ToString("F1"))%
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                            <tfoot>
                                <tr class="fw-bold">
                                    <td>Total</td>
                                    <td>@Model.TotalHours.ToString("F1")</td>
                                    <td>100%</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Summary</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Period
                            <span>@Model.StartDate.ToShortDateString() - @Model.EndDate.ToShortDateString()</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Total Days
                            <span>@((Model.EndDate - Model.StartDate).Days + 1)</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Total Hours
                            <span>@Model.TotalHours.ToString("F1")</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Daily Average
                            <span>@((Model.TotalHours / ((Model.EndDate - Model.StartDate).Days + 1)).ToString("F1"))</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
