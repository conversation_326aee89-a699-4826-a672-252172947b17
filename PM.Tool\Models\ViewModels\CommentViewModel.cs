using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Models.ViewModels
{
    public class CommentViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Comment content is required")]
        [MinLength(2, ErrorMessage = "Comment must be at least 2 characters long")]
        [MaxLength(1000, ErrorMessage = "Comment cannot exceed 1000 characters")]
        public string Content { get; set; } = string.Empty;

        public int TaskId { get; set; }
        public string AuthorName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public bool IsEdited => UpdatedAt.HasValue;
        public string FormattedContent { get; set; } = string.Empty;
    }
}