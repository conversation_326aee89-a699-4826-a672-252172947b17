using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PM.Tool.Core.Constants;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Data;
using PM.Tool.Models.ViewModels;
using System.Security.Claims;
using AutoMapper;
using TaskStatus = PM.Tool.Core.Enums.TaskStatus;

namespace PM.Tool.Controllers
{
    [Authorize]
    [AutoValidateAntiforgeryToken]
    public class DashboardController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IProjectService _projectService;
        private readonly ITaskService _taskService;
        private readonly INotificationService _notificationService;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IMapper _mapper;
        private readonly IReportingService _reportingService;

        public DashboardController(
            ApplicationDbContext context,
            IProjectService projectService,
            ITaskService taskService,
            INotificationService notificationService,
            UserManager<ApplicationUser> userManager,
            IMapper mapper,
            IReportingService reportingService)
        {
            _context = context;
            _projectService = projectService;
            _taskService = taskService;
            _notificationService = notificationService;
            _userManager = userManager;
            _mapper = mapper;
            _reportingService = reportingService;
        }

        public async Task<IActionResult> Index(DateTime? startDate = null, DateTime? endDate = null)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var isAdmin = User.IsInRole(Roles.Admin);

            // Default to last 30 days if no dates provided
            startDate ??= DateTime.UtcNow.AddDays(-30);
            endDate ??= DateTime.UtcNow;

            var viewModel = new DashboardViewModel
            {
                Stats = await GetDashboardStats(userId, isAdmin, startDate.Value, endDate.Value),
                Charts = await GetChartData(userId, isAdmin, startDate.Value, endDate.Value),
                RecentProjects = await GetRecentProjects(userId, isAdmin),
                MyTasks = await GetUserTasks(userId),
                OverdueTasks = await GetOverdueTasks(userId, isAdmin),
                UpcomingTasks = await GetUpcomingTasks(userId, isAdmin),
                RecentNotifications = _mapper.Map<List<NotificationViewModel>>(
                    (await _notificationService.GetUserNotificationsAsync(userId)).Take(5)),
                UpcomingMilestones = await GetUpcomingMilestones(userId, isAdmin),
                StartDate = startDate.Value,
                EndDate = endDate.Value
            };

            return View(viewModel);
        }

        [HttpGet]
        public async Task<IActionResult> ExportTasks(DateTime? startDate = null, DateTime? endDate = null)
        {
            var file = await _reportingService.GenerateTasksReportAsync(startDate, endDate);
            return File(file, "text/csv", $"tasks_report_{DateTime.UtcNow:yyyyMMdd}.csv");
        }

        [HttpGet]
        public async Task<IActionResult> ExportProjects(DateTime? startDate = null, DateTime? endDate = null)
        {
            var file = await _reportingService.GenerateProjectReportAsync(startDate, endDate);
            return File(file, "text/csv", $"projects_report_{DateTime.UtcNow:yyyyMMdd}.csv");
        }

        [HttpGet]
        public async Task<IActionResult> GetVelocityMetrics(DateTime? startDate = null, DateTime? endDate = null)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            startDate ??= DateTime.UtcNow.AddDays(-30);
            endDate ??= DateTime.UtcNow;

            var metrics = await _reportingService.GetTeamVelocityMetricsAsync(userId, startDate.Value, endDate.Value);
            return Json(metrics);
        }

        [HttpGet]
        public async Task<IActionResult> GetProductivityMetrics(DateTime? startDate = null, DateTime? endDate = null)
        {
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            startDate ??= DateTime.UtcNow.AddDays(-30);
            endDate ??= DateTime.UtcNow;

            var metrics = await _reportingService.GetTeamProductivityMetricsAsync(userId, startDate.Value, endDate.Value);
            return Json(metrics);
        }

        [HttpGet]
        public IActionResult TestSaaS()
        {
            return View();
        }

        private async Task<DashboardStatsViewModel> GetDashboardStats(string userId, bool isAdmin)
        {
            var query = isAdmin ? _context.Projects : _context.Projects.Where(p => p.Members.Any(m => m.UserId == userId));

            var projects = await query.ToListAsync();
            var tasks = await _context.Tasks
                .Where(t => isAdmin || t.Project.Members.Any(m => m.UserId == userId))
                .ToListAsync();

            return new DashboardStatsViewModel
            {
                TotalProjects = projects.Count,
                ActiveProjects = projects.Count(p => p.Status == Core.Enums.ProjectStatus.InProgress),
                CompletedProjects = projects.Count(p => p.Status == Core.Enums.ProjectStatus.Completed),
                TotalTasks = tasks.Count,
                MyTasks = tasks.Count(t => t.AssignedToUserId == userId),
                CompletedTasks = tasks.Count(t => t.Status == Core.Enums.TaskStatus.Done),
                OverdueTasks = tasks.Count(t => t.DueDate.HasValue && t.DueDate.Value < DateTime.UtcNow && t.Status != Core.Enums.TaskStatus.Done),
                OverallProgress = projects.Any() ? projects.Average(p => p.ProgressPercentage) : 0
            };
        }

        private async Task<DashboardStatsViewModel> GetDashboardStats(string userId, bool isAdmin, DateTime startDate, DateTime endDate)
        {
            var query = isAdmin ? _context.Projects : _context.Projects.Where(p => p.Members.Any(m => m.UserId == userId));

            var projects = await query
                .Where(p => p.UpdatedAt >= startDate && p.UpdatedAt <= endDate)
                .ToListAsync();
            var tasks = await _context.Tasks
                .Where(t => (isAdmin || t.Project.Members.Any(m => m.UserId == userId)) && t.UpdatedAt >= startDate && t.UpdatedAt <= endDate)
                .ToListAsync();

            return new DashboardStatsViewModel
            {
                TotalProjects = projects.Count,
                ActiveProjects = projects.Count(p => p.Status == Core.Enums.ProjectStatus.InProgress),
                CompletedProjects = projects.Count(p => p.Status == Core.Enums.ProjectStatus.Completed),
                TotalTasks = tasks.Count,
                MyTasks = tasks.Count(t => t.AssignedToUserId == userId),
                CompletedTasks = tasks.Count(t => t.Status == Core.Enums.TaskStatus.Done),
                OverdueTasks = tasks.Count(t => t.DueDate.HasValue && t.DueDate.Value < DateTime.UtcNow && t.Status != Core.Enums.TaskStatus.Done),
                OverallProgress = projects.Any() ? projects.Average(p => p.ProgressPercentage) : 0
            };
        }

        private async Task<ChartDataViewModel> GetChartData(string userId, bool isAdmin)
        {
            var projectQuery = isAdmin ? _context.Projects : _context.Projects.Where(p => p.Members.Any(m => m.UserId == userId));
            var taskQuery = isAdmin ? _context.Tasks : _context.Tasks.Where(t => t.Project.Members.Any(m => m.UserId == userId));

            var projects = await projectQuery.ToListAsync();
            var tasks = await taskQuery.ToListAsync();
            var teamMembers = await _userManager.Users.ToListAsync();

            return new ChartDataViewModel
            {
                StatusByProject = projects
                    .GroupBy(p => p.Status)
                    .Select(g => new ChartDataPoint
                    {
                        Label = g.Key.ToString(),
                        Value = g.Count(),
                        Color = GetStatusColor(g.Key.ToString())
                    })
                    .ToList(),

                StatusByTask = tasks
                    .GroupBy(t => t.Status)
                    .Select(g => new ChartDataPoint
                    {
                        Label = g.Key.ToString(),
                        Value = g.Count(),
                        Color = GetStatusColor(g.Key.ToString())
                    })
                    .ToList(),

                ProgressByTimelineProject = projects
                    .OrderBy(p => p.StartDate)
                    .Select(p => new ChartDataPoint
                    {
                        Label = p.Name,
                        Value = p.ProgressPercentage,
                        Date = p.StartDate
                    })
                    .ToList(),

                PriorityByTask = tasks
                    .GroupBy(t => t.Priority)
                    .Select(g => new ChartDataPoint
                    {
                        Label = g.Key.ToString(),
                        Value = g.Count(),
                        Color = GetPriorityColor(g.Key.ToString())
                    })
                    .ToList(),

                WorkloadByTeamMember = teamMembers
                    .Select(u => new ChartDataPoint
                    {
                        Label = $"{u.FirstName} {u.LastName}",
                        Value = tasks.Count(t => t.AssignedToUserId == u.Id && t.Status != Core.Enums.TaskStatus.Done)
                    })
                    .Where(p => p.Value > 0)
                    .ToList()
            };
        }

        private async Task<ChartDataViewModel> GetChartData(string userId, bool isAdmin, DateTime startDate, DateTime endDate)
        {
            var projectQuery = isAdmin ? _context.Projects : _context.Projects.Where(p => p.Members.Any(m => m.UserId == userId));
            var taskQuery = isAdmin ? _context.Tasks : _context.Tasks.Where(t => t.Project.Members.Any(m => m.UserId == userId));

            var projects = await projectQuery
                .Where(p => p.UpdatedAt >= startDate && p.UpdatedAt <= endDate)
                .ToListAsync();
            var tasks = await taskQuery
                .Where(t => t.UpdatedAt >= startDate && t.UpdatedAt <= endDate)
                .ToListAsync();
            var teamMembers = await _userManager.Users.ToListAsync();

            return new ChartDataViewModel
            {
                StatusByProject = projects
                    .GroupBy(p => p.Status)
                    .Select(g => new ChartDataPoint
                    {
                        Label = g.Key.ToString(),
                        Value = g.Count(),
                        Color = GetStatusColor(g.Key.ToString())
                    })
                    .ToList(),

                StatusByTask = tasks
                    .GroupBy(t => t.Status)
                    .Select(g => new ChartDataPoint
                    {
                        Label = g.Key.ToString(),
                        Value = g.Count(),
                        Color = GetStatusColor(g.Key.ToString())
                    })
                    .ToList(),

                ProgressByTimelineProject = projects
                    .OrderBy(p => p.StartDate)
                    .Select(p => new ChartDataPoint
                    {
                        Label = p.Name,
                        Value = p.ProgressPercentage,
                        Date = p.StartDate
                    })
                    .ToList(),

                PriorityByTask = tasks
                    .GroupBy(t => t.Priority)
                    .Select(g => new ChartDataPoint
                    {
                        Label = g.Key.ToString(),
                        Value = g.Count(),
                        Color = GetPriorityColor(g.Key.ToString())
                    })
                    .ToList(),

                WorkloadByTeamMember = teamMembers
                    .Select(u => new ChartDataPoint
                    {
                        Label = $"{u.FirstName} {u.LastName}",
                        Value = tasks.Count(t => t.AssignedToUserId == u.Id && t.Status != Core.Enums.TaskStatus.Done)
                    })
                    .Where(p => p.Value > 0)
                    .ToList()
            };
        }

        private string GetStatusColor(string status) => status switch
        {
            "Planning" => "#6c757d",
            "InProgress" => "#007bff",
            "OnHold" => "#ffc107",
            "Completed" => "#28a745",
            "Cancelled" => "#dc3545",
            _ => "#6c757d"
        };

        private string GetPriorityColor(string priority) => priority switch
        {
            "Low" => "#28a745",
            "Medium" => "#ffc107",
            "High" => "#fd7e14",
            "Critical" => "#dc3545",
            _ => "#6c757d"
        };

        // Implementation of other helper methods...
        private async Task<List<ProjectSummaryViewModel>> GetRecentProjects(string userId, bool isAdmin)
        {
            var query = isAdmin
                ? _context.Projects
                : _context.Projects.Where(p => p.Members.Any(m => m.UserId == userId));

            var projects = await query
                .OrderByDescending(p => p.UpdatedAt)
                .Take(5)
                .ToListAsync();

            return _mapper.Map<List<ProjectSummaryViewModel>>(projects);
        }

        private async Task<List<TaskSummaryViewModel>> GetUserTasks(string userId)
        {
            return await _context.Tasks
                .Where(t => t.AssignedToUserId == userId && t.Status != Core.Enums.TaskStatus.Done)
                .OrderBy(t => t.DueDate)
                .Take(5)
                .Select(t => new TaskSummaryViewModel
                {
                    Id = t.Id,
                    Title = t.Title,
                    Status = t.Status,
                    Priority = t.Priority,
                    DueDate = t.DueDate,
                    ProjectId = t.ProjectId,
                    ProjectName = t.Project.Name
                })
                .ToListAsync();
        }

        private async Task<List<TaskSummaryViewModel>> GetOverdueTasks(string userId, bool isAdmin)
        {
            var query = isAdmin
                ? _context.Tasks
                : _context.Tasks.Where(t => t.Project.Members.Any(m => m.UserId == userId));

            var tasks = await query
                .Where(t => t.DueDate < DateTime.UtcNow && t.Status != Core.Enums.TaskStatus.Done)
                .OrderBy(t => t.DueDate)
                .Take(5)
                .Include(t => t.Project)
                .ToListAsync();

            return _mapper.Map<List<TaskSummaryViewModel>>(tasks);
        }

        private async Task<List<TaskSummaryViewModel>> GetUpcomingTasks(string userId, bool isAdmin)
        {
            var query = isAdmin
                ? _context.Tasks
                : _context.Tasks.Where(t => t.Project.Members.Any(m => m.UserId == userId));

            var tasks = await query
                .Where(t => t.DueDate >= DateTime.UtcNow && t.Status != Core.Enums.TaskStatus.Done)
                .OrderBy(t => t.DueDate)
                .Take(5)
                .Include(t => t.Project)
                .ToListAsync();

            return _mapper.Map<List<TaskSummaryViewModel>>(tasks);
        }

        private async Task<List<MilestoneViewModel>> GetUpcomingMilestones(string userId, bool isAdmin)
        {
            var query = isAdmin
                ? _context.Milestones
                : _context.Milestones.Where(m => m.Project.Members.Any(pm => pm.UserId == userId));

            var milestones = await query
                .Where(m => m.DueDate >= DateTime.UtcNow)
                .OrderBy(m => m.DueDate)
                .Take(5)
                .Include(m => m.Project)
                .ToListAsync();

            return _mapper.Map<List<MilestoneViewModel>>(milestones);
        }
    }
}
