@model PM.Tool.Models.ViewModels.FeatureEditViewModel
@using PM.Tool.Core.Entities.Agile

@{
    ViewData["Title"] = "Edit Feature";
    ViewData["PageTitle"] = $"Edit Feature - {Model.Title}";
    ViewData["PageDescription"] = "Update feature information and settings.";
    var project = ViewBag.Project as PM.Tool.Core.Entities.Project;
}

@section Styles {
    <style>
        .form-section {
            transition: all 0.2s ease-in-out;
        }
        .form-section:hover {
            transform: translateY(-1px);
        }
        .progress-indicator {
            transition: width 0.3s ease-in-out;
        }
    </style>
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <div class="flex items-center space-x-3 mb-2">
                <span class="text-sm font-mono text-neutral-500 dark:text-dark-400 bg-neutral-100 dark:bg-dark-300 px-3 py-1 rounded">
                    @Model.FeatureKey
                </span>
                <span class="px-3 py-1 text-sm font-medium rounded-full @GetStatusBadgeClass(Model.Status)">
                    @Model.Status
                </span>
            </div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-edit mr-3 text-primary-600 dark:text-primary-400"></i>
                Edit Feature
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                @(project?.Name ?? "Project") - Update feature information
            </p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-3">
            @{
                ViewData["Text"] = "View Details";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-eye";
                ViewData["Href"] = Url.Action("Details", new { id = Model.Id });
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "Back to List";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Index", new { projectId = Model.ProjectId });
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Progress Indicator -->
@if (Model.ProgressPercentage > 0)
{
    <div class="mb-6">
        @{
            ViewData["Title"] = "Current Progress";
            ViewData["Icon"] = "fas fa-chart-line";
        }
        <partial name="Components/_Card" view-data="ViewData">
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-neutral-900 dark:text-dark-100">Feature Progress</span>
                <span class="text-sm font-bold text-primary-600 dark:text-primary-400">@Model.ProgressPercentage.ToString("F1")%</span>
            </div>
            <div class="w-full bg-neutral-200 dark:bg-dark-300 rounded-full h-3">
                <div class="progress-indicator bg-primary-600 dark:bg-primary-500 h-3 rounded-full" style="width: @Model.ProgressPercentage%"></div>
            </div>
            <div class="flex justify-between text-xs text-neutral-500 dark:text-dark-400 mt-2">
                <span>@Model.CompletedUserStoryCount completed user stories</span>
                <span>@Model.UserStoryCount total user stories</span>
            </div>
        </partial>
    </div>
}

<!-- Edit Form -->
@{
    ViewData["Title"] = "Feature Information";
    ViewData["Icon"] = "fas fa-puzzle-piece";
}
<partial name="Components/_Card" view-data="ViewData">
    <form asp-action="Edit" method="post" class="space-y-8">
        <input type="hidden" asp-for="Id" />
        <input type="hidden" asp-for="ProjectId" />
        <input type="hidden" asp-for="FeatureKey" />
        <input type="hidden" asp-for="CreatedAt" />
        <input type="hidden" asp-for="ProgressPercentage" />
        <input type="hidden" asp-for="UserStoryCount" />
        <input type="hidden" asp-for="CompletedUserStoryCount" />

        <!-- Basic Information -->
        <div class="form-section">
            <h3 class="text-lg font-medium text-neutral-900 dark:text-dark-100 mb-4">Basic Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Title -->
                @{
                    ViewData["Label"] = "Feature Title";
                    ViewData["Name"] = "Title";
                    ViewData["Type"] = "text";
                    ViewData["Required"] = true;
                    ViewData["Icon"] = "fas fa-heading";
                    ViewData["Placeholder"] = "Enter feature title...";
                    ViewData["Value"] = Model.Title;
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Title");
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Epic -->
                @{
                    var epicOptions = "<option value=\"\">-- Select Epic (Optional) --</option>";
                    if (ViewBag.EpicId != null)
                    {
                        foreach (var epic in (IEnumerable<SelectListItem>)ViewBag.EpicId)
                        {
                            var selected = epic.Value == Model.EpicId.ToString() ? "selected" : "";
                            epicOptions += $"<option value=\"{epic.Value}\" {selected}>{epic.Text}</option>";
                        }
                    }

                    ViewData["Label"] = "Epic";
                    ViewData["Name"] = "EpicId";
                    ViewData["Type"] = "select";
                    ViewData["Required"] = false;
                    ViewData["Icon"] = "fas fa-layer-group";
                    ViewData["Options"] = epicOptions;
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("EpicId");
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Priority -->
                @{
                    var priorityOptions = "";
                    foreach (FeaturePriority priority in Enum.GetValues<FeaturePriority>())
                    {
                        var selected = priority == Model.Priority ? "selected" : "";
                        priorityOptions += $"<option value=\"{(int)priority}\" {selected}>{priority}</option>";
                    }

                    ViewData["Label"] = "Priority";
                    ViewData["Name"] = "Priority";
                    ViewData["Type"] = "select";
                    ViewData["Required"] = true;
                    ViewData["Icon"] = "fas fa-exclamation";
                    ViewData["Options"] = priorityOptions;
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Priority");
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Status -->
                @{
                    var statusOptions = "";
                    foreach (FeatureStatus status in Enum.GetValues<FeatureStatus>())
                    {
                        var selected = status == Model.Status ? "selected" : "";
                        statusOptions += $"<option value=\"{(int)status}\" {selected}>{status}</option>";
                    }

                    ViewData["Label"] = "Status";
                    ViewData["Name"] = "Status";
                    ViewData["Type"] = "select";
                    ViewData["Required"] = true;
                    ViewData["Icon"] = "fas fa-flag";
                    ViewData["Options"] = statusOptions;
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Status");
                }
                <partial name="Components/_FormInput" view-data="ViewData" />
            </div>
        </div>

        <!-- Description & Details -->
        <div class="form-section">
            <h3 class="text-lg font-medium text-neutral-900 dark:text-dark-100 mb-4">Description & Details</h3>
            <div class="space-y-6">
                <!-- Description -->
                @{
                    ViewData["Label"] = "Description";
                    ViewData["Name"] = "Description";
                    ViewData["Type"] = "textarea";
                    ViewData["Required"] = true;
                    ViewData["Icon"] = "fas fa-align-left";
                    ViewData["Placeholder"] = "Describe the feature and its purpose...";
                    ViewData["Rows"] = "4";
                    ViewData["Value"] = Model.Description;
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Description");
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Business Value -->
                @{
                    ViewData["Label"] = "Business Value";
                    ViewData["Name"] = "BusinessValue";
                    ViewData["Type"] = "textarea";
                    ViewData["Required"] = false;
                    ViewData["Icon"] = "fas fa-chart-line";
                    ViewData["Placeholder"] = "Explain the business value and impact...";
                    ViewData["Rows"] = "3";
                    ViewData["Value"] = Model.BusinessValue;
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("BusinessValue");
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Acceptance Criteria -->
                @{
                    ViewData["Label"] = "Acceptance Criteria";
                    ViewData["Name"] = "AcceptanceCriteria";
                    ViewData["Type"] = "textarea";
                    ViewData["Required"] = false;
                    ViewData["Icon"] = "fas fa-check-square";
                    ViewData["Placeholder"] = "Define the acceptance criteria for this feature...";
                    ViewData["Rows"] = "4";
                    ViewData["Value"] = Model.AcceptanceCriteria;
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("AcceptanceCriteria");
                }
                <partial name="Components/_FormInput" view-data="ViewData" />
            </div>
        </div>

        <!-- Planning & Estimation -->
        <div class="form-section">
            <h3 class="text-lg font-medium text-neutral-900 dark:text-dark-100 mb-4">Planning & Estimation</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Estimated Story Points -->
                @{
                    ViewData["Label"] = "Estimated Story Points";
                    ViewData["Name"] = "EstimatedStoryPoints";
                    ViewData["Type"] = "number";
                    ViewData["Required"] = false;
                    ViewData["Icon"] = "fas fa-calculator";
                    ViewData["Placeholder"] = "0";
                    ViewData["Min"] = "0";
                    ViewData["Step"] = "1";
                    ViewData["Value"] = Model.EstimatedStoryPoints.ToString();
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("EstimatedStoryPoints");
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Actual Story Points (Read-only) -->
                @{
                    ViewData["Label"] = "Actual Story Points";
                    ViewData["Name"] = "ActualStoryPoints";
                    ViewData["Type"] = "number";
                    ViewData["Required"] = false;
                    ViewData["Icon"] = "fas fa-chart-bar";
                    ViewData["Placeholder"] = "0";
                    ViewData["Min"] = "0";
                    ViewData["Step"] = "1";
                    ViewData["Value"] = Model.ActualStoryPoints.ToString();
                    ViewData["Readonly"] = true;
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("ActualStoryPoints");
                    ViewData["HelpText"] = "Calculated from completed user stories";
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Target Date -->
                @{
                    ViewData["Label"] = "Target Date";
                    ViewData["Name"] = "TargetDate";
                    ViewData["Type"] = "date";
                    ViewData["Required"] = false;
                    ViewData["Icon"] = "fas fa-calendar";
                    ViewData["Value"] = Model.TargetDate?.ToString("yyyy-MM-dd");
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("TargetDate");
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Sort Order -->
                @{
                    ViewData["Label"] = "Sort Order";
                    ViewData["Name"] = "SortOrder";
                    ViewData["Type"] = "number";
                    ViewData["Required"] = false;
                    ViewData["Icon"] = "fas fa-sort-numeric-up";
                    ViewData["Placeholder"] = "0";
                    ViewData["Min"] = "0";
                    ViewData["Step"] = "1";
                    ViewData["Value"] = Model.SortOrder.ToString();
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("SortOrder");
                }
                <partial name="Components/_FormInput" view-data="ViewData" />

                <!-- Tags -->
                @{
                    ViewData["Label"] = "Tags";
                    ViewData["Name"] = "Tags";
                    ViewData["Type"] = "text";
                    ViewData["Required"] = false;
                    ViewData["Icon"] = "fas fa-tags";
                    ViewData["Placeholder"] = "Enter tags separated by commas...";
                    ViewData["Value"] = Model.Tags;
                    ViewData["ErrorMessage"] = ViewData.GetValidationErrorMessage("Tags");
                }
                <partial name="Components/_FormInput" view-data="ViewData" />
            </div>
        </div>

        <!-- Audit Information -->
        <div class="form-section">
            <h3 class="text-lg font-medium text-neutral-900 dark:text-dark-100 mb-4">Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-neutral-50 dark:bg-dark-300 rounded-lg p-4">
                    <div class="text-sm text-neutral-600 dark:text-dark-300 mb-1">Created</div>
                    <div class="font-medium text-neutral-900 dark:text-dark-100">@Model.CreatedAt.ToString("MMM dd, yyyy")</div>
                </div>
                @if (Model.UpdatedAt.HasValue)
                {
                    <div class="bg-neutral-50 dark:bg-dark-300 rounded-lg p-4">
                        <div class="text-sm text-neutral-600 dark:text-dark-300 mb-1">Last Updated</div>
                        <div class="font-medium text-neutral-900 dark:text-dark-100">@Model.UpdatedAt.Value.ToString("MMM dd, yyyy")</div>
                    </div>
                }
                <div class="bg-neutral-50 dark:bg-dark-300 rounded-lg p-4">
                    <div class="text-sm text-neutral-600 dark:text-dark-300 mb-1">Feature Key</div>
                    <div class="font-medium text-neutral-900 dark:text-dark-100 font-mono">@Model.FeatureKey</div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        @{
            ViewData["SubmitText"] = "Update Feature";
            ViewData["SubmitIcon"] = "fas fa-save";
            ViewData["CancelUrl"] = Url.Action("Details", new { id = Model.Id });
            ViewData["ShowReset"] = false;
        }
        <partial name="Components/_FormActions" view-data="ViewData" />
    </form>
</partial>

@functions {
    private string GetStatusBadgeClass(FeatureStatus status)
    {
        return status switch
        {
            FeatureStatus.Draft => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100",
            FeatureStatus.InProgress => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
            FeatureStatus.Testing => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
            FeatureStatus.Completed => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
            FeatureStatus.OnHold => "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
            FeatureStatus.Cancelled => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
            _ => "bg-neutral-100 text-neutral-800 dark:bg-dark-300 dark:text-dark-100"
        };
    }
}

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        $(document).ready(function() {
            setupFormValidation();
            setupFormEnhancements();
            setupStatusChangeWarning();
        });

        function setupFormValidation() {
            $('form').on('submit', function(e) {
                var isValid = true;
                var errors = [];

                // Validate required fields
                $(this).find('[required]').each(function() {
                    var $field = $(this);
                    var value = $field.val().trim();

                    if (!value) {
                        isValid = false;
                        $field.addClass('border-red-300 dark:border-red-600');
                        errors.push($field.closest('.form-group').find('label').text() + ' is required');
                    } else {
                        $field.removeClass('border-red-300 dark:border-red-600');
                    }
                });

                // Validate target date
                var targetDate = $('input[name="TargetDate"]').val();
                if (targetDate) {
                    var target = new Date(targetDate);
                    var today = new Date();
                    today.setHours(0, 0, 0, 0);

                    if (target < today) {
                        isValid = false;
                        errors.push('Target date cannot be in the past');
                    }
                }

                if (!isValid) {
                    e.preventDefault();
                    showValidationErrors(errors);
                } else {
                    hideValidationErrors();
                }
            });
        }

        function setupFormEnhancements() {
            // Auto-resize textareas
            $('textarea').each(function() {
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
            }).on('input', function() {
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
            });

            // Tag input enhancement
            $('input[name="Tags"]').on('blur', function() {
                var tags = $(this).val().split(',').map(tag => tag.trim()).filter(tag => tag);
                $(this).val(tags.join(', '));
            });
        }

        function setupStatusChangeWarning() {
            var originalStatus = $('select[name="Status"]').val();

            $('select[name="Status"]').on('change', function() {
                var newStatus = $(this).val();
                var statusText = $(this).find('option:selected').text();

                if (originalStatus !== newStatus) {
                    if (newStatus == '@((int)FeatureStatus.Completed)') {
                        showStatusWarning('Marking this feature as completed will affect progress calculations and reporting.');
                    } else if (newStatus == '@((int)FeatureStatus.Cancelled)') {
                        showStatusWarning('Cancelling this feature will remove it from active development tracking.');
                    } else if (newStatus == '@((int)FeatureStatus.OnHold)') {
                        showStatusWarning('Putting this feature on hold will pause progress tracking.');
                    }
                }
            });
        }

        function showStatusWarning(message) {
            $('.status-warning').remove();

            var warningHtml = `
                <div class="status-warning bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3 mt-2">
                    <div class="flex">
                        <i class="fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400 mt-0.5 mr-2"></i>
                        <p class="text-sm text-yellow-800 dark:text-yellow-200">${message}</p>
                    </div>
                </div>
            `;

            $('select[name="Status"]').closest('.form-group').append(warningHtml);
        }

        function showValidationErrors(errors) {
            hideValidationErrors();

            var errorHtml = `
                <div class="validation-errors bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <i class="fas fa-exclamation-circle text-red-600 dark:text-red-400 mt-0.5 mr-3"></i>
                        <div>
                            <h4 class="text-sm font-medium text-red-800 dark:text-red-200 mb-2">Please correct the following errors:</h4>
                            <ul class="text-sm text-red-700 dark:text-red-300 list-disc list-inside space-y-1">
                                ${errors.map(error => `<li>${error}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                </div>
            `;

            $('form').prepend(errorHtml);
            $('html, body').animate({ scrollTop: 0 }, 300);
        }

        function hideValidationErrors() {
            $('.validation-errors').remove();
        }
    </script>
}
