using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using PM.Tool.Core.Entities;
using PM.Tool.Core.Interfaces;
using PM.Tool.Models.ViewModels;

namespace PM.Tool.Controllers
{
    [Authorize]
    public class DocumentController : Controller
    {
        private readonly IDocumentService _documentService;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IProjectService _projectService;
        private readonly ITaskService _taskService;
        private readonly ILogger<DocumentController> _logger;

        public DocumentController(
            IDocumentService documentService,
            UserManager<ApplicationUser> userManager,
            IProjectService projectService,
            ITaskService taskService,
            ILogger<DocumentController> logger)
        {
            _documentService = documentService;
            _userManager = userManager;
            _projectService = projectService;
            _taskService = taskService;
            _logger = logger;
        }

        // GET: Document/Categories
        public async Task<IActionResult> Categories()
        {
            var categories = await _documentService.GetAllCategoriesAsync();
            var viewModels = categories.Select(c => new DocumentCategoryViewModel
            {
                Id = c.Id,
                Name = c.Name,
                Description = c.Description,
                CreatedByName = c.CreatedBy.UserName ?? "Unknown",
                CreatedAt = c.CreatedAt,
                ProjectAttachmentCount = c.ProjectAttachments.Count,
                TaskAttachmentCount = c.TaskAttachments.Count
            });

            return View(viewModels);
        }

        // POST: Document/CreateCategory
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateCategory(DocumentCategoryViewModel model)
        {
            if (!ModelState.IsValid)
                return RedirectToAction(nameof(Categories));

            var user = await _userManager.GetUserAsync(User);
            if (user == null)
                return RedirectToAction("Login", "Account");

            var category = new DocumentCategory
            {
                Name = model.Name,
                Description = model.Description,
                CreatedByUserId = user.Id
            };

            await _documentService.CreateCategoryAsync(category);
            return RedirectToAction(nameof(Categories));
        }

        // GET: Document/AttachmentDetails/5?type=Project
        public async Task<IActionResult> AttachmentDetails(int id, string type)
        {
            var viewModel = await GetAttachmentDetailsViewModelAsync(id, type);
            if (viewModel == null)
                return NotFound();

            var categories = await _documentService.GetAllCategoriesAsync();
            viewModel.AvailableCategories = categories.Select(c => new DocumentCategoryViewModel
            {
                Id = c.Id,
                Name = c.Name,
                Description = c.Description
            }).ToList();

            return View(viewModel);
        }

        // POST: Document/UpdateMetadata
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateMetadata(UpdateAttachmentMetadataViewModel model)
        {
            if (!ModelState.IsValid)
                return RedirectToAction(nameof(AttachmentDetails), new { id = model.Id, type = model.AttachmentType });

            await _documentService.UpdateAttachmentMetadataAsync(
                model.Id,
                model.AttachmentType,
                model.Description,
                model.Tags);

            if (model.CategoryId.HasValue)
            {
                await _documentService.UpdateAttachmentCategoryAsync(
                    model.Id,
                    model.AttachmentType,
                    model.CategoryId);
            }

            return RedirectToAction(nameof(AttachmentDetails), new { id = model.Id, type = model.AttachmentType });
        }

        // POST: Document/AddVersion
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddVersion([FromForm] NewVersionViewModel model)
        {
            if (!ModelState.IsValid)
                return RedirectToAction(nameof(AttachmentDetails), 
                    new { id = model.AttachmentId, type = model.AttachmentType });

            var user = await _userManager.GetUserAsync(User);
            if (user == null)
                return RedirectToAction("Login", "Account");

            // Get current versions
            var versions = await _documentService.GetVersionsForAttachmentAsync(
                model.AttachmentId,
                model.AttachmentType);
            var nextVersion = versions.Any() ? versions.Max(v => v.Version) + 1 : 1;

            // Save the file
            var fileName = Path.GetFileName(model.File.FileName);
            var uniqueFileName = $"{Path.GetFileNameWithoutExtension(fileName)}_{DateTime.UtcNow:yyyyMMddHHmmss}{Path.GetExtension(fileName)}";
            var filePath = Path.Combine("uploads", model.AttachmentType.ToLower(), uniqueFileName);
            var fullPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", filePath);

            Directory.CreateDirectory(Path.GetDirectoryName(fullPath) ?? string.Empty);
            await using (var stream = new FileStream(fullPath, FileMode.Create))
            {
                await model.File.CopyToAsync(stream);
            }

            // Create version record
            var version = new DocumentVersion
            {
                FileName = fileName,
                FilePath = filePath,
                ContentType = model.File.ContentType,
                FileSize = model.File.Length,
                Version = nextVersion,
                ChangeDescription = model.ChangeDescription,
                AttachmentId = model.AttachmentId,
                AttachmentType = model.AttachmentType,
                UploadedByUserId = user.Id
            };

            await _documentService.AddVersionAsync(version);

            return RedirectToAction(nameof(AttachmentDetails), 
                new { id = model.AttachmentId, type = model.AttachmentType });
        }

        private async Task<AttachmentDetailsViewModel?> GetAttachmentDetailsViewModelAsync(int id, string type)
        {            if (type == "Project")
            {
                var project = await _projectService.GetProjectByIdAsync(id);
                if (project?.Attachments == null)
                    return null;

                var attachment = project.Attachments.FirstOrDefault();
                if (attachment == null)
                    return null;

                var versions = await _documentService.GetVersionsForAttachmentAsync(attachment.Id, "Project");
                return new AttachmentDetailsViewModel
                {
                    Id = attachment.Id,
                    FileName = attachment.FileName,
                    FilePath = attachment.FilePath,
                    ContentType = attachment.ContentType,
                    FileSize = attachment.FileSize,
                    Description = attachment.Description,
                    Tags = attachment.Tags,
                    CurrentVersion = attachment.CurrentVersion,
                    CategoryId = attachment.CategoryId,
                    CategoryName = attachment.Category?.Name,
                    UploadedByName = attachment.UploadedBy.UserName ?? "Unknown",
                    UploadedAt = attachment.UploadedAt,
                    LastModifiedAt = attachment.LastModifiedAt,
                    AttachmentType = "Project",
                    ParentId = project.Id,
                    ParentName = project.Name,
                    Versions = versions.Select(v => new DocumentVersionViewModel
                    {
                        Id = v.Id,
                        FileName = v.FileName,
                        FilePath = v.FilePath,
                        ContentType = v.ContentType,
                        FileSize = v.FileSize,
                        Version = v.Version,
                        ChangeDescription = v.ChangeDescription,
                        UploadedByName = v.UploadedBy.UserName ?? "Unknown",
                        UploadedAt = v.UploadedAt
                    }).ToList()
                };
            }
            else if (type == "Task")
            {
                var task = await _taskService.GetTaskByIdAsync(id);
                if (task?.Attachments == null)
                    return null;

                var attachment = task.Attachments.FirstOrDefault();
                if (attachment == null)
                    return null;

                var versions = await _documentService.GetVersionsForAttachmentAsync(attachment.Id, "Task");
                return new AttachmentDetailsViewModel
                {
                    Id = attachment.Id,
                    FileName = attachment.FileName,
                    FilePath = attachment.FilePath,
                    ContentType = attachment.ContentType,
                    FileSize = attachment.FileSize,
                    Description = attachment.Description,
                    Tags = attachment.Tags,
                    CurrentVersion = attachment.CurrentVersion,
                    CategoryId = attachment.CategoryId,
                    CategoryName = attachment.Category?.Name,
                    UploadedByName = attachment.UploadedBy.UserName ?? "Unknown",
                    UploadedAt = attachment.UploadedAt,
                    LastModifiedAt = attachment.LastModifiedAt,
                    AttachmentType = "Task",
                    ParentId = task.Id,
                    ParentName = task.Title,
                    Versions = versions.Select(v => new DocumentVersionViewModel
                    {
                        Id = v.Id,
                        FileName = v.FileName,
                        FilePath = v.FilePath,
                        ContentType = v.ContentType,
                        FileSize = v.FileSize,
                        Version = v.Version,
                        ChangeDescription = v.ChangeDescription,
                        UploadedByName = v.UploadedBy.UserName ?? "Unknown",
                        UploadedAt = v.UploadedAt
                    }).ToList()
                };
            }

            return null;
        }
    }
}
