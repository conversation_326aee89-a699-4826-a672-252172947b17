/* Professional Documentation Styles */

:root {
    /* Documentation-specific colors */
    --docs-primary: #2563eb;
    --docs-secondary: #64748b;
    --docs-accent: #06b6d4;
    --docs-success: #10b981;
    --docs-warning: #f59e0b;
    --docs-danger: #ef4444;

    /* Documentation layout */
    --docs-header-height: 70px;
    --docs-sidebar-width: 280px;
    --docs-toc-width: 240px;
    --docs-content-max-width: 800px;

    /* Documentation colors */
    --docs-bg: #ffffff;
    --docs-bg-secondary: #f8fafc;
    --docs-bg-tertiary: #f1f5f9;
    --docs-border: #e2e8f0;
    --docs-text: #0f172a;
    --docs-text-secondary: #475569;
    --docs-text-muted: #64748b;

    /* Code highlighting */
    --docs-code-bg: #f8fafc;
    --docs-code-border: #e2e8f0;
    --docs-code-text: #1e293b;
}

/* Dark theme */
[data-theme="dark"] {
    --docs-bg: #0f172a;
    --docs-bg-secondary: #1e293b;
    --docs-bg-tertiary: #334155;
    --docs-border: #475569;
    --docs-text: #f1f5f9;
    --docs-text-secondary: #cbd5e1;
    --docs-text-muted: #94a3b8;
    --docs-code-bg: #1e293b;
    --docs-code-border: #475569;
    --docs-code-text: #f1f5f9;
}

/* Base layout */
.docs-layout {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--docs-bg);
    color: var(--docs-text);
    line-height: 1.6;
}

/* Header */
.docs-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--docs-header-height);
    background: var(--docs-bg);
    border-bottom: 1px solid var(--docs-border);
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.docs-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 1rem;
}

.docs-brand {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.docs-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: var(--docs-text);
    font-weight: 600;
    font-size: 1.125rem;
}

.docs-logo:hover {
    color: var(--docs-primary);
}

.docs-title {
    font-weight: 600;
}

.docs-version {
    background: var(--docs-primary);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Search */
.docs-search {
    flex: 1;
    max-width: 400px;
    margin: 0 2rem;
    position: relative;
}

.search-container {
    position: relative;
}

.search-input {
    padding-left: 2.5rem;
    border: 1px solid var(--docs-border);
    border-radius: 0.5rem;
    background: var(--docs-bg-secondary);
    transition: all 0.15s ease;
}

.search-input:focus {
    border-color: var(--docs-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    background: var(--docs-bg);
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--docs-text-muted);
    z-index: 10;
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--docs-bg);
    border: 1px solid var(--docs-border);
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    max-height: 400px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.search-result-item {
    padding: 0.75rem;
    border-bottom: 1px solid var(--docs-border);
    cursor: pointer;
    transition: background-color 0.15s ease;
}

.search-result-item:hover,
.search-result-item.active {
    background: var(--docs-bg-secondary);
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-title {
    font-weight: 500;
    color: var(--docs-text);
    margin-bottom: 0.25rem;
}

.search-result-excerpt {
    font-size: 0.875rem;
    color: var(--docs-text-secondary);
    line-height: 1.4;
}

.search-result-section {
    font-size: 0.75rem;
    color: var(--docs-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.25rem;
}

/* Header actions */
.docs-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.docs-mobile-toggle {
    background: none;
    border: 1px solid var(--docs-border);
    color: var(--docs-text);
    padding: 0.5rem;
    border-radius: 0.375rem;
    cursor: pointer;
}

/* Container */
.docs-container {
    display: flex;
    margin-top: var(--docs-header-height);
    min-height: calc(100vh - var(--docs-header-height));
}

/* Sidebar */
.docs-sidebar {
    width: var(--docs-sidebar-width);
    background: var(--docs-bg-secondary);
    border-right: 1px solid var(--docs-border);
    position: fixed;
    top: var(--docs-header-height);
    left: 0;
    height: calc(100vh - var(--docs-header-height));
    overflow-y: auto;
    z-index: 900;
    transition: transform 0.3s ease;
}

.docs-sidebar-content {
    padding: 1.5rem 0;
}

/* Navigation */
.docs-nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.docs-nav-item {
    margin-bottom: 0.25rem;
}

.docs-nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: var(--docs-text-secondary);
    text-decoration: none;
    transition: all 0.15s ease;
    border-radius: 0;
}

.docs-nav-link:hover,
.docs-nav-link.active {
    background: var(--docs-primary);
    color: white;
}

.docs-nav-section {
    margin-bottom: 0.5rem;
}

.docs-nav-section-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: var(--docs-text);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease;
    user-select: none;
}

.docs-nav-section-header:hover,
.docs-nav-section-header.active {
    background: var(--docs-bg-tertiary);
    color: var(--docs-primary);
}

.docs-nav-section-header .collapse-icon {
    margin-left: auto;
    transition: transform 0.15s ease;
}

.docs-nav-section-header[aria-expanded="true"] .collapse-icon {
    transform: rotate(180deg);
}

.docs-nav-sublist {
    list-style: none;
    padding: 0;
    margin: 0;
    background: var(--docs-bg);
}

.docs-nav-subitem {
    border-left: 2px solid transparent;
}

.docs-nav-sublink {
    display: block;
    padding: 0.5rem 1.5rem 0.5rem 3rem;
    color: var(--docs-text-secondary);
    text-decoration: none;
    font-size: 0.875rem;
    transition: all 0.15s ease;
}

.docs-nav-sublink:hover {
    color: var(--docs-primary);
    background: var(--docs-bg-secondary);
}

.docs-nav-sublink.active {
    color: var(--docs-primary);
    background: rgba(37, 99, 235, 0.1);
    border-left-color: var(--docs-primary);
}

/* Main content */
.docs-main {
    flex: 1;
    margin-left: var(--docs-sidebar-width);
    padding: 2rem;
    max-width: calc(100vw - var(--docs-sidebar-width) - var(--docs-toc-width));
}

/* Breadcrumb */
.docs-breadcrumb {
    margin-bottom: 2rem;
}

.docs-breadcrumb .breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
}

.docs-breadcrumb .breadcrumb-item {
    font-size: 0.875rem;
}

.docs-breadcrumb .breadcrumb-item a {
    color: var(--docs-text-muted);
    text-decoration: none;
}

.docs-breadcrumb .breadcrumb-item a:hover {
    color: var(--docs-primary);
}

.docs-breadcrumb .breadcrumb-item.active {
    color: var(--docs-text);
}

/* Content */
.docs-content {
    max-width: var(--docs-content-max-width);
    margin-bottom: 3rem;
}

.docs-content h1,
.docs-content h2,
.docs-content h3,
.docs-content h4,
.docs-content h5,
.docs-content h6 {
    color: var(--docs-text);
    font-weight: 600;
    line-height: 1.25;
    margin-top: 2rem;
    margin-bottom: 1rem;
    scroll-margin-top: calc(var(--docs-header-height) + 1rem);
}

.docs-content h1 {
    font-size: 2.25rem;
    margin-top: 0;
    border-bottom: 1px solid var(--docs-border);
    padding-bottom: 1rem;
}

.docs-content h2 {
    font-size: 1.875rem;
    border-bottom: 1px solid var(--docs-border);
    padding-bottom: 0.5rem;
}

.docs-content h3 {
    font-size: 1.5rem;
}

.docs-content h4 {
    font-size: 1.25rem;
}

.docs-content h5 {
    font-size: 1.125rem;
}

.docs-content h6 {
    font-size: 1rem;
}

.docs-content p {
    margin-bottom: 1rem;
    color: var(--docs-text-secondary);
}

.docs-content a {
    color: var(--docs-primary);
    text-decoration: none;
}

.docs-content a:hover {
    text-decoration: underline;
}

.docs-content ul,
.docs-content ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.docs-content li {
    margin-bottom: 0.5rem;
    color: var(--docs-text-secondary);
}

.docs-content blockquote {
    border-left: 4px solid var(--docs-primary);
    padding: 1rem 1.5rem;
    margin: 1.5rem 0;
    background: var(--docs-bg-secondary);
    border-radius: 0 0.375rem 0.375rem 0;
}

.docs-content blockquote p {
    margin: 0;
    font-style: italic;
}

/* Code blocks */
.docs-content pre {
    background: var(--docs-code-bg);
    border: 1px solid var(--docs-code-border);
    border-radius: 0.5rem;
    padding: 1rem;
    overflow-x: auto;
    margin: 1.5rem 0;
    font-family: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
    font-size: 0.875rem;
    line-height: 1.5;
}

.docs-content code {
    background: var(--docs-code-bg);
    border: 1px solid var(--docs-code-border);
    border-radius: 0.25rem;
    padding: 0.125rem 0.375rem;
    font-family: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
    font-size: 0.875rem;
    color: var(--docs-code-text);
}

.docs-content pre code {
    background: none;
    border: none;
    padding: 0;
}

/* Tables */
.docs-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5rem 0;
    background: var(--docs-bg);
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.docs-content th,
.docs-content td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--docs-border);
}

.docs-content th {
    background: var(--docs-bg-secondary);
    font-weight: 600;
    color: var(--docs-text);
}

.docs-content td {
    color: var(--docs-text-secondary);
}

.docs-content tr:last-child td {
    border-bottom: none;
}

/* Images */
.docs-content img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    margin: 1rem 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Page navigation */
.docs-page-nav {
    border-top: 1px solid var(--docs-border);
    padding-top: 2rem;
    margin-top: 3rem;
}

.docs-page-nav-link {
    display: block;
    padding: 1rem;
    border: 1px solid var(--docs-border);
    border-radius: 0.5rem;
    text-decoration: none;
    color: var(--docs-text);
    transition: all 0.15s ease;
}

.docs-page-nav-link:hover {
    border-color: var(--docs-primary);
    background: var(--docs-bg-secondary);
    color: var(--docs-text);
}

.docs-page-nav-prev {
    text-align: left;
}

.docs-page-nav-next {
    text-align: right;
}

.docs-page-nav-direction {
    font-size: 0.875rem;
    color: var(--docs-text-muted);
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.docs-page-nav-next .docs-page-nav-direction {
    justify-content: flex-end;
}

.docs-page-nav-title {
    font-weight: 500;
    color: var(--docs-text);
}

/* Table of Contents */
.docs-toc {
    width: var(--docs-toc-width);
    position: fixed;
    top: var(--docs-header-height);
    right: 0;
    height: calc(100vh - var(--docs-header-height));
    padding: 2rem 1rem;
    border-left: 1px solid var(--docs-border);
    background: var(--docs-bg);
    overflow-y: auto;
}

.docs-toc-content {
    position: sticky;
    top: 2rem;
}

.docs-toc-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--docs-text);
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.docs-toc-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.docs-toc-nav li {
    margin-bottom: 0.25rem;
}

.docs-toc-nav a {
    display: block;
    padding: 0.25rem 0;
    color: var(--docs-text-muted);
    text-decoration: none;
    font-size: 0.875rem;
    line-height: 1.4;
    transition: color 0.15s ease;
    border-left: 2px solid transparent;
    padding-left: 0.5rem;
}

.docs-toc-nav a:hover,
.docs-toc-nav a.active {
    color: var(--docs-primary);
    border-left-color: var(--docs-primary);
}

.docs-toc-nav ul ul a {
    padding-left: 1rem;
    font-size: 0.8125rem;
}

.docs-toc-nav ul ul ul a {
    padding-left: 1.5rem;
}

/* Footer */
.docs-footer {
    background: var(--docs-bg-secondary);
    border-top: 1px solid var(--docs-border);
    padding: 2rem 0;
    margin-left: var(--docs-sidebar-width);
}

.docs-footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

.docs-footer-info p {
    margin: 0;
    font-size: 0.875rem;
    color: var(--docs-text-muted);
}

.docs-footer-links {
    display: flex;
    gap: 1rem;
}

.docs-footer-links a {
    color: var(--docs-text-muted);
    text-decoration: none;
    font-size: 0.875rem;
}

.docs-footer-links a:hover {
    color: var(--docs-primary);
}

/* Responsive design */
@media (max-width: 1200px) {
    .docs-toc {
        display: none;
    }

    .docs-main {
        max-width: calc(100vw - var(--docs-sidebar-width));
    }
}

@media (max-width: 992px) {
    .docs-sidebar {
        transform: translateX(-100%);
    }

    .docs-sidebar.show {
        transform: translateX(0);
    }

    .docs-main {
        margin-left: 0;
        max-width: 100vw;
    }

    .docs-footer {
        margin-left: 0;
    }

    .docs-search {
        display: none;
    }
}

@media (max-width: 768px) {
    .docs-header-content {
        padding: 0 1rem;
    }

    .docs-brand .docs-title {
        display: none;
    }

    .docs-actions {
        gap: 0.25rem;
    }

    .docs-actions .btn {
        padding: 0.375rem 0.5rem;
    }

    .docs-main {
        padding: 1rem;
    }

    .docs-content {
        max-width: 100%;
    }

    .docs-footer-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

/* Print styles */
@media print {
    .docs-header,
    .docs-sidebar,
    .docs-toc,
    .docs-footer,
    .docs-page-nav {
        display: none !important;
    }

    .docs-main {
        margin-left: 0;
        max-width: 100%;
        padding: 0;
    }

    .docs-content {
        max-width: 100%;
    }

    .docs-content h1,
    .docs-content h2,
    .docs-content h3,
    .docs-content h4,
    .docs-content h5,
    .docs-content h6 {
        break-after: avoid;
    }

    .docs-content pre,
    .docs-content blockquote,
    .docs-content table {
        break-inside: avoid;
    }
}

/* RTL support */
[dir="rtl"] .docs-sidebar {
    left: auto;
    right: 0;
    border-right: none;
    border-left: 1px solid var(--docs-border);
}

[dir="rtl"] .docs-main {
    margin-left: 0;
    margin-right: var(--docs-sidebar-width);
}

[dir="rtl"] .docs-toc {
    right: auto;
    left: 0;
    border-left: none;
    border-right: 1px solid var(--docs-border);
}

[dir="rtl"] .docs-footer {
    margin-left: 0;
    margin-right: var(--docs-sidebar-width);
}

[dir="rtl"] .docs-nav-subitem {
    border-left: none;
    border-right: 2px solid transparent;
}

[dir="rtl"] .docs-nav-sublink.active {
    border-left-color: transparent;
    border-right-color: var(--docs-primary);
}

[dir="rtl"] .docs-toc-nav a {
    border-left: none;
    border-right: 2px solid transparent;
    padding-left: 0;
    padding-right: 0.5rem;
}

[dir="rtl"] .docs-toc-nav a:hover,
[dir="rtl"] .docs-toc-nav a.active {
    border-left-color: transparent;
    border-right-color: var(--docs-primary);
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .docs-content {
        --docs-border: #000000;
        --docs-text: #000000;
        --docs-bg: #ffffff;
    }
}

/* Focus styles for accessibility */
.docs-nav-link:focus,
.docs-nav-sublink:focus,
.docs-toc-nav a:focus,
.search-input:focus {
    outline: 2px solid var(--docs-primary);
    outline-offset: 2px;
}

/* Additional Documentation Styles */

/* Documentation Home Styles */
.docs-home {
    max-width: 1200px;
    margin: 0 auto;
}

.docs-hero {
    text-align: center;
    padding: 3rem 0;
    background: linear-gradient(135deg, var(--docs-primary) 0%, var(--docs-accent) 100%);
    color: white;
    border-radius: 1rem;
    margin-bottom: 3rem;
}

.docs-hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.docs-hero-description {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.docs-hero-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.docs-stat {
    text-align: center;
}

.docs-stat-number {
    font-size: 2rem;
    font-weight: 700;
}

.docs-stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.docs-quick-start-card,
.docs-section-card,
.docs-popular-page-card,
.docs-help-card {
    background: var(--docs-bg);
    border: 1px solid var(--docs-border);
    border-radius: 0.75rem;
    padding: 1.5rem;
    height: 100%;
    transition: all 0.15s ease;
}

.docs-quick-start-card:hover,
.docs-section-card:hover,
.docs-popular-page-card:hover,
.docs-help-card:hover {
    border-color: var(--docs-primary);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.1);
    transform: translateY(-2px);
}

.docs-quick-start-icon,
.docs-feature-icon {
    width: 3rem;
    height: 3rem;
    background: var(--docs-primary);
    color: white;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    margin-bottom: 1rem;
}

.docs-sections-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

.docs-section-card-header {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.docs-section-card-icon {
    width: 2.5rem;
    height: 2.5rem;
    background: var(--docs-bg-secondary);
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--docs-primary);
    font-size: 1.125rem;
    flex-shrink: 0;
}

.docs-section-card-title {
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.docs-section-card-title a {
    color: var(--docs-text);
    text-decoration: none;
}

.docs-section-card-title a:hover {
    color: var(--docs-primary);
}

.docs-section-card-description {
    color: var(--docs-text-secondary);
    margin: 0;
    font-size: 0.875rem;
}

.docs-page-count {
    font-size: 0.75rem;
    color: var(--docs-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.docs-page-list {
    list-style: none;
    padding: 0;
    margin: 0.5rem 0 0 0;
}

.docs-page-list li {
    margin-bottom: 0.25rem;
}

.docs-page-list a {
    color: var(--docs-text-secondary);
    text-decoration: none;
    font-size: 0.875rem;
}

.docs-page-list a:hover {
    color: var(--docs-primary);
}

.docs-more-pages a {
    color: var(--docs-primary);
    font-weight: 500;
}

.docs-popular-page-title {
    margin: 0 0 0.75rem 0;
    font-size: 1.125rem;
    font-weight: 500;
}

.docs-popular-page-title a {
    color: var(--docs-text);
    text-decoration: none;
}

.docs-popular-page-title a:hover {
    color: var(--docs-primary);
}

.docs-popular-page-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 0.75rem;
    color: var(--docs-text-muted);
}

.docs-popular-page-section {
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 500;
}

.docs-popular-page-time {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.docs-popular-page-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.docs-features {
    margin-top: 2rem;
}

.docs-feature {
    text-align: center;
    margin-bottom: 2rem;
}

.docs-feature h4 {
    margin: 1rem 0 0.5rem 0;
    font-weight: 600;
    color: var(--docs-text);
}

.docs-feature p {
    color: var(--docs-text-secondary);
    font-size: 0.875rem;
    margin: 0;
}

.docs-help-card h4 {
    margin: 0 0 0.75rem 0;
    font-weight: 600;
    color: var(--docs-text);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.docs-help-card p {
    color: var(--docs-text-secondary);
    margin-bottom: 1rem;
}
