@using PM.Tool.Core.Enums
@using TaskStatus = PM.Tool.Core.Enums.TaskStatus
@model ProjectDetailsViewModel

@{
    ViewData["Title"] = $"Project: {Model.Name}";
}

<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-project-diagram mr-3 text-primary-600 dark:text-primary-400"></i>
                @Model.Name
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Created by @Model.CreatedByName on @Model.CreatedAt.ToString("MMM dd, yyyy")
            </p>
        </div>
        <div class="flex space-x-3">
            @if (Model.CanEdit)
            {
                    ViewData["Text"] = "Edit Project";
                    ViewData["Variant"] = "primary";
                    ViewData["Icon"] = "fas fa-edit";
                    ViewData["Href"] = Url.Action("Edit", new { id = Model.Id });

                <partial name="Components/_Button" view-data="ViewData" />
            }
            @if (Model.CanDelete)
            {
                    ViewData["Text"] = "Delete Project";
                    ViewData["Variant"] = "danger";
                    ViewData["Icon"] = "fas fa-trash";
                    ViewData["Href"] = Url.Action("Delete", new { id = Model.Id });
                <
            partial name="Components/_Button" view-data="ViewData" />
            }
            @{
                ViewData["Text"] = "Back to Projects";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Index");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="grid grid-cols-1 xl:grid-cols-3 gap-6">
    <!-- Project Details -->
    <div class="xl:col-span-2 space-y-6">
        <!-- Project Information Card -->
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-info-circle text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Project Information</h3>
                    </div>
                </div>
            </div>
            <div class="card-body-custom">
                <!-- Description -->
                @if (!string.IsNullOrEmpty(Model.Description))
                {
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wide mb-2">Description</h4>
                        <div class="prose prose-sm dark:prose-invert max-w-none">
                            @Html.Raw(Model.Description)
                        </div>
                    </div>
                }

                <!-- Key Information Grid -->
                <div class="grid grid-cols-2 lg:grid-cols-4 gap-6">
                    <div>
                        <p class="text-sm font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wide mb-1">Status</p>
                        @{
                            var statusClass = Model.Status.ToString().ToLower() switch {
                                "active" => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
                                "completed" => "bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200",
                                "onhold" => "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
                                "planning" => "bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200",
                                _ => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200"
                            };
                        }
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @statusClass">
                            @Model.Status
                        </span>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wide mb-1">Start Date</p>
                        <p class="text-sm font-semibold text-neutral-900 dark:text-dark-100">@Model.StartDate.ToString("MMM dd, yyyy")</p>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wide mb-1">End Date</p>
                        <p class="text-sm font-semibold text-neutral-900 dark:text-dark-100">@(Model.EndDate?.ToString("MMM dd, yyyy") ?? "Not set")</p>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wide mb-1">Budget</p>
                        <p class="text-sm font-semibold text-neutral-900 dark:text-dark-100">@Model.Budget.ToString("C")</p>
                    </div>
                </div>

                <!-- Progress Section -->
                <div class="mt-6">
                    <h4 class="text-sm font-medium text-neutral-500 dark:text-dark-400 uppercase tracking-wide mb-3">Project Progress</h4>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-neutral-900 dark:text-dark-100">
                                @Model.CompletedTasks of @Model.TotalTasks tasks completed
                            </span>
                            <span class="text-sm font-semibold text-neutral-900 dark:text-dark-100">
                                @Model.ProgressPercentage.ToString("F1")%
                            </span>
                        </div>
                        <div class="w-full bg-neutral-200 dark:bg-dark-600 rounded-full h-2">
                            @{
                                var progressClass = Model.IsOverdue ? "bg-danger-500" : "bg-primary-500";
                            }
                            <div class="@progressClass h-2 rounded-full transition-all duration-300" style="width: @Model.ProgressPercentage%"></div>
                        </div>
                        @if (Model.IsOverdue)
                        {
                            <p class="text-sm text-danger-600 dark:text-danger-400 flex items-center">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                This project is overdue
                            </p>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Project Actions Card -->
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                        <i class="fas fa-tools text-primary-600 dark:text-primary-400 text-sm"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Project Tools</h3>
                    </div>
                </div>
            </div>
            <div class="card-body-custom">
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- WBS -->
                    @{
                        ViewData["Text"] = "Work Breakdown Structure";
                        ViewData["Variant"] = "outline";
                        ViewData["Icon"] = "fas fa-sitemap";
                        ViewData["Href"] = Url.Action("ProjectWbs", "Wbs", new { projectId = Model.Id });
                        ViewData["FullWidth"] = true;
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    <!-- Risk Management -->
                    @{
                        ViewData["Text"] = "Risk Management";
                        ViewData["Variant"] = "outline";
                        ViewData["Icon"] = "fas fa-exclamation-triangle";
                        ViewData["Href"] = Url.Action("Index", "Risk", new { projectId = Model.Id });
                        ViewData["FullWidth"] = true;
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    <!-- Meetings -->
                    @{
                        ViewData["Text"] = "Meetings";
                        ViewData["Variant"] = "outline";
                        ViewData["Icon"] = "fas fa-calendar-alt";
                        ViewData["Href"] = Url.Action("Index", "Meeting", new { projectId = Model.Id });
                        ViewData["FullWidth"] = true;
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    <!-- Requirements -->
                    @{
                        ViewData["Text"] = "Requirements";
                        ViewData["Variant"] = "outline";
                        ViewData["Icon"] = "fas fa-file-alt";
                        ViewData["Href"] = Url.Action("Index", "Requirement", new { projectId = Model.Id });
                        ViewData["FullWidth"] = true;
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    <!-- Gantt Chart -->
                    @{
                        ViewData["Text"] = "Gantt Chart";
                        ViewData["Variant"] = "outline";
                        ViewData["Icon"] = "fas fa-chart-gantt";
                        ViewData["Href"] = Url.Action("Gantt", "Projects", new { id = Model.Id });
                        ViewData["FullWidth"] = true;
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    <!-- Analytics -->
                    @{
                        ViewData["Text"] = "Project Analytics";
                        ViewData["Variant"] = "outline";
                        ViewData["Icon"] = "fas fa-chart-line";
                        ViewData["Href"] = Url.Action("Project", "Analytics", new { id = Model.Id });
                        ViewData["FullWidth"] = true;
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    <!-- Requirements Hierarchy -->
                    @{
                        ViewData["Text"] = "Requirements Hierarchy";
                        ViewData["Variant"] = "outline";
                        ViewData["Icon"] = "fas fa-project-diagram";
                        ViewData["Href"] = Url.Action("Hierarchy", "Requirement", new { projectId = Model.Id });
                        ViewData["FullWidth"] = true;
                    }
                    <partial name="Components/_Button" view-data="ViewData" />

                    <!-- Meeting Calendar -->
                    @{
                        ViewData["Text"] = "Meeting Calendar";
                        ViewData["Variant"] = "outline";
                        ViewData["Icon"] = "fas fa-calendar";
                        ViewData["Href"] = Url.Action("Calendar", "Meeting", new { projectId = Model.Id });
                        ViewData["FullWidth"] = true;
                    }
                    <partial name="Components/_Button" view-data="ViewData" />
                </div>
            </div>
        </div>

        <!-- Recent Tasks Card -->
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                            <i class="fas fa-tasks text-primary-600 dark:text-primary-400 text-sm"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Recent Tasks</h3>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        @{
                            ViewData["Text"] = "Add Task";
                            ViewData["Variant"] = "primary";
                            ViewData["Size"] = "sm";
                            ViewData["Icon"] = "fas fa-plus";
                            ViewData["Href"] = Url.Action("Create", "Tasks", new { projectId = Model.Id });
                        }
                        <partial name="Components/_Button" view-data="ViewData" />

                        @{
                            ViewData["Text"] = "View All";
                            ViewData["Variant"] = "outline";
                            ViewData["Size"] = "sm";
                            ViewData["Icon"] = "fas fa-list";
                            ViewData["Href"] = Url.Action("Index", "Tasks", new { projectId = Model.Id });
                        }
                        <partial name="Components/_Button" view-data="ViewData" />
                    </div>
                </div>
            </div>
            <div class="card-body-custom">
                @if (Model.RecentTasks.Any())
                {
                    <div class="space-y-3">
                        @foreach (var task in Model.RecentTasks)
                        {
                            <a href="@Url.Action("Details", "Tasks", new { id = task.Id })"
                               class="block p-4 bg-neutral-50 dark:bg-dark-700 rounded-lg hover:bg-neutral-100 dark:hover:bg-dark-600 transition-colors">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <h4 class="text-sm font-semibold text-neutral-900 dark:text-dark-100 mb-1">@task.Title</h4>
                                        <p class="text-xs text-neutral-500 dark:text-dark-400">
                                            <i class="fas fa-user mr-1"></i>
                                            @(task.AssignedToName ?? "Unassigned")
                                        </p>
                                    </div>
                                    <div>
                                        @{
                                            var taskStatusClass = task.Status.ToString().ToLower() switch {
                                                "done" => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
                                                "inprogress" => "bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200",
                                                "inreview" => "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
                                                _ => "bg-neutral-100 dark:bg-dark-600 text-neutral-800 dark:text-dark-200"
                                            };
                                        }
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @taskStatusClass">
                                            @task.Status
                                        </span>
                                    </div>
                                </div>
                            </a>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center py-8">
                        <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-tasks text-2xl text-neutral-400 dark:text-dark-500"></i>
                        </div>
                        <p class="text-neutral-500 dark:text-dark-400 mb-4">No tasks found for this project</p>
                        @{
                            ViewData["Text"] = "Create First Task";
                            ViewData["Variant"] = "primary";
                            ViewData["Icon"] = "fas fa-plus";
                            ViewData["Href"] = Url.Action("Create", "Tasks", new { projectId = Model.Id });
                        }
                        <partial name="Components/_Button" view-data="ViewData" />
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="xl:col-span-1 space-y-6">

        <!-- Team Members Card -->
        <div class="card-custom">
            <div class="card-header-custom">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                            <i class="fas fa-users text-primary-600 dark:text-primary-400 text-sm"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Team Members</h3>
                        </div>
                    </div>
                    @if (Model.CurrentUserRole == UserRole.ProjectManager || Model.CurrentUserRole == UserRole.Admin)
                    {
                        <div>
                            @{
                                ViewData["Text"] = "";
                                ViewData["Variant"] = "outline";
                                ViewData["Size"] = "sm";
                                ViewData["Icon"] = "fas fa-user-plus";
                                ViewData["Href"] = Url.Action("AddMember", new { id = Model.Id });
                                ViewData["Title"] = "Add Member";
                            }
                            <partial name="Components/_Button" view-data="ViewData" />
                        </div>
                    }
                </div>
            </div>
            <div class="card-body-custom">
                @if (Model.Members.Any())
                {
                    <div class="space-y-3">
                        @foreach (var member in Model.Members)
                        {
                            <div class="flex items-center justify-between p-3 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-primary-600 dark:text-primary-400 text-sm"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-semibold text-neutral-900 dark:text-dark-100">@member.FullName</h4>
                                        <p class="text-xs text-neutral-500 dark:text-dark-400">@member.Role</p>
                                    </div>
                                </div>
                                @if (Model.CurrentUserRole == UserRole.ProjectManager || Model.CurrentUserRole == UserRole.Admin)
                                {
                                    <div class="relative">
                                        <button class="p-1 rounded hover:bg-neutral-200 dark:hover:bg-dark-600 transition-colors member-menu-toggle"
                                                data-member-id="@member.Id">
                                            <i class="fas fa-ellipsis-v text-neutral-400 dark:text-dark-500"></i>
                                        </button>
                                        <div class="absolute right-0 mt-2 w-48 bg-white dark:bg-surface-dark rounded-lg shadow-strong border border-neutral-200 dark:border-dark-200 py-2 hidden member-menu"
                                             data-member-id="@member.Id">
                                            <button class="w-full text-left px-4 py-2 text-sm text-neutral-700 dark:text-dark-200 hover:bg-neutral-100 dark:hover:bg-dark-700 transition-colors edit-member-btn"
                                                    data-member-id="@member.Id" data-member-role="@member.Role">
                                                <i class="fas fa-edit mr-2"></i>Edit Role
                                            </button>
                                            <button class="w-full text-left px-4 py-2 text-sm text-danger-600 dark:text-danger-400 hover:bg-danger-50 dark:hover:bg-danger-900 transition-colors remove-member-btn"
                                                    data-member-id="@member.Id" data-member-name="@member.FullName">
                                                <i class="fas fa-trash mr-2"></i>Remove
                                            </button>
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center py-8">
                        <div class="w-16 h-16 bg-neutral-100 dark:bg-dark-700 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-users text-2xl text-neutral-400 dark:text-dark-500"></i>
                        </div>
                        <p class="text-neutral-500 dark:text-dark-400 mb-4">No team members yet</p>
                        @if (Model.CurrentUserRole == UserRole.ProjectManager || Model.CurrentUserRole == UserRole.Admin)
                        {
                            ViewData["Text"] = "Add First Member";
                            ViewData["Variant"] = "primary";
                            ViewData["Icon"] = "fas fa-user-plus";
                            ViewData["Href"] = Url.Action("AddMember", new { id = Model.Id });
                            <partial name="Components/_Button" view-data="ViewData" />
                        }
                    </div>
                }
            </div>
        </div>

        <!-- Milestones Card -->
        @if (Model.Milestones.Any())
        {
            <div class="card-custom">
                <div class="card-header-custom">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center">
                                <i class="fas fa-flag-checkered text-primary-600 dark:text-primary-400 text-sm"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-neutral-900 dark:text-dark-100">Milestones</h3>
                            </div>
                        </div>
                        @if (Model.CanEdit)
                        {
                            <div>
                                @{
                                    ViewData["Text"] = "";
                                    ViewData["Variant"] = "outline";
                                    ViewData["Size"] = "sm";
                                    ViewData["Icon"] = "fas fa-plus";
                                    ViewData["Href"] = Url.Action("Create", "Milestones", new { projectId = Model.Id });
                                    ViewData["Title"] = "Add Milestone";
                                }
                                <partial name="Components/_Button" view-data="ViewData" />
                            </div>
                        }
                    </div>
                </div>
                <div class="card-body-custom">
                    <div class="space-y-4">
                        @foreach (var milestone in Model.Milestones)
                        {
                            <div class="flex items-center justify-between p-3 bg-neutral-50 dark:bg-dark-700 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                                        <i class="fas fa-flag text-primary-600 dark:text-primary-400 text-sm"></i>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-semibold text-neutral-900 dark:text-dark-100">@milestone.Name</h4>
                                        <p class="text-xs text-neutral-500 dark:text-dark-400">
                                            <i class="fas fa-calendar mr-1"></i>
                                            Due: @milestone.DueDate.ToString("MMM dd, yyyy")
                                        </p>
                                    </div>
                                </div>
                                <div>
                                    @{
                                        var milestoneStatusClass = milestone.IsCompleted ? "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200" :
                                                                   milestone.IsOverdue ? "bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200" :
                                                                   "bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200";
                                        var milestoneStatusText = milestone.IsCompleted ? "Completed" : milestone.IsOverdue ? "Overdue" : "In Progress";
                                    }
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @milestoneStatusClass">
                                        @milestoneStatusText
                                    </span>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Handle member menu toggles
            $('.member-menu-toggle').on('click', function(e) {
                e.stopPropagation();
                var memberId = $(this).data('member-id');
                var menu = $(`.member-menu[data-member-id="${memberId}"]`);

                // Close other menus
                $('.member-menu').not(menu).addClass('hidden');

                // Toggle current menu
                menu.toggleClass('hidden');
            });

            // Close menus when clicking outside
            $(document).on('click', function() {
                $('.member-menu').addClass('hidden');
            });

            // Handle edit member role
            $('.edit-member-btn').on('click', function() {
                var memberId = $(this).data('member-id');
                var currentRole = $(this).data('member-role');

                // Close menu
                $('.member-menu').addClass('hidden');

                // Show edit modal (implement as needed)
                console.log('Edit member:', memberId, currentRole);
                // You can implement modal or inline editing here
            });

            // Handle remove member
            $('.remove-member-btn').on('click', function() {
                var memberId = $(this).data('member-id');
                var memberName = $(this).data('member-name');

                // Close menu
                $('.member-menu').addClass('hidden');

                // Show confirmation
                if (confirm(`Are you sure you want to remove ${memberName} from this project?`)) {
                    // Implement removal logic
                    console.log('Remove member:', memberId);
                    // You can make an AJAX call here
                }
            });

            // Enhanced member management
            window.showMemberEditModal = function(memberId, currentRole) {
                // Professional modal implementation would go here
                console.log('Edit member modal:', memberId, currentRole);
            };

            window.confirmMemberRemoval = function(memberId, memberName) {
                return confirm(`Are you sure you want to remove ${memberName} from this project?\n\nThis action cannot be undone and will remove their access to all project resources.`);
            };
        });
    </script>
}
