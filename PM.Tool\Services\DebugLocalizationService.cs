using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using PM.Tool.Resources;
using System.Globalization;
using System.Reflection;

namespace PM.Tool.Services
{
    public class DebugLocalizationService
    {
        private readonly IStringLocalizer<SharedResources> _localizer;
        private readonly ILogger<DebugLocalizationService> _logger;

        public DebugLocalizationService(IStringLocalizer<SharedResources> localizer, ILogger<DebugLocalizationService> logger)
        {
            _localizer = localizer;
            _logger = logger;
        }

        public void LogLocalizationDetails()
        {
            _logger.LogInformation("=== DETAILED LOCALIZATION ANALYSIS ===");
            
            // Log current culture information
            _logger.LogInformation("Current Culture: {Culture}", CultureInfo.CurrentCulture.Name);
            _logger.LogInformation("Current UI Culture: {UICulture}", CultureInfo.CurrentUICulture.Name);
            _logger.LogInformation("Default Thread Culture: {DefaultCulture}", CultureInfo.DefaultThreadCurrentCulture?.Name ?? "null");
            _logger.LogInformation("Default Thread UI Culture: {DefaultUICulture}", CultureInfo.DefaultThreadCurrentUICulture?.Name ?? "null");

            // Log localizer information
            _logger.LogInformation("Localizer Type: {LocalizerType}", _localizer.GetType().FullName);
            
            // Try to get internal information about the localizer
            try
            {
                var localizerType = _localizer.GetType();
                var fields = localizerType.GetFields(BindingFlags.NonPublic | BindingFlags.Instance);
                var properties = localizerType.GetProperties(BindingFlags.NonPublic | BindingFlags.Instance);
                
                _logger.LogInformation("Localizer has {FieldCount} private fields and {PropertyCount} private properties", 
                    fields.Length, properties.Length);
                
                foreach (var field in fields)
                {
                    try
                    {
                        var value = field.GetValue(_localizer);
                        _logger.LogInformation("Field {FieldName}: {FieldType} = {Value}", 
                            field.Name, field.FieldType.Name, value?.ToString() ?? "null");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning("Could not access field {FieldName}: {Error}", field.Name, ex.Message);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error inspecting localizer internals");
            }

            // Test resource file access
            TestResourceFileAccess();
            
            // Test key retrieval
            TestKeyRetrieval();
            
            _logger.LogInformation("=== END DETAILED LOCALIZATION ANALYSIS ===");
        }

        private void TestResourceFileAccess()
        {
            _logger.LogInformation("--- Testing Resource File Access ---");
            
            try
            {
                // Check if resource files exist
                var assembly = Assembly.GetExecutingAssembly();
                var resourceNames = assembly.GetManifestResourceNames();
                
                _logger.LogInformation("Assembly: {AssemblyName}", assembly.FullName);
                _logger.LogInformation("Found {ResourceCount} embedded resources:", resourceNames.Length);
                
                foreach (var resourceName in resourceNames)
                {
                    _logger.LogInformation("  - {ResourceName}", resourceName);
                }

                // Look for .resx files specifically
                var resxResources = resourceNames.Where(r => r.Contains("SharedResources")).ToArray();
                _logger.LogInformation("Found {ResxCount} SharedResources-related resources:", resxResources.Length);
                
                foreach (var resxResource in resxResources)
                {
                    _logger.LogInformation("  - {ResxResource}", resxResource);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking resource files");
            }
        }

        private void TestKeyRetrieval()
        {
            _logger.LogInformation("--- Testing Key Retrieval ---");
            
            var testKeys = new[]
            {
                "Nav.Dashboard",
                "Nav.Projects", 
                "Nav.Tasks",
                "Common.Save",
                "Common.Cancel",
                "Project.Title",
                "Task.Title"
            };

            foreach (var key in testKeys)
            {
                try
                {
                    var localizedString = _localizer[key];
                    
                    _logger.LogInformation("Key: {Key}", key);
                    _logger.LogInformation("  Value: {Value}", localizedString.Value);
                    _logger.LogInformation("  ResourceNotFound: {ResourceNotFound}", localizedString.ResourceNotFound);
                    _logger.LogInformation("  SearchedLocation: {SearchedLocation}", localizedString.SearchedLocation);
                    _logger.LogInformation("  Name: {Name}", localizedString.Name);
                    
                    // Try with different cultures
                    var originalCulture = CultureInfo.CurrentUICulture;
                    
                    try
                    {
                        CultureInfo.CurrentUICulture = new CultureInfo("es");
                        var spanishString = _localizer[key];
                        _logger.LogInformation("  Spanish Value: {SpanishValue} (ResourceNotFound: {SpanishNotFound})", 
                            spanishString.Value, spanishString.ResourceNotFound);
                    }
                    finally
                    {
                        CultureInfo.CurrentUICulture = originalCulture;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error retrieving key {Key}", key);
                }
            }
        }

        public string GetKeyWithLogging(string key)
        {
            try
            {
                var localizedString = _localizer[key];
                
                _logger.LogDebug("Retrieved key {Key}: Value='{Value}', ResourceNotFound={ResourceNotFound}, SearchedLocation='{SearchedLocation}'",
                    key, localizedString.Value, localizedString.ResourceNotFound, localizedString.SearchedLocation);
                
                return localizedString.Value;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving localization key {Key}", key);
                return key; // Return the key itself as fallback
            }
        }
    }
}
