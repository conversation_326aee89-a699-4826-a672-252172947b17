@using Microsoft.AspNetCore.Identity
@using PM.Tool.Core.Entities
@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager
<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - PM Tool Documentation</title>
    <meta name="description" content="@ViewData["Description"]" />
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="~/favicon.ico" />
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    
    <!-- Tailwind CSS -->
    <link href="~/css/output.css" rel="stylesheet" />
    
    <!-- Documentation Specific CSS -->
    <link href="~/css/documentation.css" rel="stylesheet" />
    
    <!-- Prism.js for code highlighting -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />
</head>
<body class="h-full bg-white dark:bg-gray-900 text-neutral-900 dark:text-white">
    <!-- Documentation Header -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-white dark:bg-gray-900 border-b border-neutral-200 dark:border-gray-700 h-16">
        <div class="flex items-center justify-between h-full px-6">
            <!-- Logo and Title -->
            <div class="flex items-center space-x-4">
                <a href="@Url.Action("Index", "Home")" class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-project-diagram text-white text-sm"></i>
                    </div>
                    <span class="text-xl font-bold text-neutral-900 dark:text-white">PM Tool</span>
                </a>
                <div class="hidden md:block w-px h-6 bg-neutral-300 dark:bg-gray-600"></div>
                <div class="hidden md:block">
                    <span class="text-lg font-semibold text-neutral-700 dark:text-neutral-300">Documentation</span>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="hidden lg:flex items-center space-x-6">
                <a href="@Url.Action("Index", "Documentation")" class="text-neutral-600 dark:text-neutral-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                    Home
                </a>
                <a href="@Url.Action("Search", "Documentation")" class="text-neutral-600 dark:text-neutral-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                    Search
                </a>
                <a href="@Url.Action("Sitemap", "Documentation")" class="text-neutral-600 dark:text-neutral-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                    Sitemap
                </a>
            </nav>

            <!-- Actions -->
            <div class="flex items-center space-x-4">
                <!-- Theme Toggle -->
                <button id="theme-toggle" class="p-2 rounded-lg hover:bg-neutral-100 dark:hover:bg-gray-700 transition-colors">
                    <i id="theme-icon" class="fas fa-moon text-neutral-600 dark:text-neutral-300"></i>
                </button>

                <!-- Back to App -->
                <a href="@Url.Action("Index", "Home")" 
                   class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-lg hover:bg-primary-700 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to App
                </a>

                <!-- Mobile Menu Toggle -->
                <button id="mobile-menu-toggle" class="lg:hidden p-2 rounded-lg hover:bg-neutral-100 dark:hover:bg-gray-700 transition-colors">
                    <i class="fas fa-bars text-neutral-600 dark:text-neutral-300"></i>
                </button>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobile-menu" class="lg:hidden hidden bg-white dark:bg-gray-900 border-t border-neutral-200 dark:border-gray-700">
            <nav class="px-6 py-4 space-y-2">
                <a href="@Url.Action("Index", "Documentation")" class="block py-2 text-neutral-600 dark:text-neutral-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                    Home
                </a>
                <a href="@Url.Action("Search", "Documentation")" class="block py-2 text-neutral-600 dark:text-neutral-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                    Search
                </a>
                <a href="@Url.Action("Sitemap", "Documentation")" class="block py-2 text-neutral-600 dark:text-neutral-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                    Sitemap
                </a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="pt-16 min-h-screen">
        <div class="max-w-7xl mx-auto px-6 py-8">
            @RenderBody()
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-neutral-50 dark:bg-gray-800 border-t border-neutral-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-6 py-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <h3 class="text-lg font-semibold text-neutral-900 dark:text-white mb-4">PM Tool Documentation</h3>
                    <p class="text-neutral-600 dark:text-neutral-300 text-sm">
                        Comprehensive documentation for the PM Tool project management platform.
                    </p>
                </div>
                <div>
                    <h4 class="text-md font-semibold text-neutral-900 dark:text-white mb-4">Quick Links</h4>
                    <ul class="space-y-2 text-sm">
                        <li><a href="@Url.Action("Index", "Documentation")" class="text-neutral-600 dark:text-neutral-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Documentation Home</a></li>
                        <li><a href="@Url.Action("Search", "Documentation")" class="text-neutral-600 dark:text-neutral-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Search</a></li>
                        <li><a href="@Url.Action("Sitemap", "Documentation")" class="text-neutral-600 dark:text-neutral-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Sitemap</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-md font-semibold text-neutral-900 dark:text-white mb-4">Support</h4>
                    <ul class="space-y-2 text-sm">
                        <li><a href="@Url.Action("Index", "Home")" class="text-neutral-600 dark:text-neutral-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Back to Application</a></li>
                        <li><a href="#" class="text-neutral-600 dark:text-neutral-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">Contact Support</a></li>
                    </ul>
                </div>
            </div>
            <div class="mt-8 pt-8 border-t border-neutral-200 dark:border-gray-700 text-center">
                <p class="text-neutral-500 dark:text-neutral-400 text-sm">
                    &copy; @DateTime.Now.Year PM Tool. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/js/theme.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    
    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-toggle')?.addEventListener('click', function() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            const menu = document.getElementById('mobile-menu');
            const toggle = document.getElementById('mobile-menu-toggle');
            
            if (!menu.contains(event.target) && !toggle.contains(event.target)) {
                menu.classList.add('hidden');
            }
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
