@using Microsoft.AspNetCore.Mvc.Localization
@inject IViewLocalizer Localizer
@model PM.Tool.Core.Entities.Requirement
@{
    ViewData["Title"] = "Edit Requirement";
}

<!-- Breadcrumb -->
@{
    ViewData["Items"] = new[] {
        new { Text = "Dashboard", Href = Url.Action("Index", "Home"), Icon = "fas fa-home" },
        new { Text = "Requirements", Href = Url.Action("Index", "Requirement"), Icon = "fas fa-clipboard-list" },
        new { Text = Model.Title, Href = Url.Action("Details", new { id = Model.Id }), Icon = "fas fa-file-alt" },
        new { Text = "Edit", Href = "", Icon = "fas fa-edit" }
    };
}
<partial name="Components/_Breadcrumb" view-data="ViewData" />

<!-- Page Header -->
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <div class="flex items-center space-x-3 mb-2">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200">
                    <EMAIL>("D4")
                </span>
                @{
                    var statusClass = GetStatusTailwindClass(Model.Status);
                }
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @statusClass">
                    @Model.Status
                </span>
            </div>
            <h1 class="text-2xl font-bold text-neutral-900 dark:text-dark-100 flex items-center">
                <i class="fas fa-edit mr-3 text-primary-600 dark:text-primary-400"></i>
                Edit Requirement
            </h1>
            <p class="mt-1 text-sm text-neutral-500 dark:text-dark-400">
                Update requirement details and specifications
            </p>
        </div>
        <div class="flex space-x-3">
            @{
                ViewData["Text"] = "View Details";
                ViewData["Variant"] = "outline";
                ViewData["Icon"] = "fas fa-eye";
                ViewData["Href"] = Url.Action("Details", new { id = Model.Id });
            }
            <partial name="Components/_Button" view-data="ViewData" />

            @{
                ViewData["Text"] = "Back to Requirements";
                ViewData["Variant"] = "secondary";
                ViewData["Icon"] = "fas fa-arrow-left";
                ViewData["Href"] = Url.Action("Index");
            }
            <partial name="Components/_Button" view-data="ViewData" />
        </div>
    </div>
</div>

<!-- Form -->
<form asp-action="Edit" method="post" id="requirementEditForm" class="space-y-8">
    <input asp-for="Id" type="hidden" />
    <input asp-for="CreatedAt" type="hidden" />
    <div asp-validation-summary="ModelOnly" class="bg-danger-50 dark:bg-danger-900/20 border border-danger-200 dark:border-danger-800 rounded-lg p-4 text-danger-800 dark:text-danger-200"></div>

    <!-- Basic Information -->
    @{
        var basicInfoContent = $@"
            <div class='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div class='md:col-span-2'>
                    <label asp-for='Title' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Title *</label>
                    <input asp-for='Title' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500' placeholder='Enter requirement title'>
                    <span asp-validation-for='Title' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>
                
                <div>
                    <label asp-for='ProjectId' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Project *</label>
                    <select asp-for='ProjectId' id='projectSelect' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                        <option value=''>Select a project</option>
                    </select>
                    <span asp-validation-for='ProjectId' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>

                <div>
                    <label asp-for='RequirementId' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Requirement ID</label>
                    <input asp-for='RequirementId' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500' placeholder='REQ-001'>
                    <span asp-validation-for='RequirementId' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>

                <div>
                    <label asp-for='Type' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Type *</label>
                    <select asp-for='Type' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                        <option value='Functional'>Functional</option>
                        <option value='NonFunctional'>Non-Functional</option>
                        <option value='Technical'>Technical</option>
                        <option value='Business'>Business</option>
                    </select>
                    <span asp-validation-for='Type' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>

                <div>
                    <label asp-for='Priority' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Priority *</label>
                    <select asp-for='Priority' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                        <option value='Low'>Low</option>
                        <option value='Medium'>Medium</option>
                        <option value='High'>High</option>
                        <option value='Critical'>Critical</option>
                    </select>
                    <span asp-validation-for='Priority' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>

                <div>
                    <label asp-for='Source' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Source</label>
                    <select asp-for='Source' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                        <option value='Stakeholder'>Stakeholder</option>
                        <option value='Customer'>Customer</option>
                        <option value='BusinessAnalyst'>Business Analyst</option>
                        <option value='Developer'>Developer</option>
                        <option value='Tester'>Tester</option>
                        <option value='ProjectManager'>Project Manager</option>
                    </select>
                    <span asp-validation-for='Source' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>

                <div>
                    <label asp-for='Status' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Status</label>
                    <select asp-for='Status' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                        <option value='Draft'>Draft</option>
                        <option value='UnderReview'>Under Review</option>
                        <option value='Approved'>Approved</option>
                        <option value='InProgress'>In Progress</option>
                        <option value='Completed'>Completed</option>
                        <option value='OnHold'>On Hold</option>
                    </select>
                    <span asp-validation-for='Status' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>
            </div>
        ";
        
        ViewData["Title"] = "Basic Information";
        ViewData["Icon"] = "fas fa-info-circle";
        ViewData["BodyContent"] = basicInfoContent;
    }
    <partial name="Components/_Card" view-data="ViewData" />

    <!-- Description -->
    @{
        var descriptionContent = @"
            <div>
                <label asp-for='Description' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Description *</label>
                <textarea asp-for='Description' rows='6' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500' placeholder='Provide a detailed description of the requirement...'></textarea>
                <span asp-validation-for='Description' class='text-danger-600 dark:text-danger-400 text-sm'></span>
            </div>
        ";
        
        ViewData["Title"] = "Description";
        ViewData["Icon"] = "fas fa-align-left";
        ViewData["BodyContent"] = descriptionContent;
    }
    <partial name="Components/_Card" view-data="ViewData" />

    <!-- Acceptance Criteria -->
    @{
        var acceptanceCriteriaContent = @"
            <div>
                <label asp-for='AcceptanceCriteria' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Acceptance Criteria</label>
                <textarea asp-for='AcceptanceCriteria' rows='4' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500' placeholder='Define the criteria that must be met for this requirement to be considered complete...'></textarea>
                <span asp-validation-for='AcceptanceCriteria' class='text-danger-600 dark:text-danger-400 text-sm'></span>
            </div>
        ";
        
        ViewData["Title"] = "Acceptance Criteria";
        ViewData["Icon"] = "fas fa-check-square";
        ViewData["BodyContent"] = acceptanceCriteriaContent;
    }
    <partial name="Components/_Card" view-data="ViewData" />

    <!-- Business Justification -->
    @{
        var businessJustificationContent = @"
            <div>
                <label asp-for='BusinessJustification' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Business Justification</label>
                <textarea asp-for='BusinessJustification' rows='3' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500' placeholder='Explain the business value and rationale for this requirement...'></textarea>
                <span asp-validation-for='BusinessJustification' class='text-danger-600 dark:text-danger-400 text-sm'></span>
            </div>
        ";
        
        ViewData["Title"] = "Business Justification";
        ViewData["Icon"] = "fas fa-business-time";
        ViewData["BodyContent"] = businessJustificationContent;
    }
    <partial name="Components/_Card" view-data="ViewData" />

    <!-- Assignments -->
    @{
        var assignmentsContent = @"
            <div class='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div>
                    <label asp-for='StakeholderUserId' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Stakeholder</label>
                    <select asp-for='StakeholderUserId' id='stakeholderSelect' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                        <option value=''>Select stakeholder</option>
                    </select>
                    <span asp-validation-for='StakeholderUserId' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>

                <div>
                    <label asp-for='AnalystUserId' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Business Analyst</label>
                    <select asp-for='AnalystUserId' id='analystSelect' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                        <option value=''>Select analyst</option>
                    </select>
                    <span asp-validation-for='AnalystUserId' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>

                <div>
                    <label asp-for='DeveloperUserId' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Developer</label>
                    <select asp-for='DeveloperUserId' id='developerSelect' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                        <option value=''>Select developer</option>
                    </select>
                    <span asp-validation-for='DeveloperUserId' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>

                <div>
                    <label asp-for='TesterUserId' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Tester</label>
                    <select asp-for='TesterUserId' id='testerSelect' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                        <option value=''>Select tester</option>
                    </select>
                    <span asp-validation-for='TesterUserId' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>
            </div>
        ";
        
        ViewData["Title"] = "Assignments";
        ViewData["Icon"] = "fas fa-users";
        ViewData["BodyContent"] = assignmentsContent;
    }
    <partial name="Components/_Card" view-data="ViewData" />

    <!-- Additional Information -->
    @{
        var additionalInfoContent = @"
            <div class='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div>
                    <label asp-for='EstimatedEffort' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Estimated Effort (hours)</label>
                    <input asp-for='EstimatedEffort' type='number' step='0.5' min='0' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500' placeholder='0'>
                    <span asp-validation-for='EstimatedEffort' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>

                <div>
                    <label asp-for='ParentRequirementId' class='block text-sm font-medium text-neutral-700 dark:text-dark-300 mb-2'>Parent Requirement</label>
                    <select asp-for='ParentRequirementId' id='parentRequirementSelect' class='w-full px-3 py-2 border border-neutral-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-800 text-neutral-900 dark:text-dark-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500'>
                        <option value=''>No parent requirement</option>
                    </select>
                    <span asp-validation-for='ParentRequirementId' class='text-danger-600 dark:text-danger-400 text-sm'></span>
                </div>
            </div>
        ";
        
        ViewData["Title"] = "Additional Information";
        ViewData["Icon"] = "fas fa-cog";
        ViewData["BodyContent"] = additionalInfoContent;
    }
    <partial name="Components/_Card" view-data="ViewData" />

    <!-- Form Actions -->
    <div class="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3 pt-6 border-t border-neutral-200 dark:border-dark-600">
        @{
            ViewData["Text"] = "Cancel";
            ViewData["Variant"] = "secondary";
            ViewData["Href"] = Url.Action("Details", new { id = Model.Id });
        }
        <partial name="Components/_Button" view-data="ViewData" />

        @{
            ViewData["Text"] = "Update Requirement";
            ViewData["Variant"] = "primary";
            ViewData["Type"] = "submit";
            ViewData["Icon"] = "fas fa-save";
        }
        <partial name="Components/_Button" view-data="ViewData" />
    </div>
</form>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function() {
            loadProjects();
            loadUsers();
            loadParentRequirements();
            setupFormValidation();
        });

        function loadProjects() {
            $.get('@Url.Action("GetProjects", "Project")')
                .done(function(data) {
                    const select = $('#projectSelect');
                    if (data && Array.isArray(data)) {
                        data.forEach(function(project) {
                            const selected = project.id == @Model.ProjectId ? 'selected' : '';
                            select.append(`<option value="${project.id}" ${selected}>${project.name}</option>`);
                        });
                    }
                })
                .fail(function() {
                    console.error('Failed to load projects');
                });
        }

        function loadUsers() {
            $.get('@Url.Action("GetUsers", "User")')
                .done(function(data) {
                    if (data && Array.isArray(data)) {
                        populateUserSelect('#stakeholderSelect', data, '@(Model.StakeholderUserId ?? "")');
                        populateUserSelect('#analystSelect', data, '@(Model.AnalystUserId ?? "")');
                        populateUserSelect('#developerSelect', data, '@(Model.DeveloperUserId ?? "")');
                        populateUserSelect('#testerSelect', data, '@(Model.TesterUserId ?? "")');
                    }
                })
                .fail(function() {
                    console.error('Failed to load users');
                });
        }

        function populateUserSelect(selector, users, selectedUserId) {
            const select = $(selector);
            users.forEach(function(user) {
                const selected = user.id === selectedUserId ? 'selected' : '';
                select.append(`<option value="${user.id}" ${selected}>${user.userName} (${user.email})</option>`);
            });
        }

        function loadParentRequirements() {
            const projectId = $('#projectSelect').val() || @Model.ProjectId;
            if (projectId) {
                $.get('@Url.Action("GetProjectRequirements", "Requirement")', { projectId: projectId })
                    .done(function(data) {
                        const select = $('#parentRequirementSelect');
                        select.empty().append('<option value="">No parent requirement</option>');
                        
                        if (data && Array.isArray(data)) {
                            data.forEach(function(req) {
                                if (req.id !== @Model.Id) { // Don't allow self as parent
                                    const selected = req.id == @(Model.ParentRequirementId ?? 0) ? 'selected' : '';
                                    select.append(`<option value="${req.id}" ${selected}>${req.requirementId} - ${req.title}</option>`);
                                }
                            });
                        }
                    })
                    .fail(function() {
                        console.error('Failed to load parent requirements');
                    });
            }
        }

        function setupFormValidation() {
            $('#projectSelect').on('change', loadParentRequirements);
            
            $('#requirementEditForm').on('submit', function(e) {
                // Additional client-side validation can be added here
            });
        }
    </script>
}

@functions {
    string GetStatusTailwindClass(PM.Tool.Core.Entities.RequirementStatus status)
    {
        return status switch
        {
            PM.Tool.Core.Entities.RequirementStatus.Draft => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200",
            PM.Tool.Core.Entities.RequirementStatus.UnderReview => "bg-warning-100 dark:bg-warning-900 text-warning-800 dark:text-warning-200",
            PM.Tool.Core.Entities.RequirementStatus.Approved => "bg-info-100 dark:bg-info-900 text-info-800 dark:text-info-200",
            PM.Tool.Core.Entities.RequirementStatus.InProgress => "bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200",
            PM.Tool.Core.Entities.RequirementStatus.Completed => "bg-success-100 dark:bg-success-900 text-success-800 dark:text-success-200",
            PM.Tool.Core.Entities.RequirementStatus.OnHold => "bg-danger-100 dark:bg-danger-900 text-danger-800 dark:text-danger-200",
            _ => "bg-neutral-100 dark:bg-dark-700 text-neutral-800 dark:text-dark-200"
        };
    }
}
