using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using PM.Tool.Core.Interfaces;

namespace PM.Tool.Application.Services
{
    public class DeadlineNotificationBackgroundService : BackgroundService
    {
        private readonly ILogger<DeadlineNotificationBackgroundService> _logger;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly TimeSpan _checkInterval = TimeSpan.FromHours(24); // Check once per day

        public DeadlineNotificationBackgroundService(
            ILogger<DeadlineNotificationBackgroundService> logger,
            IServiceScopeFactory serviceScopeFactory)
        {
            _logger = logger;
            _serviceScopeFactory = serviceScopeFactory;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await CheckDeadlinesAsync();
                    await Task.Delay(_checkInterval, stoppingToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred while checking deadlines");
                    // Wait a shorter time if there was an error
                    await Task.Delay(TimeSpan.FromMinutes(15), stoppingToken);
                }
            }
        }

        private async Task CheckDeadlinesAsync()
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();

            await notificationService.NotifyDeadlineApproachingAsync();
            _logger.LogInformation("Deadline check completed at: {time}", DateTimeOffset.Now);
        }
    }
}
