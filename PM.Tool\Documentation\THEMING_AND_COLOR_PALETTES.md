
---

### ✅ AI Agent Prompt for Professional Theme Refinement

> You are designing a modern, accessible, enterprise-grade theming system for a project management web app. The base system supports light/dark mode, semantic color usage, and uses both custom CSS and Tailwind CSS.

#### 🧠 TASK OBJECTIVE:

Enhance the existing component styles (cards, forms, alerts, status pills) with a **refined dark mode** and **professional UI improvements**. Maintain visual consistency and adhere to accessibility and branding best practices.

#### 🎯 OUTPUT REQUIREMENTS:

1. **Professional Dark Mode Appearance**

   * Improve contrast and readability in dark mode.
   * Avoid overly harsh black or pure white—use deep neutrals (e.g., `#1e1e1e`, `#171717`) and soft highlight colors.
   * Ensure clear focus/hover states in dark mode.

2. **Enterprise-grade Color Refinement**

   * Use a trusted palette:

     * Neutrals: `#f9fafb`, `#111827`, `#1f2937`, `#e5e7eb`, `#374151`
     * Primary: `#2563eb` (blue-600), hover: `#1d4ed8`
     * Success: `#22c55e` (green-500), dark bg: `#14532d`
     * Warning: `#f59e0b`, dark bg: `#78350f`
     * Danger: `#ef4444`, dark bg: `#7f1d1d`
     * Info: `#0ea5e9`, dark bg: `#075985`

3. **Update Component Styles:**

   * **Card**: Add subtle dark mode shadows, background transitions, and improved border visibility.
   * **Form Inputs**: Use strong contrast for text and borders in dark mode. Highlight focus state elegantly.
   * **Alerts**: Improve background and border contrast in dark mode, ensure legibility.
   * **Status Pills**: Ensure distinct background/text pairs for each status in both themes.

4. **Accessible and Responsive Design**

   * Respect user preferences: `prefers-color-scheme`, `prefers-contrast`, `prefers-reduced-motion`.
   * Ensure WCAG AA+ compliance (min 4.5:1 contrast for text).
   * Provide clear focus outlines and keyboard navigation support.

5. **Implement via CSS Custom Properties**

   * Use semantic variables (`--color-bg-primary`, `--color-text-secondary`, etc.).
   * Use Tailwind `theme.extend.colors` for scalable theme tokens.

6. **Test the Theme System**

   * Simulate light/dark mode.
   * Test alert/message visibility across screen sizes.
   * Validate usability in both reduced motion and high contrast modes.

#### 📄 INPUT FILES TO MODIFY:

* `/wwwroot/css/components.css`
* `tailwind.config.js`
* Any base or theme CSS files defining `:root` or `[data-theme]`.

#### 📌 FINAL GOAL:

Deliver a **visually polished**, **accessible**, and **brand-consistent** theme that feels **modern and premium**, especially in **dark mode**, suitable for enterprise use.

---

