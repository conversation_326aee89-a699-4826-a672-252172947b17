using System.ComponentModel.DataAnnotations;

namespace PM.Tool.Core.Entities
{
    public class ResourceSkill : BaseEntity
    {
        public int ResourceId { get; set; }
        
        public int SkillId { get; set; }

        public SkillLevel Level { get; set; } = SkillLevel.Intermediate;

        public int YearsOfExperience { get; set; }

        private DateTime? _certificationDate;
        public DateTime? CertificationDate
        {
            get => _certificationDate;
            set => _certificationDate = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        private DateTime? _certificationExpiry;
        public DateTime? CertificationExpiry
        {
            get => _certificationExpiry;
            set => _certificationExpiry = value.HasValue ? DateTime.SpecifyKind(value.Value, DateTimeKind.Utc) : null;
        }

        [MaxLength(500)]
        public string? Notes { get; set; }

        // Navigation properties
        public virtual Resource Resource { get; set; } = null!;
        public virtual Skill Skill { get; set; } = null!;

        // Computed properties
        public bool IsCertified => CertificationDate.HasValue;
        public bool IsCertificationValid => CertificationExpiry.HasValue && CertificationExpiry > DateTime.UtcNow;
    }

    public class Skill : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? Description { get; set; }

        [MaxLength(100)]
        public string? Category { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ICollection<ResourceSkill> ResourceSkills { get; set; } = new List<ResourceSkill>();
        public virtual ICollection<TaskSkillRequirement> TaskRequirements { get; set; } = new List<TaskSkillRequirement>();
    }

    public class TaskSkillRequirement : BaseEntity
    {
        public int TaskId { get; set; }
        
        public int SkillId { get; set; }

        public SkillLevel RequiredLevel { get; set; } = SkillLevel.Intermediate;

        public bool IsRequired { get; set; } = true;

        [MaxLength(500)]
        public string? Notes { get; set; }

        // Navigation properties
        public virtual TaskEntity Task { get; set; } = null!;
        public virtual Skill Skill { get; set; } = null!;
    }

    public enum SkillLevel
    {
        Beginner = 1,
        Intermediate = 2,
        Advanced = 3,
        Expert = 4
    }
}
